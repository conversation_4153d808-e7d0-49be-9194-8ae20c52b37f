name: Solana RPS Game Tests

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]
  workflow_dispatch:
    inputs:
      test_type:
        description: 'Type of tests to run'
        required: true
        default: 'all'
        type: 'choice'
        options:
          - all
          - basic
          - security
          - performance
          - e2e

jobs:
  build-and-test:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18.x'
          cache: 'npm'
          cache-dependency-path: solana-rps-game/testing/package-lock.json

      - name: Install dependencies
        working-directory: solana-rps-game/testing
        run: npm ci

      - name: Generate wallets
        working-directory: solana-rps-game/testing
        run: npm run generate-wallets

      - name: Run basic tests
        if: github.event.inputs.test_type == 'basic' || github.event.inputs.test_type == 'all' || github.event.inputs.test_type == ''
        working-directory: solana-rps-game/testing
        run: npm run test-basic

      - name: Run mock security tests
        if: github.event.inputs.test_type == 'security' || github.event.inputs.test_type == 'all' || github.event.inputs.test_type == ''
        working-directory: solana-rps-game/testing
        run: npm run test-mock-security

      - name: Run performance tests
        if: github.event.inputs.test_type == 'performance' || github.event.inputs.test_type == 'all' || github.event.inputs.test_type == ''
        working-directory: solana-rps-game/testing
        run: npm run test-performance

      - name: Run E2E tests
        if: github.event.inputs.test_type == 'e2e' || github.event.inputs.test_type == 'all' || github.event.inputs.test_type == ''
        working-directory: solana-rps-game/testing
        run: npm run test-e2e

      - name: Upload test results
        uses: actions/upload-artifact@v3
        with:
          name: test-results
          path: solana-rps-game/testing/results/
          retention-days: 7
