modules = ["nodejs-20", "rust-stable", "web", "bash"]
run = "node index.js"

[nix]
channel = "stable-24_05"

[deployment]
run = ["sh", "-c", "node index.js"]

# Environment variables expected by the app.
# Configure real values via the Replit “Secrets” tab.
# NOTE: values here are just fall-back defaults for local CLI use.
# In Replit these are automatically overridden by Secrets.
[env]
VITE_RPC_ENDPOINT="https://api.devnet.solana.com"
VITE_RPS_PROGRAM_ID="<your_program_id>"
VITE_RPS_TOKEN_MINT="<optional_token_mint>"
VITE_FEE_COLLECTOR_ACCOUNT="<fee_collector_pubkey>"

[workflows]
runButton = "Run Frontend Prod"

[[workflows.workflow]]
name = "Run Frontend Dev"
author = ********
mode = "sequential"

[[workflows.workflow.tasks]]
task = "packager.installForAll"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "npm run dev"

[[workflows.workflow]]
name = "Run Frontend Prod"
author = ********
mode = "sequential"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "npm run build && npm run start"

[[ports]]
localPort = 3000
externalPort = 80

[[ports]]
localPort = 5000
externalPort = 5000

[[ports]]
localPort = 5173
externalPort = 5173
exposeLocalhost = true
