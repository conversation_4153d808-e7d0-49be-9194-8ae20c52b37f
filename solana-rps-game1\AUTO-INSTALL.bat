@echo off
REM Solana RPS Game - FULLY AUTOMATIC INSTALLER
REM This installs everything with zero user interaction

title Solana RPS Game - Auto Installer

echo.
echo ==========================================
echo    SOLANA RPS GAME - AUTO INSTALLER
echo ==========================================
echo.
echo This will automatically install everything needed.
echo No user input required - just wait for completion.
echo.
echo Installing:
echo - Node.js (JavaScript runtime)
echo - Rust (Programming language)
echo - Solana CLI (Blockchain tools)
echo - All game dependencies
echo.
echo Estimated time: 5-10 minutes
echo.

REM Set execution policy for PowerShell
powershell -Command "Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser -Force" >nul 2>&1

REM Create installation directory
if not exist "install-logs" mkdir install-logs

echo [1/10] Preparing installation...
echo %date% %time% - Starting installation > install-logs\progress.log

REM Install Chocolatey (Windows package manager)
echo [2/10] Installing package manager...
where choco >nul 2>&1
if %errorLevel% neq 0 (
    echo Installing Chocolatey package manager...
    powershell -NoProfile -InputFormat None -ExecutionPolicy Bypass -Command "iex ((New-Object System.Net.WebClient).DownloadString('https://chocolatey.org/install.ps1'))" > install-logs\chocolatey.log 2>&1
    if %errorLevel% neq 0 (
        echo Failed to install Chocolatey. Trying alternative method...
        powershell -Command "Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))" >> install-logs\chocolatey.log 2>&1
    )
    REM Refresh environment variables
    call refreshenv >nul 2>&1
    set PATH=%PATH%;%ALLUSERSPROFILE%\chocolatey\bin
)

REM Install Node.js
echo [3/10] Installing Node.js...
where node >nul 2>&1
if %errorLevel% neq 0 (
    echo Downloading and installing Node.js...
    choco install nodejs -y --force > install-logs\nodejs.log 2>&1
    if %errorLevel% neq 0 (
        echo Chocolatey failed, trying direct download...
        powershell -Command "Invoke-WebRequest -Uri 'https://nodejs.org/dist/v20.10.0/node-v20.10.0-x64.msi' -OutFile 'node-installer.msi'" > install-logs\nodejs-direct.log 2>&1
        msiexec /i node-installer.msi /quiet /norestart >> install-logs\nodejs-direct.log 2>&1
        del node-installer.msi >nul 2>&1
    )
    call refreshenv >nul 2>&1
)

REM Install Git
echo [4/10] Installing Git...
where git >nul 2>&1
if %errorLevel% neq 0 (
    echo Installing Git...
    choco install git -y --force > install-logs\git.log 2>&1
    if %errorLevel% neq 0 (
        echo Chocolatey failed, trying direct download...
        powershell -Command "Invoke-WebRequest -Uri 'https://github.com/git-for-windows/git/releases/download/v2.42.0.windows.2/Git-2.42.0.2-64-bit.exe' -OutFile 'git-installer.exe'" > install-logs\git-direct.log 2>&1
        git-installer.exe /VERYSILENT /NORESTART >> install-logs\git-direct.log 2>&1
        del git-installer.exe >nul 2>&1
    )
    call refreshenv >nul 2>&1
)

REM Install Rust
echo [5/10] Installing Rust...
where rustc >nul 2>&1
if %errorLevel% neq 0 (
    echo Installing Rust programming language...
    powershell -Command "Invoke-WebRequest -Uri 'https://win.rustup.rs/' -OutFile 'rustup-init.exe'" > install-logs\rust.log 2>&1
    rustup-init.exe -y --default-toolchain stable >> install-logs\rust.log 2>&1
    del rustup-init.exe >nul 2>&1
    set PATH=%USERPROFILE%\.cargo\bin;%PATH%
)

REM Install Solana CLI
echo [6/10] Installing Solana CLI...
where solana >nul 2>&1
if %errorLevel% neq 0 (
    echo Installing Solana blockchain tools...
    powershell -Command "Invoke-WebRequest -Uri 'https://release.solana.com/v1.18.22/solana-install-init-x86_64-pc-windows-msvc.exe' -OutFile 'solana-installer.exe'" > install-logs\solana.log 2>&1
    solana-installer.exe v1.18.22 >> install-logs\solana.log 2>&1
    del solana-installer.exe >nul 2>&1
    set PATH=%USERPROFILE%\.local\share\solana\install\active_release\bin;%PATH%
)

REM Setup environment
echo [7/10] Configuring environment...
if not exist ".env" (
    echo # Solana RPS Game Configuration > .env
    echo VITE_RPC_ENDPOINT=https://api.devnet.solana.com >> .env
    echo VITE_RPS_PROGRAM_ID=7Y9dRMY6V9cmVkXNFrHeUZmYf2tAV5wSVFcYyD5bLQpZ >> .env
    echo VITE_RPS_TOKEN_MINT= >> .env
    echo VITE_FEE_COLLECTOR_ACCOUNT=FeeKHhL1CcJCyd82xextWTbBT5jGzVQwXVQKNjHV8SDD >> .env
)

if not exist "frontend\.env" copy .env frontend\.env >nul 2>&1

REM Setup Solana wallet
echo [8/10] Setting up blockchain wallet...
if not exist "%USERPROFILE%\.config\solana" mkdir "%USERPROFILE%\.config\solana" >nul 2>&1
if not exist "%USERPROFILE%\.config\solana\id.json" (
    solana-keygen new --no-bip39-passphrase --silent --outfile "%USERPROFILE%\.config\solana\id.json" > install-logs\wallet.log 2>&1
)
solana config set --url https://api.devnet.solana.com > install-logs\config.log 2>&1

REM Install project dependencies
echo [9/10] Installing game dependencies...
if exist "frontend\package.json" (
    cd frontend
    call npm install --legacy-peer-deps --silent > ..\install-logs\npm-frontend.log 2>&1
    cd ..
)

if exist "testing\package.json" (
    cd testing
    call npm install --silent > ..\install-logs\npm-testing.log 2>&1
    cd ..
)

if exist "backend\monitoring\package.json" (
    cd backend\monitoring
    call npm install --silent > ..\..\install-logs\npm-monitoring.log 2>&1
    cd ..\..
)

REM Start services
echo [10/10] Starting game services...

REM Start Solana validator
solana cluster-version >nul 2>&1
if %errorLevel% neq 0 (
    echo Starting blockchain validator...
    start "Solana Validator" /min cmd /c "solana-test-validator > install-logs\validator.log 2>&1"
    timeout /t 15 /nobreak >nul
)

REM Get test SOL
echo Getting test cryptocurrency...
solana airdrop 2 > install-logs\airdrop.log 2>&1

REM Start frontend
echo Starting game server...
cd frontend
start "Solana RPS Game" cmd /c "echo Game starting... && npm run dev"
cd ..

REM Wait for startup
echo Waiting for game to initialize...
timeout /t 10 /nobreak >nul

echo.
echo ==========================================
echo        INSTALLATION COMPLETED!
echo ==========================================
echo.
echo ✓ All software installed automatically
echo ✓ Blockchain validator running
echo ✓ Game server starting
echo ✓ Wallet configured with test funds
echo.
echo 🎮 GAME URL: http://localhost:5173
echo.

REM Open browser
echo Opening game in browser...
timeout /t 3 /nobreak >nul
start http://localhost:5173

echo.
echo WHAT TO DO NEXT:
echo.
echo 1. Install Phantom wallet browser extension:
echo    https://phantom.app/
echo.
echo 2. In Phantom wallet:
echo    - Create new wallet or import existing
echo    - Switch network to "Devnet"
echo    - Get free test SOL: https://faucet.solana.com/
echo.
echo 3. In the game:
echo    - Click "Connect Wallet"
echo    - Approve connection
echo    - Start playing Rock Paper Scissors!
echo.
echo Installation logs saved in: install-logs\
echo.
echo If game doesn't load immediately, wait 30 seconds and refresh.
echo.

pause
