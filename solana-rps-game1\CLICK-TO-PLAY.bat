@echo off
REM One-click game launcher

title Solana RPS Game

cls
echo.
echo ==========================================
echo         SOLANA RPS GAME LAUNCHER
echo ==========================================
echo.

REM Check if already installed
if exist "install-logs\progress.log" (
    echo Previous installation detected.
    echo Starting game...
    goto start_game
)

echo This will automatically install and start the game.
echo No technical knowledge required!
echo.
echo What will be installed:
echo - Node.js (for running the game)
echo - Rust (for blockchain programs)  
echo - Solana CLI (blockchain tools)
echo - Game dependencies
echo.
echo Time needed: 5-10 minutes
echo.

choice /C YN /M "Install and start the game now"
if errorlevel 2 exit /b
if errorlevel 1 goto install

:install
echo.
echo Starting automatic installation...
call AUTO-INSTALL.bat
goto end

:start_game
REM Quick start for already installed
where node >nul 2>&1
if %errorLevel% neq 0 (
    echo Installation incomplete. Running full installer...
    call AUTO-INSTALL.bat
    goto end
)

echo Checking services...

REM Start validator if needed
solana cluster-version >nul 2>&1
if %errorLevel% neq 0 (
    echo Starting blockchain...
    start "Solana Validator" /min cmd /c "solana-test-validator"
    timeout /t 10 /nobreak >nul
)

REM Start frontend if needed
netstat -an | findstr ":5173" >nul 2>&1
if %errorLevel% neq 0 (
    echo Starting game server...
    cd frontend
    start "Solana RPS Game" cmd /c "npm run dev"
    cd ..
    timeout /t 8 /nobreak >nul
)

echo.
echo ✓ Game is running!
echo ✓ Opening browser...
start http://localhost:5173

echo.
echo Game URL: http://localhost:5173
echo.
echo Remember to:
echo 1. Install Phantom wallet: https://phantom.app/
echo 2. Switch to Devnet in wallet settings
echo 3. Get test SOL: https://faucet.solana.com/
echo.

:end
pause
