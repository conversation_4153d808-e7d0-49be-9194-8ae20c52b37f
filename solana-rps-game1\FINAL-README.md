# 🎮 Solana Rock Paper Scissors - READY TO PLAY!

## 🚀 ZERO-EFFORT INSTALLATION

**Just double-click: `CLICK-TO-PLAY.bat`**

That's literally it! The script will:
- ✅ Install everything automatically (Node.js, <PERSON>ust, Solana CLI)
- ✅ Set up the blockchain and wallets
- ✅ Start the game server
- ✅ Open the game in your browser
- ✅ No technical knowledge needed!

## 📱 What You Need to Play:

**Only 1 thing: Phantom Wallet**
1. Go to: https://phantom.app/
2. Install the browser extension
3. Create a wallet
4. Switch to "Devnet" (for free test coins)
5. Get free SOL: https://faucet.solana.com/

## 🎯 Files to Use:

- **`CLICK-TO-PLAY.bat`** ← **Double-click this!**
- **`AUTO-INSTALL.bat`** ← Alternative installer
- **`START-GAME.bat`** ← Restart game later

## 🎮 Game Features:

- **Multi-player**: Play with 3-4 people
- **Tournaments**: Bracket-style competitions
- **Auto-play**: Let the computer play for you
- **Secure**: Blockchain ensures fair play
- **Free**: Uses test cryptocurrency (not real money)

## ❓ Troubleshooting:

**Game won't start?**
- Right-click `CLICK-TO-PLAY.bat` → "Run as administrator"
- Wait 5-10 minutes for first-time installation
- Make sure you have internet connection

**"localhost refused to connect"?**
- Wait 30 seconds and refresh the page
- The game server takes time to start

**Need help?**
- All installation logs are saved in `install-logs/`
- Game runs at: http://localhost:5173

## 🌟 What Makes This Special:

- **Fully Automated**: No manual installation steps
- **Beginner Friendly**: No blockchain knowledge needed  
- **Secure**: Enhanced cryptographic protection
- **Tested**: Comprehensive security and performance tests
- **Fun**: Play Rock Paper Scissors with real stakes!

---

**Ready to play? Just double-click `CLICK-TO-PLAY.bat` and you're done!** 🚀

*The script does everything for you - installs all software, sets up the blockchain, configures wallets, and starts the game. No technical skills required!*
