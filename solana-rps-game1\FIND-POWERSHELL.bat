@echo off
REM Check if PowerShell is available and show how to access it

title Finding PowerShell

echo.
echo ==========================================
echo    FINDING POWERSHELL ON YOUR SYSTEM
echo ==========================================
echo.

echo PowerShell comes built-in with Windows!
echo Let's check if it's available...
echo.

REM Test PowerShell 5.1 (Windows PowerShell)
echo [TEST 1] Checking Windows PowerShell...
powershell -Command "Write-Host 'Windows PowerShell is available!' -ForegroundColor Green; $PSVersionTable.PSVersion" 2>nul
if %errorLevel% == 0 (
    echo ✓ Windows PowerShell found!
    set POWERSHELL_FOUND=1
) else (
    echo ✗ Windows PowerShell not found
)

echo.

REM Test PowerShell 7+ (PowerShell Core)
echo [TEST 2] Checking PowerShell Core...
pwsh -Command "Write-Host 'PowerShell Core is available!' -ForegroundColor Green; $PSVersionTable.PSVersion" 2>nul
if %errorLevel% == 0 (
    echo ✓ PowerShell Core found!
    set PWSH_FOUND=1
) else (
    echo ✗ PowerShell Core not found
)

echo.

if defined POWERSHELL_FOUND (
    echo ==========================================
    echo    POWERSHELL IS AVAILABLE!
    echo ==========================================
    echo.
    echo You can access PowerShell in several ways:
    echo.
    echo METHOD 1: Start Menu
    echo   - Press Windows key
    echo   - Type "PowerShell"
    echo   - Right-click "Windows PowerShell"
    echo   - Choose "Run as administrator"
    echo.
    echo METHOD 2: Run Dialog
    echo   - Press Windows + R
    echo   - Type "powershell"
    echo   - Press Ctrl + Shift + Enter (for admin)
    echo.
    echo METHOD 3: Command Prompt
    echo   - Open Command Prompt as admin
    echo   - Type "powershell"
    echo   - Press Enter
    echo.
    echo ==========================================
    echo    READY TO INSTALL THE GAME!
    echo ==========================================
    echo.
    choice /C YN /M "Run the game installer now"
    if errorlevel 1 (
        echo.
        echo Running installer...
        powershell -ExecutionPolicy Bypass -File "Install-Game.ps1"
    )
) else (
    echo ==========================================
    echo    POWERSHELL NOT DETECTED
    echo ==========================================
    echo.
    echo This is unusual for Windows 10/11.
    echo.
    echo SOLUTIONS:
    echo.
    echo 1. UPDATE WINDOWS:
    echo    - Go to Settings ^> Update ^& Security
    echo    - Install all available updates
    echo    - Restart your computer
    echo.
    echo 2. INSTALL POWERSHELL 7:
    echo    - Go to: https://github.com/PowerShell/PowerShell/releases
    echo    - Download PowerShell-7.x.x-win-x64.msi
    echo    - Install it
    echo.
    echo 3. USE ALTERNATIVE INSTALLER:
    echo    - Try the manual installation method
    echo    - Download Node.js from: https://nodejs.org/
    echo    - Download Rust from: https://rustup.rs/
    echo    - Download Solana CLI from: https://docs.solana.com/cli/install-solana-cli-tools
    echo.
    echo What Windows version are you running?
    ver
    echo.
    echo If you're on Windows 7 or 8, you may need to install PowerShell manually.
)

echo.
pause
