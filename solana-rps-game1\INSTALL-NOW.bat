@echo off
REM Solana RPS Game - INSTANT FIX SCRIPT
REM This will fix everything and get the game running

title Solana RPS Game - Installing...

echo.
echo ==========================================
echo    SOLANA RPS GAME - INSTANT INSTALLER
echo ==========================================
echo.
echo This will install everything needed and start the game.
echo Please wait while we set everything up...
echo.

REM Create logs directory
if not exist "logs" mkdir logs

REM Step 1: Install Chocolatey (package manager)
echo [1/8] Installing package manager...
where choco >nul 2>&1
if %errorLevel% neq 0 (
    echo Installing Chocolatey...
    powershell -Command "Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))" > logs\choco.log 2>&1
    call refreshenv
    echo ✓ Package manager installed
) else (
    echo ✓ Package manager already installed
)

REM Step 2: Install Node.js
echo [2/8] Installing Node.js...
where node >nul 2>&1
if %errorLevel% neq 0 (
    echo Installing Node.js (this may take a few minutes)...
    choco install nodejs -y > logs\nodejs.log 2>&1
    call refreshenv
    echo ✓ Node.js installed
) else (
    echo ✓ Node.js already installed
)

REM Step 3: Install Git
echo [3/8] Installing Git...
where git >nul 2>&1
if %errorLevel% neq 0 (
    echo Installing Git...
    choco install git -y > logs\git.log 2>&1
    call refreshenv
    echo ✓ Git installed
) else (
    echo ✓ Git already installed
)

REM Step 4: Install Rust
echo [4/8] Installing Rust...
where rustc >nul 2>&1
if %errorLevel% neq 0 (
    echo Installing Rust (this may take several minutes)...
    curl -sSf https://win.rustup.rs/ -o rustup-init.exe > logs\rust.log 2>&1
    rustup-init.exe -y >> logs\rust.log 2>&1
    del rustup-init.exe
    set PATH=%USERPROFILE%\.cargo\bin;%PATH%
    echo ✓ Rust installed
) else (
    echo ✓ Rust already installed
)

REM Step 5: Install Solana CLI
echo [5/8] Installing Solana CLI...
where solana >nul 2>&1
if %errorLevel% neq 0 (
    echo Installing Solana CLI...
    curl -sSfL https://release.solana.com/v1.18.22/solana-install-init-x86_64-pc-windows-msvc.exe -o solana-install.exe > logs\solana.log 2>&1
    solana-install.exe v1.18.22 >> logs\solana.log 2>&1
    del solana-install.exe
    set PATH=%USERPROFILE%\.local\share\solana\install\active_release\bin;%PATH%
    echo ✓ Solana CLI installed
) else (
    echo ✓ Solana CLI already installed
)

REM Step 6: Setup project
echo [6/8] Setting up project...

REM Create environment file
echo # Solana RPS Game Configuration > .env
echo VITE_RPC_ENDPOINT=https://api.devnet.solana.com >> .env
echo VITE_RPS_PROGRAM_ID=7Y9dRMY6V9cmVkXNFrHeUZmYf2tAV5wSVFcYyD5bLQpZ >> .env
echo VITE_RPS_TOKEN_MINT= >> .env

if not exist "frontend\.env" copy .env frontend\.env >nul

REM Setup Solana wallet
if not exist "%USERPROFILE%\.config\solana\id.json" (
    echo Creating Solana wallet...
    solana-keygen new --no-bip39-passphrase --silent --outfile "%USERPROFILE%\.config\solana\id.json" > logs\wallet.log 2>&1
)

solana config set --url https://api.devnet.solana.com > logs\config.log 2>&1

REM Install dependencies
echo Installing project dependencies...
cd frontend
call npm install --legacy-peer-deps > ..\logs\npm.log 2>&1
cd ..

echo ✓ Project setup complete

REM Step 7: Start Solana validator
echo [7/8] Starting blockchain...
solana cluster-version >nul 2>&1
if %errorLevel% neq 0 (
    echo Starting Solana test validator...
    start "Solana Validator" /min cmd /c "solana-test-validator > logs\validator.log 2>&1"
    echo Waiting for validator to start...
    timeout /t 15 /nobreak >nul
    echo ✓ Blockchain started
) else (
    echo ✓ Blockchain already running
)

REM Get some SOL for testing
echo Getting test SOL...
solana airdrop 2 > logs\airdrop.log 2>&1

REM Step 8: Start the game
echo [8/8] Starting the game...
echo Starting frontend server...
cd frontend
start "Solana RPS Game" cmd /c "npm run dev"
cd ..

echo Waiting for game to start...
timeout /t 10 /nobreak >nul

echo.
echo ==========================================
echo           INSTALLATION COMPLETE!
echo ==========================================
echo.
echo ✓ All dependencies installed
echo ✓ Solana blockchain running
echo ✓ Game server starting
echo.
echo 🎮 GAME URL: http://localhost:5173
echo.
echo Opening game in browser...
start http://localhost:5173

echo.
echo NEXT STEPS:
echo 1. Install Phantom wallet: https://phantom.app/
echo 2. Switch wallet to "Devnet"
echo 3. Get devnet SOL: https://faucet.solana.com/
echo 4. Connect wallet in the game
echo 5. Start playing!
echo.
echo If the game doesn't load, wait 30 seconds and refresh.
echo.

pause
