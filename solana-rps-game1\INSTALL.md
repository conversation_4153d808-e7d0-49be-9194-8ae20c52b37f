# 🎮 Solana RPS Game - Installation Instructions

## 🚀 One-Click Setup (Recommended)

For a fresh Windows installation, simply run:

```powershell
# Open PowerShell as Administrator and run:
.\complete-setup.ps1
```

This will:
- ✅ Install all dependencies (Node.js, <PERSON>ust, Solana CLI)
- ✅ Configure the project environment
- ✅ Run security audits
- ✅ Build and deploy the Solana program
- ✅ Run comprehensive tests
- ✅ Generate reports and dashboards
- ✅ Start the game automatically

## 📋 Prerequisites

- **Windows 10/11** (PowerShell 5.1+)
- **Administrator privileges** (for dependency installation)
- **Internet connection** (for downloading dependencies)

## 🔧 Manual Setup (Alternative)

If you prefer step-by-step installation:

### 1. Install Dependencies
```powershell
.\setup-windows.ps1
```

### 2. Configure Project
```powershell
.\setup-project.ps1
```

### 3. Deploy Program
```powershell
.\deploy-program.ps1
```

### 4. Run Tests
```powershell
.\run-tests.ps1
```

### 5. Start Game
```powershell
.\start-game.ps1
```

## 🎯 Quick Start Options

### Option A: Full Setup with Auto-Start
```powershell
.\complete-setup.ps1 -AutoStart
```

### Option B: Setup Without Tests
```powershell
.\complete-setup.ps1 -SkipTests
```

### Option C: Project Setup Only (Dependencies Already Installed)
```powershell
.\complete-setup.ps1 -SkipDependencies
```

## 🌐 Accessing the Game

After successful setup:
- **Game URL**: http://localhost:5173
- **Test Dashboard**: `testing/dashboard/index.html`
- **Security Report**: `security-audit-report.txt`
- **Setup Report**: `setup-report.md`

## 🔒 Security Features

This installation includes:
- ✅ Enhanced commitment scheme (HMAC-SHA512)
- ✅ Constant-time comparison (timing attack protection)
- ✅ Overflow protection in fee calculations
- ✅ Comprehensive security testing
- ✅ Automated security auditing

## 🧪 Testing

The setup includes comprehensive tests:
- **Security Tests**: Vulnerability detection
- **Performance Tests**: Response time benchmarks
- **Fairness Tests**: Game outcome distribution
- **Integration Tests**: End-to-end functionality
- **Mock Tests**: Simulated blockchain interactions

## 🛠️ Troubleshooting

### Common Issues

**"Execution policy" error:**
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

**"Command not found" after installation:**
- Restart PowerShell
- Run `refreshenv`

**Port 5173 already in use:**
```powershell
# Check what's using the port
netstat -ano | findstr :5173
# Kill the process or use different port
```

**Solana program deployment fails:**
```powershell
# Check balance and request airdrop
solana balance
solana airdrop 2
```

### Getting Help

1. Check `setup-report.md` for detailed logs
2. Review `security-audit-report.txt` for security issues
3. Open `testing/dashboard/index.html` for test results
4. Ensure all dependencies are properly installed

## 📁 What Gets Installed

### System Dependencies
- **Node.js 18+**: JavaScript runtime
- **Rust**: Solana program development
- **Solana CLI**: Blockchain tools
- **Anchor CLI**: Development framework

### Project Components
- **Frontend**: React + TypeScript + Vite
- **Backend**: Rust Solana program
- **Testing**: Comprehensive test suite
- **Monitoring**: Metrics and analytics

## 🎮 Game Features

- **Multi-player Games**: 3-4 players
- **Tournament Mode**: Bracket competitions
- **Auto-play**: Automated strategies
- **Token Support**: SOL and custom tokens
- **Real-time Updates**: Live game state
- **Security**: Commit-reveal fairness

## 📈 Next Steps

After installation:
1. **Play the Game**: Connect wallet and start playing
2. **Run Tournaments**: Organize bracket competitions
3. **Monitor Performance**: Check metrics dashboard
4. **Deploy to Mainnet**: Update configuration for production
5. **Customize**: Modify game rules and features

---

**🎉 Ready to play? Run `.\start-game.ps1` and open http://localhost:5173**
