# Solana RPS Game - PowerShell Installer
# This script installs everything needed and starts the game

Write-Host ""
Write-Host "==========================================" -ForegroundColor Green
Write-Host "    SOLANA RPS GAME - AUTO INSTALLER" -ForegroundColor Green
Write-Host "==========================================" -ForegroundColor Green
Write-Host ""

Write-Host "Installing everything needed for the Solana RPS Game..." -ForegroundColor Yellow
Write-Host "This will take 5-10 minutes on first run." -ForegroundColor Yellow
Write-Host ""

# Function to check if running as administrator
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# Check admin privileges
if (-not (Test-Administrator)) {
    Write-Host "⚠️  Not running as Administrator. Some installations may fail." -ForegroundColor Yellow
    Write-Host "For best results, right-click and 'Run as Administrator'" -ForegroundColor Yellow
    Write-Host ""
}

# Function to check if command exists
function Test-Command($command) {
    try {
        Get-Command $command -ErrorAction Stop
        return $true
    } catch {
        return $false
    }
}

# Create logs directory
if (-not (Test-Path "install-logs")) {
    New-Item -ItemType Directory -Path "install-logs" | Out-Null
}

Write-Host "[1/10] Setting up package manager..." -ForegroundColor Blue

# Install Chocolatey if not present
if (-not (Test-Command "choco")) {
    Write-Host "Installing Chocolatey package manager..." -ForegroundColor White
    try {
        Set-ExecutionPolicy Bypass -Scope Process -Force
        [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
        Invoke-Expression ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
        $env:PATH += ";$env:ALLUSERSPROFILE\chocolatey\bin"
        Write-Host "✓ Chocolatey installed" -ForegroundColor Green
    } catch {
        Write-Host "⚠️  Chocolatey installation had issues, continuing..." -ForegroundColor Yellow
    }
} else {
    Write-Host "✓ Chocolatey already installed" -ForegroundColor Green
}

Write-Host "[2/10] Installing Node.js..." -ForegroundColor Blue

# Install Node.js
if (-not (Test-Command "node")) {
    Write-Host "Installing Node.js..." -ForegroundColor White
    try {
        if (Test-Command "choco") {
            choco install nodejs -y | Out-File "install-logs\nodejs.log"
        } else {
            # Direct download method
            $nodeUrl = "https://nodejs.org/dist/v20.10.0/node-v20.10.0-x64.msi"
            $nodeInstaller = "node-installer.msi"
            Invoke-WebRequest -Uri $nodeUrl -OutFile $nodeInstaller
            Start-Process msiexec.exe -ArgumentList "/i $nodeInstaller /quiet /norestart" -Wait
            Remove-Item $nodeInstaller -Force
        }
        # Refresh PATH
        $env:PATH = [System.Environment]::GetEnvironmentVariable("PATH", "Machine") + ";" + [System.Environment]::GetEnvironmentVariable("PATH", "User")
        Write-Host "✓ Node.js installed" -ForegroundColor Green
    } catch {
        Write-Host "⚠️  Node.js installation had issues" -ForegroundColor Yellow
    }
} else {
    Write-Host "✓ Node.js already installed" -ForegroundColor Green
}

Write-Host "[3/10] Installing Git..." -ForegroundColor Blue

# Install Git
if (-not (Test-Command "git")) {
    Write-Host "Installing Git..." -ForegroundColor White
    try {
        if (Test-Command "choco") {
            choco install git -y | Out-File "install-logs\git.log"
        } else {
            # Direct download method
            $gitUrl = "https://github.com/git-for-windows/git/releases/download/v2.42.0.windows.2/Git-********-64-bit.exe"
            $gitInstaller = "git-installer.exe"
            Invoke-WebRequest -Uri $gitUrl -OutFile $gitInstaller
            Start-Process $gitInstaller -ArgumentList "/VERYSILENT /NORESTART" -Wait
            Remove-Item $gitInstaller -Force
        }
        $env:PATH = [System.Environment]::GetEnvironmentVariable("PATH", "Machine") + ";" + [System.Environment]::GetEnvironmentVariable("PATH", "User")
        Write-Host "✓ Git installed" -ForegroundColor Green
    } catch {
        Write-Host "⚠️  Git installation had issues" -ForegroundColor Yellow
    }
} else {
    Write-Host "✓ Git already installed" -ForegroundColor Green
}

Write-Host "[4/10] Installing Rust..." -ForegroundColor Blue

# Install Rust
if (-not (Test-Command "rustc")) {
    Write-Host "Installing Rust..." -ForegroundColor White
    try {
        $rustInstaller = "rustup-init.exe"
        Invoke-WebRequest -Uri "https://win.rustup.rs/" -OutFile $rustInstaller
        Start-Process $rustInstaller -ArgumentList "-y --default-toolchain stable" -Wait
        Remove-Item $rustInstaller -Force
        $env:PATH += ";$env:USERPROFILE\.cargo\bin"
        Write-Host "✓ Rust installed" -ForegroundColor Green
    } catch {
        Write-Host "⚠️  Rust installation had issues" -ForegroundColor Yellow
    }
} else {
    Write-Host "✓ Rust already installed" -ForegroundColor Green
}

Write-Host "[5/10] Installing Solana CLI..." -ForegroundColor Blue

# Install Solana CLI
if (-not (Test-Command "solana")) {
    Write-Host "Installing Solana CLI..." -ForegroundColor White
    try {
        $solanaInstaller = "solana-installer.exe"
        Invoke-WebRequest -Uri "https://release.solana.com/v1.18.22/solana-install-init-x86_64-pc-windows-msvc.exe" -OutFile $solanaInstaller
        Start-Process $solanaInstaller -ArgumentList "v1.18.22" -Wait
        Remove-Item $solanaInstaller -Force
        $env:PATH += ";$env:USERPROFILE\.local\share\solana\install\active_release\bin"
        Write-Host "✓ Solana CLI installed" -ForegroundColor Green
    } catch {
        Write-Host "⚠️  Solana CLI installation had issues" -ForegroundColor Yellow
    }
} else {
    Write-Host "✓ Solana CLI already installed" -ForegroundColor Green
}

Write-Host "[6/10] Configuring environment..." -ForegroundColor Blue

# Create environment files
if (-not (Test-Path ".env")) {
    @"
# Solana RPS Game Configuration
VITE_RPC_ENDPOINT=https://api.devnet.solana.com
VITE_RPS_PROGRAM_ID=7Y9dRMY6V9cmVkXNFrHeUZmYf2tAV5wSVFcYyD5bLQpZ
VITE_RPS_TOKEN_MINT=
VITE_FEE_COLLECTOR_ACCOUNT=FeeKHhL1CcJCyd82xextWTbBT5jGzVQwXVQKNjHV8SDD
"@ | Out-File ".env" -Encoding UTF8
}

if (-not (Test-Path "frontend\.env")) {
    Copy-Item ".env" "frontend\.env"
}

Write-Host "✓ Environment configured" -ForegroundColor Green

Write-Host "[7/10] Setting up Solana wallet..." -ForegroundColor Blue

# Setup Solana wallet
$solanaConfigDir = "$env:USERPROFILE\.config\solana"
if (-not (Test-Path $solanaConfigDir)) {
    New-Item -ItemType Directory -Path $solanaConfigDir -Force | Out-Null
}

if (-not (Test-Path "$solanaConfigDir\id.json")) {
    try {
        solana-keygen new --no-bip39-passphrase --silent --outfile "$solanaConfigDir\id.json" | Out-File "install-logs\wallet.log"
        Write-Host "✓ Wallet created" -ForegroundColor Green
    } catch {
        Write-Host "⚠️  Wallet creation had issues" -ForegroundColor Yellow
    }
} else {
    Write-Host "✓ Wallet already exists" -ForegroundColor Green
}

# Configure Solana for devnet
try {
    solana config set --url https://api.devnet.solana.com | Out-File "install-logs\config.log"
} catch {
    Write-Host "⚠️  Solana configuration had issues" -ForegroundColor Yellow
}

Write-Host "[8/10] Installing project dependencies..." -ForegroundColor Blue

# Install frontend dependencies
if (Test-Path "frontend\package.json") {
    Set-Location "frontend"
    try {
        npm install --legacy-peer-deps | Out-File "..\install-logs\npm-frontend.log"
        Write-Host "✓ Frontend dependencies installed" -ForegroundColor Green
    } catch {
        Write-Host "⚠️  Frontend dependencies had issues" -ForegroundColor Yellow
    }
    Set-Location ".."
}

Write-Host "[9/10] Starting blockchain..." -ForegroundColor Blue

# Start Solana test validator
try {
    $validatorRunning = $false
    try {
        solana cluster-version | Out-Null
        $validatorRunning = $true
    } catch {
        # Validator not running
    }
    
    if (-not $validatorRunning) {
        Write-Host "Starting Solana test validator..." -ForegroundColor White
        Start-Process -FilePath "solana-test-validator" -WindowStyle Minimized
        Start-Sleep -Seconds 15
        Write-Host "✓ Blockchain started" -ForegroundColor Green
    } else {
        Write-Host "✓ Blockchain already running" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️  Blockchain startup had issues" -ForegroundColor Yellow
}

# Get test SOL
try {
    solana airdrop 2 | Out-File "install-logs\airdrop.log"
} catch {
    Write-Host "⚠️  SOL airdrop had issues" -ForegroundColor Yellow
}

Write-Host "[10/10] Starting game..." -ForegroundColor Blue

# Start frontend
Set-Location "frontend"
Start-Process -FilePath "cmd" -ArgumentList "/c npm run dev" -WindowStyle Normal
Set-Location ".."

Write-Host ""
Write-Host "==========================================" -ForegroundColor Green
Write-Host "        INSTALLATION COMPLETE!" -ForegroundColor Green
Write-Host "==========================================" -ForegroundColor Green
Write-Host ""

Write-Host "✓ All dependencies installed" -ForegroundColor Green
Write-Host "✓ Blockchain running" -ForegroundColor Green
Write-Host "✓ Game server starting" -ForegroundColor Green
Write-Host ""

Write-Host "🎮 GAME URL: " -NoNewline -ForegroundColor Yellow
Write-Host "http://localhost:5173" -ForegroundColor Cyan
Write-Host ""

Write-Host "Opening game in browser..." -ForegroundColor White
Start-Sleep -Seconds 5
Start-Process "http://localhost:5173"

Write-Host ""
Write-Host "NEXT STEPS:" -ForegroundColor Yellow
Write-Host "1. Install Phantom wallet: https://phantom.app/" -ForegroundColor White
Write-Host "2. Switch wallet to 'Devnet'" -ForegroundColor White
Write-Host "3. Get test SOL: https://faucet.solana.com/" -ForegroundColor White
Write-Host "4. Connect wallet in the game" -ForegroundColor White
Write-Host "5. Start playing!" -ForegroundColor White
Write-Host ""

Write-Host "If the game doesn't load immediately, wait 30 seconds and refresh." -ForegroundColor Gray
Write-Host ""

Read-Host "Press Enter to exit"
