# 🎮 Solana RPS Game - Quick Start Guide

## 🚀 No PowerShell Required!

You can set up this game using **Command Prompt** (cmd) - no PowerShell needed!

## ⚡ Super Quick Setup

1. **Open Command Prompt as Administrator**
   - Press `Win + R`
   - Type `cmd`
   - Press `Ctrl + Shift + Enter` (to run as admin)

2. **Navigate to the game folder**
   ```cmd
   cd C:\Users\<USER>\Downloads\solana-rps-game1\solana-rps-game1
   ```

3. **Run the setup**
   ```cmd
   setup.bat
   ```

4. **Deploy the program**
   ```cmd
   deploy.bat
   ```

5. **Start the game**
   ```cmd
   start.bat
   ```

6. **Open your browser to**: http://localhost:5173

## 📋 What Each Script Does

### `setup.bat` - Complete Installation
- ✅ Installs Node.js, Rust, Solana CLI
- ✅ Configures project environment
- ✅ Installs all dependencies
- ✅ Builds the Solana program

### `deploy.bat` - Program Deployment
- ✅ Builds the Solana smart contract
- ✅ Deploys to local/devnet
- ✅ Updates configuration files

### `start.bat` - Game Launcher
- ✅ Starts Solana test validator
- ✅ Launches the frontend
- ✅ Opens the game at http://localhost:5173

### `test.bat` - Run Tests
- ✅ Comprehensive security tests
- ✅ Performance benchmarks
- ✅ Game functionality tests
- ✅ Generates test dashboard

## 🔧 Alternative Methods

### Method 1: Command Prompt (Recommended)
```cmd
# Run these one by one:
setup.bat
deploy.bat
start.bat
```

### Method 2: PowerShell (If you prefer)
```powershell
# Run this for complete automated setup:
.\complete-setup.ps1 -AutoStart
```

### Method 3: Manual Installation
```cmd
# Install dependencies manually:
# 1. Download Node.js from https://nodejs.org/
# 2. Download Rust from https://rustup.rs/
# 3. Download Solana CLI from https://docs.solana.com/cli/install-solana-cli-tools
# 4. Then run:
setup.bat
```

## 🎯 What You Get

After setup, you'll have:
- **🎮 Full Game**: Rock Paper Scissors on Solana
- **🏆 Tournaments**: Bracket-style competitions
- **🤖 Auto-play**: Automated gameplay
- **🔒 Security**: Enhanced commit-reveal scheme
- **📊 Testing**: Comprehensive test suite
- **📈 Monitoring**: Performance metrics

## 🌐 Game Features

- **Multi-player**: 3-4 player games
- **Blockchain**: Runs on Solana devnet
- **Wallets**: Phantom, Solflare support
- **Tokens**: SOL and custom RPS tokens
- **Fair Play**: Cryptographic commitment scheme
- **Real-time**: Live game updates

## 🛠️ Troubleshooting

### "Command not found" errors
```cmd
# Restart Command Prompt after installation
# Or manually add to PATH
```

### Port already in use
```cmd
# Check what's using port 5173
netstat -ano | findstr :5173
# Kill the process or restart computer
```

### Solana program deployment fails
```cmd
# Check balance and request airdrop
solana balance
solana airdrop 2
```

### Frontend won't start
```cmd
# Reinstall dependencies
cd frontend
npm install --legacy-peer-deps
cd ..
start.bat
```

## 📁 File Structure

```
solana-rps-game1/
├── setup.bat          # Main setup script
├── start.bat          # Game launcher
├── deploy.bat         # Program deployment
├── test.bat           # Test runner
├── frontend/          # React game interface
├── backend/           # Solana smart contract
└── testing/           # Test suite
```

## 🎉 Success!

Once setup is complete:
1. **Game URL**: http://localhost:5173
2. **Connect your wallet** (Phantom recommended)
3. **Start playing** Rock Paper Scissors!

## 💡 Tips

- **Keep the validator running** while playing
- **Use devnet** for testing (free SOL)
- **Check test dashboard** for security reports
- **Run tests** to verify everything works

---

**🎮 Ready to play? Just run `setup.bat` and you're good to go!**

No PowerShell, no complex commands - just simple batch files that work on any Windows computer! 🚀
