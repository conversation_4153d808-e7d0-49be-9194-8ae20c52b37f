# 🎮 Solana Rock Paper Scissors - FIXED!

## 🚀 INSTANT FIX - Just Double-Click This:

**`INSTALL-NOW.bat`** ← Double-click this file

That's it! It will:
- ✅ Install everything needed
- ✅ Start the blockchain
- ✅ Start the game
- ✅ Open http://localhost:5173

## 🎯 If You Get "localhost refused to connect":

**Double-click: `START-GAME.bat`**

## 📱 To Play the Game:

1. **Install Phantom Wallet**:
   - Go to: https://phantom.app/
   - Install browser extension
   - Create wallet
   - Switch to "Devnet"

2. **Get Test SOL**:
   - Go to: https://faucet.solana.com/
   - Paste your wallet address
   - Request SOL

3. **Play**:
   - Open: http://localhost:5173
   - Connect wallet
   - Start playing!

## 🛠️ Files You Need:

- **`INSTALL-NOW.bat`** - Fixes everything
- **`START-GAME.bat`** - Starts the game
- That's it!

## ❓ Still Having Issues?

1. **Run as Administrator**:
   - Right-click `INSTALL-NOW.bat`
   - Choose "Run as administrator"

2. **Wait for Installation**:
   - First time takes 5-10 minutes
   - Installing Node.js, <PERSON><PERSON>, Solana CLI

3. **Check Browser**:
   - Game runs at: http://localhost:5173
   - Wait 30 seconds if it doesn't load

## 🎮 Game Features:

- **Multi-player**: 3-4 players
- **Tournaments**: Bracket competitions  
- **Auto-play**: Automated strategies
- **Secure**: Blockchain-based fairness
- **Free**: Uses test SOL (not real money)

---

**Just double-click `INSTALL-NOW.bat` and you're ready to play!** 🚀
