@echo off
REM Simple batch file to run the PowerShell installer

title Solana RPS Game Installer

echo.
echo ==========================================
echo    SOLANA RPS GAME - INSTALLER
echo ==========================================
echo.

echo This will install everything needed for the game.
echo.

REM Check if PowerShell is available
powershell -Command "Write-Host 'PowerShell is available'" >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: PowerShell is not available.
    echo Please install PowerShell or use Windows 10/11.
    pause
    exit /b 1
)

echo Running PowerShell installer...
echo.

REM Run the PowerShell script
powershell -ExecutionPolicy Bypass -File "Install-Game.ps1"

if %errorLevel% neq 0 (
    echo.
    echo Installation may have had issues.
    echo Check the install-logs folder for details.
    echo.
    pause
) else (
    echo.
    echo Installation completed successfully!
    echo Game should be opening in your browser.
    echo.
)

pause
