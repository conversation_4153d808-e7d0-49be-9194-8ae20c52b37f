# 🎮 Solana RPS Game - Complete Setup Guide

This guide will help you set up the Solana Rock Paper Scissors game on a fresh Windows installation.

## 🚀 Quick Start (Automated Setup)

For a fresh Windows installation, run these commands in PowerShell **as Administrator**:

```powershell
# 1. Run the Windows setup script (installs all dependencies)
.\setup-windows.ps1

# 2. Restart your terminal, then run project setup
.\setup-project.ps1

# 3. Deploy the Solana program
.\deploy-program.ps1

# 4. Run comprehensive tests
.\run-tests.ps1

# 5. Start the game
.\start-game.ps1
```

## 📋 What Gets Installed

### System Dependencies
- **Node.js** (v18+) - JavaScript runtime for frontend
- **Rust** - Programming language for Solana programs
- **Solana CLI** - Blockchain development tools
- **Anchor CLI** - Solana development framework
- **Git** - Version control (if not present)

### Project Dependencies
- **Frontend**: React, TypeScript, Vite, Tailwind CSS
- **Backend**: Anchor framework, SPL Token support
- **Testing**: Comprehensive test suite with security checks

## 🔧 Manual Setup (Alternative)

If you prefer manual installation or encounter issues:

### 1. Install System Dependencies

#### Node.js
```powershell
# Download from https://nodejs.org/ or use Chocolatey:
choco install nodejs
```

#### Rust
```powershell
# Download from https://rustup.rs/ or use Chocolatey:
choco install rust
```

#### Solana CLI
```powershell
# Download installer from https://docs.solana.com/cli/install-solana-cli-tools
# Or use the direct download:
Invoke-WebRequest -Uri "https://release.solana.com/v1.18.22/solana-install-init-x86_64-pc-windows-msvc.exe" -OutFile "solana-install.exe"
.\solana-install.exe
```

### 2. Configure Solana
```powershell
# Set up Solana configuration
solana config set --url https://api.devnet.solana.com
solana-keygen new --no-bip39-passphrase
```

### 3. Install Project Dependencies
```powershell
# Root dependencies
npm install

# Frontend dependencies
cd frontend
npm install --legacy-peer-deps
cd ..

# Testing dependencies
cd testing
npm install
cd ..

# Backend monitoring
cd backend/monitoring
npm install
cd ../..
```

### 4. Build Solana Program
```powershell
cd backend/solana-program
anchor build  # or cargo build-bpf
cd ../..
```

## 🎯 Testing the Setup

### Run All Tests
```powershell
.\run-tests.ps1
```

### Individual Test Categories
```powershell
cd testing

# Basic functionality
npm run test-basic

# Security tests
npm run test-mock-security

# Performance tests
npm run test-performance

# Fairness tests
npm run test-mock-fairness

# Generate test dashboard
npm run generate-dashboard
```

## 🌐 Starting the Game

### Option 1: Full Environment (Recommended)
```powershell
.\start-game.ps1
```
This starts the Solana test validator and frontend automatically.

### Option 2: Manual Start
```powershell
# Terminal 1: Start Solana test validator
solana-test-validator

# Terminal 2: Start frontend
cd frontend
npm run dev
```

The game will be available at: **http://localhost:5173**

## 🔒 Security Features

This implementation includes several security enhancements:

### Smart Contract Security
- **Enhanced Commitment Scheme**: HMAC-SHA512 with 32-byte salt
- **Constant-Time Comparison**: Prevents timing attacks
- **Fee Validation**: Prevents overflow and excessive fees
- **Access Control**: Proper signer verification
- **Parameter Validation**: Bounds checking on all inputs

### Frontend Security
- **Buffer Polyfills**: Proper browser compatibility
- **Error Boundaries**: Graceful error handling
- **Rate Limiting**: Prevents spam transactions
- **Input Validation**: Client-side parameter checking

## 🧪 Test Results

After running tests, check:
- **Test Dashboard**: `testing/dashboard/index.html`
- **Test Results**: `testing/results/` directory
- **Security Report**: Generated by security tests

## 🛠️ Troubleshooting

### Common Issues

#### "Command not found" errors
- Restart your terminal after installation
- Check if tools are in your PATH
- Run `refreshenv` in PowerShell

#### Frontend build errors
- Delete `node_modules` and run `npm install --legacy-peer-deps`
- Check Node.js version (should be 16+)

#### Solana program deployment fails
- Check your SOL balance: `solana balance`
- Request airdrop: `solana airdrop 2`
- Verify network: `solana config get`

#### Port already in use
- Check if another instance is running
- Use different port: `npm run dev -- --port 3000`

### Getting Help

1. Check the test dashboard for detailed error reports
2. Review logs in `testing/results/`
3. Ensure all dependencies are properly installed
4. Try restarting your computer if issues persist

## 📁 Project Structure

```
solana-rps-game1/
├── backend/
│   ├── solana-program/     # Rust smart contract
│   └── monitoring/         # Metrics and monitoring
├── frontend/               # React application
├── testing/               # Comprehensive test suite
├── setup-windows.ps1      # Windows dependency installer
├── setup-project.ps1      # Project configuration
├── deploy-program.ps1     # Program deployment
├── run-tests.ps1         # Test runner
└── start-game.ps1        # Game launcher
```

## 🎮 Game Features

- **Multi-player**: 3-4 player games
- **Tournaments**: Bracket-style competitions
- **Auto-play**: Automated gameplay with strategies
- **Token Support**: SOL and custom RPS tokens
- **Security**: Commit-reveal scheme for fair play
- **Monitoring**: Real-time metrics and analytics

## 📈 Next Steps

1. **Deploy to Mainnet**: Update RPC endpoint and deploy
2. **Custom Tokens**: Create and configure RPS token
3. **Monitoring**: Set up Grafana dashboards
4. **Scaling**: Configure multiple RPC providers

---

**🎉 Enjoy playing the Solana Rock Paper Scissors game!**
