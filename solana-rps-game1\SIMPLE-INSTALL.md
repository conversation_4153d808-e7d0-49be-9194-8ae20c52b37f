# 🎮 Solana RPS Game - FIXED INSTALLER

## 🚀 WORKING SOLUTION:

**Right-click `RUN-INSTALLER.bat` → "Run as administrator"**

This will:
- ✅ Run the PowerShell installer properly
- ✅ Install all dependencies automatically
- ✅ Start the game
- ✅ Open http://localhost:5173

## 📋 Alternative Methods:

### Method 1: PowerShell Direct
```powershell
# Right-click PowerShell → "Run as administrator"
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
.\Install-Game.ps1
```

### Method 2: Manual PowerShell
```powershell
# Open PowerShell as administrator
cd "C:\Users\<USER>\Downloads\solana-rps-game1\solana-rps-game1"
powershell -ExecutionPolicy Bypass -File "Install-Game.ps1"
```

## 🔧 What Was Wrong:

The batch file was trying to call `AUTO-INSTALL.bat` which didn't exist in the same directory. The PowerShell version fixes this by having everything in one file.

## 📁 Files to Use:

- **`RUN-INSTALLER.bat`** ← **Use this one!**
- **`Install-Game.ps1`** ← PowerShell installer
- **`START-GAME.bat`** ← Restart game later

## 🎯 After Installation:

1. **Install Phantom wallet**: https://phantom.app/
2. **Switch to Devnet** in wallet settings
3. **Get test SOL**: https://faucet.solana.com/
4. **Connect wallet** in the game
5. **Start playing!**

## ❓ Still Having Issues?

**Try this in PowerShell as Administrator:**
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser -Force
cd "C:\Users\<USER>\Downloads\solana-rps-game1\solana-rps-game1"
.\Install-Game.ps1
```

---

**The PowerShell installer is much more reliable and handles all the dependencies properly!** 🚀
