@echo off
REM Quick start script for the game

title Starting Solana RPS Game...

echo.
echo ==========================================
echo        STARTING SOLANA RPS GAME
echo ==========================================
echo.

REM Check if dependencies exist
where node >nul 2>&1
if %errorLevel% neq 0 (
    echo Node.js not found. Running installer...
    call INSTALL-NOW.bat
    exit /b
)

REM Start validator if not running
echo Checking blockchain...
solana cluster-version >nul 2>&1
if %errorLevel% neq 0 (
    echo Starting Solana validator...
    start "Solana Validator" /min cmd /c "solana-test-validator"
    echo Waiting for validator...
    timeout /t 10 /nobreak >nul
)

REM Start frontend
echo Starting game server...
cd frontend

REM Install deps if missing
if not exist "node_modules" (
    echo Installing dependencies...
    call npm install --legacy-peer-deps
)

REM Start the game
echo Game starting at http://localhost:5173
start "Solana RPS Game" cmd /c "npm run dev"

echo.
echo ✓ Game is starting...
echo ✓ Opening browser...
timeout /t 5 /nobreak >nul
start http://localhost:5173

echo.
echo Game is running!
echo Keep this window open while playing.
echo.
pause
