@echo off
REM Terminal-based installer for Solana RPS Game

title Installing Solana RPS Game...

echo.
echo ==========================================
echo    SOLANA RPS GAME - TERMINAL INSTALLER
echo ==========================================
echo.

echo I'll install everything using terminal commands.
echo This will take a few minutes...
echo.

REM Navigate to the correct directory
cd /d "%~dp0"
if exist "solana-rps-game1" cd solana-rps-game1

echo Current directory: %CD%
echo.

REM Step 1: Install Node.js if not present
echo [1/6] Checking Node.js...
where node >nul 2>&1
if %errorLevel% neq 0 (
    echo Installing Node.js...
    if exist "..\node-installer.msi" (
        echo Found Node.js installer, installing...
        msiexec /i "..\node-installer.msi" /quiet /norestart
        echo Waiting for installation to complete...
        timeout /t 30 /nobreak >nul
    ) else (
        echo Downloading Node.js...
        powershell -Command "Invoke-WebRequest -Uri 'https://nodejs.org/dist/v20.10.0/node-v20.10.0-x64.msi' -OutFile 'node-installer.msi'"
        echo Installing Node.js...
        msiexec /i "node-installer.msi" /quiet /norestart
        timeout /t 30 /nobreak >nul
        del "node-installer.msi"
    )
    
    REM Add Node.js to PATH for current session
    set "PATH=%PATH%;C:\Program Files\nodejs"
    
    echo Testing Node.js installation...
    "C:\Program Files\nodejs\node.exe" --version
    if %errorLevel% == 0 (
        echo ✓ Node.js installed successfully
    ) else (
        echo ⚠ Node.js installation may need a restart
    )
) else (
    echo ✓ Node.js already installed
    node --version
)

echo.

REM Step 2: Create environment file
echo [2/6] Setting up environment...
if not exist ".env" (
    echo Creating .env file...
    echo # Solana RPS Game Configuration > .env
    echo VITE_RPC_ENDPOINT=https://api.devnet.solana.com >> .env
    echo VITE_RPS_PROGRAM_ID=7Y9dRMY6V9cmVkXNFrHeUZmYf2tAV5wSVFcYyD5bLQpZ >> .env
    echo VITE_RPS_TOKEN_MINT= >> .env
    echo VITE_FEE_COLLECTOR_ACCOUNT=FeeKHhL1CcJCyd82xextWTbBT5jGzVQwXVQKNjHV8SDD >> .env
    echo ✓ Environment file created
) else (
    echo ✓ Environment file already exists
)

if not exist "frontend\.env" (
    copy .env frontend\.env >nul
    echo ✓ Frontend environment file created
)

echo.

REM Step 3: Install frontend dependencies
echo [3/6] Installing frontend dependencies...
if exist "frontend\package.json" (
    cd frontend
    
    REM Try with Node.js in PATH
    set "PATH=%PATH%;C:\Program Files\nodejs"
    
    echo Installing npm packages...
    "C:\Program Files\nodejs\npm.cmd" install --legacy-peer-deps
    if %errorLevel% == 0 (
        echo ✓ Frontend dependencies installed
    ) else (
        echo ⚠ Frontend dependency installation had issues
    )
    
    cd ..
) else (
    echo ⚠ Frontend package.json not found
)

echo.

REM Step 4: Install Rust (optional for now)
echo [4/6] Checking Rust...
where rustc >nul 2>&1
if %errorLevel% neq 0 (
    echo Rust not found. You can install it later from: https://rustup.rs/
    echo ⚠ Rust not installed (optional for basic game functionality)
) else (
    echo ✓ Rust already installed
    rustc --version
)

echo.

REM Step 5: Install Solana CLI (optional for now)
echo [5/6] Checking Solana CLI...
where solana >nul 2>&1
if %errorLevel% neq 0 (
    echo Solana CLI not found. You can install it later.
    echo ⚠ Solana CLI not installed (optional for basic game functionality)
) else (
    echo ✓ Solana CLI already installed
    solana --version
)

echo.

REM Step 6: Start the game
echo [6/6] Starting the game...

REM Check if we can start the frontend
if exist "frontend\node_modules" (
    echo Starting frontend server...
    cd frontend
    
    REM Start the development server
    echo Game will be available at: http://localhost:5173
    echo.
    echo Starting server... (this may take a moment)
    
    start "Solana RPS Game" cmd /c ""C:\Program Files\nodejs\npm.cmd" run dev"
    
    echo Waiting for server to start...
    timeout /t 10 /nobreak >nul
    
    echo Opening browser...
    start http://localhost:5173
    
    cd ..
    
    echo.
    echo ==========================================
    echo           GAME STARTED!
    echo ==========================================
    echo.
    echo ✓ Frontend server running
    echo ✓ Game available at: http://localhost:5173
    echo.
    echo NEXT STEPS:
    echo 1. Install Phantom wallet: https://phantom.app/
    echo 2. Switch wallet to "Devnet"
    echo 3. Get test SOL: https://faucet.solana.com/
    echo 4. Connect wallet in the game
    echo 5. Start playing!
    echo.
    echo NOTE: For full functionality, you may want to install:
    echo - Rust: https://rustup.rs/
    echo - Solana CLI: https://docs.solana.com/cli/install-solana-cli-tools
    echo.
) else (
    echo ❌ Frontend dependencies not installed properly
    echo.
    echo MANUAL STEPS:
    echo 1. Open Command Prompt as Administrator
    echo 2. Navigate to: %CD%\frontend
    echo 3. Run: "C:\Program Files\nodejs\npm.cmd" install --legacy-peer-deps
    echo 4. Run: "C:\Program Files\nodejs\npm.cmd" run dev
    echo 5. Open: http://localhost:5173
)

echo.
echo Installation log completed.
pause
