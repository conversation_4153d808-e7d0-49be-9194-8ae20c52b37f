# Solana Network Configuration
# Options: "http://localhost:8899", "https://api.devnet.solana.com", "https://api.mainnet-beta.solana.com"
REACT_APP_SOLANA_RPC_ENDPOINT="https://api.devnet.solana.com"

# Solana Program ID for the Rock Paper Scissors Game
# Replace with your actual deployed program ID
REACT_APP_RPS_PROGRAM_ID="RPS111111111111111111111111111111111111111"

# RPS Token Configuration
# Replace with your actual deployed RPS token mint address
REACT_APP_RPS_TOKEN_MINT_ADDRESS="RPSTokenMint111111111111111111111111111111"

# Decimals for the RPS Token (e.g., 6 for USDC-like tokens)
REACT_APP_RPS_TOKEN_DECIMALS=6

# Optional: Default Wager Amount (in SOL or RPS token units, depending on UI handling)
# REACT_APP_DEFAULT_WAGER_AMOUNT="0.1"

# Optional: Admin Authority Public Key (if needed for client-side checks, though primarily handled by program)
# REACT_APP_ADMIN_AUTHORITY_PUBKEY="AdminAuthorityPublicKeyGoesHere"

# Note: Environment variables in Create React App must be prefixed with REACT_APP_
# After setting these, you might need to restart your development server.
# For production builds, these variables should be set in your deployment environment.
