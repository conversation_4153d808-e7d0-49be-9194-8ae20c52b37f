/* 
 * Solana Rock Paper Scissors Game
 * Main Stylesheet
 */

/* Base styles and variables */
:root {
  --primary-color: #9945FF;
  --secondary-color: #14F195;
  --accent-color: #00C2FF;
  --background-color: #121212;
  --card-background: #1E1E1E;
  --text-color: #FFFFFF;
  --text-secondary: #AAAAAA;
  --success-color: #14F195;
  --error-color: #FF5252;
  --warning-color: #FFD600;
  --border-radius: 8px;
  --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  --transition: all 0.3s ease;
}

/* Global styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Inter', 'Roboto', -apple-system, BlinkMacSystemFont, sans-serif;
  background-color: var(--background-color);
  color: var(--text-color);
  line-height: 1.6;
}

button {
  cursor: pointer;
  font-family: inherit;
  border: none;
  border-radius: var(--border-radius);
  padding: 10px 16px;
  font-weight: 600;
  transition: var(--transition);
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

h1, h2, h3, h4 {
  margin-bottom: 16px;
  line-height: 1.2;
}

input {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--border-radius);
  color: var(--text-color);
  padding: 10px 16px;
  font-size: 16px;
  width: 100%;
  transition: var(--transition);
}

input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(153, 69, 255, 0.3);
}

/* Layout components */
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 30px;
}

.app-header h1 {
  margin-bottom: 0;
  color: var(--primary-color);
  font-size: 28px;
}

.app-main {
  flex: 1;
  width: 100%;
}

.app-footer {
  margin-top: 40px;
  padding: 20px 0;
  text-align: center;
  color: var(--text-secondary);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Wallet connection */
.connect-wallet-container {
  text-align: center;
  padding: 40px 20px;
  max-width: 600px;
  margin: 0 auto;
  background-color: var(--card-background);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

.connect-wallet-container h2 {
  color: var(--primary-color);
  margin-bottom: 20px;
}

.connect-wallet-container p {
  margin-bottom: 30px;
  color: var(--text-secondary);
}

.game-features {
  text-align: left;
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.game-features h3 {
  color: var(--accent-color);
}

.game-features ul {
  list-style-position: inside;
  margin-left: 10px;
}

.game-features li {
  margin-bottom: 8px;
}

/* User info section */
.user-info {
  display: flex;
  justify-content: space-between;
  background-color: var(--card-background);
  padding: 15px 20px;
  border-radius: var(--border-radius);
  margin-bottom: 30px;
  box-shadow: var(--box-shadow);
}

.user-info p {
  margin: 0;
}

.user-info strong {
  color: var(--accent-color);
}

/* Game container */
.game-container {
  width: 100%;
}

/* Create game section */
.create-game {
  background-color: var(--card-background);
  padding: 30px;
  border-radius: var(--border-radius);
  margin-bottom: 30px;
  box-shadow: var(--box-shadow);
}

.create-game h2 {
  color: var(--primary-color);
}

.wager-input {
  margin-bottom: 20px;
}

.wager-input label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

/* Games list */
.games-list {
  background-color: var(--card-background);
  padding: 30px;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

.games-list h2 {
  color: var(--primary-color);
  margin-bottom: 20px;
}

.games-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

/* Game card */
.game-card {
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: var(--border-radius);
  overflow: hidden;
  transition: var(--transition);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.game-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

.game-card-header {
  padding: 15px 20px;
  background-color: rgba(153, 69, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.game-card-header h3 {
  margin: 0;
  font-size: 18px;
  color: var(--primary-color);
}

.game-state {
  font-size: 14px;
  background-color: rgba(0, 194, 255, 0.2);
  color: var(--accent-color);
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 500;
}

.game-card-body {
  padding: 20px;
}

.game-card-body p {
  margin-bottom: 10px;
}

.game-card-body strong {
  color: var(--accent-color);
}

.game-card-footer {
  padding: 15px 20px;
  background-color: rgba(0, 0, 0, 0.2);
  text-align: center;
}

/* Active game */
.active-game {
  background-color: var(--card-background);
  padding: 30px;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

.active-game h2 {
  color: var(--primary-color);
  margin-bottom: 20px;
}

.game-info {
  background-color: rgba(255, 255, 255, 0.05);
  padding: 20px;
  border-radius: var(--border-radius);
  margin-bottom: 30px;
}

.game-info p {
  margin-bottom: 10px;
}

.game-info strong {
  color: var(--accent-color);
}

/* Move selection */
.move-selection {
  margin-bottom: 30px;
}

.move-selection h3 {
  color: var(--secondary-color);
  margin-bottom: 15px;
}

.move-buttons {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.move-button {
  flex: 1;
  padding: 15px;
  font-size: 16px;
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--text-color);
  border: 2px solid transparent;
  transition: var(--transition);
}

.move-button:hover {
  background-color: rgba(255, 255, 255, 0.15);
}

.move-button.selected {
  border-color: var(--secondary-color);
  background-color: rgba(20, 241, 149, 0.1);
}

/* Reveal move */
.reveal-move {
  margin-bottom: 30px;
  padding: 20px;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: var(--border-radius);
}

.reveal-move h3 {
  color: var(--secondary-color);
  margin-bottom: 15px;
}

/* Timeout claim */
.timeout-claim {
  margin-bottom: 30px;
  padding: 20px;
  background-color: rgba(255, 82, 82, 0.1);
  border-radius: var(--border-radius);
  border-left: 4px solid var(--error-color);
}

.timeout-claim h3 {
  color: var(--error-color);
  margin-bottom: 15px;
}

/* Buttons */
.action-button {
  background-color: var(--primary-color);
  color: white;
  padding: 12px 20px;
  font-size: 16px;
  width: 100%;
}

.action-button:hover:not(:disabled) {
  background-color: #8134e0;
  transform: translateY(-2px);
}

.secondary-button {
  background-color: transparent;
  border: 1px solid var(--primary-color);
  color: var(--primary-color);
  margin-top: 15px;
  width: 100%;
}

.secondary-button:hover {
  background-color: rgba(153, 69, 255, 0.1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .app-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .user-info {
    flex-direction: column;
    gap: 10px;
  }
  
  .move-buttons {
    flex-direction: column;
  }
  
  .games-grid {
    grid-template-columns: 1fr;
  }
}

/* Animation for loading states */
@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

.loading {
  animation: pulse 1.5s infinite;
}

/* Toast customization */
.Toastify__toast {
  border-radius: var(--border-radius);
}

.Toastify__toast--success {
  background-color: var(--success-color);
}

.Toastify__toast--error {
  background-color: var(--error-color);
}

.Toastify__toast--warning {
  background-color: var(--warning-color);
}
