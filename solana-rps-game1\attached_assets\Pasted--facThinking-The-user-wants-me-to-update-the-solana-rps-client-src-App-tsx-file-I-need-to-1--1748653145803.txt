<facThinking>The user wants me to update the `solana-rps-client/src/App.tsx` file.
I need to:
1.  **Integrate Routing**: Use a routing library (like `react-router-dom`) to create different pages/views for:
    *   Main Game Interface
    *   Admin Dashboard
    *   User Profile
2.  **Navigation Links**: Add links in the header or a dedicated navigation component to switch between these views.
3.  **Wallet-Based Authentication**:
    *   The core game logic already uses the connected wallet.
    *   The Admin Dashboard should check if the connected wallet is the admin authority.
    *   The User Profile page should display data associated with the connected wallet.
4.  **Token Selector Integration**:
    *   The `TokenSelector` component needs to be added to the game creation UI.
    *   The `createGame` and `joinGame` functions need to be updated to pass the selected `WagerCurrency` to the smart contract.
    *   The UI needs to correctly display wager amounts and balances for both SOL and RPS tokens.
5.  **Wallet Address as User Identifier**: This is inherent in Solana dApps. The connected `wallet.publicKey` will be the primary identifier. User profiles will be fetched/created based on this public key.

Let's break down the changes for `App.tsx`:

*   **Imports**: Add `react-router-dom` components (`BrowserRouter`, `Routes`, `Route`, `Link`, `Navigate`). Import `AdminDashboard` and `UserProfile` components. Import `TokenSelector` and `WagerCurrency` enum.
*   **Layout**: Create a main layout component that includes the header (with navigation) and a content area for the routes.
*   **Header**: Modify the `AppHeader` to include `Link` components for "Game", "Profile", and "Admin" (conditionally shown if the user is admin).
*   **`GameContent` Component**:
    *   Add state for `selectedCurrency` and pass it to `TokenSelector`.
    *   Modify `createGame` to:
        *   Accept `currency` as a parameter.
        *   Pass the `currency` to the `program.methods.createGame()` call.
        *   Include `playerTokenAccount` and `gameTokenAccount` in the `accounts` if `currency` is `RpsToken`. These will be `null` or derived if the contract handles ATA creation. For now, I'll assume the contract might need them passed if they exist, or it handles their creation. The IDL and program logic will dictate this.
        *   The `wagerAmount` needs to be handled correctly based on the token's decimals if it's an RPS token. For SOL, it's lamports.
    *   Modify `joinGame` similarly for `currency` and token accounts.
    *   Update UI elements to display currency type (SOL or RPS) for wagers.
    *   The `getGamePDA` might need to be adjusted if the game PDA depends on the currency or if token games use a different PDA structure (unlikely for this setup, but good to keep in mind).
    *   When fetching game details, ensure the `currency` field from the `GameAccount` is used to display wager information correctly.
*   **Admin Logic**:
    *   In the `AdminDashboard` route, potentially add a check: if the connected wallet is not the `adminData.authority`, redirect or show an "unauthorized" message. This logic is better placed within the `AdminDashboard` component itself for cleaner separation.
*   **User Profile Logic**:
    *   The `UserProfile` component will handle fetching and displaying its own data based on the connected `anchorWallet.publicKey`.
*   **Constants**:
    *   `RPS_TOKEN_MINT_ADDRESS` and `RPS_TOKEN_DECIMALS` will be needed for interacting with the RPS token.
*   **Token Account Handling**:
    *   For RPS token wagers, the frontend will need to know the player's and the game's associated token accounts (ATAs).
    *   The `getAssociatedTokenAddress` utility from `@solana/spl-token` will be used.
    *   The smart contract might create these ATAs if they don't exist, or the client might need to include instructions to create them in the transaction. The provided contract structure seems to handle this within the `handle_wager` and `distribute_prizes` functions by taking optional token accounts. The client should pass the correct ATAs.

Let's refine the `createGame` and `joinGame` account passing:
The smart contract's `CreateGame` and `JoinGame` contexts now include:
```rust
    /// Player's token account (only required for RPS token games)
    pub player_token_account: Option<Account<'info, TokenAccount>>,
    /// Game's token account (only required for RPS token games)
    pub game_token_account: Option<Account<'info, TokenAccount>>,
    pub token_program: Option<Program<'info, Token>>,
```
The client needs to derive these ATAs and pass them. If they are `None` in the Rust struct, it means they are optional in the instruction accounts.

The `revealMove` and `claimTimeout` also have token account fields now for prize/fee distribution.

I will structure the `App.tsx` with `BrowserRouter` and `Routes`.
The main `App` component will set up providers.
A new `Layout` component will contain the header and `Outlet` for routed content.
The `GameContent` component will be refactored to handle the game logic as before but with currency selection.
The `AdminDashboard` and `UserProfile` components will be rendered by their respective routes.
The `FEE_RECIPIENT` constant needs to be re-evaluated. The admin config now holds `sol_fee_recipient` and `rps_fee_recipient`. The client will need to fetch the admin config to know where to send fees if it's constructing transactions that directly involve fee transfers (though the program usually handles this internally after receiving funds). For `revealMove` and `claimTimeout`, the program now uses `admin_config` to get fee recipients. The client-side `FEE_RECIPIENT` constant might become obsolete or used differently. The `RevealMove` and `ClaimTimeout` contexts in the program now take `admin_config` and `fee_recipient` (which seems to be the SOL fee recipient from admin config, or the specific token fee account).

The `RevealMove` context in the program:
```rust
    #[account(mut)]
    pub fee_recipient: AccountInfo<'info>, // This is likely the SOL fee recipient from admin_config
    // ... token accounts for RPS ...
    pub fee_token_account: Option<Account<'info, TokenAccount>>, // This is the RPS fee recipient token account
```
The client will need to fetch `adminConfig.sol_fee_recipient` and the ATA for `adminConfig.rps_fee_recipient` with the RPS mint.

Let's start coding the `App.tsx` with these considerations.
I will need `react-router-dom` installed. The `package.json` provided earlier doesn't include it, so I'll assume it's added.
```json
"dependencies": {
    // ... other dependencies
    "react-router-dom": "^6.x.x"
  },
"devDependencies": {
    // ... other devDependencies
    "@types/react-router-dom": "^5.x.x" // or ^6.x.x depending on version
}
```
I'll use version 6 syntax for `react-router-dom`.
The `PROGRAM_ID` and `RPS_TOKEN_MINT_ADDRESS` need to be robustly defined. I'll use the IDL's metadata for `PROGRAM_ID` and a constant for the mint.
The `FEE_RECIPIENT` constant will be removed as fee recipients are now managed by the `admin_config` on-chain.
The `App.css` might need minor adjustments for new layout elements if any are introduced (like a dedicated nav bar).

The `GameContent` component will become the main interactive part for playing the game.
The `AdminDashboard` and `UserProfile` components are imported and routed.
The `TokenSelector` component is integrated into `GameContent`.
Wallet connection remains the primary way to identify and authenticate users.
The `getProgram` utility is central.
Error handling with `react-toastify` will be maintained.
State management for `adminData` will be added to determine if the "Admin" link should be shown and to pass relevant admin config (like fee recipients) to game functions if needed (though the program should mostly handle this internally now).
The `createGame` and `joinGame` methods will be updated to include `currency` and necessary token accounts.
The `revealMove` and `claimTimeout` methods will be updated to include necessary token accounts and potentially the admin config for fee recipient lookups if the program doesn't derive it internally from its own `admin_config` account. The current program structure for `RevealMove` and `ClaimTimeout` requires `fee_recipient` (for SOL) and `fee_token_account` (for RPS) to be passed, along with other player/game token accounts. The client will need to fetch the `admin_config` to get these fee recipient addresses.

Let's refine the `revealMove` and `claimTimeout` calls:
The program's `RevealMove` accounts:
```rust
    #[account(mut)]
    pub fee_recipient: AccountInfo<'info>, // SOL fee recipient from admin_config
    // ...
    pub fee_token_account: Option<Account<'info, TokenAccount>>, // RPS fee recipient token account
```
The client needs to fetch `adminConfig.sol_fee_recipient` and `adminConfig.rps_fee_recipient`. For the latter, it needs to derive the ATA.

This means `GameContent` will need access to `adminConfig` state.
I'll add a state for `adminConfig` in `GameContent` and fetch it.
This also means the `AdminDashboard` component will fetch this data, and `GameContent` might also need to fetch it or receive it via props if we want to avoid redundant fetches. For simplicity now, `GameContent` can fetch it.
The `getAssociatedTokenAddress` from `@solana/spl-token` is crucial here.
The `idl.metadata.address` is the correct way to get `PROGRAM_ID` if the IDL is up-to-date.
The `RPS_TOKEN_MINT_ADDRESS` should be a `PublicKey` instance.
The `RPS_TOKEN_DECIMALS` will be used for display and input conversion.
The `App.css` is not being modified in this step, only `App.tsx`.
The `UserProfile` component will be a new route.
The `AdminDashboard` component will be a new route.
Navigation links for these will be added.
The core game logic within `GameContent` will be adapted for token selection.
Wallet address is already used as the user's identifier via `wallet.publicKey`.
The `fee_recipient` in `revealMove` and `claimTimeout` needs to be the correct one based on game currency. The program seems to expect the SOL fee recipient for SOL games and the RPS token fee account for RPS games.
The `admin_config` account is now part of many instruction contexts in the program, so the client needs to pass it.
The `user_profile` account is also now optional in `createGame` and `joinGame`, and in `revealMove` and `claimTimeout` for both creator and joiner. The client should derive the PDA for the current user's profile and pass it if it exists or if the instruction expects it. The program seems to handle `Option<Account<'info, UserProfile>>`.
The client should probably attempt to initialize the user profile if it doesn't exist upon first interaction or on a dedicated profile page. The `UserProfile.tsx` component handles its own initialization. For game interactions, we can try to fetch and pass it.
The `getGamePDA` now uses `player.key()` as seed, which is standard.
The `RPS_TOKEN_MINT` in `token_integration.rs` is a string constant. The client should use the same.
The `FEE_RECIPIENT` constant is indeed obsolete for the client now.
The `program.account.adminConfig.fetch(adminConfigPDA)` will be used to get admin settings.
The `TokenSelector` component is imported and used.
The `WagerCurrency` enum is imported from `TokenSelector.tsx`.
The logic for `player_token_account` and `game_token_account` in `createGame` and `joinGame` needs to be conditional based on `currency`.
For `revealMove` and `claimTimeout`, the `creator_token_account`, `joiner_token_account`, `fee_token_account`, `game_token_account` also need to be handled conditionally.
The `admin_config` PDA needs to be passed to most instructions now.
The `user_profile` PDAs for creator and joiner also need to be passed where applicable.
The `TokenProgram.programId` is `TOKEN_PROGRAM_ID` from `@solana/spl-token`.
The `SystemProgram.programId` is `web3.SystemProgram.programId`.
The `RentProgram.programId` is `web3.SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` needs `allowOwnerOffCurve = true` if the owner is a PDA. For user-owned ATAs, it's `false` or default. Game PDA and Fee Vault PDA are off-curve.
The `RPS_TOKEN_MINT_ADDRESS` should be a `PublicKey`.
The `solana_rps.json` (IDL) must be correctly placed and imported.
The `App.css` is assumed to handle basic styling for new elements, or they'll be unstyled for now.
The `AdminDashboard` and `UserProfile` components are assumed to exist in `src/components/`.
The `TokenSelector` component is assumed to exist in `src/components/`.
The `WagerCurrency` enum is defined in `TokenSelector.tsx` and will be imported.
The `PROGRAM_ID` should be derived from the IDL to ensure it matches the deployed program.
The `RPS_TOKEN_MINT_ADDRESS` is critical and must be correct.
The `RPS_TOKEN_DECIMALS` is also critical for UI display and input.
The `createGame` and `joinGame` functions will now take `currency` as an argument.
The UI for wager input should clarify if it's SOL or RPS tokens.
The `activeGame` display should show the currency.
The `gamesList` display should show the currency for each game.
The `handle_wager` and `distribute_prizes` in the program now handle currency logic.
The client needs to provide the correct token accounts.
The `admin_config` PDA is fetched once and stored in state for use in various function calls.
The `user_profile` PDA for the current player is also fetched/derived.
For `revealMove` and `claimTimeout`, the profiles of *both* players might be needed if the program updates them. The program's `RevealMove` and `ClaimTimeout` contexts include `creator_profile` and `joiner_profile` as `Option<Account<'info, UserProfile>>`. The client should derive and pass these PDAs.
This makes the account lists for these instructions quite long.
The `getAssociatedTokenAddress` from `@solana/spl-token` is the modern way, not `Token.getAssociatedTokenAddress`.
The `TOKEN_PROGRAM_ID` is also from `@solana/spl-token`.
The `idl.metadata.address` is a good source for `PROGRAM_ID`.
The `AdminDashboard` and `UserProfile` components are not part of this file's generation, so their internal logic (like admin checks) is self-contained.
The navigation will be simple `Link` components.
The `WalletMultiButton` is already in the header.
The `ToastContainer` is already set up.
The `GameContent` component will house most of the game interaction logic.
The `useEffect` for fetching games and balance will remain.
The `getGamePDA` is specific to the game account, not user profiles or admin config.
User profile PDA: `[Buffer.from("user-profile"), userKey.toBuffer()]`
Admin config PDA: `[Buffer.from("admin")]`
Fee vault PDA: `[Buffer.from("fee-vault"), adminConfigKey.toBuffer()]`
The `program.account.gameAccount.all([...])` is used for fetching games.
The `program.account.adminConfig.fetch(adminConfigPDA)` for admin data.
The `program.account.userProfile.fetch(userProfilePDA)` for user profile.
The `App.css` is not being modified here.
The `RPS_TOKEN_MINT_ADDRESS` will be a `const` `PublicKey`.
The `RPS_TOKEN_DECIMALS` will be a `const` number.
The `useWallet` hook provides `publicKey` which is the user's identifier.
The `useAnchorWallet` is used for transaction signing.
The `useConnection` provides the connection.
The `WalletAdapterNetwork` and `clusterApiUrl` are used for endpoint configuration.
The `BN` from `@project-serum/anchor` is used for large numbers.
The `createHash` from `crypto` is used for move hashing.
The `LAMPORTS_PER_SOL` from `@solana/web3.js` is used for SOL conversion.
The `SystemProgram.programId` and `TOKEN_PROGRAM_ID` are used.
The `idl` is imported directly.
The structure will be:
`App` (providers)
  `BrowserRouter`
    `Layout` (header with nav, outlet)
      `Routes`
        `Route path="/" element={<GameContent />}`
        `Route path="/profile" element={<UserProfile />}`
        `Route path="/admin" element={<AdminRoute element={<AdminDashboard />} />}` (or similar protected route logic)
For simplicity, `AdminRoute` can just be a regular route, and `AdminDashboard` handles its own auth check.
The `refreshTrigger` state is a good pattern for forcing re-fetch.
The `loading` state is used for disabling buttons during operations.
The `activeGame` state manages the currently viewed/interacted game.
The `selectedMove`, `salt`, `moveHash` states are for the commit-reveal flow.
The `wagerAmount` state is for creating games.
New state for `selectedCurrency` and `adminConfigData`.
The `getAssociatedTokenAddress` is an async function.
The `PublicKey.findProgramAddressSync` is synchronous.
The `program.methods.instructionName(...).accounts({...}).rpc()` pattern is standard.
The `idl as any` cast is common for Anchor IDLs with TypeScript.
The `Game`, `GameState`, `GameMove`, `GameResult` interfaces/enums match the contract.
The `AdminData` interface will be added for the admin config.
The `UserProfileData` interface will be added for user profiles.
The `TokenSelector` and `WagerCurrency` enum will be imported from `./components/TokenSelector`.
The `AdminDashboard` from `./components/AdminDashboard`.
The `UserProfile` from `./components/UserProfile`.
The `PROGRAM_ID` should be robustly defined, e.g., `new PublicKey(idl.metadata.address)`.
The `RPS_TOKEN_MINT_ADDRESS` needs to be a `PublicKey`.
The `RPS_TOKEN_DECIMALS` is a number.
The `getAssociatedTokenAddress` from `@solana/spl-token` is the one to use.
The `TOKEN_PROGRAM_ID` also from `@solana/spl-token`.
The `SystemProgram.programId` from `@solana/web3.js`.
The `SYSVAR_RENT_PUBKEY` from `@solana/web3.js`.
The `Layout` component will be defined within `App.tsx` for simplicity.
The `AdminData` and `UserProfileData` interfaces should match the Rust structs.
The `BN` fields in Rust will be `BN` in TypeScript.
The `PublicKey` fields in Rust will be `PublicKey` in TypeScript.
The `u64` fields in Rust will be `BN` in TypeScript.
The `String` fields in Rust will be `string` in TypeScript.
The `bool` fields in Rust will be `boolean` in TypeScript.
The `i64` fields in Rust will be `BN` in TypeScript.
The `[u8; 32]` (hash) will be `Uint8Array` or `number[]`. The IDL usually maps it to `number[]`.
The `Option<T>` fields in Rust will be `T | null` in TypeScript.
The `bump` fields are `number`.
The `WagerCurrency` enum will be shared or duplicated.
The `GameState`, `GameMove`, `GameResult` enums are already defined.
The `AdminGameResult` from `admin.rs` is also an enum.
The `RPSTokenError` and `AdminError` are error enums in Rust, client handles JS errors.
The `get_rps_mint()` function in Rust returns a `Pubkey`. Client will use `RPS_TOKEN_MINT_ADDRESS`.
The `sol_to_rps_tokens` and `rps_tokens_to_sol` are utility functions. Client might need similar logic for UI or rely on contract for conversions if applicable.
The `exchange_rate` in `AdminConfig` is `RPS per SOL * 10^9`. Client needs to handle this for display.
The `fee_percent` in `GameAccount` is now set by the program based on `admin_config` and `currency`.
The `PLATFORM_FEE_PERCENT` constant in the client is now obsolete.
The `createGame` instruction now takes `currency`.
The `joinGame` instruction does not take currency, it uses `game.currency`.
The `accounts` for `createGame`, `joinGame`, `revealMove`, `claimTimeout` need to be updated carefully to include all new optional and required accounts like `admin_config`, various token accounts, and user profiles.
The `token_program` account is `TOKEN_PROGRAM_ID`.
The `system_program` account is `SystemProgram.programId`.
The `rent` account is `web3.SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token` is the standard.
The `allowOwnerOffCurve` parameter for `getAssociatedTokenAddress` defaults to `false`. For PDAs as owners, it should be `true`. Game PDA is an owner for its token account. Fee vault PDA is an owner for its token account.
The `player_token_account` is owned by the player (wallet).
The `game_token_account` is owned by the game PDA.
The `fee_token_account` (for RPS fees) is owned by the fee vault PDA.
The `creator_token_account` and `joiner_token_account` are owned by the respective players.
The `winner_token_account` is owned by the winner.
The `recipient_token_account` (for RPS fee withdrawal) is owned by the admin's chosen recipient.
This is getting complex. I will focus on the routing and basic integration of token selection first, then ensure the account structures for main game functions are updated.
The `App.css` is not touched.
The `AdminDashboard` and `UserProfile` components are assumed to handle their own data fetching and logic.
The `TokenSelector` is a UI component.
The `WagerCurrency` enum will be defined locally or imported if it's in `TokenSelector.tsx`. I'll define it locally for clarity in `App.tsx` if it's simple.
The `PROGRAM_ID` is crucial.
The `RPS_TOKEN_MINT` needs to be a `PublicKey`.
The `RPS_TOKEN_DECIMALS` for UI.
The `Layout` component will be internal to `App.tsx`.
The `AdminData` and `UserProfileData` interfaces are important.
The `BN` conversions for display are important.
The `getAssociatedTokenAddress` is async.
The `findProgramAddressSync` is sync.
The `program.methods.instruction(...).accounts({...}).rpc()` pattern.
The IDL `as any` cast.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state will be added.
The `UserProfile` state for the current user will be added.
The `createGame` and `joinGame` will be updated.
The `revealMove` and `claimTimeout` will be updated.
The `TokenProgram.programId` is `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId` is `web3.SystemProgram.programId`.
The `Rent` is `web3.SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token` is the way.
The `allowOwnerOffCurve` for PDAs. Game PDA and Fee Vault PDA.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS` for UI.
The `AdminDashboard` and `UserProfile` components are black boxes for now.
The `TokenSelector` is a black box.
The `WagerCurrency` enum will be defined in `App.tsx` for now.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums.
The `AdminConfig` state.
The `UserProfileData` state for current user.
The `createGame`, `joinGame`, `revealMove`, `claimTimeout` updates.
The `TOKEN_PROGRAM_ID`.
The `SystemProgram.programId`.
The `SYSVAR_RENT_PUBKEY`.
The `getAssociatedTokenAddress` from `@solana/spl-token`.
The `allowOwnerOffCurve`.
The `RPS_TOKEN_MINT_ADDRESS` as `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `AdminDashboard` and `UserProfile` components.
The `TokenSelector` component.
The `WagerCurrency` enum.
The `PROGRAM_ID` from IDL.
The `RPS_TOKEN_MINT` `PublicKey`.
The `RPS_TOKEN_DECIMALS`.
The `Layout` component.
The `AdminData` and `UserProfileData` interfaces.
The `BN` display.
The `getAssociatedTokenAddress` async.
The `findProgramAddressSync` sync.
The `program.methods...rpc()`.
The `idl as any`.
The `Game`, `GameState`, `GameMove`, `GameResult` enums