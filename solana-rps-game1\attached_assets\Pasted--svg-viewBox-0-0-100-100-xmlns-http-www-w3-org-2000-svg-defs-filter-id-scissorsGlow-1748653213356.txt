<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter id="scissorsGlow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="2.5" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    <linearGradient id="bladeGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#A0FFE5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#5EFFC1;stop-opacity:1" />
    </linearGradient>
     <linearGradient id="handleGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#14F195;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0FAD73;stop-opacity:1" />
    </linearGradient>
  </defs>

  <g id="scissors-group" style="transform-origin: center center;">
    <!-- Blade 1 -->
    <g id="blade1-group">
      <path d="M 50 50 L 20 20 L 25 15 L 52 48 Z" fill="url(#bladeGradient)" stroke="#A0FFE5" stroke-width="1.5" stroke-linejoin="round" />
      <circle cx="30" cy="70" r="12" fill="none" stroke="url(#handleGradient)" stroke-width="5"/>
      <path d="M 50 50 L 38 60" stroke="url(#handleGradient)" stroke-width="5" stroke-linecap="round"/>
        <animateTransform
            xlink:href="#blade1-group"
            attributeName="transform"
            attributeType="XML"
            type="rotate"
            from="0 50 50"
            to="-5 50 50"
            dur="1.5s"
            repeatCount="indefinite"
            calcMode="spline"
            keyTimes="0; 0.5; 1"
            keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"
            values="0 50 50; -5 50 50; 0 50 50"
        />
    </g>

    <!-- Blade 2 -->
    <g id="blade2-group">
      <path d="M 50 50 L 80 20 L 75 15 L 48 48 Z" fill="url(#bladeGradient)" stroke="#A0FFE5" stroke-width="1.5" stroke-linejoin="round" />
      <circle cx="70" cy="70" r="12" fill="none" stroke="url(#handleGradient)" stroke-width="5"/>
      <path d="M 50 50 L 62 60" stroke="url(#handleGradient)" stroke-width="5" stroke-linecap="round"/>
       <animateTransform
            xlink:href="#blade2-group"
            attributeName="transform"
            attributeType="XML"
            type="rotate"
            from="0 50 50"
            to="5 50 50"
            dur="1.5s"
            repeatCount="indefinite"
            calcMode="spline"
            keyTimes="0; 0.5; 1"
            keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"
            values="0 50 50; 5 50 50; 0 50 50"
        />
    </g>
    
    <!-- Central Pivot -->
    <circle cx="50" cy="50" r="4" fill="#0FAD73" stroke="#A0FFE5" stroke-width="1"/>

    <animate 
        xlink:href="#scissors-group"
        attributeName="filter"
        values="none; url(#scissorsGlow); none"
        dur="3s"
        repeatCount="indefinite"
        calcMode="spline"
        keyTimes="0; 0.5; 1"
        keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"
    />
  </g>
</svg>
