
[features]
seeds = false
skip-lint = false

[programs.localnet]
solana_rps = "RPS111111111111111111111111111111111111111"

[programs.devnet]
solana_rps = "RPS111111111111111111111111111111111111111"

[programs.testnet]
solana_rps = "RPS111111111111111111111111111111111111111"

[programs.mainnet]
solana_rps = "RPS111111111111111111111111111111111111111"

[registry]
url = "https://api.apr.dev"

[provider]
cluster = "localnet"
wallet = "~/.config/solana/id.json"

[scripts]
test = "yarn run ts-mocha -p ./tsconfig.json -t 1000000 tests/**/*.ts"
build = "anchor build"
deploy = "anchor deploy"
deploy:devnet = "anchor deploy --provider.cluster devnet"
deploy:testnet = "anchor deploy --provider.cluster testnet"
deploy:mainnet = "anchor deploy --provider.cluster mainnet"

[test]
startup_wait = 10000

[test.validator]
url = "https://api.mainnet-beta.solana.com"

# Copy mainnet tokens for testing with real token accounts
[[test.validator.clone]]
address = "So11111111111111111111111111111111111111112"  # Native SOL wrapped token

# Fee account for the platform
[workspace]
types = "target/types"
members = []

[workspace.dependencies]
anchor-lang = "0.29.0"
anchor-spl = "0.29.0"
solana-program = "1.17.0"
