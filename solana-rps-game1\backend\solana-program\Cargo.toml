
[package]
name = "solana-rps-game"
version = "0.1.0"
description = "Rock Paper Scissors game on Solana blockchain"
edition = "2021"
license = "MIT"

[lib]
crate-type = ["cdylib", "lib"]
name = "solana_rps_game"

[features]
no-entrypoint = []
no-idl = []
no-log-ix-name = []
cpi = ["no-entrypoint"]
default = []

[dependencies]
solana-program = "1.16.0"
thiserror = "1.0.43"
borsh = "0.10.3"
num-derive = "0.3"
num-traits = "0.2"

[dev-dependencies]
solana-program-test = "1.16.0"
solana-sdk = "1.16.0"

[profile.release]
overflow-checks = true
lto = "fat"
codegen-units = 1

[profile.release.build-override]
opt-level = 3
incremental = false
codegen-units = 1
