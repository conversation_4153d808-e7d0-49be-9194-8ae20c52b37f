@echo off
REM Solana RPS Game - System Check Script
REM This script checks if all required dependencies are installed

echo.
echo ========================================
echo   Solana RPS Game - System Check
echo ========================================
echo.

set MISSING_DEPS=0
set WARNINGS=0

echo [INFO] Checking system requirements...
echo.

REM Check Windows version
echo [CHECK] Windows Version:
ver
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [PASS] Running as Administrator
) else (
    echo [WARN] Not running as Administrator - some installations may fail
    set /a WARNINGS+=1
)
echo.

REM Check Node.js
echo [CHECK] Node.js:
where node >nul 2>&1
if %errorLevel% == 0 (
    echo [PASS] Node.js found
    node --version
    npm --version
) else (
    echo [FAIL] Node.js not found
    echo [ACTION] Download from: https://nodejs.org/
    set /a MISSING_DEPS+=1
)
echo.

REM Check Git
echo [CHECK] Git:
where git >nul 2>&1
if %errorLevel% == 0 (
    echo [PASS] Git found
    git --version
) else (
    echo [FAIL] Git not found
    echo [ACTION] Download from: https://git-scm.com/
    set /a MISSING_DEPS+=1
)
echo.

REM Check Rust
echo [CHECK] Rust:
where rustc >nul 2>&1
if %errorLevel% == 0 (
    echo [PASS] Rust found
    rustc --version
    cargo --version
) else (
    echo [FAIL] Rust not found
    echo [ACTION] Download from: https://rustup.rs/
    set /a MISSING_DEPS+=1
)
echo.

REM Check Solana CLI
echo [CHECK] Solana CLI:
where solana >nul 2>&1
if %errorLevel% == 0 (
    echo [PASS] Solana CLI found
    solana --version
    echo [INFO] Current configuration:
    solana config get
) else (
    echo [FAIL] Solana CLI not found
    echo [ACTION] Download from: https://docs.solana.com/cli/install-solana-cli-tools
    set /a MISSING_DEPS+=1
)
echo.

REM Check Anchor (optional but recommended)
echo [CHECK] Anchor CLI (optional):
where anchor >nul 2>&1
if %errorLevel% == 0 (
    echo [PASS] Anchor CLI found
    anchor --version
) else (
    echo [INFO] Anchor CLI not found (optional)
    echo [ACTION] Install with: cargo install --git https://github.com/coral-xyz/anchor avm --locked --force
)
echo.

REM Check project structure
echo [CHECK] Project Structure:
if exist "frontend" (
    echo [PASS] Frontend directory found
) else (
    echo [FAIL] Frontend directory missing
    set /a MISSING_DEPS+=1
)

if exist "backend\solana-program" (
    echo [PASS] Backend directory found
) else (
    echo [FAIL] Backend directory missing
    set /a MISSING_DEPS+=1
)

if exist "testing" (
    echo [PASS] Testing directory found
) else (
    echo [FAIL] Testing directory missing
    set /a MISSING_DEPS+=1
)
echo.

REM Check environment files
echo [CHECK] Environment Configuration:
if exist ".env" (
    echo [PASS] Root .env file found
    type .env
) else (
    echo [WARN] Root .env file missing
    echo [ACTION] Will be created during setup
    set /a WARNINGS+=1
)

if exist "frontend\.env" (
    echo [PASS] Frontend .env file found
) else (
    echo [WARN] Frontend .env file missing
    echo [ACTION] Will be created during setup
    set /a WARNINGS+=1
)
echo.

REM Check if dependencies are installed
echo [CHECK] Project Dependencies:
if exist "node_modules" (
    echo [PASS] Root node_modules found
) else (
    echo [WARN] Root dependencies not installed
    set /a WARNINGS+=1
)

if exist "frontend\node_modules" (
    echo [PASS] Frontend dependencies installed
) else (
    echo [WARN] Frontend dependencies not installed
    set /a WARNINGS+=1
)

if exist "testing\node_modules" (
    echo [PASS] Testing dependencies installed
) else (
    echo [WARN] Testing dependencies not installed
    set /a WARNINGS+=1
)
echo.

REM Check if Solana program is built
echo [CHECK] Solana Program:
if exist "backend\solana-program\target\deploy\*.so" (
    echo [PASS] Solana program built
    dir "backend\solana-program\target\deploy\*.so" /b
) else (
    echo [WARN] Solana program not built
    echo [ACTION] Run deploy.bat to build and deploy
    set /a WARNINGS+=1
)
echo.

REM Check wallet setup
echo [CHECK] Solana Wallet:
if exist "%USERPROFILE%\.config\solana\id.json" (
    echo [PASS] Solana keypair found
    echo [INFO] Wallet address:
    solana address 2>nul
    echo [INFO] Balance:
    solana balance 2>nul
) else (
    echo [WARN] Solana keypair not found
    echo [ACTION] Will be created during setup
    set /a WARNINGS+=1
)
echo.

REM Check if test validator is running
echo [CHECK] Solana Test Validator:
solana cluster-version >nul 2>&1
if %errorLevel% == 0 (
    echo [PASS] Solana validator is running
    solana cluster-version
) else (
    echo [INFO] Solana validator not running (normal if not started yet)
    echo [ACTION] Will be started automatically
)
echo.

REM Check browser wallet extensions
echo [CHECK] Browser Wallet Extensions:
echo [INFO] Please ensure you have a Solana wallet installed:
echo   - Phantom: https://phantom.app/
echo   - Solflare: https://solflare.com/
echo   - Backpack: https://backpack.app/
echo.

REM Summary
echo ========================================
echo   System Check Summary
echo ========================================
echo.

if %MISSING_DEPS% == 0 (
    echo [SUCCESS] All required dependencies are installed!
    if %WARNINGS% == 0 (
        echo [SUCCESS] System is ready to run the game!
        echo.
        echo Next steps:
        echo 1. Run: deploy.bat
        echo 2. Run: start.bat
        echo 3. Open: http://localhost:5173
    ) else (
        echo [INFO] %WARNINGS% warnings found - setup may be needed
        echo.
        echo Next steps:
        echo 1. Run: setup.bat (to fix warnings)
        echo 2. Run: deploy.bat
        echo 3. Run: start.bat
    )
) else (
    echo [ERROR] %MISSING_DEPS% required dependencies are missing!
    echo.
    echo Required actions:
    echo 1. Install missing dependencies (see above)
    echo 2. Run: setup.bat
    echo 3. Run: deploy.bat
    echo 4. Run: start.bat
)

echo.
echo ========================================
echo   Installation Commands
echo ========================================
echo.

if %MISSING_DEPS% gtr 0 (
    echo To install missing dependencies automatically:
    echo   setup.bat
    echo.
    echo Or install manually:
    where node >nul 2>&1 || echo   - Node.js: https://nodejs.org/
    where git >nul 2>&1 || echo   - Git: https://git-scm.com/
    where rustc >nul 2>&1 || echo   - Rust: https://rustup.rs/
    where solana >nul 2>&1 || echo   - Solana CLI: https://docs.solana.com/cli/install-solana-cli-tools
    echo.
)

echo To set up the project:
echo   setup.bat       (install dependencies and configure)
echo   deploy.bat      (build and deploy Solana program)
echo   start.bat       (start the game)
echo   test.bat        (run tests)
echo.

pause
