# Solana RPS Game - Complete Setup Script
# This script performs the entire setup process from start to finish

param(
    [switch]$SkipDependencies,
    [switch]$SkipTests,
    [switch]$AutoStart
)

Write-Host "🎮 Solana RPS Game - Complete Setup" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green
Write-Host ""

$startTime = Get-Date
$setupSteps = @()

function Add-SetupStep($step, $status, $details = "") {
    $script:setupSteps += [PSCustomObject]@{
        Step = $step
        Status = $status
        Details = $details
        Timestamp = Get-Date
    }
}

function Show-Progress($current, $total, $activity) {
    $percent = [math]::Round(($current / $total) * 100)
    Write-Progress -Activity $activity -PercentComplete $percent -Status "$current of $total steps completed"
}

# Step 1: Check if running as administrator
Write-Host "🔍 Checking administrator privileges..." -ForegroundColor Blue
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin -and -not $SkipDependencies) {
    Write-Host "⚠️  Not running as administrator. Some installations may fail." -ForegroundColor Yellow
    Write-Host "Consider running PowerShell as Administrator for best results." -ForegroundColor Yellow
    Write-Host ""
}

$totalSteps = 8
$currentStep = 0

# Step 2: Install system dependencies
if (-not $SkipDependencies) {
    $currentStep++
    Show-Progress $currentStep $totalSteps "Installing system dependencies"
    Write-Host "📦 Installing system dependencies..." -ForegroundColor Blue
    
    try {
        & .\setup-windows.ps1
        Add-SetupStep "System Dependencies" "SUCCESS" "Node.js, Rust, Solana CLI installed"
        Write-Host "✅ System dependencies installed" -ForegroundColor Green
    } catch {
        Add-SetupStep "System Dependencies" "FAILED" $_.Exception.Message
        Write-Host "❌ Failed to install system dependencies: $_" -ForegroundColor Red
        Write-Host "Please install manually or run with administrator privileges." -ForegroundColor Yellow
    }
} else {
    Write-Host "⏭️  Skipping system dependencies installation" -ForegroundColor Yellow
    Add-SetupStep "System Dependencies" "SKIPPED" "User requested skip"
}

# Step 3: Setup project configuration
$currentStep++
Show-Progress $currentStep $totalSteps "Setting up project configuration"
Write-Host "🔧 Setting up project configuration..." -ForegroundColor Blue

try {
    & .\setup-project.ps1
    Add-SetupStep "Project Configuration" "SUCCESS" "Dependencies installed, environment configured"
    Write-Host "✅ Project configuration completed" -ForegroundColor Green
} catch {
    Add-SetupStep "Project Configuration" "FAILED" $_.Exception.Message
    Write-Host "❌ Failed to setup project: $_" -ForegroundColor Red
}

# Step 4: Run security audit
$currentStep++
Show-Progress $currentStep $totalSteps "Running security audit"
Write-Host "🔒 Running security audit..." -ForegroundColor Blue

try {
    & .\security-audit.ps1
    Add-SetupStep "Security Audit" "SUCCESS" "Security checks completed"
    Write-Host "✅ Security audit completed" -ForegroundColor Green
} catch {
    Add-SetupStep "Security Audit" "WARNING" "Security audit had issues: $($_.Exception.Message)"
    Write-Host "⚠️  Security audit had issues: $_" -ForegroundColor Yellow
}

# Step 5: Build and deploy program
$currentStep++
Show-Progress $currentStep $totalSteps "Building and deploying Solana program"
Write-Host "🚀 Building and deploying Solana program..." -ForegroundColor Blue

try {
    & .\deploy-program.ps1
    Add-SetupStep "Program Deployment" "SUCCESS" "Solana program built and deployed"
    Write-Host "✅ Program deployment completed" -ForegroundColor Green
} catch {
    Add-SetupStep "Program Deployment" "FAILED" $_.Exception.Message
    Write-Host "❌ Failed to deploy program: $_" -ForegroundColor Red
    Write-Host "You may need to start solana-test-validator manually." -ForegroundColor Yellow
}

# Step 6: Run tests
if (-not $SkipTests) {
    $currentStep++
    Show-Progress $currentStep $totalSteps "Running comprehensive tests"
    Write-Host "🧪 Running comprehensive tests..." -ForegroundColor Blue
    
    try {
        & .\run-tests.ps1
        Add-SetupStep "Testing" "SUCCESS" "All tests completed"
        Write-Host "✅ Testing completed" -ForegroundColor Green
    } catch {
        Add-SetupStep "Testing" "WARNING" "Some tests failed: $($_.Exception.Message)"
        Write-Host "⚠️  Some tests failed: $_" -ForegroundColor Yellow
    }
} else {
    Write-Host "⏭️  Skipping tests" -ForegroundColor Yellow
    Add-SetupStep "Testing" "SKIPPED" "User requested skip"
}

# Step 7: Verify setup
$currentStep++
Show-Progress $currentStep $totalSteps "Verifying setup"
Write-Host "✅ Verifying setup..." -ForegroundColor Blue

$verificationResults = @()

# Check if frontend builds
try {
    Set-Location "frontend"
    $buildOutput = npm run build 2>&1
    if ($LASTEXITCODE -eq 0) {
        $verificationResults += "Frontend builds successfully"
    } else {
        $verificationResults += "Frontend build failed"
    }
    Set-Location ".."
} catch {
    $verificationResults += "Could not test frontend build"
}

# Check if Solana program exists
if (Test-Path "backend\solana-program\target\deploy\*.so") {
    $verificationResults += "Solana program compiled"
} else {
    $verificationResults += "Solana program not found"
}

# Check if test dashboard exists
if (Test-Path "testing\dashboard\index.html") {
    $verificationResults += "Test dashboard generated"
} else {
    $verificationResults += "Test dashboard not found"
}

Add-SetupStep "Verification" "SUCCESS" ($verificationResults -join "; ")

# Step 8: Generate setup report
$currentStep++
Show-Progress $currentStep $totalSteps "Generating setup report"
Write-Host "📊 Generating setup report..." -ForegroundColor Blue

$endTime = Get-Date
$duration = $endTime - $startTime

# Create setup report
$reportContent = @"
# Solana RPS Game Setup Report
Generated: $endTime
Duration: $($duration.TotalMinutes.ToString("F2")) minutes

## Setup Steps
"@

foreach ($step in $setupSteps) {
    $reportContent += @"

### $($step.Step)
- Status: $($step.Status)
- Time: $($step.Timestamp)
- Details: $($step.Details)
"@
}

$reportContent += @"

## Verification Results
$($verificationResults | ForEach-Object { "- $_" } | Out-String)

## Next Steps
1. Start the game: .\start-game.ps1
2. Open browser to: http://localhost:5173
3. Review test dashboard: testing\dashboard\index.html
4. Check security report: security-audit-report.txt

## Troubleshooting
If you encounter issues:
1. Restart your terminal
2. Check the setup logs above
3. Run individual setup scripts manually
4. Ensure all dependencies are installed
"@

$reportPath = "setup-report.md"
$reportContent | Out-File $reportPath -Encoding UTF8

Write-Progress -Activity "Setup Complete" -Completed

# Display final results
Write-Host ""
Write-Host "🎉 Setup Complete!" -ForegroundColor Green
Write-Host "==================" -ForegroundColor Green
Write-Host ""

Write-Host "📊 Setup Summary:" -ForegroundColor Yellow
$successCount = ($setupSteps | Where-Object { $_.Status -eq "SUCCESS" }).Count
$failedCount = ($setupSteps | Where-Object { $_.Status -eq "FAILED" }).Count
$warningCount = ($setupSteps | Where-Object { $_.Status -eq "WARNING" }).Count
$skippedCount = ($setupSteps | Where-Object { $_.Status -eq "SKIPPED" }).Count

Write-Host "  ✅ Successful: $successCount" -ForegroundColor Green
Write-Host "  ❌ Failed: $failedCount" -ForegroundColor Red
Write-Host "  ⚠️  Warnings: $warningCount" -ForegroundColor Yellow
Write-Host "  ⏭️  Skipped: $skippedCount" -ForegroundColor Gray
Write-Host "  ⏱️  Total time: $($duration.TotalMinutes.ToString("F2")) minutes" -ForegroundColor Cyan
Write-Host ""

Write-Host "📄 Reports generated:" -ForegroundColor Yellow
Write-Host "  - Setup report: $reportPath" -ForegroundColor White
Write-Host "  - Security audit: security-audit-report.txt" -ForegroundColor White
if (Test-Path "testing\dashboard\index.html") {
    $dashboardPath = Resolve-Path "testing\dashboard\index.html"
    Write-Host "  - Test dashboard: file://$dashboardPath" -ForegroundColor White
}
Write-Host ""

if ($failedCount -eq 0) {
    Write-Host "🚀 Ready to start!" -ForegroundColor Green
    Write-Host "Run: .\start-game.ps1" -ForegroundColor Cyan
    
    if ($AutoStart) {
        Write-Host ""
        Write-Host "🎮 Auto-starting the game..." -ForegroundColor Blue
        & .\start-game.ps1
    }
} else {
    Write-Host "⚠️  Some steps failed. Please review the setup report and fix issues before starting." -ForegroundColor Yellow
}

Add-SetupStep "Setup Report" "SUCCESS" "Report generated at $reportPath"
