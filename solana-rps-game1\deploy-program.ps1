# Solana RPS Game - Program Deployment Script
# This script deploys the Solana program and updates configuration

Write-Host "🚀 Deploying Solana RPS Game program..." -ForegroundColor Green

# Function to check if a command exists
function Test-Command($command) {
    try {
        Get-Command $command -ErrorAction Stop
        return $true
    } catch {
        return $false
    }
}

# Verify Solana CLI is available
if (-not (Test-Command "solana")) {
    Write-Host "❌ Solana CLI not found. Please run setup-windows.ps1 first." -ForegroundColor Red
    exit 1
}

# Check if we have a keypair
$keypairPath = "$env:USERPROFILE\.config\solana\id.json"
if (-not (Test-Path $keypairPath)) {
    Write-Host "🔑 Generating new Solana keypair..." -ForegroundColor Blue
    solana-keygen new --no-bip39-passphrase --silent --outfile $keypairPath
}

# Get current configuration
Write-Host "📋 Current Solana configuration:" -ForegroundColor Blue
solana config get

# Check balance and airdrop if needed
Write-Host "💰 Checking wallet balance..." -ForegroundColor Blue
$balance = solana balance --lamports
$balanceSOL = [math]::Round($balance / 1000000000, 2)
Write-Host "Current balance: $balanceSOL SOL" -ForegroundColor White

if ($balanceSOL -lt 2) {
    Write-Host "💸 Requesting airdrop..." -ForegroundColor Blue
    try {
        solana airdrop 2
        Start-Sleep -Seconds 5
        $newBalance = solana balance --lamports
        $newBalanceSOL = [math]::Round($newBalance / 1000000000, 2)
        Write-Host "New balance: $newBalanceSOL SOL" -ForegroundColor Green
    } catch {
        Write-Host "⚠️  Airdrop failed. You may need to request SOL manually." -ForegroundColor Yellow
    }
}

# Build the program first
Write-Host "🔨 Building Solana program..." -ForegroundColor Blue
Set-Location "backend\solana-program"

try {
    if (Test-Command "anchor") {
        Write-Host "Using Anchor build..." -ForegroundColor Blue
        anchor build
        $programPath = "target\deploy\solana_rps.so"
    } else {
        Write-Host "Using cargo build-bpf..." -ForegroundColor Blue
        cargo build-bpf
        $programPath = "target\deploy\rps_game.so"
    }
    
    if (-not (Test-Path $programPath)) {
        Write-Host "❌ Program binary not found at $programPath" -ForegroundColor Red
        Set-Location "..\..\"
        exit 1
    }
    
    Write-Host "✅ Program built successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Program build failed: $_" -ForegroundColor Red
    Set-Location "..\..\"
    exit 1
}

# Deploy the program
Write-Host "🚀 Deploying program to Solana devnet..." -ForegroundColor Blue
try {
    $deployOutput = solana program deploy $programPath 2>&1
    Write-Host $deployOutput -ForegroundColor White
    
    # Extract program ID from output
    $programIdMatch = $deployOutput | Select-String "Program Id: ([A-Za-z0-9]+)"
    if ($programIdMatch) {
        $programId = $programIdMatch.Matches[0].Groups[1].Value
        Write-Host "✅ Program deployed successfully!" -ForegroundColor Green
        Write-Host "Program ID: $programId" -ForegroundColor Cyan
        
        # Update environment files with new program ID
        Set-Location "..\..\"
        
        Write-Host "📝 Updating environment files with new Program ID..." -ForegroundColor Blue
        
        # Update root .env
        $envContent = Get-Content ".env" -Raw
        $envContent = $envContent -replace "VITE_RPS_PROGRAM_ID=.*", "VITE_RPS_PROGRAM_ID=$programId"
        Set-Content ".env" $envContent
        
        # Update frontend .env
        $frontendEnvContent = Get-Content "frontend\.env" -Raw
        $frontendEnvContent = $frontendEnvContent -replace "VITE_RPS_PROGRAM_ID=.*", "VITE_RPS_PROGRAM_ID=$programId"
        Set-Content "frontend\.env" $frontendEnvContent
        
        # Update testing config
        if (Test-Path "testing\config.json") {
            $testingConfig = Get-Content "testing\config.json" -Raw | ConvertFrom-Json
            $testingConfig.programId = $programId
            $testingConfig | ConvertTo-Json -Depth 10 | Set-Content "testing\config.json"
        }
        
        Write-Host "✅ Environment files updated" -ForegroundColor Green
        
    } else {
        Write-Host "⚠️  Could not extract Program ID from deployment output" -ForegroundColor Yellow
        Write-Host "Please manually update the VITE_RPS_PROGRAM_ID in .env files" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "❌ Program deployment failed: $_" -ForegroundColor Red
    Set-Location "..\..\"
    exit 1
}

Set-Location "..\..\"

Write-Host ""
Write-Host "🎉 Deployment complete!" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Start the frontend: npm run dev" -ForegroundColor White
Write-Host "2. Run tests: .\run-tests.ps1" -ForegroundColor White
Write-Host "3. Open browser to: http://localhost:5173" -ForegroundColor White
Write-Host ""
Write-Host "📋 Program Information:" -ForegroundColor Cyan
if ($programId) {
    Write-Host "Program ID: $programId" -ForegroundColor White
}
Write-Host "Network: Solana Devnet" -ForegroundColor White
Write-Host "RPC Endpoint: https://api.devnet.solana.com" -ForegroundColor White
