@echo off
REM Solana RPS Game - Deploy Script (Command Prompt)
REM This script builds and deploys the Solana program

echo.
echo ========================================
echo   Deploying Solana RPS Game Program
echo ========================================
echo.

REM Check if Solana CLI is available
where solana >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Solana CLI not found. Please run setup.bat first.
    pause
    exit /b 1
)

REM Check if we have a keypair
if not exist "%USERPROFILE%\.config\solana\id.json" (
    echo [INFO] Generating new Solana keypair...
    solana-keygen new --no-bip39-passphrase --silent --outfile "%USERPROFILE%\.config\solana\id.json"
    if %errorLevel% neq 0 (
        echo [ERROR] Failed to generate keypair
        pause
        exit /b 1
    )
)

REM Get current configuration
echo [INFO] Current Solana configuration:
solana config get

REM Check balance and airdrop if needed
echo [INFO] Checking wallet balance...
for /f "tokens=1" %%i in ('solana balance --lamports 2^>nul') do set balance=%%i
if "%balance%"=="" set balance=0

set /a sol_balance=%balance%/1000000000
echo [INFO] Current balance: %sol_balance% SOL

if %sol_balance% lss 2 (
    echo [INFO] Requesting airdrop...
    solana airdrop 2
    if %errorLevel% neq 0 (
        echo [WARNING] Airdrop failed. You may need to request SOL manually.
    ) else (
        timeout /t 5 /nobreak >nul
        for /f "tokens=1" %%i in ('solana balance --lamports 2^>nul') do set new_balance=%%i
        set /a new_sol_balance=%new_balance%/1000000000
        echo [SUCCESS] New balance: %new_sol_balance% SOL
    )
)

REM Build the program
echo [INFO] Building Solana program...
cd backend\solana-program

REM Try Anchor build first
where anchor >nul 2>&1
if %errorLevel% == 0 (
    echo [INFO] Using Anchor build...
    call anchor build
    if %errorLevel% == 0 (
        set PROGRAM_PATH=target\deploy\solana_rps.so
        goto deploy_program
    ) else (
        echo [WARNING] Anchor build failed, trying cargo...
    )
)

REM Fallback to cargo build-bpf
echo [INFO] Using cargo build-bpf...
call cargo build-bpf
if %errorLevel% neq 0 (
    echo [ERROR] Program build failed
    cd ..\..
    pause
    exit /b 1
)

REM Find the .so file
if exist "target\deploy\rps_game.so" (
    set PROGRAM_PATH=target\deploy\rps_game.so
) else if exist "target\deploy\solana_rps.so" (
    set PROGRAM_PATH=target\deploy\solana_rps.so
) else (
    echo [ERROR] Program binary not found
    cd ..\..
    pause
    exit /b 1
)

:deploy_program
echo [SUCCESS] Program built successfully: %PROGRAM_PATH%

REM Deploy the program
echo [INFO] Deploying program to Solana devnet...
solana program deploy %PROGRAM_PATH% > deploy_output.txt 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Program deployment failed
    type deploy_output.txt
    del deploy_output.txt
    cd ..\..
    pause
    exit /b 1
)

REM Extract program ID from output
for /f "tokens=3" %%i in ('findstr "Program Id:" deploy_output.txt') do set PROGRAM_ID=%%i
del deploy_output.txt

if "%PROGRAM_ID%"=="" (
    echo [WARNING] Could not extract Program ID from deployment output
    echo [INFO] Please manually update the VITE_RPS_PROGRAM_ID in .env files
) else (
    echo [SUCCESS] Program deployed successfully!
    echo [INFO] Program ID: %PROGRAM_ID%
    
    REM Update environment files
    cd ..\..
    echo [INFO] Updating environment files with new Program ID...
    
    REM Update root .env
    if exist ".env" (
        powershell -Command "(Get-Content '.env') -replace 'VITE_RPS_PROGRAM_ID=.*', 'VITE_RPS_PROGRAM_ID=%PROGRAM_ID%' | Set-Content '.env'"
    )
    
    REM Update frontend .env
    if exist "frontend\.env" (
        powershell -Command "(Get-Content 'frontend\.env') -replace 'VITE_RPS_PROGRAM_ID=.*', 'VITE_RPS_PROGRAM_ID=%PROGRAM_ID%' | Set-Content 'frontend\.env'"
    )
    
    REM Update testing config
    if exist "testing\config.json" (
        powershell -Command "$config = Get-Content 'testing\config.json' | ConvertFrom-Json; $config.programId = '%PROGRAM_ID%'; $config | ConvertTo-Json -Depth 10 | Set-Content 'testing\config.json'"
    )
    
    echo [SUCCESS] Environment files updated
    goto deployment_complete
)

cd ..\..

:deployment_complete
echo.
echo ========================================
echo   Deployment Complete!
echo ========================================
echo.

echo [INFO] Next steps:
echo 1. Start the frontend: start.bat
echo 2. Run tests: test.bat
echo 3. Open browser to: http://localhost:5173
echo.

if not "%PROGRAM_ID%"=="" (
    echo [INFO] Program Information:
    echo   Program ID: %PROGRAM_ID%
    echo   Network: Solana Devnet
    echo   RPC Endpoint: https://api.devnet.solana.com
    echo.
)

pause
