@echo off
REM Solana RPS Game - Fix and Start Script
REM This script checks everything and fixes issues automatically

echo.
echo ========================================
echo   Solana RPS Game - Fix and Start
echo ========================================
echo.

echo [INFO] This script will check your system and fix any issues automatically.
echo.

REM Step 1: Check system
echo [STEP 1] Checking system requirements...
call check-system.bat > system-check.log 2>&1

REM Check if major dependencies are missing
set NEED_SETUP=0

where node >nul 2>&1 || set NEED_SETUP=1
where git >nul 2>&1 || set NEED_SETUP=1
where rustc >nul 2>&1 || set NEED_SETUP=1
where solana >nul 2>&1 || set NEED_SETUP=1

if %NEED_SETUP% == 1 (
    echo [INFO] Missing dependencies detected. Running setup...
    call setup.bat
    if %errorLevel% neq 0 (
        echo [ERROR] Setup failed. Please check system-check.log for details.
        pause
        exit /b 1
    )
) else (
    echo [SUCCESS] All major dependencies found!
)

REM Step 2: Check project configuration
echo.
echo [STEP 2] Checking project configuration...

if not exist ".env" (
    echo [INFO] Creating environment configuration...
    echo # Solana RPS Game Environment Configuration > .env
    echo VITE_RPC_ENDPOINT=https://api.devnet.solana.com >> .env
    echo VITE_RPS_PROGRAM_ID=7Y9dRMY6V9cmVkXNFrHeUZmYf2tAV5wSVFcYyD5bLQpZ >> .env
    echo VITE_RPS_TOKEN_MINT= >> .env
    echo VITE_FEE_COLLECTOR_ACCOUNT=FeeKHhL1CcJCyd82xextWTbBT5jGzVQwXVQKNjHV8SDD >> .env
    echo [SUCCESS] Environment file created
)

if not exist "frontend\.env" (
    echo [INFO] Creating frontend environment file...
    copy .env frontend\.env >nul
    echo [SUCCESS] Frontend environment file created
)

REM Step 3: Install dependencies
echo.
echo [STEP 3] Installing project dependencies...

if not exist "frontend\node_modules" (
    echo [INFO] Installing frontend dependencies...
    cd frontend
    call npm install --legacy-peer-deps
    if %errorLevel% neq 0 (
        echo [ERROR] Failed to install frontend dependencies
        cd ..
        pause
        exit /b 1
    )
    cd ..
    echo [SUCCESS] Frontend dependencies installed
) else (
    echo [SUCCESS] Frontend dependencies already installed
)

if not exist "testing\node_modules" (
    echo [INFO] Installing testing dependencies...
    cd testing
    call npm install
    if %errorLevel% neq 0 (
        echo [WARNING] Testing dependencies installation had issues
    ) else (
        echo [SUCCESS] Testing dependencies installed
    )
    cd ..
) else (
    echo [SUCCESS] Testing dependencies already installed
)

REM Step 4: Set up Solana wallet
echo.
echo [STEP 4] Setting up Solana wallet...

if not exist "%USERPROFILE%\.config\solana\id.json" (
    echo [INFO] Creating Solana keypair...
    solana-keygen new --no-bip39-passphrase --silent --outfile "%USERPROFILE%\.config\solana\id.json"
    if %errorLevel% neq 0 (
        echo [ERROR] Failed to create keypair
        pause
        exit /b 1
    )
    echo [SUCCESS] Solana keypair created
)

echo [INFO] Configuring Solana for devnet...
solana config set --url https://api.devnet.solana.com >nul 2>&1

echo [INFO] Checking wallet balance...
for /f "tokens=1" %%i in ('solana balance --lamports 2^>nul') do set balance=%%i
if "%balance%"=="" set balance=0
set /a sol_balance=%balance%/1000000000

if %sol_balance% lss 1 (
    echo [INFO] Requesting SOL airdrop...
    solana airdrop 2 >nul 2>&1
    if %errorLevel% == 0 (
        echo [SUCCESS] Airdrop completed
    ) else (
        echo [WARNING] Airdrop failed - you may need SOL for transactions
    )
) else (
    echo [SUCCESS] Wallet has sufficient balance: %sol_balance% SOL
)

REM Step 5: Build and deploy program
echo.
echo [STEP 5] Building and deploying Solana program...

if not exist "backend\solana-program\target\deploy\*.so" (
    echo [INFO] Building Solana program...
    cd backend\solana-program
    
    where anchor >nul 2>&1
    if %errorLevel% == 0 (
        call anchor build >nul 2>&1
        if %errorLevel% == 0 (
            echo [SUCCESS] Program built with Anchor
            set PROGRAM_PATH=target\deploy\solana_rps.so
        ) else (
            echo [INFO] Anchor build failed, trying cargo...
            call cargo build-bpf >nul 2>&1
            if %errorLevel% == 0 (
                echo [SUCCESS] Program built with cargo
                set PROGRAM_PATH=target\deploy\rps_game.so
            ) else (
                echo [WARNING] Program build failed
                cd ..\..
                goto skip_deploy
            )
        )
    ) else (
        call cargo build-bpf >nul 2>&1
        if %errorLevel% == 0 (
            echo [SUCCESS] Program built with cargo
            set PROGRAM_PATH=target\deploy\rps_game.so
        ) else (
            echo [WARNING] Program build failed
            cd ..\..
            goto skip_deploy
        )
    )
    
    REM Deploy the program
    echo [INFO] Deploying program...
    solana program deploy %PROGRAM_PATH% > deploy_output.txt 2>&1
    if %errorLevel% == 0 (
        for /f "tokens=3" %%i in ('findstr "Program Id:" deploy_output.txt') do set PROGRAM_ID=%%i
        if not "%PROGRAM_ID%"=="" (
            echo [SUCCESS] Program deployed: %PROGRAM_ID%
            
            REM Update environment files
            cd ..\..
            powershell -Command "(Get-Content '.env') -replace 'VITE_RPS_PROGRAM_ID=.*', 'VITE_RPS_PROGRAM_ID=%PROGRAM_ID%' | Set-Content '.env'" >nul 2>&1
            powershell -Command "(Get-Content 'frontend\.env') -replace 'VITE_RPS_PROGRAM_ID=.*', 'VITE_RPS_PROGRAM_ID=%PROGRAM_ID%' | Set-Content 'frontend\.env'" >nul 2>&1
            echo [SUCCESS] Environment files updated
        ) else (
            echo [WARNING] Could not extract program ID
            cd ..\..
        )
    ) else (
        echo [WARNING] Program deployment failed
        cd ..\..
    )
    del deploy_output.txt >nul 2>&1
) else (
    echo [SUCCESS] Solana program already built
)

:skip_deploy

REM Step 6: Start the game
echo.
echo [STEP 6] Starting the game environment...

REM Check if validator is running
solana cluster-version >nul 2>&1
if %errorLevel% neq 0 (
    echo [INFO] Starting Solana test validator...
    start "Solana Test Validator" cmd /k "solana-test-validator"
    echo [INFO] Waiting for validator to start...
    timeout /t 10 /nobreak >nul
    
    REM Wait for validator
    :wait_validator
    solana cluster-version >nul 2>&1
    if %errorLevel% neq 0 (
        timeout /t 2 /nobreak >nul
        goto wait_validator
    )
    echo [SUCCESS] Validator started
) else (
    echo [SUCCESS] Validator already running
)

REM Check if frontend is already running
netstat -an | findstr ":5173" >nul 2>&1
if %errorLevel% == 0 (
    echo [INFO] Frontend already running on port 5173
    echo [SUCCESS] Game is ready!
    goto show_results
)

echo [INFO] Starting frontend server...
cd frontend
start "Solana RPS Game" cmd /k "npm run dev"
cd ..

echo [INFO] Waiting for frontend to start...
timeout /t 5 /nobreak >nul

:show_results
echo.
echo ========================================
echo   Setup Complete - Game Ready!
echo ========================================
echo.

echo [SUCCESS] Solana RPS Game is now running!
echo.
echo 🌐 Game URL: http://localhost:5173
echo 🔗 Open this link in your browser
echo.
echo 📋 What you need to do:
echo 1. Install a Solana wallet browser extension:
echo    - Phantom: https://phantom.app/ (recommended)
echo    - Solflare: https://solflare.com/
echo.
echo 2. In your wallet:
echo    - Switch to "Devnet" network
echo    - Get devnet SOL from: https://faucet.solana.com/
echo.
echo 3. In the game:
echo    - Click "Connect Wallet"
echo    - Approve the connection
echo    - Start playing Rock Paper Scissors!
echo.

echo 🎮 Game Features:
echo - Multi-player games (3-4 players)
echo - Tournament mode
echo - Auto-play with strategies
echo - Secure blockchain gameplay
echo.

echo 🛠️ Useful commands:
echo - Check system: check-system.bat
echo - Setup wallet: setup-wallet.bat
echo - Run tests: test.bat
echo - Restart game: start.bat
echo.

REM Open browser automatically
choice /C YN /M "Open browser automatically"
if errorlevel 1 (
    start http://localhost:5173
)

echo.
echo [INFO] Keep the validator and frontend windows open while playing!
echo [INFO] Press any key to exit this setup script...
pause >nul
