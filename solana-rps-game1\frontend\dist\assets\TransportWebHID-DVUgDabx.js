import{l as vt,D as Rt,T as Oe,a as It,c as gt,d as St}from"./index-Bf2gjslW.js";import{a as Lt,g as Je}from"./solana-DY4aUzi8.js";var Qe={};const wt=Lt(vt);(function(r){r.__esModule=!0;var t=wt,e=5;function n(c){var d=Buffer.alloc(2);return d.writeUInt16BE(c,0),d}var i={data:Buffer.alloc(0),dataLength:0,sequence:0},a=function(c,d){return{makeBlocks:function(o){var l=Buffer.concat([n(o.length),o]),s=d-5,f=Math.ceil(l.length/s);l=Buffer.concat([l,Buffer.alloc(f*s-l.length+1).fill(0)]);for(var I=[],h=0;h<f;h++){var y=Buffer.alloc(5);y.writeUInt16BE(c,0),y.writeUInt8(e,2),y.writeUInt16BE(h,3);var m=l.slice(h*s,(h+1)*s);I.push(Buffer.concat([y,m]))}return I},reduceResponse:function(o,l){var s=o||i,f=s.data,I=s.dataLength,h=s.sequence;if(l.readUInt16BE(0)!==c)throw new t.TransportError("Invalid channel","InvalidChannel");if(l.readUInt8(2)!==e)throw new t.TransportError("Invalid tag","InvalidTag");if(l.readUInt16BE(3)!==h)throw new t.TransportError("Invalid sequence","InvalidSequence");o||(I=l.readUInt16BE(5)),h++;var y=l.slice(o?5:7);return f=Buffer.concat([f,y]),f.length>I&&(f=f.slice(0,I)),{data:f,dataLength:I,sequence:h}},getReducedResult:function(o){if(o&&o.dataLength===o.data.length)return o.data}}};r.default=a})(Qe);const Nt=Je(Qe);var de={exports:{}};const Ot="2.0.0",et=256,Tt=Number.MAX_SAFE_INTEGER||9007199254740991,At=16,yt=et-6,Pt=["major","premajor","minor","preminor","patch","prepatch","prerelease"];var te={MAX_LENGTH:et,MAX_SAFE_COMPONENT_LENGTH:At,MAX_SAFE_BUILD_LENGTH:yt,MAX_SAFE_INTEGER:Tt,RELEASE_TYPES:Pt,SEMVER_SPEC_VERSION:Ot,FLAG_INCLUDE_PRERELEASE:1,FLAG_LOOSE:2},ae={};const _t=typeof process=="object"&&ae&&ae.NODE_DEBUG&&/\bsemver\b/i.test(ae.NODE_DEBUG)?(...r)=>console.error("SEMVER",...r):()=>{};var re=_t;(function(r,t){const{MAX_SAFE_COMPONENT_LENGTH:e,MAX_SAFE_BUILD_LENGTH:n,MAX_LENGTH:i}=te,a=re;t=r.exports={};const c=t.re=[],d=t.safeRe=[],o=t.src=[],l=t.safeSrc=[],s=t.t={};let f=0;const I="[a-zA-Z0-9-]",h=[["\\s",1],["\\d",i],[I,n]],y=C=>{for(const[O,X]of h)C=C.split(`${O}*`).join(`${O}{0,${X}}`).split(`${O}+`).join(`${O}{1,${X}}`);return C},m=(C,O,X)=>{const T=y(O),G=f++;a(C,G,O),s[C]=G,o[G]=O,l[G]=T,c[G]=new RegExp(O,X?"g":void 0),d[G]=new RegExp(T,X?"g":void 0)};m("NUMERICIDENTIFIER","0|[1-9]\\d*"),m("NUMERICIDENTIFIERLOOSE","\\d+"),m("NONNUMERICIDENTIFIER",`\\d*[a-zA-Z-]${I}*`),m("MAINVERSION",`(${o[s.NUMERICIDENTIFIER]})\\.(${o[s.NUMERICIDENTIFIER]})\\.(${o[s.NUMERICIDENTIFIER]})`),m("MAINVERSIONLOOSE",`(${o[s.NUMERICIDENTIFIERLOOSE]})\\.(${o[s.NUMERICIDENTIFIERLOOSE]})\\.(${o[s.NUMERICIDENTIFIERLOOSE]})`),m("PRERELEASEIDENTIFIER",`(?:${o[s.NONNUMERICIDENTIFIER]}|${o[s.NUMERICIDENTIFIER]})`),m("PRERELEASEIDENTIFIERLOOSE",`(?:${o[s.NONNUMERICIDENTIFIER]}|${o[s.NUMERICIDENTIFIERLOOSE]})`),m("PRERELEASE",`(?:-(${o[s.PRERELEASEIDENTIFIER]}(?:\\.${o[s.PRERELEASEIDENTIFIER]})*))`),m("PRERELEASELOOSE",`(?:-?(${o[s.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${o[s.PRERELEASEIDENTIFIERLOOSE]})*))`),m("BUILDIDENTIFIER",`${I}+`),m("BUILD",`(?:\\+(${o[s.BUILDIDENTIFIER]}(?:\\.${o[s.BUILDIDENTIFIER]})*))`),m("FULLPLAIN",`v?${o[s.MAINVERSION]}${o[s.PRERELEASE]}?${o[s.BUILD]}?`),m("FULL",`^${o[s.FULLPLAIN]}$`),m("LOOSEPLAIN",`[v=\\s]*${o[s.MAINVERSIONLOOSE]}${o[s.PRERELEASELOOSE]}?${o[s.BUILD]}?`),m("LOOSE",`^${o[s.LOOSEPLAIN]}$`),m("GTLT","((?:<|>)?=?)"),m("XRANGEIDENTIFIERLOOSE",`${o[s.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`),m("XRANGEIDENTIFIER",`${o[s.NUMERICIDENTIFIER]}|x|X|\\*`),m("XRANGEPLAIN",`[v=\\s]*(${o[s.XRANGEIDENTIFIER]})(?:\\.(${o[s.XRANGEIDENTIFIER]})(?:\\.(${o[s.XRANGEIDENTIFIER]})(?:${o[s.PRERELEASE]})?${o[s.BUILD]}?)?)?`),m("XRANGEPLAINLOOSE",`[v=\\s]*(${o[s.XRANGEIDENTIFIERLOOSE]})(?:\\.(${o[s.XRANGEIDENTIFIERLOOSE]})(?:\\.(${o[s.XRANGEIDENTIFIERLOOSE]})(?:${o[s.PRERELEASELOOSE]})?${o[s.BUILD]}?)?)?`),m("XRANGE",`^${o[s.GTLT]}\\s*${o[s.XRANGEPLAIN]}$`),m("XRANGELOOSE",`^${o[s.GTLT]}\\s*${o[s.XRANGEPLAINLOOSE]}$`),m("COERCEPLAIN",`(^|[^\\d])(\\d{1,${e}})(?:\\.(\\d{1,${e}}))?(?:\\.(\\d{1,${e}}))?`),m("COERCE",`${o[s.COERCEPLAIN]}(?:$|[^\\d])`),m("COERCEFULL",o[s.COERCEPLAIN]+`(?:${o[s.PRERELEASE]})?(?:${o[s.BUILD]})?(?:$|[^\\d])`),m("COERCERTL",o[s.COERCE],!0),m("COERCERTLFULL",o[s.COERCEFULL],!0),m("LONETILDE","(?:~>?)"),m("TILDETRIM",`(\\s*)${o[s.LONETILDE]}\\s+`,!0),t.tildeTrimReplace="$1~",m("TILDE",`^${o[s.LONETILDE]}${o[s.XRANGEPLAIN]}$`),m("TILDELOOSE",`^${o[s.LONETILDE]}${o[s.XRANGEPLAINLOOSE]}$`),m("LONECARET","(?:\\^)"),m("CARETTRIM",`(\\s*)${o[s.LONECARET]}\\s+`,!0),t.caretTrimReplace="$1^",m("CARET",`^${o[s.LONECARET]}${o[s.XRANGEPLAIN]}$`),m("CARETLOOSE",`^${o[s.LONECARET]}${o[s.XRANGEPLAINLOOSE]}$`),m("COMPARATORLOOSE",`^${o[s.GTLT]}\\s*(${o[s.LOOSEPLAIN]})$|^$`),m("COMPARATOR",`^${o[s.GTLT]}\\s*(${o[s.FULLPLAIN]})$|^$`),m("COMPARATORTRIM",`(\\s*)${o[s.GTLT]}\\s*(${o[s.LOOSEPLAIN]}|${o[s.XRANGEPLAIN]})`,!0),t.comparatorTrimReplace="$1$2$3",m("HYPHENRANGE",`^\\s*(${o[s.XRANGEPLAIN]})\\s+-\\s+(${o[s.XRANGEPLAIN]})\\s*$`),m("HYPHENRANGELOOSE",`^\\s*(${o[s.XRANGEPLAINLOOSE]})\\s+-\\s+(${o[s.XRANGEPLAINLOOSE]})\\s*$`),m("STAR","(<|>)?=?\\s*\\*"),m("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$"),m("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")})(de,de.exports);var q=de.exports;const Ct=Object.freeze({loose:!0}),bt=Object.freeze({}),Dt=r=>r?typeof r!="object"?Ct:r:bt;var ve=Dt;const Te=/^[0-9]+$/,tt=(r,t)=>{const e=Te.test(r),n=Te.test(t);return e&&n&&(r=+r,t=+t),r===t?0:e&&!n?-1:n&&!e?1:r<t?-1:1},Ut=(r,t)=>tt(t,r);var rt={compareIdentifiers:tt,rcompareIdentifiers:Ut};const z=re,{MAX_LENGTH:Ae,MAX_SAFE_INTEGER:W}=te,{safeRe:Y,t:K}=q,xt=ve,{compareIdentifiers:k}=rt;let Ft=class U{constructor(t,e){if(e=xt(e),t instanceof U){if(t.loose===!!e.loose&&t.includePrerelease===!!e.includePrerelease)return t;t=t.version}else if(typeof t!="string")throw new TypeError(`Invalid version. Must be a string. Got type "${typeof t}".`);if(t.length>Ae)throw new TypeError(`version is longer than ${Ae} characters`);z("SemVer",t,e),this.options=e,this.loose=!!e.loose,this.includePrerelease=!!e.includePrerelease;const n=t.trim().match(e.loose?Y[K.LOOSE]:Y[K.FULL]);if(!n)throw new TypeError(`Invalid Version: ${t}`);if(this.raw=t,this.major=+n[1],this.minor=+n[2],this.patch=+n[3],this.major>W||this.major<0)throw new TypeError("Invalid major version");if(this.minor>W||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>W||this.patch<0)throw new TypeError("Invalid patch version");n[4]?this.prerelease=n[4].split(".").map(i=>{if(/^[0-9]+$/.test(i)){const a=+i;if(a>=0&&a<W)return a}return i}):this.prerelease=[],this.build=n[5]?n[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(t){if(z("SemVer.compare",this.version,this.options,t),!(t instanceof U)){if(typeof t=="string"&&t===this.version)return 0;t=new U(t,this.options)}return t.version===this.version?0:this.compareMain(t)||this.comparePre(t)}compareMain(t){return t instanceof U||(t=new U(t,this.options)),k(this.major,t.major)||k(this.minor,t.minor)||k(this.patch,t.patch)}comparePre(t){if(t instanceof U||(t=new U(t,this.options)),this.prerelease.length&&!t.prerelease.length)return-1;if(!this.prerelease.length&&t.prerelease.length)return 1;if(!this.prerelease.length&&!t.prerelease.length)return 0;let e=0;do{const n=this.prerelease[e],i=t.prerelease[e];if(z("prerelease compare",e,n,i),n===void 0&&i===void 0)return 0;if(i===void 0)return 1;if(n===void 0)return-1;if(n===i)continue;return k(n,i)}while(++e)}compareBuild(t){t instanceof U||(t=new U(t,this.options));let e=0;do{const n=this.build[e],i=t.build[e];if(z("build compare",e,n,i),n===void 0&&i===void 0)return 0;if(i===void 0)return 1;if(n===void 0)return-1;if(n===i)continue;return k(n,i)}while(++e)}inc(t,e,n){if(t.startsWith("pre")){if(!e&&n===!1)throw new Error("invalid increment argument: identifier is empty");if(e){const i=`-${e}`.match(this.options.loose?Y[K.PRERELEASELOOSE]:Y[K.PRERELEASE]);if(!i||i[1]!==e)throw new Error(`invalid identifier: ${e}`)}}switch(t){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",e,n);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",e,n);break;case"prepatch":this.prerelease.length=0,this.inc("patch",e,n),this.inc("pre",e,n);break;case"prerelease":this.prerelease.length===0&&this.inc("patch",e,n),this.inc("pre",e,n);break;case"release":if(this.prerelease.length===0)throw new Error(`version ${this.raw} is not a prerelease`);this.prerelease.length=0;break;case"major":(this.minor!==0||this.patch!==0||this.prerelease.length===0)&&this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":(this.patch!==0||this.prerelease.length===0)&&this.minor++,this.patch=0,this.prerelease=[];break;case"patch":this.prerelease.length===0&&this.patch++,this.prerelease=[];break;case"pre":{const i=Number(n)?1:0;if(this.prerelease.length===0)this.prerelease=[i];else{let a=this.prerelease.length;for(;--a>=0;)typeof this.prerelease[a]=="number"&&(this.prerelease[a]++,a=-2);if(a===-1){if(e===this.prerelease.join(".")&&n===!1)throw new Error("invalid increment argument: identifier already exists");this.prerelease.push(i)}}if(e){let a=[e,i];n===!1&&(a=[e]),k(this.prerelease[0],e)===0?isNaN(this.prerelease[1])&&(this.prerelease=a):this.prerelease=a}break}default:throw new Error(`invalid increment argument: ${t}`)}return this.raw=this.format(),this.build.length&&(this.raw+=`+${this.build.join(".")}`),this}};var P=Ft;const ye=P,Gt=(r,t,e=!1)=>{if(r instanceof ye)return r;try{return new ye(r,t)}catch(n){if(!e)return null;throw n}};var B=Gt;const jt=B,Xt=(r,t)=>{const e=jt(r,t);return e?e.version:null};var Vt=Xt;const kt=B,Bt=(r,t)=>{const e=kt(r.trim().replace(/^[=v]+/,""),t);return e?e.version:null};var Mt=Bt;const Pe=P,Ht=(r,t,e,n,i)=>{typeof e=="string"&&(i=n,n=e,e=void 0);try{return new Pe(r instanceof Pe?r.version:r,e).inc(t,n,i).version}catch{return null}};var qt=Ht;const _e=B,zt=(r,t)=>{const e=_e(r,null,!0),n=_e(t,null,!0),i=e.compare(n);if(i===0)return null;const a=i>0,c=a?e:n,d=a?n:e,o=!!c.prerelease.length;if(!!d.prerelease.length&&!o){if(!d.patch&&!d.minor)return"major";if(d.compareMain(c)===0)return d.minor&&!d.patch?"minor":"patch"}const s=o?"pre":"";return e.major!==n.major?s+"major":e.minor!==n.minor?s+"minor":e.patch!==n.patch?s+"patch":"prerelease"};var Wt=zt;const Yt=P,Kt=(r,t)=>new Yt(r,t).major;var Zt=Kt;const Jt=P,Qt=(r,t)=>new Jt(r,t).minor;var er=Qt;const tr=P,rr=(r,t)=>new tr(r,t).patch;var nr=rr;const sr=B,ir=(r,t)=>{const e=sr(r,t);return e&&e.prerelease.length?e.prerelease:null};var ar=ir;const Ce=P,or=(r,t,e)=>new Ce(r,e).compare(new Ce(t,e));var b=or;const cr=b,lr=(r,t,e)=>cr(t,r,e);var ur=lr;const fr=b,hr=(r,t)=>fr(r,t,!0);var Er=hr;const be=P,dr=(r,t,e)=>{const n=new be(r,e),i=new be(t,e);return n.compare(i)||n.compareBuild(i)};var Re=dr;const pr=Re,$r=(r,t)=>r.sort((e,n)=>pr(e,n,t));var mr=$r;const vr=Re,Rr=(r,t)=>r.sort((e,n)=>vr(n,e,t));var Ir=Rr;const gr=b,Sr=(r,t,e)=>gr(r,t,e)>0;var ne=Sr;const Lr=b,wr=(r,t,e)=>Lr(r,t,e)<0;var Ie=wr;const Nr=b,Or=(r,t,e)=>Nr(r,t,e)===0;var nt=Or;const Tr=b,Ar=(r,t,e)=>Tr(r,t,e)!==0;var st=Ar;const yr=b,Pr=(r,t,e)=>yr(r,t,e)>=0;var ge=Pr;const _r=b,Cr=(r,t,e)=>_r(r,t,e)<=0;var Se=Cr;const br=nt,Dr=st,Ur=ne,xr=ge,Fr=Ie,Gr=Se,jr=(r,t,e,n)=>{switch(t){case"===":return typeof r=="object"&&(r=r.version),typeof e=="object"&&(e=e.version),r===e;case"!==":return typeof r=="object"&&(r=r.version),typeof e=="object"&&(e=e.version),r!==e;case"":case"=":case"==":return br(r,e,n);case"!=":return Dr(r,e,n);case">":return Ur(r,e,n);case">=":return xr(r,e,n);case"<":return Fr(r,e,n);case"<=":return Gr(r,e,n);default:throw new TypeError(`Invalid operator: ${t}`)}};var it=jr;const Xr=P,Vr=B,{safeRe:Z,t:J}=q,kr=(r,t)=>{if(r instanceof Xr)return r;if(typeof r=="number"&&(r=String(r)),typeof r!="string")return null;t=t||{};let e=null;if(!t.rtl)e=r.match(t.includePrerelease?Z[J.COERCEFULL]:Z[J.COERCE]);else{const o=t.includePrerelease?Z[J.COERCERTLFULL]:Z[J.COERCERTL];let l;for(;(l=o.exec(r))&&(!e||e.index+e[0].length!==r.length);)(!e||l.index+l[0].length!==e.index+e[0].length)&&(e=l),o.lastIndex=l.index+l[1].length+l[2].length;o.lastIndex=-1}if(e===null)return null;const n=e[2],i=e[3]||"0",a=e[4]||"0",c=t.includePrerelease&&e[5]?`-${e[5]}`:"",d=t.includePrerelease&&e[6]?`+${e[6]}`:"";return Vr(`${n}.${i}.${a}${c}${d}`,t)};var Br=kr;class Mr{constructor(){this.max=1e3,this.map=new Map}get(t){const e=this.map.get(t);if(e!==void 0)return this.map.delete(t),this.map.set(t,e),e}delete(t){return this.map.delete(t)}set(t,e){if(!this.delete(t)&&e!==void 0){if(this.map.size>=this.max){const i=this.map.keys().next().value;this.delete(i)}this.map.set(t,e)}return this}}var Hr=Mr,oe,De;function D(){if(De)return oe;De=1;const r=/\s+/g;class t{constructor(u,v){if(v=i(v),u instanceof t)return u.loose===!!v.loose&&u.includePrerelease===!!v.includePrerelease?u:new t(u.raw,v);if(u instanceof a)return this.raw=u.value,this.set=[[u]],this.formatted=void 0,this;if(this.options=v,this.loose=!!v.loose,this.includePrerelease=!!v.includePrerelease,this.raw=u.trim().replace(r," "),this.set=this.raw.split("||").map(p=>this.parseRange(p.trim())).filter(p=>p.length),!this.set.length)throw new TypeError(`Invalid SemVer Range: ${this.raw}`);if(this.set.length>1){const p=this.set[0];if(this.set=this.set.filter(R=>!m(R[0])),this.set.length===0)this.set=[p];else if(this.set.length>1){for(const R of this.set)if(R.length===1&&C(R[0])){this.set=[R];break}}}this.formatted=void 0}get range(){if(this.formatted===void 0){this.formatted="";for(let u=0;u<this.set.length;u++){u>0&&(this.formatted+="||");const v=this.set[u];for(let p=0;p<v.length;p++)p>0&&(this.formatted+=" "),this.formatted+=v[p].toString().trim()}}return this.formatted}format(){return this.range}toString(){return this.range}parseRange(u){const p=((this.options.includePrerelease&&h)|(this.options.loose&&y))+":"+u,R=n.get(p);if(R)return R;const $=this.options.loose,g=$?o[l.HYPHENRANGELOOSE]:o[l.HYPHENRANGE];u=u.replace(g,$t(this.options.includePrerelease)),c("hyphen replace",u),u=u.replace(o[l.COMPARATORTRIM],s),c("comparator trim",u),u=u.replace(o[l.TILDETRIM],f),c("tilde trim",u),u=u.replace(o[l.CARETTRIM],I),c("caret trim",u);let L=u.split(" ").map(N=>X(N,this.options)).join(" ").split(/\s+/).map(N=>pt(N,this.options));$&&(L=L.filter(N=>(c("loose invalid filter",N,this.options),!!N.match(o[l.COMPARATORLOOSE])))),c("range list",L);const S=new Map,w=L.map(N=>new a(N,this.options));for(const N of w){if(m(N))return[N];S.set(N.value,N)}S.size>1&&S.has("")&&S.delete("");const _=[...S.values()];return n.set(p,_),_}intersects(u,v){if(!(u instanceof t))throw new TypeError("a Range is required");return this.set.some(p=>O(p,v)&&u.set.some(R=>O(R,v)&&p.every($=>R.every(g=>$.intersects(g,v)))))}test(u){if(!u)return!1;if(typeof u=="string")try{u=new d(u,this.options)}catch{return!1}for(let v=0;v<this.set.length;v++)if(mt(this.set[v],u,this.options))return!0;return!1}}oe=t;const e=Hr,n=new e,i=ve,a=se(),c=re,d=P,{safeRe:o,t:l,comparatorTrimReplace:s,tildeTrimReplace:f,caretTrimReplace:I}=q,{FLAG_INCLUDE_PRERELEASE:h,FLAG_LOOSE:y}=te,m=E=>E.value==="<0.0.0-0",C=E=>E.value==="",O=(E,u)=>{let v=!0;const p=E.slice();let R=p.pop();for(;v&&p.length;)v=p.every($=>R.intersects($,u)),R=p.pop();return v},X=(E,u)=>(c("comp",E,u),E=ut(E,u),c("caret",E),E=G(E,u),c("tildes",E),E=ht(E,u),c("xrange",E),E=dt(E,u),c("stars",E),E),T=E=>!E||E.toLowerCase()==="x"||E==="*",G=(E,u)=>E.trim().split(/\s+/).map(v=>lt(v,u)).join(" "),lt=(E,u)=>{const v=u.loose?o[l.TILDELOOSE]:o[l.TILDE];return E.replace(v,(p,R,$,g,L)=>{c("tilde",E,p,R,$,g,L);let S;return T(R)?S="":T($)?S=`>=${R}.0.0 <${+R+1}.0.0-0`:T(g)?S=`>=${R}.${$}.0 <${R}.${+$+1}.0-0`:L?(c("replaceTilde pr",L),S=`>=${R}.${$}.${g}-${L} <${R}.${+$+1}.0-0`):S=`>=${R}.${$}.${g} <${R}.${+$+1}.0-0`,c("tilde return",S),S})},ut=(E,u)=>E.trim().split(/\s+/).map(v=>ft(v,u)).join(" "),ft=(E,u)=>{c("caret",E,u);const v=u.loose?o[l.CARETLOOSE]:o[l.CARET],p=u.includePrerelease?"-0":"";return E.replace(v,(R,$,g,L,S)=>{c("caret",E,R,$,g,L,S);let w;return T($)?w="":T(g)?w=`>=${$}.0.0${p} <${+$+1}.0.0-0`:T(L)?$==="0"?w=`>=${$}.${g}.0${p} <${$}.${+g+1}.0-0`:w=`>=${$}.${g}.0${p} <${+$+1}.0.0-0`:S?(c("replaceCaret pr",S),$==="0"?g==="0"?w=`>=${$}.${g}.${L}-${S} <${$}.${g}.${+L+1}-0`:w=`>=${$}.${g}.${L}-${S} <${$}.${+g+1}.0-0`:w=`>=${$}.${g}.${L}-${S} <${+$+1}.0.0-0`):(c("no pr"),$==="0"?g==="0"?w=`>=${$}.${g}.${L}${p} <${$}.${g}.${+L+1}-0`:w=`>=${$}.${g}.${L}${p} <${$}.${+g+1}.0-0`:w=`>=${$}.${g}.${L} <${+$+1}.0.0-0`),c("caret return",w),w})},ht=(E,u)=>(c("replaceXRanges",E,u),E.split(/\s+/).map(v=>Et(v,u)).join(" ")),Et=(E,u)=>{E=E.trim();const v=u.loose?o[l.XRANGELOOSE]:o[l.XRANGE];return E.replace(v,(p,R,$,g,L,S)=>{c("xRange",E,p,R,$,g,L,S);const w=T($),_=w||T(g),N=_||T(L),M=N;return R==="="&&M&&(R=""),S=u.includePrerelease?"-0":"",w?R===">"||R==="<"?p="<0.0.0-0":p="*":R&&M?(_&&(g=0),L=0,R===">"?(R=">=",_?($=+$+1,g=0,L=0):(g=+g+1,L=0)):R==="<="&&(R="<",_?$=+$+1:g=+g+1),R==="<"&&(S="-0"),p=`${R+$}.${g}.${L}${S}`):_?p=`>=${$}.0.0${S} <${+$+1}.0.0-0`:N&&(p=`>=${$}.${g}.0${S} <${$}.${+g+1}.0-0`),c("xRange return",p),p})},dt=(E,u)=>(c("replaceStars",E,u),E.trim().replace(o[l.STAR],"")),pt=(E,u)=>(c("replaceGTE0",E,u),E.trim().replace(o[u.includePrerelease?l.GTE0PRE:l.GTE0],"")),$t=E=>(u,v,p,R,$,g,L,S,w,_,N,M)=>(T(p)?v="":T(R)?v=`>=${p}.0.0${E?"-0":""}`:T($)?v=`>=${p}.${R}.0${E?"-0":""}`:g?v=`>=${v}`:v=`>=${v}${E?"-0":""}`,T(w)?S="":T(_)?S=`<${+w+1}.0.0-0`:T(N)?S=`<${w}.${+_+1}.0-0`:M?S=`<=${w}.${_}.${N}-${M}`:E?S=`<${w}.${_}.${+N+1}-0`:S=`<=${S}`,`${v} ${S}`.trim()),mt=(E,u,v)=>{for(let p=0;p<E.length;p++)if(!E[p].test(u))return!1;if(u.prerelease.length&&!v.includePrerelease){for(let p=0;p<E.length;p++)if(c(E[p].semver),E[p].semver!==a.ANY&&E[p].semver.prerelease.length>0){const R=E[p].semver;if(R.major===u.major&&R.minor===u.minor&&R.patch===u.patch)return!0}return!1}return!0};return oe}var ce,Ue;function se(){if(Ue)return ce;Ue=1;const r=Symbol("SemVer ANY");class t{static get ANY(){return r}constructor(s,f){if(f=e(f),s instanceof t){if(s.loose===!!f.loose)return s;s=s.value}s=s.trim().split(/\s+/).join(" "),c("comparator",s,f),this.options=f,this.loose=!!f.loose,this.parse(s),this.semver===r?this.value="":this.value=this.operator+this.semver.version,c("comp",this)}parse(s){const f=this.options.loose?n[i.COMPARATORLOOSE]:n[i.COMPARATOR],I=s.match(f);if(!I)throw new TypeError(`Invalid comparator: ${s}`);this.operator=I[1]!==void 0?I[1]:"",this.operator==="="&&(this.operator=""),I[2]?this.semver=new d(I[2],this.options.loose):this.semver=r}toString(){return this.value}test(s){if(c("Comparator.test",s,this.options.loose),this.semver===r||s===r)return!0;if(typeof s=="string")try{s=new d(s,this.options)}catch{return!1}return a(s,this.operator,this.semver,this.options)}intersects(s,f){if(!(s instanceof t))throw new TypeError("a Comparator is required");return this.operator===""?this.value===""?!0:new o(s.value,f).test(this.value):s.operator===""?s.value===""?!0:new o(this.value,f).test(s.semver):(f=e(f),f.includePrerelease&&(this.value==="<0.0.0-0"||s.value==="<0.0.0-0")||!f.includePrerelease&&(this.value.startsWith("<0.0.0")||s.value.startsWith("<0.0.0"))?!1:!!(this.operator.startsWith(">")&&s.operator.startsWith(">")||this.operator.startsWith("<")&&s.operator.startsWith("<")||this.semver.version===s.semver.version&&this.operator.includes("=")&&s.operator.includes("=")||a(this.semver,"<",s.semver,f)&&this.operator.startsWith(">")&&s.operator.startsWith("<")||a(this.semver,">",s.semver,f)&&this.operator.startsWith("<")&&s.operator.startsWith(">")))}}ce=t;const e=ve,{safeRe:n,t:i}=q,a=it,c=re,d=P,o=D();return ce}const qr=D(),zr=(r,t,e)=>{try{t=new qr(t,e)}catch{return!1}return t.test(r)};var ie=zr;const Wr=D(),Yr=(r,t)=>new Wr(r,t).set.map(e=>e.map(n=>n.value).join(" ").trim().split(" "));var Kr=Yr;const Zr=P,Jr=D(),Qr=(r,t,e)=>{let n=null,i=null,a=null;try{a=new Jr(t,e)}catch{return null}return r.forEach(c=>{a.test(c)&&(!n||i.compare(c)===-1)&&(n=c,i=new Zr(n,e))}),n};var en=Qr;const tn=P,rn=D(),nn=(r,t,e)=>{let n=null,i=null,a=null;try{a=new rn(t,e)}catch{return null}return r.forEach(c=>{a.test(c)&&(!n||i.compare(c)===1)&&(n=c,i=new tn(n,e))}),n};var sn=nn;const le=P,an=D(),xe=ne,on=(r,t)=>{r=new an(r,t);let e=new le("0.0.0");if(r.test(e)||(e=new le("0.0.0-0"),r.test(e)))return e;e=null;for(let n=0;n<r.set.length;++n){const i=r.set[n];let a=null;i.forEach(c=>{const d=new le(c.semver.version);switch(c.operator){case">":d.prerelease.length===0?d.patch++:d.prerelease.push(0),d.raw=d.format();case"":case">=":(!a||xe(d,a))&&(a=d);break;case"<":case"<=":break;default:throw new Error(`Unexpected operation: ${c.operator}`)}}),a&&(!e||xe(e,a))&&(e=a)}return e&&r.test(e)?e:null};var cn=on;const ln=D(),un=(r,t)=>{try{return new ln(r,t).range||"*"}catch{return null}};var fn=un;const hn=P,at=se(),{ANY:En}=at,dn=D(),pn=ie,Fe=ne,Ge=Ie,$n=Se,mn=ge,vn=(r,t,e,n)=>{r=new hn(r,n),t=new dn(t,n);let i,a,c,d,o;switch(e){case">":i=Fe,a=$n,c=Ge,d=">",o=">=";break;case"<":i=Ge,a=mn,c=Fe,d="<",o="<=";break;default:throw new TypeError('Must provide a hilo val of "<" or ">"')}if(pn(r,t,n))return!1;for(let l=0;l<t.set.length;++l){const s=t.set[l];let f=null,I=null;if(s.forEach(h=>{h.semver===En&&(h=new at(">=0.0.0")),f=f||h,I=I||h,i(h.semver,f.semver,n)?f=h:c(h.semver,I.semver,n)&&(I=h)}),f.operator===d||f.operator===o||(!I.operator||I.operator===d)&&a(r,I.semver))return!1;if(I.operator===o&&c(r,I.semver))return!1}return!0};var Le=vn;const Rn=Le,In=(r,t,e)=>Rn(r,t,">",e);var gn=In;const Sn=Le,Ln=(r,t,e)=>Sn(r,t,"<",e);var wn=Ln;const je=D(),Nn=(r,t,e)=>(r=new je(r,e),t=new je(t,e),r.intersects(t,e));var On=Nn;const Tn=ie,An=b;var yn=(r,t,e)=>{const n=[];let i=null,a=null;const c=r.sort((s,f)=>An(s,f,e));for(const s of c)Tn(s,t,e)?(a=s,i||(i=s)):(a&&n.push([i,a]),a=null,i=null);i&&n.push([i,null]);const d=[];for(const[s,f]of n)s===f?d.push(s):!f&&s===c[0]?d.push("*"):f?s===c[0]?d.push(`<=${f}`):d.push(`${s} - ${f}`):d.push(`>=${s}`);const o=d.join(" || "),l=typeof t.raw=="string"?t.raw:String(t);return o.length<l.length?o:t};const Xe=D(),we=se(),{ANY:ue}=we,H=ie,Ne=b,Pn=(r,t,e={})=>{if(r===t)return!0;r=new Xe(r,e),t=new Xe(t,e);let n=!1;e:for(const i of r.set){for(const a of t.set){const c=Cn(i,a,e);if(n=n||c!==null,c)continue e}if(n)return!1}return!0},_n=[new we(">=0.0.0-0")],Ve=[new we(">=0.0.0")],Cn=(r,t,e)=>{if(r===t)return!0;if(r.length===1&&r[0].semver===ue){if(t.length===1&&t[0].semver===ue)return!0;e.includePrerelease?r=_n:r=Ve}if(t.length===1&&t[0].semver===ue){if(e.includePrerelease)return!0;t=Ve}const n=new Set;let i,a;for(const h of r)h.operator===">"||h.operator===">="?i=ke(i,h,e):h.operator==="<"||h.operator==="<="?a=Be(a,h,e):n.add(h.semver);if(n.size>1)return null;let c;if(i&&a){if(c=Ne(i.semver,a.semver,e),c>0)return null;if(c===0&&(i.operator!==">="||a.operator!=="<="))return null}for(const h of n){if(i&&!H(h,String(i),e)||a&&!H(h,String(a),e))return null;for(const y of t)if(!H(h,String(y),e))return!1;return!0}let d,o,l,s,f=a&&!e.includePrerelease&&a.semver.prerelease.length?a.semver:!1,I=i&&!e.includePrerelease&&i.semver.prerelease.length?i.semver:!1;f&&f.prerelease.length===1&&a.operator==="<"&&f.prerelease[0]===0&&(f=!1);for(const h of t){if(s=s||h.operator===">"||h.operator===">=",l=l||h.operator==="<"||h.operator==="<=",i){if(I&&h.semver.prerelease&&h.semver.prerelease.length&&h.semver.major===I.major&&h.semver.minor===I.minor&&h.semver.patch===I.patch&&(I=!1),h.operator===">"||h.operator===">="){if(d=ke(i,h,e),d===h&&d!==i)return!1}else if(i.operator===">="&&!H(i.semver,String(h),e))return!1}if(a){if(f&&h.semver.prerelease&&h.semver.prerelease.length&&h.semver.major===f.major&&h.semver.minor===f.minor&&h.semver.patch===f.patch&&(f=!1),h.operator==="<"||h.operator==="<="){if(o=Be(a,h,e),o===h&&o!==a)return!1}else if(a.operator==="<="&&!H(a.semver,String(h),e))return!1}if(!h.operator&&(a||i)&&c!==0)return!1}return!(i&&l&&!a&&c!==0||a&&s&&!i&&c!==0||I||f)},ke=(r,t,e)=>{if(!r)return t;const n=Ne(r.semver,t.semver,e);return n>0?r:n<0||t.operator===">"&&r.operator===">="?t:r},Be=(r,t,e)=>{if(!r)return t;const n=Ne(r.semver,t.semver,e);return n<0?r:n>0||t.operator==="<"&&r.operator==="<="?t:r};var bn=Pn;const fe=q,Me=te,Dn=P,He=rt,Un=B,xn=Vt,Fn=Mt,Gn=qt,jn=Wt,Xn=Zt,Vn=er,kn=nr,Bn=ar,Mn=b,Hn=ur,qn=Er,zn=Re,Wn=mr,Yn=Ir,Kn=ne,Zn=Ie,Jn=nt,Qn=st,es=ge,ts=Se,rs=it,ns=Br,ss=se(),is=D(),as=ie,os=Kr,cs=en,ls=sn,us=cn,fs=fn,hs=Le,Es=gn,ds=wn,ps=On,$s=yn,ms=bn;var vs={parse:Un,valid:xn,clean:Fn,inc:Gn,diff:jn,major:Xn,minor:Vn,patch:kn,prerelease:Bn,compare:Mn,rcompare:Hn,compareLoose:qn,compareBuild:zn,sort:Wn,rsort:Yn,gt:Kn,lt:Zn,eq:Jn,neq:Qn,gte:es,lte:ts,cmp:rs,coerce:ns,Comparator:ss,Range:is,satisfies:as,toComparators:os,maxSatisfying:cs,minSatisfying:ls,minVersion:us,validRange:fs,outside:hs,gtr:Es,ltr:ds,intersects:ps,simplifyRange:$s,subset:ms,SemVer:Dn,re:fe.re,src:fe.src,tokens:fe.t,SEMVER_SPEC_VERSION:Me.SEMVER_SPEC_VERSION,RELEASE_TYPES:Me.RELEASE_TYPES,compareIdentifiers:He.compareIdentifiers,rcompareIdentifiers:He.rcompareIdentifiers};const qe=Je(vs);var pe=function(){return pe=Object.assign||function(r){for(var t,e=1,n=arguments.length;e<n;e++){t=arguments[e];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(r[i]=t[i])}return r},pe.apply(this,arguments)},V,A;(function(r){r.blue="blue",r.nanoS="nanoS",r.nanoSP="nanoSP",r.nanoX="nanoX",r.nanoFTS="nanoFTS"})(A||(A={}));var $e=(V={},V[A.blue]={id:A.blue,productName:"Ledger Blue",productIdMM:0,legacyUsbProductId:0,usbOnly:!0,memorySize:480*1024,masks:[822083584,822149120],getBlockSize:function(r){return 4*1024}},V[A.nanoS]={id:A.nanoS,productName:"Ledger Nano S",productIdMM:16,legacyUsbProductId:1,usbOnly:!0,memorySize:320*1024,masks:[823132160],getBlockSize:function(r){var t;return qe.lt((t=qe.coerce(r))!==null&&t!==void 0?t:"","2.0.0")?4*1024:2*1024}},V[A.nanoSP]={id:A.nanoSP,productName:"Ledger Nano S Plus",productIdMM:80,legacyUsbProductId:5,usbOnly:!0,memorySize:1536*1024,masks:[856686592],getBlockSize:function(r){return 32}},V[A.nanoX]={id:A.nanoX,productName:"Ledger Nano X",productIdMM:64,legacyUsbProductId:4,usbOnly:!1,memorySize:2*1024*1024,masks:[855638016],getBlockSize:function(r){return 4*1024},bluetoothSpec:[{serviceUuid:"13d63400-2c97-0004-0000-4c6564676572",notifyUuid:"13d63400-2c97-0004-0001-4c6564676572",writeUuid:"13d63400-2c97-0004-0002-4c6564676572",writeCmdUuid:"13d63400-2c97-0004-0003-4c6564676572"}]},V[A.nanoFTS]={id:A.nanoFTS,productName:"Ledger Nano FTS",productIdMM:96,legacyUsbProductId:6,usbOnly:!1,memorySize:2*1024*1024,masks:[857735168],getBlockSize:function(r){return 4*1024},bluetoothSpec:[{serviceUuid:"13d63400-2c97-6004-0000-4c6564676572",notifyUuid:"13d63400-2c97-6004-0001-4c6564676572",writeUuid:"13d63400-2c97-6004-0002-4c6564676572",writeCmdUuid:"13d63400-2c97-6004-0003-4c6564676572"}]},V);A.blue,A.nanoS,A.nanoSP,A.nanoX,A.nanoFTS;var ze=Object.values($e),ot=11415,We=function(r){var t=ze.find(function(i){return i.legacyUsbProductId===r});if(t)return t;var e=r>>8,n=ze.find(function(i){return i.productIdMM===e});return n},Rs=[],Ye={};for(var Is in $e){var Ke=$e[Is],he=Ke.bluetoothSpec;if(he)for(var Ee=0;Ee<he.length;Ee++){var Q=he[Ee];Rs.push(Q.serviceUuid),Ye[Q.serviceUuid]=Ye[Q.serviceUuid.replace(/-/g,"")]=pe({deviceModel:Ke},Q)}}let gs=0;const j=[],Ze=(r,t,e)=>{const n={type:r,id:String(++gs),date:new Date};t&&(n.message=t),Ls(n)},Ss=r=>(j.push(r),()=>{const t=j.indexOf(r);t!==-1&&(j[t]=j[j.length-1],j.pop())});function Ls(r){for(let t=0;t<j.length;t++)try{j[t](r)}catch(e){console.error(e)}}typeof window<"u"&&(window.__ledgerLogsListen=Ss);var ws=function(){var r=function(t,e){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(n[a]=i[a])},r(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");r(t,e);function n(){this.constructor=t}t.prototype=e===null?Object.create(e):(n.prototype=e.prototype,new n)}}(),x=function(r,t,e,n){function i(a){return a instanceof e?a:new e(function(c){c(a)})}return new(e||(e=Promise))(function(a,c){function d(s){try{l(n.next(s))}catch(f){c(f)}}function o(s){try{l(n.throw(s))}catch(f){c(f)}}function l(s){s.done?a(s.value):i(s.value).then(d,o)}l((n=n.apply(r,t||[])).next())})},F=function(r,t){var e={label:0,sent:function(){if(a[0]&1)throw a[1];return a[1]},trys:[],ops:[]},n,i,a,c;return c={next:d(0),throw:d(1),return:d(2)},typeof Symbol=="function"&&(c[Symbol.iterator]=function(){return this}),c;function d(l){return function(s){return o([l,s])}}function o(l){if(n)throw new TypeError("Generator is already executing.");for(;e;)try{if(n=1,i&&(a=l[0]&2?i.return:l[0]?i.throw||((a=i.return)&&a.call(i),0):i.next)&&!(a=a.call(i,l[1])).done)return a;switch(i=0,a&&(l=[l[0]&2,a.value]),l[0]){case 0:case 1:a=l;break;case 4:return e.label++,{value:l[1],done:!1};case 5:e.label++,i=l[1],l=[0];continue;case 7:l=e.ops.pop(),e.trys.pop();continue;default:if(a=e.trys,!(a=a.length>0&&a[a.length-1])&&(l[0]===6||l[0]===2)){e=0;continue}if(l[0]===3&&(!a||l[1]>a[0]&&l[1]<a[3])){e.label=l[1];break}if(l[0]===6&&e.label<a[1]){e.label=a[1],a=l;break}if(a&&e.label<a[2]){e.label=a[2],e.ops.push(l);break}a[2]&&e.ops.pop(),e.trys.pop();continue}l=t.call(r,e)}catch(s){l=[6,s],i=0}finally{n=a=0}if(l[0]&5)throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}},Ns=function(r,t){var e=typeof Symbol=="function"&&r[Symbol.iterator];if(!e)return r;var n=e.call(r),i,a=[],c;try{for(;(t===void 0||t-- >0)&&!(i=n.next()).done;)a.push(i.value)}catch(d){c={error:d}}finally{try{i&&!i.done&&(e=n.return)&&e.call(n)}finally{if(c)throw c.error}}return a},Os=[{vendorId:ot}],Ts=function(){return Promise.resolve(!!(window.navigator&&window.navigator.hid))},ee=function(){var r=navigator.hid;if(!r)throw new gt("navigator.hid is not supported","HIDNotSupported");return r};function ct(){return x(this,void 0,void 0,function(){var r;return F(this,function(t){switch(t.label){case 0:return[4,ee().requestDevice({filters:Os})];case 1:return r=t.sent(),Array.isArray(r)?[2,r]:[2,[r]]}})})}function me(){return x(this,void 0,void 0,function(){var r;return F(this,function(t){switch(t.label){case 0:return[4,ee().getDevices()];case 1:return r=t.sent(),[2,r.filter(function(e){return e.vendorId===ot})]}})})}function As(){return x(this,void 0,void 0,function(){var r,t;return F(this,function(e){switch(e.label){case 0:return[4,me()];case 1:return r=e.sent(),r.length>0?[2,r[0]]:[4,ct()];case 2:return t=e.sent(),[2,t[0]]}})})}var _s=function(r){ws(t,r);function t(e){var n=r.call(this)||this;return n.channel=Math.floor(Math.random()*65535),n.packetSize=64,n.inputs=[],n.read=function(){return n.inputs.length?Promise.resolve(n.inputs.shift()):new Promise(function(i){n.inputCallback=i})},n.onInputReport=function(i){var a=Buffer.from(i.data.buffer);n.inputCallback?(n.inputCallback(a),n.inputCallback=null):n.inputs.push(a)},n._disconnectEmitted=!1,n._emitDisconnect=function(i){n._disconnectEmitted||(n._disconnectEmitted=!0,n.emit("disconnect",i))},n.exchange=function(i){return x(n,void 0,void 0,function(){var a,c=this;return F(this,function(d){switch(d.label){case 0:return[4,this.exchangeAtomicImpl(function(){return x(c,void 0,void 0,function(){var o,l,s,f,I,h,y,m,C;return F(this,function(O){switch(O.label){case 0:o=this,l=o.channel,s=o.packetSize,Ze("apdu","=> "+i.toString("hex")),f=Nt(l,s),I=f.makeBlocks(i),h=0,O.label=1;case 1:return h<I.length?[4,this.device.sendReport(0,I[h])]:[3,4];case 2:O.sent(),O.label=3;case 3:return h++,[3,1];case 4:return(y=f.getReducedResult(m))?[3,6]:[4,this.read()];case 5:return C=O.sent(),m=f.reduceResponse(m,C),[3,4];case 6:return Ze("apdu","<= "+y.toString("hex")),[2,y]}})})}).catch(function(o){throw o&&o.message&&o.message.includes("write")?(c._emitDisconnect(o),new Rt(o.message)):o})];case 1:return a=d.sent(),[2,a]}})})},n.device=e,n.deviceModel=typeof e.productId=="number"?We(e.productId):void 0,e.addEventListener("inputreport",n.onInputReport),n}return t.request=function(){return x(this,void 0,void 0,function(){var e,n;return F(this,function(i){switch(i.label){case 0:return[4,ct()];case 1:return e=Ns.apply(void 0,[i.sent(),1]),n=e[0],[2,t.open(n)]}})})},t.openConnected=function(){return x(this,void 0,void 0,function(){var e;return F(this,function(n){switch(n.label){case 0:return[4,me()];case 1:return e=n.sent(),e.length===0?[2,null]:[2,t.open(e[0])]}})})},t.open=function(e){return x(this,void 0,void 0,function(){var n,i;return F(this,function(a){switch(a.label){case 0:return[4,e.open()];case 1:return a.sent(),n=new t(e),i=function(c){e===c.device&&(ee().removeEventListener("disconnect",i),n._emitDisconnect(new St))},ee().addEventListener("disconnect",i),[2,n]}})})},t.prototype.close=function(){return x(this,void 0,void 0,function(){return F(this,function(e){switch(e.label){case 0:return[4,this.exchangeBusyPromise];case 1:return e.sent(),this.device.removeEventListener("inputreport",this.onInputReport),[4,this.device.close()];case 2:return e.sent(),[2]}})})},t.prototype.setScrambleKey=function(){},t.isSupported=Ts,t.list=me,t.listen=function(e){var n=!1;As().then(function(a){if(!a)e.error(new Oe("Access denied to use Ledger device"));else if(!n){var c=typeof a.productId=="number"?We(a.productId):void 0;e.next({type:"add",descriptor:a,deviceModel:c}),e.complete()}},function(a){e.error(new Oe(a.message))});function i(){n=!0}return{unsubscribe:i}},t}(It);export{_s as default};
