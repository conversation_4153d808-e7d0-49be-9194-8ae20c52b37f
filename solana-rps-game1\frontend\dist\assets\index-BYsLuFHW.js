var xy=Object.defineProperty;var Sy=(e,t,n)=>t in e?xy(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var J=(e,t,n)=>(Sy(e,typeof t!="symbol"?t+"":t,n),n);import{b as ke,g as Yo,C as oo,a as xs,i as Ny,e as Ey,c as Vi,V as My,T as wt,d as uc,S as by,P as se,r as wf,f as tm,s as Yn,h as nm,u as fi,j as td,k as Js,L as hi,l as ya,K as vf,m as Zt,n as rm}from"./solana-D2Z2xLUD.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))r(s);new MutationObserver(s=>{for(const i of s)if(i.type==="childList")for(const o of i.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function n(s){const i={};return s.integrity&&(i.integrity=s.integrity),s.referrerPolicy&&(i.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?i.credentials="include":s.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(s){if(s.ep)return;s.ep=!0;const i=n(s);fetch(s.href,i)}})();window.global=window;window.Buffer=ke.Buffer;window.process=window.process||{env:{},browser:!0,nextTick:e=>setTimeout(e,0)};try{const t=Buffer.from("7Y9dRMY6V9cmVkXNFrHeUZmYf2tAV5wSVFcYyD5bLQpZ");console.log("✅ Polyfills initialized successfully!",{hasBuffer:typeof Buffer<"u",hasBufferFrom:typeof Buffer.from=="function",bufferTest:t.toString("hex").substring(0,10)+"...",hasProcess:typeof process<"u"})}catch(e){console.error("❌ Failed to initialize polyfills:",e)}console.log("Polyfills loaded successfully:",{buffer:typeof window.Buffer<"u",bufferFrom:typeof window.Buffer.from=="function",bufferAlloc:typeof window.Buffer.alloc=="function",process:typeof window.process<"u"});var sm={exports:{}},Ko={},im={exports:{}},ee={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Oi=Symbol.for("react.element"),jy=Symbol.for("react.portal"),_y=Symbol.for("react.fragment"),Ay=Symbol.for("react.strict_mode"),Ty=Symbol.for("react.profiler"),Cy=Symbol.for("react.provider"),Iy=Symbol.for("react.context"),ky=Symbol.for("react.forward_ref"),Ly=Symbol.for("react.suspense"),Oy=Symbol.for("react.memo"),Py=Symbol.for("react.lazy"),xf=Symbol.iterator;function Ry(e){return e===null||typeof e!="object"?null:(e=xf&&e[xf]||e["@@iterator"],typeof e=="function"?e:null)}var am={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},om=Object.assign,lm={};function Ss(e,t,n){this.props=e,this.context=t,this.refs=lm,this.updater=n||am}Ss.prototype.isReactComponent={};Ss.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Ss.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function cm(){}cm.prototype=Ss.prototype;function nd(e,t,n){this.props=e,this.context=t,this.refs=lm,this.updater=n||am}var rd=nd.prototype=new cm;rd.constructor=nd;om(rd,Ss.prototype);rd.isPureReactComponent=!0;var Sf=Array.isArray,um=Object.prototype.hasOwnProperty,sd={current:null},dm={key:!0,ref:!0,__self:!0,__source:!0};function fm(e,t,n){var r,s={},i=null,o=null;if(t!=null)for(r in t.ref!==void 0&&(o=t.ref),t.key!==void 0&&(i=""+t.key),t)um.call(t,r)&&!dm.hasOwnProperty(r)&&(s[r]=t[r]);var l=arguments.length-2;if(l===1)s.children=n;else if(1<l){for(var c=Array(l),u=0;u<l;u++)c[u]=arguments[u+2];s.children=c}if(e&&e.defaultProps)for(r in l=e.defaultProps,l)s[r]===void 0&&(s[r]=l[r]);return{$$typeof:Oi,type:e,key:i,ref:o,props:s,_owner:sd.current}}function Dy(e,t){return{$$typeof:Oi,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function id(e){return typeof e=="object"&&e!==null&&e.$$typeof===Oi}function zy(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Nf=/\/+/g;function Sl(e,t){return typeof e=="object"&&e!==null&&e.key!=null?zy(""+e.key):t.toString(36)}function wa(e,t,n,r,s){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var o=!1;if(e===null)o=!0;else switch(i){case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case Oi:case jy:o=!0}}if(o)return o=e,s=s(o),e=r===""?"."+Sl(o,0):r,Sf(s)?(n="",e!=null&&(n=e.replace(Nf,"$&/")+"/"),wa(s,t,n,"",function(u){return u})):s!=null&&(id(s)&&(s=Dy(s,n+(!s.key||o&&o.key===s.key?"":(""+s.key).replace(Nf,"$&/")+"/")+e)),t.push(s)),1;if(o=0,r=r===""?".":r+":",Sf(e))for(var l=0;l<e.length;l++){i=e[l];var c=r+Sl(i,l);o+=wa(i,t,n,c,s)}else if(c=Ry(e),typeof c=="function")for(e=c.call(e),l=0;!(i=e.next()).done;)i=i.value,c=r+Sl(i,l++),o+=wa(i,t,n,c,s);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return o}function Qi(e,t,n){if(e==null)return e;var r=[],s=0;return wa(e,r,"","",function(i){return t.call(n,i,s++)}),r}function Wy(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var qe={current:null},va={transition:null},By={ReactCurrentDispatcher:qe,ReactCurrentBatchConfig:va,ReactCurrentOwner:sd};function hm(){throw Error("act(...) is not supported in production builds of React.")}ee.Children={map:Qi,forEach:function(e,t,n){Qi(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Qi(e,function(){t++}),t},toArray:function(e){return Qi(e,function(t){return t})||[]},only:function(e){if(!id(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};ee.Component=Ss;ee.Fragment=_y;ee.Profiler=Ty;ee.PureComponent=nd;ee.StrictMode=Ay;ee.Suspense=Ly;ee.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=By;ee.act=hm;ee.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=om({},e.props),s=e.key,i=e.ref,o=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,o=sd.current),t.key!==void 0&&(s=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(c in t)um.call(t,c)&&!dm.hasOwnProperty(c)&&(r[c]=t[c]===void 0&&l!==void 0?l[c]:t[c])}var c=arguments.length-2;if(c===1)r.children=n;else if(1<c){l=Array(c);for(var u=0;u<c;u++)l[u]=arguments[u+2];r.children=l}return{$$typeof:Oi,type:e.type,key:s,ref:i,props:r,_owner:o}};ee.createContext=function(e){return e={$$typeof:Iy,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Cy,_context:e},e.Consumer=e};ee.createElement=fm;ee.createFactory=function(e){var t=fm.bind(null,e);return t.type=e,t};ee.createRef=function(){return{current:null}};ee.forwardRef=function(e){return{$$typeof:ky,render:e}};ee.isValidElement=id;ee.lazy=function(e){return{$$typeof:Py,_payload:{_status:-1,_result:e},_init:Wy}};ee.memo=function(e,t){return{$$typeof:Oy,type:e,compare:t===void 0?null:t}};ee.startTransition=function(e){var t=va.transition;va.transition={};try{e()}finally{va.transition=t}};ee.unstable_act=hm;ee.useCallback=function(e,t){return qe.current.useCallback(e,t)};ee.useContext=function(e){return qe.current.useContext(e)};ee.useDebugValue=function(){};ee.useDeferredValue=function(e){return qe.current.useDeferredValue(e)};ee.useEffect=function(e,t){return qe.current.useEffect(e,t)};ee.useId=function(){return qe.current.useId()};ee.useImperativeHandle=function(e,t,n){return qe.current.useImperativeHandle(e,t,n)};ee.useInsertionEffect=function(e,t){return qe.current.useInsertionEffect(e,t)};ee.useLayoutEffect=function(e,t){return qe.current.useLayoutEffect(e,t)};ee.useMemo=function(e,t){return qe.current.useMemo(e,t)};ee.useReducer=function(e,t,n){return qe.current.useReducer(e,t,n)};ee.useRef=function(e){return qe.current.useRef(e)};ee.useState=function(e){return qe.current.useState(e)};ee.useSyncExternalStore=function(e,t,n){return qe.current.useSyncExternalStore(e,t,n)};ee.useTransition=function(){return qe.current.useTransition()};ee.version="18.3.1";im.exports=ee;var E=im.exports;const W=Yo(E);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Uy=E,Fy=Symbol.for("react.element"),Gy=Symbol.for("react.fragment"),$y=Object.prototype.hasOwnProperty,Hy=Uy.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Yy={key:!0,ref:!0,__self:!0,__source:!0};function mm(e,t,n){var r,s={},i=null,o=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(o=t.ref);for(r in t)$y.call(t,r)&&!Yy.hasOwnProperty(r)&&(s[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)s[r]===void 0&&(s[r]=t[r]);return{$$typeof:Fy,type:e,key:i,ref:o,props:s,_owner:Hy.current}}Ko.Fragment=Gy;Ko.jsx=mm;Ko.jsxs=mm;sm.exports=Ko;var a=sm.exports;typeof window<"u"&&(window.Buffer=ke.Buffer,window.global=window,window.process||(window.process={env:{},browser:!0,nextTick:e=>setTimeout(e,0)}));typeof globalThis<"u"&&(globalThis.Buffer=ke.Buffer);var dc={},pm={exports:{}},pt={},gm={exports:{}},ym={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(L,P){var U=L.length;L.push(P);e:for(;0<U;){var $=U-1>>>1,Y=L[$];if(0<s(Y,P))L[$]=P,L[U]=Y,U=$;else break e}}function n(L){return L.length===0?null:L[0]}function r(L){if(L.length===0)return null;var P=L[0],U=L.pop();if(U!==P){L[0]=U;e:for(var $=0,Y=L.length,ae=Y>>>1;$<ae;){var Q=2*($+1)-1,Re=L[Q],at=Q+1,Mr=L[at];if(0>s(Re,U))at<Y&&0>s(Mr,Re)?(L[$]=Mr,L[at]=U,$=at):(L[$]=Re,L[Q]=U,$=Q);else if(at<Y&&0>s(Mr,U))L[$]=Mr,L[at]=U,$=at;else break e}}return P}function s(L,P){var U=L.sortIndex-P.sortIndex;return U!==0?U:L.id-P.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var o=Date,l=o.now();e.unstable_now=function(){return o.now()-l}}var c=[],u=[],d=1,h=null,y=3,g=!1,S=!1,N=!1,A=typeof setTimeout=="function"?setTimeout:null,p=typeof clearTimeout=="function"?clearTimeout:null,m=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function f(L){for(var P=n(u);P!==null;){if(P.callback===null)r(u);else if(P.startTime<=L)r(u),P.sortIndex=P.expirationTime,t(c,P);else break;P=n(u)}}function x(L){if(N=!1,f(L),!S)if(n(c)!==null)S=!0,R(M);else{var P=n(u);P!==null&&z(x,P.startTime-L)}}function M(L,P){S=!1,N&&(N=!1,p(b),b=-1),g=!0;var U=y;try{for(f(P),h=n(c);h!==null&&(!(h.expirationTime>P)||L&&!O());){var $=h.callback;if(typeof $=="function"){h.callback=null,y=h.priorityLevel;var Y=$(h.expirationTime<=P);P=e.unstable_now(),typeof Y=="function"?h.callback=Y:h===n(c)&&r(c),f(P)}else r(c);h=n(c)}if(h!==null)var ae=!0;else{var Q=n(u);Q!==null&&z(x,Q.startTime-P),ae=!1}return ae}finally{h=null,y=U,g=!1}}var w=!1,v=null,b=-1,T=5,C=-1;function O(){return!(e.unstable_now()-C<T)}function D(){if(v!==null){var L=e.unstable_now();C=L;var P=!0;try{P=v(!0,L)}finally{P?I():(w=!1,v=null)}}else w=!1}var I;if(typeof m=="function")I=function(){m(D)};else if(typeof MessageChannel<"u"){var F=new MessageChannel,_=F.port2;F.port1.onmessage=D,I=function(){_.postMessage(null)}}else I=function(){A(D,0)};function R(L){v=L,w||(w=!0,I())}function z(L,P){b=A(function(){L(e.unstable_now())},P)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(L){L.callback=null},e.unstable_continueExecution=function(){S||g||(S=!0,R(M))},e.unstable_forceFrameRate=function(L){0>L||125<L?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):T=0<L?Math.floor(1e3/L):5},e.unstable_getCurrentPriorityLevel=function(){return y},e.unstable_getFirstCallbackNode=function(){return n(c)},e.unstable_next=function(L){switch(y){case 1:case 2:case 3:var P=3;break;default:P=y}var U=y;y=P;try{return L()}finally{y=U}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(L,P){switch(L){case 1:case 2:case 3:case 4:case 5:break;default:L=3}var U=y;y=L;try{return P()}finally{y=U}},e.unstable_scheduleCallback=function(L,P,U){var $=e.unstable_now();switch(typeof U=="object"&&U!==null?(U=U.delay,U=typeof U=="number"&&0<U?$+U:$):U=$,L){case 1:var Y=-1;break;case 2:Y=250;break;case 5:Y=**********;break;case 4:Y=1e4;break;default:Y=5e3}return Y=U+Y,L={id:d++,callback:P,priorityLevel:L,startTime:U,expirationTime:Y,sortIndex:-1},U>$?(L.sortIndex=U,t(u,L),n(c)===null&&L===n(u)&&(N?(p(b),b=-1):N=!0,z(x,U-$))):(L.sortIndex=Y,t(c,L),S||g||(S=!0,R(M))),L},e.unstable_shouldYield=O,e.unstable_wrapCallback=function(L){var P=y;return function(){var U=y;y=P;try{return L.apply(this,arguments)}finally{y=U}}}})(ym);gm.exports=ym;var Ky=gm.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Vy=E,mt=Ky;function B(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var wm=new Set,mi={};function vr(e,t){os(e,t),os(e+"Capture",t)}function os(e,t){for(mi[e]=t,e=0;e<t.length;e++)wm.add(t[e])}var on=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),fc=Object.prototype.hasOwnProperty,Qy=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Ef={},Mf={};function Zy(e){return fc.call(Mf,e)?!0:fc.call(Ef,e)?!1:Qy.test(e)?Mf[e]=!0:(Ef[e]=!0,!1)}function Jy(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function qy(e,t,n,r){if(t===null||typeof t>"u"||Jy(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Xe(e,t,n,r,s,i,o){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=s,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=o}var Fe={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Fe[e]=new Xe(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Fe[t]=new Xe(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Fe[e]=new Xe(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Fe[e]=new Xe(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Fe[e]=new Xe(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Fe[e]=new Xe(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Fe[e]=new Xe(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Fe[e]=new Xe(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Fe[e]=new Xe(e,5,!1,e.toLowerCase(),null,!1,!1)});var ad=/[\-:]([a-z])/g;function od(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(ad,od);Fe[t]=new Xe(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(ad,od);Fe[t]=new Xe(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(ad,od);Fe[t]=new Xe(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Fe[e]=new Xe(e,1,!1,e.toLowerCase(),null,!1,!1)});Fe.xlinkHref=new Xe("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Fe[e]=new Xe(e,1,!1,e.toLowerCase(),null,!0,!0)});function ld(e,t,n,r){var s=Fe.hasOwnProperty(t)?Fe[t]:null;(s!==null?s.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(qy(t,n,s,r)&&(n=null),r||s===null?Zy(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):s.mustUseProperty?e[s.propertyName]=n===null?s.type===3?!1:"":n:(t=s.attributeName,r=s.attributeNamespace,n===null?e.removeAttribute(t):(s=s.type,n=s===3||s===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var hn=Vy.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Zi=Symbol.for("react.element"),Rr=Symbol.for("react.portal"),Dr=Symbol.for("react.fragment"),cd=Symbol.for("react.strict_mode"),hc=Symbol.for("react.profiler"),vm=Symbol.for("react.provider"),xm=Symbol.for("react.context"),ud=Symbol.for("react.forward_ref"),mc=Symbol.for("react.suspense"),pc=Symbol.for("react.suspense_list"),dd=Symbol.for("react.memo"),vn=Symbol.for("react.lazy"),Sm=Symbol.for("react.offscreen"),bf=Symbol.iterator;function _s(e){return e===null||typeof e!="object"?null:(e=bf&&e[bf]||e["@@iterator"],typeof e=="function"?e:null)}var Se=Object.assign,Nl;function Rs(e){if(Nl===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Nl=t&&t[1]||""}return`
`+Nl+e}var El=!1;function Ml(e,t){if(!e||El)return"";El=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var s=u.stack.split(`
`),i=r.stack.split(`
`),o=s.length-1,l=i.length-1;1<=o&&0<=l&&s[o]!==i[l];)l--;for(;1<=o&&0<=l;o--,l--)if(s[o]!==i[l]){if(o!==1||l!==1)do if(o--,l--,0>l||s[o]!==i[l]){var c=`
`+s[o].replace(" at new "," at ");return e.displayName&&c.includes("<anonymous>")&&(c=c.replace("<anonymous>",e.displayName)),c}while(1<=o&&0<=l);break}}}finally{El=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Rs(e):""}function Xy(e){switch(e.tag){case 5:return Rs(e.type);case 16:return Rs("Lazy");case 13:return Rs("Suspense");case 19:return Rs("SuspenseList");case 0:case 2:case 15:return e=Ml(e.type,!1),e;case 11:return e=Ml(e.type.render,!1),e;case 1:return e=Ml(e.type,!0),e;default:return""}}function gc(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Dr:return"Fragment";case Rr:return"Portal";case hc:return"Profiler";case cd:return"StrictMode";case mc:return"Suspense";case pc:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case xm:return(e.displayName||"Context")+".Consumer";case vm:return(e._context.displayName||"Context")+".Provider";case ud:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case dd:return t=e.displayName||null,t!==null?t:gc(e.type)||"Memo";case vn:t=e._payload,e=e._init;try{return gc(e(t))}catch{}}return null}function ew(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return gc(t);case 8:return t===cd?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function zn(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Nm(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function tw(e){var t=Nm(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var s=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return s.call(this)},set:function(o){r=""+o,i.call(this,o)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(o){r=""+o},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Ji(e){e._valueTracker||(e._valueTracker=tw(e))}function Em(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Nm(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function lo(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function yc(e,t){var n=t.checked;return Se({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function jf(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=zn(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Mm(e,t){t=t.checked,t!=null&&ld(e,"checked",t,!1)}function wc(e,t){Mm(e,t);var n=zn(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?vc(e,t.type,n):t.hasOwnProperty("defaultValue")&&vc(e,t.type,zn(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function _f(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function vc(e,t,n){(t!=="number"||lo(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Ds=Array.isArray;function Jr(e,t,n,r){if(e=e.options,t){t={};for(var s=0;s<n.length;s++)t["$"+n[s]]=!0;for(n=0;n<e.length;n++)s=t.hasOwnProperty("$"+e[n].value),e[n].selected!==s&&(e[n].selected=s),s&&r&&(e[n].defaultSelected=!0)}else{for(n=""+zn(n),t=null,s=0;s<e.length;s++){if(e[s].value===n){e[s].selected=!0,r&&(e[s].defaultSelected=!0);return}t!==null||e[s].disabled||(t=e[s])}t!==null&&(t.selected=!0)}}function xc(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(B(91));return Se({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Af(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(B(92));if(Ds(n)){if(1<n.length)throw Error(B(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:zn(n)}}function bm(e,t){var n=zn(t.value),r=zn(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Tf(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function jm(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Sc(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?jm(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var qi,_m=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,s){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,s)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(qi=qi||document.createElement("div"),qi.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=qi.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function pi(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var qs={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},nw=["Webkit","ms","Moz","O"];Object.keys(qs).forEach(function(e){nw.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),qs[t]=qs[e]})});function Am(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||qs.hasOwnProperty(e)&&qs[e]?(""+t).trim():t+"px"}function Tm(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,s=Am(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,s):e[n]=s}}var rw=Se({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Nc(e,t){if(t){if(rw[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(B(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(B(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(B(61))}if(t.style!=null&&typeof t.style!="object")throw Error(B(62))}}function Ec(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Mc=null;function fd(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var bc=null,qr=null,Xr=null;function Cf(e){if(e=Di(e)){if(typeof bc!="function")throw Error(B(280));var t=e.stateNode;t&&(t=qo(t),bc(e.stateNode,e.type,t))}}function Cm(e){qr?Xr?Xr.push(e):Xr=[e]:qr=e}function Im(){if(qr){var e=qr,t=Xr;if(Xr=qr=null,Cf(e),t)for(e=0;e<t.length;e++)Cf(t[e])}}function km(e,t){return e(t)}function Lm(){}var bl=!1;function Om(e,t,n){if(bl)return e(t,n);bl=!0;try{return km(e,t,n)}finally{bl=!1,(qr!==null||Xr!==null)&&(Lm(),Im())}}function gi(e,t){var n=e.stateNode;if(n===null)return null;var r=qo(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(B(231,t,typeof n));return n}var jc=!1;if(on)try{var As={};Object.defineProperty(As,"passive",{get:function(){jc=!0}}),window.addEventListener("test",As,As),window.removeEventListener("test",As,As)}catch{jc=!1}function sw(e,t,n,r,s,i,o,l,c){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(d){this.onError(d)}}var Xs=!1,co=null,uo=!1,_c=null,iw={onError:function(e){Xs=!0,co=e}};function aw(e,t,n,r,s,i,o,l,c){Xs=!1,co=null,sw.apply(iw,arguments)}function ow(e,t,n,r,s,i,o,l,c){if(aw.apply(this,arguments),Xs){if(Xs){var u=co;Xs=!1,co=null}else throw Error(B(198));uo||(uo=!0,_c=u)}}function xr(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Pm(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function If(e){if(xr(e)!==e)throw Error(B(188))}function lw(e){var t=e.alternate;if(!t){if(t=xr(e),t===null)throw Error(B(188));return t!==e?null:e}for(var n=e,r=t;;){var s=n.return;if(s===null)break;var i=s.alternate;if(i===null){if(r=s.return,r!==null){n=r;continue}break}if(s.child===i.child){for(i=s.child;i;){if(i===n)return If(s),e;if(i===r)return If(s),t;i=i.sibling}throw Error(B(188))}if(n.return!==r.return)n=s,r=i;else{for(var o=!1,l=s.child;l;){if(l===n){o=!0,n=s,r=i;break}if(l===r){o=!0,r=s,n=i;break}l=l.sibling}if(!o){for(l=i.child;l;){if(l===n){o=!0,n=i,r=s;break}if(l===r){o=!0,r=i,n=s;break}l=l.sibling}if(!o)throw Error(B(189))}}if(n.alternate!==r)throw Error(B(190))}if(n.tag!==3)throw Error(B(188));return n.stateNode.current===n?e:t}function Rm(e){return e=lw(e),e!==null?Dm(e):null}function Dm(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Dm(e);if(t!==null)return t;e=e.sibling}return null}var zm=mt.unstable_scheduleCallback,kf=mt.unstable_cancelCallback,cw=mt.unstable_shouldYield,uw=mt.unstable_requestPaint,Ee=mt.unstable_now,dw=mt.unstable_getCurrentPriorityLevel,hd=mt.unstable_ImmediatePriority,Wm=mt.unstable_UserBlockingPriority,fo=mt.unstable_NormalPriority,fw=mt.unstable_LowPriority,Bm=mt.unstable_IdlePriority,Vo=null,Ht=null;function hw(e){if(Ht&&typeof Ht.onCommitFiberRoot=="function")try{Ht.onCommitFiberRoot(Vo,e,void 0,(e.current.flags&128)===128)}catch{}}var Ot=Math.clz32?Math.clz32:gw,mw=Math.log,pw=Math.LN2;function gw(e){return e>>>=0,e===0?32:31-(mw(e)/pw|0)|0}var Xi=64,ea=4194304;function zs(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ho(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,s=e.suspendedLanes,i=e.pingedLanes,o=n&268435455;if(o!==0){var l=o&~s;l!==0?r=zs(l):(i&=o,i!==0&&(r=zs(i)))}else o=n&~s,o!==0?r=zs(o):i!==0&&(r=zs(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&s)&&(s=r&-r,i=t&-t,s>=i||s===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Ot(t),s=1<<n,r|=e[n],t&=~s;return r}function yw(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function ww(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,s=e.expirationTimes,i=e.pendingLanes;0<i;){var o=31-Ot(i),l=1<<o,c=s[o];c===-1?(!(l&n)||l&r)&&(s[o]=yw(l,t)):c<=t&&(e.expiredLanes|=l),i&=~l}}function Ac(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Um(){var e=Xi;return Xi<<=1,!(Xi&4194240)&&(Xi=64),e}function jl(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Pi(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Ot(t),e[t]=n}function vw(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var s=31-Ot(n),i=1<<s;t[s]=0,r[s]=-1,e[s]=-1,n&=~i}}function md(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Ot(n),s=1<<r;s&t|e[r]&t&&(e[r]|=t),n&=~s}}var ce=0;function Fm(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Gm,pd,$m,Hm,Ym,Tc=!1,ta=[],Cn=null,In=null,kn=null,yi=new Map,wi=new Map,Nn=[],xw="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Lf(e,t){switch(e){case"focusin":case"focusout":Cn=null;break;case"dragenter":case"dragleave":In=null;break;case"mouseover":case"mouseout":kn=null;break;case"pointerover":case"pointerout":yi.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":wi.delete(t.pointerId)}}function Ts(e,t,n,r,s,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[s]},t!==null&&(t=Di(t),t!==null&&pd(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,s!==null&&t.indexOf(s)===-1&&t.push(s),e)}function Sw(e,t,n,r,s){switch(t){case"focusin":return Cn=Ts(Cn,e,t,n,r,s),!0;case"dragenter":return In=Ts(In,e,t,n,r,s),!0;case"mouseover":return kn=Ts(kn,e,t,n,r,s),!0;case"pointerover":var i=s.pointerId;return yi.set(i,Ts(yi.get(i)||null,e,t,n,r,s)),!0;case"gotpointercapture":return i=s.pointerId,wi.set(i,Ts(wi.get(i)||null,e,t,n,r,s)),!0}return!1}function Km(e){var t=tr(e.target);if(t!==null){var n=xr(t);if(n!==null){if(t=n.tag,t===13){if(t=Pm(n),t!==null){e.blockedOn=t,Ym(e.priority,function(){$m(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function xa(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Cc(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Mc=r,n.target.dispatchEvent(r),Mc=null}else return t=Di(n),t!==null&&pd(t),e.blockedOn=n,!1;t.shift()}return!0}function Of(e,t,n){xa(e)&&n.delete(t)}function Nw(){Tc=!1,Cn!==null&&xa(Cn)&&(Cn=null),In!==null&&xa(In)&&(In=null),kn!==null&&xa(kn)&&(kn=null),yi.forEach(Of),wi.forEach(Of)}function Cs(e,t){e.blockedOn===t&&(e.blockedOn=null,Tc||(Tc=!0,mt.unstable_scheduleCallback(mt.unstable_NormalPriority,Nw)))}function vi(e){function t(s){return Cs(s,e)}if(0<ta.length){Cs(ta[0],e);for(var n=1;n<ta.length;n++){var r=ta[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Cn!==null&&Cs(Cn,e),In!==null&&Cs(In,e),kn!==null&&Cs(kn,e),yi.forEach(t),wi.forEach(t),n=0;n<Nn.length;n++)r=Nn[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Nn.length&&(n=Nn[0],n.blockedOn===null);)Km(n),n.blockedOn===null&&Nn.shift()}var es=hn.ReactCurrentBatchConfig,mo=!0;function Ew(e,t,n,r){var s=ce,i=es.transition;es.transition=null;try{ce=1,gd(e,t,n,r)}finally{ce=s,es.transition=i}}function Mw(e,t,n,r){var s=ce,i=es.transition;es.transition=null;try{ce=4,gd(e,t,n,r)}finally{ce=s,es.transition=i}}function gd(e,t,n,r){if(mo){var s=Cc(e,t,n,r);if(s===null)Rl(e,t,r,po,n),Lf(e,r);else if(Sw(s,e,t,n,r))r.stopPropagation();else if(Lf(e,r),t&4&&-1<xw.indexOf(e)){for(;s!==null;){var i=Di(s);if(i!==null&&Gm(i),i=Cc(e,t,n,r),i===null&&Rl(e,t,r,po,n),i===s)break;s=i}s!==null&&r.stopPropagation()}else Rl(e,t,r,null,n)}}var po=null;function Cc(e,t,n,r){if(po=null,e=fd(r),e=tr(e),e!==null)if(t=xr(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Pm(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return po=e,null}function Vm(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(dw()){case hd:return 1;case Wm:return 4;case fo:case fw:return 16;case Bm:return 536870912;default:return 16}default:return 16}}var jn=null,yd=null,Sa=null;function Qm(){if(Sa)return Sa;var e,t=yd,n=t.length,r,s="value"in jn?jn.value:jn.textContent,i=s.length;for(e=0;e<n&&t[e]===s[e];e++);var o=n-e;for(r=1;r<=o&&t[n-r]===s[i-r];r++);return Sa=s.slice(e,1<r?1-r:void 0)}function Na(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function na(){return!0}function Pf(){return!1}function gt(e){function t(n,r,s,i,o){this._reactName=n,this._targetInst=s,this.type=r,this.nativeEvent=i,this.target=o,this.currentTarget=null;for(var l in e)e.hasOwnProperty(l)&&(n=e[l],this[l]=n?n(i):i[l]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?na:Pf,this.isPropagationStopped=Pf,this}return Se(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=na)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=na)},persist:function(){},isPersistent:na}),t}var Ns={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},wd=gt(Ns),Ri=Se({},Ns,{view:0,detail:0}),bw=gt(Ri),_l,Al,Is,Qo=Se({},Ri,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:vd,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Is&&(Is&&e.type==="mousemove"?(_l=e.screenX-Is.screenX,Al=e.screenY-Is.screenY):Al=_l=0,Is=e),_l)},movementY:function(e){return"movementY"in e?e.movementY:Al}}),Rf=gt(Qo),jw=Se({},Qo,{dataTransfer:0}),_w=gt(jw),Aw=Se({},Ri,{relatedTarget:0}),Tl=gt(Aw),Tw=Se({},Ns,{animationName:0,elapsedTime:0,pseudoElement:0}),Cw=gt(Tw),Iw=Se({},Ns,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),kw=gt(Iw),Lw=Se({},Ns,{data:0}),Df=gt(Lw),Ow={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Pw={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Rw={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Dw(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Rw[e])?!!t[e]:!1}function vd(){return Dw}var zw=Se({},Ri,{key:function(e){if(e.key){var t=Ow[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Na(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Pw[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:vd,charCode:function(e){return e.type==="keypress"?Na(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Na(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Ww=gt(zw),Bw=Se({},Qo,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),zf=gt(Bw),Uw=Se({},Ri,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:vd}),Fw=gt(Uw),Gw=Se({},Ns,{propertyName:0,elapsedTime:0,pseudoElement:0}),$w=gt(Gw),Hw=Se({},Qo,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Yw=gt(Hw),Kw=[9,13,27,32],xd=on&&"CompositionEvent"in window,ei=null;on&&"documentMode"in document&&(ei=document.documentMode);var Vw=on&&"TextEvent"in window&&!ei,Zm=on&&(!xd||ei&&8<ei&&11>=ei),Wf=" ",Bf=!1;function Jm(e,t){switch(e){case"keyup":return Kw.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function qm(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var zr=!1;function Qw(e,t){switch(e){case"compositionend":return qm(t);case"keypress":return t.which!==32?null:(Bf=!0,Wf);case"textInput":return e=t.data,e===Wf&&Bf?null:e;default:return null}}function Zw(e,t){if(zr)return e==="compositionend"||!xd&&Jm(e,t)?(e=Qm(),Sa=yd=jn=null,zr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Zm&&t.locale!=="ko"?null:t.data;default:return null}}var Jw={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Uf(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Jw[e.type]:t==="textarea"}function Xm(e,t,n,r){Cm(r),t=go(t,"onChange"),0<t.length&&(n=new wd("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var ti=null,xi=null;function qw(e){up(e,0)}function Zo(e){var t=Ur(e);if(Em(t))return e}function Xw(e,t){if(e==="change")return t}var ep=!1;if(on){var Cl;if(on){var Il="oninput"in document;if(!Il){var Ff=document.createElement("div");Ff.setAttribute("oninput","return;"),Il=typeof Ff.oninput=="function"}Cl=Il}else Cl=!1;ep=Cl&&(!document.documentMode||9<document.documentMode)}function Gf(){ti&&(ti.detachEvent("onpropertychange",tp),xi=ti=null)}function tp(e){if(e.propertyName==="value"&&Zo(xi)){var t=[];Xm(t,xi,e,fd(e)),Om(qw,t)}}function e1(e,t,n){e==="focusin"?(Gf(),ti=t,xi=n,ti.attachEvent("onpropertychange",tp)):e==="focusout"&&Gf()}function t1(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Zo(xi)}function n1(e,t){if(e==="click")return Zo(t)}function r1(e,t){if(e==="input"||e==="change")return Zo(t)}function s1(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Rt=typeof Object.is=="function"?Object.is:s1;function Si(e,t){if(Rt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var s=n[r];if(!fc.call(t,s)||!Rt(e[s],t[s]))return!1}return!0}function $f(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Hf(e,t){var n=$f(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=$f(n)}}function np(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?np(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function rp(){for(var e=window,t=lo();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=lo(e.document)}return t}function Sd(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function i1(e){var t=rp(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&np(n.ownerDocument.documentElement,n)){if(r!==null&&Sd(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var s=n.textContent.length,i=Math.min(r.start,s);r=r.end===void 0?i:Math.min(r.end,s),!e.extend&&i>r&&(s=r,r=i,i=s),s=Hf(n,i);var o=Hf(n,r);s&&o&&(e.rangeCount!==1||e.anchorNode!==s.node||e.anchorOffset!==s.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&(t=t.createRange(),t.setStart(s.node,s.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var a1=on&&"documentMode"in document&&11>=document.documentMode,Wr=null,Ic=null,ni=null,kc=!1;function Yf(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;kc||Wr==null||Wr!==lo(r)||(r=Wr,"selectionStart"in r&&Sd(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),ni&&Si(ni,r)||(ni=r,r=go(Ic,"onSelect"),0<r.length&&(t=new wd("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Wr)))}function ra(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Br={animationend:ra("Animation","AnimationEnd"),animationiteration:ra("Animation","AnimationIteration"),animationstart:ra("Animation","AnimationStart"),transitionend:ra("Transition","TransitionEnd")},kl={},sp={};on&&(sp=document.createElement("div").style,"AnimationEvent"in window||(delete Br.animationend.animation,delete Br.animationiteration.animation,delete Br.animationstart.animation),"TransitionEvent"in window||delete Br.transitionend.transition);function Jo(e){if(kl[e])return kl[e];if(!Br[e])return e;var t=Br[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in sp)return kl[e]=t[n];return e}var ip=Jo("animationend"),ap=Jo("animationiteration"),op=Jo("animationstart"),lp=Jo("transitionend"),cp=new Map,Kf="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Un(e,t){cp.set(e,t),vr(t,[e])}for(var Ll=0;Ll<Kf.length;Ll++){var Ol=Kf[Ll],o1=Ol.toLowerCase(),l1=Ol[0].toUpperCase()+Ol.slice(1);Un(o1,"on"+l1)}Un(ip,"onAnimationEnd");Un(ap,"onAnimationIteration");Un(op,"onAnimationStart");Un("dblclick","onDoubleClick");Un("focusin","onFocus");Un("focusout","onBlur");Un(lp,"onTransitionEnd");os("onMouseEnter",["mouseout","mouseover"]);os("onMouseLeave",["mouseout","mouseover"]);os("onPointerEnter",["pointerout","pointerover"]);os("onPointerLeave",["pointerout","pointerover"]);vr("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));vr("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));vr("onBeforeInput",["compositionend","keypress","textInput","paste"]);vr("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));vr("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));vr("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ws="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),c1=new Set("cancel close invalid load scroll toggle".split(" ").concat(Ws));function Vf(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,ow(r,t,void 0,e),e.currentTarget=null}function up(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],s=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var o=r.length-1;0<=o;o--){var l=r[o],c=l.instance,u=l.currentTarget;if(l=l.listener,c!==i&&s.isPropagationStopped())break e;Vf(s,l,u),i=c}else for(o=0;o<r.length;o++){if(l=r[o],c=l.instance,u=l.currentTarget,l=l.listener,c!==i&&s.isPropagationStopped())break e;Vf(s,l,u),i=c}}}if(uo)throw e=_c,uo=!1,_c=null,e}function me(e,t){var n=t[Dc];n===void 0&&(n=t[Dc]=new Set);var r=e+"__bubble";n.has(r)||(dp(t,e,2,!1),n.add(r))}function Pl(e,t,n){var r=0;t&&(r|=4),dp(n,e,r,t)}var sa="_reactListening"+Math.random().toString(36).slice(2);function Ni(e){if(!e[sa]){e[sa]=!0,wm.forEach(function(n){n!=="selectionchange"&&(c1.has(n)||Pl(n,!1,e),Pl(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[sa]||(t[sa]=!0,Pl("selectionchange",!1,t))}}function dp(e,t,n,r){switch(Vm(t)){case 1:var s=Ew;break;case 4:s=Mw;break;default:s=gd}n=s.bind(null,t,n,e),s=void 0,!jc||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(s=!0),r?s!==void 0?e.addEventListener(t,n,{capture:!0,passive:s}):e.addEventListener(t,n,!0):s!==void 0?e.addEventListener(t,n,{passive:s}):e.addEventListener(t,n,!1)}function Rl(e,t,n,r,s){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var o=r.tag;if(o===3||o===4){var l=r.stateNode.containerInfo;if(l===s||l.nodeType===8&&l.parentNode===s)break;if(o===4)for(o=r.return;o!==null;){var c=o.tag;if((c===3||c===4)&&(c=o.stateNode.containerInfo,c===s||c.nodeType===8&&c.parentNode===s))return;o=o.return}for(;l!==null;){if(o=tr(l),o===null)return;if(c=o.tag,c===5||c===6){r=i=o;continue e}l=l.parentNode}}r=r.return}Om(function(){var u=i,d=fd(n),h=[];e:{var y=cp.get(e);if(y!==void 0){var g=wd,S=e;switch(e){case"keypress":if(Na(n)===0)break e;case"keydown":case"keyup":g=Ww;break;case"focusin":S="focus",g=Tl;break;case"focusout":S="blur",g=Tl;break;case"beforeblur":case"afterblur":g=Tl;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":g=Rf;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":g=_w;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":g=Fw;break;case ip:case ap:case op:g=Cw;break;case lp:g=$w;break;case"scroll":g=bw;break;case"wheel":g=Yw;break;case"copy":case"cut":case"paste":g=kw;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":g=zf}var N=(t&4)!==0,A=!N&&e==="scroll",p=N?y!==null?y+"Capture":null:y;N=[];for(var m=u,f;m!==null;){f=m;var x=f.stateNode;if(f.tag===5&&x!==null&&(f=x,p!==null&&(x=gi(m,p),x!=null&&N.push(Ei(m,x,f)))),A)break;m=m.return}0<N.length&&(y=new g(y,S,null,n,d),h.push({event:y,listeners:N}))}}if(!(t&7)){e:{if(y=e==="mouseover"||e==="pointerover",g=e==="mouseout"||e==="pointerout",y&&n!==Mc&&(S=n.relatedTarget||n.fromElement)&&(tr(S)||S[ln]))break e;if((g||y)&&(y=d.window===d?d:(y=d.ownerDocument)?y.defaultView||y.parentWindow:window,g?(S=n.relatedTarget||n.toElement,g=u,S=S?tr(S):null,S!==null&&(A=xr(S),S!==A||S.tag!==5&&S.tag!==6)&&(S=null)):(g=null,S=u),g!==S)){if(N=Rf,x="onMouseLeave",p="onMouseEnter",m="mouse",(e==="pointerout"||e==="pointerover")&&(N=zf,x="onPointerLeave",p="onPointerEnter",m="pointer"),A=g==null?y:Ur(g),f=S==null?y:Ur(S),y=new N(x,m+"leave",g,n,d),y.target=A,y.relatedTarget=f,x=null,tr(d)===u&&(N=new N(p,m+"enter",S,n,d),N.target=f,N.relatedTarget=A,x=N),A=x,g&&S)t:{for(N=g,p=S,m=0,f=N;f;f=br(f))m++;for(f=0,x=p;x;x=br(x))f++;for(;0<m-f;)N=br(N),m--;for(;0<f-m;)p=br(p),f--;for(;m--;){if(N===p||p!==null&&N===p.alternate)break t;N=br(N),p=br(p)}N=null}else N=null;g!==null&&Qf(h,y,g,N,!1),S!==null&&A!==null&&Qf(h,A,S,N,!0)}}e:{if(y=u?Ur(u):window,g=y.nodeName&&y.nodeName.toLowerCase(),g==="select"||g==="input"&&y.type==="file")var M=Xw;else if(Uf(y))if(ep)M=r1;else{M=t1;var w=e1}else(g=y.nodeName)&&g.toLowerCase()==="input"&&(y.type==="checkbox"||y.type==="radio")&&(M=n1);if(M&&(M=M(e,u))){Xm(h,M,n,d);break e}w&&w(e,y,u),e==="focusout"&&(w=y._wrapperState)&&w.controlled&&y.type==="number"&&vc(y,"number",y.value)}switch(w=u?Ur(u):window,e){case"focusin":(Uf(w)||w.contentEditable==="true")&&(Wr=w,Ic=u,ni=null);break;case"focusout":ni=Ic=Wr=null;break;case"mousedown":kc=!0;break;case"contextmenu":case"mouseup":case"dragend":kc=!1,Yf(h,n,d);break;case"selectionchange":if(a1)break;case"keydown":case"keyup":Yf(h,n,d)}var v;if(xd)e:{switch(e){case"compositionstart":var b="onCompositionStart";break e;case"compositionend":b="onCompositionEnd";break e;case"compositionupdate":b="onCompositionUpdate";break e}b=void 0}else zr?Jm(e,n)&&(b="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(b="onCompositionStart");b&&(Zm&&n.locale!=="ko"&&(zr||b!=="onCompositionStart"?b==="onCompositionEnd"&&zr&&(v=Qm()):(jn=d,yd="value"in jn?jn.value:jn.textContent,zr=!0)),w=go(u,b),0<w.length&&(b=new Df(b,e,null,n,d),h.push({event:b,listeners:w}),v?b.data=v:(v=qm(n),v!==null&&(b.data=v)))),(v=Vw?Qw(e,n):Zw(e,n))&&(u=go(u,"onBeforeInput"),0<u.length&&(d=new Df("onBeforeInput","beforeinput",null,n,d),h.push({event:d,listeners:u}),d.data=v))}up(h,t)})}function Ei(e,t,n){return{instance:e,listener:t,currentTarget:n}}function go(e,t){for(var n=t+"Capture",r=[];e!==null;){var s=e,i=s.stateNode;s.tag===5&&i!==null&&(s=i,i=gi(e,n),i!=null&&r.unshift(Ei(e,i,s)),i=gi(e,t),i!=null&&r.push(Ei(e,i,s))),e=e.return}return r}function br(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Qf(e,t,n,r,s){for(var i=t._reactName,o=[];n!==null&&n!==r;){var l=n,c=l.alternate,u=l.stateNode;if(c!==null&&c===r)break;l.tag===5&&u!==null&&(l=u,s?(c=gi(n,i),c!=null&&o.unshift(Ei(n,c,l))):s||(c=gi(n,i),c!=null&&o.push(Ei(n,c,l)))),n=n.return}o.length!==0&&e.push({event:t,listeners:o})}var u1=/\r\n?/g,d1=/\u0000|\uFFFD/g;function Zf(e){return(typeof e=="string"?e:""+e).replace(u1,`
`).replace(d1,"")}function ia(e,t,n){if(t=Zf(t),Zf(e)!==t&&n)throw Error(B(425))}function yo(){}var Lc=null,Oc=null;function Pc(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Rc=typeof setTimeout=="function"?setTimeout:void 0,f1=typeof clearTimeout=="function"?clearTimeout:void 0,Jf=typeof Promise=="function"?Promise:void 0,h1=typeof queueMicrotask=="function"?queueMicrotask:typeof Jf<"u"?function(e){return Jf.resolve(null).then(e).catch(m1)}:Rc;function m1(e){setTimeout(function(){throw e})}function Dl(e,t){var n=t,r=0;do{var s=n.nextSibling;if(e.removeChild(n),s&&s.nodeType===8)if(n=s.data,n==="/$"){if(r===0){e.removeChild(s),vi(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=s}while(n);vi(t)}function Ln(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function qf(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Es=Math.random().toString(36).slice(2),Ut="__reactFiber$"+Es,Mi="__reactProps$"+Es,ln="__reactContainer$"+Es,Dc="__reactEvents$"+Es,p1="__reactListeners$"+Es,g1="__reactHandles$"+Es;function tr(e){var t=e[Ut];if(t)return t;for(var n=e.parentNode;n;){if(t=n[ln]||n[Ut]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=qf(e);e!==null;){if(n=e[Ut])return n;e=qf(e)}return t}e=n,n=e.parentNode}return null}function Di(e){return e=e[Ut]||e[ln],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Ur(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(B(33))}function qo(e){return e[Mi]||null}var zc=[],Fr=-1;function Fn(e){return{current:e}}function pe(e){0>Fr||(e.current=zc[Fr],zc[Fr]=null,Fr--)}function he(e,t){Fr++,zc[Fr]=e.current,e.current=t}var Wn={},Ke=Fn(Wn),rt=Fn(!1),hr=Wn;function ls(e,t){var n=e.type.contextTypes;if(!n)return Wn;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var s={},i;for(i in n)s[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=s),s}function st(e){return e=e.childContextTypes,e!=null}function wo(){pe(rt),pe(Ke)}function Xf(e,t,n){if(Ke.current!==Wn)throw Error(B(168));he(Ke,t),he(rt,n)}function fp(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var s in r)if(!(s in t))throw Error(B(108,ew(e)||"Unknown",s));return Se({},n,r)}function vo(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Wn,hr=Ke.current,he(Ke,e),he(rt,rt.current),!0}function eh(e,t,n){var r=e.stateNode;if(!r)throw Error(B(169));n?(e=fp(e,t,hr),r.__reactInternalMemoizedMergedChildContext=e,pe(rt),pe(Ke),he(Ke,e)):pe(rt),he(rt,n)}var Xt=null,Xo=!1,zl=!1;function hp(e){Xt===null?Xt=[e]:Xt.push(e)}function y1(e){Xo=!0,hp(e)}function Gn(){if(!zl&&Xt!==null){zl=!0;var e=0,t=ce;try{var n=Xt;for(ce=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Xt=null,Xo=!1}catch(s){throw Xt!==null&&(Xt=Xt.slice(e+1)),zm(hd,Gn),s}finally{ce=t,zl=!1}}return null}var Gr=[],$r=0,xo=null,So=0,vt=[],xt=0,mr=null,nn=1,rn="";function Kn(e,t){Gr[$r++]=So,Gr[$r++]=xo,xo=e,So=t}function mp(e,t,n){vt[xt++]=nn,vt[xt++]=rn,vt[xt++]=mr,mr=e;var r=nn;e=rn;var s=32-Ot(r)-1;r&=~(1<<s),n+=1;var i=32-Ot(t)+s;if(30<i){var o=s-s%5;i=(r&(1<<o)-1).toString(32),r>>=o,s-=o,nn=1<<32-Ot(t)+s|n<<s|r,rn=i+e}else nn=1<<i|n<<s|r,rn=e}function Nd(e){e.return!==null&&(Kn(e,1),mp(e,1,0))}function Ed(e){for(;e===xo;)xo=Gr[--$r],Gr[$r]=null,So=Gr[--$r],Gr[$r]=null;for(;e===mr;)mr=vt[--xt],vt[xt]=null,rn=vt[--xt],vt[xt]=null,nn=vt[--xt],vt[xt]=null}var dt=null,ut=null,ge=!1,It=null;function pp(e,t){var n=St(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function th(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,dt=e,ut=Ln(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,dt=e,ut=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=mr!==null?{id:nn,overflow:rn}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=St(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,dt=e,ut=null,!0):!1;default:return!1}}function Wc(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Bc(e){if(ge){var t=ut;if(t){var n=t;if(!th(e,t)){if(Wc(e))throw Error(B(418));t=Ln(n.nextSibling);var r=dt;t&&th(e,t)?pp(r,n):(e.flags=e.flags&-4097|2,ge=!1,dt=e)}}else{if(Wc(e))throw Error(B(418));e.flags=e.flags&-4097|2,ge=!1,dt=e}}}function nh(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;dt=e}function aa(e){if(e!==dt)return!1;if(!ge)return nh(e),ge=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Pc(e.type,e.memoizedProps)),t&&(t=ut)){if(Wc(e))throw gp(),Error(B(418));for(;t;)pp(e,t),t=Ln(t.nextSibling)}if(nh(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(B(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){ut=Ln(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}ut=null}}else ut=dt?Ln(e.stateNode.nextSibling):null;return!0}function gp(){for(var e=ut;e;)e=Ln(e.nextSibling)}function cs(){ut=dt=null,ge=!1}function Md(e){It===null?It=[e]:It.push(e)}var w1=hn.ReactCurrentBatchConfig;function ks(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(B(309));var r=n.stateNode}if(!r)throw Error(B(147,e));var s=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(o){var l=s.refs;o===null?delete l[i]:l[i]=o},t._stringRef=i,t)}if(typeof e!="string")throw Error(B(284));if(!n._owner)throw Error(B(290,e))}return e}function oa(e,t){throw e=Object.prototype.toString.call(t),Error(B(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function rh(e){var t=e._init;return t(e._payload)}function yp(e){function t(p,m){if(e){var f=p.deletions;f===null?(p.deletions=[m],p.flags|=16):f.push(m)}}function n(p,m){if(!e)return null;for(;m!==null;)t(p,m),m=m.sibling;return null}function r(p,m){for(p=new Map;m!==null;)m.key!==null?p.set(m.key,m):p.set(m.index,m),m=m.sibling;return p}function s(p,m){return p=Dn(p,m),p.index=0,p.sibling=null,p}function i(p,m,f){return p.index=f,e?(f=p.alternate,f!==null?(f=f.index,f<m?(p.flags|=2,m):f):(p.flags|=2,m)):(p.flags|=1048576,m)}function o(p){return e&&p.alternate===null&&(p.flags|=2),p}function l(p,m,f,x){return m===null||m.tag!==6?(m=Hl(f,p.mode,x),m.return=p,m):(m=s(m,f),m.return=p,m)}function c(p,m,f,x){var M=f.type;return M===Dr?d(p,m,f.props.children,x,f.key):m!==null&&(m.elementType===M||typeof M=="object"&&M!==null&&M.$$typeof===vn&&rh(M)===m.type)?(x=s(m,f.props),x.ref=ks(p,m,f),x.return=p,x):(x=Ta(f.type,f.key,f.props,null,p.mode,x),x.ref=ks(p,m,f),x.return=p,x)}function u(p,m,f,x){return m===null||m.tag!==4||m.stateNode.containerInfo!==f.containerInfo||m.stateNode.implementation!==f.implementation?(m=Yl(f,p.mode,x),m.return=p,m):(m=s(m,f.children||[]),m.return=p,m)}function d(p,m,f,x,M){return m===null||m.tag!==7?(m=dr(f,p.mode,x,M),m.return=p,m):(m=s(m,f),m.return=p,m)}function h(p,m,f){if(typeof m=="string"&&m!==""||typeof m=="number")return m=Hl(""+m,p.mode,f),m.return=p,m;if(typeof m=="object"&&m!==null){switch(m.$$typeof){case Zi:return f=Ta(m.type,m.key,m.props,null,p.mode,f),f.ref=ks(p,null,m),f.return=p,f;case Rr:return m=Yl(m,p.mode,f),m.return=p,m;case vn:var x=m._init;return h(p,x(m._payload),f)}if(Ds(m)||_s(m))return m=dr(m,p.mode,f,null),m.return=p,m;oa(p,m)}return null}function y(p,m,f,x){var M=m!==null?m.key:null;if(typeof f=="string"&&f!==""||typeof f=="number")return M!==null?null:l(p,m,""+f,x);if(typeof f=="object"&&f!==null){switch(f.$$typeof){case Zi:return f.key===M?c(p,m,f,x):null;case Rr:return f.key===M?u(p,m,f,x):null;case vn:return M=f._init,y(p,m,M(f._payload),x)}if(Ds(f)||_s(f))return M!==null?null:d(p,m,f,x,null);oa(p,f)}return null}function g(p,m,f,x,M){if(typeof x=="string"&&x!==""||typeof x=="number")return p=p.get(f)||null,l(m,p,""+x,M);if(typeof x=="object"&&x!==null){switch(x.$$typeof){case Zi:return p=p.get(x.key===null?f:x.key)||null,c(m,p,x,M);case Rr:return p=p.get(x.key===null?f:x.key)||null,u(m,p,x,M);case vn:var w=x._init;return g(p,m,f,w(x._payload),M)}if(Ds(x)||_s(x))return p=p.get(f)||null,d(m,p,x,M,null);oa(m,x)}return null}function S(p,m,f,x){for(var M=null,w=null,v=m,b=m=0,T=null;v!==null&&b<f.length;b++){v.index>b?(T=v,v=null):T=v.sibling;var C=y(p,v,f[b],x);if(C===null){v===null&&(v=T);break}e&&v&&C.alternate===null&&t(p,v),m=i(C,m,b),w===null?M=C:w.sibling=C,w=C,v=T}if(b===f.length)return n(p,v),ge&&Kn(p,b),M;if(v===null){for(;b<f.length;b++)v=h(p,f[b],x),v!==null&&(m=i(v,m,b),w===null?M=v:w.sibling=v,w=v);return ge&&Kn(p,b),M}for(v=r(p,v);b<f.length;b++)T=g(v,p,b,f[b],x),T!==null&&(e&&T.alternate!==null&&v.delete(T.key===null?b:T.key),m=i(T,m,b),w===null?M=T:w.sibling=T,w=T);return e&&v.forEach(function(O){return t(p,O)}),ge&&Kn(p,b),M}function N(p,m,f,x){var M=_s(f);if(typeof M!="function")throw Error(B(150));if(f=M.call(f),f==null)throw Error(B(151));for(var w=M=null,v=m,b=m=0,T=null,C=f.next();v!==null&&!C.done;b++,C=f.next()){v.index>b?(T=v,v=null):T=v.sibling;var O=y(p,v,C.value,x);if(O===null){v===null&&(v=T);break}e&&v&&O.alternate===null&&t(p,v),m=i(O,m,b),w===null?M=O:w.sibling=O,w=O,v=T}if(C.done)return n(p,v),ge&&Kn(p,b),M;if(v===null){for(;!C.done;b++,C=f.next())C=h(p,C.value,x),C!==null&&(m=i(C,m,b),w===null?M=C:w.sibling=C,w=C);return ge&&Kn(p,b),M}for(v=r(p,v);!C.done;b++,C=f.next())C=g(v,p,b,C.value,x),C!==null&&(e&&C.alternate!==null&&v.delete(C.key===null?b:C.key),m=i(C,m,b),w===null?M=C:w.sibling=C,w=C);return e&&v.forEach(function(D){return t(p,D)}),ge&&Kn(p,b),M}function A(p,m,f,x){if(typeof f=="object"&&f!==null&&f.type===Dr&&f.key===null&&(f=f.props.children),typeof f=="object"&&f!==null){switch(f.$$typeof){case Zi:e:{for(var M=f.key,w=m;w!==null;){if(w.key===M){if(M=f.type,M===Dr){if(w.tag===7){n(p,w.sibling),m=s(w,f.props.children),m.return=p,p=m;break e}}else if(w.elementType===M||typeof M=="object"&&M!==null&&M.$$typeof===vn&&rh(M)===w.type){n(p,w.sibling),m=s(w,f.props),m.ref=ks(p,w,f),m.return=p,p=m;break e}n(p,w);break}else t(p,w);w=w.sibling}f.type===Dr?(m=dr(f.props.children,p.mode,x,f.key),m.return=p,p=m):(x=Ta(f.type,f.key,f.props,null,p.mode,x),x.ref=ks(p,m,f),x.return=p,p=x)}return o(p);case Rr:e:{for(w=f.key;m!==null;){if(m.key===w)if(m.tag===4&&m.stateNode.containerInfo===f.containerInfo&&m.stateNode.implementation===f.implementation){n(p,m.sibling),m=s(m,f.children||[]),m.return=p,p=m;break e}else{n(p,m);break}else t(p,m);m=m.sibling}m=Yl(f,p.mode,x),m.return=p,p=m}return o(p);case vn:return w=f._init,A(p,m,w(f._payload),x)}if(Ds(f))return S(p,m,f,x);if(_s(f))return N(p,m,f,x);oa(p,f)}return typeof f=="string"&&f!==""||typeof f=="number"?(f=""+f,m!==null&&m.tag===6?(n(p,m.sibling),m=s(m,f),m.return=p,p=m):(n(p,m),m=Hl(f,p.mode,x),m.return=p,p=m),o(p)):n(p,m)}return A}var us=yp(!0),wp=yp(!1),No=Fn(null),Eo=null,Hr=null,bd=null;function jd(){bd=Hr=Eo=null}function _d(e){var t=No.current;pe(No),e._currentValue=t}function Uc(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function ts(e,t){Eo=e,bd=Hr=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(nt=!0),e.firstContext=null)}function Et(e){var t=e._currentValue;if(bd!==e)if(e={context:e,memoizedValue:t,next:null},Hr===null){if(Eo===null)throw Error(B(308));Hr=e,Eo.dependencies={lanes:0,firstContext:e}}else Hr=Hr.next=e;return t}var nr=null;function Ad(e){nr===null?nr=[e]:nr.push(e)}function vp(e,t,n,r){var s=t.interleaved;return s===null?(n.next=n,Ad(t)):(n.next=s.next,s.next=n),t.interleaved=n,cn(e,r)}function cn(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var xn=!1;function Td(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function xp(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function an(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function On(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,ne&2){var s=r.pending;return s===null?t.next=t:(t.next=s.next,s.next=t),r.pending=t,cn(e,n)}return s=r.interleaved,s===null?(t.next=t,Ad(r)):(t.next=s.next,s.next=t),r.interleaved=t,cn(e,n)}function Ea(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,md(e,n)}}function sh(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var s=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?s=i=o:i=i.next=o,n=n.next}while(n!==null);i===null?s=i=t:i=i.next=t}else s=i=t;n={baseState:r.baseState,firstBaseUpdate:s,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Mo(e,t,n,r){var s=e.updateQueue;xn=!1;var i=s.firstBaseUpdate,o=s.lastBaseUpdate,l=s.shared.pending;if(l!==null){s.shared.pending=null;var c=l,u=c.next;c.next=null,o===null?i=u:o.next=u,o=c;var d=e.alternate;d!==null&&(d=d.updateQueue,l=d.lastBaseUpdate,l!==o&&(l===null?d.firstBaseUpdate=u:l.next=u,d.lastBaseUpdate=c))}if(i!==null){var h=s.baseState;o=0,d=u=c=null,l=i;do{var y=l.lane,g=l.eventTime;if((r&y)===y){d!==null&&(d=d.next={eventTime:g,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var S=e,N=l;switch(y=t,g=n,N.tag){case 1:if(S=N.payload,typeof S=="function"){h=S.call(g,h,y);break e}h=S;break e;case 3:S.flags=S.flags&-65537|128;case 0:if(S=N.payload,y=typeof S=="function"?S.call(g,h,y):S,y==null)break e;h=Se({},h,y);break e;case 2:xn=!0}}l.callback!==null&&l.lane!==0&&(e.flags|=64,y=s.effects,y===null?s.effects=[l]:y.push(l))}else g={eventTime:g,lane:y,tag:l.tag,payload:l.payload,callback:l.callback,next:null},d===null?(u=d=g,c=h):d=d.next=g,o|=y;if(l=l.next,l===null){if(l=s.shared.pending,l===null)break;y=l,l=y.next,y.next=null,s.lastBaseUpdate=y,s.shared.pending=null}}while(!0);if(d===null&&(c=h),s.baseState=c,s.firstBaseUpdate=u,s.lastBaseUpdate=d,t=s.shared.interleaved,t!==null){s=t;do o|=s.lane,s=s.next;while(s!==t)}else i===null&&(s.shared.lanes=0);gr|=o,e.lanes=o,e.memoizedState=h}}function ih(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],s=r.callback;if(s!==null){if(r.callback=null,r=n,typeof s!="function")throw Error(B(191,s));s.call(r)}}}var zi={},Yt=Fn(zi),bi=Fn(zi),ji=Fn(zi);function rr(e){if(e===zi)throw Error(B(174));return e}function Cd(e,t){switch(he(ji,t),he(bi,e),he(Yt,zi),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Sc(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Sc(t,e)}pe(Yt),he(Yt,t)}function ds(){pe(Yt),pe(bi),pe(ji)}function Sp(e){rr(ji.current);var t=rr(Yt.current),n=Sc(t,e.type);t!==n&&(he(bi,e),he(Yt,n))}function Id(e){bi.current===e&&(pe(Yt),pe(bi))}var ve=Fn(0);function bo(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Wl=[];function kd(){for(var e=0;e<Wl.length;e++)Wl[e]._workInProgressVersionPrimary=null;Wl.length=0}var Ma=hn.ReactCurrentDispatcher,Bl=hn.ReactCurrentBatchConfig,pr=0,xe=null,Te=null,Le=null,jo=!1,ri=!1,_i=0,v1=0;function Ge(){throw Error(B(321))}function Ld(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Rt(e[n],t[n]))return!1;return!0}function Od(e,t,n,r,s,i){if(pr=i,xe=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Ma.current=e===null||e.memoizedState===null?E1:M1,e=n(r,s),ri){i=0;do{if(ri=!1,_i=0,25<=i)throw Error(B(301));i+=1,Le=Te=null,t.updateQueue=null,Ma.current=b1,e=n(r,s)}while(ri)}if(Ma.current=_o,t=Te!==null&&Te.next!==null,pr=0,Le=Te=xe=null,jo=!1,t)throw Error(B(300));return e}function Pd(){var e=_i!==0;return _i=0,e}function Wt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Le===null?xe.memoizedState=Le=e:Le=Le.next=e,Le}function Mt(){if(Te===null){var e=xe.alternate;e=e!==null?e.memoizedState:null}else e=Te.next;var t=Le===null?xe.memoizedState:Le.next;if(t!==null)Le=t,Te=e;else{if(e===null)throw Error(B(310));Te=e,e={memoizedState:Te.memoizedState,baseState:Te.baseState,baseQueue:Te.baseQueue,queue:Te.queue,next:null},Le===null?xe.memoizedState=Le=e:Le=Le.next=e}return Le}function Ai(e,t){return typeof t=="function"?t(e):t}function Ul(e){var t=Mt(),n=t.queue;if(n===null)throw Error(B(311));n.lastRenderedReducer=e;var r=Te,s=r.baseQueue,i=n.pending;if(i!==null){if(s!==null){var o=s.next;s.next=i.next,i.next=o}r.baseQueue=s=i,n.pending=null}if(s!==null){i=s.next,r=r.baseState;var l=o=null,c=null,u=i;do{var d=u.lane;if((pr&d)===d)c!==null&&(c=c.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var h={lane:d,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};c===null?(l=c=h,o=r):c=c.next=h,xe.lanes|=d,gr|=d}u=u.next}while(u!==null&&u!==i);c===null?o=r:c.next=l,Rt(r,t.memoizedState)||(nt=!0),t.memoizedState=r,t.baseState=o,t.baseQueue=c,n.lastRenderedState=r}if(e=n.interleaved,e!==null){s=e;do i=s.lane,xe.lanes|=i,gr|=i,s=s.next;while(s!==e)}else s===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Fl(e){var t=Mt(),n=t.queue;if(n===null)throw Error(B(311));n.lastRenderedReducer=e;var r=n.dispatch,s=n.pending,i=t.memoizedState;if(s!==null){n.pending=null;var o=s=s.next;do i=e(i,o.action),o=o.next;while(o!==s);Rt(i,t.memoizedState)||(nt=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function Np(){}function Ep(e,t){var n=xe,r=Mt(),s=t(),i=!Rt(r.memoizedState,s);if(i&&(r.memoizedState=s,nt=!0),r=r.queue,Rd(jp.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||Le!==null&&Le.memoizedState.tag&1){if(n.flags|=2048,Ti(9,bp.bind(null,n,r,s,t),void 0,null),Pe===null)throw Error(B(349));pr&30||Mp(n,t,s)}return s}function Mp(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=xe.updateQueue,t===null?(t={lastEffect:null,stores:null},xe.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function bp(e,t,n,r){t.value=n,t.getSnapshot=r,_p(t)&&Ap(e)}function jp(e,t,n){return n(function(){_p(t)&&Ap(e)})}function _p(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Rt(e,n)}catch{return!0}}function Ap(e){var t=cn(e,1);t!==null&&Pt(t,e,1,-1)}function ah(e){var t=Wt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Ai,lastRenderedState:e},t.queue=e,e=e.dispatch=N1.bind(null,xe,e),[t.memoizedState,e]}function Ti(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=xe.updateQueue,t===null?(t={lastEffect:null,stores:null},xe.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Tp(){return Mt().memoizedState}function ba(e,t,n,r){var s=Wt();xe.flags|=e,s.memoizedState=Ti(1|t,n,void 0,r===void 0?null:r)}function el(e,t,n,r){var s=Mt();r=r===void 0?null:r;var i=void 0;if(Te!==null){var o=Te.memoizedState;if(i=o.destroy,r!==null&&Ld(r,o.deps)){s.memoizedState=Ti(t,n,i,r);return}}xe.flags|=e,s.memoizedState=Ti(1|t,n,i,r)}function oh(e,t){return ba(8390656,8,e,t)}function Rd(e,t){return el(2048,8,e,t)}function Cp(e,t){return el(4,2,e,t)}function Ip(e,t){return el(4,4,e,t)}function kp(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Lp(e,t,n){return n=n!=null?n.concat([e]):null,el(4,4,kp.bind(null,t,e),n)}function Dd(){}function Op(e,t){var n=Mt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Ld(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Pp(e,t){var n=Mt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Ld(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Rp(e,t,n){return pr&21?(Rt(n,t)||(n=Um(),xe.lanes|=n,gr|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,nt=!0),e.memoizedState=n)}function x1(e,t){var n=ce;ce=n!==0&&4>n?n:4,e(!0);var r=Bl.transition;Bl.transition={};try{e(!1),t()}finally{ce=n,Bl.transition=r}}function Dp(){return Mt().memoizedState}function S1(e,t,n){var r=Rn(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},zp(e))Wp(t,n);else if(n=vp(e,t,n,r),n!==null){var s=Je();Pt(n,e,r,s),Bp(n,t,r)}}function N1(e,t,n){var r=Rn(e),s={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(zp(e))Wp(t,s);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var o=t.lastRenderedState,l=i(o,n);if(s.hasEagerState=!0,s.eagerState=l,Rt(l,o)){var c=t.interleaved;c===null?(s.next=s,Ad(t)):(s.next=c.next,c.next=s),t.interleaved=s;return}}catch{}finally{}n=vp(e,t,s,r),n!==null&&(s=Je(),Pt(n,e,r,s),Bp(n,t,r))}}function zp(e){var t=e.alternate;return e===xe||t!==null&&t===xe}function Wp(e,t){ri=jo=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Bp(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,md(e,n)}}var _o={readContext:Et,useCallback:Ge,useContext:Ge,useEffect:Ge,useImperativeHandle:Ge,useInsertionEffect:Ge,useLayoutEffect:Ge,useMemo:Ge,useReducer:Ge,useRef:Ge,useState:Ge,useDebugValue:Ge,useDeferredValue:Ge,useTransition:Ge,useMutableSource:Ge,useSyncExternalStore:Ge,useId:Ge,unstable_isNewReconciler:!1},E1={readContext:Et,useCallback:function(e,t){return Wt().memoizedState=[e,t===void 0?null:t],e},useContext:Et,useEffect:oh,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,ba(4194308,4,kp.bind(null,t,e),n)},useLayoutEffect:function(e,t){return ba(4194308,4,e,t)},useInsertionEffect:function(e,t){return ba(4,2,e,t)},useMemo:function(e,t){var n=Wt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Wt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=S1.bind(null,xe,e),[r.memoizedState,e]},useRef:function(e){var t=Wt();return e={current:e},t.memoizedState=e},useState:ah,useDebugValue:Dd,useDeferredValue:function(e){return Wt().memoizedState=e},useTransition:function(){var e=ah(!1),t=e[0];return e=x1.bind(null,e[1]),Wt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=xe,s=Wt();if(ge){if(n===void 0)throw Error(B(407));n=n()}else{if(n=t(),Pe===null)throw Error(B(349));pr&30||Mp(r,t,n)}s.memoizedState=n;var i={value:n,getSnapshot:t};return s.queue=i,oh(jp.bind(null,r,i,e),[e]),r.flags|=2048,Ti(9,bp.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=Wt(),t=Pe.identifierPrefix;if(ge){var n=rn,r=nn;n=(r&~(1<<32-Ot(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=_i++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=v1++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},M1={readContext:Et,useCallback:Op,useContext:Et,useEffect:Rd,useImperativeHandle:Lp,useInsertionEffect:Cp,useLayoutEffect:Ip,useMemo:Pp,useReducer:Ul,useRef:Tp,useState:function(){return Ul(Ai)},useDebugValue:Dd,useDeferredValue:function(e){var t=Mt();return Rp(t,Te.memoizedState,e)},useTransition:function(){var e=Ul(Ai)[0],t=Mt().memoizedState;return[e,t]},useMutableSource:Np,useSyncExternalStore:Ep,useId:Dp,unstable_isNewReconciler:!1},b1={readContext:Et,useCallback:Op,useContext:Et,useEffect:Rd,useImperativeHandle:Lp,useInsertionEffect:Cp,useLayoutEffect:Ip,useMemo:Pp,useReducer:Fl,useRef:Tp,useState:function(){return Fl(Ai)},useDebugValue:Dd,useDeferredValue:function(e){var t=Mt();return Te===null?t.memoizedState=e:Rp(t,Te.memoizedState,e)},useTransition:function(){var e=Fl(Ai)[0],t=Mt().memoizedState;return[e,t]},useMutableSource:Np,useSyncExternalStore:Ep,useId:Dp,unstable_isNewReconciler:!1};function _t(e,t){if(e&&e.defaultProps){t=Se({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Fc(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:Se({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var tl={isMounted:function(e){return(e=e._reactInternals)?xr(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Je(),s=Rn(e),i=an(r,s);i.payload=t,n!=null&&(i.callback=n),t=On(e,i,s),t!==null&&(Pt(t,e,s,r),Ea(t,e,s))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Je(),s=Rn(e),i=an(r,s);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=On(e,i,s),t!==null&&(Pt(t,e,s,r),Ea(t,e,s))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Je(),r=Rn(e),s=an(n,r);s.tag=2,t!=null&&(s.callback=t),t=On(e,s,r),t!==null&&(Pt(t,e,r,n),Ea(t,e,r))}};function lh(e,t,n,r,s,i,o){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,o):t.prototype&&t.prototype.isPureReactComponent?!Si(n,r)||!Si(s,i):!0}function Up(e,t,n){var r=!1,s=Wn,i=t.contextType;return typeof i=="object"&&i!==null?i=Et(i):(s=st(t)?hr:Ke.current,r=t.contextTypes,i=(r=r!=null)?ls(e,s):Wn),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=tl,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=s,e.__reactInternalMemoizedMaskedChildContext=i),t}function ch(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&tl.enqueueReplaceState(t,t.state,null)}function Gc(e,t,n,r){var s=e.stateNode;s.props=n,s.state=e.memoizedState,s.refs={},Td(e);var i=t.contextType;typeof i=="object"&&i!==null?s.context=Et(i):(i=st(t)?hr:Ke.current,s.context=ls(e,i)),s.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(Fc(e,t,i,n),s.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof s.getSnapshotBeforeUpdate=="function"||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(t=s.state,typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount(),t!==s.state&&tl.enqueueReplaceState(s,s.state,null),Mo(e,n,s,r),s.state=e.memoizedState),typeof s.componentDidMount=="function"&&(e.flags|=4194308)}function fs(e,t){try{var n="",r=t;do n+=Xy(r),r=r.return;while(r);var s=n}catch(i){s=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:s,digest:null}}function Gl(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function $c(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var j1=typeof WeakMap=="function"?WeakMap:Map;function Fp(e,t,n){n=an(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){To||(To=!0,eu=r),$c(e,t)},n}function Gp(e,t,n){n=an(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var s=t.value;n.payload=function(){return r(s)},n.callback=function(){$c(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){$c(e,t),typeof r!="function"&&(Pn===null?Pn=new Set([this]):Pn.add(this));var o=t.stack;this.componentDidCatch(t.value,{componentStack:o!==null?o:""})}),n}function uh(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new j1;var s=new Set;r.set(t,s)}else s=r.get(t),s===void 0&&(s=new Set,r.set(t,s));s.has(n)||(s.add(n),e=B1.bind(null,e,t,n),t.then(e,e))}function dh(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function fh(e,t,n,r,s){return e.mode&1?(e.flags|=65536,e.lanes=s,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=an(-1,1),t.tag=2,On(n,t,1))),n.lanes|=1),e)}var _1=hn.ReactCurrentOwner,nt=!1;function Ze(e,t,n,r){t.child=e===null?wp(t,null,n,r):us(t,e.child,n,r)}function hh(e,t,n,r,s){n=n.render;var i=t.ref;return ts(t,s),r=Od(e,t,n,r,i,s),n=Pd(),e!==null&&!nt?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,un(e,t,s)):(ge&&n&&Nd(t),t.flags|=1,Ze(e,t,r,s),t.child)}function mh(e,t,n,r,s){if(e===null){var i=n.type;return typeof i=="function"&&!Hd(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,$p(e,t,i,r,s)):(e=Ta(n.type,null,r,t,t.mode,s),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&s)){var o=i.memoizedProps;if(n=n.compare,n=n!==null?n:Si,n(o,r)&&e.ref===t.ref)return un(e,t,s)}return t.flags|=1,e=Dn(i,r),e.ref=t.ref,e.return=t,t.child=e}function $p(e,t,n,r,s){if(e!==null){var i=e.memoizedProps;if(Si(i,r)&&e.ref===t.ref)if(nt=!1,t.pendingProps=r=i,(e.lanes&s)!==0)e.flags&131072&&(nt=!0);else return t.lanes=e.lanes,un(e,t,s)}return Hc(e,t,n,r,s)}function Hp(e,t,n){var r=t.pendingProps,s=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},he(Kr,lt),lt|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,he(Kr,lt),lt|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,he(Kr,lt),lt|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,he(Kr,lt),lt|=r;return Ze(e,t,s,n),t.child}function Yp(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Hc(e,t,n,r,s){var i=st(n)?hr:Ke.current;return i=ls(t,i),ts(t,s),n=Od(e,t,n,r,i,s),r=Pd(),e!==null&&!nt?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,un(e,t,s)):(ge&&r&&Nd(t),t.flags|=1,Ze(e,t,n,s),t.child)}function ph(e,t,n,r,s){if(st(n)){var i=!0;vo(t)}else i=!1;if(ts(t,s),t.stateNode===null)ja(e,t),Up(t,n,r),Gc(t,n,r,s),r=!0;else if(e===null){var o=t.stateNode,l=t.memoizedProps;o.props=l;var c=o.context,u=n.contextType;typeof u=="object"&&u!==null?u=Et(u):(u=st(n)?hr:Ke.current,u=ls(t,u));var d=n.getDerivedStateFromProps,h=typeof d=="function"||typeof o.getSnapshotBeforeUpdate=="function";h||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(l!==r||c!==u)&&ch(t,o,r,u),xn=!1;var y=t.memoizedState;o.state=y,Mo(t,r,o,s),c=t.memoizedState,l!==r||y!==c||rt.current||xn?(typeof d=="function"&&(Fc(t,n,d,r),c=t.memoizedState),(l=xn||lh(t,n,l,r,y,c,u))?(h||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=c),o.props=r,o.state=c,o.context=u,r=l):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,xp(e,t),l=t.memoizedProps,u=t.type===t.elementType?l:_t(t.type,l),o.props=u,h=t.pendingProps,y=o.context,c=n.contextType,typeof c=="object"&&c!==null?c=Et(c):(c=st(n)?hr:Ke.current,c=ls(t,c));var g=n.getDerivedStateFromProps;(d=typeof g=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(l!==h||y!==c)&&ch(t,o,r,c),xn=!1,y=t.memoizedState,o.state=y,Mo(t,r,o,s);var S=t.memoizedState;l!==h||y!==S||rt.current||xn?(typeof g=="function"&&(Fc(t,n,g,r),S=t.memoizedState),(u=xn||lh(t,n,u,r,y,S,c)||!1)?(d||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(r,S,c),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(r,S,c)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||l===e.memoizedProps&&y===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&y===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=S),o.props=r,o.state=S,o.context=c,r=u):(typeof o.componentDidUpdate!="function"||l===e.memoizedProps&&y===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&y===e.memoizedState||(t.flags|=1024),r=!1)}return Yc(e,t,n,r,i,s)}function Yc(e,t,n,r,s,i){Yp(e,t);var o=(t.flags&128)!==0;if(!r&&!o)return s&&eh(t,n,!1),un(e,t,i);r=t.stateNode,_1.current=t;var l=o&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&o?(t.child=us(t,e.child,null,i),t.child=us(t,null,l,i)):Ze(e,t,l,i),t.memoizedState=r.state,s&&eh(t,n,!0),t.child}function Kp(e){var t=e.stateNode;t.pendingContext?Xf(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Xf(e,t.context,!1),Cd(e,t.containerInfo)}function gh(e,t,n,r,s){return cs(),Md(s),t.flags|=256,Ze(e,t,n,r),t.child}var Kc={dehydrated:null,treeContext:null,retryLane:0};function Vc(e){return{baseLanes:e,cachePool:null,transitions:null}}function Vp(e,t,n){var r=t.pendingProps,s=ve.current,i=!1,o=(t.flags&128)!==0,l;if((l=o)||(l=e!==null&&e.memoizedState===null?!1:(s&2)!==0),l?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(s|=1),he(ve,s&1),e===null)return Bc(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(o=r.children,e=r.fallback,i?(r=t.mode,i=t.child,o={mode:"hidden",children:o},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=o):i=sl(o,r,0,null),e=dr(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=Vc(n),t.memoizedState=Kc,e):zd(t,o));if(s=e.memoizedState,s!==null&&(l=s.dehydrated,l!==null))return A1(e,t,o,r,l,s,n);if(i){i=r.fallback,o=t.mode,s=e.child,l=s.sibling;var c={mode:"hidden",children:r.children};return!(o&1)&&t.child!==s?(r=t.child,r.childLanes=0,r.pendingProps=c,t.deletions=null):(r=Dn(s,c),r.subtreeFlags=s.subtreeFlags&14680064),l!==null?i=Dn(l,i):(i=dr(i,o,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,o=e.child.memoizedState,o=o===null?Vc(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},i.memoizedState=o,i.childLanes=e.childLanes&~n,t.memoizedState=Kc,r}return i=e.child,e=i.sibling,r=Dn(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function zd(e,t){return t=sl({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function la(e,t,n,r){return r!==null&&Md(r),us(t,e.child,null,n),e=zd(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function A1(e,t,n,r,s,i,o){if(n)return t.flags&256?(t.flags&=-257,r=Gl(Error(B(422))),la(e,t,o,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,s=t.mode,r=sl({mode:"visible",children:r.children},s,0,null),i=dr(i,s,o,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&us(t,e.child,null,o),t.child.memoizedState=Vc(o),t.memoizedState=Kc,i);if(!(t.mode&1))return la(e,t,o,null);if(s.data==="$!"){if(r=s.nextSibling&&s.nextSibling.dataset,r)var l=r.dgst;return r=l,i=Error(B(419)),r=Gl(i,r,void 0),la(e,t,o,r)}if(l=(o&e.childLanes)!==0,nt||l){if(r=Pe,r!==null){switch(o&-o){case 4:s=2;break;case 16:s=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:s=32;break;case 536870912:s=268435456;break;default:s=0}s=s&(r.suspendedLanes|o)?0:s,s!==0&&s!==i.retryLane&&(i.retryLane=s,cn(e,s),Pt(r,e,s,-1))}return $d(),r=Gl(Error(B(421))),la(e,t,o,r)}return s.data==="$?"?(t.flags|=128,t.child=e.child,t=U1.bind(null,e),s._reactRetry=t,null):(e=i.treeContext,ut=Ln(s.nextSibling),dt=t,ge=!0,It=null,e!==null&&(vt[xt++]=nn,vt[xt++]=rn,vt[xt++]=mr,nn=e.id,rn=e.overflow,mr=t),t=zd(t,r.children),t.flags|=4096,t)}function yh(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Uc(e.return,t,n)}function $l(e,t,n,r,s){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:s}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=s)}function Qp(e,t,n){var r=t.pendingProps,s=r.revealOrder,i=r.tail;if(Ze(e,t,r.children,n),r=ve.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&yh(e,n,t);else if(e.tag===19)yh(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(he(ve,r),!(t.mode&1))t.memoizedState=null;else switch(s){case"forwards":for(n=t.child,s=null;n!==null;)e=n.alternate,e!==null&&bo(e)===null&&(s=n),n=n.sibling;n=s,n===null?(s=t.child,t.child=null):(s=n.sibling,n.sibling=null),$l(t,!1,s,n,i);break;case"backwards":for(n=null,s=t.child,t.child=null;s!==null;){if(e=s.alternate,e!==null&&bo(e)===null){t.child=s;break}e=s.sibling,s.sibling=n,n=s,s=e}$l(t,!0,n,null,i);break;case"together":$l(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function ja(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function un(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),gr|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(B(153));if(t.child!==null){for(e=t.child,n=Dn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Dn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function T1(e,t,n){switch(t.tag){case 3:Kp(t),cs();break;case 5:Sp(t);break;case 1:st(t.type)&&vo(t);break;case 4:Cd(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,s=t.memoizedProps.value;he(No,r._currentValue),r._currentValue=s;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(he(ve,ve.current&1),t.flags|=128,null):n&t.child.childLanes?Vp(e,t,n):(he(ve,ve.current&1),e=un(e,t,n),e!==null?e.sibling:null);he(ve,ve.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Qp(e,t,n);t.flags|=128}if(s=t.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),he(ve,ve.current),r)break;return null;case 22:case 23:return t.lanes=0,Hp(e,t,n)}return un(e,t,n)}var Zp,Qc,Jp,qp;Zp=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Qc=function(){};Jp=function(e,t,n,r){var s=e.memoizedProps;if(s!==r){e=t.stateNode,rr(Yt.current);var i=null;switch(n){case"input":s=yc(e,s),r=yc(e,r),i=[];break;case"select":s=Se({},s,{value:void 0}),r=Se({},r,{value:void 0}),i=[];break;case"textarea":s=xc(e,s),r=xc(e,r),i=[];break;default:typeof s.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=yo)}Nc(n,r);var o;n=null;for(u in s)if(!r.hasOwnProperty(u)&&s.hasOwnProperty(u)&&s[u]!=null)if(u==="style"){var l=s[u];for(o in l)l.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(mi.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var c=r[u];if(l=s!=null?s[u]:void 0,r.hasOwnProperty(u)&&c!==l&&(c!=null||l!=null))if(u==="style")if(l){for(o in l)!l.hasOwnProperty(o)||c&&c.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in c)c.hasOwnProperty(o)&&l[o]!==c[o]&&(n||(n={}),n[o]=c[o])}else n||(i||(i=[]),i.push(u,n)),n=c;else u==="dangerouslySetInnerHTML"?(c=c?c.__html:void 0,l=l?l.__html:void 0,c!=null&&l!==c&&(i=i||[]).push(u,c)):u==="children"?typeof c!="string"&&typeof c!="number"||(i=i||[]).push(u,""+c):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(mi.hasOwnProperty(u)?(c!=null&&u==="onScroll"&&me("scroll",e),i||l===c||(i=[])):(i=i||[]).push(u,c))}n&&(i=i||[]).push("style",n);var u=i;(t.updateQueue=u)&&(t.flags|=4)}};qp=function(e,t,n,r){n!==r&&(t.flags|=4)};function Ls(e,t){if(!ge)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function $e(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var s=e.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags&14680064,r|=s.flags&14680064,s.return=e,s=s.sibling;else for(s=e.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags,r|=s.flags,s.return=e,s=s.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function C1(e,t,n){var r=t.pendingProps;switch(Ed(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return $e(t),null;case 1:return st(t.type)&&wo(),$e(t),null;case 3:return r=t.stateNode,ds(),pe(rt),pe(Ke),kd(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(aa(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,It!==null&&(ru(It),It=null))),Qc(e,t),$e(t),null;case 5:Id(t);var s=rr(ji.current);if(n=t.type,e!==null&&t.stateNode!=null)Jp(e,t,n,r,s),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(B(166));return $e(t),null}if(e=rr(Yt.current),aa(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[Ut]=t,r[Mi]=i,e=(t.mode&1)!==0,n){case"dialog":me("cancel",r),me("close",r);break;case"iframe":case"object":case"embed":me("load",r);break;case"video":case"audio":for(s=0;s<Ws.length;s++)me(Ws[s],r);break;case"source":me("error",r);break;case"img":case"image":case"link":me("error",r),me("load",r);break;case"details":me("toggle",r);break;case"input":jf(r,i),me("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},me("invalid",r);break;case"textarea":Af(r,i),me("invalid",r)}Nc(n,i),s=null;for(var o in i)if(i.hasOwnProperty(o)){var l=i[o];o==="children"?typeof l=="string"?r.textContent!==l&&(i.suppressHydrationWarning!==!0&&ia(r.textContent,l,e),s=["children",l]):typeof l=="number"&&r.textContent!==""+l&&(i.suppressHydrationWarning!==!0&&ia(r.textContent,l,e),s=["children",""+l]):mi.hasOwnProperty(o)&&l!=null&&o==="onScroll"&&me("scroll",r)}switch(n){case"input":Ji(r),_f(r,i,!0);break;case"textarea":Ji(r),Tf(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=yo)}r=s,t.updateQueue=r,r!==null&&(t.flags|=4)}else{o=s.nodeType===9?s:s.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=jm(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=o.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=o.createElement(n,{is:r.is}):(e=o.createElement(n),n==="select"&&(o=e,r.multiple?o.multiple=!0:r.size&&(o.size=r.size))):e=o.createElementNS(e,n),e[Ut]=t,e[Mi]=r,Zp(e,t,!1,!1),t.stateNode=e;e:{switch(o=Ec(n,r),n){case"dialog":me("cancel",e),me("close",e),s=r;break;case"iframe":case"object":case"embed":me("load",e),s=r;break;case"video":case"audio":for(s=0;s<Ws.length;s++)me(Ws[s],e);s=r;break;case"source":me("error",e),s=r;break;case"img":case"image":case"link":me("error",e),me("load",e),s=r;break;case"details":me("toggle",e),s=r;break;case"input":jf(e,r),s=yc(e,r),me("invalid",e);break;case"option":s=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},s=Se({},r,{value:void 0}),me("invalid",e);break;case"textarea":Af(e,r),s=xc(e,r),me("invalid",e);break;default:s=r}Nc(n,s),l=s;for(i in l)if(l.hasOwnProperty(i)){var c=l[i];i==="style"?Tm(e,c):i==="dangerouslySetInnerHTML"?(c=c?c.__html:void 0,c!=null&&_m(e,c)):i==="children"?typeof c=="string"?(n!=="textarea"||c!=="")&&pi(e,c):typeof c=="number"&&pi(e,""+c):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(mi.hasOwnProperty(i)?c!=null&&i==="onScroll"&&me("scroll",e):c!=null&&ld(e,i,c,o))}switch(n){case"input":Ji(e),_f(e,r,!1);break;case"textarea":Ji(e),Tf(e);break;case"option":r.value!=null&&e.setAttribute("value",""+zn(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?Jr(e,!!r.multiple,i,!1):r.defaultValue!=null&&Jr(e,!!r.multiple,r.defaultValue,!0);break;default:typeof s.onClick=="function"&&(e.onclick=yo)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return $e(t),null;case 6:if(e&&t.stateNode!=null)qp(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(B(166));if(n=rr(ji.current),rr(Yt.current),aa(t)){if(r=t.stateNode,n=t.memoizedProps,r[Ut]=t,(i=r.nodeValue!==n)&&(e=dt,e!==null))switch(e.tag){case 3:ia(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&ia(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Ut]=t,t.stateNode=r}return $e(t),null;case 13:if(pe(ve),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(ge&&ut!==null&&t.mode&1&&!(t.flags&128))gp(),cs(),t.flags|=98560,i=!1;else if(i=aa(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(B(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(B(317));i[Ut]=t}else cs(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;$e(t),i=!1}else It!==null&&(ru(It),It=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||ve.current&1?Ce===0&&(Ce=3):$d())),t.updateQueue!==null&&(t.flags|=4),$e(t),null);case 4:return ds(),Qc(e,t),e===null&&Ni(t.stateNode.containerInfo),$e(t),null;case 10:return _d(t.type._context),$e(t),null;case 17:return st(t.type)&&wo(),$e(t),null;case 19:if(pe(ve),i=t.memoizedState,i===null)return $e(t),null;if(r=(t.flags&128)!==0,o=i.rendering,o===null)if(r)Ls(i,!1);else{if(Ce!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(o=bo(e),o!==null){for(t.flags|=128,Ls(i,!1),r=o.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,o=i.alternate,o===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=o.childLanes,i.lanes=o.lanes,i.child=o.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=o.memoizedProps,i.memoizedState=o.memoizedState,i.updateQueue=o.updateQueue,i.type=o.type,e=o.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return he(ve,ve.current&1|2),t.child}e=e.sibling}i.tail!==null&&Ee()>hs&&(t.flags|=128,r=!0,Ls(i,!1),t.lanes=4194304)}else{if(!r)if(e=bo(o),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Ls(i,!0),i.tail===null&&i.tailMode==="hidden"&&!o.alternate&&!ge)return $e(t),null}else 2*Ee()-i.renderingStartTime>hs&&n!==1073741824&&(t.flags|=128,r=!0,Ls(i,!1),t.lanes=4194304);i.isBackwards?(o.sibling=t.child,t.child=o):(n=i.last,n!==null?n.sibling=o:t.child=o,i.last=o)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=Ee(),t.sibling=null,n=ve.current,he(ve,r?n&1|2:n&1),t):($e(t),null);case 22:case 23:return Gd(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?lt&1073741824&&($e(t),t.subtreeFlags&6&&(t.flags|=8192)):$e(t),null;case 24:return null;case 25:return null}throw Error(B(156,t.tag))}function I1(e,t){switch(Ed(t),t.tag){case 1:return st(t.type)&&wo(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return ds(),pe(rt),pe(Ke),kd(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Id(t),null;case 13:if(pe(ve),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(B(340));cs()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return pe(ve),null;case 4:return ds(),null;case 10:return _d(t.type._context),null;case 22:case 23:return Gd(),null;case 24:return null;default:return null}}var ca=!1,Ye=!1,k1=typeof WeakSet=="function"?WeakSet:Set,G=null;function Yr(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){Ne(e,t,r)}else n.current=null}function Zc(e,t,n){try{n()}catch(r){Ne(e,t,r)}}var wh=!1;function L1(e,t){if(Lc=mo,e=rp(),Sd(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var s=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var o=0,l=-1,c=-1,u=0,d=0,h=e,y=null;t:for(;;){for(var g;h!==n||s!==0&&h.nodeType!==3||(l=o+s),h!==i||r!==0&&h.nodeType!==3||(c=o+r),h.nodeType===3&&(o+=h.nodeValue.length),(g=h.firstChild)!==null;)y=h,h=g;for(;;){if(h===e)break t;if(y===n&&++u===s&&(l=o),y===i&&++d===r&&(c=o),(g=h.nextSibling)!==null)break;h=y,y=h.parentNode}h=g}n=l===-1||c===-1?null:{start:l,end:c}}else n=null}n=n||{start:0,end:0}}else n=null;for(Oc={focusedElem:e,selectionRange:n},mo=!1,G=t;G!==null;)if(t=G,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,G=e;else for(;G!==null;){t=G;try{var S=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(S!==null){var N=S.memoizedProps,A=S.memoizedState,p=t.stateNode,m=p.getSnapshotBeforeUpdate(t.elementType===t.type?N:_t(t.type,N),A);p.__reactInternalSnapshotBeforeUpdate=m}break;case 3:var f=t.stateNode.containerInfo;f.nodeType===1?f.textContent="":f.nodeType===9&&f.documentElement&&f.removeChild(f.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(B(163))}}catch(x){Ne(t,t.return,x)}if(e=t.sibling,e!==null){e.return=t.return,G=e;break}G=t.return}return S=wh,wh=!1,S}function si(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var s=r=r.next;do{if((s.tag&e)===e){var i=s.destroy;s.destroy=void 0,i!==void 0&&Zc(t,n,i)}s=s.next}while(s!==r)}}function nl(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Jc(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Xp(e){var t=e.alternate;t!==null&&(e.alternate=null,Xp(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Ut],delete t[Mi],delete t[Dc],delete t[p1],delete t[g1])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function eg(e){return e.tag===5||e.tag===3||e.tag===4}function vh(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||eg(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function qc(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=yo));else if(r!==4&&(e=e.child,e!==null))for(qc(e,t,n),e=e.sibling;e!==null;)qc(e,t,n),e=e.sibling}function Xc(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Xc(e,t,n),e=e.sibling;e!==null;)Xc(e,t,n),e=e.sibling}var De=null,Ct=!1;function pn(e,t,n){for(n=n.child;n!==null;)tg(e,t,n),n=n.sibling}function tg(e,t,n){if(Ht&&typeof Ht.onCommitFiberUnmount=="function")try{Ht.onCommitFiberUnmount(Vo,n)}catch{}switch(n.tag){case 5:Ye||Yr(n,t);case 6:var r=De,s=Ct;De=null,pn(e,t,n),De=r,Ct=s,De!==null&&(Ct?(e=De,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):De.removeChild(n.stateNode));break;case 18:De!==null&&(Ct?(e=De,n=n.stateNode,e.nodeType===8?Dl(e.parentNode,n):e.nodeType===1&&Dl(e,n),vi(e)):Dl(De,n.stateNode));break;case 4:r=De,s=Ct,De=n.stateNode.containerInfo,Ct=!0,pn(e,t,n),De=r,Ct=s;break;case 0:case 11:case 14:case 15:if(!Ye&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){s=r=r.next;do{var i=s,o=i.destroy;i=i.tag,o!==void 0&&(i&2||i&4)&&Zc(n,t,o),s=s.next}while(s!==r)}pn(e,t,n);break;case 1:if(!Ye&&(Yr(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){Ne(n,t,l)}pn(e,t,n);break;case 21:pn(e,t,n);break;case 22:n.mode&1?(Ye=(r=Ye)||n.memoizedState!==null,pn(e,t,n),Ye=r):pn(e,t,n);break;default:pn(e,t,n)}}function xh(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new k1),t.forEach(function(r){var s=F1.bind(null,e,r);n.has(r)||(n.add(r),r.then(s,s))})}}function bt(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var s=n[r];try{var i=e,o=t,l=o;e:for(;l!==null;){switch(l.tag){case 5:De=l.stateNode,Ct=!1;break e;case 3:De=l.stateNode.containerInfo,Ct=!0;break e;case 4:De=l.stateNode.containerInfo,Ct=!0;break e}l=l.return}if(De===null)throw Error(B(160));tg(i,o,s),De=null,Ct=!1;var c=s.alternate;c!==null&&(c.return=null),s.return=null}catch(u){Ne(s,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)ng(t,e),t=t.sibling}function ng(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(bt(t,e),Dt(e),r&4){try{si(3,e,e.return),nl(3,e)}catch(N){Ne(e,e.return,N)}try{si(5,e,e.return)}catch(N){Ne(e,e.return,N)}}break;case 1:bt(t,e),Dt(e),r&512&&n!==null&&Yr(n,n.return);break;case 5:if(bt(t,e),Dt(e),r&512&&n!==null&&Yr(n,n.return),e.flags&32){var s=e.stateNode;try{pi(s,"")}catch(N){Ne(e,e.return,N)}}if(r&4&&(s=e.stateNode,s!=null)){var i=e.memoizedProps,o=n!==null?n.memoizedProps:i,l=e.type,c=e.updateQueue;if(e.updateQueue=null,c!==null)try{l==="input"&&i.type==="radio"&&i.name!=null&&Mm(s,i),Ec(l,o);var u=Ec(l,i);for(o=0;o<c.length;o+=2){var d=c[o],h=c[o+1];d==="style"?Tm(s,h):d==="dangerouslySetInnerHTML"?_m(s,h):d==="children"?pi(s,h):ld(s,d,h,u)}switch(l){case"input":wc(s,i);break;case"textarea":bm(s,i);break;case"select":var y=s._wrapperState.wasMultiple;s._wrapperState.wasMultiple=!!i.multiple;var g=i.value;g!=null?Jr(s,!!i.multiple,g,!1):y!==!!i.multiple&&(i.defaultValue!=null?Jr(s,!!i.multiple,i.defaultValue,!0):Jr(s,!!i.multiple,i.multiple?[]:"",!1))}s[Mi]=i}catch(N){Ne(e,e.return,N)}}break;case 6:if(bt(t,e),Dt(e),r&4){if(e.stateNode===null)throw Error(B(162));s=e.stateNode,i=e.memoizedProps;try{s.nodeValue=i}catch(N){Ne(e,e.return,N)}}break;case 3:if(bt(t,e),Dt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{vi(t.containerInfo)}catch(N){Ne(e,e.return,N)}break;case 4:bt(t,e),Dt(e);break;case 13:bt(t,e),Dt(e),s=e.child,s.flags&8192&&(i=s.memoizedState!==null,s.stateNode.isHidden=i,!i||s.alternate!==null&&s.alternate.memoizedState!==null||(Ud=Ee())),r&4&&xh(e);break;case 22:if(d=n!==null&&n.memoizedState!==null,e.mode&1?(Ye=(u=Ye)||d,bt(t,e),Ye=u):bt(t,e),Dt(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!d&&e.mode&1)for(G=e,d=e.child;d!==null;){for(h=G=d;G!==null;){switch(y=G,g=y.child,y.tag){case 0:case 11:case 14:case 15:si(4,y,y.return);break;case 1:Yr(y,y.return);var S=y.stateNode;if(typeof S.componentWillUnmount=="function"){r=y,n=y.return;try{t=r,S.props=t.memoizedProps,S.state=t.memoizedState,S.componentWillUnmount()}catch(N){Ne(r,n,N)}}break;case 5:Yr(y,y.return);break;case 22:if(y.memoizedState!==null){Nh(h);continue}}g!==null?(g.return=y,G=g):Nh(h)}d=d.sibling}e:for(d=null,h=e;;){if(h.tag===5){if(d===null){d=h;try{s=h.stateNode,u?(i=s.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(l=h.stateNode,c=h.memoizedProps.style,o=c!=null&&c.hasOwnProperty("display")?c.display:null,l.style.display=Am("display",o))}catch(N){Ne(e,e.return,N)}}}else if(h.tag===6){if(d===null)try{h.stateNode.nodeValue=u?"":h.memoizedProps}catch(N){Ne(e,e.return,N)}}else if((h.tag!==22&&h.tag!==23||h.memoizedState===null||h===e)&&h.child!==null){h.child.return=h,h=h.child;continue}if(h===e)break e;for(;h.sibling===null;){if(h.return===null||h.return===e)break e;d===h&&(d=null),h=h.return}d===h&&(d=null),h.sibling.return=h.return,h=h.sibling}}break;case 19:bt(t,e),Dt(e),r&4&&xh(e);break;case 21:break;default:bt(t,e),Dt(e)}}function Dt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(eg(n)){var r=n;break e}n=n.return}throw Error(B(160))}switch(r.tag){case 5:var s=r.stateNode;r.flags&32&&(pi(s,""),r.flags&=-33);var i=vh(e);Xc(e,i,s);break;case 3:case 4:var o=r.stateNode.containerInfo,l=vh(e);qc(e,l,o);break;default:throw Error(B(161))}}catch(c){Ne(e,e.return,c)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function O1(e,t,n){G=e,rg(e)}function rg(e,t,n){for(var r=(e.mode&1)!==0;G!==null;){var s=G,i=s.child;if(s.tag===22&&r){var o=s.memoizedState!==null||ca;if(!o){var l=s.alternate,c=l!==null&&l.memoizedState!==null||Ye;l=ca;var u=Ye;if(ca=o,(Ye=c)&&!u)for(G=s;G!==null;)o=G,c=o.child,o.tag===22&&o.memoizedState!==null?Eh(s):c!==null?(c.return=o,G=c):Eh(s);for(;i!==null;)G=i,rg(i),i=i.sibling;G=s,ca=l,Ye=u}Sh(e)}else s.subtreeFlags&8772&&i!==null?(i.return=s,G=i):Sh(e)}}function Sh(e){for(;G!==null;){var t=G;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:Ye||nl(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!Ye)if(n===null)r.componentDidMount();else{var s=t.elementType===t.type?n.memoizedProps:_t(t.type,n.memoizedProps);r.componentDidUpdate(s,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&ih(t,i,r);break;case 3:var o=t.updateQueue;if(o!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}ih(t,o,n)}break;case 5:var l=t.stateNode;if(n===null&&t.flags&4){n=l;var c=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":c.autoFocus&&n.focus();break;case"img":c.src&&(n.src=c.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var d=u.memoizedState;if(d!==null){var h=d.dehydrated;h!==null&&vi(h)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(B(163))}Ye||t.flags&512&&Jc(t)}catch(y){Ne(t,t.return,y)}}if(t===e){G=null;break}if(n=t.sibling,n!==null){n.return=t.return,G=n;break}G=t.return}}function Nh(e){for(;G!==null;){var t=G;if(t===e){G=null;break}var n=t.sibling;if(n!==null){n.return=t.return,G=n;break}G=t.return}}function Eh(e){for(;G!==null;){var t=G;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{nl(4,t)}catch(c){Ne(t,n,c)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var s=t.return;try{r.componentDidMount()}catch(c){Ne(t,s,c)}}var i=t.return;try{Jc(t)}catch(c){Ne(t,i,c)}break;case 5:var o=t.return;try{Jc(t)}catch(c){Ne(t,o,c)}}}catch(c){Ne(t,t.return,c)}if(t===e){G=null;break}var l=t.sibling;if(l!==null){l.return=t.return,G=l;break}G=t.return}}var P1=Math.ceil,Ao=hn.ReactCurrentDispatcher,Wd=hn.ReactCurrentOwner,Nt=hn.ReactCurrentBatchConfig,ne=0,Pe=null,be=null,Ue=0,lt=0,Kr=Fn(0),Ce=0,Ci=null,gr=0,rl=0,Bd=0,ii=null,tt=null,Ud=0,hs=1/0,qt=null,To=!1,eu=null,Pn=null,ua=!1,_n=null,Co=0,ai=0,tu=null,_a=-1,Aa=0;function Je(){return ne&6?Ee():_a!==-1?_a:_a=Ee()}function Rn(e){return e.mode&1?ne&2&&Ue!==0?Ue&-Ue:w1.transition!==null?(Aa===0&&(Aa=Um()),Aa):(e=ce,e!==0||(e=window.event,e=e===void 0?16:Vm(e.type)),e):1}function Pt(e,t,n,r){if(50<ai)throw ai=0,tu=null,Error(B(185));Pi(e,n,r),(!(ne&2)||e!==Pe)&&(e===Pe&&(!(ne&2)&&(rl|=n),Ce===4&&En(e,Ue)),it(e,r),n===1&&ne===0&&!(t.mode&1)&&(hs=Ee()+500,Xo&&Gn()))}function it(e,t){var n=e.callbackNode;ww(e,t);var r=ho(e,e===Pe?Ue:0);if(r===0)n!==null&&kf(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&kf(n),t===1)e.tag===0?y1(Mh.bind(null,e)):hp(Mh.bind(null,e)),h1(function(){!(ne&6)&&Gn()}),n=null;else{switch(Fm(r)){case 1:n=hd;break;case 4:n=Wm;break;case 16:n=fo;break;case 536870912:n=Bm;break;default:n=fo}n=dg(n,sg.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function sg(e,t){if(_a=-1,Aa=0,ne&6)throw Error(B(327));var n=e.callbackNode;if(ns()&&e.callbackNode!==n)return null;var r=ho(e,e===Pe?Ue:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Io(e,r);else{t=r;var s=ne;ne|=2;var i=ag();(Pe!==e||Ue!==t)&&(qt=null,hs=Ee()+500,ur(e,t));do try{z1();break}catch(l){ig(e,l)}while(!0);jd(),Ao.current=i,ne=s,be!==null?t=0:(Pe=null,Ue=0,t=Ce)}if(t!==0){if(t===2&&(s=Ac(e),s!==0&&(r=s,t=nu(e,s))),t===1)throw n=Ci,ur(e,0),En(e,r),it(e,Ee()),n;if(t===6)En(e,r);else{if(s=e.current.alternate,!(r&30)&&!R1(s)&&(t=Io(e,r),t===2&&(i=Ac(e),i!==0&&(r=i,t=nu(e,i))),t===1))throw n=Ci,ur(e,0),En(e,r),it(e,Ee()),n;switch(e.finishedWork=s,e.finishedLanes=r,t){case 0:case 1:throw Error(B(345));case 2:Vn(e,tt,qt);break;case 3:if(En(e,r),(r&130023424)===r&&(t=Ud+500-Ee(),10<t)){if(ho(e,0)!==0)break;if(s=e.suspendedLanes,(s&r)!==r){Je(),e.pingedLanes|=e.suspendedLanes&s;break}e.timeoutHandle=Rc(Vn.bind(null,e,tt,qt),t);break}Vn(e,tt,qt);break;case 4:if(En(e,r),(r&4194240)===r)break;for(t=e.eventTimes,s=-1;0<r;){var o=31-Ot(r);i=1<<o,o=t[o],o>s&&(s=o),r&=~i}if(r=s,r=Ee()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*P1(r/1960))-r,10<r){e.timeoutHandle=Rc(Vn.bind(null,e,tt,qt),r);break}Vn(e,tt,qt);break;case 5:Vn(e,tt,qt);break;default:throw Error(B(329))}}}return it(e,Ee()),e.callbackNode===n?sg.bind(null,e):null}function nu(e,t){var n=ii;return e.current.memoizedState.isDehydrated&&(ur(e,t).flags|=256),e=Io(e,t),e!==2&&(t=tt,tt=n,t!==null&&ru(t)),e}function ru(e){tt===null?tt=e:tt.push.apply(tt,e)}function R1(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var s=n[r],i=s.getSnapshot;s=s.value;try{if(!Rt(i(),s))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function En(e,t){for(t&=~Bd,t&=~rl,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Ot(t),r=1<<n;e[n]=-1,t&=~r}}function Mh(e){if(ne&6)throw Error(B(327));ns();var t=ho(e,0);if(!(t&1))return it(e,Ee()),null;var n=Io(e,t);if(e.tag!==0&&n===2){var r=Ac(e);r!==0&&(t=r,n=nu(e,r))}if(n===1)throw n=Ci,ur(e,0),En(e,t),it(e,Ee()),n;if(n===6)throw Error(B(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Vn(e,tt,qt),it(e,Ee()),null}function Fd(e,t){var n=ne;ne|=1;try{return e(t)}finally{ne=n,ne===0&&(hs=Ee()+500,Xo&&Gn())}}function yr(e){_n!==null&&_n.tag===0&&!(ne&6)&&ns();var t=ne;ne|=1;var n=Nt.transition,r=ce;try{if(Nt.transition=null,ce=1,e)return e()}finally{ce=r,Nt.transition=n,ne=t,!(ne&6)&&Gn()}}function Gd(){lt=Kr.current,pe(Kr)}function ur(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,f1(n)),be!==null)for(n=be.return;n!==null;){var r=n;switch(Ed(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&wo();break;case 3:ds(),pe(rt),pe(Ke),kd();break;case 5:Id(r);break;case 4:ds();break;case 13:pe(ve);break;case 19:pe(ve);break;case 10:_d(r.type._context);break;case 22:case 23:Gd()}n=n.return}if(Pe=e,be=e=Dn(e.current,null),Ue=lt=t,Ce=0,Ci=null,Bd=rl=gr=0,tt=ii=null,nr!==null){for(t=0;t<nr.length;t++)if(n=nr[t],r=n.interleaved,r!==null){n.interleaved=null;var s=r.next,i=n.pending;if(i!==null){var o=i.next;i.next=s,r.next=o}n.pending=r}nr=null}return e}function ig(e,t){do{var n=be;try{if(jd(),Ma.current=_o,jo){for(var r=xe.memoizedState;r!==null;){var s=r.queue;s!==null&&(s.pending=null),r=r.next}jo=!1}if(pr=0,Le=Te=xe=null,ri=!1,_i=0,Wd.current=null,n===null||n.return===null){Ce=1,Ci=t,be=null;break}e:{var i=e,o=n.return,l=n,c=t;if(t=Ue,l.flags|=32768,c!==null&&typeof c=="object"&&typeof c.then=="function"){var u=c,d=l,h=d.tag;if(!(d.mode&1)&&(h===0||h===11||h===15)){var y=d.alternate;y?(d.updateQueue=y.updateQueue,d.memoizedState=y.memoizedState,d.lanes=y.lanes):(d.updateQueue=null,d.memoizedState=null)}var g=dh(o);if(g!==null){g.flags&=-257,fh(g,o,l,i,t),g.mode&1&&uh(i,u,t),t=g,c=u;var S=t.updateQueue;if(S===null){var N=new Set;N.add(c),t.updateQueue=N}else S.add(c);break e}else{if(!(t&1)){uh(i,u,t),$d();break e}c=Error(B(426))}}else if(ge&&l.mode&1){var A=dh(o);if(A!==null){!(A.flags&65536)&&(A.flags|=256),fh(A,o,l,i,t),Md(fs(c,l));break e}}i=c=fs(c,l),Ce!==4&&(Ce=2),ii===null?ii=[i]:ii.push(i),i=o;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var p=Fp(i,c,t);sh(i,p);break e;case 1:l=c;var m=i.type,f=i.stateNode;if(!(i.flags&128)&&(typeof m.getDerivedStateFromError=="function"||f!==null&&typeof f.componentDidCatch=="function"&&(Pn===null||!Pn.has(f)))){i.flags|=65536,t&=-t,i.lanes|=t;var x=Gp(i,l,t);sh(i,x);break e}}i=i.return}while(i!==null)}lg(n)}catch(M){t=M,be===n&&n!==null&&(be=n=n.return);continue}break}while(!0)}function ag(){var e=Ao.current;return Ao.current=_o,e===null?_o:e}function $d(){(Ce===0||Ce===3||Ce===2)&&(Ce=4),Pe===null||!(gr&268435455)&&!(rl&268435455)||En(Pe,Ue)}function Io(e,t){var n=ne;ne|=2;var r=ag();(Pe!==e||Ue!==t)&&(qt=null,ur(e,t));do try{D1();break}catch(s){ig(e,s)}while(!0);if(jd(),ne=n,Ao.current=r,be!==null)throw Error(B(261));return Pe=null,Ue=0,Ce}function D1(){for(;be!==null;)og(be)}function z1(){for(;be!==null&&!cw();)og(be)}function og(e){var t=ug(e.alternate,e,lt);e.memoizedProps=e.pendingProps,t===null?lg(e):be=t,Wd.current=null}function lg(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=I1(n,t),n!==null){n.flags&=32767,be=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Ce=6,be=null;return}}else if(n=C1(n,t,lt),n!==null){be=n;return}if(t=t.sibling,t!==null){be=t;return}be=t=e}while(t!==null);Ce===0&&(Ce=5)}function Vn(e,t,n){var r=ce,s=Nt.transition;try{Nt.transition=null,ce=1,W1(e,t,n,r)}finally{Nt.transition=s,ce=r}return null}function W1(e,t,n,r){do ns();while(_n!==null);if(ne&6)throw Error(B(327));n=e.finishedWork;var s=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(B(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(vw(e,i),e===Pe&&(be=Pe=null,Ue=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||ua||(ua=!0,dg(fo,function(){return ns(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=Nt.transition,Nt.transition=null;var o=ce;ce=1;var l=ne;ne|=4,Wd.current=null,L1(e,n),ng(n,e),i1(Oc),mo=!!Lc,Oc=Lc=null,e.current=n,O1(n),uw(),ne=l,ce=o,Nt.transition=i}else e.current=n;if(ua&&(ua=!1,_n=e,Co=s),i=e.pendingLanes,i===0&&(Pn=null),hw(n.stateNode),it(e,Ee()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)s=t[n],r(s.value,{componentStack:s.stack,digest:s.digest});if(To)throw To=!1,e=eu,eu=null,e;return Co&1&&e.tag!==0&&ns(),i=e.pendingLanes,i&1?e===tu?ai++:(ai=0,tu=e):ai=0,Gn(),null}function ns(){if(_n!==null){var e=Fm(Co),t=Nt.transition,n=ce;try{if(Nt.transition=null,ce=16>e?16:e,_n===null)var r=!1;else{if(e=_n,_n=null,Co=0,ne&6)throw Error(B(331));var s=ne;for(ne|=4,G=e.current;G!==null;){var i=G,o=i.child;if(G.flags&16){var l=i.deletions;if(l!==null){for(var c=0;c<l.length;c++){var u=l[c];for(G=u;G!==null;){var d=G;switch(d.tag){case 0:case 11:case 15:si(8,d,i)}var h=d.child;if(h!==null)h.return=d,G=h;else for(;G!==null;){d=G;var y=d.sibling,g=d.return;if(Xp(d),d===u){G=null;break}if(y!==null){y.return=g,G=y;break}G=g}}}var S=i.alternate;if(S!==null){var N=S.child;if(N!==null){S.child=null;do{var A=N.sibling;N.sibling=null,N=A}while(N!==null)}}G=i}}if(i.subtreeFlags&2064&&o!==null)o.return=i,G=o;else e:for(;G!==null;){if(i=G,i.flags&2048)switch(i.tag){case 0:case 11:case 15:si(9,i,i.return)}var p=i.sibling;if(p!==null){p.return=i.return,G=p;break e}G=i.return}}var m=e.current;for(G=m;G!==null;){o=G;var f=o.child;if(o.subtreeFlags&2064&&f!==null)f.return=o,G=f;else e:for(o=m;G!==null;){if(l=G,l.flags&2048)try{switch(l.tag){case 0:case 11:case 15:nl(9,l)}}catch(M){Ne(l,l.return,M)}if(l===o){G=null;break e}var x=l.sibling;if(x!==null){x.return=l.return,G=x;break e}G=l.return}}if(ne=s,Gn(),Ht&&typeof Ht.onPostCommitFiberRoot=="function")try{Ht.onPostCommitFiberRoot(Vo,e)}catch{}r=!0}return r}finally{ce=n,Nt.transition=t}}return!1}function bh(e,t,n){t=fs(n,t),t=Fp(e,t,1),e=On(e,t,1),t=Je(),e!==null&&(Pi(e,1,t),it(e,t))}function Ne(e,t,n){if(e.tag===3)bh(e,e,n);else for(;t!==null;){if(t.tag===3){bh(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Pn===null||!Pn.has(r))){e=fs(n,e),e=Gp(t,e,1),t=On(t,e,1),e=Je(),t!==null&&(Pi(t,1,e),it(t,e));break}}t=t.return}}function B1(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Je(),e.pingedLanes|=e.suspendedLanes&n,Pe===e&&(Ue&n)===n&&(Ce===4||Ce===3&&(Ue&130023424)===Ue&&500>Ee()-Ud?ur(e,0):Bd|=n),it(e,t)}function cg(e,t){t===0&&(e.mode&1?(t=ea,ea<<=1,!(ea&130023424)&&(ea=4194304)):t=1);var n=Je();e=cn(e,t),e!==null&&(Pi(e,t,n),it(e,n))}function U1(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),cg(e,n)}function F1(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,s=e.memoizedState;s!==null&&(n=s.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(B(314))}r!==null&&r.delete(t),cg(e,n)}var ug;ug=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||rt.current)nt=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return nt=!1,T1(e,t,n);nt=!!(e.flags&131072)}else nt=!1,ge&&t.flags&1048576&&mp(t,So,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;ja(e,t),e=t.pendingProps;var s=ls(t,Ke.current);ts(t,n),s=Od(null,t,r,e,s,n);var i=Pd();return t.flags|=1,typeof s=="object"&&s!==null&&typeof s.render=="function"&&s.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,st(r)?(i=!0,vo(t)):i=!1,t.memoizedState=s.state!==null&&s.state!==void 0?s.state:null,Td(t),s.updater=tl,t.stateNode=s,s._reactInternals=t,Gc(t,r,e,n),t=Yc(null,t,r,!0,i,n)):(t.tag=0,ge&&i&&Nd(t),Ze(null,t,s,n),t=t.child),t;case 16:r=t.elementType;e:{switch(ja(e,t),e=t.pendingProps,s=r._init,r=s(r._payload),t.type=r,s=t.tag=$1(r),e=_t(r,e),s){case 0:t=Hc(null,t,r,e,n);break e;case 1:t=ph(null,t,r,e,n);break e;case 11:t=hh(null,t,r,e,n);break e;case 14:t=mh(null,t,r,_t(r.type,e),n);break e}throw Error(B(306,r,""))}return t;case 0:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:_t(r,s),Hc(e,t,r,s,n);case 1:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:_t(r,s),ph(e,t,r,s,n);case 3:e:{if(Kp(t),e===null)throw Error(B(387));r=t.pendingProps,i=t.memoizedState,s=i.element,xp(e,t),Mo(t,r,null,n);var o=t.memoizedState;if(r=o.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){s=fs(Error(B(423)),t),t=gh(e,t,r,n,s);break e}else if(r!==s){s=fs(Error(B(424)),t),t=gh(e,t,r,n,s);break e}else for(ut=Ln(t.stateNode.containerInfo.firstChild),dt=t,ge=!0,It=null,n=wp(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(cs(),r===s){t=un(e,t,n);break e}Ze(e,t,r,n)}t=t.child}return t;case 5:return Sp(t),e===null&&Bc(t),r=t.type,s=t.pendingProps,i=e!==null?e.memoizedProps:null,o=s.children,Pc(r,s)?o=null:i!==null&&Pc(r,i)&&(t.flags|=32),Yp(e,t),Ze(e,t,o,n),t.child;case 6:return e===null&&Bc(t),null;case 13:return Vp(e,t,n);case 4:return Cd(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=us(t,null,r,n):Ze(e,t,r,n),t.child;case 11:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:_t(r,s),hh(e,t,r,s,n);case 7:return Ze(e,t,t.pendingProps,n),t.child;case 8:return Ze(e,t,t.pendingProps.children,n),t.child;case 12:return Ze(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,s=t.pendingProps,i=t.memoizedProps,o=s.value,he(No,r._currentValue),r._currentValue=o,i!==null)if(Rt(i.value,o)){if(i.children===s.children&&!rt.current){t=un(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var l=i.dependencies;if(l!==null){o=i.child;for(var c=l.firstContext;c!==null;){if(c.context===r){if(i.tag===1){c=an(-1,n&-n),c.tag=2;var u=i.updateQueue;if(u!==null){u=u.shared;var d=u.pending;d===null?c.next=c:(c.next=d.next,d.next=c),u.pending=c}}i.lanes|=n,c=i.alternate,c!==null&&(c.lanes|=n),Uc(i.return,n,t),l.lanes|=n;break}c=c.next}}else if(i.tag===10)o=i.type===t.type?null:i.child;else if(i.tag===18){if(o=i.return,o===null)throw Error(B(341));o.lanes|=n,l=o.alternate,l!==null&&(l.lanes|=n),Uc(o,n,t),o=i.sibling}else o=i.child;if(o!==null)o.return=i;else for(o=i;o!==null;){if(o===t){o=null;break}if(i=o.sibling,i!==null){i.return=o.return,o=i;break}o=o.return}i=o}Ze(e,t,s.children,n),t=t.child}return t;case 9:return s=t.type,r=t.pendingProps.children,ts(t,n),s=Et(s),r=r(s),t.flags|=1,Ze(e,t,r,n),t.child;case 14:return r=t.type,s=_t(r,t.pendingProps),s=_t(r.type,s),mh(e,t,r,s,n);case 15:return $p(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:_t(r,s),ja(e,t),t.tag=1,st(r)?(e=!0,vo(t)):e=!1,ts(t,n),Up(t,r,s),Gc(t,r,s,n),Yc(null,t,r,!0,e,n);case 19:return Qp(e,t,n);case 22:return Hp(e,t,n)}throw Error(B(156,t.tag))};function dg(e,t){return zm(e,t)}function G1(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function St(e,t,n,r){return new G1(e,t,n,r)}function Hd(e){return e=e.prototype,!(!e||!e.isReactComponent)}function $1(e){if(typeof e=="function")return Hd(e)?1:0;if(e!=null){if(e=e.$$typeof,e===ud)return 11;if(e===dd)return 14}return 2}function Dn(e,t){var n=e.alternate;return n===null?(n=St(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Ta(e,t,n,r,s,i){var o=2;if(r=e,typeof e=="function")Hd(e)&&(o=1);else if(typeof e=="string")o=5;else e:switch(e){case Dr:return dr(n.children,s,i,t);case cd:o=8,s|=8;break;case hc:return e=St(12,n,t,s|2),e.elementType=hc,e.lanes=i,e;case mc:return e=St(13,n,t,s),e.elementType=mc,e.lanes=i,e;case pc:return e=St(19,n,t,s),e.elementType=pc,e.lanes=i,e;case Sm:return sl(n,s,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case vm:o=10;break e;case xm:o=9;break e;case ud:o=11;break e;case dd:o=14;break e;case vn:o=16,r=null;break e}throw Error(B(130,e==null?e:typeof e,""))}return t=St(o,n,t,s),t.elementType=e,t.type=r,t.lanes=i,t}function dr(e,t,n,r){return e=St(7,e,r,t),e.lanes=n,e}function sl(e,t,n,r){return e=St(22,e,r,t),e.elementType=Sm,e.lanes=n,e.stateNode={isHidden:!1},e}function Hl(e,t,n){return e=St(6,e,null,t),e.lanes=n,e}function Yl(e,t,n){return t=St(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function H1(e,t,n,r,s){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=jl(0),this.expirationTimes=jl(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=jl(0),this.identifierPrefix=r,this.onRecoverableError=s,this.mutableSourceEagerHydrationData=null}function Yd(e,t,n,r,s,i,o,l,c){return e=new H1(e,t,n,l,c),t===1?(t=1,i===!0&&(t|=8)):t=0,i=St(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Td(i),e}function Y1(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Rr,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function fg(e){if(!e)return Wn;e=e._reactInternals;e:{if(xr(e)!==e||e.tag!==1)throw Error(B(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(st(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(B(171))}if(e.tag===1){var n=e.type;if(st(n))return fp(e,n,t)}return t}function hg(e,t,n,r,s,i,o,l,c){return e=Yd(n,r,!0,e,s,i,o,l,c),e.context=fg(null),n=e.current,r=Je(),s=Rn(n),i=an(r,s),i.callback=t??null,On(n,i,s),e.current.lanes=s,Pi(e,s,r),it(e,r),e}function il(e,t,n,r){var s=t.current,i=Je(),o=Rn(s);return n=fg(n),t.context===null?t.context=n:t.pendingContext=n,t=an(i,o),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=On(s,t,o),e!==null&&(Pt(e,s,o,i),Ea(e,s,o)),o}function ko(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function jh(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Kd(e,t){jh(e,t),(e=e.alternate)&&jh(e,t)}function K1(){return null}var mg=typeof reportError=="function"?reportError:function(e){console.error(e)};function Vd(e){this._internalRoot=e}al.prototype.render=Vd.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(B(409));il(e,t,null,null)};al.prototype.unmount=Vd.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;yr(function(){il(null,e,null,null)}),t[ln]=null}};function al(e){this._internalRoot=e}al.prototype.unstable_scheduleHydration=function(e){if(e){var t=Hm();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Nn.length&&t!==0&&t<Nn[n].priority;n++);Nn.splice(n,0,e),n===0&&Km(e)}};function Qd(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function ol(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function _h(){}function V1(e,t,n,r,s){if(s){if(typeof r=="function"){var i=r;r=function(){var u=ko(o);i.call(u)}}var o=hg(t,r,e,0,null,!1,!1,"",_h);return e._reactRootContainer=o,e[ln]=o.current,Ni(e.nodeType===8?e.parentNode:e),yr(),o}for(;s=e.lastChild;)e.removeChild(s);if(typeof r=="function"){var l=r;r=function(){var u=ko(c);l.call(u)}}var c=Yd(e,0,!1,null,null,!1,!1,"",_h);return e._reactRootContainer=c,e[ln]=c.current,Ni(e.nodeType===8?e.parentNode:e),yr(function(){il(t,c,n,r)}),c}function ll(e,t,n,r,s){var i=n._reactRootContainer;if(i){var o=i;if(typeof s=="function"){var l=s;s=function(){var c=ko(o);l.call(c)}}il(t,o,e,s)}else o=V1(n,t,e,s,r);return ko(o)}Gm=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=zs(t.pendingLanes);n!==0&&(md(t,n|1),it(t,Ee()),!(ne&6)&&(hs=Ee()+500,Gn()))}break;case 13:yr(function(){var r=cn(e,1);if(r!==null){var s=Je();Pt(r,e,1,s)}}),Kd(e,1)}};pd=function(e){if(e.tag===13){var t=cn(e,134217728);if(t!==null){var n=Je();Pt(t,e,134217728,n)}Kd(e,134217728)}};$m=function(e){if(e.tag===13){var t=Rn(e),n=cn(e,t);if(n!==null){var r=Je();Pt(n,e,t,r)}Kd(e,t)}};Hm=function(){return ce};Ym=function(e,t){var n=ce;try{return ce=e,t()}finally{ce=n}};bc=function(e,t,n){switch(t){case"input":if(wc(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var s=qo(r);if(!s)throw Error(B(90));Em(r),wc(r,s)}}}break;case"textarea":bm(e,n);break;case"select":t=n.value,t!=null&&Jr(e,!!n.multiple,t,!1)}};km=Fd;Lm=yr;var Q1={usingClientEntryPoint:!1,Events:[Di,Ur,qo,Cm,Im,Fd]},Os={findFiberByHostInstance:tr,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Z1={bundleType:Os.bundleType,version:Os.version,rendererPackageName:Os.rendererPackageName,rendererConfig:Os.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:hn.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Rm(e),e===null?null:e.stateNode},findFiberByHostInstance:Os.findFiberByHostInstance||K1,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var da=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!da.isDisabled&&da.supportsFiber)try{Vo=da.inject(Z1),Ht=da}catch{}}pt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Q1;pt.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Qd(t))throw Error(B(200));return Y1(e,t,null,n)};pt.createRoot=function(e,t){if(!Qd(e))throw Error(B(299));var n=!1,r="",s=mg;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(s=t.onRecoverableError)),t=Yd(e,1,!1,null,null,n,!1,r,s),e[ln]=t.current,Ni(e.nodeType===8?e.parentNode:e),new Vd(t)};pt.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(B(188)):(e=Object.keys(e).join(","),Error(B(268,e)));return e=Rm(t),e=e===null?null:e.stateNode,e};pt.flushSync=function(e){return yr(e)};pt.hydrate=function(e,t,n){if(!ol(t))throw Error(B(200));return ll(null,e,t,!0,n)};pt.hydrateRoot=function(e,t,n){if(!Qd(e))throw Error(B(405));var r=n!=null&&n.hydratedSources||null,s=!1,i="",o=mg;if(n!=null&&(n.unstable_strictMode===!0&&(s=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(o=n.onRecoverableError)),t=hg(t,null,e,1,n??null,s,!1,i,o),e[ln]=t.current,Ni(e),r)for(e=0;e<r.length;e++)n=r[e],s=n._getVersion,s=s(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,s]:t.mutableSourceEagerHydrationData.push(n,s);return new al(t)};pt.render=function(e,t,n){if(!ol(t))throw Error(B(200));return ll(null,e,t,!1,n)};pt.unmountComponentAtNode=function(e){if(!ol(e))throw Error(B(40));return e._reactRootContainer?(yr(function(){ll(null,null,e,!1,function(){e._reactRootContainer=null,e[ln]=null})}),!0):!1};pt.unstable_batchedUpdates=Fd;pt.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!ol(n))throw Error(B(200));if(e==null||e._reactInternals===void 0)throw Error(B(38));return ll(e,t,n,!1,r)};pt.version="18.3.1-next-f1338f8080-20240426";function pg(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(pg)}catch(e){console.error(e)}}pg(),pm.exports=pt;var gg=pm.exports,Ah=gg;dc.createRoot=Ah.createRoot,dc.hydrateRoot=Ah.hydrateRoot;var yg={exports:{}};(function(e){var t=Object.prototype.hasOwnProperty,n="~";function r(){}Object.create&&(r.prototype=Object.create(null),new r().__proto__||(n=!1));function s(c,u,d){this.fn=c,this.context=u,this.once=d||!1}function i(c,u,d,h,y){if(typeof d!="function")throw new TypeError("The listener must be a function");var g=new s(d,h||c,y),S=n?n+u:u;return c._events[S]?c._events[S].fn?c._events[S]=[c._events[S],g]:c._events[S].push(g):(c._events[S]=g,c._eventsCount++),c}function o(c,u){--c._eventsCount===0?c._events=new r:delete c._events[u]}function l(){this._events=new r,this._eventsCount=0}l.prototype.eventNames=function(){var u=[],d,h;if(this._eventsCount===0)return u;for(h in d=this._events)t.call(d,h)&&u.push(n?h.slice(1):h);return Object.getOwnPropertySymbols?u.concat(Object.getOwnPropertySymbols(d)):u},l.prototype.listeners=function(u){var d=n?n+u:u,h=this._events[d];if(!h)return[];if(h.fn)return[h.fn];for(var y=0,g=h.length,S=new Array(g);y<g;y++)S[y]=h[y].fn;return S},l.prototype.listenerCount=function(u){var d=n?n+u:u,h=this._events[d];return h?h.fn?1:h.length:0},l.prototype.emit=function(u,d,h,y,g,S){var N=n?n+u:u;if(!this._events[N])return!1;var A=this._events[N],p=arguments.length,m,f;if(A.fn){switch(A.once&&this.removeListener(u,A.fn,void 0,!0),p){case 1:return A.fn.call(A.context),!0;case 2:return A.fn.call(A.context,d),!0;case 3:return A.fn.call(A.context,d,h),!0;case 4:return A.fn.call(A.context,d,h,y),!0;case 5:return A.fn.call(A.context,d,h,y,g),!0;case 6:return A.fn.call(A.context,d,h,y,g,S),!0}for(f=1,m=new Array(p-1);f<p;f++)m[f-1]=arguments[f];A.fn.apply(A.context,m)}else{var x=A.length,M;for(f=0;f<x;f++)switch(A[f].once&&this.removeListener(u,A[f].fn,void 0,!0),p){case 1:A[f].fn.call(A[f].context);break;case 2:A[f].fn.call(A[f].context,d);break;case 3:A[f].fn.call(A[f].context,d,h);break;case 4:A[f].fn.call(A[f].context,d,h,y);break;default:if(!m)for(M=1,m=new Array(p-1);M<p;M++)m[M-1]=arguments[M];A[f].fn.apply(A[f].context,m)}}return!0},l.prototype.on=function(u,d,h){return i(this,u,d,h,!1)},l.prototype.once=function(u,d,h){return i(this,u,d,h,!0)},l.prototype.removeListener=function(u,d,h,y){var g=n?n+u:u;if(!this._events[g])return this;if(!d)return o(this,g),this;var S=this._events[g];if(S.fn)S.fn===d&&(!y||S.once)&&(!h||S.context===h)&&o(this,g);else{for(var N=0,A=[],p=S.length;N<p;N++)(S[N].fn!==d||y&&!S[N].once||h&&S[N].context!==h)&&A.push(S[N]);A.length?this._events[g]=A.length===1?A[0]:A:o(this,g)}return this},l.prototype.removeAllListeners=function(u){var d;return u?(d=n?n+u:u,this._events[d]&&o(this,d)):(this._events=new r,this._eventsCount=0),this},l.prototype.off=l.prototype.removeListener,l.prototype.addListener=l.prototype.on,l.prefixed=n,l.EventEmitter=l,e.exports=l})(yg);var J1=yg.exports;const wg=Yo(J1);class ye extends Error{constructor(t,n){super(t),this.error=n}}class dn extends ye{constructor(){super(...arguments),this.name="WalletNotReadyError"}}class Zd extends ye{constructor(){super(...arguments),this.name="WalletLoadError"}}class sr extends ye{constructor(){super(...arguments),this.name="WalletConfigError"}}class Bn extends ye{constructor(){super(...arguments),this.name="WalletConnectionError"}}class Sr extends ye{constructor(){super(...arguments),this.name="WalletDisconnectedError"}}class Nr extends ye{constructor(){super(...arguments),this.name="WalletDisconnectionError"}}class $t extends ye{constructor(){super(...arguments),this.name="WalletAccountError"}}class fn extends ye{constructor(){super(...arguments),this.name="WalletPublicKeyError"}}class q1 extends ye{constructor(){super(...arguments),this.name="WalletKeypairError"}}class ie extends ye{constructor(){super(...arguments),this.name="WalletNotConnectedError"}}class kt extends ye{constructor(){super(...arguments),this.name="WalletSendTransactionError"}}class Be extends ye{constructor(){super(...arguments),this.name="WalletSignTransactionError"}}class Wi extends ye{constructor(){super(...arguments),this.name="WalletSignMessageError"}}class su extends ye{constructor(){super(...arguments),this.name="WalletSignInError"}}class X1 extends ye{constructor(){super(...arguments),this.name="WalletTimeoutError"}}class ev extends ye{constructor(){super(...arguments),this.name="WalletWindowBlockedError"}}class tv extends ye{constructor(){super(...arguments),this.name="WalletWindowClosedError"}}var V;(function(e){e.Installed="Installed",e.NotDetected="NotDetected",e.Loadable="Loadable",e.Unsupported="Unsupported"})(V||(V={}));class Jd extends wg{get connected(){return!!this.publicKey}async autoConnect(){await this.connect()}async prepareTransaction(t,n,r={}){const s=this.publicKey;if(!s)throw new ie;return t.feePayer=t.feePayer||s,t.recentBlockhash=t.recentBlockhash||(await n.getLatestBlockhash({commitment:r.preflightCommitment,minContextSlot:r.minContextSlot})).blockhash,t}}function Bi(e){if(typeof window>"u"||typeof document>"u")return;const t=[];function n(){if(e())for(const i of t)i()}const r=setInterval(n,1e3);t.push(()=>clearInterval(r)),document.readyState==="loading"&&(document.addEventListener("DOMContentLoaded",n,{once:!0}),t.push(()=>document.removeEventListener("DOMContentLoaded",n))),document.readyState!=="complete"&&(window.addEventListener("load",n,{once:!0}),t.push(()=>window.removeEventListener("load",n))),n()}function Lo(){if(!navigator)return!1;const e=navigator.userAgent.toLowerCase(),t=e.includes("iphone")||e.includes("ipad"),n=e.includes("safari");return t&&n}function Lt(e){return"version"in e}class qd extends Jd{async sendTransaction(t,n,r={}){let s=!0;try{if(Lt(t)){if(!this.supportedTransactionVersions)throw new kt("Sending versioned transactions isn't supported by this wallet");if(!this.supportedTransactionVersions.has(t.version))throw new kt(`Sending transaction version ${t.version} isn't supported by this wallet`);try{t=await this.signTransaction(t);const i=t.serialize();return await n.sendRawTransaction(i,r)}catch(i){throw i instanceof Be?(s=!1,i):new kt(i==null?void 0:i.message,i)}}else try{const{signers:i,...o}=r;t=await this.prepareTransaction(t,n,o),i!=null&&i.length&&t.partialSign(...i),t=await this.signTransaction(t);const l=t.serialize();return await n.sendRawTransaction(l,o)}catch(i){throw i instanceof Be?(s=!1,i):new kt(i==null?void 0:i.message,i)}}catch(i){throw s&&this.emit("error",i),i}}async signAllTransactions(t){for(const r of t)if(Lt(r)){if(!this.supportedTransactionVersions)throw new Be("Signing versioned transactions isn't supported by this wallet");if(!this.supportedTransactionVersions.has(r.version))throw new Be(`Signing transaction version ${r.version} isn't supported by this wallet`)}const n=[];for(const r of t)n.push(await this.signTransaction(r));return n}}class Ms extends qd{}class nv extends Ms{}const Ft="solana:signAndSendTransaction",Ca="solana:signIn",Xn="solana:signMessage",Ae="solana:signTransaction",rv="solana:signAndSendAllTransactions",sv=Object.freeze(Object.defineProperty({__proto__:null,SignAndSendAllTransactions:rv,SolanaSignAndSendTransaction:Ft,SolanaSignIn:Ca,SolanaSignMessage:Xn,SolanaSignTransaction:Ae},Symbol.toStringTag,{value:"Module"})),Ui="standard:connect",iv=Ui,Ii="standard:disconnect",av=Ii,Fi="standard:events",ov=Fi,lv=Object.freeze(Object.defineProperty({__proto__:null,Connect:iv,Disconnect:av,Events:ov,StandardConnect:Ui,StandardDisconnect:Ii,StandardEvents:Fi},Symbol.toStringTag,{value:"Module"}));function vg(e){return Ui in e.features&&Fi in e.features&&(Ft in e.features||Ae in e.features)}var Oo;(function(e){e.Mainnet="mainnet-beta",e.Testnet="testnet",e.Devnet="devnet"})(Oo||(Oo={}));const cv=Object.freeze(Object.defineProperty({__proto__:null,BaseMessageSignerWalletAdapter:Ms,BaseSignInMessageSignerWalletAdapter:nv,BaseSignerWalletAdapter:qd,BaseWalletAdapter:Jd,EventEmitter:wg,WalletAccountError:$t,get WalletAdapterNetwork(){return Oo},WalletConfigError:sr,WalletConnectionError:Bn,WalletDisconnectedError:Sr,WalletDisconnectionError:Nr,WalletError:ye,WalletKeypairError:q1,WalletLoadError:Zd,WalletNotConnectedError:ie,WalletNotReadyError:dn,WalletPublicKeyError:fn,get WalletReadyState(){return V},WalletSendTransactionError:kt,WalletSignInError:su,WalletSignMessageError:Wi,WalletSignTransactionError:Be,WalletTimeoutError:X1,WalletWindowBlockedError:ev,WalletWindowClosedError:tv,isIosAndRedirectable:Lo,isVersionedTransaction:Lt,isWalletAdapterCompatibleStandardWallet:vg,scopePollingDetectionStrategy:Bi},Symbol.toStringTag,{value:"Module"})),xg=E.createContext({});function Sg(){return E.useContext(xg)}const uv=({children:e,endpoint:t,config:n={commitment:"confirmed"}})=>{const r=E.useMemo(()=>new oo(t,n),[t,n]);return W.createElement(xg.Provider,{value:{connection:r}},e)};class Th extends ye{constructor(){super(...arguments),this.name="WalletNotSelectedError"}}const dv=[],cl={autoConnect:!1,connecting:!1,connected:!1,disconnecting:!1,select(){At("call","select")},connect(){return Promise.reject(At("call","connect"))},disconnect(){return Promise.reject(At("call","disconnect"))},sendTransaction(){return Promise.reject(At("call","sendTransaction"))},signTransaction(){return Promise.reject(At("call","signTransaction"))},signAllTransactions(){return Promise.reject(At("call","signAllTransactions"))},signMessage(){return Promise.reject(At("call","signMessage"))},signIn(){return Promise.reject(At("call","signIn"))}};Object.defineProperty(cl,"wallets",{get(){return At("read","wallets"),dv}});Object.defineProperty(cl,"wallet",{get(){return At("read","wallet"),null}});Object.defineProperty(cl,"publicKey",{get(){return At("read","publicKey"),null}});function At(e,t){const n=new Error(`You have tried to ${e} "${t}" on a WalletContext without providing one. Make sure to render a WalletProvider as an ancestor of the component that uses WalletContext.`);return console.error(n),n}const Ng=E.createContext(cl);function ul(){return E.useContext(Ng)}function fv(e,t){const n=E.useState(()=>{try{const i=localStorage.getItem(e);if(i)return JSON.parse(i)}catch(i){typeof window<"u"&&console.error(i)}return t}),r=n[0],s=E.useRef(!0);return E.useEffect(()=>{if(s.current){s.current=!1;return}try{r===null?localStorage.removeItem(e):localStorage.setItem(e,JSON.stringify(r))}catch(i){typeof window<"u"&&console.error(i)}},[r,e]),n}var $n={};const hv=xs(cv),Eg=xs(Ny),Mg=xs(sv);var Kt={},Gi={},mv=function(){return typeof Promise=="function"&&Promise.prototype&&Promise.prototype.then},bg={},yt={};let Xd;const pv=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];yt.getSymbolSize=function(t){if(!t)throw new Error('"version" cannot be null or undefined');if(t<1||t>40)throw new Error('"version" should be in range from 1 to 40');return t*4+17};yt.getSymbolTotalCodewords=function(t){return pv[t]};yt.getBCHDigit=function(e){let t=0;for(;e!==0;)t++,e>>>=1;return t};yt.setToSJISFunction=function(t){if(typeof t!="function")throw new Error('"toSJISFunc" is not a valid function.');Xd=t};yt.isKanjiModeEnabled=function(){return typeof Xd<"u"};yt.toSJIS=function(t){return Xd(t)};var dl={};(function(e){e.L={bit:1},e.M={bit:0},e.Q={bit:3},e.H={bit:2};function t(n){if(typeof n!="string")throw new Error("Param is not a string");switch(n.toLowerCase()){case"l":case"low":return e.L;case"m":case"medium":return e.M;case"q":case"quartile":return e.Q;case"h":case"high":return e.H;default:throw new Error("Unknown EC Level: "+n)}}e.isValid=function(r){return r&&typeof r.bit<"u"&&r.bit>=0&&r.bit<4},e.from=function(r,s){if(e.isValid(r))return r;try{return t(r)}catch{return s}}})(dl);function jg(){this.buffer=[],this.length=0}jg.prototype={get:function(e){const t=Math.floor(e/8);return(this.buffer[t]>>>7-e%8&1)===1},put:function(e,t){for(let n=0;n<t;n++)this.putBit((e>>>t-n-1&1)===1)},getLengthInBits:function(){return this.length},putBit:function(e){const t=Math.floor(this.length/8);this.buffer.length<=t&&this.buffer.push(0),e&&(this.buffer[t]|=128>>>this.length%8),this.length++}};var gv=jg;function $i(e){if(!e||e<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=e,this.data=new Uint8Array(e*e),this.reservedBit=new Uint8Array(e*e)}$i.prototype.set=function(e,t,n,r){const s=e*this.size+t;this.data[s]=n,r&&(this.reservedBit[s]=!0)};$i.prototype.get=function(e,t){return this.data[e*this.size+t]};$i.prototype.xor=function(e,t,n){this.data[e*this.size+t]^=n};$i.prototype.isReserved=function(e,t){return this.reservedBit[e*this.size+t]};var yv=$i,_g={};(function(e){const t=yt.getSymbolSize;e.getRowColCoords=function(r){if(r===1)return[];const s=Math.floor(r/7)+2,i=t(r),o=i===145?26:Math.ceil((i-13)/(2*s-2))*2,l=[i-7];for(let c=1;c<s-1;c++)l[c]=l[c-1]-o;return l.push(6),l.reverse()},e.getPositions=function(r){const s=[],i=e.getRowColCoords(r),o=i.length;for(let l=0;l<o;l++)for(let c=0;c<o;c++)l===0&&c===0||l===0&&c===o-1||l===o-1&&c===0||s.push([i[l],i[c]]);return s}})(_g);var Ag={};const wv=yt.getSymbolSize,Ch=7;Ag.getPositions=function(t){const n=wv(t);return[[0,0],[n-Ch,0],[0,n-Ch]]};var Tg={};(function(e){e.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};const t={N1:3,N2:3,N3:40,N4:10};e.isValid=function(s){return s!=null&&s!==""&&!isNaN(s)&&s>=0&&s<=7},e.from=function(s){return e.isValid(s)?parseInt(s,10):void 0},e.getPenaltyN1=function(s){const i=s.size;let o=0,l=0,c=0,u=null,d=null;for(let h=0;h<i;h++){l=c=0,u=d=null;for(let y=0;y<i;y++){let g=s.get(h,y);g===u?l++:(l>=5&&(o+=t.N1+(l-5)),u=g,l=1),g=s.get(y,h),g===d?c++:(c>=5&&(o+=t.N1+(c-5)),d=g,c=1)}l>=5&&(o+=t.N1+(l-5)),c>=5&&(o+=t.N1+(c-5))}return o},e.getPenaltyN2=function(s){const i=s.size;let o=0;for(let l=0;l<i-1;l++)for(let c=0;c<i-1;c++){const u=s.get(l,c)+s.get(l,c+1)+s.get(l+1,c)+s.get(l+1,c+1);(u===4||u===0)&&o++}return o*t.N2},e.getPenaltyN3=function(s){const i=s.size;let o=0,l=0,c=0;for(let u=0;u<i;u++){l=c=0;for(let d=0;d<i;d++)l=l<<1&2047|s.get(u,d),d>=10&&(l===1488||l===93)&&o++,c=c<<1&2047|s.get(d,u),d>=10&&(c===1488||c===93)&&o++}return o*t.N3},e.getPenaltyN4=function(s){let i=0;const o=s.data.length;for(let c=0;c<o;c++)i+=s.data[c];return Math.abs(Math.ceil(i*100/o/5)-10)*t.N4};function n(r,s,i){switch(r){case e.Patterns.PATTERN000:return(s+i)%2===0;case e.Patterns.PATTERN001:return s%2===0;case e.Patterns.PATTERN010:return i%3===0;case e.Patterns.PATTERN011:return(s+i)%3===0;case e.Patterns.PATTERN100:return(Math.floor(s/2)+Math.floor(i/3))%2===0;case e.Patterns.PATTERN101:return s*i%2+s*i%3===0;case e.Patterns.PATTERN110:return(s*i%2+s*i%3)%2===0;case e.Patterns.PATTERN111:return(s*i%3+(s+i)%2)%2===0;default:throw new Error("bad maskPattern:"+r)}}e.applyMask=function(s,i){const o=i.size;for(let l=0;l<o;l++)for(let c=0;c<o;c++)i.isReserved(c,l)||i.xor(c,l,n(s,c,l))},e.getBestMask=function(s,i){const o=Object.keys(e.Patterns).length;let l=0,c=1/0;for(let u=0;u<o;u++){i(u),e.applyMask(u,s);const d=e.getPenaltyN1(s)+e.getPenaltyN2(s)+e.getPenaltyN3(s)+e.getPenaltyN4(s);e.applyMask(u,s),d<c&&(c=d,l=u)}return l}})(Tg);var fl={};const An=dl,fa=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],ha=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];fl.getBlocksCount=function(t,n){switch(n){case An.L:return fa[(t-1)*4+0];case An.M:return fa[(t-1)*4+1];case An.Q:return fa[(t-1)*4+2];case An.H:return fa[(t-1)*4+3];default:return}};fl.getTotalCodewordsCount=function(t,n){switch(n){case An.L:return ha[(t-1)*4+0];case An.M:return ha[(t-1)*4+1];case An.Q:return ha[(t-1)*4+2];case An.H:return ha[(t-1)*4+3];default:return}};var Cg={},hl={};const oi=new Uint8Array(512),Po=new Uint8Array(256);(function(){let t=1;for(let n=0;n<255;n++)oi[n]=t,Po[t]=n,t<<=1,t&256&&(t^=285);for(let n=255;n<512;n++)oi[n]=oi[n-255]})();hl.log=function(t){if(t<1)throw new Error("log("+t+")");return Po[t]};hl.exp=function(t){return oi[t]};hl.mul=function(t,n){return t===0||n===0?0:oi[Po[t]+Po[n]]};(function(e){const t=hl;e.mul=function(r,s){const i=new Uint8Array(r.length+s.length-1);for(let o=0;o<r.length;o++)for(let l=0;l<s.length;l++)i[o+l]^=t.mul(r[o],s[l]);return i},e.mod=function(r,s){let i=new Uint8Array(r);for(;i.length-s.length>=0;){const o=i[0];for(let c=0;c<s.length;c++)i[c]^=t.mul(s[c],o);let l=0;for(;l<i.length&&i[l]===0;)l++;i=i.slice(l)}return i},e.generateECPolynomial=function(r){let s=new Uint8Array([1]);for(let i=0;i<r;i++)s=e.mul(s,new Uint8Array([1,t.exp(i)]));return s}})(Cg);const Ig=Cg;function ef(e){this.genPoly=void 0,this.degree=e,this.degree&&this.initialize(this.degree)}ef.prototype.initialize=function(t){this.degree=t,this.genPoly=Ig.generateECPolynomial(this.degree)};ef.prototype.encode=function(t){if(!this.genPoly)throw new Error("Encoder not initialized");const n=new Uint8Array(t.length+this.degree);n.set(t);const r=Ig.mod(n,this.genPoly),s=this.degree-r.length;if(s>0){const i=new Uint8Array(this.degree);return i.set(r,s),i}return r};var vv=ef,kg={},Hn={},tf={};tf.isValid=function(t){return!isNaN(t)&&t>=1&&t<=40};var Vt={};const Lg="[0-9]+",xv="[A-Z $%*+\\-./:]+";let ki="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";ki=ki.replace(/u/g,"\\u");const Sv="(?:(?![A-Z0-9 $%*+\\-./:]|"+ki+`)(?:.|[\r
]))+`;Vt.KANJI=new RegExp(ki,"g");Vt.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g");Vt.BYTE=new RegExp(Sv,"g");Vt.NUMERIC=new RegExp(Lg,"g");Vt.ALPHANUMERIC=new RegExp(xv,"g");const Nv=new RegExp("^"+ki+"$"),Ev=new RegExp("^"+Lg+"$"),Mv=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");Vt.testKanji=function(t){return Nv.test(t)};Vt.testNumeric=function(t){return Ev.test(t)};Vt.testAlphanumeric=function(t){return Mv.test(t)};(function(e){const t=tf,n=Vt;e.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},e.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},e.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},e.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},e.MIXED={bit:-1},e.getCharCountIndicator=function(i,o){if(!i.ccBits)throw new Error("Invalid mode: "+i);if(!t.isValid(o))throw new Error("Invalid version: "+o);return o>=1&&o<10?i.ccBits[0]:o<27?i.ccBits[1]:i.ccBits[2]},e.getBestModeForData=function(i){return n.testNumeric(i)?e.NUMERIC:n.testAlphanumeric(i)?e.ALPHANUMERIC:n.testKanji(i)?e.KANJI:e.BYTE},e.toString=function(i){if(i&&i.id)return i.id;throw new Error("Invalid mode")},e.isValid=function(i){return i&&i.bit&&i.ccBits};function r(s){if(typeof s!="string")throw new Error("Param is not a string");switch(s.toLowerCase()){case"numeric":return e.NUMERIC;case"alphanumeric":return e.ALPHANUMERIC;case"kanji":return e.KANJI;case"byte":return e.BYTE;default:throw new Error("Unknown mode: "+s)}}e.from=function(i,o){if(e.isValid(i))return i;try{return r(i)}catch{return o}}})(Hn);(function(e){const t=yt,n=fl,r=dl,s=Hn,i=tf,o=7973,l=t.getBCHDigit(o);function c(y,g,S){for(let N=1;N<=40;N++)if(g<=e.getCapacity(N,S,y))return N}function u(y,g){return s.getCharCountIndicator(y,g)+4}function d(y,g){let S=0;return y.forEach(function(N){const A=u(N.mode,g);S+=A+N.getBitsLength()}),S}function h(y,g){for(let S=1;S<=40;S++)if(d(y,S)<=e.getCapacity(S,g,s.MIXED))return S}e.from=function(g,S){return i.isValid(g)?parseInt(g,10):S},e.getCapacity=function(g,S,N){if(!i.isValid(g))throw new Error("Invalid QR Code version");typeof N>"u"&&(N=s.BYTE);const A=t.getSymbolTotalCodewords(g),p=n.getTotalCodewordsCount(g,S),m=(A-p)*8;if(N===s.MIXED)return m;const f=m-u(N,g);switch(N){case s.NUMERIC:return Math.floor(f/10*3);case s.ALPHANUMERIC:return Math.floor(f/11*2);case s.KANJI:return Math.floor(f/13);case s.BYTE:default:return Math.floor(f/8)}},e.getBestVersionForData=function(g,S){let N;const A=r.from(S,r.M);if(Array.isArray(g)){if(g.length>1)return h(g,A);if(g.length===0)return 1;N=g[0]}else N=g;return c(N.mode,N.getLength(),A)},e.getEncodedBits=function(g){if(!i.isValid(g)||g<7)throw new Error("Invalid QR Code version");let S=g<<12;for(;t.getBCHDigit(S)-l>=0;)S^=o<<t.getBCHDigit(S)-l;return g<<12|S}})(kg);var Og={};const iu=yt,Pg=1335,bv=21522,Ih=iu.getBCHDigit(Pg);Og.getEncodedBits=function(t,n){const r=t.bit<<3|n;let s=r<<10;for(;iu.getBCHDigit(s)-Ih>=0;)s^=Pg<<iu.getBCHDigit(s)-Ih;return(r<<10|s)^bv};var Rg={};const jv=Hn;function ms(e){this.mode=jv.NUMERIC,this.data=e.toString()}ms.getBitsLength=function(t){return 10*Math.floor(t/3)+(t%3?t%3*3+1:0)};ms.prototype.getLength=function(){return this.data.length};ms.prototype.getBitsLength=function(){return ms.getBitsLength(this.data.length)};ms.prototype.write=function(t){let n,r,s;for(n=0;n+3<=this.data.length;n+=3)r=this.data.substr(n,3),s=parseInt(r,10),t.put(s,10);const i=this.data.length-n;i>0&&(r=this.data.substr(n),s=parseInt(r,10),t.put(s,i*3+1))};var _v=ms;const Av=Hn,Kl=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function ps(e){this.mode=Av.ALPHANUMERIC,this.data=e}ps.getBitsLength=function(t){return 11*Math.floor(t/2)+6*(t%2)};ps.prototype.getLength=function(){return this.data.length};ps.prototype.getBitsLength=function(){return ps.getBitsLength(this.data.length)};ps.prototype.write=function(t){let n;for(n=0;n+2<=this.data.length;n+=2){let r=Kl.indexOf(this.data[n])*45;r+=Kl.indexOf(this.data[n+1]),t.put(r,11)}this.data.length%2&&t.put(Kl.indexOf(this.data[n]),6)};var Tv=ps;const Cv=Hn;function gs(e){this.mode=Cv.BYTE,typeof e=="string"?this.data=new TextEncoder().encode(e):this.data=new Uint8Array(e)}gs.getBitsLength=function(t){return t*8};gs.prototype.getLength=function(){return this.data.length};gs.prototype.getBitsLength=function(){return gs.getBitsLength(this.data.length)};gs.prototype.write=function(e){for(let t=0,n=this.data.length;t<n;t++)e.put(this.data[t],8)};var Iv=gs;const kv=Hn,Lv=yt;function ys(e){this.mode=kv.KANJI,this.data=e}ys.getBitsLength=function(t){return t*13};ys.prototype.getLength=function(){return this.data.length};ys.prototype.getBitsLength=function(){return ys.getBitsLength(this.data.length)};ys.prototype.write=function(e){let t;for(t=0;t<this.data.length;t++){let n=Lv.toSJIS(this.data[t]);if(n>=33088&&n<=40956)n-=33088;else if(n>=57408&&n<=60351)n-=49472;else throw new Error("Invalid SJIS character: "+this.data[t]+`
Make sure your charset is UTF-8`);n=(n>>>8&255)*192+(n&255),e.put(n,13)}};var Ov=ys,Dg={exports:{}};(function(e){var t={single_source_shortest_paths:function(n,r,s){var i={},o={};o[r]=0;var l=t.PriorityQueue.make();l.push(r,0);for(var c,u,d,h,y,g,S,N,A;!l.empty();){c=l.pop(),u=c.value,h=c.cost,y=n[u]||{};for(d in y)y.hasOwnProperty(d)&&(g=y[d],S=h+g,N=o[d],A=typeof o[d]>"u",(A||N>S)&&(o[d]=S,l.push(d,S),i[d]=u))}if(typeof s<"u"&&typeof o[s]>"u"){var p=["Could not find a path from ",r," to ",s,"."].join("");throw new Error(p)}return i},extract_shortest_path_from_predecessor_list:function(n,r){for(var s=[],i=r;i;)s.push(i),n[i],i=n[i];return s.reverse(),s},find_path:function(n,r,s){var i=t.single_source_shortest_paths(n,r,s);return t.extract_shortest_path_from_predecessor_list(i,s)},PriorityQueue:{make:function(n){var r=t.PriorityQueue,s={},i;n=n||{};for(i in r)r.hasOwnProperty(i)&&(s[i]=r[i]);return s.queue=[],s.sorter=n.sorter||r.default_sorter,s},default_sorter:function(n,r){return n.cost-r.cost},push:function(n,r){var s={value:n,cost:r};this.queue.push(s),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return this.queue.length===0}}};e.exports=t})(Dg);var Pv=Dg.exports;(function(e){const t=Hn,n=_v,r=Tv,s=Iv,i=Ov,o=Vt,l=yt,c=Pv;function u(p){return unescape(encodeURIComponent(p)).length}function d(p,m,f){const x=[];let M;for(;(M=p.exec(f))!==null;)x.push({data:M[0],index:M.index,mode:m,length:M[0].length});return x}function h(p){const m=d(o.NUMERIC,t.NUMERIC,p),f=d(o.ALPHANUMERIC,t.ALPHANUMERIC,p);let x,M;return l.isKanjiModeEnabled()?(x=d(o.BYTE,t.BYTE,p),M=d(o.KANJI,t.KANJI,p)):(x=d(o.BYTE_KANJI,t.BYTE,p),M=[]),m.concat(f,x,M).sort(function(v,b){return v.index-b.index}).map(function(v){return{data:v.data,mode:v.mode,length:v.length}})}function y(p,m){switch(m){case t.NUMERIC:return n.getBitsLength(p);case t.ALPHANUMERIC:return r.getBitsLength(p);case t.KANJI:return i.getBitsLength(p);case t.BYTE:return s.getBitsLength(p)}}function g(p){return p.reduce(function(m,f){const x=m.length-1>=0?m[m.length-1]:null;return x&&x.mode===f.mode?(m[m.length-1].data+=f.data,m):(m.push(f),m)},[])}function S(p){const m=[];for(let f=0;f<p.length;f++){const x=p[f];switch(x.mode){case t.NUMERIC:m.push([x,{data:x.data,mode:t.ALPHANUMERIC,length:x.length},{data:x.data,mode:t.BYTE,length:x.length}]);break;case t.ALPHANUMERIC:m.push([x,{data:x.data,mode:t.BYTE,length:x.length}]);break;case t.KANJI:m.push([x,{data:x.data,mode:t.BYTE,length:u(x.data)}]);break;case t.BYTE:m.push([{data:x.data,mode:t.BYTE,length:u(x.data)}])}}return m}function N(p,m){const f={},x={start:{}};let M=["start"];for(let w=0;w<p.length;w++){const v=p[w],b=[];for(let T=0;T<v.length;T++){const C=v[T],O=""+w+T;b.push(O),f[O]={node:C,lastCount:0},x[O]={};for(let D=0;D<M.length;D++){const I=M[D];f[I]&&f[I].node.mode===C.mode?(x[I][O]=y(f[I].lastCount+C.length,C.mode)-y(f[I].lastCount,C.mode),f[I].lastCount+=C.length):(f[I]&&(f[I].lastCount=C.length),x[I][O]=y(C.length,C.mode)+4+t.getCharCountIndicator(C.mode,m))}}M=b}for(let w=0;w<M.length;w++)x[M[w]].end=0;return{map:x,table:f}}function A(p,m){let f;const x=t.getBestModeForData(p);if(f=t.from(m,x),f!==t.BYTE&&f.bit<x.bit)throw new Error('"'+p+'" cannot be encoded with mode '+t.toString(f)+`.
 Suggested mode is: `+t.toString(x));switch(f===t.KANJI&&!l.isKanjiModeEnabled()&&(f=t.BYTE),f){case t.NUMERIC:return new n(p);case t.ALPHANUMERIC:return new r(p);case t.KANJI:return new i(p);case t.BYTE:return new s(p)}}e.fromArray=function(m){return m.reduce(function(f,x){return typeof x=="string"?f.push(A(x,null)):x.data&&f.push(A(x.data,x.mode)),f},[])},e.fromString=function(m,f){const x=h(m,l.isKanjiModeEnabled()),M=S(x),w=N(M,f),v=c.find_path(w.map,"start","end"),b=[];for(let T=1;T<v.length-1;T++)b.push(w.table[v[T]].node);return e.fromArray(g(b))},e.rawSplit=function(m){return e.fromArray(h(m,l.isKanjiModeEnabled()))}})(Rg);const ml=yt,Vl=dl,Rv=gv,Dv=yv,zv=_g,Wv=Ag,au=Tg,ou=fl,Bv=vv,Ro=kg,Uv=Og,Fv=Hn,Ql=Rg;function Gv(e,t){const n=e.size,r=Wv.getPositions(t);for(let s=0;s<r.length;s++){const i=r[s][0],o=r[s][1];for(let l=-1;l<=7;l++)if(!(i+l<=-1||n<=i+l))for(let c=-1;c<=7;c++)o+c<=-1||n<=o+c||(l>=0&&l<=6&&(c===0||c===6)||c>=0&&c<=6&&(l===0||l===6)||l>=2&&l<=4&&c>=2&&c<=4?e.set(i+l,o+c,!0,!0):e.set(i+l,o+c,!1,!0))}}function $v(e){const t=e.size;for(let n=8;n<t-8;n++){const r=n%2===0;e.set(n,6,r,!0),e.set(6,n,r,!0)}}function Hv(e,t){const n=zv.getPositions(t);for(let r=0;r<n.length;r++){const s=n[r][0],i=n[r][1];for(let o=-2;o<=2;o++)for(let l=-2;l<=2;l++)o===-2||o===2||l===-2||l===2||o===0&&l===0?e.set(s+o,i+l,!0,!0):e.set(s+o,i+l,!1,!0)}}function Yv(e,t){const n=e.size,r=Ro.getEncodedBits(t);let s,i,o;for(let l=0;l<18;l++)s=Math.floor(l/3),i=l%3+n-8-3,o=(r>>l&1)===1,e.set(s,i,o,!0),e.set(i,s,o,!0)}function Zl(e,t,n){const r=e.size,s=Uv.getEncodedBits(t,n);let i,o;for(i=0;i<15;i++)o=(s>>i&1)===1,i<6?e.set(i,8,o,!0):i<8?e.set(i+1,8,o,!0):e.set(r-15+i,8,o,!0),i<8?e.set(8,r-i-1,o,!0):i<9?e.set(8,15-i-1+1,o,!0):e.set(8,15-i-1,o,!0);e.set(r-8,8,1,!0)}function Kv(e,t){const n=e.size;let r=-1,s=n-1,i=7,o=0;for(let l=n-1;l>0;l-=2)for(l===6&&l--;;){for(let c=0;c<2;c++)if(!e.isReserved(s,l-c)){let u=!1;o<t.length&&(u=(t[o]>>>i&1)===1),e.set(s,l-c,u),i--,i===-1&&(o++,i=7)}if(s+=r,s<0||n<=s){s-=r,r=-r;break}}}function Vv(e,t,n){const r=new Rv;n.forEach(function(c){r.put(c.mode.bit,4),r.put(c.getLength(),Fv.getCharCountIndicator(c.mode,e)),c.write(r)});const s=ml.getSymbolTotalCodewords(e),i=ou.getTotalCodewordsCount(e,t),o=(s-i)*8;for(r.getLengthInBits()+4<=o&&r.put(0,4);r.getLengthInBits()%8!==0;)r.putBit(0);const l=(o-r.getLengthInBits())/8;for(let c=0;c<l;c++)r.put(c%2?17:236,8);return Qv(r,e,t)}function Qv(e,t,n){const r=ml.getSymbolTotalCodewords(t),s=ou.getTotalCodewordsCount(t,n),i=r-s,o=ou.getBlocksCount(t,n),l=r%o,c=o-l,u=Math.floor(r/o),d=Math.floor(i/o),h=d+1,y=u-d,g=new Bv(y);let S=0;const N=new Array(o),A=new Array(o);let p=0;const m=new Uint8Array(e.buffer);for(let v=0;v<o;v++){const b=v<c?d:h;N[v]=m.slice(S,S+b),A[v]=g.encode(N[v]),S+=b,p=Math.max(p,b)}const f=new Uint8Array(r);let x=0,M,w;for(M=0;M<p;M++)for(w=0;w<o;w++)M<N[w].length&&(f[x++]=N[w][M]);for(M=0;M<y;M++)for(w=0;w<o;w++)f[x++]=A[w][M];return f}function Zv(e,t,n,r){let s;if(Array.isArray(e))s=Ql.fromArray(e);else if(typeof e=="string"){let u=t;if(!u){const d=Ql.rawSplit(e);u=Ro.getBestVersionForData(d,n)}s=Ql.fromString(e,u||40)}else throw new Error("Invalid data");const i=Ro.getBestVersionForData(s,n);if(!i)throw new Error("The amount of data is too big to be stored in a QR Code");if(!t)t=i;else if(t<i)throw new Error(`
The chosen QR Code version cannot contain this amount of data.
Minimum version required to store current data is: `+i+`.
`);const o=Vv(t,n,s),l=ml.getSymbolSize(t),c=new Dv(l);return Gv(c,t),$v(c),Hv(c,t),Zl(c,n,0),t>=7&&Yv(c,t),Kv(c,o),isNaN(r)&&(r=au.getBestMask(c,Zl.bind(null,c,n))),au.applyMask(r,c),Zl(c,n,r),{modules:c,version:t,errorCorrectionLevel:n,maskPattern:r,segments:s}}bg.create=function(t,n){if(typeof t>"u"||t==="")throw new Error("No input text");let r=Vl.M,s,i;return typeof n<"u"&&(r=Vl.from(n.errorCorrectionLevel,Vl.M),s=Ro.from(n.version),i=au.from(n.maskPattern),n.toSJISFunc&&ml.setToSJISFunction(n.toSJISFunc)),Zv(t,s,r,i)};var zg={},nf={};(function(e){function t(n){if(typeof n=="number"&&(n=n.toString()),typeof n!="string")throw new Error("Color should be defined as hex string");let r=n.slice().replace("#","").split("");if(r.length<3||r.length===5||r.length>8)throw new Error("Invalid hex color: "+n);(r.length===3||r.length===4)&&(r=Array.prototype.concat.apply([],r.map(function(i){return[i,i]}))),r.length===6&&r.push("F","F");const s=parseInt(r.join(""),16);return{r:s>>24&255,g:s>>16&255,b:s>>8&255,a:s&255,hex:"#"+r.slice(0,6).join("")}}e.getOptions=function(r){r||(r={}),r.color||(r.color={});const s=typeof r.margin>"u"||r.margin===null||r.margin<0?4:r.margin,i=r.width&&r.width>=21?r.width:void 0,o=r.scale||4;return{width:i,scale:i?4:o,margin:s,color:{dark:t(r.color.dark||"#000000ff"),light:t(r.color.light||"#ffffffff")},type:r.type,rendererOpts:r.rendererOpts||{}}},e.getScale=function(r,s){return s.width&&s.width>=r+s.margin*2?s.width/(r+s.margin*2):s.scale},e.getImageWidth=function(r,s){const i=e.getScale(r,s);return Math.floor((r+s.margin*2)*i)},e.qrToImageData=function(r,s,i){const o=s.modules.size,l=s.modules.data,c=e.getScale(o,i),u=Math.floor((o+i.margin*2)*c),d=i.margin*c,h=[i.color.light,i.color.dark];for(let y=0;y<u;y++)for(let g=0;g<u;g++){let S=(y*u+g)*4,N=i.color.light;if(y>=d&&g>=d&&y<u-d&&g<u-d){const A=Math.floor((y-d)/c),p=Math.floor((g-d)/c);N=h[l[A*o+p]?1:0]}r[S++]=N.r,r[S++]=N.g,r[S++]=N.b,r[S]=N.a}}})(nf);(function(e){const t=nf;function n(s,i,o){s.clearRect(0,0,i.width,i.height),i.style||(i.style={}),i.height=o,i.width=o,i.style.height=o+"px",i.style.width=o+"px"}function r(){try{return document.createElement("canvas")}catch{throw new Error("You need to specify a canvas element")}}e.render=function(i,o,l){let c=l,u=o;typeof c>"u"&&(!o||!o.getContext)&&(c=o,o=void 0),o||(u=r()),c=t.getOptions(c);const d=t.getImageWidth(i.modules.size,c),h=u.getContext("2d"),y=h.createImageData(d,d);return t.qrToImageData(y.data,i,c),n(h,u,d),h.putImageData(y,0,0),u},e.renderToDataURL=function(i,o,l){let c=l;typeof c>"u"&&(!o||!o.getContext)&&(c=o,o=void 0),c||(c={});const u=e.render(i,o,c),d=c.type||"image/png",h=c.rendererOpts||{};return u.toDataURL(d,h.quality)}})(zg);var Wg={};const Jv=nf;function kh(e,t){const n=e.a/255,r=t+'="'+e.hex+'"';return n<1?r+" "+t+'-opacity="'+n.toFixed(2).slice(1)+'"':r}function Jl(e,t,n){let r=e+t;return typeof n<"u"&&(r+=" "+n),r}function qv(e,t,n){let r="",s=0,i=!1,o=0;for(let l=0;l<e.length;l++){const c=Math.floor(l%t),u=Math.floor(l/t);!c&&!i&&(i=!0),e[l]?(o++,l>0&&c>0&&e[l-1]||(r+=i?Jl("M",c+n,.5+u+n):Jl("m",s,0),s=0,i=!1),c+1<t&&e[l+1]||(r+=Jl("h",o),o=0)):s++}return r}Wg.render=function(t,n,r){const s=Jv.getOptions(n),i=t.modules.size,o=t.modules.data,l=i+s.margin*2,c=s.color.light.a?"<path "+kh(s.color.light,"fill")+' d="M0 0h'+l+"v"+l+'H0z"/>':"",u="<path "+kh(s.color.dark,"stroke")+' d="'+qv(o,i,s.margin)+'"/>',d='viewBox="0 0 '+l+" "+l+'"',y='<svg xmlns="http://www.w3.org/2000/svg" '+(s.width?'width="'+s.width+'" height="'+s.width+'" ':"")+d+' shape-rendering="crispEdges">'+c+u+`</svg>
`;return typeof r=="function"&&r(null,y),y};const Xv=mv,lu=bg,Bg=zg,ex=Wg;function rf(e,t,n,r,s){const i=[].slice.call(arguments,1),o=i.length,l=typeof i[o-1]=="function";if(!l&&!Xv())throw new Error("Callback required as last argument");if(l){if(o<2)throw new Error("Too few arguments provided");o===2?(s=n,n=t,t=r=void 0):o===3&&(t.getContext&&typeof s>"u"?(s=r,r=void 0):(s=r,r=n,n=t,t=void 0))}else{if(o<1)throw new Error("Too few arguments provided");return o===1?(n=t,t=r=void 0):o===2&&!t.getContext&&(r=n,n=t,t=void 0),new Promise(function(c,u){try{const d=lu.create(n,r);c(e(d,t,r))}catch(d){u(d)}})}try{const c=lu.create(n,r);s(null,e(c,t,r))}catch(c){s(c)}}Gi.create=lu.create;Gi.toCanvas=rf.bind(null,Bg.render);Gi.toDataURL=rf.bind(null,Bg.renderToDataURL);Gi.toString=rf.bind(null,function(e,t,n){return ex.render(e,n)});const tx=xs(lv);var rs={};(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.SOLANA_CHAINS=e.SOLANA_LOCALNET_CHAIN=e.SOLANA_TESTNET_CHAIN=e.SOLANA_DEVNET_CHAIN=e.SOLANA_MAINNET_CHAIN=void 0,e.isSolanaChain=t,e.SOLANA_MAINNET_CHAIN="solana:mainnet",e.SOLANA_DEVNET_CHAIN="solana:devnet",e.SOLANA_TESTNET_CHAIN="solana:testnet",e.SOLANA_LOCALNET_CHAIN="solana:localnet",e.SOLANA_CHAINS=[e.SOLANA_MAINNET_CHAIN,e.SOLANA_DEVNET_CHAIN,e.SOLANA_TESTNET_CHAIN,e.SOLANA_LOCALNET_CHAIN];function t(n){return e.SOLANA_CHAINS.includes(n)}})(rs);var Qt={},Vr={},sf={};Object.defineProperty(sf,"__esModule",{value:!0});sf.getCommitment=nx;function nx(e){switch(e){case"processed":case"confirmed":case"finalized":case void 0:return e;case"recent":return"processed";case"single":case"singleGossip":return"confirmed";case"max":case"root":return"finalized";default:return}}var Ug={};(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.LOCALNET_ENDPOINT=e.TESTNET_ENDPOINT=e.DEVNET_ENDPOINT=e.MAINNET_ENDPOINT=void 0,e.getChainForEndpoint=n,e.getEndpointForChain=r;const t=rs;e.MAINNET_ENDPOINT="https://api.mainnet-beta.solana.com",e.DEVNET_ENDPOINT="https://api.devnet.solana.com",e.TESTNET_ENDPOINT="https://api.testnet.solana.com",e.LOCALNET_ENDPOINT="http://localhost:8899";function n(s){return s.includes(e.MAINNET_ENDPOINT)?t.SOLANA_MAINNET_CHAIN:/\bdevnet\b/i.test(s)?t.SOLANA_DEVNET_CHAIN:/\btestnet\b/i.test(s)?t.SOLANA_TESTNET_CHAIN:/\blocalhost\b/i.test(s)||/\b127\.0\.0\.1\b/.test(s)?t.SOLANA_LOCALNET_CHAIN:t.SOLANA_MAINNET_CHAIN}function r(s,i){return i||(s===t.SOLANA_MAINNET_CHAIN?e.MAINNET_ENDPOINT:s===t.SOLANA_DEVNET_CHAIN?e.DEVNET_ENDPOINT:s===t.SOLANA_TESTNET_CHAIN?e.TESTNET_ENDPOINT:s===t.SOLANA_LOCALNET_CHAIN?e.LOCALNET_ENDPOINT:e.MAINNET_ENDPOINT)}})(Ug);var mn={},Hi={};const rx=xs(Ey);var Yi={};Object.defineProperty(Yi,"__esModule",{value:!0});Yi.arraysEqual=Fg;Yi.bytesEqual=sx;function Fg(e,t){if(e===t)return!0;const n=e.length;if(n!==t.length)return!1;for(let r=0;r<n;r++)if(e[r]!==t[r])return!1;return!0}function sx(e,t){return Fg(e,t)}Object.defineProperty(Hi,"__esModule",{value:!0});Hi.verifyMessageSignature=Gg;Hi.verifySignMessage=ox;const ix=rx,ax=Yi;function Gg({message:e,signedMessage:t,signature:n,publicKey:r}){return(0,ax.bytesEqual)(e,t)&&ix.ed25519.verify(n,t,r)}function ox(e,t){const{message:n,account:{publicKey:r}}=e,{signedMessage:s,signature:i}=t;return Gg({message:n,signedMessage:s,signature:i,publicKey:r})}Object.defineProperty(mn,"__esModule",{value:!0});mn.verifySignIn=ux;mn.deriveSignInMessage=$g;mn.deriveSignInMessageText=Hg;mn.parseSignInMessage=Yg;mn.parseSignInMessageText=Kg;mn.createSignInMessage=bx;mn.createSignInMessageText=af;const lx=Hi,cx=Yi;function ux(e,t){const{signedMessage:n,signature:r,account:{publicKey:s}}=t,i=$g(e,t);return!!i&&(0,lx.verifyMessageSignature)({message:i,signedMessage:n,signature:r,publicKey:s})}function $g(e,t){const n=Hg(e,t);return n?new TextEncoder().encode(n):null}function Hg(e,t){const n=Yg(t.signedMessage);if(!n||e.domain&&e.domain!==n.domain||e.address&&e.address!==n.address||e.statement!==n.statement||e.uri!==n.uri||e.version!==n.version||e.chainId!==n.chainId||e.nonce!==n.nonce||e.issuedAt!==n.issuedAt||e.expirationTime!==n.expirationTime||e.notBefore!==n.notBefore||e.requestId!==n.requestId)return null;if(e.resources){if(!n.resources||!(0,cx.arraysEqual)(e.resources,n.resources))return null}else if(n.resources)return null;return af(n)}function Yg(e){const t=new TextDecoder().decode(e);return Kg(t)}const dx="(?<domain>[^\\n]+?) wants you to sign in with your Solana account:\\n",fx="(?<address>[^\\n]+)(?:\\n|$)",hx="(?:\\n(?<statement>[\\S\\s]*?)(?:\\n|$))??",mx="(?:\\nURI: (?<uri>[^\\n]+))?",px="(?:\\nVersion: (?<version>[^\\n]+))?",gx="(?:\\nChain ID: (?<chainId>[^\\n]+))?",yx="(?:\\nNonce: (?<nonce>[^\\n]+))?",wx="(?:\\nIssued At: (?<issuedAt>[^\\n]+))?",vx="(?:\\nExpiration Time: (?<expirationTime>[^\\n]+))?",xx="(?:\\nNot Before: (?<notBefore>[^\\n]+))?",Sx="(?:\\nRequest ID: (?<requestId>[^\\n]+))?",Nx="(?:\\nResources:(?<resources>(?:\\n- [^\\n]+)*))?",Ex=`${mx}${px}${gx}${yx}${wx}${vx}${xx}${Sx}${Nx}`,Mx=new RegExp(`^${dx}${fx}${hx}${Ex}\\n*$`);function Kg(e){var t;const n=Mx.exec(e);if(!n)return null;const r=n.groups;return r?{domain:r.domain,address:r.address,statement:r.statement,uri:r.uri,version:r.version,nonce:r.nonce,chainId:r.chainId,issuedAt:r.issuedAt,expirationTime:r.expirationTime,notBefore:r.notBefore,requestId:r.requestId,resources:(t=r.resources)===null||t===void 0?void 0:t.split(`
- `).slice(1)}:null}function bx(e){const t=af(e);return new TextEncoder().encode(t)}function af(e){let t=`${e.domain} wants you to sign in with your Solana account:
`;t+=`${e.address}`,e.statement&&(t+=`

${e.statement}`);const n=[];if(e.uri&&n.push(`URI: ${e.uri}`),e.version&&n.push(`Version: ${e.version}`),e.chainId&&n.push(`Chain ID: ${e.chainId}`),e.nonce&&n.push(`Nonce: ${e.nonce}`),e.issuedAt&&n.push(`Issued At: ${e.issuedAt}`),e.expirationTime&&n.push(`Expiration Time: ${e.expirationTime}`),e.notBefore&&n.push(`Not Before: ${e.notBefore}`),e.requestId&&n.push(`Request ID: ${e.requestId}`),e.resources){n.push("Resources:");for(const r of e.resources)n.push(`- ${r}`)}return n.length&&(t+=`

${n.join(`
`)}`),t}(function(e){var t=Vi&&Vi.__createBinding||(Object.create?function(r,s,i,o){o===void 0&&(o=i);var l=Object.getOwnPropertyDescriptor(s,i);(!l||("get"in l?!s.__esModule:l.writable||l.configurable))&&(l={enumerable:!0,get:function(){return s[i]}}),Object.defineProperty(r,o,l)}:function(r,s,i,o){o===void 0&&(o=i),r[o]=s[i]}),n=Vi&&Vi.__exportStar||function(r,s){for(var i in r)i!=="default"&&!Object.prototype.hasOwnProperty.call(s,i)&&t(s,r,i)};Object.defineProperty(e,"__esModule",{value:!0}),n(sf,e),n(Ug,e),n(mn,e),n(Hi,e)})(Vr);Object.defineProperty(Qt,"__esModule",{value:!0});var jx=Vr;const ft={ERROR_ASSOCIATION_PORT_OUT_OF_RANGE:"ERROR_ASSOCIATION_PORT_OUT_OF_RANGE",ERROR_REFLECTOR_ID_OUT_OF_RANGE:"ERROR_REFLECTOR_ID_OUT_OF_RANGE",ERROR_FORBIDDEN_WALLET_BASE_URL:"ERROR_FORBIDDEN_WALLET_BASE_URL",ERROR_SECURE_CONTEXT_REQUIRED:"ERROR_SECURE_CONTEXT_REQUIRED",ERROR_SESSION_CLOSED:"ERROR_SESSION_CLOSED",ERROR_SESSION_TIMEOUT:"ERROR_SESSION_TIMEOUT",ERROR_WALLET_NOT_FOUND:"ERROR_WALLET_NOT_FOUND",ERROR_INVALID_PROTOCOL_VERSION:"ERROR_INVALID_PROTOCOL_VERSION",ERROR_BROWSER_NOT_SUPPORTED:"ERROR_BROWSER_NOT_SUPPORTED"};class ht extends Error{constructor(...t){const[n,r,s]=t;super(r),this.code=n,this.data=s,this.name="SolanaMobileWalletAdapterError"}}const _x={ERROR_AUTHORIZATION_FAILED:-1,ERROR_INVALID_PAYLOADS:-2,ERROR_NOT_SIGNED:-3,ERROR_NOT_SUBMITTED:-4,ERROR_TOO_MANY_PAYLOADS:-5,ERROR_ATTEST_ORIGIN_ANDROID:-100};class pl extends Error{constructor(...t){const[n,r,s,i]=t;super(s),this.code=r,this.data=i,this.jsonRpcMessageId=n,this.name="SolanaMobileWalletAdapterProtocolError"}}function le(e,t,n,r){function s(i){return i instanceof n?i:new n(function(o){o(i)})}return new(n||(n=Promise))(function(i,o){function l(d){try{u(r.next(d))}catch(h){o(h)}}function c(d){try{u(r.throw(d))}catch(h){o(h)}}function u(d){d.done?i(d.value):s(d.value).then(l,c)}u((r=r.apply(e,[])).next())})}function Ax(e){return window.btoa(e)}function cu(e,t){const n=window.btoa(String.fromCharCode.call(null,...e));return t?n.replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,""):n}function Tx(e){return new Uint8Array(window.atob(e).split("").map(t=>t.charCodeAt(0)))}function Ia(e,t){return le(this,void 0,void 0,function*(){const n=yield crypto.subtle.exportKey("raw",e),r=yield crypto.subtle.sign({hash:"SHA-256",name:"ECDSA"},t,n),s=new Uint8Array(n.byteLength+r.byteLength);return s.set(new Uint8Array(n),0),s.set(new Uint8Array(r),n.byteLength),s})}function Cx(e){return jx.createSignInMessageText(e)}function Ix(e){return Ax(Cx(e))}const Vg="solana:signTransactions",uu="solana:cloneAuthorization",kx="solana:signInWithSolana";function Qg(e,t){return new Proxy({},{get(n,r){return r==="then"?null:(n[r]==null&&(n[r]=function(s){return le(this,void 0,void 0,function*(){const{method:i,params:o}=Lx(r,s,e),l=yield t(i,o);return i==="authorize"&&o.sign_in_payload&&!l.sign_in_result&&(l.sign_in_result=yield Px(o.sign_in_payload,l,t)),Ox(r,l,e)})}),n[r])},defineProperty(){return!1},deleteProperty(){return!1}})}function Lx(e,t,n){let r=t,s=e.toString().replace(/[A-Z]/g,i=>`_${i.toLowerCase()}`).toLowerCase();switch(e){case"authorize":{let{chain:i}=r;if(n==="legacy"){switch(i){case"solana:testnet":{i="testnet";break}case"solana:devnet":{i="devnet";break}case"solana:mainnet":{i="mainnet-beta";break}default:i=r.cluster}r.cluster=i}else{switch(i){case"testnet":case"devnet":{i=`solana:${i}`;break}case"mainnet-beta":{i="solana:mainnet";break}}r.chain=i}}case"reauthorize":{const{auth_token:i,identity:o}=r;if(i)switch(n){case"legacy":{s="reauthorize",r={auth_token:i,identity:o};break}default:{s="authorize";break}}break}}return{method:s,params:r}}function Ox(e,t,n){switch(e){case"getCapabilities":{const r=t;switch(n){case"legacy":{const s=[Vg];return r.supports_clone_authorization===!0&&s.push(uu),Object.assign(Object.assign({},r),{features:s})}case"v1":return Object.assign(Object.assign({},r),{supports_sign_and_send_transactions:!0,supports_clone_authorization:r.features.includes(uu)})}}}return t}function Px(e,t,n){var r;return le(this,void 0,void 0,function*(){const s=(r=e.domain)!==null&&r!==void 0?r:window.location.host,i=t.accounts[0].address,o=Ix(Object.assign(Object.assign({},e),{domain:s,address:i})),l=yield n("sign_messages",{addresses:[i],payloads:[o]});return{address:i,signed_message:o,signature:l.signed_payloads[0].slice(o.length)}})}const sn=4;function Rx(e){if(e>=**********)throw new Error("Outbound sequence number overflow. The maximum sequence number is 32-bytes.");const t=new ArrayBuffer(sn);return new DataView(t).setUint32(0,e,!1),new Uint8Array(t)}const du=12,of=65;function Dx(e,t,n){return le(this,void 0,void 0,function*(){const r=Rx(t),s=new Uint8Array(du);crypto.getRandomValues(s);const i=yield crypto.subtle.encrypt(Jg(r,s),n,new TextEncoder().encode(e)),o=new Uint8Array(r.byteLength+s.byteLength+i.byteLength);return o.set(new Uint8Array(r),0),o.set(new Uint8Array(s),r.byteLength),o.set(new Uint8Array(i),r.byteLength+s.byteLength),o})}function Zg(e,t){return le(this,void 0,void 0,function*(){const n=e.slice(0,sn),r=e.slice(sn,sn+du),s=e.slice(sn+du),i=yield crypto.subtle.decrypt(Jg(n,r),t,s);return zx().decode(i)})}function Jg(e,t){return{additionalData:e,iv:t,name:"AES-GCM",tagLength:128}}let ql;function zx(){return ql===void 0&&(ql=new TextDecoder("utf-8")),ql}function qg(){return le(this,void 0,void 0,function*(){return yield crypto.subtle.generateKey({name:"ECDSA",namedCurve:"P-256"},!1,["sign"])})}function ka(){return le(this,void 0,void 0,function*(){return yield crypto.subtle.generateKey({name:"ECDH",namedCurve:"P-256"},!1,["deriveKey","deriveBits"])})}function Xg(e){let t="";const n=new Uint8Array(e),r=n.byteLength;for(let s=0;s<r;s++)t+=String.fromCharCode(n[s]);return window.btoa(t)}function Wx(){return e0(49152+Math.floor(Math.random()*16384))}function e0(e){if(e<49152||e>65535)throw new ht(ft.ERROR_ASSOCIATION_PORT_OUT_OF_RANGE,`Association port number must be between 49152 and 65535. ${e} given.`,{port:e});return e}function t0(e){return e.replace(/[/+=]/g,t=>({"/":"_","+":"-","=":"."})[t])}const Bx="solana-wallet";function Lh(e){return e.replace(/(^\/+|\/+$)/g,"").split("/")}function n0(e,t){let n=null;if(t){try{n=new URL(t)}catch{}if((n==null?void 0:n.protocol)!=="https:")throw new ht(ft.ERROR_FORBIDDEN_WALLET_BASE_URL,"Base URLs supplied by wallets must be valid `https` URLs")}n||(n=new URL(`${Bx}:/`));const r=e.startsWith("/")?e:[...Lh(n.pathname),...Lh(e)].join("/");return new URL(r,n)}function Ux(e,t,n,r=["v1"]){return le(this,void 0,void 0,function*(){const s=e0(t),i=yield crypto.subtle.exportKey("raw",e),o=Xg(i),l=n0("v1/associate/local",n);return l.searchParams.set("association",t0(o)),l.searchParams.set("port",`${s}`),r.forEach(c=>{l.searchParams.set("v",c)}),l})}function Fx(e,t,n,r,s=["v1"]){return le(this,void 0,void 0,function*(){const i=yield crypto.subtle.exportKey("raw",e),o=Xg(i),l=n0("v1/associate/remote",r);return l.searchParams.set("association",t0(o)),l.searchParams.set("reflector",`${t}`),l.searchParams.set("id",`${cu(n,!0)}`),s.forEach(c=>{l.searchParams.set("v",c)}),l})}function r0(e,t){return le(this,void 0,void 0,function*(){const n=JSON.stringify(e),r=e.id;return Dx(n,r,t)})}function s0(e,t){return le(this,void 0,void 0,function*(){const n=yield Zg(e,t),r=JSON.parse(n);if(Object.hasOwnProperty.call(r,"error"))throw new pl(r.id,r.error.code,r.error.message);return r})}function i0(e,t,n){return le(this,void 0,void 0,function*(){const[r,s]=yield Promise.all([crypto.subtle.exportKey("raw",t),crypto.subtle.importKey("raw",e.slice(0,of),{name:"ECDH",namedCurve:"P-256"},!1,[])]),i=yield crypto.subtle.deriveBits({name:"ECDH",public:s},n,256),o=yield crypto.subtle.importKey("raw",i,"HKDF",!1,["deriveKey"]);return yield crypto.subtle.deriveKey({name:"HKDF",hash:"SHA-256",salt:new Uint8Array(r),info:new Uint8Array},o,{name:"AES-GCM",length:128},!1,["encrypt","decrypt"])})}function a0(e,t){return le(this,void 0,void 0,function*(){const n=yield Zg(e,t),r=JSON.parse(n);let s="legacy";if(Object.hasOwnProperty.call(r,"v"))switch(r.v){case 1:case"1":case"v1":s="v1";break;case"legacy":s="legacy";break;default:throw new ht(ft.ERROR_INVALID_PROTOCOL_VERSION,`Unknown/unsupported protocol version: ${r.v}`)}return{protocol_version:s}})}const Do={Firefox:0,Other:1};function Gx(){return navigator.userAgent.indexOf("Firefox/")!==-1?Do.Firefox:Do.Other}function $x(){return new Promise((e,t)=>{function n(){clearTimeout(s),window.removeEventListener("blur",r)}function r(){n(),e()}window.addEventListener("blur",r);const s=setTimeout(()=>{n(),t()},3e3)})}let Ps=null;function Hx(e){Ps==null&&(Ps=document.createElement("iframe"),Ps.style.display="none",document.body.appendChild(Ps)),Ps.contentWindow.location.href=e.toString()}function Yx(e){return le(this,void 0,void 0,function*(){if(e.protocol==="https:")window.location.assign(e);else try{const t=Gx();switch(t){case Do.Firefox:Hx(e);break;case Do.Other:{const n=$x();window.location.assign(e),yield n;break}default:}}catch{throw new ht(ft.ERROR_WALLET_NOT_FOUND,"Found no installed wallet that supports the mobile wallet protocol.")}})}function Kx(e,t){return le(this,void 0,void 0,function*(){const n=Wx(),r=yield Ux(e,n,t);return yield Yx(r),n})}const zo={retryDelayScheduleMs:[150,150,200,500,500,750,750,1e3],timeoutMs:3e4},o0="com.solana.mobilewalletadapter.v1",Oh="com.solana.mobilewalletadapter.v1.base64";function l0(){if(typeof window>"u"||window.isSecureContext!==!0)throw new ht(ft.ERROR_SECURE_CONTEXT_REQUIRED,"The mobile wallet adapter protocol must be used in a secure context (`https`).")}function c0(e){let t;try{t=new URL(e)}catch{throw new ht(ft.ERROR_FORBIDDEN_WALLET_BASE_URL,"Invalid base URL supplied by wallet")}if(t.protocol!=="https:")throw new ht(ft.ERROR_FORBIDDEN_WALLET_BASE_URL,"Base URLs supplied by wallets must be valid `https` URLs")}function Wo(e){return new DataView(e).getUint32(0,!1)}function Vx(e){var t=new Uint8Array(e),n=e.byteLength,r=10,s=0,i=0,o;do{if(i>=n||i>r)throw new RangeError("Failed to decode varint");o=t[i++],s|=(o&127)<<7*i}while(o>=128);return{value:s,offset:i}}function Qx(e){let{value:t,offset:n}=Vx(e);return new Uint8Array(e.slice(n,n+t))}function Zx(e,t){return le(this,void 0,void 0,function*(){l0();const n=yield qg(),s=`ws://localhost:${yield Kx(n.publicKey,t==null?void 0:t.baseUri)}/solana-wallet`;let i;const o=(()=>{const d=[...zo.retryDelayScheduleMs];return()=>d.length>1?d.shift():d[0]})();let l=1,c=0,u={__type:"disconnected"};return new Promise((d,h)=>{let y;const g={},S=()=>le(this,void 0,void 0,function*(){if(u.__type!=="connecting"){console.warn(`Expected adapter state to be \`connecting\` at the moment the websocket opens. Got \`${u.__type}\`.`);return}y.removeEventListener("open",S);const{associationKeypair:M}=u,w=yield ka();y.send(yield Ia(w.publicKey,M.privateKey)),u={__type:"hello_req_sent",associationPublicKey:M.publicKey,ecdhPrivateKey:w.privateKey}}),N=M=>{M.wasClean?u={__type:"disconnected"}:h(new ht(ft.ERROR_SESSION_CLOSED,`The wallet session dropped unexpectedly (${M.code}: ${M.reason}).`,{closeEvent:M})),m()},A=M=>le(this,void 0,void 0,function*(){m(),Date.now()-i>=zo.timeoutMs?h(new ht(ft.ERROR_SESSION_TIMEOUT,`Failed to connect to the wallet websocket at ${s}.`)):(yield new Promise(w=>{const v=o();f=window.setTimeout(w,v)}),x())}),p=M=>le(this,void 0,void 0,function*(){const w=yield M.data.arrayBuffer();switch(u.__type){case"connecting":if(w.byteLength!==0)throw new Error("Encountered unexpected message while connecting");const v=yield ka();y.send(yield Ia(v.publicKey,n.privateKey)),u={__type:"hello_req_sent",associationPublicKey:n.publicKey,ecdhPrivateKey:v.privateKey};break;case"connected":try{const b=w.slice(0,sn),T=Wo(b);if(T!==c+1)throw new Error("Encrypted message has invalid sequence number");c=T;const C=yield s0(w,u.sharedSecret),O=g[C.id];delete g[C.id],O.resolve(C.result)}catch(b){if(b instanceof pl){const T=g[b.jsonRpcMessageId];delete g[b.jsonRpcMessageId],T.reject(b)}else throw b}break;case"hello_req_sent":{if(w.byteLength===0){const D=yield ka();y.send(yield Ia(D.publicKey,n.privateKey)),u={__type:"hello_req_sent",associationPublicKey:n.publicKey,ecdhPrivateKey:D.privateKey};break}const b=yield i0(w,u.associationPublicKey,u.ecdhPrivateKey),T=w.slice(of),C=T.byteLength!==0?yield le(this,void 0,void 0,function*(){const D=T.slice(0,sn),I=Wo(D);if(I!==c+1)throw new Error("Encrypted message has invalid sequence number");return c=I,a0(T,b)}):{protocol_version:"legacy"};u={__type:"connected",sharedSecret:b,sessionProperties:C};const O=Qg(C.protocol_version,(D,I)=>le(this,void 0,void 0,function*(){const F=l++;return y.send(yield r0({id:F,jsonrpc:"2.0",method:D,params:I??{}},b)),new Promise((_,R)=>{g[F]={resolve(z){switch(D){case"authorize":case"reauthorize":{const{wallet_uri_base:L}=z;if(L!=null)try{c0(L)}catch(P){R(P);return}break}}_(z)},reject:R}})}));try{d(yield e(O))}catch(D){h(D)}finally{m(),y.close()}break}}});let m,f;const x=()=>{m&&m(),u={__type:"connecting",associationKeypair:n},i===void 0&&(i=Date.now()),y=new WebSocket(s,[o0]),y.addEventListener("open",S),y.addEventListener("close",N),y.addEventListener("error",A),y.addEventListener("message",p),m=()=>{window.clearTimeout(f),y.removeEventListener("open",S),y.removeEventListener("close",N),y.removeEventListener("error",A),y.removeEventListener("message",p)}};x()})})}function Jx(e){return le(this,void 0,void 0,function*(){l0();const t=yield qg(),n=`wss://${e==null?void 0:e.remoteHostAuthority}/reflect`;let r;const s=(()=>{const N=[...zo.retryDelayScheduleMs];return()=>N.length>1?N.shift():N[0]})();let i=1,o=0,l,c={__type:"disconnected"},u,d,h=N=>le(this,void 0,void 0,function*(){if(l=="base64"){const A=yield N.data;return Tx(A).buffer}else return yield N.data.arrayBuffer()});const y=yield new Promise((N,A)=>{const p=()=>le(this,void 0,void 0,function*(){if(c.__type!=="connecting"){console.warn(`Expected adapter state to be \`connecting\` at the moment the websocket opens. Got \`${c.__type}\`.`);return}u.protocol.includes(Oh)?l="base64":l="binary",u.removeEventListener("open",p)}),m=v=>{v.wasClean?c={__type:"disconnected"}:A(new ht(ft.ERROR_SESSION_CLOSED,`The wallet session dropped unexpectedly (${v.code}: ${v.reason}).`,{closeEvent:v})),d()},f=v=>le(this,void 0,void 0,function*(){d(),Date.now()-r>=zo.timeoutMs?A(new ht(ft.ERROR_SESSION_TIMEOUT,`Failed to connect to the wallet websocket at ${n}.`)):(yield new Promise(b=>{const T=s();M=window.setTimeout(b,T)}),w())}),x=v=>le(this,void 0,void 0,function*(){const b=yield h(v);if(c.__type==="connecting"){if(b.byteLength==0)throw new Error("Encountered unexpected message while connecting");const T=Qx(b);c={__type:"reflector_id_received",reflectorId:T};const C=yield Fx(t.publicKey,e.remoteHostAuthority,T,e==null?void 0:e.baseUri);u.removeEventListener("message",x),N(C)}});let M;const w=()=>{d&&d(),c={__type:"connecting",associationKeypair:t},r===void 0&&(r=Date.now()),u=new WebSocket(n,[o0,Oh]),u.addEventListener("open",p),u.addEventListener("close",m),u.addEventListener("error",f),u.addEventListener("message",x),d=()=>{window.clearTimeout(M),u.removeEventListener("open",p),u.removeEventListener("close",m),u.removeEventListener("error",f),u.removeEventListener("message",x)}};w()});let g=!1,S;return{associationUrl:y,close:()=>{u.close(),S()},wallet:new Promise((N,A)=>{const p={},m=f=>le(this,void 0,void 0,function*(){const x=yield h(f);switch(c.__type){case"reflector_id_received":if(x.byteLength!==0)throw new Error("Encountered unexpected message while awaiting reflection");const M=yield ka(),w=yield Ia(M.publicKey,t.privateKey);l=="base64"?u.send(cu(w)):u.send(w),c={__type:"hello_req_sent",associationPublicKey:t.publicKey,ecdhPrivateKey:M.privateKey};break;case"connected":try{const v=x.slice(0,sn),b=Wo(v);if(b!==o+1)throw new Error("Encrypted message has invalid sequence number");o=b;const T=yield s0(x,c.sharedSecret),C=p[T.id];delete p[T.id],C.resolve(T.result)}catch(v){if(v instanceof pl){const b=p[v.jsonRpcMessageId];delete p[v.jsonRpcMessageId],b.reject(v)}else throw v}break;case"hello_req_sent":{const v=yield i0(x,c.associationPublicKey,c.ecdhPrivateKey),b=x.slice(of),T=b.byteLength!==0?yield le(this,void 0,void 0,function*(){const O=b.slice(0,sn),D=Wo(O);if(D!==o+1)throw new Error("Encrypted message has invalid sequence number");return o=D,a0(b,v)}):{protocol_version:"legacy"};c={__type:"connected",sharedSecret:v,sessionProperties:T};const C=Qg(T.protocol_version,(O,D)=>le(this,void 0,void 0,function*(){const I=i++,F=yield r0({id:I,jsonrpc:"2.0",method:O,params:D??{}},v);return l=="base64"?u.send(cu(F)):u.send(F),new Promise((_,R)=>{p[I]={resolve(z){switch(O){case"authorize":case"reauthorize":{const{wallet_uri_base:L}=z;if(L!=null)try{c0(L)}catch(P){R(P);return}break}}_(z)},reject:R}})}));g=!0;try{N(C)}catch(O){A(O)}break}}});u.addEventListener("message",m),S=()=>{u.removeEventListener("message",m),d(),g||A(new ht(ft.ERROR_SESSION_CLOSED,"The wallet session was closed before connection.",{closeEvent:new CloseEvent("socket was closed before connection")}))}})}})}Qt.SolanaCloneAuthorization=uu;Qt.SolanaMobileWalletAdapterError=ht;Qt.SolanaMobileWalletAdapterErrorCode=ft;Qt.SolanaMobileWalletAdapterProtocolError=pl;Qt.SolanaMobileWalletAdapterProtocolErrorCode=_x;Qt.SolanaSignInWithSolana=kx;Qt.SolanaSignTransactions=Vg;var qx=Qt.startRemoteScenario=Jx,Xx=Qt.transact=Zx;function eS(e){if(e.length>=255)throw new TypeError("Alphabet too long");for(var t=new Uint8Array(256),n=0;n<t.length;n++)t[n]=255;for(var r=0;r<e.length;r++){var s=e.charAt(r),i=s.charCodeAt(0);if(t[i]!==255)throw new TypeError(s+" is ambiguous");t[i]=r}var o=e.length,l=e.charAt(0),c=Math.log(o)/Math.log(256),u=Math.log(256)/Math.log(o);function d(g){if(g instanceof Uint8Array||(ArrayBuffer.isView(g)?g=new Uint8Array(g.buffer,g.byteOffset,g.byteLength):Array.isArray(g)&&(g=Uint8Array.from(g))),!(g instanceof Uint8Array))throw new TypeError("Expected Uint8Array");if(g.length===0)return"";for(var S=0,N=0,A=0,p=g.length;A!==p&&g[A]===0;)A++,S++;for(var m=(p-A)*u+1>>>0,f=new Uint8Array(m);A!==p;){for(var x=g[A],M=0,w=m-1;(x!==0||M<N)&&w!==-1;w--,M++)x+=256*f[w]>>>0,f[w]=x%o>>>0,x=x/o>>>0;if(x!==0)throw new Error("Non-zero carry");N=M,A++}for(var v=m-N;v!==m&&f[v]===0;)v++;for(var b=l.repeat(S);v<m;++v)b+=e.charAt(f[v]);return b}function h(g){if(typeof g!="string")throw new TypeError("Expected String");if(g.length===0)return new Uint8Array;for(var S=0,N=0,A=0;g[S]===l;)N++,S++;for(var p=(g.length-S)*c+1>>>0,m=new Uint8Array(p);g[S];){var f=g.charCodeAt(S);if(f>255)return;var x=t[f];if(x===255)return;for(var M=0,w=p-1;(x!==0||M<A)&&w!==-1;w--,M++)x+=o*m[w]>>>0,m[w]=x%256>>>0,x=x/256>>>0;if(x!==0)throw new Error("Non-zero carry");A=M,S++}for(var v=p-A;v!==p&&m[v]===0;)v++;for(var b=new Uint8Array(N+(p-v)),T=N;v!==p;)b[T++]=m[v++];return b}function y(g){var S=h(g);if(S)return S;throw new Error("Non-base"+o+" character")}return{encode:d,decodeUnsafe:h,decode:y}}var tS=eS;const nS=tS,rS="123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz";var u0=nS(rS);const d0=Yo(u0);function Xl(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,r=Object.getOwnPropertySymbols(e);s<r.length;s++)t.indexOf(r[s])<0&&Object.prototype.propertyIsEnumerable.call(e,r[s])&&(n[r[s]]=e[r[s]]);return n}function li(e,t,n,r){function s(i){return i instanceof n?i:new n(function(o){o(i)})}return new(n||(n=Promise))(function(i,o){function l(d){try{u(r.next(d))}catch(h){o(h)}}function c(d){try{u(r.throw(d))}catch(h){o(h)}}function u(d){d.done?i(d.value):s(d.value).then(l,c)}u((r=r.apply(e,[])).next())})}function f0(e){return window.btoa(String.fromCharCode.call(null,...e))}function ec(e){return new Uint8Array(window.atob(e).split("").map(t=>t.charCodeAt(0)))}function Ph(e){const t="version"in e?e.serialize():e.serialize({requireAllSignatures:!1,verifySignatures:!1});return f0(t)}function sS(e){const n=e[0]*by+1;return My.deserializeMessageVersion(e.slice(n,e.length))==="legacy"?wt.from(e):uc.deserialize(e)}function iS(e,t){return li(this,void 0,void 0,function*(){return yield Xx(r=>e(h0(r)),t)})}function aS(e){return li(this,void 0,void 0,function*(){const{wallet:t,close:n,associationUrl:r}=yield qx(e);return{wallet:t.then(i=>h0(i)),close:n,associationUrl:r}})}function h0(e){return new Proxy({},{get(t,n){if(t[n]==null)switch(n){case"signAndSendTransactions":t[n]=function(r){var{minContextSlot:s,commitment:i,skipPreflight:o,maxRetries:l,waitForCommitmentToSendNextTransaction:c,transactions:u}=r,d=Xl(r,["minContextSlot","commitment","skipPreflight","maxRetries","waitForCommitmentToSendNextTransaction","transactions"]);return li(this,void 0,void 0,function*(){const h=u.map(Ph),y={min_context_slot:s,commitment:i,skip_preflight:o,max_retries:l,wait_for_commitment_to_send_next_transaction:c},{signatures:g}=yield e.signAndSendTransactions(Object.assign(Object.assign(Object.assign({},d),Object.values(y).some(N=>N!=null)?{options:y}:null),{payloads:h}));return g.map(ec).map(d0.encode)})};break;case"signMessages":t[n]=function(r){var{payloads:s}=r,i=Xl(r,["payloads"]);return li(this,void 0,void 0,function*(){const o=s.map(f0),{signed_payloads:l}=yield e.signMessages(Object.assign(Object.assign({},i),{payloads:o}));return l.map(ec)})};break;case"signTransactions":t[n]=function(r){var{transactions:s}=r,i=Xl(r,["transactions"]);return li(this,void 0,void 0,function*(){const o=s.map(Ph),{signed_payloads:l}=yield e.signTransactions(Object.assign(Object.assign({},i),{payloads:o}));return l.map(ec).map(sS)})};break;default:{t[n]=e[n];break}}return t[n]},defineProperty(){return!1},deleteProperty(){return!1}})}const oS=Object.freeze(Object.defineProperty({__proto__:null,startRemoteScenario:aS,transact:iS},Symbol.toStringTag,{value:"Module"})),lS=xs(oS);Object.defineProperty(Kt,"__esModule",{value:!0});var Oe=Mg,Li=Eg,cS=Gi,ss=tx,Rh=rs,m0=lS,uS=u0;function p0(e){return e&&typeof e=="object"&&"default"in e?e:{default:e}}var dS=p0(cS),Bo=p0(uS);function Z(e,t,n,r){function s(i){return i instanceof n?i:new n(function(o){o(i)})}return new(n||(n=Promise))(function(i,o){function l(d){try{u(r.next(d))}catch(h){o(h)}}function c(d){try{u(r.throw(d))}catch(h){o(h)}}function u(d){d.done?i(d.value):s(d.value).then(l,c)}u((r=r.apply(e,[])).next())})}function j(e,t,n,r){if(n==="a"&&!r)throw new TypeError("Private accessor was defined without a getter");if(typeof t=="function"?e!==t||!r:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return n==="m"?r:n==="a"?r.call(e):r?r.value:t.get(e)}function X(e,t,n,r,s){if(typeof t=="function"?e!==t||!0:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return t.set(e,n),n}var Bs,je,Qn,jr,g0,y0,w0,La;const fS=`
<div class="mobile-wallet-adapter-embedded-modal-container" role="dialog" aria-modal="true" aria-labelledby="modal-title">
    <div data-modal-close style="position: absolute; width: 100%; height: 100%;"></div>
	<div class="mobile-wallet-adapter-embedded-modal-card">
		<div>
			<button data-modal-close class="mobile-wallet-adapter-embedded-modal-close">
				<svg width="14" height="14">
					<path d="M 6.7125,8.3036995 1.9082,13.108199 c -0.2113,0.2112 -0.4765,0.3168 -0.7957,0.3168 -0.3192,0 -0.5844,-0.1056 -0.7958,-0.3168 C 0.1056,12.896899 0,12.631699 0,12.312499 c 0,-0.3192 0.1056,-0.5844 0.3167,-0.7958 L 5.1212,6.7124995 0.3167,1.9082 C 0.1056,1.6969 0,1.4317 0,1.1125 0,0.7933 0.1056,0.5281 0.3167,0.3167 0.5281,0.1056 0.7933,0 1.1125,0 1.4317,0 1.6969,0.1056 1.9082,0.3167 L 6.7125,5.1212 11.5167,0.3167 C 11.7281,0.1056 11.9933,0 12.3125,0 c 0.3192,0 0.5844,0.1056 0.7957,0.3167 0.2112,0.2114 0.3168,0.4766 0.3168,0.7958 0,0.3192 -0.1056,0.5844 -0.3168,0.7957 L 8.3037001,6.7124995 13.1082,11.516699 c 0.2112,0.2114 0.3168,0.4766 0.3168,0.7958 0,0.3192 -0.1056,0.5844 -0.3168,0.7957 -0.2113,0.2112 -0.4765,0.3168 -0.7957,0.3168 -0.3192,0 -0.5844,-0.1056 -0.7958,-0.3168 z" />
				</svg>
			</button>
		</div>
		<div class="mobile-wallet-adapter-embedded-modal-content"></div>
	</div>
</div>
`,hS=`
.mobile-wallet-adapter-embedded-modal-container {
    display: flex; /* Use flexbox to center content */
    justify-content: center; /* Center horizontally */
    align-items: center; /* Center vertically */
    position: fixed; /* Stay in place */
    z-index: 1; /* Sit on top */
    left: 0;
    top: 0;
    width: 100%; /* Full width */
    height: 100%; /* Full height */
    background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
    overflow-y: auto; /* enable scrolling */
}

.mobile-wallet-adapter-embedded-modal-card {
    display: flex;
    flex-direction: column;
    margin: auto 20px;
    max-width: 780px;
    padding: 20px;
    border-radius: 24px;
    background: #ffffff;
    font-family: "Inter Tight", "PT Sans", Calibri, sans-serif;
    transform: translateY(-200%);
    animation: slide-in 0.5s forwards;
}

@keyframes slide-in {
    100% { transform: translateY(0%); }
}

.mobile-wallet-adapter-embedded-modal-close {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    cursor: pointer;
    background: #e4e9e9;
    border: none;
    border-radius: 50%;
}

.mobile-wallet-adapter-embedded-modal-close:focus-visible {
    outline-color: red;
}

.mobile-wallet-adapter-embedded-modal-close svg {
    fill: #546266;
    transition: fill 200ms ease 0s;
}

.mobile-wallet-adapter-embedded-modal-close:hover svg {
    fill: #fff;
}
`,mS=`
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter+Tight:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet">
`;class v0{constructor(){Bs.add(this),je.set(this,null),Qn.set(this,{}),jr.set(this,!1),this.dom=null,this.open=()=>{console.debug("Modal open"),j(this,Bs,"m",y0).call(this),j(this,je,"f")&&(j(this,je,"f").style.display="flex")},this.close=(t=void 0)=>{var n;console.debug("Modal close"),j(this,Bs,"m",w0).call(this),j(this,je,"f")&&(j(this,je,"f").style.display="none"),(n=j(this,Qn,"f").close)===null||n===void 0||n.forEach(r=>r(t))},La.set(this,t=>{t.key==="Escape"&&this.close(t)}),this.init=this.init.bind(this),X(this,je,document.getElementById("mobile-wallet-adapter-embedded-root-ui"))}init(){return Z(this,void 0,void 0,function*(){console.log("Injecting modal"),j(this,Bs,"m",g0).call(this)})}addEventListener(t,n){var r;return!((r=j(this,Qn,"f")[t])===null||r===void 0)&&r.push(n)||(j(this,Qn,"f")[t]=[n]),()=>this.removeEventListener(t,n)}removeEventListener(t,n){var r;j(this,Qn,"f")[t]=(r=j(this,Qn,"f")[t])===null||r===void 0?void 0:r.filter(s=>n!==s)}}je=new WeakMap,Qn=new WeakMap,jr=new WeakMap,La=new WeakMap,Bs=new WeakSet,g0=function(){if(document.getElementById("mobile-wallet-adapter-embedded-root-ui")){j(this,je,"f")||X(this,je,document.getElementById("mobile-wallet-adapter-embedded-root-ui"));return}X(this,je,document.createElement("div")),j(this,je,"f").id="mobile-wallet-adapter-embedded-root-ui",j(this,je,"f").innerHTML=fS,j(this,je,"f").style.display="none";const t=j(this,je,"f").querySelector(".mobile-wallet-adapter-embedded-modal-content");t&&(t.innerHTML=this.contentHtml);const n=document.createElement("style");n.id="mobile-wallet-adapter-embedded-modal-styles",n.textContent=hS+this.contentStyles;const r=document.createElement("div");r.innerHTML=mS,this.dom=r.attachShadow({mode:"closed"}),this.dom.appendChild(n),this.dom.appendChild(j(this,je,"f")),document.body.appendChild(r)},y0=function(){if(!j(this,je,"f")||j(this,jr,"f"))return;[...j(this,je,"f").querySelectorAll("[data-modal-close]")].forEach(n=>n==null?void 0:n.addEventListener("click",this.close)),window.addEventListener("load",this.close),document.addEventListener("keydown",j(this,La,"f")),X(this,jr,!0)},w0=function(){if(!j(this,jr,"f")||(window.removeEventListener("load",this.close),document.removeEventListener("keydown",j(this,La,"f")),!j(this,je,"f")))return;[...j(this,je,"f").querySelectorAll("[data-modal-close]")].forEach(n=>n==null?void 0:n.removeEventListener("click",this.close)),X(this,jr,!1)};class pS extends v0{constructor(){super(...arguments),this.contentStyles=yS,this.contentHtml=gS}initWithQR(t){const n=Object.create(null,{init:{get:()=>super.init}});return Z(this,void 0,void 0,function*(){n.init.call(this),this.populateQRCode(t)})}populateQRCode(t){var n;return Z(this,void 0,void 0,function*(){const r=(n=this.dom)===null||n===void 0?void 0:n.getElementById("mobile-wallet-adapter-embedded-modal-qr-code-container");if(r){const s=yield dS.default.toCanvas(t,{width:200,margin:0});r.firstElementChild!==null?r.replaceChild(s,r.firstElementChild):r.appendChild(s)}else console.error("QRCode Container not found")})}}const gS=`
<div class="mobile-wallet-adapter-embedded-modal-qr-content">
    <div>
        <svg class="mobile-wallet-adapter-embedded-modal-icon" width="100%" height="100%">
            <circle r="52" cx="53" cy="53" fill="#99b3be" stroke="#000000" stroke-width="2"/>
            <path d="m 53,82.7305 c -3.3116,0 -6.1361,-1.169 -8.4735,-3.507 -2.338,-2.338 -3.507,-5.1625 -3.507,-8.4735 0,-3.3116 1.169,-6.1364 3.507,-8.4744 2.3374,-2.338 5.1619,-3.507 8.4735,-3.507 3.3116,0 6.1361,1.169 8.4735,3.507 2.338,2.338 3.507,5.1628 3.507,8.4744 0,3.311 -1.169,6.1355 -3.507,8.4735 -2.3374,2.338 -5.1619,3.507 -8.4735,3.507 z m 0.007,-5.25 c 1.8532,0 3.437,-0.6598 4.7512,-1.9793 1.3149,-1.3195 1.9723,-2.9058 1.9723,-4.7591 0,-1.8526 -0.6598,-3.4364 -1.9793,-4.7512 -1.3195,-1.3149 -2.9055,-1.9723 -4.7582,-1.9723 -1.8533,0 -3.437,0.6598 -4.7513,1.9793 -1.3148,1.3195 -1.9722,2.9058 -1.9722,4.7591 0,1.8527 0.6597,3.4364 1.9792,4.7512 1.3195,1.3149 2.9056,1.9723 4.7583,1.9723 z m -28,-33.5729 -3.85,-3.6347 c 4.1195,-4.025 8.8792,-7.1984 14.2791,-9.52 5.4005,-2.3223 11.2551,-3.4834 17.5639,-3.4834 6.3087,0 12.1634,1.1611 17.5639,3.4834 5.3999,2.3216 10.1596,5.495 14.2791,9.52 l -3.85,3.6347 C 77.2999,40.358 73.0684,37.5726 68.2985,35.5514 63.5292,33.5301 58.4296,32.5195 53,32.5195 c -5.4297,0 -10.5292,1.0106 -15.2985,3.0319 -4.7699,2.0212 -9.0014,4.8066 -12.6945,8.3562 z m 44.625,10.8771 c -2.2709,-2.1046 -4.7962,-3.7167 -7.5758,-4.8361 -2.7795,-1.12 -5.7983,-1.68 -9.0562,-1.68 -3.2579,0 -6.2621,0.56 -9.0125,1.68 -2.7504,1.1194 -5.2903,2.7315 -7.6195,4.8361 L 32.5189,51.15 c 2.8355,-2.6028 5.9777,-4.6086 9.4263,-6.0174 3.4481,-1.4087 7.133,-2.1131 11.0548,-2.1131 3.9217,0 7.5979,0.7044 11.0285,2.1131 3.43,1.4088 6.5631,3.4146 9.3992,6.0174 z"/>
        </svg>
        <div class="mobile-wallet-adapter-embedded-modal-title">Remote Mobile Wallet Adapter</div>
    </div>
    <div>
        <div>
            <h4 class="mobile-wallet-adapter-embedded-modal-qr-label">
                Open your wallet and scan this code
            </h4>
        </div>
        <div id="mobile-wallet-adapter-embedded-modal-qr-code-container" class="mobile-wallet-adapter-embedded-modal-qr-code-container"></div>
    </div>
</div>
<div class="mobile-wallet-adapter-embedded-modal-divider"><hr></div>
<div class="mobile-wallet-adapter-embedded-modal-footer">
    <div class="mobile-wallet-adapter-embedded-modal-subtitle">
        Follow the instructions on your device. When you're finished, this screen will update.
    </div>
    <div class="mobile-wallet-adapter-embedded-modal-progress-badge">
        <div>
            <div class="spinner">
                <div class="leftWrapper">
                    <div class="left">
                        <div class="circle"></div>
                    </div>
                </div>
                <div class="rightWrapper">
                    <div class="right">
                        <div class="circle"></div>
                    </div>
                </div>
            </div>
        </div>
        <div>Waiting for scan</div>
    </div>
</div>
`,yS=`
.mobile-wallet-adapter-embedded-modal-qr-content {
    display: flex; 
    margin-top: 10px;
    padding: 10px;
}

.mobile-wallet-adapter-embedded-modal-qr-content > div:first-child {
    display: flex;
    flex-direction: column;
    flex: 2;
    margin-top: auto;
    margin-right: 30px;
}

.mobile-wallet-adapter-embedded-modal-qr-content > div:nth-child(2) {
    display: flex;
    flex-direction: column;
    flex: 1;
    margin-left: auto;
}

.mobile-wallet-adapter-embedded-modal-footer {
    display: flex;
    padding: 10px;
}

.mobile-wallet-adapter-embedded-modal-icon {}

.mobile-wallet-adapter-embedded-modal-title {
    color: #000000;
    font-size: 2.5em;
    font-weight: 600;
}

.mobile-wallet-adapter-embedded-modal-qr-label {
    text-align: right;
    color: #000000;
}

.mobile-wallet-adapter-embedded-modal-qr-code-container {
    margin-left: auto;
}

.mobile-wallet-adapter-embedded-modal-divider {
    margin-top: 20px;
    padding-left: 10px;
    padding-right: 10px;
}

.mobile-wallet-adapter-embedded-modal-divider hr {
    border-top: 1px solid #D9DEDE;
}

.mobile-wallet-adapter-embedded-modal-subtitle {
    margin: auto;
    margin-right: 60px;
    padding: 20px;
    color: #6E8286;
}

.mobile-wallet-adapter-embedded-modal-progress-badge {
    display: flex;
    background: #F7F8F8;
    height: 56px;
    min-width: 200px;
    margin: auto;
    padding-left: 20px;
    padding-right: 20px;
    border-radius: 18px;
    color: #A8B6B8;
    align-items: center;
}

.mobile-wallet-adapter-embedded-modal-progress-badge > div:first-child {
    margin-left: auto;
    margin-right: 20px;
}

.mobile-wallet-adapter-embedded-modal-progress-badge > div:nth-child(2) {
    margin-right: auto;
}

/* Smaller screens */
@media all and (max-width: 600px) {
    .mobile-wallet-adapter-embedded-modal-card {
        text-align: center;
    }
    .mobile-wallet-adapter-embedded-modal-qr-content {
        flex-direction: column;
    }
    .mobile-wallet-adapter-embedded-modal-qr-content > div:first-child {
        margin: auto;
    }
    .mobile-wallet-adapter-embedded-modal-qr-content > div:nth-child(2) {
        margin: auto;
        flex: 2 auto;
    }
    .mobile-wallet-adapter-embedded-modal-footer {
        flex-direction: column;
    }
    .mobile-wallet-adapter-embedded-modal-icon {
        display: none;
    }
    .mobile-wallet-adapter-embedded-modal-title {
        font-size: 1.5em;
    }
    .mobile-wallet-adapter-embedded-modal-subtitle {
        margin-right: unset;
    }
    .mobile-wallet-adapter-embedded-modal-qr-label {
        text-align: center;
    }
    .mobile-wallet-adapter-embedded-modal-qr-code-container {
        margin: auto;
    }
}

/* Spinner */
@keyframes spinLeft {
    0% {
        transform: rotate(20deg);
    }
    50% {
        transform: rotate(160deg);
    }
    100% {
        transform: rotate(20deg);
    }
}
@keyframes spinRight {
    0% {
        transform: rotate(160deg);
    }
    50% {
        transform: rotate(20deg);
    }
    100% {
        transform: rotate(160deg);
    }
}
@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(2520deg);
    }
}

.spinner {
    position: relative;
    width: 1.5em;
    height: 1.5em;
    margin: auto;
    animation: spin 10s linear infinite;
}
.spinner::before {
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
}
.right, .rightWrapper, .left, .leftWrapper {
    position: absolute;
    top: 0;
    overflow: hidden;
    width: .75em;
    height: 1.5em;
}
.left, .leftWrapper {
    left: 0;
}
.right {
    left: -12px;
}
.rightWrapper {
    right: 0;
}
.circle {
    border: .125em solid #A8B6B8;
    width: 1.25em; /* 1.5em - 2*0.125em border */
    height: 1.25em; /* 1.5em - 2*0.125em border */
    border-radius: 0.75em; /* 0.5*1.5em spinner size 8 */
}
.left {
    transform-origin: 100% 50%;
    animation: spinLeft 2.5s cubic-bezier(.2,0,.8,1) infinite;
}
.right {
    transform-origin: 100% 50%;
    animation: spinRight 2.5s cubic-bezier(.2,0,.8,1) infinite;
}
`,x0="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik03IDIuNUgxN0MxNy44Mjg0IDIuNSAxOC41IDMuMTcxNTcgMTguNSA0VjIwQzE4LjUgMjAuODI4NCAxNy44Mjg0IDIxLjUgMTcgMjEuNUg3QzYuMTcxNTcgMjEuNSA1LjUgMjAuODI4NCA1LjUgMjBWNEM1LjUgMy4xNzE1NyA2LjE3MTU3IDIuNSA3IDIuNVpNMyA0QzMgMS43OTA4NiA0Ljc5MDg2IDAgNyAwSDE3QzE5LjIwOTEgMCAyMSAxLjc5MDg2IDIxIDRWMjBDMjEgMjIuMjA5MSAxOS4yMDkxIDI0IDE3IDI0SDdDNC43OTA4NiAyNCAzIDIyLjIwOTEgMyAyMFY0Wk0xMSA0LjYxNTM4QzEwLjQ0NzcgNC42MTUzOCAxMCA1LjA2MzEgMTAgNS42MTUzOFY2LjM4NDYyQzEwIDYuOTM2OSAxMC40NDc3IDcuMzg0NjIgMTEgNy4zODQ2MkgxM0MxMy41NTIzIDcuMzg0NjIgMTQgNi45MzY5IDE0IDYuMzg0NjJWNS42MTUzOEMxNCA1LjA2MzEgMTMuNTUyMyA0LjYxNTM4IDEzIDQuNjE1MzhIMTFaIiBmaWxsPSIjRENCOEZGIi8+Cjwvc3ZnPgo=";function S0(e){return"version"in e}function N0(e){return window.btoa(String.fromCharCode.call(null,...e))}function Tn(e){return new Uint8Array(window.atob(e).split("").map(t=>t.charCodeAt(0)))}var _r,ir,fu,hu,mu,pu,Us,Ve,Jt,gn,Ar,Tr,Oa,Fs,Pa,gu,Ra,E0,yu,Da,Cr,wu,Gs,za,Ir,$s,Wa,vu,xu,Ba,Su,Nu,Eu,Ua,kr,ar,Mu,bu,ju,_u,Hs,Qe,yn,wn,Lr,Or,Fa,Ys,Ga,$a,jt,Au,Ha,M0,Tu,Ya,Ks,Cu,Vs,Ka,Pr,Qs,Va,Iu,ku,Qa,Lu,Ou,Pu,Za;const lf="Mobile Wallet Adapter",b0=64,j0=[Oe.SolanaSignAndSendTransaction,Oe.SolanaSignTransaction,Oe.SolanaSignMessage,Oe.SolanaSignIn];class _0{constructor(t){_r.add(this),ir.set(this,{}),fu.set(this,"1.0.0"),hu.set(this,lf),mu.set(this,"https://solanamobile.com/wallets"),pu.set(this,x0),Us.set(this,void 0),Ve.set(this,void 0),Jt.set(this,void 0),gn.set(this,!1),Ar.set(this,0),Tr.set(this,[]),Oa.set(this,void 0),Fs.set(this,void 0),Pa.set(this,void 0),gu.set(this,(n,r)=>{var s;return!((s=j(this,ir,"f")[n])===null||s===void 0)&&s.push(r)||(j(this,ir,"f")[n]=[r]),()=>j(this,_r,"m",E0).call(this,n,r)}),yu.set(this,({silent:n}={})=>Z(this,void 0,void 0,function*(){if(j(this,gn,"f")||this.connected)return{accounts:this.accounts};X(this,gn,!0);try{if(n){const r=yield j(this,Jt,"f").get();if(r)yield j(this,Cr,"f").call(this,r);else return{accounts:this.accounts}}else yield j(this,Da,"f").call(this)}catch(r){throw new Error(r instanceof Error&&r.message||"Unknown error")}finally{X(this,gn,!1)}return{accounts:this.accounts}})),Da.set(this,n=>Z(this,void 0,void 0,function*(){try{const r=yield j(this,Jt,"f").get();if(r)return j(this,Cr,"f").call(this,r),r;const s=yield j(this,Oa,"f").select(j(this,Tr,"f"));return yield j(this,Ir,"f").call(this,i=>Z(this,void 0,void 0,function*(){const[o,l]=yield Promise.all([i.getCapabilities(),i.authorize({chain:s,identity:j(this,Us,"f"),sign_in_payload:n})]),c=j(this,Wa,"f").call(this,l.accounts),u=Object.assign(Object.assign({},l),{accounts:c,chain:s});return Promise.all([j(this,wu,"f").call(this,o),j(this,Jt,"f").set(u),j(this,Cr,"f").call(this,u)]),u}))}catch(r){throw new Error(r instanceof Error&&r.message||"Unknown error")}})),Cr.set(this,n=>Z(this,void 0,void 0,function*(){var r;const s=j(this,Ve,"f")==null||((r=j(this,Ve,"f"))===null||r===void 0?void 0:r.accounts.length)!==n.accounts.length||j(this,Ve,"f").accounts.some((i,o)=>i.address!==n.accounts[o].address);X(this,Ve,n),s&&j(this,_r,"m",Ra).call(this,"change",{accounts:this.accounts})})),wu.set(this,n=>Z(this,void 0,void 0,function*(){const r=n.features.includes("solana:signTransactions"),s=n.supports_sign_and_send_transactions,i=Oe.SolanaSignAndSendTransaction in this.features!==s||Oe.SolanaSignTransaction in this.features!==r;X(this,Fs,Object.assign(Object.assign({},(s||!s&&!r)&&{[Oe.SolanaSignAndSendTransaction]:{version:"1.0.0",supportedTransactionVersions:["legacy",0],signAndSendTransaction:j(this,Ba,"f")}}),r&&{[Oe.SolanaSignTransaction]:{version:"1.0.0",supportedTransactionVersions:["legacy",0],signTransaction:j(this,Su,"f")}})),i&&j(this,_r,"m",Ra).call(this,"change",{features:this.features})})),Gs.set(this,(n,r,s)=>Z(this,void 0,void 0,function*(){try{const i=yield n.authorize({auth_token:r,identity:j(this,Us,"f"),chain:s}),o=j(this,Wa,"f").call(this,i.accounts),l=Object.assign(Object.assign({},i),{accounts:o,chain:s});Promise.all([j(this,Jt,"f").set(l),j(this,Cr,"f").call(this,l)])}catch(i){throw j(this,za,"f").call(this),new Error(i instanceof Error&&i.message||"Unknown error")}})),za.set(this,()=>Z(this,void 0,void 0,function*(){var n;j(this,Jt,"f").clear(),X(this,gn,!1),X(this,Ar,(n=j(this,Ar,"f"),n++,n)),X(this,Ve,void 0),j(this,_r,"m",Ra).call(this,"change",{accounts:this.accounts})})),Ir.set(this,n=>Z(this,void 0,void 0,function*(){var r;const s=(r=j(this,Ve,"f"))===null||r===void 0?void 0:r.wallet_uri_base,i=s?{baseUri:s}:void 0,o=j(this,Ar,"f");try{return yield m0.transact(n,i)}catch(l){throw j(this,Ar,"f")!==o&&(yield new Promise(()=>{})),l instanceof Error&&l.name==="SolanaMobileWalletAdapterError"&&l.code==="ERROR_WALLET_NOT_FOUND"&&(yield j(this,Pa,"f").call(this,this)),l}})),$s.set(this,()=>{if(!j(this,Ve,"f"))throw new Error("Wallet not connected");return{authToken:j(this,Ve,"f").auth_token,chain:j(this,Ve,"f").chain}}),Wa.set(this,n=>n.map(r=>{var s,i;const o=Tn(r.address);return{address:Bo.default.encode(o),publicKey:o,label:r.label,icon:r.icon,chains:(s=r.chains)!==null&&s!==void 0?s:j(this,Tr,"f"),features:(i=r.features)!==null&&i!==void 0?i:j0}})),vu.set(this,n=>Z(this,void 0,void 0,function*(){const{authToken:r,chain:s}=j(this,$s,"f").call(this);try{return yield j(this,Ir,"f").call(this,i=>Z(this,void 0,void 0,function*(){return yield j(this,Gs,"f").call(this,i,r,s),yield i.signTransactions({transactions:n})}))}catch(i){throw new Error(i instanceof Error&&i.message||"Unknown error")}})),xu.set(this,(n,r)=>Z(this,void 0,void 0,function*(){const{authToken:s,chain:i}=j(this,$s,"f").call(this);try{return yield j(this,Ir,"f").call(this,o=>Z(this,void 0,void 0,function*(){const[l,c]=yield Promise.all([o.getCapabilities(),j(this,Gs,"f").call(this,o,s,i)]);if(l.supports_sign_and_send_transactions)return(yield o.signAndSendTransactions(Object.assign(Object.assign({},r),{transactions:[n]})))[0];throw new Error("connected wallet does not support signAndSendTransaction")}))}catch(o){throw new Error(o instanceof Error&&o.message||"Unknown error")}})),Ba.set(this,(...n)=>Z(this,void 0,void 0,function*(){const r=[];for(const s of n){const i=Li.VersionedTransaction.deserialize(s.transaction),o=yield j(this,xu,"f").call(this,i,s.options);r.push({signature:Bo.default.decode(o)})}return r})),Su.set(this,(...n)=>Z(this,void 0,void 0,function*(){const r=n.map(({transaction:i})=>Li.VersionedTransaction.deserialize(i));return(yield j(this,vu,"f").call(this,r)).map(i=>({signedTransaction:S0(i)?i.serialize():new Uint8Array(i.serialize({requireAllSignatures:!1,verifySignatures:!1}))}))})),Nu.set(this,(...n)=>Z(this,void 0,void 0,function*(){const{authToken:r,chain:s}=j(this,$s,"f").call(this),i=n.map(({account:l})=>N0(l.publicKey)),o=n.map(({message:l})=>l);try{return yield j(this,Ir,"f").call(this,l=>Z(this,void 0,void 0,function*(){return yield j(this,Gs,"f").call(this,l,r,s),(yield l.signMessages({addresses:i,payloads:o})).map(u=>({signedMessage:u,signature:u.slice(-b0)}))}))}catch(l){throw new Error(l instanceof Error&&l.message||"Unknown error")}})),Eu.set(this,(...n)=>Z(this,void 0,void 0,function*(){const r=[];if(n.length>1)for(const s of n)r.push(yield j(this,Ua,"f").call(this,s));else return[yield j(this,Ua,"f").call(this,n[0])];return r})),Ua.set(this,n=>Z(this,void 0,void 0,function*(){var r,s;X(this,gn,!0);try{const i=yield j(this,Da,"f").call(this,Object.assign(Object.assign({},n),{domain:(r=n==null?void 0:n.domain)!==null&&r!==void 0?r:window.location.host}));if(!i.sign_in_result)throw new Error("Sign in failed, no sign in result returned by wallet");const o=i.sign_in_result.address;return{account:Object.assign(Object.assign({},(s=i.accounts.find(c=>c.address==o))!==null&&s!==void 0?s:{address:o}),{publicKey:Tn(o)}),signedMessage:Tn(i.sign_in_result.signed_message),signature:Tn(i.sign_in_result.signature)}}catch(i){throw new Error(i instanceof Error&&i.message||"Unknown error")}finally{X(this,gn,!1)}})),X(this,Jt,t.authorizationCache),X(this,Us,t.appIdentity),X(this,Tr,t.chains),X(this,Oa,t.chainSelector),X(this,Pa,t.onWalletNotFound),X(this,Fs,{[Oe.SolanaSignAndSendTransaction]:{version:"1.0.0",supportedTransactionVersions:["legacy",0],signAndSendTransaction:j(this,Ba,"f")}})}get version(){return j(this,fu,"f")}get name(){return j(this,hu,"f")}get url(){return j(this,mu,"f")}get icon(){return j(this,pu,"f")}get chains(){return j(this,Tr,"f")}get features(){return Object.assign({[ss.StandardConnect]:{version:"1.0.0",connect:j(this,yu,"f")},[ss.StandardDisconnect]:{version:"1.0.0",disconnect:j(this,za,"f")},[ss.StandardEvents]:{version:"1.0.0",on:j(this,gu,"f")},[Oe.SolanaSignMessage]:{version:"1.0.0",signMessage:j(this,Nu,"f")},[Oe.SolanaSignIn]:{version:"1.0.0",signIn:j(this,Eu,"f")}},j(this,Fs,"f"))}get accounts(){var t,n;return(n=(t=j(this,Ve,"f"))===null||t===void 0?void 0:t.accounts)!==null&&n!==void 0?n:[]}get connected(){return!!j(this,Ve,"f")}get isAuthorized(){return!!j(this,Ve,"f")}get currentAuthorization(){return j(this,Ve,"f")}get cachedAuthorizationResult(){return j(this,Jt,"f").get()}}ir=new WeakMap,fu=new WeakMap,hu=new WeakMap,mu=new WeakMap,pu=new WeakMap,Us=new WeakMap,Ve=new WeakMap,Jt=new WeakMap,gn=new WeakMap,Ar=new WeakMap,Tr=new WeakMap,Oa=new WeakMap,Fs=new WeakMap,Pa=new WeakMap,gu=new WeakMap,yu=new WeakMap,Da=new WeakMap,Cr=new WeakMap,wu=new WeakMap,Gs=new WeakMap,za=new WeakMap,Ir=new WeakMap,$s=new WeakMap,Wa=new WeakMap,vu=new WeakMap,xu=new WeakMap,Ba=new WeakMap,Su=new WeakMap,Nu=new WeakMap,Eu=new WeakMap,Ua=new WeakMap,_r=new WeakSet,Ra=function(t,...n){var r;(r=j(this,ir,"f")[t])===null||r===void 0||r.forEach(s=>s.apply(null,n))},E0=function(t,n){var r;j(this,ir,"f")[t]=(r=j(this,ir,"f")[t])===null||r===void 0?void 0:r.filter(s=>n!==s)};class A0{constructor(t){kr.add(this),ar.set(this,{}),Mu.set(this,"1.0.0"),bu.set(this,lf),ju.set(this,"https://solanamobile.com/wallets"),_u.set(this,x0),Hs.set(this,void 0),Qe.set(this,void 0),yn.set(this,void 0),wn.set(this,!1),Lr.set(this,0),Or.set(this,[]),Fa.set(this,void 0),Ys.set(this,void 0),Ga.set(this,void 0),$a.set(this,void 0),jt.set(this,void 0),Au.set(this,(n,r)=>{var s;return!((s=j(this,ar,"f")[n])===null||s===void 0)&&s.push(r)||(j(this,ar,"f")[n]=[r]),()=>j(this,kr,"m",M0).call(this,n,r)}),Tu.set(this,({silent:n}={})=>Z(this,void 0,void 0,function*(){if(j(this,wn,"f")||this.connected)return{accounts:this.accounts};X(this,wn,!0);try{yield j(this,Ya,"f").call(this)}catch(r){throw new Error(r instanceof Error&&r.message||"Unknown error")}finally{X(this,wn,!1)}return{accounts:this.accounts}})),Ya.set(this,n=>Z(this,void 0,void 0,function*(){try{const r=yield j(this,yn,"f").get();if(r)return j(this,Ks,"f").call(this,r),r;j(this,jt,"f")&&X(this,jt,void 0,"f");const s=yield j(this,Fa,"f").select(j(this,Or,"f"));return yield j(this,Pr,"f").call(this,i=>Z(this,void 0,void 0,function*(){const[o,l]=yield Promise.all([i.getCapabilities(),i.authorize({chain:s,identity:j(this,Hs,"f"),sign_in_payload:n})]),c=j(this,Va,"f").call(this,l.accounts),u=Object.assign(Object.assign({},l),{accounts:c,chain:s});return Promise.all([j(this,Cu,"f").call(this,o),j(this,yn,"f").set(u),j(this,Ks,"f").call(this,u)]),u}))}catch(r){throw new Error(r instanceof Error&&r.message||"Unknown error")}})),Ks.set(this,n=>Z(this,void 0,void 0,function*(){var r;const s=j(this,Qe,"f")==null||((r=j(this,Qe,"f"))===null||r===void 0?void 0:r.accounts.length)!==n.accounts.length||j(this,Qe,"f").accounts.some((i,o)=>i.address!==n.accounts[o].address);X(this,Qe,n),s&&j(this,kr,"m",Ha).call(this,"change",{accounts:this.accounts})})),Cu.set(this,n=>Z(this,void 0,void 0,function*(){const r=n.features.includes("solana:signTransactions"),s=n.supports_sign_and_send_transactions||n.features.includes("solana:signAndSendTransaction"),i=Oe.SolanaSignAndSendTransaction in this.features!==s||Oe.SolanaSignTransaction in this.features!==r;X(this,Ys,Object.assign(Object.assign({},s&&{[Oe.SolanaSignAndSendTransaction]:{version:"1.0.0",supportedTransactionVersions:n.supported_transaction_versions,signAndSendTransaction:j(this,Qa,"f")}}),r&&{[Oe.SolanaSignTransaction]:{version:"1.0.0",supportedTransactionVersions:n.supported_transaction_versions,signTransaction:j(this,Lu,"f")}})),i&&j(this,kr,"m",Ha).call(this,"change",{features:this.features})})),Vs.set(this,(n,r,s)=>Z(this,void 0,void 0,function*(){try{const i=yield n.authorize({auth_token:r,identity:j(this,Hs,"f")}),o=j(this,Va,"f").call(this,i.accounts),l=Object.assign(Object.assign({},i),{accounts:o,chain:s});Promise.all([j(this,yn,"f").set(l),j(this,Ks,"f").call(this,l)])}catch(i){throw j(this,Ka,"f").call(this),new Error(i instanceof Error&&i.message||"Unknown error")}})),Ka.set(this,()=>Z(this,void 0,void 0,function*(){var n,r;(n=j(this,jt,"f"))===null||n===void 0||n.close(),j(this,yn,"f").clear(),X(this,wn,!1),X(this,Lr,(r=j(this,Lr,"f"),r++,r)),X(this,Qe,void 0),X(this,jt,void 0),j(this,kr,"m",Ha).call(this,"change",{accounts:this.accounts})})),Pr.set(this,n=>Z(this,void 0,void 0,function*(){var r;const s=(r=j(this,Qe,"f"))===null||r===void 0?void 0:r.wallet_uri_base,o=Object.assign(Object.assign({},s?{baseUri:s}:void 0),{remoteHostAuthority:j(this,$a,"f")}),l=j(this,Lr,"f"),c=new pS;if(j(this,jt,"f"))return n(j(this,jt,"f").wallet);try{const{associationUrl:u,close:d,wallet:h}=yield m0.startRemoteScenario(o),y=c.addEventListener("close",g=>{g&&d()});return c.initWithQR(u.toString()),c.open(),X(this,jt,{close:d,wallet:yield h},"f"),y(),c.close(),yield n(j(this,jt,"f").wallet)}catch(u){throw c.close(),j(this,Lr,"f")!==l&&(yield new Promise(()=>{})),u instanceof Error&&u.name==="SolanaMobileWalletAdapterError"&&u.code==="ERROR_WALLET_NOT_FOUND"&&(yield j(this,Ga,"f").call(this,this)),u}})),Qs.set(this,()=>{if(!j(this,Qe,"f"))throw new Error("Wallet not connected");return{authToken:j(this,Qe,"f").auth_token,chain:j(this,Qe,"f").chain}}),Va.set(this,n=>n.map(r=>{var s,i;const o=Tn(r.address);return{address:Bo.default.encode(o),publicKey:o,label:r.label,icon:r.icon,chains:(s=r.chains)!==null&&s!==void 0?s:j(this,Or,"f"),features:(i=r.features)!==null&&i!==void 0?i:j0}})),Iu.set(this,n=>Z(this,void 0,void 0,function*(){const{authToken:r,chain:s}=j(this,Qs,"f").call(this);try{return yield j(this,Pr,"f").call(this,i=>Z(this,void 0,void 0,function*(){return yield j(this,Vs,"f").call(this,i,r,s),yield i.signTransactions({transactions:n})}))}catch(i){throw new Error(i instanceof Error&&i.message||"Unknown error")}})),ku.set(this,(n,r)=>Z(this,void 0,void 0,function*(){const{authToken:s,chain:i}=j(this,Qs,"f").call(this);try{return yield j(this,Pr,"f").call(this,o=>Z(this,void 0,void 0,function*(){const[l,c]=yield Promise.all([o.getCapabilities(),j(this,Vs,"f").call(this,o,s,i)]);if(l.supports_sign_and_send_transactions)return(yield o.signAndSendTransactions(Object.assign(Object.assign({},r),{transactions:[n]})))[0];throw new Error("connected wallet does not support signAndSendTransaction")}))}catch(o){throw new Error(o instanceof Error&&o.message||"Unknown error")}})),Qa.set(this,(...n)=>Z(this,void 0,void 0,function*(){const r=[];for(const s of n){const i=Li.VersionedTransaction.deserialize(s.transaction),o=yield j(this,ku,"f").call(this,i,s.options);r.push({signature:Bo.default.decode(o)})}return r})),Lu.set(this,(...n)=>Z(this,void 0,void 0,function*(){const r=n.map(({transaction:i})=>Li.VersionedTransaction.deserialize(i));return(yield j(this,Iu,"f").call(this,r)).map(i=>({signedTransaction:S0(i)?i.serialize():new Uint8Array(i.serialize({requireAllSignatures:!1,verifySignatures:!1}))}))})),Ou.set(this,(...n)=>Z(this,void 0,void 0,function*(){const{authToken:r,chain:s}=j(this,Qs,"f").call(this),i=n.map(({account:l})=>N0(l.publicKey)),o=n.map(({message:l})=>l);try{return yield j(this,Pr,"f").call(this,l=>Z(this,void 0,void 0,function*(){return yield j(this,Vs,"f").call(this,l,r,s),(yield l.signMessages({addresses:i,payloads:o})).map(u=>({signedMessage:u,signature:u.slice(-b0)}))}))}catch(l){throw new Error(l instanceof Error&&l.message||"Unknown error")}})),Pu.set(this,(...n)=>Z(this,void 0,void 0,function*(){const r=[];if(n.length>1)for(const s of n)r.push(yield j(this,Za,"f").call(this,s));else return[yield j(this,Za,"f").call(this,n[0])];return r})),Za.set(this,n=>Z(this,void 0,void 0,function*(){var r,s;X(this,wn,!0);try{const i=yield j(this,Ya,"f").call(this,Object.assign(Object.assign({},n),{domain:(r=n==null?void 0:n.domain)!==null&&r!==void 0?r:window.location.host}));if(!i.sign_in_result)throw new Error("Sign in failed, no sign in result returned by wallet");const o=i.sign_in_result.address;return{account:Object.assign(Object.assign({},(s=i.accounts.find(c=>c.address==o))!==null&&s!==void 0?s:{address:o}),{publicKey:Tn(o)}),signedMessage:Tn(i.sign_in_result.signed_message),signature:Tn(i.sign_in_result.signature)}}catch(i){throw new Error(i instanceof Error&&i.message||"Unknown error")}finally{X(this,wn,!1)}})),X(this,yn,t.authorizationCache),X(this,Hs,t.appIdentity),X(this,Or,t.chains),X(this,Fa,t.chainSelector),X(this,$a,t.remoteHostAuthority),X(this,Ga,t.onWalletNotFound),X(this,Ys,{[Oe.SolanaSignAndSendTransaction]:{version:"1.0.0",supportedTransactionVersions:["legacy",0],signAndSendTransaction:j(this,Qa,"f")}})}get version(){return j(this,Mu,"f")}get name(){return j(this,bu,"f")}get url(){return j(this,ju,"f")}get icon(){return j(this,_u,"f")}get chains(){return j(this,Or,"f")}get features(){return Object.assign({[ss.StandardConnect]:{version:"1.0.0",connect:j(this,Tu,"f")},[ss.StandardDisconnect]:{version:"1.0.0",disconnect:j(this,Ka,"f")},[ss.StandardEvents]:{version:"1.0.0",on:j(this,Au,"f")},[Oe.SolanaSignMessage]:{version:"1.0.0",signMessage:j(this,Ou,"f")},[Oe.SolanaSignIn]:{version:"1.0.0",signIn:j(this,Pu,"f")}},j(this,Ys,"f"))}get accounts(){var t,n;return(n=(t=j(this,Qe,"f"))===null||t===void 0?void 0:t.accounts)!==null&&n!==void 0?n:[]}get connected(){return!!j(this,jt,"f")&&!!j(this,Qe,"f")}get isAuthorized(){return!!j(this,Qe,"f")}get currentAuthorization(){return j(this,Qe,"f")}get cachedAuthorizationResult(){return j(this,yn,"f").get()}}ar=new WeakMap,Mu=new WeakMap,bu=new WeakMap,ju=new WeakMap,_u=new WeakMap,Hs=new WeakMap,Qe=new WeakMap,yn=new WeakMap,wn=new WeakMap,Lr=new WeakMap,Or=new WeakMap,Fa=new WeakMap,Ys=new WeakMap,Ga=new WeakMap,$a=new WeakMap,jt=new WeakMap,Au=new WeakMap,Tu=new WeakMap,Ya=new WeakMap,Ks=new WeakMap,Cu=new WeakMap,Vs=new WeakMap,Ka=new WeakMap,Pr=new WeakMap,Qs=new WeakMap,Va=new WeakMap,Iu=new WeakMap,ku=new WeakMap,Qa=new WeakMap,Lu=new WeakMap,Ou=new WeakMap,Pu=new WeakMap,Za=new WeakMap,kr=new WeakSet,Ha=function(t,...n){var r;(r=j(this,ar,"f")[t])===null||r===void 0||r.forEach(s=>s.apply(null,n))},M0=function(t,n){var r;j(this,ar,"f")[t]=(r=j(this,ar,"f")[t])===null||r===void 0?void 0:r.filter(s=>n!==s)};var wS=function(e,t,n,r,s){if(r==="m")throw new TypeError("Private method is not writable");if(r==="a"&&!s)throw new TypeError("Private accessor was defined without a setter");if(typeof t=="function"?e!==t||!s:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return r==="a"?s.call(e,n):s?s.value=n:t.set(e,n),n},vS=function(e,t,n,r){if(n==="a"&&!r)throw new TypeError("Private accessor was defined without a getter");if(typeof t=="function"?e!==t||!r:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return n==="m"?r:n==="a"?r.call(e):r?r.value:t.get(e)},Ja;function Dh(e){const t=({register:n})=>n(e);try{window.dispatchEvent(new xS(t))}catch(n){console.error(`wallet-standard:register-wallet event could not be dispatched
`,n)}try{window.addEventListener("wallet-standard:app-ready",({detail:n})=>t(n))}catch(n){console.error(`wallet-standard:app-ready event listener could not be added
`,n)}}let xS=class extends Event{constructor(t){super("wallet-standard:register-wallet",{bubbles:!1,cancelable:!1,composed:!1}),Ja.set(this,void 0),wS(this,Ja,t,"f")}get detail(){return vS(this,Ja,"f")}get type(){return"wallet-standard:register-wallet"}preventDefault(){throw new Error("preventDefault cannot be called")}stopImmediatePropagation(){throw new Error("stopImmediatePropagation cannot be called")}stopPropagation(){throw new Error("stopPropagation cannot be called")}};Ja=new WeakMap;function SS(){return typeof window<"u"&&window.isSecureContext&&typeof document<"u"&&/android/i.test(navigator.userAgent)}function NS(){return typeof window<"u"&&window.isSecureContext&&typeof document<"u"&&!/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)}function ES(e){SS()?Dh(new _0(e)):NS()&&e.remoteHostAuthority!==void 0&&Dh(new A0(Object.assign(Object.assign({},e),{remoteHostAuthority:e.remoteHostAuthority})))}const MS="To use mobile wallet adapter, you must have a compatible mobile wallet application installed on your device.",bS="This browser appears to be incompatible with mobile wallet adapter. Open this page in a compatible mobile browser app and try again.";class jS extends v0{constructor(){super(...arguments),this.contentStyles=AS,this.contentHtml=_S}initWithError(t){super.init(),this.populateError(t)}populateError(t){var n,r;const s=(n=this.dom)===null||n===void 0?void 0:n.getElementById("mobile-wallet-adapter-error-message"),i=(r=this.dom)===null||r===void 0?void 0:r.getElementById("mobile-wallet-adapter-error-action");if(s){if(t.name==="SolanaMobileWalletAdapterError")switch(t.code){case"ERROR_WALLET_NOT_FOUND":s.innerHTML=MS,i&&i.addEventListener("click",()=>{window.location.href="https://solanamobile.com/wallets"});return;case"ERROR_BROWSER_NOT_SUPPORTED":s.innerHTML=bS,i&&(i.style.display="none");return}s.innerHTML=`An unexpected error occurred: ${t.message}`}else console.log("Failed to locate error dialog element")}}const _S=`
<svg class="mobile-wallet-adapter-embedded-modal-error-icon" xmlns="http://www.w3.org/2000/svg" height="50px" viewBox="0 -960 960 960" width="50px" fill="#000000"><path d="M 280,-80 Q 197,-80 138.5,-138.5 80,-197 80,-280 80,-363 138.5,-421.5 197,-480 280,-480 q 83,0 141.5,58.5 58.5,58.5 58.5,141.5 0,83 -58.5,141.5 Q 363,-80 280,-80 Z M 824,-120 568,-376 Q 556,-389 542.5,-402.5 529,-416 516,-428 q 38,-24 61,-64 23,-40 23,-88 0,-75 -52.5,-127.5 Q 495,-760 420,-760 345,-760 292.5,-707.5 240,-655 240,-580 q 0,6 0.5,11.5 0.5,5.5 1.5,11.5 -18,2 -39.5,8 -21.5,6 -38.5,14 -2,-11 -3,-22 -1,-11 -1,-23 0,-109 75.5,-184.5 Q 311,-840 420,-840 q 109,0 184.5,75.5 75.5,75.5 75.5,184.5 0,43 -13.5,81.5 Q 653,-460 629,-428 l 251,252 z m -615,-61 71,-71 70,71 29,-28 -71,-71 71,-71 -28,-28 -71,71 -71,-71 -28,28 71,71 -71,71 z"/></svg>
<div class="mobile-wallet-adapter-embedded-modal-title">We can't find a wallet.</div>
<div id="mobile-wallet-adapter-error-message" class="mobile-wallet-adapter-embedded-modal-subtitle"></div>
<div>
    <button data-error-action id="mobile-wallet-adapter-error-action" class="mobile-wallet-adapter-embedded-modal-error-action">
        Find a wallet
    </button>
</div>
`,AS=`
.mobile-wallet-adapter-embedded-modal-content {
    text-align: center;
}

.mobile-wallet-adapter-embedded-modal-error-icon {
    margin-top: 24px;
}

.mobile-wallet-adapter-embedded-modal-title {
    margin: 18px 100px auto 100px;
    color: #000000;
    font-size: 2.75em;
    font-weight: 600;
}

.mobile-wallet-adapter-embedded-modal-subtitle {
    margin: 30px 60px 40px 60px;
    color: #000000;
    font-size: 1.25em;
    font-weight: 400;
}

.mobile-wallet-adapter-embedded-modal-error-action {
    display: block;
    width: 100%;
    height: 56px;
    /*margin-top: 40px;*/
    font-size: 1.25em;
    /*line-height: 24px;*/
    /*letter-spacing: -1%;*/
    background: #000000;
    color: #FFFFFF;
    border-radius: 18px;
}

/* Smaller screens */
@media all and (max-width: 600px) {
    .mobile-wallet-adapter-embedded-modal-title {
        font-size: 1.5em;
        margin-right: 12px;
        margin-left: 12px;
    }
    .mobile-wallet-adapter-embedded-modal-subtitle {
        margin-right: 12px;
        margin-left: 12px;
    }
}
`;function T0(){return Z(this,void 0,void 0,function*(){if(typeof window<"u"){const e=window.navigator.userAgent.toLowerCase(),t=new jS;e.includes("wv")?t.initWithError({name:"SolanaMobileWalletAdapterError",code:"ERROR_BROWSER_NOT_SUPPORTED",message:""}):t.initWithError({name:"SolanaMobileWalletAdapterError",code:"ERROR_WALLET_NOT_FOUND",message:""}),t.open()}})}function TS(){return()=>Z(this,void 0,void 0,function*(){T0()})}const tc="SolanaMobileWalletAdapterDefaultAuthorizationCache";function CS(){let e;try{e=window.localStorage}catch{}return{clear(){return Z(this,void 0,void 0,function*(){if(e)try{e.removeItem(tc)}catch{}})},get(){return Z(this,void 0,void 0,function*(){if(e)try{const t=JSON.parse(e.getItem(tc));if(t&&t.accounts){const n=t.accounts.map(r=>Object.assign(Object.assign({},r),{publicKey:"publicKey"in r?new Uint8Array(Object.values(r.publicKey)):new Li.PublicKey(r.address).toBytes()}));return Object.assign(Object.assign({},t),{accounts:n})}else return t||void 0}catch{}})},set(t){return Z(this,void 0,void 0,function*(){if(e)try{e.setItem(tc,JSON.stringify(t))}catch{}})}}}function IS(){return{select(e){return Z(this,void 0,void 0,function*(){return e.length===1?e[0]:e.includes(Rh.SOLANA_MAINNET_CHAIN)?Rh.SOLANA_MAINNET_CHAIN:e[0]})}}}Kt.LocalSolanaMobileWalletAdapterWallet=_0;Kt.RemoteSolanaMobileWalletAdapterWallet=A0;Kt.SolanaMobileWalletAdapterWalletName=lf;Kt.createDefaultAuthorizationCache=CS;Kt.createDefaultChainSelector=IS;Kt.createDefaultWalletNotFoundHandler=TS;Kt.defaultErrorModalWalletNotFoundHandler=T0;Kt.registerMwa=ES;Object.defineProperty($n,"__esModule",{value:!0});var _e=hv,qa=Eg,er=Mg,ws=Kt;function ue(e,t,n,r){function s(i){return i instanceof n?i:new n(function(o){o(i)})}return new(n||(n=Promise))(function(i,o){function l(d){try{u(r.next(d))}catch(h){o(h)}}function c(d){try{u(r.throw(d))}catch(h){o(h)}}function u(d){d.done?i(d.value):s(d.value).then(l,c)}u((r=r.apply(e,[])).next())})}function K(e,t,n,r){if(n==="a"&&!r)throw new TypeError("Private accessor was defined without a getter");if(typeof t=="function"?e!==t||!r:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return n==="m"?r:n==="a"?r.call(e):r?r.value:t.get(e)}function ot(e,t,n,r,s){if(typeof t=="function"?e!==t||!0:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return t.set(e,n),n}const Ru="standard:connect",kS="standard:disconnect",LS="standard:events";function nc(e){return window.btoa(String.fromCharCode.call(null,...e))}function OS(){return typeof window<"u"&&window.isSecureContext&&typeof document<"u"&&/android/i.test(navigator.userAgent)}var Ie,we,Mn,tn,Xa,en,Zn,Du,zu,C0,Uo,eo,Sn;const PS="Mobile Wallet Adapter",RS=64;function DS(e){return"version"in e}function cf(e){switch(e){case"mainnet-beta":return"solana:mainnet";case"testnet":return"solana:testnet";case"devnet":return"solana:devnet";default:return e}}class I0 extends _e.BaseSignInMessageSignerWalletAdapter{constructor(t,n){super(),Ie.add(this),this.supportedTransactionVersions=new Set(["legacy",0]),we.set(this,void 0),Mn.set(this,!1),tn.set(this,OS()?_e.WalletReadyState.Loadable:_e.WalletReadyState.Unsupported),Xa.set(this,void 0),en.set(this,void 0),Zn.set(this,void 0),Du.set(this,r=>ue(this,void 0,void 0,function*(){if(r.accounts&&r.accounts.length>0){K(this,Ie,"m",C0).call(this);const s=yield K(this,Xa,"f").call(this,r.accounts);s!==K(this,en,"f")&&(ot(this,en,s),ot(this,Zn,void 0),this.emit("connect",this.publicKey))}})),ot(this,Xa,r=>ue(this,void 0,void 0,function*(){var s;const i=yield n.addressSelector.select(r.map(({publicKey:o})=>nc(o)));return(s=r.find(({publicKey:o})=>nc(o)===i))!==null&&s!==void 0?s:r[0]})),ot(this,we,t),K(this,we,"f").features[LS].on("change",K(this,Du,"f")),this.name=K(this,we,"f").name,this.icon=K(this,we,"f").icon,this.url=K(this,we,"f").url}get publicKey(){var t;if(!K(this,Zn,"f")&&K(this,en,"f"))try{ot(this,Zn,new qa.PublicKey(K(this,en,"f").publicKey),"f")}catch(n){throw new _e.WalletPublicKeyError(n instanceof Error&&(n==null?void 0:n.message)||"Unknown error",n)}return(t=K(this,Zn,"f"))!==null&&t!==void 0?t:null}get connected(){return K(this,we,"f").connected}get connecting(){return K(this,Mn,"f")}get readyState(){return K(this,tn,"f")}autoConnect_DO_NOT_USE_OR_YOU_WILL_BE_FIRED(){return ue(this,void 0,void 0,function*(){return yield this.autoConnect()})}autoConnect(){return ue(this,void 0,void 0,function*(){K(this,Ie,"m",zu).call(this,!0)})}connect(){return ue(this,void 0,void 0,function*(){K(this,Ie,"m",zu).call(this)})}performAuthorization(t){return ue(this,void 0,void 0,function*(){try{const n=yield K(this,we,"f").cachedAuthorizationResult;return n?(yield K(this,we,"f").features[Ru].connect({silent:!0}),n):(t?yield K(this,we,"f").features[er.SolanaSignIn].signIn(t):yield K(this,we,"f").features[Ru].connect(),yield yield K(this,we,"f").cachedAuthorizationResult)}catch(n){throw new _e.WalletConnectionError(n instanceof Error&&n.message||"Unknown error",n)}})}disconnect(){return ue(this,void 0,void 0,function*(){return yield K(this,Ie,"m",Sn).call(this,()=>ue(this,void 0,void 0,function*(){ot(this,Mn,!1),ot(this,Zn,void 0),ot(this,en,void 0),yield K(this,we,"f").features[kS].disconnect(),this.emit("disconnect")}))})}signIn(t){return ue(this,void 0,void 0,function*(){return K(this,Ie,"m",Sn).call(this,()=>ue(this,void 0,void 0,function*(){var n;if(K(this,tn,"f")!==_e.WalletReadyState.Installed&&K(this,tn,"f")!==_e.WalletReadyState.Loadable)throw new _e.WalletNotReadyError;ot(this,Mn,!0);try{const r=yield K(this,we,"f").features[er.SolanaSignIn].signIn(Object.assign(Object.assign({},t),{domain:(n=t==null?void 0:t.domain)!==null&&n!==void 0?n:window.location.host}));if(r.length>0)return r[0];throw new Error("Sign in failed, no sign in result returned by wallet")}catch(r){throw new _e.WalletConnectionError(r instanceof Error&&r.message||"Unknown error",r)}finally{ot(this,Mn,!1)}}))})}signMessage(t){return ue(this,void 0,void 0,function*(){return yield K(this,Ie,"m",Sn).call(this,()=>ue(this,void 0,void 0,function*(){const n=K(this,Ie,"m",Uo).call(this);try{return(yield K(this,we,"f").features[er.SolanaSignMessage].signMessage({account:n,message:t}))[0].signature}catch(r){throw new _e.WalletSignMessageError(r==null?void 0:r.message,r)}}))})}sendTransaction(t,n,r){return ue(this,void 0,void 0,function*(){return yield K(this,Ie,"m",Sn).call(this,()=>ue(this,void 0,void 0,function*(){const s=K(this,Ie,"m",Uo).call(this);try{let i=function(){let o;switch(n.commitment){case"confirmed":case"finalized":case"processed":o=n.commitment;break;default:o="finalized"}let l;switch(r==null?void 0:r.preflightCommitment){case"confirmed":case"finalized":case"processed":l=r.preflightCommitment;break;case void 0:l=o;break;default:l="finalized"}return(l==="finalized"?2:l==="confirmed"?1:0)<(o==="finalized"?2:o==="confirmed"?1:0)?l:o};if(er.SolanaSignAndSendTransaction in K(this,we,"f").features){const o=cf(K(this,we,"f").currentAuthorization.chain),[l]=(yield K(this,we,"f").features[er.SolanaSignAndSendTransaction].signAndSendTransaction({account:s,transaction:t.serialize(),chain:o,options:r?{skipPreflight:r.skipPreflight,maxRetries:r.maxRetries}:void 0})).map(c=>nc(c.signature));return l}else{const[o]=yield K(this,Ie,"m",eo).call(this,[t]);if(DS(o))return yield n.sendTransaction(o);{const l=o.serialize();return yield n.sendRawTransaction(l,Object.assign(Object.assign({},r),{preflightCommitment:i()}))}}}catch(i){throw new _e.WalletSendTransactionError(i==null?void 0:i.message,i)}}))})}signTransaction(t){return ue(this,void 0,void 0,function*(){return yield K(this,Ie,"m",Sn).call(this,()=>ue(this,void 0,void 0,function*(){const[n]=yield K(this,Ie,"m",eo).call(this,[t]);return n}))})}signAllTransactions(t){return ue(this,void 0,void 0,function*(){return yield K(this,Ie,"m",Sn).call(this,()=>ue(this,void 0,void 0,function*(){return yield K(this,Ie,"m",eo).call(this,t)}))})}}we=new WeakMap,Mn=new WeakMap,tn=new WeakMap,Xa=new WeakMap,en=new WeakMap,Zn=new WeakMap,Du=new WeakMap,Ie=new WeakSet,zu=function(t=!1){return ue(this,void 0,void 0,function*(){if(!(this.connecting||this.connected))return yield K(this,Ie,"m",Sn).call(this,()=>ue(this,void 0,void 0,function*(){if(K(this,tn,"f")!==_e.WalletReadyState.Installed&&K(this,tn,"f")!==_e.WalletReadyState.Loadable)throw new _e.WalletNotReadyError;ot(this,Mn,!0);try{yield K(this,we,"f").features[Ru].connect({silent:t})}catch(n){throw new _e.WalletConnectionError(n instanceof Error&&n.message||"Unknown error",n)}finally{ot(this,Mn,!1)}}))})},C0=function(){K(this,tn,"f")!==_e.WalletReadyState.Installed&&this.emit("readyStateChange",ot(this,tn,_e.WalletReadyState.Installed))},Uo=function(){if(!K(this,we,"f").isAuthorized||!K(this,en,"f"))throw new _e.WalletNotConnectedError;return K(this,en,"f")},eo=function(t){return ue(this,void 0,void 0,function*(){const n=K(this,Ie,"m",Uo).call(this);try{if(er.SolanaSignTransaction in K(this,we,"f").features)return K(this,we,"f").features[er.SolanaSignTransaction].signTransaction(...t.map(r=>({account:n,transaction:r.serialize()}))).then(r=>r.map(s=>{const i=s.signedTransaction,l=i[0]*RS+1;return qa.VersionedMessage.deserializeMessageVersion(i.slice(l,i.length))==="legacy"?qa.Transaction.from(i):qa.VersionedTransaction.deserialize(i)}));throw new Error("Connected wallet does not support signing transactions")}catch(r){throw new _e.WalletSignTransactionError(r==null?void 0:r.message,r)}})},Sn=function(t){return ue(this,void 0,void 0,function*(){try{return yield t()}catch(n){throw this.emit("error",n),n}})};class k0 extends I0{constructor(t){var n;const r=cf((n=t.chain)!==null&&n!==void 0?n:t.cluster);super(new ws.LocalSolanaMobileWalletAdapterWallet({appIdentity:t.appIdentity,authorizationCache:{set:t.authorizationResultCache.set,get:()=>ue(this,void 0,void 0,function*(){const s=yield t.authorizationResultCache.get();return s&&"chain"in s?s:s?Object.assign(Object.assign({},s),{chain:r}):void 0}),clear:t.authorizationResultCache.clear},chains:[r],chainSelector:ws.createDefaultChainSelector(),onWalletNotFound:()=>ue(this,void 0,void 0,function*(){t.onWalletNotFound(this)})}),{addressSelector:t.addressSelector,chain:r})}}class zS extends I0{constructor(t){const n=cf(t.chain);super(new ws.RemoteSolanaMobileWalletAdapterWallet({appIdentity:t.appIdentity,authorizationCache:{set:t.authorizationResultCache.set,get:()=>ue(this,void 0,void 0,function*(){const r=yield t.authorizationResultCache.get();return r&&"chain"in r?r:r?Object.assign(Object.assign({},r),{chain:n}):void 0}),clear:t.authorizationResultCache.clear},chains:[n],chainSelector:ws.createDefaultChainSelector(),remoteHostAuthority:t.remoteHostAuthority,onWalletNotFound:()=>ue(this,void 0,void 0,function*(){t.onWalletNotFound(this)})}),{addressSelector:t.addressSelector,chain:n})}}class WS extends k0{}function BS(){return{select(e){return ue(this,void 0,void 0,function*(){return e[0]})}}}function US(){return ws.createDefaultAuthorizationCache()}function FS(e){return ue(this,void 0,void 0,function*(){return ws.defaultErrorModalWalletNotFoundHandler()})}function GS(){return FS}$n.LocalSolanaMobileWalletAdapter=k0;$n.RemoteSolanaMobileWalletAdapter=zS;var $S=$n.SolanaMobileWalletAdapter=WS,Jn=$n.SolanaMobileWalletAdapterWalletName=PS,HS=$n.createDefaultAddressSelector=BS,YS=$n.createDefaultAuthorizationResultCache=US,KS=$n.createDefaultWalletNotFoundHandler=GS,VS=function(e,t,n,r){if(n==="a"&&!r)throw new TypeError("Private accessor was defined without a getter");if(typeof t=="function"?e!==t||!r:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return n==="m"?r:n==="a"?r.call(e):r?r.value:t.get(e)},QS=function(e,t,n,r,s){if(r==="m")throw new TypeError("Private method is not writable");if(r==="a"&&!s)throw new TypeError("Private accessor was defined without a setter");if(typeof t=="function"?e!==t||!s:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return r==="a"?s.call(e,n):s?s.value=n:t.set(e,n),n},to;function ZS(e){const t=({register:n})=>n(e);try{window.dispatchEvent(new JS(t))}catch(n){console.error(`wallet-standard:register-wallet event could not be dispatched
`,n)}try{window.addEventListener("wallet-standard:app-ready",({detail:n})=>t(n))}catch(n){console.error(`wallet-standard:app-ready event listener could not be added
`,n)}}class JS extends Event{get detail(){return VS(this,to,"f")}get type(){return"wallet-standard:register-wallet"}constructor(t){super("wallet-standard:register-wallet",{bubbles:!1,cancelable:!1,composed:!1}),to.set(this,void 0),QS(this,to,t,"f")}preventDefault(){throw new Error("preventDefault cannot be called")}stopImmediatePropagation(){throw new Error("stopImmediatePropagation cannot be called")}stopPropagation(){throw new Error("stopPropagation cannot be called")}}to=new WeakMap;function qS(e,t){if(e===t)return!0;const n=e.length;if(n!==t.length)return!1;for(let r=0;r<n;r++)if(e[r]!==t[r])return!1;return!0}var H=function(e,t,n,r){if(n==="a"&&!r)throw new TypeError("Private accessor was defined without a getter");if(typeof t=="function"?e!==t||!r:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return n==="m"?r:n==="a"?r.call(e):r?r.value:t.get(e)},Me=function(e,t,n,r,s){if(r==="m")throw new TypeError("Private method is not writable");if(r==="a"&&!s)throw new TypeError("Private accessor was defined without a setter");if(typeof t=="function"?e!==t||!s:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return r==="a"?s.call(e,n):s?s.value=n:t.set(e,n),n},We,et,or,lr,qn,Zs,Fo,re,Go,Wu,no,Bu,ci,Uu,zh,Wh,Bh,Uh;class XS extends Jd{get name(){return H(this,re,"f").name}get url(){return"https://github.com/solana-labs/wallet-standard"}get icon(){return H(this,re,"f").icon}get readyState(){return H(this,Go,"f")}get publicKey(){return H(this,or,"f")}get connecting(){return H(this,lr,"f")}get supportedTransactionVersions(){return H(this,Fo,"f")}get wallet(){return H(this,re,"f")}get standard(){return!0}constructor({wallet:t}){super(),We.add(this),et.set(this,void 0),or.set(this,void 0),lr.set(this,void 0),qn.set(this,void 0),Zs.set(this,void 0),Fo.set(this,void 0),re.set(this,void 0),Go.set(this,typeof window>"u"||typeof document>"u"?V.Unsupported:V.Installed),Uu.set(this,n=>{if("accounts"in n){const r=H(this,re,"f").accounts[0];H(this,et,"f")&&!H(this,qn,"f")&&r!==H(this,et,"f")&&(r?H(this,We,"m",no).call(this,r):(this.emit("error",new Sr),H(this,We,"m",Bu).call(this)))}"features"in n&&H(this,We,"m",ci).call(this)}),Me(this,re,t,"f"),Me(this,et,null,"f"),Me(this,or,null,"f"),Me(this,lr,!1,"f"),Me(this,qn,!1,"f"),Me(this,Zs,H(this,re,"f").features[Fi].on("change",H(this,Uu,"f")),"f"),H(this,We,"m",ci).call(this)}destroy(){Me(this,et,null,"f"),Me(this,or,null,"f"),Me(this,lr,!1,"f"),Me(this,qn,!1,"f");const t=H(this,Zs,"f");t&&(Me(this,Zs,null,"f"),t())}async autoConnect(){return H(this,We,"m",Wu).call(this,{silent:!0})}async connect(){return H(this,We,"m",Wu).call(this)}async disconnect(){if(Ii in H(this,re,"f").features)try{Me(this,qn,!0,"f"),await H(this,re,"f").features[Ii].disconnect()}catch(t){this.emit("error",new Nr(t==null?void 0:t.message,t))}finally{Me(this,qn,!1,"f")}H(this,We,"m",Bu).call(this)}async sendTransaction(t,n,r={}){try{const s=H(this,et,"f");if(!s)throw new ie;let i;if(Ft in H(this,re,"f").features)if(s.features.includes(Ft))i=Ft;else if(Ae in H(this,re,"f").features&&s.features.includes(Ae))i=Ae;else throw new $t;else if(Ae in H(this,re,"f").features){if(!s.features.includes(Ae))throw new $t;i=Ae}else throw new sr;const o=Vr.getChainForEndpoint(n.rpcEndpoint);if(!s.chains.includes(o))throw new kt;try{const{signers:l,...c}=r;let u;if(Lt(t)?(l!=null&&l.length&&t.sign(l),u=t.serialize()):(t=await this.prepareTransaction(t,n,c),l!=null&&l.length&&t.partialSign(...l),u=new Uint8Array(t.serialize({requireAllSignatures:!1,verifySignatures:!1}))),i===Ft){const[d]=await H(this,re,"f").features[Ft].signAndSendTransaction({account:s,chain:o,transaction:u,options:{preflightCommitment:Vr.getCommitment(c.preflightCommitment||n.commitment),skipPreflight:c.skipPreflight,maxRetries:c.maxRetries,minContextSlot:c.minContextSlot}});return d0.encode(d.signature)}else{const[d]=await H(this,re,"f").features[Ae].signTransaction({account:s,chain:o,transaction:u,options:{preflightCommitment:Vr.getCommitment(c.preflightCommitment||n.commitment),minContextSlot:c.minContextSlot}});return await n.sendRawTransaction(d.signedTransaction,{...c,preflightCommitment:Vr.getCommitment(c.preflightCommitment||n.commitment)})}}catch(l){throw l instanceof ye?l:new kt(l==null?void 0:l.message,l)}}catch(s){throw this.emit("error",s),s}}}et=new WeakMap,or=new WeakMap,lr=new WeakMap,qn=new WeakMap,Zs=new WeakMap,Fo=new WeakMap,re=new WeakMap,Go=new WeakMap,Uu=new WeakMap,We=new WeakSet,Wu=async function(t){try{if(this.connected||this.connecting)return;if(H(this,Go,"f")!==V.Installed)throw new dn;if(Me(this,lr,!0,"f"),!H(this,re,"f").accounts.length)try{await H(this,re,"f").features[Ui].connect(t)}catch(r){throw new Bn(r==null?void 0:r.message,r)}const n=H(this,re,"f").accounts[0];if(!n)throw new $t;H(this,We,"m",no).call(this,n)}catch(n){throw this.emit("error",n),n}finally{Me(this,lr,!1,"f")}},no=function(t){let n;try{n=new se(t.address)}catch(r){throw new fn(r==null?void 0:r.message,r)}Me(this,et,t,"f"),Me(this,or,n,"f"),H(this,We,"m",ci).call(this),this.emit("connect",n)},Bu=function(){Me(this,et,null,"f"),Me(this,or,null,"f"),H(this,We,"m",ci).call(this),this.emit("disconnect")},ci=function(){var n,r;const t=Ft in H(this,re,"f").features?H(this,re,"f").features[Ft].supportedTransactionVersions:H(this,re,"f").features[Ae].supportedTransactionVersions;Me(this,Fo,qS(t,["legacy"])?null:new Set(t),"f"),Ae in H(this,re,"f").features&&((n=H(this,et,"f"))!=null&&n.features.includes(Ae))?(this.signTransaction=H(this,We,"m",zh),this.signAllTransactions=H(this,We,"m",Wh)):(delete this.signTransaction,delete this.signAllTransactions),Xn in H(this,re,"f").features&&((r=H(this,et,"f"))!=null&&r.features.includes(Xn))?this.signMessage=H(this,We,"m",Bh):delete this.signMessage,Ca in H(this,re,"f").features?this.signIn=H(this,We,"m",Uh):delete this.signIn},zh=async function(t){try{const n=H(this,et,"f");if(!n)throw new ie;if(!(Ae in H(this,re,"f").features))throw new sr;if(!n.features.includes(Ae))throw new $t;try{const s=(await H(this,re,"f").features[Ae].signTransaction({account:n,transaction:Lt(t)?t.serialize():new Uint8Array(t.serialize({requireAllSignatures:!1,verifySignatures:!1}))}))[0].signedTransaction;return Lt(t)?uc.deserialize(s):wt.from(s)}catch(r){throw r instanceof ye?r:new Be(r==null?void 0:r.message,r)}}catch(n){throw this.emit("error",n),n}},Wh=async function(t){try{const n=H(this,et,"f");if(!n)throw new ie;if(!(Ae in H(this,re,"f").features))throw new sr;if(!n.features.includes(Ae))throw new $t;try{const r=await H(this,re,"f").features[Ae].signTransaction(...t.map(s=>({account:n,transaction:Lt(s)?s.serialize():new Uint8Array(s.serialize({requireAllSignatures:!1,verifySignatures:!1}))})));return t.map((s,i)=>{const o=r[i].signedTransaction;return Lt(s)?uc.deserialize(o):wt.from(o)})}catch(r){throw new Be(r==null?void 0:r.message,r)}}catch(n){throw this.emit("error",n),n}},Bh=async function(t){try{const n=H(this,et,"f");if(!n)throw new ie;if(!(Xn in H(this,re,"f").features))throw new sr;if(!n.features.includes(Xn))throw new $t;try{return(await H(this,re,"f").features[Xn].signMessage({account:n,message:t}))[0].signature}catch(r){throw new Wi(r==null?void 0:r.message,r)}}catch(n){throw this.emit("error",n),n}},Uh=async function(t={}){try{if(!(Ca in H(this,re,"f").features))throw new sr;let n;try{[n]=await H(this,re,"f").features[Ca].signIn(t)}catch(r){throw new su(r==null?void 0:r.message,r)}if(!n)throw new su;return H(this,We,"m",no).call(this,n.account),n}catch(n){throw this.emit("error",n),n}};const eN=vg;var tN=function(e,t,n,r){if(n==="a"&&!r)throw new TypeError("Private accessor was defined without a getter");if(typeof t=="function"?e!==t||!r:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return n==="m"?r:n==="a"?r.call(e):r?r.value:t.get(e)},nN=function(e,t,n,r,s){if(r==="m")throw new TypeError("Private method is not writable");if(r==="a"&&!s)throw new TypeError("Private accessor was defined without a setter");if(typeof t=="function"?e!==t||!s:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return r==="a"?s.call(e,n):s?s.value=n:t.set(e,n),n},ro;let ct;const gl=new Set;function rN(e){ui=void 0,gl.add(e)}function sN(e){ui=void 0,gl.delete(e)}const Qr={};function iN(){if(ct||(ct=Object.freeze({register:Fh,get:aN,on:oN}),typeof window>"u"))return ct;const e=Object.freeze({register:Fh});try{window.addEventListener("wallet-standard:register-wallet",({detail:t})=>t(e))}catch(t){console.error(`wallet-standard:register-wallet event listener could not be added
`,t)}try{window.dispatchEvent(new lN(e))}catch(t){console.error(`wallet-standard:app-ready event could not be dispatched
`,t)}return ct}function Fh(...e){var t;return e=e.filter(n=>!gl.has(n)),e.length?(e.forEach(n=>rN(n)),(t=Qr.register)==null||t.forEach(n=>Fu(()=>n(...e))),function(){var r;e.forEach(s=>sN(s)),(r=Qr.unregister)==null||r.forEach(s=>Fu(()=>s(...e)))}):()=>{}}let ui;function aN(){return ui||(ui=[...gl]),ui}function oN(e,t){var n;return(n=Qr[e])!=null&&n.push(t)||(Qr[e]=[t]),function(){var s;Qr[e]=(s=Qr[e])==null?void 0:s.filter(i=>t!==i)}}function Fu(e){try{e()}catch(t){console.error(t)}}class lN extends Event{get detail(){return tN(this,ro,"f")}get type(){return"wallet-standard:app-ready"}constructor(t){super("wallet-standard:app-ready",{bubbles:!1,cancelable:!1,composed:!1}),ro.set(this,void 0),nN(this,ro,t,"f")}preventDefault(){throw new Error("preventDefault cannot be called")}stopImmediatePropagation(){throw new Error("stopImmediatePropagation cannot be called")}stopPropagation(){throw new Error("stopPropagation cannot be called")}}ro=new WeakMap;function cN(){if(ct||(ct=iN(),typeof window>"u"))return ct;const e=window.navigator.wallets||[];if(!Array.isArray(e))return console.error("window.navigator.wallets is not an array"),ct;const{register:t}=ct,n=(...r)=>r.forEach(s=>Fu(()=>s({register:t})));try{Object.defineProperty(window.navigator,"wallets",{value:Object.freeze({push:n})})}catch{return console.error("window.navigator.wallets could not be set"),ct}return n(...e),ct}function uN(e){const t=Gh(()=>new Set),{get:n,on:r}=Gh(()=>cN()),[s,i]=E.useState(()=>$h(n()));E.useEffect(()=>{const l=[r("register",(...c)=>i(u=>[...u,...$h(c)])),r("unregister",(...c)=>i(u=>u.filter(d=>c.some(h=>h===d.wallet))))];return()=>l.forEach(c=>c())},[r]);const o=dN(s);return E.useEffect(()=>{if(!o)return;const l=new Set(s);new Set(o.filter(u=>!l.has(u))).forEach(u=>u.destroy())},[o,s]),E.useEffect(()=>()=>s.forEach(l=>l.destroy()),[]),E.useMemo(()=>[...s,...e.filter(({name:l})=>s.some(c=>c.name===l)?(t.has(l)||(t.add(l),console.warn(`${l} was registered as a Standard Wallet. The Wallet Adapter for ${l} can be removed from your app.`)),!1):!0)],[s,e,t])}function Gh(e){const t=E.useRef(void 0);return t.current===void 0&&(t.current={value:e()}),t.current.value}function dN(e){const t=E.useRef(void 0);return E.useEffect(()=>{t.current=e}),t.current}function $h(e){return e.filter(eN).map(t=>new XS({wallet:t}))}var is;(function(e){e[e.DESKTOP_WEB=0]="DESKTOP_WEB",e[e.MOBILE_WEB=1]="MOBILE_WEB"})(is||(is={}));function fN(e){return/(WebView|Version\/.+(Chrome)\/(\d+)\.(\d+)\.(\d+)\.(\d+)|; wv\).+(Chrome)\/(\d+)\.(\d+)\.(\d+)\.(\d+))/i.test(e)}function hN({adapters:e,userAgentString:t}){return e.some(n=>n.name!==Jn&&n.readyState===V.Installed)?is.DESKTOP_WEB:t&&/android/i.test(t)&&!fN(t)?is.MOBILE_WEB:is.DESKTOP_WEB}function mN(e){return e?/devnet/i.test(e)?"devnet":/testnet/i.test(e)?"testnet":"mainnet-beta":"mainnet-beta"}function pN({children:e,wallets:t,adapter:n,isUnloadingRef:r,onAutoConnectRequest:s,onConnectError:i,onError:o,onSelectWallet:l}){const c=E.useRef(!1),[u,d]=E.useState(!1),h=E.useRef(!1),[y,g]=E.useState(!1),[S,N]=E.useState(()=>(n==null?void 0:n.publicKey)??null),[A,p]=E.useState(()=>(n==null?void 0:n.connected)??!1),m=E.useRef(o);E.useEffect(()=>(m.current=o,()=>{m.current=void 0}),[o]);const f=E.useRef((_,R)=>(r.current||(m.current?m.current(_,R):(console.error(_,R),_ instanceof dn&&typeof window<"u"&&R&&window.open(R.url,"_blank"))),_)),[x,M]=E.useState(()=>t.map(_=>({adapter:_,readyState:_.readyState})).filter(({readyState:_})=>_!==V.Unsupported));E.useEffect(()=>{M(R=>t.map((z,L)=>{const P=R[L];return P&&P.adapter===z&&P.readyState===z.readyState?P:{adapter:z,readyState:z.readyState}}).filter(({readyState:z})=>z!==V.Unsupported));function _(R){M(z=>{const L=z.findIndex(({adapter:U})=>U===this);if(L===-1)return z;const{adapter:P}=z[L];return[...z.slice(0,L),{adapter:P,readyState:R},...z.slice(L+1)].filter(({readyState:U})=>U!==V.Unsupported)})}return t.forEach(R=>R.on("readyStateChange",_,R)),()=>{t.forEach(R=>R.off("readyStateChange",_,R))}},[n,t]);const w=E.useMemo(()=>x.find(_=>_.adapter===n)??null,[n,x]);E.useEffect(()=>{if(!n)return;const _=L=>{N(L),c.current=!1,d(!1),p(!0),h.current=!1,g(!1)},R=()=>{r.current||(N(null),c.current=!1,d(!1),p(!1),h.current=!1,g(!1))},z=L=>{f.current(L,n)};return n.on("connect",_),n.on("disconnect",R),n.on("error",z),()=>{n.off("connect",_),n.off("disconnect",R),n.off("error",z),R()}},[n,r]);const v=E.useRef(!1);E.useEffect(()=>()=>{v.current=!1},[n]),E.useEffect(()=>{v.current||c.current||A||!s||!((w==null?void 0:w.readyState)===V.Installed||(w==null?void 0:w.readyState)===V.Loadable)||(c.current=!0,d(!0),v.current=!0,async function(){try{await s()}catch{i()}finally{d(!1),c.current=!1}}())},[A,s,i,w]);const b=E.useCallback(async(_,R,z)=>{if(!n)throw f.current(new Th);if(!A)throw f.current(new ie,n);return await n.sendTransaction(_,R,z)},[n,A]),T=E.useMemo(()=>n&&"signTransaction"in n?async _=>{if(!A)throw f.current(new ie,n);return await n.signTransaction(_)}:void 0,[n,A]),C=E.useMemo(()=>n&&"signAllTransactions"in n?async _=>{if(!A)throw f.current(new ie,n);return await n.signAllTransactions(_)}:void 0,[n,A]),O=E.useMemo(()=>n&&"signMessage"in n?async _=>{if(!A)throw f.current(new ie,n);return await n.signMessage(_)}:void 0,[n,A]),D=E.useMemo(()=>n&&"signIn"in n?async _=>await n.signIn(_):void 0,[n]),I=E.useCallback(async()=>{if(c.current||h.current||w!=null&&w.adapter.connected)return;if(!w)throw f.current(new Th);const{adapter:_,readyState:R}=w;if(!(R===V.Installed||R===V.Loadable))throw f.current(new dn,_);c.current=!0,d(!0);try{await _.connect()}catch(z){throw i(),z}finally{d(!1),c.current=!1}},[i,w]),F=E.useCallback(async()=>{if(!h.current&&n){h.current=!0,g(!0);try{await n.disconnect()}finally{g(!1),h.current=!1}}},[n]);return W.createElement(Ng.Provider,{value:{autoConnect:!!s,wallets:x,wallet:w,publicKey:S,connected:A,connecting:u,disconnecting:y,select:l,connect:I,disconnect:F,sendTransaction:b,signTransaction:T,signAllTransactions:C,signMessage:O,signIn:D}},e)}let rc;function gN(){var e;return rc===void 0&&(rc=((e=globalThis.navigator)==null?void 0:e.userAgent)??null),rc}function ma(e){const t=gN();return hN({adapters:e,userAgentString:t})===is.MOBILE_WEB}function yN(){const e=globalThis.location;if(e)return`${e.protocol}//${e.host}`}function wN({children:e,wallets:t,autoConnect:n,localStorageKey:r="walletName",onError:s}){const{connection:i}=Sg(),o=uN(t),l=E.useMemo(()=>{if(!ma(o))return null;const m=o.find(f=>f.name===Jn);return m||new $S({addressSelector:HS(),appIdentity:{uri:yN()},authorizationResultCache:YS(),cluster:mN(i==null?void 0:i.rpcEndpoint),onWalletNotFound:KS()})},[o,i==null?void 0:i.rpcEndpoint]),c=E.useMemo(()=>l==null||o.indexOf(l)!==-1?o:[l,...o],[o,l]),[u,d]=fv(r,ma(o)?Jn:null),h=E.useMemo(()=>c.find(m=>m.name===u)??null,[c,u]),y=E.useCallback(m=>{u!==m&&(h&&h.name!==Jn&&h.disconnect(),d(m))},[h,d,u]);E.useEffect(()=>{if(!h)return;function m(){N.current||u===Jn&&ma(o)||d(null)}return h.on("disconnect",m),()=>{h.off("disconnect",m)}},[h,o,d,u]);const g=E.useRef(!1),S=E.useMemo(()=>{if(!(!n||!h))return async()=>{(n===!0||await n(h))&&(g.current?await h.connect():await h.autoConnect())}},[n,h]),N=E.useRef(!1);E.useEffect(()=>{if(u===Jn&&ma(o)){N.current=!1;return}function m(){N.current=!0}return window.addEventListener("beforeunload",m),()=>{window.removeEventListener("beforeunload",m)}},[o,u]);const A=E.useCallback(()=>{h&&h.name!==Jn&&y(null)},[h,y]),p=E.useCallback(m=>{g.current=!0,y(m)},[y]);return W.createElement(pN,{wallets:c,adapter:h,isUnloadingRef:N,onAutoConnectRequest:S,onConnectError:A,onError:s,onSelectWallet:p},e)}const L0={setVisible(e){console.error(O0("call","setVisible"))},visible:!1};Object.defineProperty(L0,"visible",{get(){return console.error(O0("read","visible")),!1}});function O0(e,t){return`You have tried to  ${e} "${t}" on a WalletModalContext without providing one. Make sure to render a WalletModalProvider as an ancestor of the component that uses WalletModalContext`}const P0=E.createContext(L0);function R0(){return E.useContext(P0)}const vN=[],yl={autoConnect:!1,connecting:!1,connected:!1,disconnecting:!1,select(){Tt("call","select")},connect(){return Promise.reject(Tt("call","connect"))},disconnect(){return Promise.reject(Tt("call","disconnect"))},sendTransaction(){return Promise.reject(Tt("call","sendTransaction"))},signTransaction(){return Promise.reject(Tt("call","signTransaction"))},signAllTransactions(){return Promise.reject(Tt("call","signAllTransactions"))},signMessage(){return Promise.reject(Tt("call","signMessage"))},signIn(){return Promise.reject(Tt("call","signIn"))}};Object.defineProperty(yl,"wallets",{get(){return Tt("read","wallets"),vN}});Object.defineProperty(yl,"wallet",{get(){return Tt("read","wallet"),null}});Object.defineProperty(yl,"publicKey",{get(){return Tt("read","publicKey"),null}});function Tt(e,t){const n=new Error(`You have tried to ${e} "${t}" on a WalletContext without providing one. Make sure to render a WalletProvider as an ancestor of the component that uses WalletContext.`);return console.error(n),n}const xN=E.createContext(yl);function SN(){return E.useContext(xN)}function NN({onSelectWallet:e}){const{connect:t,connected:n,connecting:r,disconnect:s,disconnecting:i,publicKey:o,select:l,wallet:c,wallets:u}=SN();let d;r?d="connecting":n?d="connected":i?d="disconnecting":c?d="has-wallet":d="no-wallet";const h=E.useCallback(()=>{t().catch(()=>{})},[t]),y=E.useCallback(()=>{s().catch(()=>{})},[s]),g=E.useCallback(()=>{e({onSelectWallet:l,wallets:u})},[e,l,u]);return{buttonState:d,onConnect:d==="has-wallet"?h:void 0,onDisconnect:d!=="disconnecting"&&d!=="no-wallet"?y:void 0,onSelectWallet:g,publicKey:o??void 0,walletIcon:c==null?void 0:c.adapter.icon,walletName:c==null?void 0:c.adapter.name}}const D0=e=>W.createElement("button",{className:`wallet-adapter-button ${e.className||""}`,disabled:e.disabled,style:e.style,onClick:e.onClick,tabIndex:e.tabIndex||0,type:"button"},e.startIcon&&W.createElement("i",{className:"wallet-adapter-button-start-icon"},e.startIcon),e.children,e.endIcon&&W.createElement("i",{className:"wallet-adapter-button-end-icon"},e.endIcon)),z0=({wallet:e,...t})=>e&&W.createElement("img",{src:e.adapter.icon,alt:`${e.adapter.name} icon`,...t});function EN({walletIcon:e,walletName:t,...n}){return W.createElement(D0,{...n,className:"wallet-adapter-button-trigger",startIcon:e&&t?W.createElement(z0,{wallet:{adapter:{icon:e,name:t}}}):void 0})}function MN({children:e,labels:t,...n}){const{setVisible:r}=R0(),{buttonState:s,onConnect:i,onDisconnect:o,publicKey:l,walletIcon:c,walletName:u}=NN({onSelectWallet(){r(!0)}}),[d,h]=E.useState(!1),[y,g]=E.useState(!1),S=E.useRef(null);E.useEffect(()=>{const A=p=>{const m=S.current;!m||m.contains(p.target)||g(!1)};return document.addEventListener("mousedown",A),document.addEventListener("touchstart",A),()=>{document.removeEventListener("mousedown",A),document.removeEventListener("touchstart",A)}},[]);const N=E.useMemo(()=>{if(e)return e;if(l){const A=l.toBase58();return A.slice(0,4)+".."+A.slice(-4)}else return s==="connecting"||s==="has-wallet"?t[s]:t["no-wallet"]},[s,e,t,l]);return W.createElement("div",{className:"wallet-adapter-dropdown"},W.createElement(EN,{...n,"aria-expanded":y,style:{pointerEvents:y?"none":"auto",...n.style},onClick:()=>{switch(s){case"no-wallet":r(!0);break;case"has-wallet":i&&i();break;case"connected":g(!0);break}},walletIcon:c,walletName:u},N),W.createElement("ul",{"aria-label":"dropdown-list",className:`wallet-adapter-dropdown-list ${y&&"wallet-adapter-dropdown-list-active"}`,ref:S,role:"menu"},l?W.createElement("li",{className:"wallet-adapter-dropdown-list-item",onClick:async()=>{await navigator.clipboard.writeText(l.toBase58()),h(!0),setTimeout(()=>h(!1),400)},role:"menuitem"},d?t.copied:t["copy-address"]):null,W.createElement("li",{className:"wallet-adapter-dropdown-list-item",onClick:()=>{r(!0),g(!1)},role:"menuitem"},t["change-wallet"]),o?W.createElement("li",{className:"wallet-adapter-dropdown-list-item",onClick:()=>{o(),g(!1)},role:"menuitem"},t.disconnect):null))}const Hh=({id:e,children:t,expanded:n=!1})=>{const r=E.useRef(null),s=E.useRef(!0),i="height 250ms ease-out",o=()=>{const c=r.current;c&&requestAnimationFrame(()=>{c.style.height=c.scrollHeight+"px"})},l=()=>{const c=r.current;c&&requestAnimationFrame(()=>{c.style.height=c.offsetHeight+"px",c.style.overflow="hidden",requestAnimationFrame(()=>{c.style.height="0"})})};return E.useLayoutEffect(()=>{n?o():l()},[n]),E.useLayoutEffect(()=>{const c=r.current;if(!c)return;function u(){c&&(c.style.overflow=n?"initial":"hidden",n&&(c.style.height="auto"))}function d(h){c&&h.target===c&&h.propertyName==="height"&&u()}return s.current&&(u(),s.current=!1),c.addEventListener("transitionend",d),()=>c.removeEventListener("transitionend",d)},[n]),W.createElement("div",{className:"wallet-adapter-collapse",id:e,ref:r,role:"region",style:{height:0,transition:s.current?void 0:i}},t)},sc=({handleClick:e,tabIndex:t,wallet:n})=>W.createElement("li",null,W.createElement(D0,{onClick:e,startIcon:W.createElement(z0,{wallet:n}),tabIndex:t},n.adapter.name,n.readyState===V.Installed&&W.createElement("span",null,"Detected"))),bN=()=>W.createElement("svg",{width:"97",height:"96",viewBox:"0 0 97 96",fill:"none",xmlns:"http://www.w3.org/2000/svg"},W.createElement("circle",{cx:"48.5",cy:"48",r:"48",fill:"url(#paint0_linear_880_5115)",fillOpacity:"0.1"}),W.createElement("circle",{cx:"48.5",cy:"48",r:"47",stroke:"url(#paint1_linear_880_5115)",strokeOpacity:"0.4",strokeWidth:"2"}),W.createElement("g",{clipPath:"url(#clip0_880_5115)"},W.createElement("path",{d:"M65.5769 28.1523H31.4231C27.6057 28.1523 24.5 31.258 24.5 35.0754V60.9215C24.5 64.7389 27.6057 67.8446 31.4231 67.8446H65.5769C69.3943 67.8446 72.5 64.7389 72.5 60.9215V35.0754C72.5 31.258 69.3943 28.1523 65.5769 28.1523ZM69.7308 52.1523H59.5769C57.2865 52.1523 55.4231 50.289 55.4231 47.9985C55.4231 45.708 57.2864 43.8446 59.5769 43.8446H69.7308V52.1523ZM69.7308 41.0754H59.5769C55.7595 41.0754 52.6539 44.1811 52.6539 47.9985C52.6539 51.8159 55.7595 54.9215 59.5769 54.9215H69.7308V60.9215C69.7308 63.2119 67.8674 65.0754 65.5769 65.0754H31.4231C29.1327 65.0754 27.2692 63.212 27.2692 60.9215V35.0754C27.2692 32.785 29.1326 30.9215 31.4231 30.9215H65.5769C67.8673 30.9215 69.7308 32.7849 69.7308 35.0754V41.0754Z",fill:"url(#paint2_linear_880_5115)"}),W.createElement("path",{d:"M61.4231 46.6172H59.577C58.8123 46.6172 58.1924 47.2371 58.1924 48.0018C58.1924 48.7665 58.8123 49.3863 59.577 49.3863H61.4231C62.1878 49.3863 62.8077 48.7664 62.8077 48.0018C62.8077 47.2371 62.1878 46.6172 61.4231 46.6172Z",fill:"url(#paint3_linear_880_5115)"})),W.createElement("defs",null,W.createElement("linearGradient",{id:"paint0_linear_880_5115",x1:"3.41664",y1:"98.0933",x2:"103.05",y2:"8.42498",gradientUnits:"userSpaceOnUse"},W.createElement("stop",{stopColor:"#9945FF"}),W.createElement("stop",{offset:"0.14",stopColor:"#8A53F4"}),W.createElement("stop",{offset:"0.42",stopColor:"#6377D6"}),W.createElement("stop",{offset:"0.79",stopColor:"#24B0A7"}),W.createElement("stop",{offset:"0.99",stopColor:"#00D18C"}),W.createElement("stop",{offset:"1",stopColor:"#00D18C"})),W.createElement("linearGradient",{id:"paint1_linear_880_5115",x1:"3.41664",y1:"98.0933",x2:"103.05",y2:"8.42498",gradientUnits:"userSpaceOnUse"},W.createElement("stop",{stopColor:"#9945FF"}),W.createElement("stop",{offset:"0.14",stopColor:"#8A53F4"}),W.createElement("stop",{offset:"0.42",stopColor:"#6377D6"}),W.createElement("stop",{offset:"0.79",stopColor:"#24B0A7"}),W.createElement("stop",{offset:"0.99",stopColor:"#00D18C"}),W.createElement("stop",{offset:"1",stopColor:"#00D18C"})),W.createElement("linearGradient",{id:"paint2_linear_880_5115",x1:"25.9583",y1:"68.7101",x2:"67.2337",y2:"23.7879",gradientUnits:"userSpaceOnUse"},W.createElement("stop",{stopColor:"#9945FF"}),W.createElement("stop",{offset:"0.14",stopColor:"#8A53F4"}),W.createElement("stop",{offset:"0.42",stopColor:"#6377D6"}),W.createElement("stop",{offset:"0.79",stopColor:"#24B0A7"}),W.createElement("stop",{offset:"0.99",stopColor:"#00D18C"}),W.createElement("stop",{offset:"1",stopColor:"#00D18C"})),W.createElement("linearGradient",{id:"paint3_linear_880_5115",x1:"58.3326",y1:"49.4467",x2:"61.0002",y2:"45.4453",gradientUnits:"userSpaceOnUse"},W.createElement("stop",{stopColor:"#9945FF"}),W.createElement("stop",{offset:"0.14",stopColor:"#8A53F4"}),W.createElement("stop",{offset:"0.42",stopColor:"#6377D6"}),W.createElement("stop",{offset:"0.79",stopColor:"#24B0A7"}),W.createElement("stop",{offset:"0.99",stopColor:"#00D18C"}),W.createElement("stop",{offset:"1",stopColor:"#00D18C"})),W.createElement("clipPath",{id:"clip0_880_5115"},W.createElement("rect",{width:"48",height:"48",fill:"white",transform:"translate(24.5 24)"})))),jN=({className:e="",container:t="body"})=>{const n=E.useRef(null),{wallets:r,select:s}=ul(),{setVisible:i}=R0(),[o,l]=E.useState(!1),[c,u]=E.useState(!1),[d,h]=E.useState(null),[y,g]=E.useMemo(()=>{const f=[],x=[],M=[];for(const b of r)b.readyState===V.NotDetected?M.push(b):b.readyState===V.Loadable?x.push(b):b.readyState===V.Installed&&f.push(b);let w=[],v=[];return f.length?(w=f,v=[...x,...M]):(x.length&&(w=x),v=M),[w,v]},[r]),S=E.useCallback(()=>{u(!1),setTimeout(()=>i(!1),150)},[i]),N=E.useCallback(f=>{f.preventDefault(),S()},[S]),A=E.useCallback((f,x)=>{s(x),N(f)},[s,N]),p=E.useCallback(()=>l(!o),[o]),m=E.useCallback(f=>{const x=n.current;if(!x)return;const M=x.querySelectorAll("button"),w=M[0],v=M[M.length-1];f.shiftKey?document.activeElement===w&&(v.focus(),f.preventDefault()):document.activeElement===v&&(w.focus(),f.preventDefault())},[n]);return E.useLayoutEffect(()=>{const f=M=>{M.key==="Escape"?S():M.key==="Tab"&&m(M)},{overflow:x}=window.getComputedStyle(document.body);return setTimeout(()=>u(!0),0),document.body.style.overflow="hidden",window.addEventListener("keydown",f,!1),()=>{document.body.style.overflow=x,window.removeEventListener("keydown",f,!1)}},[S,m]),E.useLayoutEffect(()=>h(document.querySelector(t)),[t]),d&&gg.createPortal(W.createElement("div",{"aria-labelledby":"wallet-adapter-modal-title","aria-modal":"true",className:`wallet-adapter-modal ${c&&"wallet-adapter-modal-fade-in"} ${e}`,ref:n,role:"dialog"},W.createElement("div",{className:"wallet-adapter-modal-container"},W.createElement("div",{className:"wallet-adapter-modal-wrapper"},W.createElement("button",{onClick:N,className:"wallet-adapter-modal-button-close"},W.createElement("svg",{width:"14",height:"14"},W.createElement("path",{d:"M14 12.461 8.3 6.772l5.234-5.233L12.006 0 6.772 5.234 1.54 0 0 1.539l5.234 5.233L0 12.006l1.539 1.528L6.772 8.3l5.69 5.7L14 12.461z"}))),y.length?W.createElement(W.Fragment,null,W.createElement("h1",{className:"wallet-adapter-modal-title"},"Connect a wallet on Solana to continue"),W.createElement("ul",{className:"wallet-adapter-modal-list"},y.map(f=>W.createElement(sc,{key:f.adapter.name,handleClick:x=>A(x,f.adapter.name),wallet:f})),g.length?W.createElement(Hh,{expanded:o,id:"wallet-adapter-modal-collapse"},g.map(f=>W.createElement(sc,{key:f.adapter.name,handleClick:x=>A(x,f.adapter.name),tabIndex:o?0:-1,wallet:f}))):null),g.length?W.createElement("button",{className:"wallet-adapter-modal-list-more",onClick:p,tabIndex:0},W.createElement("span",null,o?"Less ":"More ","options"),W.createElement("svg",{width:"13",height:"7",viewBox:"0 0 13 7",xmlns:"http://www.w3.org/2000/svg",className:`${o?"wallet-adapter-modal-list-more-icon-rotate":""}`},W.createElement("path",{d:"M0.71418 1.626L5.83323 6.26188C5.91574 6.33657 6.0181 6.39652 6.13327 6.43762C6.24844 6.47872 6.37371 6.5 6.50048 6.5C6.62725 6.5 6.75252 6.47872 6.8677 6.43762C6.98287 6.39652 7.08523 6.33657 7.16774 6.26188L12.2868 1.626C12.7753 1.1835 12.3703 0.5 11.6195 0.5H1.37997C0.629216 0.5 0.224175 1.1835 0.71418 1.626Z"}))):null):W.createElement(W.Fragment,null,W.createElement("h1",{className:"wallet-adapter-modal-title"},"You'll need a wallet on Solana to continue"),W.createElement("div",{className:"wallet-adapter-modal-middle"},W.createElement(bN,null)),g.length?W.createElement(W.Fragment,null,W.createElement("button",{className:"wallet-adapter-modal-list-more",onClick:p,tabIndex:0},W.createElement("span",null,o?"Hide ":"Already have a wallet? View ","options"),W.createElement("svg",{width:"13",height:"7",viewBox:"0 0 13 7",xmlns:"http://www.w3.org/2000/svg",className:`${o?"wallet-adapter-modal-list-more-icon-rotate":""}`},W.createElement("path",{d:"M0.71418 1.626L5.83323 6.26188C5.91574 6.33657 6.0181 6.39652 6.13327 6.43762C6.24844 6.47872 6.37371 6.5 6.50048 6.5C6.62725 6.5 6.75252 6.47872 6.8677 6.43762C6.98287 6.39652 7.08523 6.33657 7.16774 6.26188L12.2868 1.626C12.7753 1.1835 12.3703 0.5 11.6195 0.5H1.37997C0.629216 0.5 0.224175 1.1835 0.71418 1.626Z"}))),W.createElement(Hh,{expanded:o,id:"wallet-adapter-modal-collapse"},W.createElement("ul",{className:"wallet-adapter-modal-list"},g.map(f=>W.createElement(sc,{key:f.adapter.name,handleClick:x=>A(x,f.adapter.name),tabIndex:o?0:-1,wallet:f}))))):null))),W.createElement("div",{className:"wallet-adapter-modal-overlay",onMouseDown:N})),d)},_N=({children:e,...t})=>{const[n,r]=E.useState(!1);return W.createElement(P0.Provider,{value:{visible:n,setVisible:r}},e,n&&W.createElement(jN,{...t}))},AN={"change-wallet":"Change wallet",connecting:"Connecting ...","copy-address":"Copy address",copied:"Copied",disconnect:"Disconnect","has-wallet":"Connect","no-wallet":"Select Wallet"};function uf(e){return W.createElement(MN,{...e,labels:AN})}const TN="Phantom";class CN extends Ms{constructor(t={}){super(),this.name=TN,this.url="https://phantom.app",this.icon="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDgiIGhlaWdodD0iMTA4IiB2aWV3Qm94PSIwIDAgMTA4IDEwOCIgZmlsbD0ibm9uZSI+CjxyZWN0IHdpZHRoPSIxMDgiIGhlaWdodD0iMTA4IiByeD0iMjYiIGZpbGw9IiNBQjlGRjIiLz4KPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik00Ni41MjY3IDY5LjkyMjlDNDIuMDA1NCA3Ni44NTA5IDM0LjQyOTIgODUuNjE4MiAyNC4zNDggODUuNjE4MkMxOS41ODI0IDg1LjYxODIgMTUgODMuNjU2MyAxNSA3NS4xMzQyQzE1IDUzLjQzMDUgNDQuNjMyNiAxOS44MzI3IDcyLjEyNjggMTkuODMyN0M4Ny43NjggMTkuODMyNyA5NCAzMC42ODQ2IDk0IDQzLjAwNzlDOTQgNTguODI1OCA4My43MzU1IDc2LjkxMjIgNzMuNTMyMSA3Ni45MTIyQzcwLjI5MzkgNzYuOTEyMiA2OC43MDUzIDc1LjEzNDIgNjguNzA1MyA3Mi4zMTRDNjguNzA1MyA3MS41NzgzIDY4LjgyNzUgNzAuNzgxMiA2OS4wNzE5IDY5LjkyMjlDNjUuNTg5MyA3NS44Njk5IDU4Ljg2ODUgODEuMzg3OCA1Mi41NzU0IDgxLjM4NzhDNDcuOTkzIDgxLjM4NzggNDUuNjcxMyA3OC41MDYzIDQ1LjY3MTMgNzQuNDU5OEM0NS42NzEzIDcyLjk4ODQgNDUuOTc2OCA3MS40NTU2IDQ2LjUyNjcgNjkuOTIyOVpNODMuNjc2MSA0Mi41Nzk0QzgzLjY3NjEgNDYuMTcwNCA4MS41NTc1IDQ3Ljk2NTggNzkuMTg3NSA0Ny45NjU4Qzc2Ljc4MTYgNDcuOTY1OCA3NC42OTg5IDQ2LjE3MDQgNzQuNjk4OSA0Mi41Nzk0Qzc0LjY5ODkgMzguOTg4NSA3Ni43ODE2IDM3LjE5MzEgNzkuMTg3NSAzNy4xOTMxQzgxLjU1NzUgMzcuMTkzMSA4My42NzYxIDM4Ljk4ODUgODMuNjc2MSA0Mi41Nzk0Wk03MC4yMTAzIDQyLjU3OTVDNzAuMjEwMyA0Ni4xNzA0IDY4LjA5MTYgNDcuOTY1OCA2NS43MjE2IDQ3Ljk2NThDNjMuMzE1NyA0Ny45NjU4IDYxLjIzMyA0Ni4xNzA0IDYxLjIzMyA0Mi41Nzk1QzYxLjIzMyAzOC45ODg1IDYzLjMxNTcgMzcuMTkzMSA2NS43MjE2IDM3LjE5MzFDNjguMDkxNiAzNy4xOTMxIDcwLjIxMDMgMzguOTg4NSA3MC4yMTAzIDQyLjU3OTVaIiBmaWxsPSIjRkZGREY4Ii8+Cjwvc3ZnPg==",this.supportedTransactionVersions=new Set(["legacy",0]),this._readyState=typeof window>"u"||typeof document>"u"?V.Unsupported:V.NotDetected,this._disconnected=()=>{const n=this._wallet;n&&(n.off("disconnect",this._disconnected),n.off("accountChanged",this._accountChanged),this._wallet=null,this._publicKey=null,this.emit("error",new Sr),this.emit("disconnect"))},this._accountChanged=n=>{const r=this._publicKey;if(r){try{n=new se(n.toBytes())}catch(s){this.emit("error",new fn(s==null?void 0:s.message,s));return}r.equals(n)||(this._publicKey=n,this.emit("connect",n))}},this._connecting=!1,this._wallet=null,this._publicKey=null,this._readyState!==V.Unsupported&&(Lo()?(this._readyState=V.Loadable,this.emit("readyStateChange",this._readyState)):Bi(()=>{var n,r,s;return(r=(n=window.phantom)==null?void 0:n.solana)!=null&&r.isPhantom||(s=window.solana)!=null&&s.isPhantom?(this._readyState=V.Installed,this.emit("readyStateChange",this._readyState),!0):!1}))}get publicKey(){return this._publicKey}get connecting(){return this._connecting}get readyState(){return this._readyState}async autoConnect(){this.readyState===V.Installed&&await this.connect()}async connect(){var t;try{if(this.connected||this.connecting)return;if(this.readyState===V.Loadable){const s=encodeURIComponent(window.location.href),i=encodeURIComponent(window.location.origin);window.location.href=`https://phantom.app/ul/browse/${s}?ref=${i}`;return}if(this.readyState!==V.Installed)throw new dn;this._connecting=!0;const n=((t=window.phantom)==null?void 0:t.solana)||window.solana;if(!n.isConnected)try{await n.connect()}catch(s){throw new Bn(s==null?void 0:s.message,s)}if(!n.publicKey)throw new $t;let r;try{r=new se(n.publicKey.toBytes())}catch(s){throw new fn(s==null?void 0:s.message,s)}n.on("disconnect",this._disconnected),n.on("accountChanged",this._accountChanged),this._wallet=n,this._publicKey=r,this.emit("connect",r)}catch(n){throw this.emit("error",n),n}finally{this._connecting=!1}}async disconnect(){const t=this._wallet;if(t){t.off("disconnect",this._disconnected),t.off("accountChanged",this._accountChanged),this._wallet=null,this._publicKey=null;try{await t.disconnect()}catch(n){this.emit("error",new Nr(n==null?void 0:n.message,n))}}this.emit("disconnect")}async sendTransaction(t,n,r={}){try{const s=this._wallet;if(!s)throw new ie;try{const{signers:i,...o}=r;Lt(t)?i!=null&&i.length&&t.sign(i):(t=await this.prepareTransaction(t,n,o),i!=null&&i.length&&t.partialSign(...i)),o.preflightCommitment=o.preflightCommitment||n.commitment;const{signature:l}=await s.signAndSendTransaction(t,o);return l}catch(i){throw i instanceof ye?i:new kt(i==null?void 0:i.message,i)}}catch(s){throw this.emit("error",s),s}}async signTransaction(t){try{const n=this._wallet;if(!n)throw new ie;try{return await n.signTransaction(t)||t}catch(r){throw new Be(r==null?void 0:r.message,r)}}catch(n){throw this.emit("error",n),n}}async signAllTransactions(t){try{const n=this._wallet;if(!n)throw new ie;try{return await n.signAllTransactions(t)||t}catch(r){throw new Be(r==null?void 0:r.message,r)}}catch(n){throw this.emit("error",n),n}}async signMessage(t){try{const n=this._wallet;if(!n)throw new ie;try{const{signature:r}=await n.signMessage(t);return r}catch(r){throw new Wi(r==null?void 0:r.message,r)}}catch(n){throw this.emit("error",n),n}}}const IN="modulepreload",kN=function(e){return"/"+e},Yh={},df=function(t,n,r){let s=Promise.resolve();return n&&n.length>0&&(document.getElementsByTagName("link"),s=Promise.all(n.map(i=>{if(i=kN(i),i in Yh)return;Yh[i]=!0;const o=i.endsWith(".css"),l=o?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${i}"]${l}`))return;const c=document.createElement("link");if(c.rel=o?"stylesheet":IN,o||(c.as="script",c.crossOrigin=""),c.href=i,document.head.appendChild(c),o)return new Promise((u,d)=>{c.addEventListener("load",u),c.addEventListener("error",()=>d(new Error(`Unable to preload CSS for ${i}`)))})}))),s.then(()=>t()).catch(i=>{const o=new Event("vite:preloadError",{cancelable:!0});if(o.payload=i,window.dispatchEvent(o),!o.defaultPrevented)throw i})},LN="data:image/svg+xml;base64,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";var oe=function(e,t,n,r){if(n==="a"&&!r)throw new TypeError("Private accessor was defined without a getter");if(typeof t=="function"?e!==t||!r:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return n==="m"?r:n==="a"?r.call(e):r?r.value:t.get(e)},ON=function(e,t,n,r,s){if(r==="m")throw new TypeError("Private method is not writable");if(r==="a"&&!s)throw new TypeError("Private accessor was defined without a setter");if(typeof t=="function"?e!==t||!s:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return r==="a"?s.call(e,n):s?s.value=n:t.set(e,n),n},so,cr,Gu,$u,Hu,He,Yu,W0,B0,Ku,Vu,Qu,Zu,Ju;class PN{constructor(){so.add(this),cr.set(this,{}),Gu.set(this,"1.0.0"),$u.set(this,"MetaMask"),Hu.set(this,LN),He.set(this,null),Yu.set(this,(t,n)=>{var r;return(r=oe(this,cr,"f")[t])!=null&&r.push(n)||(oe(this,cr,"f")[t]=[n]),()=>oe(this,so,"m",B0).call(this,t,n)}),Ku.set(this,async()=>{if(!oe(this,He,"f")){let t;try{t=(await df(()=>import("./index-DY-jIVDY.js"),__vite__mapDeps([0,1]))).default}catch{throw new Error("Unable to load Solflare MetaMask SDK")}ON(this,He,new t,"f"),oe(this,He,"f").on("standard_change",n=>oe(this,so,"m",W0).call(this,"change",n))}return this.accounts.length||await oe(this,He,"f").connect(),{accounts:this.accounts}}),Vu.set(this,async()=>{oe(this,He,"f")&&await oe(this,He,"f").disconnect()}),Qu.set(this,async(...t)=>{if(!oe(this,He,"f"))throw new ie;return await oe(this,He,"f").standardSignAndSendTransaction(...t)}),Zu.set(this,async(...t)=>{if(!oe(this,He,"f"))throw new ie;return await oe(this,He,"f").standardSignTransaction(...t)}),Ju.set(this,async(...t)=>{if(!oe(this,He,"f"))throw new ie;return await oe(this,He,"f").standardSignMessage(...t)})}get version(){return oe(this,Gu,"f")}get name(){return oe(this,$u,"f")}get icon(){return oe(this,Hu,"f")}get chains(){return[rs.SOLANA_MAINNET_CHAIN,rs.SOLANA_DEVNET_CHAIN,rs.SOLANA_TESTNET_CHAIN]}get features(){return{[Ui]:{version:"1.0.0",connect:oe(this,Ku,"f")},[Ii]:{version:"1.0.0",disconnect:oe(this,Vu,"f")},[Fi]:{version:"1.0.0",on:oe(this,Yu,"f")},[Ft]:{version:"1.0.0",supportedTransactionVersions:["legacy",0],signAndSendTransaction:oe(this,Qu,"f")},[Ae]:{version:"1.0.0",supportedTransactionVersions:["legacy",0],signTransaction:oe(this,Zu,"f")},[Xn]:{version:"1.0.0",signMessage:oe(this,Ju,"f")}}}get accounts(){return oe(this,He,"f")?oe(this,He,"f").standardAccounts:[]}}cr=new WeakMap,Gu=new WeakMap,$u=new WeakMap,Hu=new WeakMap,He=new WeakMap,Yu=new WeakMap,Ku=new WeakMap,Vu=new WeakMap,Qu=new WeakMap,Zu=new WeakMap,Ju=new WeakMap,so=new WeakSet,W0=function(t,...n){var r;(r=oe(this,cr,"f")[t])==null||r.forEach(s=>s.apply(null,n))},B0=function(t,n){var r;oe(this,cr,"f")[t]=(r=oe(this,cr,"f")[t])==null?void 0:r.filter(s=>n!==s)};let Kh=!1;function RN(){Kh||(ZS(new PN),Kh=!0)}async function DN(){const e="solflare-detect-metamask";function t(){window.postMessage({target:"metamask-contentscript",data:{name:"metamask-provider",data:{id:e,jsonrpc:"2.0",method:"wallet_getSnaps"}}},window.location.origin)}function n(r){var i,o;const s=r.data;(s==null?void 0:s.target)==="metamask-inpage"&&((i=s.data)==null?void 0:i.name)==="metamask-provider"&&(((o=s.data.data)==null?void 0:o.id)===e?(window.removeEventListener("message",n),s.data.data.error||RN()):t())}window.addEventListener("message",n),window.setTimeout(()=>window.removeEventListener("message",n),5e3),t()}const zN="Solflare";class WN extends Ms{constructor(t={}){super(),this.name=zN,this.url="https://solflare.com",this.icon="data:image/svg+xml;base64,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",this.supportedTransactionVersions=new Set(["legacy",0]),this._readyState=typeof window>"u"||typeof document>"u"?V.Unsupported:V.Loadable,this._disconnected=()=>{const n=this._wallet;n&&(n.off("disconnect",this._disconnected),this._wallet=null,this._publicKey=null,this.emit("error",new Sr),this.emit("disconnect"))},this._accountChanged=n=>{if(!n)return;const r=this._publicKey;if(r){try{n=new se(n.toBytes())}catch(s){this.emit("error",new fn(s==null?void 0:s.message,s));return}r.equals(n)||(this._publicKey=n,this.emit("connect",n))}},this._connecting=!1,this._publicKey=null,this._wallet=null,this._config=t,this._readyState!==V.Unsupported&&(Bi(()=>{var n;return(n=window.solflare)!=null&&n.isSolflare||window.SolflareApp?(this._readyState=V.Installed,this.emit("readyStateChange",this._readyState),!0):!1}),DN())}get publicKey(){return this._publicKey}get connecting(){return this._connecting}get connected(){var t;return!!((t=this._wallet)!=null&&t.connected)}get readyState(){return this._readyState}async autoConnect(){this.readyState===V.Loadable&&Lo()||await this.connect()}async connect(){try{if(this.connected||this.connecting)return;if(this._readyState!==V.Loadable&&this._readyState!==V.Installed)throw new dn;if(this.readyState===V.Loadable&&Lo()){const s=encodeURIComponent(window.location.href),i=encodeURIComponent(window.location.origin);window.location.href=`https://solflare.com/ul/v1/browse/${s}?ref=${i}`;return}let t;try{t=(await df(()=>import("./index-BwgYx0Rk.js"),__vite__mapDeps([2,1]))).default}catch(s){throw new Zd(s==null?void 0:s.message,s)}let n;try{n=new t({network:this._config.network})}catch(s){throw new sr(s==null?void 0:s.message,s)}if(this._connecting=!0,!n.connected)try{await n.connect()}catch(s){throw new Bn(s==null?void 0:s.message,s)}if(!n.publicKey)throw new Bn;let r;try{r=new se(n.publicKey.toBytes())}catch(s){throw new fn(s==null?void 0:s.message,s)}n.on("disconnect",this._disconnected),n.on("accountChanged",this._accountChanged),this._wallet=n,this._publicKey=r,this.emit("connect",r)}catch(t){throw this.emit("error",t),t}finally{this._connecting=!1}}async disconnect(){const t=this._wallet;if(t){t.off("disconnect",this._disconnected),t.off("accountChanged",this._accountChanged),this._wallet=null,this._publicKey=null;try{await t.disconnect()}catch(n){this.emit("error",new Nr(n==null?void 0:n.message,n))}}this.emit("disconnect")}async sendTransaction(t,n,r={}){try{const s=this._wallet;if(!s)throw new ie;try{const{signers:i,...o}=r;return Lt(t)?i!=null&&i.length&&t.sign(i):(t=await this.prepareTransaction(t,n,o),i!=null&&i.length&&t.partialSign(...i)),o.preflightCommitment=o.preflightCommitment||n.commitment,await s.signAndSendTransaction(t,o)}catch(i){throw i instanceof ye?i:new kt(i==null?void 0:i.message,i)}}catch(s){throw this.emit("error",s),s}}async signTransaction(t){try{const n=this._wallet;if(!n)throw new ie;try{return await n.signTransaction(t)||t}catch(r){throw new Be(r==null?void 0:r.message,r)}}catch(n){throw this.emit("error",n),n}}async signAllTransactions(t){try{const n=this._wallet;if(!n)throw new ie;try{return await n.signAllTransactions(t)||t}catch(r){throw new Be(r==null?void 0:r.message,r)}}catch(n){throw this.emit("error",n),n}}async signMessage(t){try{const n=this._wallet;if(!n)throw new ie;try{return await n.signMessage(t,"utf8")}catch(r){throw new Wi(r==null?void 0:r.message,r)}}catch(n){throw this.emit("error",n),n}}}const BN="Backpack";class UN extends Ms{constructor(t={}){super(),this.name=BN,this.url="https://backpack.app",this.icon="data:image/png;base64,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",this.supportedTransactionVersions=null,this._readyState=typeof window>"u"||typeof document>"u"?V.Unsupported:V.NotDetected,this._disconnected=()=>{const n=this._wallet;n&&(n.off("disconnect",this._disconnected),this._wallet=null,this._publicKey=null,this.emit("error",new Sr),this.emit("disconnect"))},this._connecting=!1,this._wallet=null,this._publicKey=null,this._readyState!==V.Unsupported&&Bi(()=>{var n;return(n=window.backpack)!=null&&n.isBackpack?(this._readyState=V.Installed,this.emit("readyStateChange",this._readyState),!0):!1})}get publicKey(){return this._publicKey}get connecting(){return this._connecting}get connected(){var t;return!!((t=this._wallet)!=null&&t.isConnected)}get readyState(){return this._readyState}async connect(){try{if(this.connected||this.connecting)return;if(this._readyState!==V.Installed)throw new dn;this._connecting=!0;const t=window.backpack;try{await t.connect()}catch(r){throw new Bn(r==null?void 0:r.message,r)}if(!t.publicKey)throw new $t;let n;try{n=new se(t.publicKey.toBytes())}catch(r){throw new fn(r==null?void 0:r.message,r)}t.on("disconnect",this._disconnected),this._wallet=t,this._publicKey=n,this.emit("connect",n)}catch(t){throw this.emit("error",t),t}finally{this._connecting=!1}}async disconnect(){const t=this._wallet;if(t){t.off("disconnect",this._disconnected),this._wallet=null,this._publicKey=null;try{await t.disconnect()}catch(n){this.emit("error",new Nr(n==null?void 0:n.message,n))}}this.emit("disconnect")}async sendTransaction(t,n,r={}){try{const s=this._wallet;if(!s)throw new ie;const{signers:i,...o}=r;try{return await s.send(t,i,o,n,this.publicKey)}catch(l){throw new kt(l==null?void 0:l.message,l)}}catch(s){throw this.emit("error",s),s}}async signTransaction(t){try{const n=this._wallet;if(!n)throw new ie;try{return await n.signTransaction(t,this.publicKey)}catch(r){throw new Be(r==null?void 0:r.message,r)}}catch(n){throw this.emit("error",n),n}}async signAllTransactions(t){try{const n=this._wallet;if(!n)throw new ie;try{return await n.signAllTransactions(t,this.publicKey)}catch(r){throw new Be(r==null?void 0:r.message,r)}}catch(n){throw this.emit("error",n),n}}async signMessage(t){try{const n=this._wallet;if(!n)throw new ie;try{return await n.signMessage(t,this.publicKey)}catch(r){throw new Wi(r==null?void 0:r.message,r)}}catch(n){throw this.emit("error",n),n}}}const FN="Coinbase Wallet";class GN extends Ms{constructor(t={}){super(),this.name=FN,this.url="https://chrome.google.com/webstore/detail/coinbase-wallet-extension/hnfanknocfeofbddgcijnmhnfnkdnaad",this.icon="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAyNCIgaGVpZ2h0PSIxMDI0IiB2aWV3Qm94PSIwIDAgMTAyNCAxMDI0IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8Y2lyY2xlIGN4PSI1MTIiIGN5PSI1MTIiIHI9IjUxMiIgZmlsbD0iIzAwNTJGRiIvPgo8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTE1MiA1MTJDMTUyIDcxMC44MjMgMzEzLjE3NyA4NzIgNTEyIDg3MkM3MTAuODIzIDg3MiA4NzIgNzEwLjgyMyA4NzIgNTEyQzg3MiAzMTMuMTc3IDcxMC44MjMgMTUyIDUxMiAxNTJDMzEzLjE3NyAxNTIgMTUyIDMxMy4xNzcgMTUyIDUxMlpNNDIwIDM5NkM0MDYuNzQ1IDM5NiAzOTYgNDA2Ljc0NSAzOTYgNDIwVjYwNEMzOTYgNjE3LjI1NSA0MDYuNzQ1IDYyOCA0MjAgNjI4SDYwNEM2MTcuMjU1IDYyOCA2MjggNjE3LjI1NSA2MjggNjA0VjQyMEM2MjggNDA2Ljc0NSA2MTcuMjU1IDM5NiA2MDQgMzk2SDQyMFoiIGZpbGw9IndoaXRlIi8+Cjwvc3ZnPgo=",this.supportedTransactionVersions=null,this._readyState=typeof window>"u"||typeof document>"u"?V.Unsupported:V.NotDetected,this._disconnected=()=>{const n=this._wallet;n&&(n.off("disconnect",this._disconnected),this._wallet=null,this._publicKey=null,this.emit("error",new Sr),this.emit("disconnect"))},this._connecting=!1,this._wallet=null,this._publicKey=null,this._readyState!==V.Unsupported&&Bi(()=>window!=null&&window.coinbaseSolana?(this._readyState=V.Installed,this.emit("readyStateChange",this._readyState),!0):!1)}get publicKey(){return this._publicKey}get connecting(){return this._connecting}get readyState(){return this._readyState}async connect(){try{if(this.connected||this.connecting)return;if(this._readyState!==V.Installed)throw new dn;this._connecting=!0;const t=window.coinbaseSolana;try{await t.connect()}catch(r){throw new Bn(r==null?void 0:r.message,r)}if(!t.publicKey)throw new $t;let n;try{n=new se(t.publicKey.toBytes())}catch(r){throw new fn(r==null?void 0:r.message,r)}t.on("disconnect",this._disconnected),this._wallet=t,this._publicKey=n,this.emit("connect",n)}catch(t){throw this.emit("error",t),t}finally{this._connecting=!1}}async disconnect(){const t=this._wallet;if(t){t.off("disconnect",this._disconnected),this._wallet=null,this._publicKey=null;try{await t.disconnect()}catch(n){this.emit("error",new Nr(n==null?void 0:n.message,n))}}this.emit("disconnect")}async sendTransaction(t,n,r={}){try{const s=this._wallet;if(!s)throw new ie;try{const{signers:i,...o}=r;t=await this.prepareTransaction(t,n,o),i!=null&&i.length&&t.partialSign(...i),o.preflightCommitment=o.preflightCommitment||n.commitment;const{signature:l}=await s.signAndSendTransaction(t,o);return l}catch(i){throw i instanceof ye?i:new kt(i==null?void 0:i.message,i)}}catch(s){throw this.emit("error",s),s}}async signTransaction(t){try{const n=this._wallet;if(!n)throw new ie;try{return await n.signTransaction(t)||t}catch(r){throw new Be(r==null?void 0:r.message,r)}}catch(n){throw this.emit("error",n),n}}async signAllTransactions(t){try{const n=this._wallet;if(!n)throw new ie;try{return await n.signAllTransactions(t)||t}catch(r){throw new Be(r==null?void 0:r.message,r)}}catch(n){throw this.emit("error",n),n}}async signMessage(t){try{const n=this._wallet;if(!n)throw new ie;try{const{signature:r}=await n.signMessage(t);return r}catch(r){throw new Be(r==null?void 0:r.message,r)}}catch(n){throw this.emit("error",n),n}}}var ff={exports:{}},as=typeof Reflect=="object"?Reflect:null,Vh=as&&typeof as.apply=="function"?as.apply:function(t,n,r){return Function.prototype.apply.call(t,n,r)},io;as&&typeof as.ownKeys=="function"?io=as.ownKeys:Object.getOwnPropertySymbols?io=function(t){return Object.getOwnPropertyNames(t).concat(Object.getOwnPropertySymbols(t))}:io=function(t){return Object.getOwnPropertyNames(t)};function $N(e){console&&console.warn&&console.warn(e)}var U0=Number.isNaN||function(t){return t!==t};function fe(){fe.init.call(this)}ff.exports=fe;ff.exports.once=VN;fe.EventEmitter=fe;fe.prototype._events=void 0;fe.prototype._eventsCount=0;fe.prototype._maxListeners=void 0;var Qh=10;function wl(e){if(typeof e!="function")throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}Object.defineProperty(fe,"defaultMaxListeners",{enumerable:!0,get:function(){return Qh},set:function(e){if(typeof e!="number"||e<0||U0(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");Qh=e}});fe.init=function(){(this._events===void 0||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0};fe.prototype.setMaxListeners=function(t){if(typeof t!="number"||t<0||U0(t))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+t+".");return this._maxListeners=t,this};function F0(e){return e._maxListeners===void 0?fe.defaultMaxListeners:e._maxListeners}fe.prototype.getMaxListeners=function(){return F0(this)};fe.prototype.emit=function(t){for(var n=[],r=1;r<arguments.length;r++)n.push(arguments[r]);var s=t==="error",i=this._events;if(i!==void 0)s=s&&i.error===void 0;else if(!s)return!1;if(s){var o;if(n.length>0&&(o=n[0]),o instanceof Error)throw o;var l=new Error("Unhandled error."+(o?" ("+o.message+")":""));throw l.context=o,l}var c=i[t];if(c===void 0)return!1;if(typeof c=="function")Vh(c,this,n);else for(var u=c.length,d=K0(c,u),r=0;r<u;++r)Vh(d[r],this,n);return!0};function G0(e,t,n,r){var s,i,o;if(wl(n),i=e._events,i===void 0?(i=e._events=Object.create(null),e._eventsCount=0):(i.newListener!==void 0&&(e.emit("newListener",t,n.listener?n.listener:n),i=e._events),o=i[t]),o===void 0)o=i[t]=n,++e._eventsCount;else if(typeof o=="function"?o=i[t]=r?[n,o]:[o,n]:r?o.unshift(n):o.push(n),s=F0(e),s>0&&o.length>s&&!o.warned){o.warned=!0;var l=new Error("Possible EventEmitter memory leak detected. "+o.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");l.name="MaxListenersExceededWarning",l.emitter=e,l.type=t,l.count=o.length,$N(l)}return e}fe.prototype.addListener=function(t,n){return G0(this,t,n,!1)};fe.prototype.on=fe.prototype.addListener;fe.prototype.prependListener=function(t,n){return G0(this,t,n,!0)};function HN(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length===0?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function $0(e,t,n){var r={fired:!1,wrapFn:void 0,target:e,type:t,listener:n},s=HN.bind(r);return s.listener=n,r.wrapFn=s,s}fe.prototype.once=function(t,n){return wl(n),this.on(t,$0(this,t,n)),this};fe.prototype.prependOnceListener=function(t,n){return wl(n),this.prependListener(t,$0(this,t,n)),this};fe.prototype.removeListener=function(t,n){var r,s,i,o,l;if(wl(n),s=this._events,s===void 0)return this;if(r=s[t],r===void 0)return this;if(r===n||r.listener===n)--this._eventsCount===0?this._events=Object.create(null):(delete s[t],s.removeListener&&this.emit("removeListener",t,r.listener||n));else if(typeof r!="function"){for(i=-1,o=r.length-1;o>=0;o--)if(r[o]===n||r[o].listener===n){l=r[o].listener,i=o;break}if(i<0)return this;i===0?r.shift():YN(r,i),r.length===1&&(s[t]=r[0]),s.removeListener!==void 0&&this.emit("removeListener",t,l||n)}return this};fe.prototype.off=fe.prototype.removeListener;fe.prototype.removeAllListeners=function(t){var n,r,s;if(r=this._events,r===void 0)return this;if(r.removeListener===void 0)return arguments.length===0?(this._events=Object.create(null),this._eventsCount=0):r[t]!==void 0&&(--this._eventsCount===0?this._events=Object.create(null):delete r[t]),this;if(arguments.length===0){var i=Object.keys(r),o;for(s=0;s<i.length;++s)o=i[s],o!=="removeListener"&&this.removeAllListeners(o);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if(n=r[t],typeof n=="function")this.removeListener(t,n);else if(n!==void 0)for(s=n.length-1;s>=0;s--)this.removeListener(t,n[s]);return this};function H0(e,t,n){var r=e._events;if(r===void 0)return[];var s=r[t];return s===void 0?[]:typeof s=="function"?n?[s.listener||s]:[s]:n?KN(s):K0(s,s.length)}fe.prototype.listeners=function(t){return H0(this,t,!0)};fe.prototype.rawListeners=function(t){return H0(this,t,!1)};fe.listenerCount=function(e,t){return typeof e.listenerCount=="function"?e.listenerCount(t):Y0.call(e,t)};fe.prototype.listenerCount=Y0;function Y0(e){var t=this._events;if(t!==void 0){var n=t[e];if(typeof n=="function")return 1;if(n!==void 0)return n.length}return 0}fe.prototype.eventNames=function(){return this._eventsCount>0?io(this._events):[]};function K0(e,t){for(var n=new Array(t),r=0;r<t;++r)n[r]=e[r];return n}function YN(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}function KN(e){for(var t=new Array(e.length),n=0;n<t.length;++n)t[n]=e[n].listener||e[n];return t}function VN(e,t){return new Promise(function(n,r){function s(o){e.removeListener(t,i),r(o)}function i(){typeof e.removeListener=="function"&&e.removeListener("error",s),n([].slice.call(arguments))}V0(e,t,i,{once:!0}),t!=="error"&&QN(e,s,{once:!0})})}function QN(e,t,n){typeof e.on=="function"&&V0(e,"error",t,n)}function V0(e,t,n,r){if(typeof e.on=="function")r.once?e.once(t,n):e.on(t,n);else if(typeof e.addEventListener=="function")e.addEventListener(t,function s(i){r.once&&e.removeEventListener(t,s),n(i)});else throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e)}var ZN=ff.exports;const JN=Yo(ZN),Q0={},Z0={},hf=(e,t)=>{Z0[e]=t},k=e=>{class t extends Error{constructor(s,i,o){super(s||e,o);J(this,"cause");if(Object.setPrototypeOf(this,t.prototype),this.name=e,i)for(const l in i)this[l]=i[l];if(o&&qN(o)&&"cause"in o&&!this.cause){const l=o.cause;this.cause=l,"stack"in l&&(this.stack=this.stack+`
CAUSE: `+l.stack)}}}return Q0[e]=t,t};function qN(e){return typeof e=="object"}const J0=e=>{if(e&&typeof e=="object"){try{if(typeof e.message=="string"){const n=JSON.parse(e.message);n.message&&n.name&&(e=n)}}catch{}let t;if(typeof e.name=="string"){const{name:n}=e,r=Z0[n];if(r)t=r(e);else{let s=n==="Error"?Error:Q0[n];s||(console.warn("deserializing an unknown class '"+n+"'"),s=k(n)),t=Object.create(s.prototype);try{for(const i in e)e.hasOwnProperty(i)&&(t[i]=e[i])}catch{}}}else typeof e.message=="string"&&(t=new Error(e.message));return t&&!t.stack&&Error.captureStackTrace&&Error.captureStackTrace(t,J0),t}return new Error(String(e))},XN=e=>e&&(typeof e=="object"?q0(e,[]):typeof e=="function"?`[Function: ${e.name||"anonymous"}]`:e);function q0(e,t){const n={};t.push(e);for(const r of Object.keys(e)){const s=e[r];if(typeof s!="function"){if(!s||typeof s!="object"){n[r]=s;continue}if(t.indexOf(e[r])===-1){n[r]=q0(e[r],t.slice(0));continue}n[r]="[Circular]"}}return typeof e.name=="string"&&(n.name=e.name),typeof e.message=="string"&&(n.message=e.message),typeof e.stack=="string"&&(n.stack=e.stack),n}const e2=k("AccountNameRequired"),t2=k("AccountNotSupported"),n2=k("AccountAwaitingSendPendingOperations"),r2=k("AmountRequired"),s2=k("BluetoothRequired"),i2=k("BtcUnmatchedApp"),a2=k("CantOpenDevice"),o2=k("CashAddrNotSupported"),l2=k("ClaimRewardsFeesWarning"),c2=k("CurrencyNotSupported"),u2=k("DeviceAppVerifyNotSupported"),d2=k("DeviceGenuineSocketEarlyClose"),f2=k("DeviceNotGenuine"),h2=k("DeviceOnDashboardExpected"),m2=k("DeviceOnDashboardUnexpected"),p2=k("DeviceInOSUExpected"),g2=k("DeviceHalted"),y2=k("DeviceNameInvalid"),w2=k("DeviceSocketFail"),v2=k("DeviceSocketNoBulkStatus"),x2=k("DeviceSocketNoBulkStatus"),S2=k("UnresponsiveDeviceError"),N2=k("DisconnectedDevice"),E2=k("DisconnectedDeviceDuringOperation"),M2=k("DeviceExtractOnboardingStateError"),b2=k("DeviceOnboardingStatePollingError"),j2=k("EnpointConfig"),_2=k("EthAppPleaseEnableContractData"),A2=k("FeeEstimationFailed"),T2=k("FirmwareNotRecognized"),C2=k("HardResetFail"),I2=k("InvalidXRPTag"),k2=k("InvalidAddress"),L2=k("InvalidNonce"),O2=k("InvalidAddressBecauseDestinationIsAlsoSource"),P2=k("LatestMCUInstalledError"),R2=k("LatestFirmwareVersionRequired"),D2=k("UnknownMCU"),z2=k("LedgerAPIError"),W2=k("LedgerAPIErrorWithMessage"),B2=k("LedgerAPINotAvailable"),U2=k("ManagerAppAlreadyInstalled"),F2=k("ManagerAppRelyOnBTC"),G2=k("ManagerAppDepInstallRequired"),$2=k("ManagerAppDepUninstallRequired"),H2=k("ManagerDeviceLocked"),Y2=k("ManagerFirmwareNotEnoughSpace"),K2=k("ManagerNotEnoughSpace"),V2=k("ManagerUninstallBTCDep"),Q2=k("NetworkDown"),Z2=k("NetworkError"),J2=k("NoAddressesFound"),q2=k("NotEnoughBalance"),X2=k("NotEnoughBalanceFees"),eE=k("NotEnoughBalanceSwap"),tE=k("NotEnoughBalanceToDelegate"),nE=k("NotEnoughBalanceInParentAccount"),rE=k("NotEnoughSpendableBalance"),sE=k("NotEnoughBalanceBecauseDestinationNotCreated"),iE=k("NoAccessToCamera"),aE=k("NotEnoughGas"),oE=k("NotEnoughGasSwap"),lE=k("TronEmptyAccount"),cE=k("MaybeKeepTronAccountAlive"),uE=k("NotSupportedLegacyAddress"),dE=k("GasLessThanEstimate"),fE=k("PriorityFeeTooLow"),hE=k("PriorityFeeTooHigh"),mE=k("PriorityFeeHigherThanMaxFee"),pE=k("MaxFeeTooLow"),gE=k("PasswordsDontMatch"),yE=k("PasswordIncorrect"),wE=k("RecommendSubAccountsToEmpty"),vE=k("RecommendUndelegation"),xE=k("TimeoutTagged"),SE=k("UnexpectedBootloader"),NE=k("MCUNotGenuineToDashboard"),EE=k("RecipientRequired"),ME=k("UnavailableTezosOriginatedAccountReceive"),bE=k("UnavailableTezosOriginatedAccountSend"),jE=k("UpdateFetchFileFail"),_E=k("UpdateIncorrectHash"),AE=k("UpdateIncorrectSig"),TE=k("UpdateYourApp"),CE=k("UserRefusedDeviceNameChange"),IE=k("UserRefusedAddress"),kE=k("UserRefusedFirmwareUpdate"),LE=k("UserRefusedAllowManager"),OE=k("UserRefusedOnDevice"),PE=k("PinNotSet"),RE=k("ExpertModeRequired"),DE=k("TransportOpenUserCancelled"),zE=k("TransportInterfaceNotAvailable"),X0=k("TransportRaceCondition"),WE=k("TransportWebUSBGestureRequired"),BE=k("TransactionHasBeenValidatedError"),UE=k("TransportExchangeTimeoutError"),FE=k("DeviceShouldStayInApp"),GE=k("WebsocketConnectionError"),$E=k("WebsocketConnectionFailed"),HE=k("WrongDeviceForAccount"),YE=k("WrongDeviceForAccountPayout"),KE=k("WrongDeviceForAccountRefund"),VE=k("WrongAppForCurrency"),QE=k("ETHAddressNonEIP"),ZE=k("CantScanQRCode"),JE=k("FeeNotLoaded"),qE=k("FeeNotLoadedSwap"),XE=k("FeeRequired"),eM=k("FeeTooHigh"),tM=k("PendingOperation"),nM=k("SyncError"),rM=k("PairingFailed"),sM=k("PeerRemovedPairing"),iM=k("GenuineCheckFailed"),aM=k("LedgerAPI4xx"),oM=k("LedgerAPI5xx"),lM=k("FirmwareOrAppUpdateRequired"),cM=k("ReplacementTransactionUnderpriced"),uM=k("OpReturnSizeLimit"),dM=k("DustLimit"),fM=k("LanguageNotFound"),hM=k("NoDBPathGiven"),mM=k("DBWrongPassword"),pM=k("DBNotReset"),gM=k("SequenceNumberError"),yM=k("DisabledTransactionBroadcastError");var qu;(function(e){e.Unknown="Unknown",e.LocationServicesDisabled="LocationServicesDisabled",e.LocationServicesUnauthorized="LocationServicesUnauthorized",e.BluetoothScanStartFailed="BluetoothScanStartFailed"})(qu||(qu={}));class mf extends Error{constructor(n,r){super(r);J(this,"type");this.name="HwTransportError",this.type=n,Object.setPrototypeOf(this,mf.prototype)}}class Zr extends Error{constructor(n,r){const s="TransportError";super(n||s);J(this,"id");this.name=s,this.message=n,this.stack=new Error(n).stack,this.id=r}}hf("TransportError",e=>new Zr(e.message,e.id));const fr={ACCESS_CONDITION_NOT_FULFILLED:38916,ALGORITHM_NOT_SUPPORTED:38020,CLA_NOT_SUPPORTED:28160,CODE_BLOCKED:38976,CODE_NOT_INITIALIZED:38914,COMMAND_INCOMPATIBLE_FILE_STRUCTURE:27009,CONDITIONS_OF_USE_NOT_SATISFIED:27013,CONTRADICTION_INVALIDATION:38928,CONTRADICTION_SECRET_CODE_STATUS:38920,DEVICE_IN_RECOVERY_MODE:26159,CUSTOM_IMAGE_EMPTY:26158,FILE_ALREADY_EXISTS:27273,FILE_NOT_FOUND:37892,GP_AUTH_FAILED:25344,HALTED:28586,INCONSISTENT_FILE:37896,INCORRECT_DATA:27264,INCORRECT_LENGTH:26368,INCORRECT_P1_P2:27392,INS_NOT_SUPPORTED:27904,DEVICE_NOT_ONBOARDED:27911,DEVICE_NOT_ONBOARDED_2:26129,INVALID_KCV:38021,INVALID_OFFSET:37890,LICENSING:28482,LOCKED_DEVICE:21781,MAX_VALUE_REACHED:38992,MEMORY_PROBLEM:37440,MISSING_CRITICAL_PARAMETER:26624,NO_EF_SELECTED:37888,NOT_ENOUGH_MEMORY_SPACE:27268,OK:36864,PIN_REMAINING_ATTEMPTS:25536,REFERENCED_DATA_NOT_FOUND:27272,SECURITY_STATUS_NOT_SATISFIED:27010,TECHNICAL_PROBLEM:28416,UNKNOWN_APDU:27906,USER_REFUSED_ON_DEVICE:21761,NOT_ENOUGH_SPACE:20738,APP_NOT_FOUND_OR_INVALID_CONTEXT:20771,INVALID_APP_NAME_LENGTH:26378,GEN_AES_KEY_FAILED:21529,INTERNAL_CRYPTO_OPERATION_FAILED:21530,INTERNAL_COMPUTE_AES_CMAC_FAILED:21531,ENCRYPT_APP_STORAGE_FAILED:21532,INVALID_BACKUP_STATE:26178,PIN_NOT_SET:21762,INVALID_BACKUP_LENGTH:26419,INVALID_RESTORE_STATE:26179,INVALID_CHUNK_LENGTH:26420,INVALID_BACKUP_HEADER:26698,TRUSTCHAIN_WRONG_SEED:45063};function ey(e){switch(e){case 26368:return"Incorrect length";case 26624:return"Missing critical parameter";case 27010:return"Security not satisfied (dongle locked or have invalid access rights)";case 27013:return"Condition of use not satisfied (denied by the user?)";case 27264:return"Invalid data received";case 27392:return"Invalid parameter received";case 21781:return"Locked device"}if(28416<=e&&e<=28671)return"Internal error, please report"}class Er extends Error{constructor(n,{canBeMappedToChildError:r=!0}={}){const s=Object.keys(fr).find(c=>fr[c]===n)||"UNKNOWN_ERROR",i=ey(n)||s,o=n.toString(16),l=`Ledger device: ${i} (0x${o})`;super(l);J(this,"statusCode");J(this,"statusText");if(this.name="TransportStatusError",this.statusCode=n,this.statusText=s,Object.setPrototypeOf(this,Er.prototype),r&&n===fr.LOCKED_DEVICE)return new vl(l)}}class vl extends Er{constructor(t){super(fr.LOCKED_DEVICE,{canBeMappedToChildError:!1}),t&&(this.message=t),this.name="LockedDeviceError",Object.setPrototypeOf(this,vl.prototype)}}class pf extends Error{constructor(t,n){super(n),this.name=t,Object.setPrototypeOf(this,pf.prototype)}}hf("TransportStatusError",e=>new Er(e.statusCode));const qb=Object.freeze(Object.defineProperty({__proto__:null,AccountAwaitingSendPendingOperations:n2,AccountNameRequiredError:e2,AccountNotSupported:t2,AmountRequired:r2,BluetoothRequired:s2,BtcUnmatchedApp:i2,CantOpenDevice:a2,CantScanQRCode:ZE,CashAddrNotSupported:o2,ClaimRewardsFeesWarning:l2,CurrencyNotSupported:c2,DBNotReset:pM,DBWrongPassword:mM,DeviceAppVerifyNotSupported:u2,DeviceExtractOnboardingStateError:M2,DeviceGenuineSocketEarlyClose:d2,DeviceHalted:g2,DeviceInOSUExpected:p2,DeviceMangementKitError:pf,DeviceNameInvalid:y2,DeviceNeedsRestart:x2,DeviceNotGenuineError:f2,DeviceOnDashboardExpected:h2,DeviceOnDashboardUnexpected:m2,DeviceOnboardingStatePollingError:b2,DeviceShouldStayInApp:FE,DeviceSocketFail:w2,DeviceSocketNoBulkStatus:v2,DisabledTransactionBroadcastError:yM,DisconnectedDevice:N2,DisconnectedDeviceDuringOperation:E2,DustLimit:dM,ETHAddressNonEIP:QE,EnpointConfigError:j2,EthAppPleaseEnableContractData:_2,ExpertModeRequired:RE,FeeEstimationFailed:A2,FeeNotLoaded:JE,FeeNotLoadedSwap:qE,FeeRequired:XE,FeeTooHigh:eM,FirmwareNotRecognized:T2,FirmwareOrAppUpdateRequired:lM,GasLessThanEstimate:dE,GenuineCheckFailed:iM,HardResetFail:C2,HwTransportError:mf,get HwTransportErrorType(){return qu},InvalidAddress:k2,InvalidAddressBecauseDestinationIsAlsoSource:O2,InvalidNonce:L2,InvalidXRPTag:I2,LanguageNotFound:fM,LatestFirmwareVersionRequired:R2,LatestMCUInstalledError:P2,LedgerAPI4xx:aM,LedgerAPI5xx:oM,LedgerAPIError:z2,LedgerAPIErrorWithMessage:W2,LedgerAPINotAvailable:B2,LockedDeviceError:vl,MCUNotGenuineToDashboard:NE,ManagerAppAlreadyInstalledError:U2,ManagerAppDepInstallRequired:G2,ManagerAppDepUninstallRequired:$2,ManagerAppRelyOnBTCError:F2,ManagerDeviceLockedError:H2,ManagerFirmwareNotEnoughSpaceError:Y2,ManagerNotEnoughSpaceError:K2,ManagerUninstallBTCDep:V2,MaxFeeTooLow:pE,MaybeKeepTronAccountAlive:cE,NetworkDown:Q2,NetworkError:Z2,NoAccessToCamera:iE,NoAddressesFound:J2,NoDBPathGiven:hM,NotEnoughBalance:q2,NotEnoughBalanceBecauseDestinationNotCreated:sE,NotEnoughBalanceFees:X2,NotEnoughBalanceInParentAccount:nE,NotEnoughBalanceSwap:eE,NotEnoughBalanceToDelegate:tE,NotEnoughGas:aE,NotEnoughGasSwap:oE,NotEnoughSpendableBalance:rE,NotSupportedLegacyAddress:uE,OpReturnDataSizeLimit:uM,PairingFailed:rM,PasswordIncorrectError:yE,PasswordsDontMatchError:gE,PeerRemovedPairing:sM,PendingOperation:tM,PinNotSet:PE,PriorityFeeHigherThanMaxFee:mE,PriorityFeeTooHigh:hE,PriorityFeeTooLow:fE,RecipientRequired:EE,RecommendSubAccountsToEmpty:wE,RecommendUndelegation:vE,ReplacementTransactionUnderpriced:cM,SequenceNumberError:gM,StatusCodes:fr,SyncError:nM,TimeoutTagged:xE,TransactionHasBeenValidatedError:BE,TransportError:Zr,TransportExchangeTimeoutError:UE,TransportInterfaceNotAvailable:zE,TransportOpenUserCancelled:DE,TransportRaceCondition:X0,TransportStatusError:Er,TransportWebUSBGestureRequired:WE,TronEmptyAccount:lE,UnavailableTezosOriginatedAccountReceive:ME,UnavailableTezosOriginatedAccountSend:bE,UnexpectedBootloader:SE,UnknownMCU:D2,UnresponsiveDeviceError:S2,UpdateFetchFileFail:jE,UpdateIncorrectHash:_E,UpdateIncorrectSig:AE,UpdateYourApp:TE,UserRefusedAddress:IE,UserRefusedAllowManager:LE,UserRefusedDeviceNameChange:CE,UserRefusedFirmwareUpdate:kE,UserRefusedOnDevice:OE,WebsocketConnectionError:GE,WebsocketConnectionFailed:$E,WrongAppForCurrency:VE,WrongDeviceForAccount:HE,WrongDeviceForAccountPayout:YE,WrongDeviceForAccountRefund:KE,addCustomErrorDeserializer:hf,createCustomErrorClass:k,deserializeError:J0,getAltStatusMessage:ey,serializeError:XN},Symbol.toStringTag,{value:"Module"}));var ic=function(e,t,n,r){function s(i){return i instanceof n?i:new n(function(o){o(i)})}return new(n||(n=Promise))(function(i,o){function l(d){try{u(r.next(d))}catch(h){o(h)}}function c(d){try{u(r.throw(d))}catch(h){o(h)}}function u(d){d.done?i(d.value):s(d.value).then(l,c)}u((r=r.apply(e,t||[])).next())})},ac=function(e,t){var n={label:0,sent:function(){if(i[0]&1)throw i[1];return i[1]},trys:[],ops:[]},r,s,i,o;return o={next:l(0),throw:l(1),return:l(2)},typeof Symbol=="function"&&(o[Symbol.iterator]=function(){return this}),o;function l(u){return function(d){return c([u,d])}}function c(u){if(r)throw new TypeError("Generator is already executing.");for(;n;)try{if(r=1,s&&(i=u[0]&2?s.return:u[0]?s.throw||((i=s.return)&&i.call(s),0):s.next)&&!(i=i.call(s,u[1])).done)return i;switch(s=0,i&&(u=[u[0]&2,i.value]),u[0]){case 0:case 1:i=u;break;case 4:return n.label++,{value:u[1],done:!1};case 5:n.label++,s=u[1],u=[0];continue;case 7:u=n.ops.pop(),n.trys.pop();continue;default:if(i=n.trys,!(i=i.length>0&&i[i.length-1])&&(u[0]===6||u[0]===2)){n=0;continue}if(u[0]===3&&(!i||u[1]>i[0]&&u[1]<i[3])){n.label=u[1];break}if(u[0]===6&&n.label<i[1]){n.label=i[1],i=u;break}if(i&&n.label<i[2]){n.label=i[2],n.ops.push(u);break}i[2]&&n.ops.pop(),n.trys.pop();continue}u=t.call(e,n)}catch(d){u=[6,d],s=0}finally{r=i=0}if(u[0]&5)throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}},wM=function(e,t){var n=typeof Symbol=="function"&&e[Symbol.iterator];if(!n)return e;var r=n.call(e),s,i=[],o;try{for(;(t===void 0||t-- >0)&&!(s=r.next()).done;)i.push(s.value)}catch(l){o={error:l}}finally{try{s&&!s.done&&(n=r.return)&&n.call(r)}finally{if(o)throw o.error}}return i},vM=function(e,t,n){if(n||arguments.length===2)for(var r=0,s=t.length,i;r<s;r++)(i||!(r in t))&&(i||(i=Array.prototype.slice.call(t,0,r)),i[r]=t[r]);return e.concat(i||Array.prototype.slice.call(t))},xM=function(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},Xb=function(){function e(){var t=this;this.exchangeTimeout=3e4,this.unresponsiveTimeout=15e3,this.deviceModel=null,this._events=new JN,this.send=function(n,r,s,i,o,l){return o===void 0&&(o=Buffer.alloc(0)),l===void 0&&(l=[fr.OK]),ic(t,void 0,void 0,function(){var c,u;return ac(this,function(d){switch(d.label){case 0:if(o.length>=256)throw new Zr("data.length exceed 256 bytes limit. Got: "+o.length,"DataLengthTooBig");return[4,this.exchange(Buffer.concat([Buffer.from([n,r,s,i]),Buffer.from([o.length]),o]))];case 1:if(c=d.sent(),u=c.readUInt16BE(c.length-2),!l.some(function(h){return h===u}))throw new Er(u);return[2,c]}})})},this.exchangeAtomicImpl=function(n){return ic(t,void 0,void 0,function(){var r,s,i,o,l,c=this;return ac(this,function(u){switch(u.label){case 0:if(this.exchangeBusyPromise)throw new X0("An action was already pending on the Ledger device. Please deny or reconnect.");s=new Promise(function(d){r=d}),this.exchangeBusyPromise=s,i=!1,o=setTimeout(function(){i=!0,c.emit("unresponsive")},this.unresponsiveTimeout),u.label=1;case 1:return u.trys.push([1,,3,4]),[4,n()];case 2:return l=u.sent(),i&&this.emit("responsive"),[2,l];case 3:return clearTimeout(o),r&&r(),this.exchangeBusyPromise=null,[7];case 4:return[2]}})})},this._appAPIlock=null}return e.prototype.exchange=function(t){throw new Error("exchange not implemented")},e.prototype.setScrambleKey=function(t){},e.prototype.close=function(){return Promise.resolve()},e.prototype.on=function(t,n){this._events.on(t,n)},e.prototype.off=function(t,n){this._events.removeListener(t,n)},e.prototype.emit=function(t){for(var n,r=[],s=1;s<arguments.length;s++)r[s-1]=arguments[s];(n=this._events).emit.apply(n,vM([t],wM(r),!1))},e.prototype.setDebugMode=function(){console.warn("setDebugMode is deprecated. use @ledgerhq/logs instead. No logs are emitted in this anymore.")},e.prototype.setExchangeTimeout=function(t){this.exchangeTimeout=t},e.prototype.setExchangeUnresponsiveTimeout=function(t){this.unresponsiveTimeout=t},e.create=function(t,n){var r=this;return t===void 0&&(t=3e3),new Promise(function(s,i){var o=!1,l=r.listen({next:function(u){o=!0,l&&l.unsubscribe(),c&&clearTimeout(c),r.open(u.descriptor,t).then(s,i)},error:function(u){c&&clearTimeout(c),i(u)},complete:function(){c&&clearTimeout(c),o||i(new Zr(r.ErrorMessage_NoDeviceFound,"NoDeviceFound"))}}),c=n?setTimeout(function(){l.unsubscribe(),i(new Zr(r.ErrorMessage_ListenTimeout,"ListenTimeout"))},n):null})},e.prototype.decorateAppAPIMethods=function(t,n,r){var s,i;try{for(var o=xM(n),l=o.next();!l.done;l=o.next()){var c=l.value;t[c]=this.decorateAppAPIMethod(c,t[c],t,r)}}catch(u){s={error:u}}finally{try{l&&!l.done&&(i=o.return)&&i.call(o)}finally{if(s)throw s.error}}},e.prototype.decorateAppAPIMethod=function(t,n,r,s){var i=this;return function(){for(var o=[],l=0;l<arguments.length;l++)o[l]=arguments[l];return ic(i,void 0,void 0,function(){var c;return ac(this,function(u){switch(u.label){case 0:if(c=this._appAPIlock,c)return[2,Promise.reject(new Zr("Ledger Device is busy (lock "+c+")","TransportLocked"))];u.label=1;case 1:return u.trys.push([1,,3,4]),this._appAPIlock=t,this.setScrambleKey(s),[4,n.apply(r,o)];case 2:return[2,u.sent()];case 3:return this._appAPIlock=null,[7];case 4:return[2]}})})}},e.ErrorMessage_ListenTimeout="No Ledger device found (timeout)",e.ErrorMessage_NoDeviceFound="No Ledger device found",e}();function SM(e,t){const r=Buffer.alloc(17);let s=r.writeUInt8(4,0);return s=r.writeUInt32BE(pa(44),s),s=r.writeUInt32BE(pa(501),s),s=r.writeUInt32BE(pa(e),s),r.writeUInt32BE(pa(t),s),r}const NM=1<<31>>>0;function pa(e){return(e|NM)>>>0}const EM=5,MM=6,bM=0,jM=1,_M=1,AM=2,ga=255,Zh=224;async function TM(e,t){const n=await ty(e,EM,bM,t);return new se(n)}async function CM(e,t,n){const r=Buffer.alloc(1);r.writeUInt8(1,0);const s=Lt(t)?t.message.serialize():t.serializeMessage(),i=Buffer.concat([r,n,s]);return await ty(e,MM,jM,i)}async function ty(e,t,n,r){let s=0,i=0;if(r.length>ga)for(;r.length-i>ga;){const c=r.slice(i,i+ga);if((await e.send(Zh,t,n,s|AM,c)).length!==2)throw new Er(fr.INCORRECT_DATA);s|=_M,i+=ga}const o=r.slice(i),l=await e.send(Zh,t,n,s,o);return l.slice(0,l.length-2)}const IM="Ledger";class kM extends qd{constructor(t={}){super(),this.name=IM,this.url="https://ledger.com",this.icon="data:image/svg+xml;base64,PHN2ZyB2aWV3Qm94PSIwIDAgMzUgMzUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0iI2ZmZiI+PHBhdGggZD0ibTIzLjU4OCAwaC0xNnYyMS41ODNoMjEuNnYtMTZhNS41ODUgNS41ODUgMCAwIDAgLTUuNi01LjU4M3oiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDUuNzM5KSIvPjxwYXRoIGQ9Im04LjM0MiAwaC0yLjc1N2E1LjU4NSA1LjU4NSAwIDAgMCAtNS41ODUgNS41ODV2Mi43NTdoOC4zNDJ6Ii8+PHBhdGggZD0ibTAgNy41OWg4LjM0MnY4LjM0MmgtOC4zNDJ6IiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwIDUuNzM5KSIvPjxwYXRoIGQ9Im0xNS4xOCAyMy40NTFoMi43NTdhNS41ODUgNS41ODUgMCAwIDAgNS41ODUtNS42di0yLjY3MWgtOC4zNDJ6IiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgxMS40NzggMTEuNDc4KSIvPjxwYXRoIGQ9Im03LjU5IDE1LjE4aDguMzQydjguMzQyaC04LjM0MnoiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDUuNzM5IDExLjQ3OCkiLz48cGF0aCBkPSJtMCAxNS4xOHYyLjc1N2E1LjU4NSA1LjU4NSAwIDAgMCA1LjU4NSA1LjU4NWgyLjc1N3YtOC4zNDJ6IiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwIDExLjQ3OCkiLz48L2c+PC9zdmc+",this.supportedTransactionVersions=new Set(["legacy",0]),this._readyState=typeof window>"u"||typeof document>"u"||typeof navigator>"u"||!navigator.hid?V.Unsupported:V.Loadable,this._disconnected=()=>{const n=this._transport;n&&(n.off("disconnect",this._disconnected),this._transport=null,this._publicKey=null,this.emit("error",new Sr),this.emit("disconnect"))},this._derivationPath=t.derivationPath||SM(0,0),this._connecting=!1,this._transport=null,this._publicKey=null}get publicKey(){return this._publicKey}get connecting(){return this._connecting}get readyState(){return this._readyState}async connect(){try{if(this.connected||this.connecting)return;if(this._readyState!==V.Loadable)throw new dn;this._connecting=!0;let t;try{t=(await df(()=>import("./TransportWebHID-DUFM2z6P.js"),__vite__mapDeps([3,1]))).default}catch(s){throw new Zd(s==null?void 0:s.message,s)}let n;try{n=await t.create()}catch(s){throw new Bn(s==null?void 0:s.message,s)}let r;try{r=await TM(n,this._derivationPath)}catch(s){throw new fn(s==null?void 0:s.message,s)}n.on("disconnect",this._disconnected),this._transport=n,this._publicKey=r,this.emit("connect",r)}catch(t){throw this.emit("error",t),t}finally{this._connecting=!1}}async disconnect(){const t=this._transport;if(t){t.off("disconnect",this._disconnected),this._transport=null,this._publicKey=null;try{await t.close()}catch(n){this.emit("error",new Nr(n==null?void 0:n.message,n))}}this.emit("disconnect")}async signTransaction(t){try{const n=this._transport,r=this._publicKey;if(!n||!r)throw new ie;try{const s=await CM(n,t,this._derivationPath);t.addSignature(r,s)}catch(s){throw new Be(s==null?void 0:s.message,s)}return t}catch(n){throw this.emit("error",n),n}}}var ny={exports:{}};/**
 * [js-sha256]{@link https://github.com/emn178/js-sha256}
 *
 * @version 0.11.0
 * <AUTHOR> Yi-Cyuan [<EMAIL>]
 * @copyright Chen, Yi-Cyuan 2014-2024
 * @license MIT
 */(function(e){(function(){var t="input is invalid type",n=typeof window=="object",r=n?window:{};r.JS_SHA256_NO_WINDOW&&(n=!1);var s=!n&&typeof self=="object",i=!r.JS_SHA256_NO_NODE_JS&&typeof process=="object"&&process.versions&&process.versions.node;i?r=globalThis:s&&(r=self);var o=!r.JS_SHA256_NO_COMMON_JS&&!0&&e.exports,l=!r.JS_SHA256_NO_ARRAY_BUFFER&&typeof ArrayBuffer<"u",c="0123456789abcdef".split(""),u=[-**********,8388608,32768,128],d=[24,16,8,0],h=[**********,**********,**********,**********,961987163,**********,**********,**********,**********,310598401,607225278,**********,**********,**********,**********,**********,**********,**********,264347078,604807628,770255983,**********,**********,**********,**********,**********,**********,**********,**********,**********,113926993,338241895,666307205,773529912,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],y=["hex","array","digest","arrayBuffer"],g=[];(r.JS_SHA256_NO_NODE_JS||!Array.isArray)&&(Array.isArray=function(w){return Object.prototype.toString.call(w)==="[object Array]"}),l&&(r.JS_SHA256_NO_ARRAY_BUFFER_IS_VIEW||!ArrayBuffer.isView)&&(ArrayBuffer.isView=function(w){return typeof w=="object"&&w.buffer&&w.buffer.constructor===ArrayBuffer});var S=function(w,v){return function(b){return new f(v,!0).update(b)[w]()}},N=function(w){var v=S("hex",w);i&&(v=A(v,w)),v.create=function(){return new f(w)},v.update=function(C){return v.create().update(C)};for(var b=0;b<y.length;++b){var T=y[b];v[T]=S(T,w)}return v},A=function(w,v){var b=wf,T=wf.Buffer,C=v?"sha224":"sha256",O;T.from&&!r.JS_SHA256_NO_BUFFER_FROM?O=T.from:O=function(I){return new T(I)};var D=function(I){if(typeof I=="string")return b.createHash(C).update(I,"utf8").digest("hex");if(I==null)throw new Error(t);return I.constructor===ArrayBuffer&&(I=new Uint8Array(I)),Array.isArray(I)||ArrayBuffer.isView(I)||I.constructor===T?b.createHash(C).update(O(I)).digest("hex"):w(I)};return D},p=function(w,v){return function(b,T){return new x(b,v,!0).update(T)[w]()}},m=function(w){var v=p("hex",w);v.create=function(C){return new x(C,w)},v.update=function(C,O){return v.create(C).update(O)};for(var b=0;b<y.length;++b){var T=y[b];v[T]=p(T,w)}return v};function f(w,v){v?(g[0]=g[16]=g[1]=g[2]=g[3]=g[4]=g[5]=g[6]=g[7]=g[8]=g[9]=g[10]=g[11]=g[12]=g[13]=g[14]=g[15]=0,this.blocks=g):this.blocks=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],w?(this.h0=3238371032,this.h1=914150663,this.h2=812702999,this.h3=4144912697,this.h4=4290775857,this.h5=1750603025,this.h6=1694076839,this.h7=3204075428):(this.h0=1779033703,this.h1=3144134277,this.h2=1013904242,this.h3=2773480762,this.h4=1359893119,this.h5=2600822924,this.h6=528734635,this.h7=1541459225),this.block=this.start=this.bytes=this.hBytes=0,this.finalized=this.hashed=!1,this.first=!0,this.is224=w}f.prototype.update=function(w){if(!this.finalized){var v,b=typeof w;if(b!=="string"){if(b==="object"){if(w===null)throw new Error(t);if(l&&w.constructor===ArrayBuffer)w=new Uint8Array(w);else if(!Array.isArray(w)&&(!l||!ArrayBuffer.isView(w)))throw new Error(t)}else throw new Error(t);v=!0}for(var T,C=0,O,D=w.length,I=this.blocks;C<D;){if(this.hashed&&(this.hashed=!1,I[0]=this.block,this.block=I[16]=I[1]=I[2]=I[3]=I[4]=I[5]=I[6]=I[7]=I[8]=I[9]=I[10]=I[11]=I[12]=I[13]=I[14]=I[15]=0),v)for(O=this.start;C<D&&O<64;++C)I[O>>>2]|=w[C]<<d[O++&3];else for(O=this.start;C<D&&O<64;++C)T=w.charCodeAt(C),T<128?I[O>>>2]|=T<<d[O++&3]:T<2048?(I[O>>>2]|=(192|T>>>6)<<d[O++&3],I[O>>>2]|=(128|T&63)<<d[O++&3]):T<55296||T>=57344?(I[O>>>2]|=(224|T>>>12)<<d[O++&3],I[O>>>2]|=(128|T>>>6&63)<<d[O++&3],I[O>>>2]|=(128|T&63)<<d[O++&3]):(T=65536+((T&1023)<<10|w.charCodeAt(++C)&1023),I[O>>>2]|=(240|T>>>18)<<d[O++&3],I[O>>>2]|=(128|T>>>12&63)<<d[O++&3],I[O>>>2]|=(128|T>>>6&63)<<d[O++&3],I[O>>>2]|=(128|T&63)<<d[O++&3]);this.lastByteIndex=O,this.bytes+=O-this.start,O>=64?(this.block=I[16],this.start=O-64,this.hash(),this.hashed=!0):this.start=O}return this.bytes>4294967295&&(this.hBytes+=this.bytes/**********<<0,this.bytes=this.bytes%**********),this}},f.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var w=this.blocks,v=this.lastByteIndex;w[16]=this.block,w[v>>>2]|=u[v&3],this.block=w[16],v>=56&&(this.hashed||this.hash(),w[0]=this.block,w[16]=w[1]=w[2]=w[3]=w[4]=w[5]=w[6]=w[7]=w[8]=w[9]=w[10]=w[11]=w[12]=w[13]=w[14]=w[15]=0),w[14]=this.hBytes<<3|this.bytes>>>29,w[15]=this.bytes<<3,this.hash()}},f.prototype.hash=function(){var w=this.h0,v=this.h1,b=this.h2,T=this.h3,C=this.h4,O=this.h5,D=this.h6,I=this.h7,F=this.blocks,_,R,z,L,P,U,$,Y,ae,Q,Re;for(_=16;_<64;++_)P=F[_-15],R=(P>>>7|P<<25)^(P>>>18|P<<14)^P>>>3,P=F[_-2],z=(P>>>17|P<<15)^(P>>>19|P<<13)^P>>>10,F[_]=F[_-16]+R+F[_-7]+z<<0;for(Re=v&b,_=0;_<64;_+=4)this.first?(this.is224?(Y=300032,P=F[0]-1413257819,I=P-150054599<<0,T=P+24177077<<0):(Y=704751109,P=F[0]-210244248,I=P-1521486534<<0,T=P+143694565<<0),this.first=!1):(R=(w>>>2|w<<30)^(w>>>13|w<<19)^(w>>>22|w<<10),z=(C>>>6|C<<26)^(C>>>11|C<<21)^(C>>>25|C<<7),Y=w&v,L=Y^w&b^Re,$=C&O^~C&D,P=I+z+$+h[_]+F[_],U=R+L,I=T+P<<0,T=P+U<<0),R=(T>>>2|T<<30)^(T>>>13|T<<19)^(T>>>22|T<<10),z=(I>>>6|I<<26)^(I>>>11|I<<21)^(I>>>25|I<<7),ae=T&w,L=ae^T&v^Y,$=I&C^~I&O,P=D+z+$+h[_+1]+F[_+1],U=R+L,D=b+P<<0,b=P+U<<0,R=(b>>>2|b<<30)^(b>>>13|b<<19)^(b>>>22|b<<10),z=(D>>>6|D<<26)^(D>>>11|D<<21)^(D>>>25|D<<7),Q=b&T,L=Q^b&w^ae,$=D&I^~D&C,P=O+z+$+h[_+2]+F[_+2],U=R+L,O=v+P<<0,v=P+U<<0,R=(v>>>2|v<<30)^(v>>>13|v<<19)^(v>>>22|v<<10),z=(O>>>6|O<<26)^(O>>>11|O<<21)^(O>>>25|O<<7),Re=v&b,L=Re^v&T^Q,$=O&D^~O&I,P=C+z+$+h[_+3]+F[_+3],U=R+L,C=w+P<<0,w=P+U<<0,this.chromeBugWorkAround=!0;this.h0=this.h0+w<<0,this.h1=this.h1+v<<0,this.h2=this.h2+b<<0,this.h3=this.h3+T<<0,this.h4=this.h4+C<<0,this.h5=this.h5+O<<0,this.h6=this.h6+D<<0,this.h7=this.h7+I<<0},f.prototype.hex=function(){this.finalize();var w=this.h0,v=this.h1,b=this.h2,T=this.h3,C=this.h4,O=this.h5,D=this.h6,I=this.h7,F=c[w>>>28&15]+c[w>>>24&15]+c[w>>>20&15]+c[w>>>16&15]+c[w>>>12&15]+c[w>>>8&15]+c[w>>>4&15]+c[w&15]+c[v>>>28&15]+c[v>>>24&15]+c[v>>>20&15]+c[v>>>16&15]+c[v>>>12&15]+c[v>>>8&15]+c[v>>>4&15]+c[v&15]+c[b>>>28&15]+c[b>>>24&15]+c[b>>>20&15]+c[b>>>16&15]+c[b>>>12&15]+c[b>>>8&15]+c[b>>>4&15]+c[b&15]+c[T>>>28&15]+c[T>>>24&15]+c[T>>>20&15]+c[T>>>16&15]+c[T>>>12&15]+c[T>>>8&15]+c[T>>>4&15]+c[T&15]+c[C>>>28&15]+c[C>>>24&15]+c[C>>>20&15]+c[C>>>16&15]+c[C>>>12&15]+c[C>>>8&15]+c[C>>>4&15]+c[C&15]+c[O>>>28&15]+c[O>>>24&15]+c[O>>>20&15]+c[O>>>16&15]+c[O>>>12&15]+c[O>>>8&15]+c[O>>>4&15]+c[O&15]+c[D>>>28&15]+c[D>>>24&15]+c[D>>>20&15]+c[D>>>16&15]+c[D>>>12&15]+c[D>>>8&15]+c[D>>>4&15]+c[D&15];return this.is224||(F+=c[I>>>28&15]+c[I>>>24&15]+c[I>>>20&15]+c[I>>>16&15]+c[I>>>12&15]+c[I>>>8&15]+c[I>>>4&15]+c[I&15]),F},f.prototype.toString=f.prototype.hex,f.prototype.digest=function(){this.finalize();var w=this.h0,v=this.h1,b=this.h2,T=this.h3,C=this.h4,O=this.h5,D=this.h6,I=this.h7,F=[w>>>24&255,w>>>16&255,w>>>8&255,w&255,v>>>24&255,v>>>16&255,v>>>8&255,v&255,b>>>24&255,b>>>16&255,b>>>8&255,b&255,T>>>24&255,T>>>16&255,T>>>8&255,T&255,C>>>24&255,C>>>16&255,C>>>8&255,C&255,O>>>24&255,O>>>16&255,O>>>8&255,O&255,D>>>24&255,D>>>16&255,D>>>8&255,D&255];return this.is224||F.push(I>>>24&255,I>>>16&255,I>>>8&255,I&255),F},f.prototype.array=f.prototype.digest,f.prototype.arrayBuffer=function(){this.finalize();var w=new ArrayBuffer(this.is224?28:32),v=new DataView(w);return v.setUint32(0,this.h0),v.setUint32(4,this.h1),v.setUint32(8,this.h2),v.setUint32(12,this.h3),v.setUint32(16,this.h4),v.setUint32(20,this.h5),v.setUint32(24,this.h6),this.is224||v.setUint32(28,this.h7),w};function x(w,v,b){var T,C=typeof w;if(C==="string"){var O=[],D=w.length,I=0,F;for(T=0;T<D;++T)F=w.charCodeAt(T),F<128?O[I++]=F:F<2048?(O[I++]=192|F>>>6,O[I++]=128|F&63):F<55296||F>=57344?(O[I++]=224|F>>>12,O[I++]=128|F>>>6&63,O[I++]=128|F&63):(F=65536+((F&1023)<<10|w.charCodeAt(++T)&1023),O[I++]=240|F>>>18,O[I++]=128|F>>>12&63,O[I++]=128|F>>>6&63,O[I++]=128|F&63);w=O}else if(C==="object"){if(w===null)throw new Error(t);if(l&&w.constructor===ArrayBuffer)w=new Uint8Array(w);else if(!Array.isArray(w)&&(!l||!ArrayBuffer.isView(w)))throw new Error(t)}else throw new Error(t);w.length>64&&(w=new f(v,!0).update(w).array());var _=[],R=[];for(T=0;T<64;++T){var z=w[T]||0;_[T]=92^z,R[T]=54^z}f.call(this,v,b),this.update(R),this.oKeyPad=_,this.inner=!0,this.sharedMemory=b}x.prototype=new f,x.prototype.finalize=function(){if(f.prototype.finalize.call(this),this.inner){this.inner=!1;var w=this.array();f.call(this,this.is224,this.sharedMemory),this.update(this.oKeyPad),this.update(w),f.prototype.finalize.call(this)}};var M=N();M.sha256=M,M.sha224=N(!0),M.sha256.hmac=m(),M.sha224.hmac=m(!0),o?e.exports=M:(r.sha256=M.sha256,r.sha224=M.sha224)})()})(ny);var LM=ny.exports;function oc(e){const t={kind:"struct",fields:e.map(n=>[n.key,n.type])};return{encode:(n,r)=>Yn(t,n,r),decode:n=>tm(t,n)}}function OM(e,t,n){try{return tm(e,t,n)}catch(r){throw console.error("Deserialization error:",r),new Error("Failed to deserialize data")}}var te=(e=>(e.HOME="HOME",e.CREATE_GAME="CREATE_GAME",e.JOIN_GAME="JOIN_GAME",e.GAME_LOBBY="GAME_LOBBY",e.COMMIT_CHOICE="COMMIT_CHOICE",e.REVEAL_CHOICE="REVEAL_CHOICE",e.GAME_RESULTS="GAME_RESULTS",e.AUTO_PLAY="AUTO_PLAY",e.SECURITY="SECURITY",e.TESTING="TESTING",e.TOURNAMENT="TOURNAMENT",e.PROFILE="PROFILE",e))(te||{}),ry=(e=>(e[e.WaitingForPlayers=0]="WaitingForPlayers",e[e.CommitPhase=1]="CommitPhase",e[e.RevealPhase=2]="RevealPhase",e[e.Finished=3]="Finished",e))(ry||{}),q=(e=>(e.SOL="SOL",e.RPS_TOKEN="RPS_TOKEN",e.RPSTOKEN="RPSToken",e[e.NEW_CURRENCY=2]="NEW_CURRENCY",e))(q||{}),Gt=(e=>(e.FIXED="fixed",e.MARTINGALE="martingale",e.DALEMBERT="dalembert",e.FIBONACCI="fibonacci",e))(Gt||{}),Bt=(e=>(e[e.None=0]="None",e[e.Rock=1]="Rock",e[e.Paper=2]="Paper",e[e.Scissors=3]="Scissors",e))(Bt||{});const wr=new se("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA");new se("TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb");const sy=new se("ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL");new se("So11111111111111111111111111111111111111112");new se("9pan9bMn5HatX4EJdBwg9VgCa7Uz5HL8N1m5D3NdXejP");const gf=e=>{const t=e.decode.bind(e),n=e.encode.bind(e);return{decode:t,encode:n}};var Ki={};Object.defineProperty(Ki,"__esModule",{value:!0});function PM(e){{const t=Buffer.from(e);t.reverse();const n=t.toString("hex");return n.length===0?BigInt(0):BigInt(`0x${n}`)}}var RM=Ki.toBigIntLE=PM;function DM(e){{const t=e.toString("hex");return t.length===0?BigInt(0):BigInt(`0x${t}`)}}Ki.toBigIntBE=DM;function zM(e,t){{const n=e.toString(16),r=Buffer.from(n.padStart(t*2,"0").slice(0,t*2),"hex");return r.reverse(),r}}var WM=Ki.toBufferLE=zM;function BM(e,t){{const n=e.toString(16);return Buffer.from(n.padStart(t*2,"0").slice(0,t*2),"hex")}}Ki.toBufferBE=BM;const UM=e=>t=>{const n=nm(e,t),{encode:r,decode:s}=gf(n),i=n;return i.decode=(o,l)=>{const c=s(o,l);return RM(Buffer.from(c))},i.encode=(o,l,c)=>{const u=WM(o,e);return r(u,l,c)},i},ao=UM(8),iy=e=>{const t=fi(e),{encode:n,decode:r}=gf(t),s=t;return s.decode=(i,o)=>!!r(i,o),s.encode=(i,o,l)=>{const c=Number(i);return n(c,o,l)},s},ze=e=>{const t=nm(32,e),{encode:n,decode:r}=gf(t),s=t;return s.decode=(i,o)=>{const l=r(i,o);return new se(l)},s.encode=(i,o,l)=>{const c=i.toBuffer();return n(c,o,l)},s};class bs extends Error{constructor(t){super(t)}}class ay extends bs{constructor(){super(...arguments),this.name="TokenAccountNotFoundError"}}class FM extends bs{constructor(){super(...arguments),this.name="TokenInvalidAccountError"}}class oy extends bs{constructor(){super(...arguments),this.name="TokenInvalidAccountOwnerError"}}class di extends bs{constructor(){super(...arguments),this.name="TokenInvalidAccountSizeError"}}class GM extends bs{constructor(){super(...arguments),this.name="TokenInvalidMintError"}}class $M extends bs{constructor(){super(...arguments),this.name="TokenOwnerOffCurveError"}}var $o;(function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Mint=1]="Mint",e[e.Account=2]="Account"})($o||($o={}));const ly=1,HM=td([fi("m"),fi("n"),iy("isInitialized"),ze("signer1"),ze("signer2"),ze("signer3"),ze("signer4"),ze("signer5"),ze("signer6"),ze("signer7"),ze("signer8"),ze("signer9"),ze("signer10"),ze("signer11")]),cy=HM.span;var Ho;(function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initialized=1]="Initialized",e[e.Frozen=2]="Frozen"})(Ho||(Ho={}));const uy=td([ze("mint"),ze("owner"),ao("amount"),Js("delegateOption"),ze("delegate"),fi("state"),Js("isNativeOption"),ao("isNative"),ao("delegatedAmount"),Js("closeAuthorityOption"),ze("closeAuthority")]),bn=uy.span;async function YM(e,t,n,r=wr){const s=await e.getAccountInfo(t,n);return KM(t,s,r)}function KM(e,t,n=wr){if(!t)throw new ay;if(!t.owner.equals(n))throw new oy;if(t.data.length<bn)throw new di;const r=uy.decode(t.data.slice(0,bn));let s=Buffer.alloc(0);if(t.data.length>bn){if(t.data.length===cy)throw new di;if(t.data[bn]!=$o.Account)throw new FM;s=t.data.slice(bn+ly)}return{address:e,mint:r.mint,owner:r.owner,amount:r.amount,delegate:r.delegateOption?r.delegate:null,delegatedAmount:r.delegatedAmount,isInitialized:r.state!==Ho.Uninitialized,isFrozen:r.state===Ho.Frozen,isNative:!!r.isNativeOption,rentExemptReserve:r.isNativeOption?r.isNative:null,closeAuthority:r.closeAuthorityOption?r.closeAuthority:null,tlvData:s}}const dy=td([Js("mintAuthorityOption"),ze("mintAuthority"),ao("supply"),fi("decimals"),iy("isInitialized"),Js("freezeAuthorityOption"),ze("freezeAuthority")]),lc=dy.span;async function VM(e,t,n,r=wr){const s=await e.getAccountInfo(t,n);return QM(t,s,r)}function QM(e,t,n=wr){if(!t)throw new ay;if(!t.owner.equals(n))throw new oy;if(t.data.length<lc)throw new di;const r=dy.decode(t.data.slice(0,lc));let s=Buffer.alloc(0);if(t.data.length>lc){if(t.data.length<=bn)throw new di;if(t.data.length===cy)throw new di;if(t.data[bn]!=$o.Mint)throw new GM;s=t.data.slice(bn+ly)}return{address:e,mintAuthority:r.mintAuthorityOption?r.mintAuthority:null,supply:r.supply,decimals:r.decimals,isInitialized:r.isInitialized,freezeAuthority:r.freezeAuthorityOption?r.freezeAuthority:null,tlvData:s}}async function ZM(e,t,n=!1,r=wr,s=sy){if(!n&&!se.isOnCurve(t.toBuffer()))throw new $M;const[i]=await se.findProgramAddress([t.toBuffer(),r.toBuffer(),e.toBuffer()],s);return i}var fy={};const JM=fy.REACT_APP_RPS_TOKEN_MINT||"RPSTokenMintAddressHereAbcdefg1234567890",yf=new se(JM),qM=fy.REACT_APP_FEE_COLLECTOR_ACCOUNT||"FeeCoLLeCToRyouNEEDtoUPDATEthiswithREALaccount111",XM=new se(qM),eb=10,tb=1e3,nb=XM,Jh=yf,rb=(e,t)=>t===q.RPSTOKEN?.05:0,sb=(e,t,n,r)=>{const s=new wt;return r===q.SOL&&s.add(ya.transfer({fromPubkey:t,toPubkey:n,lamports:e})),s};function ib(e,t){const r=BigInt(Math.floor(e*hi))*BigInt(eb)/BigInt(tb);return Number(r)/hi}const ab=async(e,t)=>{try{return await e.getBalance(t)/hi}catch(n){return console.error("Error fetching SOL balance:",n),0}},ob=async(e,t,n=yf)=>{var r,s;try{const i=await ZM(n,t,!1,wr,sy),o=await YM(e,i,"confirmed",wr),l=await VM(e,n);return Number(o.amount)/10**l.decimals}catch(i){return(r=i.message)!=null&&r.includes("could not find account")||(s=i.message)!=null&&s.includes("Account does not exist")||console.error("Error fetching RPS Token balance:",i),0}},qh=async(e,t,n=yf)=>{const r=await ab(e,t),s=await ob(e,t,n);return{sol:r,rpsToken:s}};typeof window<"u"&&typeof window.Buffer>"u"&&(window.Buffer=ke.Buffer);class lb{constructor(t){J(this,"pubkey");J(this,"choice");J(this,"committedChoice");J(this,"revealed");J(this,"score");this.pubkey=t.pubkey,this.choice=t.choice,this.committedChoice=t.committedChoice,this.revealed=t.revealed,this.score=t.score}}class Xu{constructor(t){J(this,"host");J(this,"players");J(this,"minPlayers");J(this,"maxPlayers");J(this,"state");J(this,"currentRound");J(this,"totalRounds");J(this,"entryFee");J(this,"gamePot");J(this,"requiredTimeout");J(this,"lastActionTimestamp");J(this,"playerCount");J(this,"losersCanRejoin");J(this,"currencyMode");this.host=t.host,this.players=t.players,this.minPlayers=t.minPlayers,this.maxPlayers=t.maxPlayers,this.state=t.state,this.currentRound=t.currentRound,this.totalRounds=t.totalRounds,this.entryFee=t.entryFee,this.gamePot=t.gamePot,this.requiredTimeout=t.requiredTimeout,this.lastActionTimestamp=t.lastActionTimestamp,this.playerCount=t.playerCount,this.losersCanRejoin=t.losersCanRejoin,this.currencyMode=t.currencyMode}}const hy={kind:"struct",fields:[["pubkey",[32]],["choice","u8"],["committedChoice",[32]],["revealed","u8"],["score","u8"]]},cb={kind:"struct",fields:[["host",[32]],["players",[hy]],["minPlayers","u8"],["maxPlayers","u8"],["state","u8"],["currentRound","u8"],["totalRounds","u8"],["entryFee","u64"],["gamePot","u64"],["requiredTimeout","u64"],["lastActionTimestamp","u64"],["playerCount","u8"],["losersCanRejoin","u8"],["currencyMode","u8"]]},ub=new Map([[lb,hy],[Xu,cb]]);class db{constructor(t,n,r,s={}){J(this,"connection");J(this,"wallet");J(this,"programId");J(this,"config");J(this,"gameSubscriptions",new Map);this.connection=t,this.wallet=n,this.programId=r,this.config=s}async getProgramAccounts(){return await this.connection.getProgramAccounts(this.programId)}async getGameAccount(t){const n=await this.connection.getAccountInfo(t);if(!n)throw new Error(`Game account not found: ${t.toBase58()}`);return this.deserializeGameAccount(n.data)}deserializeGameAccount(t){try{return OM(ub,Xu,t)}catch(n){return console.error("Error deserializing game account:",n),new Xu({host:new Uint8Array(32),players:[],minPlayers:2,maxPlayers:4,state:0,currentRound:0,totalRounds:1,entryFee:BigInt(0),gamePot:BigInt(0),requiredTimeout:BigInt(0),lastActionTimestamp:BigInt(0),playerCount:0,losersCanRejoin:!1,currencyMode:q.SOL})}}notifyStatus(t){this.config.onStatusUpdate&&this.config.onStatusUpdate(t)}notifyError(t){this.config.onError&&this.config.onError(t)}async subscribeToGameUpdates(t,n){const r=new se(t);if(this.gameSubscriptions.has(t)){const i=this.gameSubscriptions.get(t);this.connection.removeAccountChangeListener(i)}const s=this.connection.onAccountChange(r,i=>{try{const o=this.deserializeGameAccount(i.data);n(o)}catch(o){this.notifyError(o)}});return this.gameSubscriptions.set(t,s),s}unsubscribeFromGameUpdates(t){if(this.gameSubscriptions.has(t)){const n=this.gameSubscriptions.get(t);this.connection.removeAccountChangeListener(n),this.gameSubscriptions.delete(t)}}async createGame(t,n,r,s,i,o,l=q.SOL){this.notifyStatus("Creating new game...");const c=vf.generate(),u=this.createInitializeGameInstruction(c.publicKey,t,n,r,s,i,o,l),d=new wt().add(u);try{d.feePayer=this.wallet.publicKey,d.recentBlockhash=(await this.connection.getLatestBlockhash()).blockhash;const h=await this.wallet.signTransaction(d);h.partialSign(c);const y=await this.wallet.sendTransaction(h,this.connection);await this.connection.confirmTransaction(y,"confirmed");const g=ib(s,l);this.notifyStatus(`Game created with ${g.toFixed(6)} ${l===q.SOL?"SOL":"RPSTOKEN"} fee.`);const S=await this.getGameAccount(c.publicKey);return this.notifyStatus("Game created successfully!"),{gameId:c.publicKey.toString(),gameAccount:S}}catch(h){throw this.notifyError(h),h}}createInitializeGameInstruction(t,n,r,s,i,o,l,c=q.SOL){const u=Math.round(i*hi);let d=u;if(c===q.RPSTOKEN){const g=rb(i,c);d=Math.round(u*(1+g))}const h=oc([{key:"instruction",type:"u8"},{key:"minPlayers",type:"u8"},{key:"maxPlayers",type:"u8"},{key:"totalRounds",type:"u8"},{key:"entryFee",type:"u64"},{key:"timeoutSeconds",type:"u64"},{key:"losersCanRejoin",type:"u8"},{key:"currencyMode",type:"u8"},{key:"autoRoundDelay",type:"u64"},{key:"maxAutoRounds",type:"u64"}]),y=ke.Buffer.alloc(1e3);try{const g=h.encode({instruction:0,minPlayers:n,maxPlayers:r,totalRounds:s,entryFee:d,timeoutSeconds:o,losersCanRejoin:l?1:0,currencyMode:c,autoRoundDelay:0,maxAutoRounds:0},y),S=[{pubkey:this.wallet.publicKey,isSigner:!0,isWritable:!0},{pubkey:t,isSigner:!0,isWritable:!0},{pubkey:ya.programId,isSigner:!1,isWritable:!1},{pubkey:nb,isSigner:!1,isWritable:!0}];return c===q.RPSTOKEN&&S.push({pubkey:Jh,isSigner:!1,isWritable:!1}),new Zt({keys:S,programId:this.programId,data:y.slice(0,g)})}catch(g){throw new Error("Error encoding instruction data: "+g.message)}}createGameInstruction(t,n,r,s,i,o,l=q.SOL){const c=vf.generate(),u={kind:"struct",fields:[["instruction","u8"],["minPlayers","u8"],["maxPlayers","u8"],["totalRounds","u8"],["entryFee","u64"],["timeoutSeconds","u64"],["losersCanRejoin","u8"],["currencyMode","u8"]]},d=s*hi,h=ke.Buffer.alloc(100),y=Yn(u,{instruction:0,minPlayers:t,maxPlayers:n,totalRounds:r,entryFee:BigInt(d),timeoutSeconds:BigInt(i),losersCanRejoin:o?1:0,currencyMode:l},h);return new Zt({keys:[{pubkey:this.wallet.publicKey,isSigner:!0,isWritable:!0},{pubkey:c.publicKey,isSigner:!0,isWritable:!0},{pubkey:ya.programId,isSigner:!1,isWritable:!1}],programId:this.programId,data:h.slice(0,y)})}createJoinGameInstruction(t,n,r){const s=oc([{key:"instruction",type:"u8"}]),i=ke.Buffer.alloc(1);try{const o=s.encode({instruction:1},i),l=[{pubkey:this.wallet.publicKey,isSigner:!0,isWritable:!0},{pubkey:t,isSigner:!1,isWritable:!0},{pubkey:ya.programId,isSigner:!1,isWritable:!1}];return r===q.RPSTOKEN&&l.push({pubkey:Jh,isSigner:!1,isWritable:!1}),new Zt({keys:l,programId:this.programId,data:i.slice(0,o)})}catch(o){throw new Error("Error encoding instruction data: "+o.message)}}createCommitChoiceInstruction(t,n,r){if(!this.wallet||!this.wallet.publicKey)throw new Error("Wallet not connected");const s=ke.Buffer.from([n]),i=ke.Buffer.from(r,"hex"),o=ke.Buffer.concat([s,i]),l=LM.sha256.create();l.update(o);const c=ke.Buffer.from(l.hex(),"hex"),u={kind:"struct",fields:[["instruction","u8"],["hashedChoice",[32]]]},d=ke.Buffer.alloc(33),h=Yn(u,{instruction:2,hashedChoice:c},d);return new Zt({keys:[{pubkey:this.wallet.publicKey,isSigner:!0,isWritable:!0},{pubkey:t,isSigner:!1,isWritable:!0}],programId:this.programId,data:d.slice(0,h)})}createRevealChoiceInstruction(t,n,r){if(!this.wallet||!this.wallet.publicKey)throw new Error("Wallet not connected");const s=ke.Buffer.from(r,"hex"),i={kind:"struct",fields:[["instruction","u8"],["choice","u8"],["salt",[32]]]},o=ke.Buffer.alloc(34),l=Yn(i,{instruction:3,choice:n,salt:s},o);return new Zt({keys:[{pubkey:this.wallet.publicKey,isSigner:!0,isWritable:!0},{pubkey:t,isSigner:!1,isWritable:!0}],programId:this.programId,data:o.slice(0,l)})}createResolveTimeoutInstruction(t){const n=oc([{key:"instruction",type:"u8"}]),r=ke.Buffer.alloc(1);try{return n.encode({instruction:4},r),new Zt({keys:[{pubkey:t,isSigner:!1,isWritable:!0},{pubkey:this.wallet.publicKey,isSigner:!0,isWritable:!1}],programId:this.programId,data:r})}catch(s){throw new Error("Error encoding instruction data: "+s.message)}}createClaimWinningsInstruction(t){const n={kind:"struct",fields:[["instruction","u8"]]},r=ke.Buffer.alloc(1),s=Yn(n,{instruction:5},r);return new Zt({keys:[{pubkey:t,isSigner:!1,isWritable:!0},{pubkey:this.wallet.publicKey,isSigner:!0,isWritable:!0}],programId:this.programId,data:r.slice(0,s)})}createRejoinGameInstruction(t,n,r){const s={kind:"struct",fields:[["instruction","u8"]]},i=ke.Buffer.alloc(1),o=Yn(s,{instruction:6},i);return new Zt({keys:[{pubkey:t,isSigner:!1,isWritable:!0},{pubkey:this.wallet.publicKey,isSigner:!0,isWritable:!0}],programId:this.programId,data:i.slice(0,o)})}createStartNewGameRoundInstruction(t){const n={kind:"struct",fields:[["instruction","u8"]]},r=ke.Buffer.alloc(1),s=Yn(n,{instruction:7},r);return new Zt({keys:[{pubkey:t,isSigner:!1,isWritable:!0},{pubkey:this.wallet.publicKey,isSigner:!0,isWritable:!1}],programId:this.programId,data:r.slice(0,s)})}async joinGame(t){this.notifyStatus("Joining game...");const n=new se(t),r=this.createJoinGameInstruction(n,0,q.SOL),s=new wt().add(r);try{const i=await this.getGameAccount(n),o=i.currencyMode||q.SOL,l=sb(i.entryFee,this.wallet.publicKey,n,o);for(const h of l.instructions)s.add(h);s.feePayer=this.wallet.publicKey,s.recentBlockhash=(await this.connection.getLatestBlockhash()).blockhash;const c=await this.wallet.signTransaction(s),u=await this.wallet.sendTransaction(c,this.connection);await this.connection.confirmTransaction(u,"confirmed");const d=await this.getGameAccount(n);return this.notifyStatus("Joined game successfully!"),d}catch(i){throw this.notifyError(i),i}}async startGame(t){this.notifyStatus("Game will start automatically when enough players join...");const n=new se(t),r=await this.getGameAccount(n);if(r.host.toString()!==this.wallet.publicKey.toBuffer().toString())throw new Error("Only the host can start the game");r.state!==0?r.state===1?this.notifyStatus("Game has already started! Time to commit your choice."):this.notifyStatus("Game has already started!"):r.players.length<r.minPlayers&&this.notifyStatus(`Waiting for more players (${r.players.length}/${r.minPlayers})...`)}async commitChoice(t,n,r){this.notifyStatus("Committing your choice...");const s=new se(t),i=this.createCommitChoiceInstruction(s,n,r),o=new wt().add(i);try{o.feePayer=this.wallet.publicKey,o.recentBlockhash=(await this.connection.getLatestBlockhash()).blockhash;const l=await this.wallet.signTransaction(o),c=await this.wallet.sendTransaction(l,this.connection);await this.connection.confirmTransaction(c,"confirmed"),this.notifyStatus("Choice committed successfully! Waiting for others...")}catch(l){throw this.notifyError(l),l}}async revealChoice(t,n,r){this.notifyStatus("Revealing your choice...");const s=new se(t),i=this.createRevealChoiceInstruction(s,n,r),o=new wt().add(i);try{o.feePayer=this.wallet.publicKey,o.recentBlockhash=(await this.connection.getLatestBlockhash()).blockhash;const l=await this.wallet.signTransaction(o),c=await this.wallet.sendTransaction(l,this.connection);await this.connection.confirmTransaction(c,"confirmed"),this.notifyStatus("Choice revealed successfully! Waiting for others...")}catch(l){throw this.notifyError(l),l}}async resolveTimeout(t){this.notifyStatus("Resolving timeout...");const n=new se(t),r=this.createResolveTimeoutInstruction(n),s=new wt().add(r);try{s.feePayer=this.wallet.publicKey,s.recentBlockhash=(await this.connection.getLatestBlockhash()).blockhash;const i=await this.wallet.signTransaction(s),o=await this.wallet.sendTransaction(i,this.connection);await this.connection.confirmTransaction(o,"confirmed"),this.notifyStatus("Timeout resolved successfully!")}catch(i){throw this.notifyError(i),i}}async claimWinnings(t){this.notifyStatus("Claiming winnings...");const n=new se(t),r=this.createClaimWinningsInstruction(n),s=new wt().add(r);try{s.feePayer=this.wallet.publicKey,s.recentBlockhash=(await this.connection.getLatestBlockhash()).blockhash;const i=await this.wallet.signTransaction(s),o=await this.wallet.sendTransaction(i,this.connection);await this.connection.confirmTransaction(o,"confirmed"),this.notifyStatus("Winnings claimed successfully!")}catch(i){throw this.notifyError(i),i}}async rejoinGame(t){this.notifyStatus("Rejoining game...");const n=new se(t),r=this.createRejoinGameInstruction(n,0,q.SOL),s=new wt().add(r);try{s.feePayer=this.wallet.publicKey,s.recentBlockhash=(await this.connection.getLatestBlockhash()).blockhash;const i=await this.wallet.signTransaction(s),o=await this.wallet.sendTransaction(i,this.connection);await this.connection.confirmTransaction(o,"confirmed"),this.notifyStatus("Rejoined game successfully!")}catch(i){throw this.notifyError(i),i}}async startNewGameRound(t){this.notifyStatus("Starting new game round...");const n=new se(t),r=this.createStartNewGameRoundInstruction(n),s=new wt().add(r);try{s.feePayer=this.wallet.publicKey,s.recentBlockhash=(await this.connection.getLatestBlockhash()).blockhash;const i=await this.wallet.signTransaction(s),o=await this.wallet.sendTransaction(i,this.connection);await this.connection.confirmTransaction(o,"confirmed"),this.notifyStatus("New game round started successfully!")}catch(i){throw this.notifyError(i),i}}async getAllActiveGames(){this.notifyStatus("Fetching active games...");try{const t=await this.getProgramAccounts(),n=[];for(const{pubkey:r,account:s}of t)try{const i=this.deserializeGameAccount(s.data);i.state===0&&n.push({gameId:r.toString(),gameAccount:i})}catch{continue}return n}catch(t){throw this.notifyError(t),t}}}const fb={requestsPerMinute:3240,failedAttempts:47,concurrentGames:826,activeUsers:3214,averageLatency:78,lastAttackTimestamp:Date.now()-1e3*60*60*3},my=({compact:e=!1,showMetrics:t=!0})=>{const[n,r]=E.useState(fb),[s,i]=E.useState(!1);E.useEffect(()=>{const u=setInterval(()=>{r(d=>({...d,requestsPerMinute:Math.floor(d.requestsPerMinute+Math.random()*100-50),concurrentGames:Math.floor(d.concurrentGames+Math.random()*10-5),activeUsers:Math.floor(d.activeUsers+Math.random()*20-10),averageLatency:Math.floor(d.averageLatency+Math.random()*10-5)}))},5e3);return()=>clearInterval(u)},[]);const o=u=>{if(!u)return"Never";const d=Math.floor((Date.now()-u)/1e3);return d<60?`${d} sec ago`:d<3600?`${Math.floor(d/60)} min ago`:d<86400?`${Math.floor(d/3600)} hrs ago`:`${Math.floor(d/86400)} days ago`},l=[{name:"Anti-DDoS Protection",description:"Distributed relay network prevents direct attacks on game servers",icon:"🛡️"},{name:"On-Chain Verification",description:"All game moves are cryptographically secured on the Solana blockchain",icon:"🔒"},{name:"Multi-Region Scaling",description:"Load balancing across global regions ensures low latency and high availability",icon:"🌐"},{name:"Anti-Bot Protection",description:"Advanced behavior analysis detects and prevents automated gameplay",icon:"🤖"},{name:"Encrypted Game Moves",description:"Commit-reveal pattern prevents players from seeing others' moves in advance",icon:"🔐"},{name:"Fraud Detection",description:"Continuous monitoring for suspicious activity and cheating attempts",icon:"🕵️"}],c=[{name:"Horizontal Scaling",description:"Automatically scales to handle thousands of concurrent games",value:"50,000+ concurrent players"},{name:"Database Sharding",description:"Game data is distributed across multiple database shards for high performance",value:"200+ transactions per second"},{name:"High Availability",description:"99.9% uptime with multi-region redundancy",value:"< 200ms global latency"}];return e?a.jsxs("div",{className:"bg-purple-900 bg-opacity-30 rounded-lg p-4 border border-purple-800",children:[a.jsxs("div",{className:"flex justify-between items-center",children:[a.jsx("h3",{className:"text-lg font-semibold text-white mb-0",children:"Enhanced Security & Scaling"}),a.jsx("button",{onClick:()=>i(!s),className:"text-purple-300 hover:text-purple-100",children:s?"▲ Hide":"▼ Details"})]}),s&&a.jsxs("div",{className:"mt-3",children:[a.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-3 mb-4",children:l.slice(0,3).map((u,d)=>a.jsxs("div",{className:"flex items-start space-x-2",children:[a.jsx("div",{className:"text-xl",children:u.icon}),a.jsx("div",{className:"text-sm",children:u.name})]},d))}),a.jsx("div",{className:"text-sm text-purple-300",children:"Supports 50,000+ concurrent players with 99.9% uptime"})]})]}):a.jsxs("div",{className:"bg-gray-800 rounded-xl border border-purple-800 overflow-hidden",children:[a.jsxs("div",{className:"bg-gradient-to-r from-purple-900 to-indigo-900 px-6 py-4",children:[a.jsx("h2",{className:"text-2xl font-bold text-white",children:"Security & Scaling Dashboard"}),a.jsx("p",{className:"text-purple-200",children:"Enterprise-grade protection and performance for Rock Paper Scissors"})]}),t&&a.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 p-4 bg-gray-900",children:[a.jsxs("div",{className:"bg-gray-800 p-3 rounded-lg",children:[a.jsx("div",{className:"text-sm text-gray-400",children:"Active Users"}),a.jsx("div",{className:"text-xl font-bold text-white",children:n.activeUsers.toLocaleString()})]}),a.jsxs("div",{className:"bg-gray-800 p-3 rounded-lg",children:[a.jsx("div",{className:"text-sm text-gray-400",children:"Concurrent Games"}),a.jsx("div",{className:"text-xl font-bold text-white",children:n.concurrentGames.toLocaleString()})]}),a.jsxs("div",{className:"bg-gray-800 p-3 rounded-lg",children:[a.jsx("div",{className:"text-sm text-gray-400",children:"Requests/min"}),a.jsx("div",{className:"text-xl font-bold text-white",children:n.requestsPerMinute.toLocaleString()})]}),a.jsxs("div",{className:"bg-gray-800 p-3 rounded-lg",children:[a.jsx("div",{className:"text-sm text-gray-400",children:"Avg Latency"}),a.jsxs("div",{className:"text-xl font-bold text-green-400",children:[n.averageLatency," ms"]})]}),a.jsxs("div",{className:"bg-gray-800 p-3 rounded-lg",children:[a.jsx("div",{className:"text-sm text-gray-400",children:"Failed Attempts"}),a.jsx("div",{className:"text-xl font-bold text-red-400",children:n.failedAttempts})]}),a.jsxs("div",{className:"bg-gray-800 p-3 rounded-lg",children:[a.jsx("div",{className:"text-sm text-gray-400",children:"Last Attack"}),a.jsx("div",{className:"text-xl font-bold text-yellow-400",children:o(n.lastAttackTimestamp)})]})]}),a.jsxs("div",{className:"p-6",children:[a.jsx("h3",{className:"text-xl font-semibold mb-4 text-purple-300",children:"Security Features"}),a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6",children:l.map((u,d)=>a.jsxs("div",{className:"bg-gray-900 p-4 rounded-lg flex items-start space-x-3",children:[a.jsx("div",{className:"text-3xl",children:u.icon}),a.jsxs("div",{children:[a.jsx("h4",{className:"font-semibold text-white",children:u.name}),a.jsx("p",{className:"text-sm text-gray-400",children:u.description})]})]},d))}),a.jsx("h3",{className:"text-xl font-semibold mb-4 text-purple-300",children:"Scaling Capabilities"}),a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:c.map((u,d)=>a.jsxs("div",{className:"bg-gray-900 p-4 rounded-lg",children:[a.jsx("h4",{className:"font-semibold text-white",children:u.name}),a.jsx("p",{className:"text-sm text-gray-400 mb-2",children:u.description}),a.jsx("div",{className:"text-lg font-bold text-green-400",children:u.value})]},d))})]})]})},hb=({setCurrentView:e,connected:t})=>{const n=()=>{if(!t){alert("Please connect your wallet first");return}e(te.CREATE_GAME)},r=()=>{if(!t){alert("Please connect your wallet first");return}e(te.JOIN_GAME)},s=()=>{if(!t){alert("Please connect your wallet first");return}e(te.AUTO_PLAY)};return a.jsxs("div",{className:"flex flex-col items-center",children:[a.jsx("h2",{className:"text-3xl font-bold mb-8 text-center",children:"Multiplayer Rock Paper Scissors on Solana"}),a.jsxs("div",{className:"flex flex-col md:flex-row justify-center w-full gap-8 mb-8",children:[a.jsxs("div",{className:"card flex flex-col items-center justify-center p-8 cursor-pointer hover:bg-gray-700 transition-colors duration-200 w-full md:w-1/3",onClick:n,children:[a.jsx("div",{className:"text-5xl mb-4",children:"🎮"}),a.jsx("h3",{className:"text-2xl font-bold mb-2",children:"Create Game"}),a.jsx("p",{className:"text-gray-300 text-center",children:"Create a new game and invite friends to play"}),a.jsx("button",{className:"mt-4 btn btn-primary",children:"Create Game"})]}),a.jsxs("div",{className:"card flex flex-col items-center justify-center p-8 cursor-pointer hover:bg-gray-700 transition-colors duration-200 w-full md:w-1/3",onClick:r,children:[a.jsx("div",{className:"text-5xl mb-4",children:"🎲"}),a.jsx("h3",{className:"text-2xl font-bold mb-2",children:"Join Game"}),a.jsx("p",{className:"text-gray-300 text-center",children:"Enter a specific game ID to join friends or a tournament game"}),a.jsx("button",{className:"mt-4 btn btn-secondary",children:"Join Game"})]}),a.jsxs("div",{className:"card flex flex-col items-center justify-center p-8 cursor-pointer hover:bg-gray-700 transition-colors duration-200 w-full md:w-1/3",onClick:s,children:[a.jsx("div",{className:"text-5xl mb-4",children:"🤖"}),a.jsx("h3",{className:"text-2xl font-bold mb-2",children:"Auto Play"}),a.jsx("p",{className:"text-gray-300 text-center",children:"Set your strategy and let the system play for you automatically"}),a.jsx("button",{className:"mt-4 btn btn-success",children:"Auto Play"})]})]}),a.jsx("div",{className:"w-full mb-8",children:a.jsx(my,{compact:!0,showMetrics:!1})}),a.jsxs("div",{className:"card w-full max-w-3xl p-6",children:[a.jsx("h3",{className:"text-xl font-bold mb-4",children:"How to Play"}),a.jsxs("ol",{className:"list-decimal list-inside space-y-2 text-gray-300",children:[a.jsx("li",{children:"Connect your Solana wallet (Phantom, Solflare, etc.)"}),a.jsx("li",{children:"Choose to join the player queue or enter a specific game ID"}),a.jsx("li",{children:"Pay the entry fee in SOL or RPS Tokens"}),a.jsx("li",{children:"Choose rock, paper, or scissors manually or set to auto-play"}),a.jsx("li",{children:"Win by having the highest score after all rounds!"})]}),a.jsxs("div",{className:"mt-6 p-4 bg-purple-900 bg-opacity-50 rounded-lg",children:[a.jsx("h4",{className:"font-bold mb-2",children:"Game Rules"}),a.jsxs("ul",{className:"list-disc list-inside space-y-1 text-gray-300",children:[a.jsx("li",{children:"Rock beats Scissors"}),a.jsx("li",{children:"Scissors beats Paper"}),a.jsx("li",{children:"Paper beats Rock"}),a.jsx("li",{children:"Each player earns 1 point for each win against another player"}),a.jsx("li",{children:"The player with the most points after all rounds wins"}),a.jsx("li",{children:"Games require 3-4 players to start automatically"})]})]})]}),a.jsxs("div",{className:"card w-full max-w-3xl p-6 mt-6",children:[a.jsx("h3",{className:"text-xl font-bold mb-4",children:"Enterprise-Grade Security & Scaling"}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[a.jsxs("div",{className:"bg-gray-900 bg-opacity-50 p-4 rounded-lg",children:[a.jsxs("h4",{className:"text-lg font-semibold mb-2 flex items-center",children:[a.jsx("span",{className:"text-xl mr-2",children:"🔒"}),a.jsx("span",{children:"Security Features"})]}),a.jsxs("ul",{className:"space-y-2 text-sm",children:[a.jsxs("li",{className:"flex items-start",children:[a.jsx("span",{className:"text-green-400 mr-2",children:"✓"}),a.jsx("span",{children:"Military-grade encryption for all game moves"})]}),a.jsxs("li",{className:"flex items-start",children:[a.jsx("span",{className:"text-green-400 mr-2",children:"✓"}),a.jsx("span",{children:"Solana blockchain verification prevents cheating"})]}),a.jsxs("li",{className:"flex items-start",children:[a.jsx("span",{className:"text-green-400 mr-2",children:"✓"}),a.jsx("span",{children:"AI-powered anti-bot protection"})]}),a.jsxs("li",{className:"flex items-start",children:[a.jsx("span",{className:"text-green-400 mr-2",children:"✓"}),a.jsx("span",{children:"DDoS protection with global traffic filtering"})]})]})]}),a.jsxs("div",{className:"bg-gray-900 bg-opacity-50 p-4 rounded-lg",children:[a.jsxs("h4",{className:"text-lg font-semibold mb-2 flex items-center",children:[a.jsx("span",{className:"text-xl mr-2",children:"🚀"}),a.jsx("span",{children:"Scaling Capabilities"})]}),a.jsxs("ul",{className:"space-y-2 text-sm",children:[a.jsxs("li",{className:"flex items-start",children:[a.jsx("span",{className:"text-green-400 mr-2",children:"✓"}),a.jsx("span",{children:"Support for 50,000+ concurrent players"})]}),a.jsxs("li",{className:"flex items-start",children:[a.jsx("span",{className:"text-green-400 mr-2",children:"✓"}),a.jsx("span",{children:"Global infrastructure across 12 regions"})]}),a.jsxs("li",{className:"flex items-start",children:[a.jsx("span",{className:"text-green-400 mr-2",children:"✓"}),a.jsx("span",{children:"Auto-scaling to handle sudden player surges"})]}),a.jsxs("li",{className:"flex items-start",children:[a.jsx("span",{className:"text-green-400 mr-2",children:"✓"}),a.jsx("span",{children:"Low latency (< 200ms) gameplay worldwide"})]})]})]})]}),a.jsx("div",{className:"text-center text-sm text-purple-300 mt-2",children:"Our infrastructure can handle thousands of concurrent games with enterprise-grade security."})]})]})},mb=({selectedCurrency:e,onCurrencyChange:t,entryFee:n,onEntryFeeChange:r,disabled:s=!1})=>a.jsxs("div",{className:"mt-4",children:[a.jsx("label",{className:"form-label",children:"Currency"}),a.jsxs("div",{className:"flex space-x-4",children:[a.jsx("button",{type:"button",onClick:()=>t(q.SOL),disabled:s,className:`flex-1 py-2 rounded-lg transition-colors ${e===q.SOL?"bg-purple-600 hover:bg-purple-700":"bg-gray-700 hover:bg-gray-600"} ${s?"opacity-50 cursor-not-allowed":"cursor-pointer"}`,children:a.jsxs("div",{className:"flex items-center justify-center",children:[a.jsx("img",{src:"https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11111111111111111111111111111111111111112/logo.png",alt:"SOL",className:"w-5 h-5 mr-2"}),"SOL"]})}),a.jsx("button",{type:"button",onClick:()=>t(q.RPS_TOKEN),disabled:s,className:`flex-1 py-2 rounded-lg transition-colors ${e===q.RPS_TOKEN?"bg-purple-600 hover:bg-purple-700":"bg-gray-700 hover:bg-gray-600"} ${s?"opacity-50 cursor-not-allowed":"cursor-pointer"}`,children:a.jsxs("div",{className:"flex items-center justify-center",children:[a.jsx("img",{src:"https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R/logo.png",alt:"RPS",className:"w-5 h-5 mr-2"}),"RPS Token"]})})]}),e===q.RPS_TOKEN&&a.jsx("div",{className:"mt-2 p-2 bg-green-800 bg-opacity-30 rounded-lg",children:a.jsxs("p",{className:"text-green-400 text-sm",children:[a.jsx("span",{className:"font-bold",children:"+5% Bonus!"})," Using RPS Tokens gives you a larger pot!"]})}),a.jsxs("div",{className:"mt-4",children:[a.jsxs("label",{className:"form-label",children:["Entry Fee (",e===q.SOL?"SOL":"RPS Token",")"]}),a.jsxs("div",{className:"relative",children:[a.jsx("input",{type:"number",value:n,onChange:i=>r(Math.max(.01,parseFloat(i.target.value)||.01)),min:"0.01",step:"0.01",disabled:s,className:`form-control pr-12 ${s?"opacity-50 cursor-not-allowed":""}`}),a.jsx("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400",children:e===q.SOL?"SOL":"RPS"})]}),e===q.RPS_TOKEN&&a.jsx("p",{className:"text-xs text-gray-400 mt-1",children:"Only 0.05% fee instead of 0.1% when using RPS Tokens!"})]})]}),pb=({setCurrentView:e,gameClient:t,createGame:n,loading:r=!1})=>{const[s,i]=E.useState(.1),[o,l]=E.useState(3),[c,u]=E.useState(3),[d,h]=E.useState(60),[y,g]=E.useState(!0),[S,N]=E.useState(q.SOL),[A,p]=E.useState(!0),[m,f]=E.useState(!1),x=()=>{e(te.HOME)},M=async v=>{if(v.preventDefault(),!t){alert("Game client not initialized. Please connect your wallet.");return}f(!0);try{await n(s,S)}catch(b){console.error("Failed to create game:",b),alert(`Failed to create game: ${b.message||"Unknown error"}`)}finally{f(!1)}},w=r||m;return a.jsx("div",{className:"max-w-3xl mx-auto",children:a.jsxs("div",{className:"card",children:[a.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Create New Game"}),a.jsxs("form",{onSubmit:M,children:[a.jsx(mb,{selectedCurrency:S,onCurrencyChange:N,entryFee:s,onEntryFeeChange:i,disabled:w}),a.jsxs("div",{className:"form-group mt-6",children:[a.jsx("label",{className:"form-label",children:"Player Count"}),a.jsxs("div",{className:"flex space-x-4",children:[a.jsx("button",{type:"button",className:`flex-1 py-3 rounded-lg ${o===3?"bg-purple-600":"bg-gray-700 hover:bg-gray-600"}`,onClick:()=>l(3),disabled:w,children:"3 Players"}),a.jsx("button",{type:"button",className:`flex-1 py-3 rounded-lg ${o===4?"bg-purple-600":"bg-gray-700 hover:bg-gray-600"}`,onClick:()=>l(4),disabled:w,children:"4 Players"})]})]}),a.jsxs("div",{className:"form-group",children:[a.jsx("label",{className:"form-label",children:"Number of Rounds"}),a.jsx("input",{type:"number",className:"form-control",value:c,onChange:v=>u(parseInt(v.target.value)),min:1,max:10,required:!0,disabled:w})]}),a.jsxs("div",{className:"form-group",children:[a.jsx("label",{className:"form-label",children:"Timeout (seconds)"}),a.jsx("input",{type:"number",className:"form-control",value:d,onChange:v=>h(parseInt(v.target.value)),min:30,required:!0,disabled:w}),a.jsx("p",{className:"text-xs text-gray-400 mt-1",children:"Time before a player is considered inactive"})]}),a.jsxs("div",{className:"form-group flex items-center",children:[a.jsx("input",{type:"checkbox",id:"losersCanRejoin",className:"mr-2 h-5 w-5 rounded text-purple-500 focus:ring-purple-500",checked:y,onChange:v=>g(v.target.checked),disabled:w}),a.jsx("label",{htmlFor:"losersCanRejoin",className:"text-white",children:"Allow losers to rejoin"})]}),a.jsxs("div",{className:"form-group flex items-center mt-4",children:[a.jsx("input",{type:"checkbox",id:"useSecureMode",className:"mr-2 h-5 w-5 rounded text-purple-500 focus:ring-purple-500",checked:A,onChange:v=>p(v.target.checked),disabled:w}),a.jsx("label",{htmlFor:"useSecureMode",className:"text-white",children:"Enable enhanced security mode"}),a.jsxs("div",{className:"ml-2 group relative",children:[a.jsx("span",{className:"cursor-help text-gray-400",children:"ⓘ"}),a.jsx("div",{className:"absolute bottom-full mb-2 hidden group-hover:block bg-gray-800 p-2 rounded shadow-lg w-64 text-xs",children:"Enhanced security mode enables additional verification of player moves, DDoS protection, and bot detection."})]})]}),a.jsxs("div",{className:"mt-6 p-4 bg-gray-800 rounded-lg",children:[a.jsx("h3",{className:"text-lg font-semibold mb-2 text-purple-300",children:"Security Information"}),a.jsxs("ul",{className:"text-sm text-gray-300 space-y-2",children:[a.jsxs("li",{className:"flex items-start",children:[a.jsx("span",{className:"text-green-400 mr-2",children:"✓"}),a.jsx("span",{children:"All game moves are cryptographically secured on the Solana blockchain"})]}),a.jsxs("li",{className:"flex items-start",children:[a.jsx("span",{className:"text-green-400 mr-2",children:"✓"}),a.jsx("span",{children:"Commit-reveal pattern prevents cheating by hiding your choice until all players commit"})]}),a.jsxs("li",{className:"flex items-start",children:[a.jsx("span",{className:"text-green-400 mr-2",children:"✓"}),a.jsx("span",{children:"Game state is entirely on-chain, ensuring 100% transparency and fairness"})]}),a.jsxs("li",{className:"flex items-start",children:[a.jsx("span",{className:"text-green-400 mr-2",children:"✓"}),a.jsx("span",{children:"Auto-scaling infrastructure supports up to 50,000 concurrent players"})]})]})]}),a.jsxs("div",{className:"flex justify-between mt-8",children:[a.jsx("button",{type:"button",onClick:x,className:"btn btn-gray",disabled:w,children:"Back"}),a.jsx("button",{type:"submit",className:"btn btn-primary",disabled:w,children:w?"Creating Game...":"Create Game"})]}),a.jsx("div",{className:"mt-6 p-3 bg-gray-800 rounded-lg text-sm text-gray-400",children:a.jsxs("div",{className:"flex items-start",children:[a.jsx("span",{className:"text-purple-400 mr-2",children:"ℹ"}),a.jsxs("p",{children:["A ",S===q.SOL?"0.1%":"0.05%"," fee will be applied to your entry fee.",S===q.RPS_TOKEN&&" RPS Token games have 50% lower fees!"]})]})})]})]})})},gb=({setCurrentView:e,gameClient:t,joinGame:n,loading:r=!1})=>{const[s,i]=E.useState(""),[o,l]=E.useState(!1),c=()=>{e(te.HOME)},u=async h=>{if(h.preventDefault(),!s.trim()||!t){alert("Please enter a game ID and make sure your wallet is connected");return}l(!0);try{await n(s.trim())}catch(y){console.error("Failed to join game:",y),alert(`Failed to join game: ${y.message||"Unknown error"}`)}finally{l(!1)}},d=r||o;return a.jsxs("div",{className:"max-w-3xl mx-auto",children:[a.jsxs("div",{className:"card",children:[a.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Join Existing Game"}),a.jsxs("form",{onSubmit:u,children:[a.jsxs("div",{className:"form-group",children:[a.jsx("label",{className:"form-label",children:"Game ID"}),a.jsx("input",{type:"text",className:"form-control",value:s,onChange:h=>i(h.target.value),placeholder:"Enter game ID",required:!0,disabled:d}),a.jsx("p",{className:"text-xs text-gray-400 mt-1",children:"Enter the ID of the game you want to join"})]}),a.jsxs("div",{className:"mt-6 p-4 bg-gray-800 rounded-lg",children:[a.jsx("h3",{className:"text-lg font-semibold mb-2 text-purple-300",children:"Security Features"}),a.jsxs("ul",{className:"text-sm text-gray-300 space-y-2",children:[a.jsxs("li",{className:"flex items-start",children:[a.jsx("span",{className:"text-green-400 mr-2",children:"✓"}),a.jsx("span",{children:"Anti-fraud protection verifies all game transactions"})]}),a.jsxs("li",{className:"flex items-start",children:[a.jsx("span",{className:"text-green-400 mr-2",children:"✓"}),a.jsx("span",{children:"Secure socket connection with 256-bit encryption"})]}),a.jsxs("li",{className:"flex items-start",children:[a.jsx("span",{className:"text-green-400 mr-2",children:"✓"}),a.jsx("span",{children:"Load balancing across multiple regions ensures low latency"})]})]})]}),a.jsxs("div",{className:"mt-8 bg-purple-900 bg-opacity-50 p-4 rounded-lg mb-8",children:[a.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Important Note"}),a.jsx("p",{className:"text-gray-300 text-sm",children:"By joining a game, you agree to pay the entry fee specified by the game creator. Make sure you have enough SOL in your wallet to cover the entry fee."})]}),a.jsxs("div",{className:"flex justify-between",children:[a.jsx("button",{type:"button",onClick:c,className:"btn btn-gray",disabled:d,children:"Back"}),a.jsx("button",{type:"submit",className:"btn btn-secondary",disabled:d||!s.trim(),children:d?"Joining Game...":"Join Game"})]})]})]}),a.jsxs("div",{className:"mt-6 card",children:[a.jsx("h3",{className:"text-lg font-semibold mb-4",children:"How to Join a Game"}),a.jsxs("ol",{className:"list-decimal list-inside space-y-2 text-gray-300",children:[a.jsx("li",{children:"Get the game ID from the game creator"}),a.jsx("li",{children:"Enter the game ID in the field above"}),a.jsx("li",{children:'Click "Join Game" to join the game'}),a.jsx("li",{children:"Confirm the transaction in your wallet to pay the entry fee"}),a.jsx("li",{children:"Wait for other players to join (games automatically start when full)"})]})]})]})};var py=(e=>(e.ButtonClick="button_click",e.Navigation="navigation",e.ModalOpen="modal_open",e.ModalClose="modal_close",e.Error="error",e.Success="success",e.Notification="notification",e.GameCreate="game_create",e.GameJoin="game_join",e.GameStart="game_start",e.GameEnd="game_end",e.GameCancel="game_cancel",e.WaitingForPlayer="waiting_player",e.ChoiceCommit="choice_commit",e.ChoiceReveal="choice_reveal",e.PlayerRock="player_rock",e.PlayerPaper="player_paper",e.PlayerScissors="player_scissors",e.RoundWin="round_win",e.RoundLose="round_lose",e.RoundTie="round_tie",e.GameWin="game_win",e.GameLose="game_lose",e.Jackpot="jackpot",e.TimerTick="timer_tick",e.YourTurn="your_turn",e.TournamentStart="tournament_start",e.TournamentEnd="tournament_end",e.TournamentBracketUpdate="tournament_bracket_update",e.MatchWin="match_win",e.MatchLose="match_lose",e.BetPlaced="bet_placed",e.WinningsClaimed="winnings_claimed",e.TokenReceived="token_received",e))(py||{}),gy=(e=>(e.MainMenu="main_menu_bgm",e.InGame="in_game_bgm",e.TournamentLobby="tournament_lobby_bgm",e.IntenseMoment="intense_moment_bgm",e))(gy||{});const cc={button_click:"/sounds/effects/ui_click_01.wav",navigation:"/sounds/effects/ui_navigate_01.wav",modal_open:"/sounds/effects/ui_modal_open_01.wav",modal_close:"/sounds/effects/ui_modal_close_01.wav",error:"/sounds/effects/ui_error_01.wav",success:"/sounds/effects/ui_success_01.wav",notification:"/sounds/effects/ui_notification_01.wav",game_create:"/sounds/effects/game_create_01.wav",game_join:"/sounds/effects/game_join_01.wav",game_start:"/sounds/effects/game_start_01.wav",game_end:"/sounds/effects/game_end_01.wav",game_cancel:"/sounds/effects/game_cancel_01.wav",waiting_player:"/sounds/effects/waiting_player_01.wav",choice_commit:"/sounds/effects/rps_commit_01.wav",choice_reveal:"/sounds/effects/rps_reveal_01.wav",player_rock:"/sounds/effects/rps_rock_01.wav",player_paper:"/sounds/effects/rps_paper_01.wav",player_scissors:"/sounds/effects/rps_scissors_01.wav",round_win:"/sounds/effects/outcome_round_win_01.wav",round_lose:"/sounds/effects/outcome_round_lose_01.wav",round_tie:"/sounds/effects/outcome_tie_01.wav",game_win:"/sounds/effects/outcome_game_win_01.wav",game_lose:"/sounds/effects/outcome_game_lose_01.wav",jackpot:"/sounds/effects/outcome_jackpot_01.wav",timer_tick:"/sounds/effects/timer_tick_01.wav",your_turn:"/sounds/effects/your_turn_01.wav",tournament_start:"/sounds/effects/tournament_start_01.wav",tournament_end:"/sounds/effects/tournament_end_01.wav",tournament_bracket_update:"/sounds/effects/tournament_bracket_update_01.wav",match_win:"/sounds/effects/tournament_match_win_01.wav",match_lose:"/sounds/effects/tournament_match_lose_01.wav",bet_placed:"/sounds/effects/currency_bet_placed_01.wav",winnings_claimed:"/sounds/effects/currency_winnings_claimed_01.wav",token_received:"/sounds/effects/currency_token_received_01.wav"},yb={main_menu_bgm:"/sounds/music/bgm_main_menu_loop.mp3",in_game_bgm:"/sounds/music/bgm_ingame_loop.mp3",tournament_lobby_bgm:"/sounds/music/bgm_tournament_lobby_loop.mp3",intense_moment_bgm:"/sounds/music/bgm_intense_moment.mp3"},Xh="rpsGameAudioSettings";class wb{constructor(){J(this,"isSupported",!1);J(this,"settings");J(this,"soundEffectCache",new Map);J(this,"preloadedSoundSources",new Map);J(this,"bgMusicElement",null);J(this,"maxConcurrentEffects",5);this.settings=this.loadPreferences(),this.checkAudioSupport(),this.isSupported?(this.applyAllSettings(),this.preloadCommonSounds(),this.settings.currentBackgroundMusic):console.warn("AudioService: HTMLAudioElement is not supported or disabled by browser.")}checkAudioSupport(){try{const t=new Audio;this.isSupported=typeof t.play=="function"}catch{this.isSupported=!1}}loadPreferences(){const t={effectsVolume:.7,musicVolume:.4,isGlobalMuted:!1,isEffectsMuted:!1,isMusicMuted:!1,currentBackgroundMusic:null};if(typeof window<"u"&&window.localStorage){const n=localStorage.getItem(Xh);if(n)try{return{...t,...JSON.parse(n)}}catch(r){return console.error("AudioService: Failed to parse stored audio settings.",r),t}}return t}savePreferences(){if(!(!this.isSupported||typeof window>"u"||!window.localStorage))try{localStorage.setItem(Xh,JSON.stringify(this.settings))}catch(t){console.error("AudioService: Failed to save audio settings.",t)}}applyAllSettings(){this.isSupported&&(this.setEffectsVolume(this.settings.effectsVolume,!1),this.setMusicVolume(this.settings.musicVolume,!1))}async preloadSound(t){if(!this.isSupported||this.preloadedSoundSources.has(t))return;const n=cc[t];if(!n){console.warn(`AudioService: Sound path not found for ${t}`);return}try{const r=new Audio(n);r.preload="auto",await r.load(),this.preloadedSoundSources.set(t,n)}catch(r){console.error(`AudioService: Error preloading sound ${t}:`,r)}}async preloadAllSoundEffects(){if(!this.isSupported)return Promise.resolve([]);const t=[];for(const n in py)cc.hasOwnProperty(n)&&t.push(this.preloadSound(n));return Promise.all(t)}async preloadCommonSounds(){await this.preloadSound("button_click"),await this.preloadSound("round_win"),await this.preloadSound("round_lose")}playSoundEffect(t,n=1){if(!this.isSupported||this.settings.isGlobalMuted||this.settings.isEffectsMuted)return;const r=this.preloadedSoundSources.get(t)||cc[t];if(!r){console.warn(`AudioService: Sound effect '${t}' not found or not preloaded.`),this.preloadSound(t);return}try{let s=this.soundEffectCache.get(t);if(s||(s=[],this.soundEffectCache.set(t,s)),s=s.filter(o=>!o.ended&&!o.paused),this.soundEffectCache.set(t,s),s.length>=this.maxConcurrentEffects){const o=s.shift();o&&(o.pause(),o.currentTime=0)}const i=new Audio(r);i.volume=Math.max(0,Math.min(1,this.settings.effectsVolume*n)),i.play().catch(o=>console.error(`AudioService: Error playing sound effect '${t}':`,o)),s.push(i)}catch(s){console.error(`AudioService: Failed to play sound effect '${t}':`,s)}}playBackgroundMusic(t,n=!0,r=!1){if(!this.isSupported)return;const s=yb[t];if(!s){console.warn(`AudioService: Background music '${t}' not found.`);return}if(!(this.bgMusicElement&&this.settings.currentBackgroundMusic===s&&this.bgMusicElement.paused&&r)){if(this.bgMusicElement&&this.settings.currentBackgroundMusic===s&&!this.bgMusicElement.paused)return;this.bgMusicElement&&(this.bgMusicElement.pause(),this.bgMusicElement.src="",this.bgMusicElement.load(),this.bgMusicElement=null),this.bgMusicElement=new Audio(s)}this.bgMusicElement.loop=n,this.bgMusicElement.volume=this.settings.musicVolume,this.settings.currentBackgroundMusic=s,this.savePreferences(),this.settings.isGlobalMuted||this.settings.isMusicMuted?this.bgMusicElement.pause():this.bgMusicElement.play().catch(i=>{console.error(`AudioService: Error playing background music '${t}':`,i)})}pauseBackgroundMusic(){!this.isSupported||!this.bgMusicElement||this.bgMusicElement.pause()}resumeBackgroundMusic(){!this.isSupported||!this.bgMusicElement||this.settings.isGlobalMuted||this.settings.isMusicMuted||this.bgMusicElement.paused&&this.bgMusicElement.play().catch(t=>console.error("AudioService: Error resuming BGM:",t))}stopBackgroundMusic(){!this.isSupported||!this.bgMusicElement||(this.bgMusicElement.pause(),this.bgMusicElement.currentTime=0,this.settings.currentBackgroundMusic=null,this.savePreferences())}setEffectsVolume(t,n=!0){this.isSupported&&(this.settings.effectsVolume=Math.max(0,Math.min(1,t)),n&&this.savePreferences())}getEffectsVolume(){return this.settings.effectsVolume}setMusicVolume(t,n=!0){this.isSupported&&(this.settings.musicVolume=Math.max(0,Math.min(1,t)),this.bgMusicElement&&(this.bgMusicElement.volume=this.settings.musicVolume),n&&this.savePreferences())}getMusicVolume(){return this.settings.musicVolume}_updateMusicMuteState(){this.bgMusicElement&&(this.settings.isGlobalMuted||this.settings.isMusicMuted?this.bgMusicElement.paused||this.bgMusicElement.pause():this.bgMusicElement.paused&&this.settings.currentBackgroundMusic&&this.bgMusicElement.play().catch(t=>console.error("AudioService: Error resuming BGM on unmute:",t)))}toggleGlobalMute(){return this.isSupported?(this.settings.isGlobalMuted=!this.settings.isGlobalMuted,this._updateMusicMuteState(),this.savePreferences(),this.settings.isGlobalMuted):this.settings.isGlobalMuted}setGlobalMute(t){!this.isSupported||this.settings.isGlobalMuted===t||(this.settings.isGlobalMuted=t,this._updateMusicMuteState(),this.savePreferences())}toggleEffectsMute(){return this.isSupported?(this.settings.isEffectsMuted=!this.settings.isEffectsMuted,this.savePreferences(),this.settings.isEffectsMuted):this.settings.isEffectsMuted}setEffectsMute(t){!this.isSupported||this.settings.isEffectsMuted===t||(this.settings.isEffectsMuted=t,this.savePreferences())}toggleMusicMute(){return this.isSupported?(this.settings.isMusicMuted=!this.settings.isMusicMuted,this._updateMusicMuteState(),this.savePreferences(),this.settings.isMusicMuted):this.settings.isMusicMuted}setMusicMute(t){!this.isSupported||this.settings.isMusicMuted===t||(this.settings.isMusicMuted=t,this._updateMusicMuteState(),this.savePreferences())}isGloballyMuted(){return this.settings.isGlobalMuted}areEffectsMuted(){return this.settings.isGlobalMuted||this.settings.isEffectsMuted}isMusicMuted(){return this.settings.isGlobalMuted||this.settings.isMusicMuted}getAudioSupportStatus(){return this.isSupported}userInteracted(){this.isSupported&&this.bgMusicElement&&this.bgMusicElement.paused&&this.settings.currentBackgroundMusic&&!this.settings.isGlobalMuted&&!this.settings.isMusicMuted&&this.bgMusicElement.play().catch(t=>{})}}const de=new wb,vb=()=>{const{connected:e,publicKey:t}=ul(),{connection:n}=Sg(),[r,s]=E.useState([]),[i,o]=E.useState(!0);return E.useEffect(()=>{const l=async()=>{if(!(!e||!t))try{const u={publicKey:t.toString(),status:"available",lastSeen:Date.now()};s(h=>[...h.filter(g=>g.publicKey!==u.publicKey),u]);const d=Date.now()-5*60*1e3;s(h=>h.filter(y=>y.lastSeen>d))}catch(u){console.error("Error updating player status:",u)}finally{o(!1)}},c=setInterval(l,3e4);return l(),()=>clearInterval(c)},[e,t,n]),i?a.jsx("div",{className:"animate-pulse",children:"Loading players..."}):a.jsxs("div",{className:"bg-gray-800 rounded-lg p-4",children:[a.jsxs("h3",{className:"text-xl font-bold mb-4",children:["Players Online (",r.length,")"]}),a.jsx("div",{className:"space-y-2",children:r.map(l=>a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{className:"flex items-center",children:[a.jsx("div",{className:`w-2 h-2 rounded-full mr-2 ${l.status==="available"?"bg-green-500":l.status==="in-game"?"bg-yellow-500":"bg-blue-500"}`}),a.jsx("span",{className:"text-sm",children:l.username||l.publicKey.slice(0,4)+"..."+l.publicKey.slice(-4)})]}),a.jsx("span",{className:"text-xs text-gray-400",children:new Date(l.lastSeen).toLocaleTimeString()})]},l.publicKey))})]})},xb=({gameClient:e,gameId:t,onGameStarted:n,onLeaveGame:r})=>{const[s,i]=E.useState(null),[o,l]=E.useState(!1),[c,u]=E.useState(!0),[d,h]=E.useState(!1),[y,g]=E.useState("");E.useEffect(()=>((async()=>{try{if(u(!0),e){const b=e.wallet;b&&b.publicKey&&g(b.publicKey.toString())}if(!d&&e){const b=await e.subscribeToGameUpdates(t,T=>{console.log("Game updated:",T);const C=Buffer.from(T.host).toString("hex"),O=T.players.map(D=>({...D,pubkey:Buffer.from(D.pubkey).toString("hex")}));i({...T,host:C,players:O}),T.state===ry.CommitPhase&&!c&&(console.log("Game started!"),n(T)),u(!1)});h(!0),console.log(`Subscribed to game ${t} with ID: ${b}`)}}catch(b){console.error("Error loading game data:",b),u(!1)}})(),()=>{d&&e&&(e.unsubscribeFromGameUpdates(t),console.log(`Unsubscribed from game ${t}`))}),[e,t,d]);const S=async()=>{try{u(!0),await e.startGame(t),de.play("success")}catch(v){console.error("Error starting game:",v),u(!1)}},N=v=>v?`${v.substring(0,6)}...${v.substring(v.length-4)}`:"",A=v=>(v/1e9).toFixed(4),p=()=>{navigator.clipboard.writeText(t),l(!0),setTimeout(()=>l(!1),2e3),de.play("click")};if(c&&!s)return a.jsx("div",{className:"max-w-3xl mx-auto",children:a.jsxs("div",{className:"card",children:[a.jsx("div",{className:"flex justify-between items-center mb-6",children:a.jsx("h2",{className:"text-2xl font-bold",children:"Loading Game..."})}),a.jsx("div",{className:"flex justify-center items-center py-20",children:a.jsx("div",{className:"animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-purple-500"})})]})});if(!s)return a.jsx("div",{className:"max-w-3xl mx-auto",children:a.jsxs("div",{className:"card",children:[a.jsx("div",{className:"flex justify-between items-center mb-6",children:a.jsx("h2",{className:"text-2xl font-bold",children:"Game Not Found"})}),a.jsx("p",{className:"mb-4",children:"Could not load game data. The game might not exist or has ended."}),a.jsx("button",{onClick:r,className:"w-full py-3 rounded-lg bg-purple-600 hover:bg-purple-700",children:"Back to Home"})]})});const m=s.host===y,f=s.players.length,x=s.minPlayers,M=f>=x,w=s.currencyMode||q.SOL;return a.jsx("div",{className:"max-w-3xl mx-auto",children:a.jsxs("div",{className:"card",children:[a.jsxs("div",{className:"flex justify-between items-center mb-6",children:[a.jsx("h2",{className:"text-2xl font-bold",children:"Game Lobby"}),a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx("span",{className:"text-gray-300 text-sm",children:"Game ID:"}),a.jsxs("div",{className:"relative",children:[a.jsx("button",{onClick:p,className:"bg-gray-700 text-white px-3 py-1 rounded text-sm font-mono",children:N(t)}),o&&a.jsx("span",{className:"absolute -bottom-8 left-0 bg-gray-800 text-white text-xs px-2 py-1 rounded",children:"Copied!"})]})]})]}),a.jsxs("div",{className:"mb-6",children:[a.jsxs("h3",{className:"text-lg font-semibold mb-3",children:["Players ",a.jsxs("span",{className:"text-purple-400",children:["(",f,"/",s.maxPlayers,")"]})]}),a.jsx("div",{className:"bg-purple-900 bg-opacity-30 p-3 rounded-lg mb-4",children:a.jsxs("div",{className:"flex justify-between items-center",children:[a.jsxs("div",{className:"flex items-center",children:[a.jsx("span",{className:"text-xl mr-3",children:"👤"}),a.jsxs("span",{className:"font-mono text-purple-200",children:[N(y)," (You)"]})]}),s.host===y&&a.jsx("span",{className:"badge badge-host",children:"Host"})]})}),s.players.filter(v=>v.pubkey!==y).map((v,b)=>a.jsxs("div",{className:"flex justify-between items-center bg-gray-700 p-3 rounded-lg mb-2",children:[a.jsxs("div",{className:"flex items-center",children:[a.jsx("span",{className:"text-xl mr-3",children:"👤"}),a.jsx("span",{className:"font-mono",children:N(v.pubkey)})]}),v.pubkey===s.host&&a.jsx("span",{className:"badge badge-host",children:"Host"})]},b)),Array(s.maxPlayers-f).fill(0).map((v,b)=>a.jsxs("div",{className:"flex items-center bg-gray-700 bg-opacity-40 p-3 rounded-lg mb-2 text-gray-400",children:[a.jsx("span",{className:"text-xl mr-3",children:"👤"}),a.jsx("span",{children:"Waiting for player..."})]},`empty-${b}`))]}),a.jsx("div",{className:"mb-6",children:a.jsx(vb,{})}),a.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6",children:[a.jsxs("div",{className:"bg-gray-700 p-4 rounded-lg",children:[a.jsx("h4",{className:"text-sm text-gray-400 mb-1",children:"Entry Fee"}),a.jsxs("div",{className:"flex items-center",children:[a.jsxs("p",{className:"text-xl font-bold",children:[A(s.entryFee)," ",w===q.SOL?"SOL":"RPSTOKEN"]}),w===q.RPSTOKEN&&a.jsx("span",{className:"ml-2 px-2 py-1 bg-green-800 bg-opacity-40 rounded text-xs text-green-400",children:"-50% Fee"})]})]}),a.jsxs("div",{className:"bg-gray-700 p-4 rounded-lg",children:[a.jsx("h4",{className:"text-sm text-gray-400 mb-1",children:"Currency"}),a.jsx("p",{className:"text-xl font-bold flex items-center",children:w===q.SOL?a.jsxs(a.Fragment,{children:[a.jsx("span",{className:"w-5 h-5 mr-1 bg-yellow-500 rounded-full flex items-center justify-center text-xs text-black",children:"S"}),"SOL"]}):a.jsxs(a.Fragment,{children:[a.jsx("span",{className:"w-5 h-5 mr-1 bg-purple-500 rounded-full flex items-center justify-center text-xs text-black",children:"R"}),"RPSTOKEN"]})})]}),a.jsxs("div",{className:"bg-gray-700 p-4 rounded-lg",children:[a.jsx("h4",{className:"text-sm text-gray-400 mb-1",children:"Total Rounds"}),a.jsx("p",{className:"text-xl font-bold",children:s.totalRounds})]}),a.jsxs("div",{className:"bg-gray-700 p-4 rounded-lg",children:[a.jsx("h4",{className:"text-sm text-gray-400 mb-1",children:"Prize Pool"}),a.jsxs("div",{className:"flex items-center",children:[a.jsxs("p",{className:"text-xl font-bold",children:[A(s.gamePot)," ",w===q.SOL?"SOL":"RPSTOKEN"]}),w===q.RPSTOKEN&&a.jsx("span",{className:"ml-2 px-2 py-1 bg-green-800 bg-opacity-40 rounded text-xs text-green-400",children:"+5% Bonus"})]})]})]}),a.jsxs("div",{className:"flex space-x-4",children:[a.jsx("button",{onClick:r,className:"flex-1 py-3 rounded-lg text-lg font-bold bg-gray-700 hover:bg-gray-600",disabled:c,children:"Leave Game"}),m?a.jsx("button",{onClick:S,className:`flex-1 py-3 rounded-lg text-lg font-bold ${M&&!c?"bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700":"bg-gray-600 cursor-not-allowed opacity-50"}`,disabled:!M||c,children:c?"Starting Game...":M?"Start Game":`Need ${x-f} More Player${x-f!==1?"s":""}`}):a.jsx("div",{className:"flex-1 bg-gray-700 p-3 rounded-lg text-center",children:a.jsx("p",{className:"text-lg",children:M?"Waiting for host to start...":`Need ${x-f} more player${x-f!==1?"s":""}`})})]}),a.jsxs("div",{className:"mt-6",children:[a.jsx("h3",{className:"text-lg font-semibold mb-2",children:"How to Invite Players"}),a.jsxs("p",{className:"text-gray-300",children:["Share your Game ID with friends so they can join your game. They will need to have a Solana wallet with sufficient ",w===q.SOL?"SOL":"RPSTOKEN"," to join."]}),a.jsxs("div",{className:"mt-4 p-3 bg-gray-800 rounded-lg",children:[a.jsx("p",{className:"text-sm text-white font-semibold mb-2",children:"Copy this invite:"}),a.jsxs("div",{className:"bg-gray-900 p-2 rounded font-mono text-xs break-all",children:["Join my Rock-Paper-Scissors game on Solana! Game ID: ",t]}),a.jsx("button",{onClick:()=>{navigator.clipboard.writeText(`Join my Rock-Paper-Scissors game on Solana! Game ID: ${t}`),de.play("click"),alert("Invite copied to clipboard!")},className:"mt-2 text-sm bg-purple-600 hover:bg-purple-700 px-3 py-1 rounded",children:"Copy Invite"})]})]})]})})},Sb=({gameData:e,userPublicKey:t,onCommitChoice:n,loading:r})=>{var y;const[s,i]=E.useState(null),o=e.players.find(g=>g.pubkey===t),l=(y=o==null?void 0:o.committedChoice)==null?void 0:y.some(g=>g!==0),c=[{value:Bt.Rock,emoji:"👊",label:"Rock"},{value:Bt.Paper,emoji:"✋",label:"Paper"},{value:Bt.Scissors,emoji:"✌️",label:"Scissors"}],u=g=>{r||l||i(g)},d=()=>{s!==null&&!r&&!l&&n(s)},h=g=>{var S;return g.pubkey===t?l?"You have committed your choice":"Make your choice":(S=g.committedChoice)!=null&&S.some(N=>N!==0)?"Has committed a choice":"Waiting for choice"};return a.jsx("div",{className:"max-w-3xl mx-auto",children:a.jsxs("div",{className:"card",children:[a.jsxs("div",{className:"text-center mb-8",children:[a.jsx("h2",{className:"text-2xl font-bold mb-2",children:"Make Your Choice"}),a.jsx("p",{className:"text-gray-300",children:"Choose rock, paper, or scissors. Your choice will be kept secret until everyone reveals."})]}),a.jsx("div",{className:"flex flex-col md:flex-row justify-center gap-4 mb-8",children:c.map(g=>a.jsxs("div",{className:`choice-btn ${s===g.value?"choice-btn-selected":"choice-btn-default"} ${l?"opacity-50 cursor-not-allowed":"cursor-pointer"}`,onClick:()=>u(g.value),children:[a.jsx("div",{className:"text-5xl mb-2",children:g.emoji}),a.jsx("div",{className:"font-medium",children:g.label})]},g.value))}),a.jsxs("div",{className:"text-center mb-8",children:[a.jsx("button",{onClick:d,className:`px-8 py-3 rounded-lg font-bold ${s!==null&&!l&&!r?"bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700":"bg-gray-600 cursor-not-allowed opacity-50"}`,disabled:s===null||l||r,children:r?"Committing...":l?"Choice Committed":"Commit Choice"}),l&&a.jsx("p",{className:"mt-2 text-green-400",children:"Your choice has been committed! Waiting for other players..."})]}),a.jsxs("div",{className:"bg-gray-800 rounded-lg p-4",children:[a.jsx("h3",{className:"text-lg font-semibold mb-3",children:"Player Status"}),a.jsx("div",{className:"space-y-2",children:e.players.map((g,S)=>{var N,A;return a.jsxs("div",{className:"flex justify-between items-center bg-gray-700 p-3 rounded-lg",children:[a.jsxs("div",{className:"flex items-center",children:[a.jsx("span",{className:"text-xl mr-3",children:g.pubkey===t?"👤":"👥"}),a.jsxs("span",{children:[`Player ${S+1}`,g.pubkey===t&&" (You)"]})]}),a.jsxs("div",{className:"flex items-center",children:[a.jsx("span",{className:`text-sm ${(N=g.committedChoice)!=null&&N.some(p=>p!==0)?"text-green-400":"text-yellow-400"}`,children:h(g)}),((A=g.committedChoice)==null?void 0:A.some(p=>p!==0))&&a.jsx("span",{className:"ml-2 text-green-400",children:"✓"})]})]},S)})})]})]})})},Nb=({gameData:e,userPublicKey:t,userChoice:n,onRevealChoice:r,loading:s})=>{const i=e.players.find(d=>d.pubkey===t),o=i==null?void 0:i.revealed,l=d=>{switch(d){case Bt.Rock:return"👊";case Bt.Paper:return"✋";case Bt.Scissors:return"✌️";default:return"❓"}},c=d=>{switch(d){case Bt.Rock:return"Rock";case Bt.Paper:return"Paper";case Bt.Scissors:return"Scissors";default:return"Unknown"}},u=d=>d.pubkey===t?o?"You have revealed your choice":"Reveal your choice":d.revealed?"Has revealed their choice":"Waiting for reveal";return a.jsx("div",{className:"max-w-3xl mx-auto",children:a.jsxs("div",{className:"card",children:[a.jsxs("div",{className:"text-center mb-8",children:[a.jsx("h2",{className:"text-2xl font-bold mb-2",children:"Reveal Your Choice"}),a.jsx("p",{className:"text-gray-300",children:"All players have committed their choices. Now it's time to reveal what you selected."})]}),a.jsx("div",{className:"flex justify-center mb-8",children:a.jsxs("div",{className:"text-center",children:[a.jsx("div",{className:"text-6xl mb-4 animate-pulse",children:l(n)}),a.jsx("h3",{className:"text-xl font-bold",children:c(n)}),a.jsx("p",{className:"text-gray-400 mt-1",children:"Your committed choice"})]})}),a.jsxs("div",{className:"text-center mb-8",children:[a.jsx("button",{onClick:r,className:`px-8 py-3 rounded-lg font-bold ${!o&&!s?"bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700":"bg-gray-600 cursor-not-allowed opacity-50"}`,disabled:o||s,children:s?"Revealing...":o?"Choice Revealed":"Reveal Choice"}),o&&a.jsx("p",{className:"mt-2 text-green-400",children:"Your choice has been revealed! Waiting for other players..."})]}),a.jsxs("div",{className:"bg-gray-800 rounded-lg p-4",children:[a.jsx("h3",{className:"text-lg font-semibold mb-3",children:"Player Status"}),a.jsx("div",{className:"space-y-2",children:e.players.map((d,h)=>a.jsxs("div",{className:"flex justify-between items-center bg-gray-700 p-3 rounded-lg",children:[a.jsxs("div",{className:"flex items-center",children:[a.jsx("span",{className:"text-xl mr-3",children:d.pubkey===t?"👤":"👥"}),a.jsxs("span",{children:[`Player ${h+1}`,d.pubkey===t&&" (You)"]})]}),a.jsxs("div",{className:"flex items-center",children:[a.jsx("span",{className:`text-sm ${d.revealed?"text-green-400":"text-yellow-400"}`,children:u(d)}),d.revealed&&a.jsx("span",{className:"ml-2 text-green-400",children:"✓"})]})]},h))})]}),a.jsxs("div",{className:"mt-8 bg-purple-900 bg-opacity-50 p-4 rounded-lg",children:[a.jsx("h3",{className:"font-semibold mb-2",children:"What happens next?"}),a.jsx("p",{className:"text-gray-300 text-sm",children:"Once all players have revealed their choices, the game will determine the winner based on the rock-paper-scissors rules. Each player's choice will be compared against all other players, and points will be awarded for each winning comparison."})]})]})})};class Eb{constructor(){J(this,"STORAGE_KEY","solana-rps-user-profiles");J(this,"LEADERBOARD_KEY","solana-rps-leaderboard")}async getUserProfile(t){try{return this.getAllProfiles()[t]||this.createDefaultProfile(t)}catch(n){return console.error("Error getting user profile:",n),null}}createDefaultProfile(t){const n={walletAddress:t,displayName:null,totalGames:0,wins:0,losses:0,ties:0,winRate:0,totalWagered:0,netProfit:0,lastActive:Date.now()},r=this.getAllProfiles();return r[t]=n,this.saveProfiles(r),n}async updateProfileAfterGame(t,n,r){const s=await this.getUserProfile(t);if(!s)throw new Error("Profile not found");s.totalGames+=1,s.lastActive=Date.now(),s.totalWagered+=r,n==="win"?(s.wins+=1,s.netProfit+=r):n==="loss"?(s.losses+=1,s.netProfit-=r):n==="tie"&&(s.ties+=1),s.winRate=s.totalGames>0?Number((s.wins/s.totalGames*100).toFixed(1)):0;const i=this.getAllProfiles();return i[t]=s,this.saveProfiles(i),this.updateLeaderboard(),s}async trackGameCompletion(t,n){if(!t)return;const r=t.toString(),s=n.winners.includes(r),i=n.gamePot/2;let o;n.winners.length>1?o="tie":s?o="win":o="loss",await this.updateProfileAfterGame(r,o,i)}async updateDisplayName(t,n){const r=await this.getUserProfile(t);if(!r)throw new Error("Profile not found");r.displayName=n;const s=this.getAllProfiles();return s[t]=r,this.saveProfiles(s),r}getAllProfiles(){const t=localStorage.getItem(this.STORAGE_KEY);return t?JSON.parse(t):{}}saveProfiles(t){localStorage.setItem(this.STORAGE_KEY,JSON.stringify(t))}updateLeaderboard(){const t=this.getAllProfiles(),n=Object.values(t);n.sort((r,s)=>s.winRate!==r.winRate?s.winRate-r.winRate:s.totalGames-r.totalGames),n.forEach((r,s)=>{r.rank=s+1}),localStorage.setItem(this.LEADERBOARD_KEY,JSON.stringify(n))}async getLeaderboard(t=10){try{const n=localStorage.getItem(this.LEADERBOARD_KEY);return n?JSON.parse(n).slice(0,t):(this.updateLeaderboard(),this.getLeaderboard(t))}catch(n){return console.error("Error getting leaderboard:",n),[]}}async getUserRank(t){try{const r=(await this.getLeaderboard(1e3)).findIndex(s=>s.walletAddress===t);return r>=0?r+1:null}catch(n){return console.error("Error getting user rank:",n),null}}async deleteProfile(t){const n=this.getAllProfiles();n[t]&&(delete n[t],this.saveProfiles(n),this.updateLeaderboard())}async clearAllProfiles(){localStorage.removeItem(this.STORAGE_KEY),localStorage.removeItem(this.LEADERBOARD_KEY)}}const vs=new Eb,Mb=({gameData:e,userPublicKey:t,setCurrentView:n})=>{const[r,s]=E.useState(null),[i,o]=E.useState(!1),[l,c]=E.useState([]),[u,d]=E.useState(!1);return E.useEffect(()=>{const h=e.players.find(S=>S.pubkey===t.toString());s(h),d(e.currentRound===e.totalRounds);const y=Math.max(...e.players.map(S=>S.score)),g=e.players.filter(S=>S.score===y);c(g),h&&g.some(S=>S.pubkey===h.pubkey)&&o(!0)},[e,t]),E.useEffect(()=>{(async()=>{u&&r&&await vs.trackGameCompletion(t,{score:r.score,winners:l.map(y=>y.pubkey),gamePot:e.gamePot})})()},[u,r,t,l,e.gamePot]),a.jsxs("div",{className:"card",children:[a.jsx("h2",{children:"Game Results"}),a.jsx("div",{className:"result-status",children:i?a.jsxs("div",{className:"winner",children:[a.jsx("h3",{children:"You Won!"}),a.jsx("p",{children:"Congratulations! You've won the game."})]}):a.jsxs("div",{className:"loser",children:[a.jsx("h3",{children:"You Lost"}),a.jsx("p",{children:"Better luck next time!"})]})}),a.jsxs("div",{className:"score-board",children:[a.jsx("h3",{children:"Final Scores"}),a.jsx("ul",{children:e.players.map(h=>a.jsxs("li",{className:h.pubkey===t.toString()?"current-player":"",children:[a.jsxs("span",{className:"player-name",children:[h.pubkey.substring(0,6),"...",h.pubkey.substring(h.pubkey.length-4)]}),a.jsx("span",{className:"player-score",children:h.score})]},h.pubkey))})]}),a.jsxs("div",{className:"action-buttons",children:[a.jsx("button",{onClick:()=>n(GameView.HOME),children:"Back to Home"}),a.jsx("button",{onClick:()=>n(GameView.CREATE_GAME),children:"Create New Game"}),a.jsx("button",{onClick:()=>n(GameView.PROFILE),children:"View Profile"})]})]})};class bb{constructor(t){J(this,"client");J(this,"isRunning",!1);J(this,"stats",{currentStreak:0,wins:0,losses:0,ties:0,totalWagered:0,netProfit:0,gameHistory:[]});J(this,"currentWager",0);this.client=t}async start(t,n,r,s=2e3,i,o){if(!this.isRunning)for(this.isRunning=!0,this.currentWager=t;this.isRunning;)try{const{gameId:l}=await this.client.createGame(3,3,1,this.currentWager,30,!1),c=Math.floor(Math.random()*3)+1,u=this.generateRandomSalt();await this.client.commitChoice(l,c,u),await this.client.revealChoice(l,c,u);const d=["win","loss","tie"],h=d[Math.floor(Math.random()*d.length)],y=[Math.floor(Math.random()*3)+1,Math.floor(Math.random()*3)+1];this.stats.totalWagered+=this.currentWager;const g={playerChoice:c,opponentChoices:y,result:h,timestamp:Date.now(),wagerAmount:this.currentWager};h==="win"?(this.stats.wins++,this.stats.currentStreak=Math.max(0,this.stats.currentStreak)+1,this.stats.netProfit+=this.currentWager*1.9):h==="loss"?(this.stats.losses++,this.stats.currentStreak=this.stats.currentStreak>0?-1:this.stats.currentStreak-1,this.stats.netProfit-=this.currentWager):this.stats.ties++,this.stats.gameHistory.push(g),this.currentWager=i(this.stats,g,this.currentWager),await new Promise(S=>setTimeout(S,s))}catch(l){console.error("Auto-play error:",l),o(l),await new Promise(c=>setTimeout(c,5e3))}}stop(){this.isRunning=!1}getStats(){return{...this.stats}}isAutoPlaying(){return this.isRunning}resetStats(){this.stats={currentStreak:0,wins:0,losses:0,ties:0,totalWagered:0,netProfit:0,gameHistory:[]}}generateRandomSalt(){return Array.from(crypto.getRandomValues(new Uint8Array(32))).map(t=>t.toString(16).padStart(2,"0")).join("")}}const jb=({isActive:e,onToggle:t,wagerAmount:n,setWagerAmount:r,stats:s,selectedCurrency:i,onCurrencyChange:o,gameHistory:l,strategy:c,onStrategyChange:u,gameSpeed:d,onGameSpeedChange:h,stopOnProfit:y,onStopProfitChange:g,stopOnLoss:S,onStopLossChange:N,useStopLimits:A,onUseStopLimitsChange:p})=>{const[m,f]=E.useState(!1),[x,M]=E.useState(!1),[w,v]=E.useState(!1);E.useEffect(()=>{if(l.length>0){const D=l[l.length-1];D.result==="win"?(f(!0),setTimeout(()=>f(!1),1e3),s.netProfit>0&&(v(!0),setTimeout(()=>v(!1),1e3))):D.result==="loss"&&(M(!0),setTimeout(()=>M(!1),1e3))}},[l,s.netProfit]);const b=()=>{de.play("hover")},T=()=>{de.play("click"),t()},C=D=>typeof D=="number"&&!isNaN(D)?D:0,O=D=>C(D).toFixed(2);return a.jsxs("div",{className:"bg-gray-800 rounded-lg p-4 md:p-6 mb-8 shadow-lg border border-gray-700",children:[a.jsxs("div",{className:"flex justify-between items-center mb-6",children:[a.jsx("h3",{className:"text-xl font-bold",children:"Auto Play"}),a.jsxs("div",{className:"flex items-center",children:[a.jsx("span",{className:"mr-3 hidden sm:inline",children:e?"Running":"Stopped"}),a.jsx("button",{onClick:T,onMouseEnter:b,className:`px-4 py-2 rounded font-bold transform transition-all duration-150 hover:scale-105 ${e?"bg-red-600 hover:bg-red-700 shadow-lg shadow-red-700/20":"bg-green-600 hover:bg-green-700 shadow-lg shadow-green-700/20"}`,children:e?"STOP":"START"})]})]}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6 mb-6",children:[a.jsxs("div",{className:"bg-gray-900 p-4 rounded-lg shadow-inner",children:[a.jsx("label",{className:"block text-sm font-medium mb-2",children:"Wager Amount"}),a.jsxs("div",{className:"flex",children:[a.jsx("input",{type:"number",value:n,onChange:D=>{r(parseFloat(D.target.value)||.01),de.play("hover")},min:"0.01",step:"0.01",disabled:e,className:"w-full px-4 py-2 bg-gray-700 rounded-l border border-gray-600 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"}),a.jsxs("select",{value:i,onChange:D=>{const I=D.target.value==="SOL"?q.SOL:q.RPSTOKEN;o(I),de.play("click")},disabled:e,className:"bg-gray-700 border-l-0 border border-gray-600 rounded-r px-3 focus:outline-none focus:ring-2 focus:ring-purple-500",children:[a.jsx("option",{value:"SOL",children:"SOL"}),a.jsx("option",{value:"RPS_TOKEN",children:"RPSTOKEN"})]})]})]}),a.jsxs("div",{className:"bg-gray-900 p-4 rounded-lg shadow-inner",children:[a.jsx("label",{className:"block text-sm font-medium mb-2",children:"Betting Strategy"}),a.jsxs("select",{value:c,onChange:D=>{const I=D.target.value;u&&u(I),de.play("click")},disabled:e,className:"w-full px-4 py-2 bg-gray-700 rounded border border-gray-600 text-white focus:outline-none focus:ring-2 focus:ring-purple-500",children:[a.jsx("option",{value:Gt.FIXED,children:"Fixed Bet"}),a.jsx("option",{value:Gt.MARTINGALE,children:"Martingale (Double on Loss)"}),a.jsx("option",{value:Gt.DALEMBERT,children:"D'Alembert (Increase 1 Unit on Loss)"}),a.jsx("option",{value:Gt.FIBONACCI,children:"Fibonacci Sequence"})]})]}),a.jsxs("div",{className:"bg-gray-900 p-4 rounded-lg shadow-inner",children:[a.jsx("label",{className:"block text-sm font-medium mb-2",children:"Game Speed (ms)"}),a.jsx("input",{type:"range",min:"500",max:"5000",step:"500",value:d,onChange:D=>{h&&h(parseInt(D.target.value)),de.play("hover")},disabled:e,className:"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"}),a.jsxs("div",{className:"flex justify-between text-xs text-gray-400 mt-1",children:[a.jsx("span",{children:"Fast"}),a.jsxs("span",{children:[d,"ms"]}),a.jsx("span",{children:"Slow"})]})]}),a.jsxs("div",{className:"bg-gray-900 p-4 rounded-lg shadow-inner",children:[a.jsxs("div",{className:"flex items-center mb-2",children:[a.jsx("input",{type:"checkbox",id:"useStopLimits",checked:A,onChange:D=>{p&&p(D.target.checked),de.play("click")},disabled:e,className:"mr-2 h-4 w-4 rounded text-purple-500 focus:ring-purple-500"}),a.jsx("label",{htmlFor:"useStopLimits",className:"text-sm font-medium",children:"Auto-Stop Limits"})]}),a.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[a.jsxs("div",{children:[a.jsx("label",{className:"block text-xs text-gray-400",children:"Stop on Profit"}),a.jsx("input",{type:"number",value:y,onChange:D=>{g&&g(parseFloat(D.target.value)||0),de.play("hover")},min:"0",step:"0.1",disabled:e||!A,className:"w-full px-2 py-1 bg-gray-700 rounded border border-gray-600 text-white text-sm focus:outline-none focus:ring-1 focus:ring-purple-500"})]}),a.jsxs("div",{children:[a.jsx("label",{className:"block text-xs text-gray-400",children:"Stop on Loss"}),a.jsx("input",{type:"number",value:S,onChange:D=>{N&&N(parseFloat(D.target.value)||0),de.play("hover")},min:"0",step:"0.1",disabled:e||!A,className:"w-full px-2 py-1 bg-gray-700 rounded border border-gray-600 text-white text-sm focus:outline-none focus:ring-1 focus:ring-purple-500"})]})]})]})]}),a.jsxs("div",{className:"bg-gray-900 rounded-lg p-4 shadow-inner",children:[a.jsx("h4",{className:"text-lg font-medium mb-4",children:"Session Stats"}),a.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4 text-center",children:[a.jsxs("div",{className:"bg-gray-800 p-3 rounded-lg shadow",children:[a.jsx("div",{className:"text-sm text-gray-400",children:"Current Streak"}),a.jsx("div",{className:`text-xl font-bold ${C(s.currentStreak)>0?"text-green-400 drop-shadow-glow-green":C(s.currentStreak)<0?"text-red-400 drop-shadow-glow-red":"text-white"}`,children:C(s.currentStreak)>0?`+${C(s.currentStreak)}`:C(s.currentStreak)})]}),a.jsxs("div",{className:`bg-gray-800 p-3 rounded-lg shadow transition-all duration-300 ${m?"scale-110 bg-green-900 bg-opacity-50":""}`,children:[a.jsx("div",{className:"text-sm text-gray-400",children:"Wins"}),a.jsx("div",{className:"text-xl font-bold text-green-400",children:C(s.wins)})]}),a.jsxs("div",{className:`bg-gray-800 p-3 rounded-lg shadow transition-all duration-300 ${x?"scale-110 bg-red-900 bg-opacity-50":""}`,children:[a.jsx("div",{className:"text-sm text-gray-400",children:"Losses"}),a.jsx("div",{className:"text-xl font-bold text-red-400",children:C(s.losses)})]}),a.jsxs("div",{className:"bg-gray-800 p-3 rounded-lg shadow",children:[a.jsx("div",{className:"text-sm text-gray-400",children:"Total Wagered"}),a.jsxs("div",{className:"text-xl font-bold",children:[O(s.totalWagered)," ",i===q.SOL?"SOL":"RPSTOKEN"]})]}),a.jsxs("div",{className:`bg-gray-800 p-3 rounded-lg shadow transition-all duration-300 ${w?"scale-110 bg-green-900 bg-opacity-50":""}`,children:[a.jsx("div",{className:"text-sm text-gray-400",children:"Net Profit"}),a.jsxs("div",{className:`text-xl font-bold ${C(s.netProfit)>=0?"text-green-400":"text-red-400"}`,children:[C(s.netProfit)>0?"+":"",O(s.netProfit)," ",i===q.SOL?"SOL":"RPSTOKEN"]})]})]})]}),a.jsxs("div",{className:"mt-6",children:[a.jsx("h4",{className:"text-lg font-medium mb-2",children:"Game History"}),a.jsx("div",{className:"bg-gray-900 p-3 rounded-lg shadow-inner overflow-x-auto",children:a.jsxs("div",{className:"flex flex-wrap gap-1 min-h-16",children:[l.map((D,I)=>a.jsx("div",{className:`w-8 h-8 rounded-full flex items-center justify-center text-xs font-bold transition-all duration-300 hover:scale-110 ${D.result==="win"?"bg-green-600 hover:bg-green-500":D.result==="loss"?"bg-red-600 hover:bg-red-500":"bg-gray-600 hover:bg-gray-500"}`,title:`Game ${I+1}: ${D.playerChoice} vs ${D.opponentChoices.join(", ")} - ${D.result.toUpperCase()}`,onMouseEnter:()=>de.play("hover"),children:D.result==="win"?"W":D.result==="loss"?"L":"T"},I)),l.length===0&&a.jsx("div",{className:"text-gray-500 text-sm py-3",children:"No games played yet"})]})})]})]})},em={0:"❓",1:"🪨",2:"📄",3:"✂️"},_b={win:"🏆",loss:"😢",tie:"🤝"},Ab=({isPlaying:e,playerChoice:t=0,opponentChoice:n=0,result:r})=>{const[s,i]=E.useState("idle"),[o,l]=E.useState(""),[c,u]=E.useState(!1),[d,h]=E.useState(!1),[y,g]=E.useState(!1),[S,N]=E.useState(!1),[A,p]=E.useState(!1),m=E.useRef(null);E.useEffect(()=>{if(e){i("counting"),u(!1),N(!1),p(!1),de.play("countdown");const x=["Rock","Paper","Scissors","Shoot!"];let M=0;const w=setInterval(()=>{M<x.length?(l(x[M]),h(!0),g(!0),setTimeout(()=>{h(!1),g(!1)},200),M++):(clearInterval(w),i("result"),setTimeout(()=>{u(!0),r==="win"?(de.play("win"),p(!0),setTimeout(()=>p(!1),7e3)):r==="loss"?de.play("lose"):de.play("tie"),N(!0),setTimeout(()=>N(!1),2e3)},500))},800);return()=>{clearInterval(w)}}else i("idle"),u(!1),N(!1),p(!1)},[e,r]);const f=()=>{const x=[];for(let w=0;w<20;w++){const v=Math.random()*.5,b=Math.random()*12+5,T=Math.random()*1+1,C=Math.random()*100,O=Math.random()*100,D=`${Math.random()>.5?"-":""}${Math.random()*100+50}px`,I=`${Math.random()>.5?"-":""}${Math.random()*100+50}px`,F=`${Math.random()*360}deg`,_=String(Math.random()*.5+.5),R=r==="win"?`hsl(${Math.random()*60+30}, 100%, 50%)`:r==="loss"?`hsl(${Math.random()*20+350}, 100%, 50%)`:`hsl(${Math.random()*60+200}, 100%, 50%)`,z={width:`${b}px`,height:`${b}px`,backgroundColor:R,left:`${C}%`,top:`${O}%`,"--tx":D,"--ty":I,"--rotate":F,"--scale":_,animation:`particle-animation ${T}s ease-out ${v}s forwards`};x.push(a.jsx("div",{className:"particle",style:z},w))}return x};return a.jsxs("div",{className:"relative w-full max-w-md mx-auto h-64 bg-gray-800 bg-opacity-40 rounded-xl p-4 overflow-hidden",children:[A&&a.jsx("div",{className:"confetti-overlay"}),a.jsxs("div",{className:"absolute inset-0 overflow-hidden",children:[a.jsx("div",{className:"absolute -left-8 -bottom-8 w-32 h-32 rounded-full bg-purple-900 opacity-20 animate-float"}),a.jsx("div",{className:"absolute -right-8 -top-8 w-24 h-24 rounded-full bg-blue-900 opacity-20 animate-float-reverse"})]}),a.jsxs("div",{className:"relative z-10 h-full flex flex-col items-center justify-center",children:[s==="idle"&&a.jsxs("div",{className:"text-center text-gray-400",children:[a.jsx("div",{className:"text-4xl mb-2",children:"🎮"}),a.jsx("p",{children:"Waiting for game to start..."})]}),s==="counting"&&a.jsxs("div",{className:"text-center",children:[a.jsx("div",{className:"text-4xl font-bold mb-4",children:o}),a.jsxs("div",{className:"flex justify-center items-center gap-12",children:[a.jsx("div",{className:`text-5xl ${d?"animate-shake":""}`,children:"👊"}),a.jsx("div",{className:"text-xl",children:"VS"}),a.jsx("div",{className:`text-5xl ${y?"animate-shake":""}`,children:"👊"})]})]}),s==="result"&&a.jsx("div",{className:"text-center",children:c?a.jsxs(a.Fragment,{children:[a.jsxs("div",{className:"flex justify-center items-center gap-12 mb-6",children:[a.jsx("div",{className:`text-5xl transition-all duration-500 transform ${r==="win"?"scale-125 drop-shadow-glow-green":""}`,children:em[t]}),a.jsx("div",{className:"text-xl",children:"VS"}),a.jsx("div",{className:`text-5xl transition-all duration-500 transform ${r==="loss"?"scale-125 drop-shadow-glow-red":""}`,children:em[n]})]}),a.jsxs("div",{className:`text-center p-3 rounded-lg transition-all duration-500 ${r==="win"?"bg-green-900 bg-opacity-40":r==="loss"?"bg-red-900 bg-opacity-40":"bg-blue-900 bg-opacity-40"}`,children:[a.jsx("div",{className:"text-4xl mb-2",children:_b[r||"tie"]}),a.jsx("div",{className:"font-bold text-xl",children:r==="win"?"You Win!":r==="loss"?"You Lose!":"It's a Tie!"})]})]}):a.jsx("div",{className:"text-4xl animate-loading",children:"⏳"})})]}),S&&a.jsx("div",{ref:m,className:"absolute inset-0 overflow-hidden pointer-events-none",children:f()})]})},Tb=({gameClient:e,onBackToHome:t})=>{const[n,r]=E.useState(null),[s,i]=E.useState(!1),[o,l]=E.useState(.1),[c,u]=E.useState(q.SOL),[d,h]=E.useState(Gt.FIXED),[y,g]=E.useState(2e3),[S,N]=E.useState(5),[A,p]=E.useState(5),[m,f]=E.useState(!1),[x,M]=E.useState({currentStreak:0,wins:0,losses:0,ties:0,totalWagered:0,netProfit:0,gameHistory:[]}),[w,v]=E.useState({isPlaying:!1}),[b,T]=E.useState("");E.useEffect(()=>{e&&r(new bb(e))},[e]),E.useEffect(()=>{s&&m&&n&&(S>0&&x.netProfit>=S||A>0&&x.netProfit<=-A)&&O()},[x.netProfit,s,m,S,A]);const C=(F,_,R)=>{if(!_||d===Gt.FIXED)return F;switch(d){case Gt.MARTINGALE:return _==="loss"?F*2:o;case Gt.DALEMBERT:const z=o*.1;return _==="loss"?F+z:_==="win"?Math.max(o,F-z):F;case Gt.FIBONACCI:if(_==="win"||_==="tie")return o;const L=R<0?Math.abs(R):0;if(L<=1)return o;let P=1,U=1;for(let $=2;$<L;$++){const Y=P+U;P=U,U=Y}return o*U;default:return F}},O=async()=>{if(n)if(s)n.stop(),i(!1),v({isPlaying:!1});else{i(!0),T("");try{await n.start(o,c,d,y,(F,_,R)=>{const z=C(R,_.result,F.currentStreak);return M(F),v({isPlaying:!0,playerChoice:_.playerChoice,opponentChoice:_.opponentChoices[0],result:_.result}),setTimeout(()=>{v(L=>({...L,isPlaying:!1})),setTimeout(()=>{v({isPlaying:!0})},300)},y*.8),z},F=>{T(`Error: ${F.message}`)})}catch(F){console.error("Failed to start auto-play:",F),i(!1),T(`Failed to start auto-play: ${F.message}`)}}},D=F=>{u(F)},I=()=>{n&&(n.resetStats(),M({currentStreak:0,wins:0,losses:0,ties:0,totalWagered:0,netProfit:0,gameHistory:[]}))};return a.jsxs("div",{className:"max-w-4xl mx-auto",children:[a.jsxs("div",{className:"card bg-gray-800 p-6 rounded-lg shadow-lg mb-6",children:[a.jsxs("div",{className:"flex justify-between items-center mb-6",children:[a.jsx("h2",{className:"text-2xl font-bold",children:"Automated RPS"}),a.jsx("button",{onClick:t,className:"px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded",children:"Back to Home"})]}),b&&a.jsx("div",{className:"bg-red-600 bg-opacity-70 rounded-lg p-4 mb-6 text-center",children:b}),a.jsx(Ab,{isPlaying:w.isPlaying,playerChoice:w.playerChoice,opponentChoice:w.opponentChoice,result:w.result}),a.jsx(jb,{isActive:s,onToggle:O,wagerAmount:o,setWagerAmount:l,stats:x,selectedCurrency:c,onCurrencyChange:D,gameHistory:x.gameHistory,strategy:d,onStrategyChange:h,gameSpeed:y,onGameSpeedChange:g,stopOnProfit:S,onStopProfitChange:N,stopOnLoss:A,onStopLossChange:p,useStopLimits:m,onUseStopLimitsChange:f}),a.jsxs("div",{className:"flex justify-between mt-6",children:[a.jsx("button",{onClick:I,className:"px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded",disabled:s,children:"Reset Stats"}),a.jsx("div",{className:"text-sm text-gray-400",children:"Note: This is a simulation. Real blockchain transactions are not being made."})]})]}),a.jsxs("div",{className:"card bg-gray-800 p-6 rounded-lg shadow-lg",children:[a.jsx("h3",{className:"text-xl font-bold mb-4",children:"Strategy Details"}),a.jsxs("div",{className:"text-gray-300 mb-4",children:[a.jsx("h4",{className:"font-bold",children:"Betting Strategies:"}),a.jsxs("ul",{className:"list-disc list-inside space-y-1 mt-2 ml-4",children:[a.jsxs("li",{children:[a.jsx("span",{className:"font-semibold",children:"Fixed:"})," Always bet the same amount"]}),a.jsxs("li",{children:[a.jsx("span",{className:"font-semibold",children:"Martingale:"})," Double your bet after each loss, reset after a win"]}),a.jsxs("li",{children:[a.jsx("span",{className:"font-semibold",children:"D'Alembert:"})," Increase bet by one unit after a loss, decrease by one unit after a win"]}),a.jsxs("li",{children:[a.jsx("span",{className:"font-semibold",children:"Fibonacci:"})," Follow the Fibonacci sequence (1,1,2,3,5,8...) during losing streaks"]})]})]}),a.jsxs("div",{className:"text-gray-300",children:[a.jsx("h4",{className:"font-bold",children:"Auto-Stop Limits:"}),a.jsx("p",{className:"mt-2 ml-4",children:"Set profit or loss limits to automatically stop playing when reached. This helps manage your bankroll and avoid excessive losses."})]})]})]})},Cb=({className:e=""})=>{const[t,n]=E.useState(""),[r,s]=E.useState(""),[i,o]=E.useState(!1),[l,c]=E.useState(!1),[u,d]=E.useState("phantom"),[h,y]=E.useState(!1);E.useEffect(()=>{g(),S()},[]);const g=()=>{const f=navigator.userAgent;let x="";f.indexOf("Chrome")>-1?x="Chrome":f.indexOf("Firefox")>-1?x="Firefox":f.indexOf("Safari")>-1?x="Safari":f.indexOf("Edge")>-1||f.indexOf("Edg")>-1?x="Edge":f.indexOf("Opera")>-1||f.indexOf("OPR")>-1?x="Opera":x="Unknown";let M="";f.indexOf("Win")>-1?M="Windows":f.indexOf("Mac")>-1?M="macOS":f.indexOf("Linux")>-1?M="Linux":f.indexOf("Android")>-1?M="Android":f.indexOf("iPhone")>-1||f.indexOf("iPad")>-1?M="iOS":M="Unknown",n(x),s(M)},S=()=>{var M,w,v;const f=(w=(M=window.phantom)==null?void 0:M.solana)==null?void 0:w.isPhantom;o(!!f);const x=(v=window.solflare)==null?void 0:v.isSolflare;c(!!x)},N=f=>f==="phantom"?r==="iOS"||r==="Android"?"https://phantom.app/download":t==="Chrome"?"https://chrome.google.com/webstore/detail/phantom/bfnaelmomeimhlpmgjnjophhpkkoljpa":t==="Firefox"?"https://addons.mozilla.org/en-US/firefox/addon/phantom-app/":t==="Edge"?"https://microsoftedge.microsoft.com/addons/detail/phantom/dfpilgmgeoehhkocfmppkegbjpkgfgha":"https://phantom.app/download":r==="iOS"||r==="Android"?"https://solflare.com/download":t==="Chrome"?"https://chrome.google.com/webstore/detail/solflare-wallet/bhhhlbepdkbapadjdnnojkbgioiodbic":t==="Firefox"?"https://addons.mozilla.org/en-US/firefox/addon/solflare-wallet/":t==="Edge"?"https://microsoftedge.microsoft.com/addons/detail/solflare-wallet/hgkjjcnchdejehjfomkjeoeebkmgeknd":"https://solflare.com/download",A=f=>{const x=f==="phantom"?"Phantom":"Solflare";return r==="iOS"||r==="Android"?a.jsxs("ol",{className:"list-decimal list-inside text-left text-gray-300 space-y-2 mb-4",children:[a.jsxs("li",{children:["Download the ",x," app from the app store"]}),a.jsx("li",{children:"Create a new wallet or import an existing one"}),a.jsx("li",{children:"Open the app and use the built-in browser to return to this site"})]}):a.jsxs("ol",{className:"list-decimal list-inside text-left text-gray-300 space-y-2 mb-4",children:[a.jsxs("li",{children:["Click the download button to install the ",x," extension"]}),a.jsx("li",{children:"Follow the installation instructions in your browser"}),a.jsx("li",{children:"Create a new wallet or import an existing one"}),a.jsx("li",{children:"Return to this page and refresh to connect"})]})},p=f=>{d(f),de.play("click")},m=f=>{const x=f==="phantom"?i:l,M=f==="phantom"?"Phantom":"Solflare";return x?a.jsxs("div",{className:"flex items-center text-green-500 mb-2",children:[a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2",viewBox:"0 0 20 20",fill:"currentColor",children:a.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),a.jsxs("span",{children:[M," is installed"]})]}):a.jsxs("div",{className:"flex items-center text-yellow-500 mb-2",children:[a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2",viewBox:"0 0 20 20",fill:"currentColor",children:a.jsx("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}),a.jsxs("span",{children:[M," is not installed"]})]})};return a.jsxs("div",{className:`wallet-helper ${e}`,children:[a.jsxs("div",{className:"wallet-status text-center mb-6",children:[a.jsx("h2",{className:"text-2xl font-bold text-white mb-2",children:"Connect a Wallet"}),a.jsx("p",{className:"text-gray-300 mb-4",children:"You need a Solana wallet to play Rock Paper Scissors on the blockchain."}),(i||l)&&a.jsxs("div",{className:"mt-4 max-w-md mx-auto",children:[a.jsx("p",{className:"text-green-400 mb-2",children:"Wallet detected! Click below to connect:"}),a.jsx("div",{className:"transform hover:scale-105 transition-transform duration-300",children:a.jsx(uf,{className:"!bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 !rounded-xl !py-4 !px-8 !text-xl"})})]})]}),!i&&!l&&a.jsxs("div",{className:"wallet-installation bg-gray-800 bg-opacity-50 rounded-xl p-6 max-w-2xl mx-auto",children:[a.jsxs("div",{className:"wallet-tabs flex mb-4",children:[a.jsx("button",{className:`flex-1 py-2 ${u==="phantom"?"border-b-2 border-purple-500 text-purple-400":"text-gray-400"}`,onClick:()=>p("phantom"),children:a.jsxs("span",{className:"flex items-center justify-center",children:[a.jsx("img",{src:"https://phantom.app/apple-touch-icon.png",alt:"Phantom",className:"w-5 h-5 mr-2"}),"Phantom"]})}),a.jsx("button",{className:`flex-1 py-2 ${u==="solflare"?"border-b-2 border-orange-500 text-orange-400":"text-gray-400"}`,onClick:()=>p("solflare"),children:a.jsxs("span",{className:"flex items-center justify-center",children:[a.jsx("img",{src:"https://solflare.com/assets/icon-solflare-orange.svg",alt:"Solflare",className:"w-5 h-5 mr-2"}),"Solflare"]})})]}),a.jsx("div",{className:"wallet-content",children:u==="phantom"?a.jsxs("div",{className:"phantom-content",children:[m("phantom"),a.jsx("p",{className:"text-gray-300 mb-4",children:"Phantom is a friendly Solana wallet built for DeFi & NFTs."}),A("phantom"),a.jsxs("a",{href:N("phantom"),target:"_blank",rel:"noopener noreferrer",className:"inline-block bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded-lg transition-colors",onClick:()=>de.play("click"),children:["Download Phantom for ",t]})]}):a.jsxs("div",{className:"solflare-content",children:[m("solflare"),a.jsx("p",{className:"text-gray-300 mb-4",children:"Solflare is a powerful wallet for Solana with full DeFi support."}),A("solflare"),a.jsxs("a",{href:N("solflare"),target:"_blank",rel:"noopener noreferrer",className:"inline-block bg-orange-600 hover:bg-orange-700 text-white font-bold py-2 px-4 rounded-lg transition-colors",onClick:()=>de.play("click"),children:["Download Solflare for ",t]})]})}),a.jsx("div",{className:"wallet-footer mt-6 pt-4 border-t border-gray-700",children:a.jsxs("p",{className:"text-sm text-gray-400",children:["By connecting a wallet, you agree to the game's ",a.jsx("a",{href:"#",className:"text-purple-400 hover:underline",children:"Terms of Service"})," and acknowledge the ",a.jsx("a",{href:"#",className:"text-purple-400 hover:underline",children:"Privacy Policy"}),"."]})})]}),a.jsx("div",{className:"mt-6 p-4 bg-purple-900 bg-opacity-20 rounded-lg border border-purple-700",children:a.jsxs("div",{className:"flex items-start",children:[a.jsx("div",{className:"text-purple-400 text-xl mr-3",children:"ℹ️"}),a.jsxs("div",{children:[a.jsx("h3",{className:"text-lg font-semibold text-purple-300 mb-1",children:"About Wallet Popups"}),a.jsx("p",{className:"text-gray-300 mb-2",children:'When connecting your Phantom wallet, you may see a popup about "Monad Testnet". This is unrelated to our app.'}),a.jsxs("button",{onClick:()=>y(!h),className:"text-purple-400 hover:text-purple-300 text-sm flex items-center",children:[h?"Hide details":"Show details",a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:`h-4 w-4 ml-1 transition-transform ${h?"rotate-180":""}`,viewBox:"0 0 20 20",fill:"currentColor",children:a.jsx("path",{fillRule:"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z",clipRule:"evenodd"})})]}),h&&a.jsxs("div",{className:"mt-2 p-3 bg-gray-800 rounded-lg text-sm",children:[a.jsxs("p",{className:"mb-2",children:[a.jsx("strong",{children:"What is Monad Testnet?"})," Monad is an Ethereum-compatible blockchain that Phantom is testing. It's completely separate from Solana and our game."]}),a.jsxs("p",{className:"mb-2",children:[a.jsx("strong",{children:"Should you enable it?"})," You don't need to enable Monad for our Rock Paper Scissors game. Our game runs exclusively on Solana."]}),a.jsxs("p",{children:[a.jsx("strong",{children:"What to do:"}),' You can simply click "Not Now" on the Monad popup and continue using our game normally on Solana.']})]})]})]})})]})},Ib=()=>a.jsxs("div",{className:"min-h-screen w-full flex flex-col justify-center items-center bg-gradient-to-b from-purple-900 via-indigo-900 to-blue-900 relative overflow-hidden",children:[a.jsxs("div",{className:"absolute inset-0 overflow-hidden",children:[a.jsx("div",{className:"absolute text-8xl opacity-10 text-white animate-float top-1/4 left-1/4",children:"👊"}),a.jsx("div",{className:"absolute text-8xl opacity-10 text-white animate-float-delayed top-3/4 right-1/3",children:"✋"}),a.jsx("div",{className:"absolute text-8xl opacity-10 text-white animate-float-reverse top-1/3 right-1/4",children:"✌️"}),a.jsx("div",{className:"absolute bottom-10 left-10 opacity-10",children:a.jsx("svg",{width:"100",height:"100",viewBox:"0 0 100 100",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:a.jsx("path",{d:"M99.5815 78.0568L83.5815 99.0568C83.3815 99.3235 83.1149 99.5235 82.8149 99.6568C82.5149 99.7902 82.1815 99.8568 81.8482 99.8568H1.84818C1.38151 99.8568 0.948177 99.6568 0.648177 99.2902C0.348177 98.9235 0.215011 98.4235 0.315011 97.9568C0.381677 97.6902 0.515011 97.4568 0.681677 97.2568L16.6817 76.2568C16.8817 75.9902 17.1482 75.7902 17.4482 75.6568C17.7482 75.5235 18.0817 75.4568 18.415 75.4568H98.415C98.8817 75.4568 99.315 75.6568 99.615 76.0235C99.915 76.3902 100.048 76.8902 99.9483 77.3568C99.8817 77.6235 99.7483 77.8568 99.5815 78.0568ZM83.5815 40.2568C83.3815 39.9902 83.1149 39.7902 82.8149 39.6568C82.5149 39.5235 82.1815 39.4568 81.8482 39.4568H1.84818C1.38151 39.4568 0.948177 39.6568 0.648177 40.0235C0.348177 40.3902 0.215011 40.8902 0.315011 41.3568C0.381677 41.6235 0.515011 41.8569 0.681677 42.0568L16.6817 63.0568C16.8817 63.3235 17.1482 63.5235 17.4482 63.6568C17.7482 63.7902 18.0817 63.8568 18.415 63.8568H98.415C98.8817 63.8568 99.315 63.6568 99.615 63.2902C99.915 62.9235 100.048 62.4235 99.9483 61.9568C99.8817 61.6902 99.7483 61.4568 99.5815 61.2568L83.5815 40.2568ZM1.84818 27.8568H81.8482C82.1815 27.8568 82.5149 27.7902 82.8149 27.6568C83.1149 27.5235 83.3815 27.3235 83.5815 27.0568L99.5815 6.05679C99.7483 5.85679 99.8817 5.62346 99.9483 5.35679C100.048 4.89012 99.915 4.39012 99.615 4.02346C99.315 3.65679 98.8817 3.45679 98.415 3.45679H18.415C18.0817 3.45679 17.7482 3.52346 17.4482 3.65679C17.1482 3.79012 16.8817 3.99012 16.6817 4.25679L0.681677 25.2568C0.515011 25.4568 0.381677 25.6902 0.315011 25.9568C0.215011 26.4235 0.348177 26.9235 0.648177 27.2902C0.948177 27.6568 1.38151 27.8568 1.84818 27.8568Z",fill:"white"})})})]}),a.jsxs("div",{className:"z-10 text-center p-8 max-w-4xl",children:[a.jsx("h1",{className:"text-6xl md:text-8xl font-bold mb-4 text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-600 animate-pulse",children:"Solana Rock Paper Scissors"}),a.jsxs("div",{className:"flex justify-center space-x-6 my-12",children:[a.jsx("div",{className:"text-7xl transform transition-all duration-300 hover:scale-125 hover:rotate-12",children:"👊"}),a.jsx("div",{className:"text-7xl transform transition-all duration-300 hover:scale-125 hover:rotate-12",children:"✋"}),a.jsx("div",{className:"text-7xl transform transition-all duration-300 hover:scale-125 hover:rotate-12",children:"✌️"})]}),a.jsx("p",{className:"text-xl md:text-2xl mb-10 text-gray-300",children:"Play the classic game on the Solana blockchain. Bet, win, and earn cryptocurrency!"}),a.jsxs("div",{className:"mb-12",children:[a.jsx("h2",{className:"text-2xl font-bold text-purple-300 mb-4",children:"Features"}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 text-left",children:[a.jsxs("div",{className:"bg-purple-900 bg-opacity-50 p-6 rounded-xl border border-purple-700 hover:shadow-lg hover:shadow-purple-600/30 transition-all duration-300",children:[a.jsx("div",{className:"text-3xl mb-2",children:"💰"}),a.jsx("h3",{className:"text-xl font-bold mb-2 text-purple-300",children:"Crypto Betting"}),a.jsx("p",{className:"text-gray-300",children:"Place bets using SOL or RPS tokens and win real cryptocurrency"})]}),a.jsxs("div",{className:"bg-purple-900 bg-opacity-50 p-6 rounded-xl border border-purple-700 hover:shadow-lg hover:shadow-purple-600/30 transition-all duration-300",children:[a.jsx("div",{className:"text-3xl mb-2",children:"🤖"}),a.jsx("h3",{className:"text-xl font-bold mb-2 text-purple-300",children:"Auto Play"}),a.jsx("p",{className:"text-gray-300",children:"Set your strategy and let the system play automatically for you"})]}),a.jsxs("div",{className:"bg-purple-900 bg-opacity-50 p-6 rounded-xl border border-purple-700 hover:shadow-lg hover:shadow-purple-600/30 transition-all duration-300",children:[a.jsx("div",{className:"text-3xl mb-2",children:"🔒"}),a.jsx("h3",{className:"text-xl font-bold mb-2 text-purple-300",children:"Secure & Transparent"}),a.jsx("p",{className:"text-gray-300",children:"All gameplay is secured by the Solana blockchain for fair outcomes"})]})]})]}),a.jsx("div",{className:"py-8",children:a.jsx(Cb,{})})]}),a.jsx("div",{className:"absolute bottom-4 text-gray-500 text-sm",children:"Running on Solana Devnet • © 2025 Solana RPS Game"})]}),kb=({onBack:e})=>a.jsxs("div",{className:"max-w-4xl mx-auto",children:[a.jsx("div",{className:"mb-8",children:a.jsx(my,{showMetrics:!0})}),a.jsxs("div",{className:"card mb-8",children:[a.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Security Architecture"}),a.jsxs("div",{className:"mb-6",children:[a.jsx("h3",{className:"text-xl font-semibold mb-3 text-purple-300",children:"Blockchain Security"}),a.jsx("p",{className:"text-gray-300 mb-4",children:"Our game leverages Solana's blockchain for secure and tamper-proof gameplay. All critical game actions are recorded on-chain, ensuring complete transparency and preventing cheating."}),a.jsxs("div",{className:"bg-gray-900 p-4 rounded-lg",children:[a.jsx("h4",{className:"font-semibold mb-2",children:"Key Security Features:"}),a.jsxs("ul",{className:"space-y-2",children:[a.jsxs("li",{className:"flex items-start",children:[a.jsx("span",{className:"text-green-400 mr-2",children:"✓"}),a.jsxs("div",{children:[a.jsx("span",{className:"font-medium",children:"Commit-Reveal Pattern"}),a.jsx("p",{className:"text-sm text-gray-400",children:"Players first commit to a hashed version of their choice, then reveal it later, preventing players from changing their mind after seeing others' choices."})]})]}),a.jsxs("li",{className:"flex items-start",children:[a.jsx("span",{className:"text-green-400 mr-2",children:"✓"}),a.jsxs("div",{children:[a.jsx("span",{className:"font-medium",children:"Cryptographic Verification"}),a.jsx("p",{className:"text-sm text-gray-400",children:"All game moves are cryptographically signed and verified on the blockchain, ensuring only legitimate moves are counted."})]})]}),a.jsxs("li",{className:"flex items-start",children:[a.jsx("span",{className:"text-green-400 mr-2",children:"✓"}),a.jsxs("div",{children:[a.jsx("span",{className:"font-medium",children:"HMAC-SHA512 Commitments"}),a.jsx("p",{className:"text-sm text-gray-400",children:"Commitment hashes were upgraded from SHA-256 to HMAC-SHA-512 with 32-byte salts stored separately, eliminating brute-force and timing-attack vectors."})]})]}),a.jsxs("li",{className:"flex items-start",children:[a.jsx("span",{className:"text-green-400 mr-2",children:"✓"}),a.jsxs("div",{children:[a.jsx("span",{className:"font-medium",children:"Authorised Fee Collection"}),a.jsx("p",{className:"text-sm text-gray-400",children:"Fees are locked in a PDA and can only be swept by the designated fee-collector address, preventing unauthorised withdrawals."})]})]}),a.jsxs("li",{className:"flex items-start",children:[a.jsx("span",{className:"text-green-400 mr-2",children:"✓"}),a.jsxs("div",{children:[a.jsx("span",{className:"font-medium",children:"Transaction Simulation & Rate-Limit"}),a.jsx("p",{className:"text-sm text-gray-400",children:"Every client transaction is simulated before broadcast and subject to per-wallet rate-limits to catch errors and mitigate spam."})]})]}),a.jsxs("li",{className:"flex items-start",children:[a.jsx("span",{className:"text-green-400 mr-2",children:"✓"}),a.jsxs("div",{children:[a.jsx("span",{className:"font-medium",children:"Real-time Metrics & Alerts"}),a.jsx("p",{className:"text-sm text-gray-400",children:"A Prometheus exporter tracks game health, fee flows, and abnormal patterns; alerts are pushed to Slack for rapid incident response."})]})]}),a.jsxs("li",{className:"flex items-start",children:[a.jsx("span",{className:"text-green-400 mr-2",children:"✓"}),a.jsxs("div",{children:[a.jsx("span",{className:"font-medium",children:"Time-Based Security"}),a.jsx("p",{className:"text-sm text-gray-400",children:"Game rounds have secure timeouts to prevent players from stalling indefinitely."})]})]})]})]})]}),a.jsxs("div",{className:"mb-6",children:[a.jsx("h3",{className:"text-xl font-semibold mb-3 text-purple-300",children:"Anti-Hacking Measures"}),a.jsx("p",{className:"text-gray-300 mb-4",children:"Our multi-layered security approach prevents various attack vectors and ensures fair gameplay for all players."}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[a.jsxs("div",{className:"bg-gray-900 p-4 rounded-lg",children:[a.jsxs("h4",{className:"font-semibold mb-2 flex items-center",children:[a.jsx("span",{className:"text-xl mr-2",children:"🛡️"}),a.jsx("span",{children:"DDoS Protection"})]}),a.jsx("p",{className:"text-sm text-gray-400",children:"Multiple layers of traffic filtering and rate limiting prevent distributed denial of service attacks, keeping games running smoothly even under attack."})]}),a.jsxs("div",{className:"bg-gray-900 p-4 rounded-lg",children:[a.jsxs("h4",{className:"font-semibold mb-2 flex items-center",children:[a.jsx("span",{className:"text-xl mr-2",children:"🤖"}),a.jsx("span",{children:"Bot Detection"})]}),a.jsx("p",{className:"text-sm text-gray-400",children:"Advanced behavior analysis algorithms detect and prevent automated gameplay, ensuring a fair environment for human players."})]}),a.jsxs("div",{className:"bg-gray-900 p-4 rounded-lg",children:[a.jsxs("h4",{className:"font-semibold mb-2 flex items-center",children:[a.jsx("span",{className:"text-xl mr-2",children:"🔐"}),a.jsx("span",{children:"Secure Transactions"})]}),a.jsx("p",{className:"text-sm text-gray-400",children:"All financial transactions are secured by Solana's cryptographic protocols, ensuring your funds are always safe."})]}),a.jsxs("div",{className:"bg-gray-900 p-4 rounded-lg",children:[a.jsxs("h4",{className:"font-semibold mb-2 flex items-center",children:[a.jsx("span",{className:"text-xl mr-2",children:"🕵️"}),a.jsx("span",{children:"Fraud Prevention"})]}),a.jsx("p",{className:"text-sm text-gray-400",children:"Real-time monitoring systems detect suspicious activity and prevent fraudulent gameplay before it affects other players."})]})]})]})]}),a.jsxs("div",{className:"card mb-8",children:[a.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Scaling Infrastructure"}),a.jsxs("div",{className:"mb-6",children:[a.jsx("p",{className:"text-gray-300 mb-4",children:"Our infrastructure is designed to handle thousands of concurrent players with low latency and high availability."}),a.jsxs("div",{className:"bg-gradient-to-r from-purple-900 to-indigo-900 p-4 rounded-lg mb-6",children:[a.jsx("div",{className:"text-center font-bold text-2xl mb-2",children:"50,000+"}),a.jsx("div",{className:"text-center text-purple-200",children:"Concurrent Players Supported"})]}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[a.jsxs("div",{className:"bg-gray-900 p-4 rounded-lg",children:[a.jsx("h4",{className:"font-semibold mb-2",children:"Horizontal Scaling"}),a.jsx("p",{className:"text-sm text-gray-400",children:"Our infrastructure automatically scales out horizontally to handle traffic spikes and increased player load."})]}),a.jsxs("div",{className:"bg-gray-900 p-4 rounded-lg",children:[a.jsx("h4",{className:"font-semibold mb-2",children:"Global Distribution"}),a.jsx("p",{className:"text-sm text-gray-400",children:"Game servers are distributed across 12 global regions to provide low-latency gameplay worldwide."})]}),a.jsxs("div",{className:"bg-gray-900 p-4 rounded-lg",children:[a.jsx("h4",{className:"font-semibold mb-2",children:"Database Sharding"}),a.jsx("p",{className:"text-sm text-gray-400",children:"Game data is automatically sharded across multiple database instances for high throughput and performance."})]})]})]}),a.jsxs("div",{className:"mb-6",children:[a.jsx("h3",{className:"text-xl font-semibold mb-3 text-purple-300",children:"Performance Metrics"}),a.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[a.jsxs("div",{className:"bg-gray-900 p-4 rounded-lg text-center",children:[a.jsx("div",{className:"text-2xl font-bold text-green-400",children:"99.9%"}),a.jsx("div",{className:"text-sm text-gray-400",children:"Uptime"})]}),a.jsxs("div",{className:"bg-gray-900 p-4 rounded-lg text-center",children:[a.jsx("div",{className:"text-2xl font-bold text-green-400",children:"< 200ms"}),a.jsx("div",{className:"text-sm text-gray-400",children:"Global Latency"})]}),a.jsxs("div",{className:"bg-gray-900 p-4 rounded-lg text-center",children:[a.jsx("div",{className:"text-2xl font-bold text-green-400",children:"200+"}),a.jsx("div",{className:"text-sm text-gray-400",children:"TPS"})]}),a.jsxs("div",{className:"bg-gray-900 p-4 rounded-lg text-center",children:[a.jsx("div",{className:"text-2xl font-bold text-green-400",children:"12"}),a.jsx("div",{className:"text-sm text-gray-400",children:"Global Regions"})]})]})]})]}),a.jsx("div",{className:"flex justify-center mb-8",children:a.jsx("button",{onClick:e,className:"px-6 py-3 bg-gradient-to-r from-purple-600 to-indigo-600 rounded-lg hover:from-purple-700 hover:to-indigo-700 font-medium",children:"Back to Home"})})]}),Lb=({gameId:e,endpoint:t,programId:n})=>{const[r,s]=E.useState([]),[i,o]=E.useState(null),[l,c]=E.useState(""),[u,d]=E.useState(null),[h,y]=E.useState(null);return E.useEffect(()=>{if(e)try{const g=new oo(t,"confirmed"),S=new se(e),N=new se(n);(async()=>{try{const f=await g.getAccountInfo(S);if(!f){d("Game account not found");return}o(f.data),c(new Date().toLocaleTimeString()),d(null)}catch(f){console.error("Error fetching game state:",f),d(`Error fetching game state: ${f.message}`)}})();const p=g.onAccountChange(S,f=>{o(f.data),c(new Date().toLocaleTimeString())},"confirmed");return y(p),(()=>{var x;const f=[];(x=window.phantom)!=null&&x.solana&&f.push("Phantom"),window.solflare&&f.push("Solflare"),window.solana&&!f.includes("Phantom")&&!f.includes("Solflare")&&f.push("Other Solana wallet"),s(f)})(),()=>{h!==null&&g.removeAccountChangeListener(h)}}catch(g){console.error("Error initializing game monitor:",g),d(`Error initializing game monitor: ${g.message}`)}},[e,t,n]),u?a.jsxs("div",{className:"bg-red-900 bg-opacity-50 p-4 rounded-lg my-4",children:[a.jsx("h3",{className:"text-lg font-semibold text-white mb-2",children:"Game Monitor Error"}),a.jsx("p",{className:"text-red-200",children:u})]}):a.jsxs("div",{className:"bg-gray-800 bg-opacity-50 p-4 rounded-lg my-4",children:[a.jsx("h3",{className:"text-lg font-semibold text-white mb-2",children:"Game Monitor"}),a.jsxs("div",{className:"mb-3",children:[a.jsxs("p",{className:"text-sm text-gray-300",children:[a.jsx("span",{className:"font-semibold",children:"Game ID:"})," ",e]}),a.jsxs("p",{className:"text-sm text-gray-300",children:[a.jsx("span",{className:"font-semibold",children:"Last Updated:"})," ",l||"Never"]})]}),a.jsxs("div",{className:"mb-3",children:[a.jsx("h4",{className:"text-md font-semibold text-purple-300 mb-1",children:"Connected Wallets"}),r.length>0?a.jsx("ul",{className:"text-sm text-gray-300",children:r.map((g,S)=>a.jsxs("li",{className:"flex items-center",children:[a.jsx("span",{className:"text-green-400 mr-2",children:"●"})," ",g]},S))}):a.jsx("p",{className:"text-sm text-yellow-400",children:"No wallets detected on this device"})]}),a.jsxs("div",{children:[a.jsx("h4",{className:"text-md font-semibold text-purple-300 mb-1",children:"Raw Game Data"}),i?a.jsx("div",{className:"text-xs text-gray-400 overflow-auto max-h-32 p-2 bg-gray-900 bg-opacity-50 rounded",children:a.jsx("pre",{children:JSON.stringify(i,null,2)})}):a.jsx("p",{className:"text-sm text-gray-400 italic",children:"Loading game data..."})]}),a.jsx("div",{className:"mt-3 text-right",children:a.jsx("button",{onClick:()=>window.location.reload(),className:"text-xs bg-purple-700 hover:bg-purple-600 text-white px-2 py-1 rounded",children:"Refresh"})})]})},Ob=()=>{const{connected:e,publicKey:t}=ul(),[n,r]=E.useState(""),[s,i]=E.useState(""),[o,l]=E.useState(!1),[c,u]=E.useState(""),[d,h]=E.useState(""),[y,g]=E.useState("7Y9dRMY6V9cmVkXNFrHeUZmYf2tAV5wSVFcYyD5bLQpZ"),[S,N]=E.useState(rm("devnet"));E.useEffect(()=>{const x=(()=>{const b=navigator,T=window.screen,C=b.userAgent+T.width+T.height+T.colorDepth+b.language+new Date().getTimezoneOffset();return Array.from(new Uint8Array(new TextEncoder().encode(C))).map(O=>O.toString(16).padStart(2,"0")).join("").substring(0,16)})();i(x);const M=navigator.userAgent;let w="Unknown",v="Unknown";M.indexOf("Firefox")>-1?w="Firefox":M.indexOf("Opera")>-1||M.indexOf("OPR")>-1?w="Opera":M.indexOf("Edge")>-1?w="Edge":M.indexOf("Chrome")>-1?w="Chrome":M.indexOf("Safari")>-1&&(w="Safari"),M.indexOf("Windows")>-1?v="Windows":M.indexOf("Mac")>-1?v="MacOS":M.indexOf("Linux")>-1?v="Linux":M.indexOf("Android")>-1?v="Android":(M.indexOf("iPhone")>-1||M.indexOf("iPad")>-1)&&(v="iOS"),h(`${w} on ${v}`),u(`${v}-${w}-${x.substring(0,4)}`)},[]);const A=f=>{r(f.target.value)},p=f=>{u(f.target.value)},m=()=>{l(!o)};return a.jsx("div",{className:"max-w-3xl mx-auto",children:a.jsxs("div",{className:"card",children:[a.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Multi-Wallet Testing"}),a.jsx("div",{className:"mb-6 p-4 bg-purple-900 bg-opacity-20 rounded-lg border border-purple-700",children:a.jsxs("div",{className:"flex items-start",children:[a.jsx("div",{className:"text-purple-400 text-xl mr-3",children:"🧪"}),a.jsxs("div",{children:[a.jsx("h3",{className:"text-lg font-semibold text-purple-300 mb-1",children:"Testing Mode"}),a.jsx("p",{className:"text-gray-300 mb-2",children:"This is a special view for testing the game with multiple wallets across different devices."}),a.jsxs("p",{className:"text-sm text-gray-400 mb-1",children:[a.jsx("span",{className:"font-semibold",children:"Device ID:"})," ",s]}),a.jsxs("p",{className:"text-sm text-gray-400",children:[a.jsx("span",{className:"font-semibold",children:"Browser:"})," ",d]})]})]})}),a.jsxs("div",{className:"mb-6",children:[a.jsxs("div",{className:"form-group",children:[a.jsx("label",{className:"form-label",children:"Device Name (for testing)"}),a.jsx("input",{type:"text",className:"form-control",value:c,onChange:p,placeholder:"Give this device a name for testing"})]}),a.jsxs("div",{className:"form-group",children:[a.jsx("label",{className:"form-label",children:"Game ID to Monitor"}),a.jsx("input",{type:"text",className:"form-control",value:n,onChange:A,placeholder:"Enter a game ID to monitor"})]}),a.jsx("div",{className:"mt-3",children:a.jsx("button",{className:`w-full py-3 rounded-lg ${n?"bg-purple-600 hover:bg-purple-700":"bg-gray-600 cursor-not-allowed opacity-60"}`,onClick:m,disabled:!n,children:o?"Hide Game Monitor":"Show Game Monitor"})})]}),o&&n&&a.jsx(Lb,{gameId:n,endpoint:S,programId:y}),a.jsxs("div",{className:"bg-gray-800 p-4 rounded-lg mb-4",children:[a.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Wallet Status"}),a.jsxs("div",{className:"flex items-center mb-2",children:[a.jsx("div",{className:`w-3 h-3 rounded-full mr-2 ${e?"bg-green-500":"bg-red-500"}`}),a.jsx("span",{children:e?"Connected":"Not Connected"})]}),e&&t&&a.jsx("div",{className:"mt-2 p-2 bg-gray-700 rounded overflow-x-auto",children:a.jsx("code",{className:"text-xs text-gray-300",children:t.toString()})}),a.jsx("div",{className:"mt-4",children:a.jsx(uf,{className:"!bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"})})]}),a.jsxs("div",{className:"mt-6 border-t border-gray-700 pt-4",children:[a.jsx("h3",{className:"text-lg font-semibold mb-3",children:"Testing Instructions"}),a.jsxs("ol",{className:"text-gray-300 list-decimal list-inside space-y-2",children:[a.jsx("li",{children:"Connect your wallet on this device"}),a.jsx("li",{children:"Create a game or get a Game ID from another test device"}),a.jsx("li",{children:'Enter the Game ID above and click "Show Game Monitor"'}),a.jsx("li",{children:"You can observe the game state as it changes"}),a.jsx("li",{children:"Use multiple browsers/devices for comprehensive testing"})]}),a.jsx("div",{className:"mt-4 text-sm text-gray-400",children:a.jsx("p",{children:"For detailed testing instructions, see the MULTI_WALLET_TESTING.md file in the project repository."})})]})]})})},Pb=({walletAddress:e,isExpanded:t=!1})=>{const[n,r]=E.useState(null),[s,i]=E.useState(!1),[o,l]=E.useState(""),[c,u]=E.useState(!0),[d,h]=E.useState(t);E.useEffect(()=>{(async()=>{if(e){u(!0);try{const N=await vs.getUserProfile(e.toString());r(N),l((N==null?void 0:N.displayName)||"")}catch(N){console.error("Error loading profile:",N)}finally{u(!1)}}else r(null),u(!1)})()},[e]);const y=async()=>{if(n&&e)try{const S=await vs.updateDisplayName(e.toString(),o);r(S),i(!1)}catch(S){console.error("Error updating display name:",S)}},g=S=>`${S.slice(0,4)}...${S.slice(-4)}`;return e?c?a.jsxs("div",{className:"bg-gray-800 rounded-lg p-4 shadow-md border border-gray-700 animate-pulse",children:[a.jsx("div",{className:"h-6 bg-gray-700 rounded w-3/4 mb-4"}),a.jsx("div",{className:"h-4 bg-gray-700 rounded w-1/2 mb-2"}),a.jsx("div",{className:"h-4 bg-gray-700 rounded w-2/3 mb-2"}),a.jsx("div",{className:"h-4 bg-gray-700 rounded w-1/3"})]}):a.jsxs("div",{className:"bg-gray-800 rounded-lg p-4 shadow-md border border-gray-700",children:[a.jsxs("div",{className:"flex justify-between items-center mb-4",children:[a.jsx("h3",{className:"text-lg font-semibold text-white",children:"Player Profile"}),a.jsx("button",{onClick:()=>h(!d),className:"text-gray-400 hover:text-white",children:d?"▲":"▼"})]}),a.jsxs("div",{className:"flex items-center mb-4",children:[a.jsx("div",{className:"w-12 h-12 rounded-full bg-purple-700 flex items-center justify-center text-white font-bold mr-3",children:n!=null&&n.displayName?n.displayName[0].toUpperCase():"P"}),a.jsxs("div",{children:[s?a.jsxs("div",{className:"flex items-center",children:[a.jsx("input",{type:"text",value:o,onChange:S=>l(S.target.value),placeholder:"Enter display name",className:"bg-gray-700 text-white p-1 rounded mr-2",maxLength:20}),a.jsx("button",{onClick:y,className:"bg-purple-600 text-white px-2 py-1 rounded text-sm",children:"Save"}),a.jsx("button",{onClick:()=>i(!1),className:"ml-1 text-gray-400 hover:text-white",children:"✕"})]}):a.jsxs("div",{className:"flex items-center",children:[a.jsx("h4",{className:"font-semibold text-white",children:(n==null?void 0:n.displayName)||g(e.toString())}),a.jsx("button",{onClick:()=>i(!0),className:"ml-2 text-gray-400 hover:text-white text-sm",title:"Edit display name",children:"✎"})]}),a.jsxs("p",{className:"text-gray-400 text-sm",children:["Rank #",(n==null?void 0:n.rank)||"—"]})]})]}),d&&n&&a.jsxs("div",{className:"border-t border-gray-700 pt-3",children:[a.jsxs("div",{className:"grid grid-cols-2 gap-2 mb-4",children:[a.jsxs("div",{className:"bg-gray-700 rounded p-2",children:[a.jsx("p",{className:"text-gray-400 text-xs",children:"Win Rate"}),a.jsxs("p",{className:"text-white font-semibold",children:[n.winRate,"%"]})]}),a.jsxs("div",{className:"bg-gray-700 rounded p-2",children:[a.jsx("p",{className:"text-gray-400 text-xs",children:"Total Games"}),a.jsx("p",{className:"text-white font-semibold",children:n.totalGames})]})]}),a.jsxs("div",{className:"grid grid-cols-3 gap-2 mb-4",children:[a.jsxs("div",{className:"bg-green-900 bg-opacity-40 rounded p-2",children:[a.jsx("p",{className:"text-green-400 text-xs",children:"Wins"}),a.jsx("p",{className:"text-white font-semibold",children:n.wins})]}),a.jsxs("div",{className:"bg-red-900 bg-opacity-40 rounded p-2",children:[a.jsx("p",{className:"text-red-400 text-xs",children:"Losses"}),a.jsx("p",{className:"text-white font-semibold",children:n.losses})]}),a.jsxs("div",{className:"bg-gray-700 rounded p-2",children:[a.jsx("p",{className:"text-gray-400 text-xs",children:"Ties"}),a.jsx("p",{className:"text-white font-semibold",children:n.ties})]})]}),a.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[a.jsxs("div",{className:"bg-gray-700 rounded p-2",children:[a.jsx("p",{className:"text-gray-400 text-xs",children:"Total Wagered"}),a.jsx("p",{className:"text-white font-semibold",children:n.totalWagered.toFixed(2)})]}),a.jsxs("div",{className:`${n.netProfit>=0?"bg-green-900 bg-opacity-40":"bg-red-900 bg-opacity-40"} rounded p-2`,children:[a.jsx("p",{className:`${n.netProfit>=0?"text-green-400":"text-red-400"} text-xs`,children:"Net Profit"}),a.jsx("p",{className:"text-white font-semibold",children:n.netProfit.toFixed(2)})]})]})]})]}):a.jsx("div",{className:"bg-gray-800 rounded-lg p-4 shadow-md border border-gray-700",children:a.jsx("p",{className:"text-gray-400 text-center",children:"Please connect your wallet to view your profile"})})},Rb=({currentUserWallet:e,limit:t=10})=>{const[n,r]=E.useState([]),[s,i]=E.useState(!0),[o,l]=E.useState(null);E.useEffect(()=>{(async()=>{i(!0);try{const d=await vs.getLeaderboard(t);if(r(d),e){const h=await vs.getUserRank(e.toString());l(h)}}catch(d){console.error("Error loading leaderboard:",d)}finally{i(!1)}})()},[e,t]);const c=u=>`${u.slice(0,4)}...${u.slice(-4)}`;return s?a.jsxs("div",{className:"bg-gray-800 rounded-lg p-4 shadow-md border border-gray-700 animate-pulse",children:[a.jsx("div",{className:"h-6 bg-gray-700 rounded w-1/2 mb-4"}),[...Array(5)].map((u,d)=>a.jsx("div",{className:"h-12 bg-gray-700 rounded mb-2"},d))]}):a.jsxs("div",{className:"bg-gray-800 rounded-lg p-4 shadow-md border border-gray-700",children:[a.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Leaderboard"}),n.length===0?a.jsx("p",{className:"text-gray-400 text-center py-6",children:"No players have joined the leaderboard yet."}):a.jsxs(a.Fragment,{children:[a.jsxs("div",{className:"grid grid-cols-10 gap-2 text-xs text-gray-400 border-b border-gray-700 pb-2 mb-2",children:[a.jsx("div",{className:"col-span-1",children:"Rank"}),a.jsx("div",{className:"col-span-4",children:"Player"}),a.jsx("div",{className:"col-span-2",children:"Win Rate"}),a.jsx("div",{className:"col-span-1",children:"W"}),a.jsx("div",{className:"col-span-1",children:"L"}),a.jsx("div",{className:"col-span-1",children:"T"})]}),a.jsx("div",{className:"max-h-80 overflow-y-auto",children:n.map((u,d)=>{const h=e&&u.walletAddress===e.toString();return a.jsxs("div",{className:`grid grid-cols-10 gap-2 py-2 text-sm ${d%2===0?"bg-gray-800":"bg-gray-900"} ${h?"bg-purple-900 bg-opacity-20 border-l-2 border-purple-500":""}`,children:[a.jsx("div",{className:"col-span-1 font-semibold text-center",children:u.rank||d+1}),a.jsxs("div",{className:"col-span-4 truncate",children:[u.displayName||c(u.walletAddress),h&&a.jsx("span",{className:"ml-1 text-purple-400",children:"(You)"})]}),a.jsx("div",{className:"col-span-2",children:a.jsxs("span",{className:u.winRate>60?"text-green-400":u.winRate<40?"text-red-400":"text-yellow-400",children:[u.winRate,"%"]})}),a.jsx("div",{className:"col-span-1 text-green-400",children:u.wins}),a.jsx("div",{className:"col-span-1 text-red-400",children:u.losses}),a.jsx("div",{className:"col-span-1 text-gray-400",children:u.ties})]},u.walletAddress)})}),e&&o&&o>t&&a.jsxs("div",{className:"mt-4 pt-2 border-t border-gray-700",children:[a.jsx("p",{className:"text-sm text-gray-400 mb-2",children:"Your ranking:"}),a.jsxs("div",{className:"grid grid-cols-10 gap-2 py-2 text-sm bg-purple-900 bg-opacity-20 border-l-2 border-purple-500",children:[a.jsx("div",{className:"col-span-1 font-semibold text-center",children:o}),a.jsxs("div",{className:"col-span-4 truncate",children:[e&&c(e.toString()),a.jsx("span",{className:"ml-1 text-purple-400",children:"(You)"})]}),a.jsx("div",{className:"col-span-2",children:a.jsx("span",{className:"text-yellow-400",children:"--"})}),a.jsx("div",{className:"col-span-1 text-green-400",children:"--"}),a.jsx("div",{className:"col-span-1 text-red-400",children:"--"}),a.jsx("div",{className:"col-span-1 text-gray-400",children:"--"})]})]})]})]})};function Db(e=10){const t=["CryptoWarrior","SolanaShark","RPSMaster","BlockchainBoss","TokenTiger","Web3Wizard","DeFiDragon","NFTNinja","MetaverseMage","CoinCollector","ChainChampion","DecentralDuke"],n=["🦸‍♂️","🥷","🤖","👑","🦅","🐅","🦄","⚡","🔥","💎","🚀","🌟"];return Array.from({length:e},(r,s)=>{const i=Math.floor(Math.random()*500)+10,o=Math.floor(Math.random()*i),l=i-o,c=o/i;return{id:`player_${s+1}`,name:t[s%t.length],gamesPlayed:i,wins:o,losses:l,winRate:c,totalEarnings:Math.floor(Math.random()*1e3)/100,avatar:n[s%n.length]}})}const zb=({publicKey:e,connected:t,onBackToHome:n})=>{const[r,s]=E.useState("profile");return E.useEffect(()=>{(async()=>{try{(await vs.getLeaderboard()).length===0&&Db(10)}catch(o){console.error("Error initializing profiles:",o)}})()},[]),a.jsxs("div",{className:"container mx-auto px-4 py-8 max-w-4xl",children:[a.jsxs("div",{className:"flex justify-between items-center mb-6",children:[a.jsx("h2",{className:"text-2xl font-bold text-white",children:"Player Profiles"}),a.jsx("button",{onClick:n,className:"px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded text-white",children:"Back to Home"})]}),a.jsxs("div",{className:"flex border-b border-gray-700 mb-6",children:[a.jsx("button",{className:`px-4 py-2 ${r==="profile"?"text-purple-400 border-b-2 border-purple-400":"text-gray-400 hover:text-white"}`,onClick:()=>s("profile"),children:"My Profile"}),a.jsx("button",{className:`px-4 py-2 ${r==="leaderboard"?"text-purple-400 border-b-2 border-purple-400":"text-gray-400 hover:text-white"}`,onClick:()=>s("leaderboard"),children:"Leaderboard"})]}),a.jsx("div",{className:"space-y-6",children:r==="profile"?a.jsxs(a.Fragment,{children:[a.jsx(Pb,{walletAddress:e,isExpanded:!0}),a.jsxs("div",{className:"bg-gray-800 rounded-lg p-4 shadow-md border border-gray-700",children:[a.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Recent Games"}),a.jsx("p",{className:"text-gray-400 text-center py-6",children:"Your recent game history will appear here as you play."})]})]}):a.jsx(Rb,{currentUserWallet:e})}),!1]})},Wb={play:e=>console.log(`Playing sound: ${e}`)},Bb=({gameClient:e,currentUserPublicKey:t,onTournamentEnd:n,navigateToGameView:r})=>{var I,F;const s=Wb,[i,o]=E.useState(null),[l,c]=E.useState(!1),[u,d]=E.useState(null),[h,y]=E.useState(null),[g,S]=E.useState(!1),[N,A]=E.useState({name:"My Awesome RPS Tournament",maxPlayers:8,entryFee:.1,currency:q.SOL}),p=t==null?void 0:t.toBase58(),m=E.useCallback(_=>{if(_.length===0)return[];const R=_.length;if(R<2)return d("Number of players must be at least 2 for a tournament."),[];let z=[..._],L=Math.pow(2,Math.ceil(Math.log2(R)));if(R!==L&&R>1){const $=L-R;for(let Y=0;Y<$;Y++)z.push({id:`bye-${Y}`,name:"BYE",isCurrentUser:!1})}z.sort(()=>Math.random()-.5);const P=[];let U=0;for(;z.length>1||z.length===1&&P.length===0&&R===1;){if(z.length===1&&R===1){P.push({roundNumber:U+1,matches:[{id:`r${U}m0`,roundIndex:U,matchIndex:0,player1:z[0],player2:null,winner:z[0],status:"Completed"}]});break}if(z.length<2)break;const $=[],Y=[];for(let ae=0;ae<z.length;ae+=2){const Q=z[ae],Re=ae+1<z.length?z[ae+1]:null,at=ae/2,Mr=`r${U}m${at}`;let xl="Scheduled",js=null;(Q==null?void 0:Q.name)==="BYE"?(js=Re,xl="Bye"):(Re==null?void 0:Re.name)==="BYE"&&(js=Q,xl="Bye"),js?Y.push(js):Y.push(null),$.push({id:Mr,roundIndex:U,matchIndex:at,player1:Q,player2:Re,winner:js,status:xl})}if(P.push({roundNumber:U+1,matches:$}),z=Y.filter(ae=>ae!==null),z.length===0&&Y.some(ae=>ae===null)&&P[P.length-1].matches.some(ae=>ae.status==="Scheduled")||z.length===1&&P[P.length-1].matches.length>0,U++,U>Math.log2(L)+2){zt.error("Too many rounds generated, breaking.");break}}return P},[d]),f=E.useCallback(async()=>{if(!e||!p){d("Wallet not connected or game client not initialized.");return}c(!0),d(null),S(!1);try{const _={id:p,name:`Player ${p.substring(0,6)}`,isCurrentUser:!0},R={id:`tourney_${Date.now()}`,name:N.name,status:"Registering",players:[_],brackets:[],currentRound:0,prizePool:0,entryFee:N.entryFee,currency:N.currency,winner:null,maxPlayers:N.maxPlayers,host:_};o(R),s.play("tournament-created"),zt.success(`Tournament "${R.name}" created locally.`)}catch(_){d(`Failed to create tournament: ${_.message}`),zt.error("Tournament creation error",_)}finally{c(!1)}},[e,p,N,s]),x=E.useCallback(async()=>{if(!i||!p||i.status!=="Registering"){d("Cannot join tournament now or not logged in.");return}if(i.players.find(_=>_.id===p)){d("You have already joined this tournament.");return}if(i.players.length>=i.maxPlayers){d("Tournament is full.");return}c(!0);try{const _={id:p,name:`Player ${p.substring(0,6)}`,isCurrentUser:!0};o(R=>R?{...R,players:[...R.players,_],prizePool:R.prizePool+R.entryFee}:null),s.play("player-join-tournament"),zt.info(`Player ${p} joined tournament ${i.id}`)}catch(_){d(`Failed to join tournament: ${_.message}`)}finally{c(!1)}},[i,p,s,e]),M=E.useCallback(()=>{if(!i||i.status!=="Registering")return;if(i.players.length<2){d("Need at least 2 players to start the tournament.");return}c(!0);const _=m(i.players);_.length>0?(o(R=>R?{...R,brackets:_,status:"InProgress",currentRound:1}:null),s.play("tournament-start"),zt.info(`Tournament ${i.id} started with ${i.players.length} players.`)):d("Failed to generate brackets. Ensure player count is valid."),c(!1)},[i,m,s]),w=E.useCallback(async _=>{if(!i||!e||!p||_.status!=="Scheduled")return;if(!_.player1||!_.player2){d("Match players not properly set.");return}if(_.player1.name==="BYE"||_.player2.name==="BYE"){zt.warn("Attempted to play a BYE match.");return}const R=_.player1.id===p,z=_.player2.id===p;if(!R&&!z){d("You are not a participant in this match.");return}if(y(_.id),c(!0),s.play("match-start"),r){zt.info(`Navigating to game view for match ${_.id} between ${_.player1.name} and ${_.player2.name}`),d("Navigation to individual game view is not fully implemented in this component. Simulating match..."),setTimeout(()=>v(_.id),2e3);return}zt.info(`Simulating match ${_.id} between ${_.player1.name} and ${_.player2.name}`),setTimeout(()=>v(_.id),3e3)},[i,e,p,s,r]),v=_=>{o(R=>{if(!R)return null;let z=null,L=0,P=0;const U=R.brackets.map($=>({...$,matches:$.matches.map(Y=>Y.id===_?!Y.player1||!Y.player2?Y:(Math.random()>.5?(z=Y.player1,L=1):(z=Y.player2,P=1),s.play((z==null?void 0:z.id)===p?"match-win":"match-lose"),{...Y,status:"Completed",winner:z,score:{player1:L,player2:P}}):Y)}));return b({...R,brackets:U},_)}),y(null),c(!1)},b=(_,R)=>{let z={..._};const L=z.brackets.findIndex($=>$.matches.some(Y=>Y.id===R));if(L===-1)return z;const P=z.brackets[L];if(P.matches.every($=>$.status==="Completed"||$.status==="Bye"))if(L===z.brackets.length-1){const $=P.matches[0];$&&$.winner&&(z.winner=$.winner,z.status="Finished",s.play("tournament-win"),zt.success(`Tournament ${z.id} finished! Winner: ${$.winner.name}`),n&&n($.winner,z.prizePool))}else{const $=L+1;if(z.brackets[$]){const Y=P.matches.map(Q=>Q.winner).filter(Q=>Q!==null);let ae=0;z.brackets[$].matches=z.brackets[$].matches.map(Q=>((Q.status==="Pending"||Q.status==="Scheduled")&&(!Q.player1&&ae<Y.length&&(Q.player1=Y[ae++]),!Q.player2&&ae<Y.length&&(Q.player2=Y[ae++]),Q.player1&&Q.player2?Q.status="Scheduled":(Q.player1&&!Q.player2&&Q.player1.name!=="BYE"||!Q.player1&&Q.player2&&Q.player2.name!=="BYE")&&(Q.winner=Q.player1||Q.player2,Q.status="Bye")),Q)),z.currentRound=$+1,zt.info(`Advanced to round ${z.currentRound}`)}}return z},T=(_,R,z)=>{var P,U,$,Y,ae,Q,Re,at;const L=_.status==="Scheduled"&&(((P=_.player1)==null?void 0:P.id)===p||((U=_.player2)==null?void 0:U.id)===p)&&(($=_.player1)==null?void 0:$.name)!=="BYE"&&((Y=_.player2)==null?void 0:Y.name)!=="BYE";return a.jsxs("div",{className:`bracket-match-node ${_.status.toLowerCase()}`,children:[a.jsxs("div",{className:"bracket-lines",children:[R&&a.jsx("div",{className:"line-top"}),a.jsx("div",{className:"line-middle"}),z&&a.jsx("div",{className:"line-bottom"})]}),a.jsxs("div",{className:"bracket-match-box",children:[a.jsxs("div",{className:`player-slot ${_.winner===_.player1?"winner":""} ${(ae=_.player1)!=null&&ae.isCurrentUser?"current-user":""}`,children:[_.player1?_.player1.name:"TBD",_.score&&a.jsx("span",{className:"player-score",children:_.score.player1})]}),a.jsx("div",{className:"vs-divider",children:"vs"}),a.jsxs("div",{className:`player-slot ${_.winner===_.player2?"winner":""} ${(Q=_.player2)!=null&&Q.isCurrentUser?"current-user":""}`,children:[_.player2?_.player2.name:"TBD",_.score&&a.jsx("span",{className:"player-score",children:_.score.player2})]}),_.status==="Scheduled"&&(((Re=_.player1)==null?void 0:Re.name)==="BYE"||((at=_.player2)==null?void 0:at.name)==="BYE")&&a.jsx("div",{className:"match-status-text",children:"BYE"}),_.status==="Scheduled"&&_.player1&&_.player2&&_.player1.name!=="BYE"&&_.player2.name!=="BYE"&&(L?a.jsx("button",{onClick:()=>w(_),disabled:l||h===_.id,className:"play-match-button",children:h===_.id?"Playing...":"Play Match"}):a.jsx("span",{className:"match-status-text",children:"Waiting"})),_.status==="InProgress"&&a.jsx("div",{className:"match-status-text",children:"In Progress..."}),_.status==="Completed"&&_.winner&&a.jsxs("div",{className:"match-status-text winner-text",children:["Winner: ",_.winner.name]})]})]},_.id)},C=()=>!i||!i.brackets||i.brackets.length===0?a.jsx("p",{children:"No bracket generated yet. Register players and start the tournament."}):a.jsx("div",{className:"tournament-brackets-container",children:i.brackets.map((_,R)=>a.jsxs("div",{className:"bracket-round",children:[a.jsxs("h3",{className:"round-title",children:["Round ",_.roundNumber]}),a.jsx("div",{className:"round-matches",children:_.matches.map((z,L)=>{var $;const P=L%2===0,U=R<i.brackets.length-1;return a.jsxs("div",{className:"match-wrapper",children:[T(z,U&&P,U&&!P),U&&(($=i.brackets[R+1])==null?void 0:$.matches[Math.floor(L/2)])&&a.jsx("div",{className:"connector-line-horizontal"})]},z.id)})})]},`round-${R}`))});if(l&&!i)return a.jsx("div",{className:"loading-indicator tournament-loading",children:"Loading Tournament..."});if(!i)return a.jsxs("div",{className:"game-tournament no-tournament",children:[a.jsx("h2",{children:"No Active Tournament"}),a.jsx("button",{onClick:()=>S(!0),className:"create-tournament-button primary",children:"Create New Tournament"}),g&&a.jsx("div",{className:"modal-overlay",children:a.jsxs("div",{className:"modal-content create-tournament-modal",children:[a.jsx("h3",{children:"Create Tournament"}),a.jsxs("div",{className:"form-group",children:[a.jsx("label",{htmlFor:"tournamentName",children:"Tournament Name:"}),a.jsx("input",{type:"text",id:"tournamentName",value:N.name,onChange:_=>A(R=>({...R,name:_.target.value}))})]}),a.jsxs("div",{className:"form-group",children:[a.jsx("label",{htmlFor:"maxPlayers",children:"Max Players (power of 2, e.g., 4, 8, 16):"}),a.jsxs("select",{id:"maxPlayers",value:N.maxPlayers,onChange:_=>A(R=>({...R,maxPlayers:parseInt(_.target.value)})),children:[a.jsx("option",{value:"2",children:"2"}),a.jsx("option",{value:"4",children:"4"}),a.jsx("option",{value:"8",children:"8"}),a.jsx("option",{value:"16",children:"16"})]})]}),a.jsxs("div",{className:"form-group",children:[a.jsx("label",{htmlFor:"entryFee",children:"Entry Fee:"}),a.jsx("input",{type:"number",id:"entryFee",value:N.entryFee,min:"0",step:"0.01",onChange:_=>A(R=>({...R,entryFee:parseFloat(_.target.value)}))})]}),a.jsxs("div",{className:"form-group",children:[a.jsx("label",{htmlFor:"currency",children:"Currency:"}),a.jsxs("select",{id:"currency",value:N.currency,onChange:_=>A(R=>({...R,currency:_.target.value})),children:[a.jsx("option",{value:q.SOL,children:"SOL"}),a.jsx("option",{value:q.RPSTOKEN,children:"RPS Token"})]})]}),a.jsxs("div",{className:"modal-actions",children:[a.jsx("button",{onClick:f,className:"primary",disabled:l,children:"Create"}),a.jsx("button",{onClick:()=>S(!1),className:"secondary",disabled:l,children:"Cancel"})]})]})})]});const O=i.status==="Registering"&&!i.players.find(_=>_.id===p)&&i.players.length<i.maxPlayers,D=i.status==="Registering"&&((I=i.host)==null?void 0:I.id)===p&&i.players.length>=2;return a.jsxs("div",{className:"game-tournament",children:[a.jsxs("header",{className:"tournament-header",children:[a.jsx("h2",{children:i.name}),a.jsxs("div",{className:"tournament-info",children:[a.jsxs("span",{children:["Status: ",a.jsx("strong",{className:`status-${i.status.toLowerCase()}`,children:i.status})]}),a.jsxs("span",{children:["Players: ",i.players.length," / ",i.maxPlayers]}),a.jsxs("span",{children:["Prize Pool: ",i.prizePool.toFixed(2)," ",i.currency]}),a.jsxs("span",{children:["Host: ",(F=i.host)==null?void 0:F.name]})]}),u&&a.jsx("p",{className:"error-message tournament-error",children:u})]}),i.status==="Registering"&&a.jsxs("div",{className:"tournament-actions registration-actions",children:[O&&a.jsxs("button",{onClick:x,disabled:l,className:"join-button primary",children:["Join Tournament (",i.entryFee," ",i.currency,")"]}),D&&a.jsx("button",{onClick:M,disabled:l,className:"start-button secondary",children:"Start Tournament"}),a.jsxs("p",{children:["Waiting for players... Share Tournament ID: ",a.jsx("strong",{children:i.id})]})]}),i.players.length>0&&a.jsxs("div",{className:"tournament-player-list",children:[a.jsx("h4",{children:"Registered Players:"}),a.jsx("ul",{children:i.players.map(_=>a.jsx("li",{className:_.isCurrentUser?"current-user-player":"",children:_.name},_.id))})]}),(i.status==="InProgress"||i.status==="Finished")&&C(),i.status==="Finished"&&i.winner&&a.jsxs("div",{className:"tournament-winner-announcement",children:[a.jsx("h3",{children:"Tournament Over!"}),a.jsxs("p",{className:"winner-name",children:["🎉 Winner: ",i.winner.name," 🎉"]}),a.jsxs("p",{children:["Prize Won: ",i.prizePool.toFixed(2)," ",i.currency]})]}),l&&a.jsx("div",{className:"loading-indicator tournament-active-loading",children:"Processing..."})]})},zt={info:console.log,warn:console.warn,error:console.error,success:console.log},Ub=({balance:e,loading:t=!1,isMobile:n=!1,onClick:r})=>{const s=()=>{r&&r()},i=r?"cursor-pointer hover:bg-gray-700":"",o=r?"transition-all duration-200":"";return n?a.jsxs("div",{className:`flex flex-col items-end space-y-1 ${i} p-1 rounded-md ${o}`,onClick:s,children:[a.jsxs("div",{className:"flex items-center text-sm",children:[a.jsx("img",{src:"https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11111111111111111111111111111111111111112/logo.png",alt:"SOL",className:"w-4 h-4 mr-1"}),a.jsx("span",{className:"text-white font-medium",children:t?"...":e.sol.toFixed(4)})]}),a.jsxs("div",{className:"flex items-center text-sm",children:[a.jsx("img",{src:"https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R/logo.png",alt:"RPS",className:"w-4 h-4 mr-1"}),a.jsx("span",{className:"text-white font-medium",children:t?"...":e.rpsToken.toFixed(1)})]})]}):a.jsxs("div",{className:`bg-gray-800 bg-opacity-80 rounded-lg p-2 flex items-center space-x-4 ${i} ${o}`,onClick:s,children:[a.jsxs("div",{className:"flex items-center",children:[a.jsx("img",{src:"https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11111111111111111111111111111111111111112/logo.png",alt:"SOL",className:"w-5 h-5 mr-2"}),a.jsxs("span",{className:"text-white font-medium",children:[t?"...":e.sol.toFixed(4)," SOL"]})]}),a.jsxs("div",{className:"flex items-center",children:[a.jsx("img",{src:"https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R/logo.png",alt:"RPS",className:"w-5 h-5 mr-2"}),a.jsxs("span",{className:"text-white font-medium",children:[t?"...":e.rpsToken.toFixed(1)," RPS"]})]}),r&&a.jsx("div",{className:"ml-1 text-gray-300 hover:text-white",children:a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:a.jsx("path",{fillRule:"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z",clipRule:"evenodd"})})})]})},Fb=({balance:e,onSwap:t,disabled:n=!1})=>{const[r,s]=E.useState(.1),[i,o]=E.useState(!1),[l,c]=E.useState(null),[u,d]=E.useState("SOL_TO_RPS"),h=async()=>{if(!(u==="SOL_TO_RPS"&&(r<=0||r>e.sol))&&!(u==="RPS_TO_SOL"&&(r<=0||r>e.rpsToken))){o(!0),c(null);try{const N=u==="SOL_TO_RPS"?"SOL":"RPS",A=u==="SOL_TO_RPS"?"RPS":"SOL";await t(N,A,r)?(c({text:`Successfully swapped ${r} ${N} for ${A}!`,type:"success"}),s(.1)):c({text:"Failed to swap tokens. Please try again.",type:"error"})}catch(N){c({text:`Error: ${N.message||"Unknown error"}`,type:"error"})}finally{o(!1)}}},y=()=>{d(N=>N==="SOL_TO_RPS"?"RPS_TO_SOL":"SOL_TO_RPS"),s(.1),c(null)},g=n||i||r<=0||u==="SOL_TO_RPS"&&r>e.sol||u==="RPS_TO_SOL"&&r>e.rpsToken,S=()=>u==="SOL_TO_RPS"?e.sol:e.rpsToken;return a.jsxs("div",{className:"p-4 bg-gray-800 rounded-lg border border-gray-700",children:[a.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Swap Tokens"}),l&&a.jsx("div",{className:`p-3 mb-4 rounded-lg ${l.type==="success"?"bg-green-800 bg-opacity-40 text-green-400":"bg-red-800 bg-opacity-40 text-red-400"}`,children:l.text}),a.jsxs("div",{className:"mb-4",children:[a.jsxs("div",{className:"flex justify-between mb-2",children:[a.jsx("label",{className:"text-sm font-medium text-gray-300",children:u==="SOL_TO_RPS"?"Swap SOL for RPS":"Swap RPS for SOL"}),a.jsx("button",{onClick:y,disabled:n||i,className:"text-xs text-purple-400 hover:text-purple-300",children:"Switch Direction"})]}),a.jsxs("div",{className:"flex space-x-2",children:[a.jsxs("div",{className:"flex-1 relative",children:[a.jsx("input",{type:"number",value:r,onChange:N=>s(Math.max(0,parseFloat(N.target.value)||0)),min:"0.01",step:"0.01",disabled:n||i,className:"w-full px-4 py-2 bg-gray-700 rounded-lg border border-gray-600 text-white pr-16"}),a.jsx("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400",children:u==="SOL_TO_RPS"?"SOL":"RPS"})]}),a.jsx("button",{onClick:h,disabled:g,className:`px-4 py-2 rounded-lg font-medium ${g?"bg-gray-600 cursor-not-allowed opacity-50":"bg-purple-600 hover:bg-purple-700"}`,children:i?"Swapping...":"Swap"})]}),a.jsxs("div",{className:"flex justify-between mt-1",children:[a.jsx("div",{className:"text-xs text-gray-400",children:u==="SOL_TO_RPS"?"Exchange rate: 1 SOL = 10 RPS":"Exchange rate: 10 RPS = 1 SOL"}),a.jsx("button",{onClick:()=>s(S()),disabled:n||i,className:"text-xs text-blue-400 hover:text-blue-300",children:"Max"})]}),u==="SOL_TO_RPS"&&r>e.sol&&a.jsx("div",{className:"text-xs text-red-400 mt-1",children:"Insufficient SOL balance"}),u==="RPS_TO_SOL"&&r>e.rpsToken&&a.jsx("div",{className:"text-xs text-red-400 mt-1",children:"Insufficient RPS balance"})]})]})},Gb=({publicKey:e,onClose:t,balance:n,onGetFree:r})=>{const[s,i]=E.useState(!1),o=u=>{u.target===u.currentTarget&&t()},l=async()=>{i(!0);try{await r()}catch(u){console.error("Error getting free tokens:",u)}finally{i(!1)}},c=async(u,d,h)=>{i(!0);try{return await new Promise(y=>setTimeout(y,1e3)),console.log(`Swapping ${h} ${u} to ${d}`),!0}catch(y){return console.error("Error swapping tokens:",y),!1}finally{i(!1)}};return a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4",onClick:o,children:a.jsxs("div",{className:"bg-gray-900 rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto",onClick:u=>u.stopPropagation(),children:[a.jsxs("div",{className:"p-4 border-b border-gray-800 flex justify-between items-center",children:[a.jsx("h2",{className:"text-xl font-bold",children:"Manage Tokens"}),a.jsx("button",{onClick:t,className:"w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-800",children:"✕"})]}),a.jsxs("div",{className:"p-4",children:[a.jsxs("div",{className:"mb-6 p-4 bg-gray-800 rounded-lg",children:[a.jsx("h3",{className:"text-lg font-medium mb-2",children:"Your Balances"}),a.jsx("div",{className:"flex justify-between items-center",children:a.jsxs("div",{className:"flex space-x-6",children:[a.jsxs("div",{children:[a.jsx("div",{className:"text-sm text-gray-400",children:"SOL"}),a.jsx("div",{className:"text-xl font-bold",children:n.sol.toFixed(4)})]}),a.jsxs("div",{children:[a.jsx("div",{className:"text-sm text-gray-400",children:"RPS Token"}),a.jsx("div",{className:"text-xl font-bold",children:n.rpsToken.toFixed(2)})]})]})})]}),a.jsxs("div",{className:"p-4 bg-gray-800 rounded-lg mb-4",children:[a.jsx("h3",{className:"text-lg font-medium mb-3",children:"Get Free Tokens"}),a.jsx("p",{className:"text-gray-300 mb-3",children:"New players can claim free RPS tokens to get started!"}),a.jsx("button",{className:"btn btn-primary w-full",onClick:l,disabled:s,children:s?"Processing...":"Claim Free RPS Tokens"}),a.jsx("p",{className:"text-xs text-gray-400 mt-2",children:"For testing purposes only. Limited to one claim per wallet."})]}),a.jsx(Fb,{balance:n,onSwap:c,disabled:s}),a.jsxs("div",{className:"mt-6 p-4 bg-indigo-900 bg-opacity-30 rounded-lg",children:[a.jsx("h3",{className:"text-lg font-medium mb-2",children:"Why Use RPS Tokens?"}),a.jsxs("ul",{className:"space-y-2",children:[a.jsxs("li",{className:"flex items-start",children:[a.jsx("span",{className:"text-green-400 mr-2",children:"✓"}),a.jsx("span",{children:"5% larger prize pools for all games"})]}),a.jsxs("li",{className:"flex items-start",children:[a.jsx("span",{className:"text-green-400 mr-2",children:"✓"}),a.jsx("span",{children:"50% lower fees (0.05% instead of 0.1%)"})]}),a.jsxs("li",{className:"flex items-start",children:[a.jsx("span",{className:"text-green-400 mr-2",children:"✓"}),a.jsx("span",{children:"Exclusive game modes and tournaments"})]}),a.jsxs("li",{className:"flex items-start",children:[a.jsx("span",{className:"text-green-400 mr-2",children:"✓"}),a.jsx("span",{children:"Future governance and staking rewards"})]})]})]}),e&&a.jsx("div",{className:"mt-4 pt-3 border-t border-gray-700",children:a.jsxs("div",{className:"text-xs text-gray-400",children:[a.jsx("span",{className:"font-semibold",children:"Connected Wallet:"}),a.jsxs("span",{className:"ml-1",children:[e.toString().slice(0,4),"...",e.toString().slice(-4)]})]})})]})]})})},$b=({showVolume:e=!1,className:t=""})=>{const[n,r]=E.useState(!1),[s,i]=E.useState(.8),[o,l]=E.useState(!1);E.useEffect(()=>{const h=localStorage.getItem("rps-audio-muted"),y=localStorage.getItem("rps-audio-volume");if(h!==null){const g=h==="true";r(g),de.setMuted(g)}if(y!==null){const g=parseFloat(y);i(g),de.setVolume(g)}},[]);const c=()=>{const h=!n;r(h),de.setMuted(h),localStorage.setItem("rps-audio-muted",h.toString()),de.play("click")},u=h=>{const y=parseFloat(h.target.value);i(y),de.setVolume(y),localStorage.setItem("rps-audio-volume",y.toString())},d=()=>{l(!o),de.play("click")};return a.jsxs("div",{className:`sound-control relative ${t}`,children:[a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx("button",{onClick:c,className:"p-2 rounded-full hover:bg-gray-700 transition-colors focus:outline-none","aria-label":n?"Unmute":"Mute",children:n?a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-gray-400",viewBox:"0 0 20 20",fill:"currentColor",children:a.jsx("path",{fillRule:"evenodd",d:"M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM12.293 7.293a1 1 0 011.414 0L15 8.586l1.293-1.293a1 1 0 111.414 1.414L16.414 10l1.293 1.293a1 1 0 01-1.414 1.414L15 11.414l-1.293 1.293a1 1 0 01-1.414-1.414L13.586 10l-1.293-1.293a1 1 0 010-1.414z",clipRule:"evenodd"})}):a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-white",viewBox:"0 0 20 20",fill:"currentColor",children:a.jsx("path",{fillRule:"evenodd",d:"M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM14.657 2.929a1 1 0 011.414 0A9.972 9.972 0 0119 10a9.972 9.972 0 01-2.929 7.071 1 1 0 01-1.414-1.414A7.971 7.971 0 0017 10c0-2.21-.894-4.208-2.343-5.657a1 1 0 010-1.414zm-2.829 2.828a1 1 0 011.415 0A5.983 5.983 0 0115 10a5.984 5.984 0 01-1.757 4.243 1 1 0 01-1.415-1.415A3.984 3.984 0 0013 10a3.983 3.983 0 00-1.172-2.828 1 1 0 010-1.415z",clipRule:"evenodd"})})}),!e&&a.jsx("button",{onClick:d,className:"p-2 rounded-full hover:bg-gray-700 transition-colors focus:outline-none","aria-label":"Volume settings",children:a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-gray-300",viewBox:"0 0 20 20",fill:"currentColor",children:a.jsx("path",{fillRule:"evenodd",d:"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z",clipRule:"evenodd"})})})]}),(e||o)&&a.jsxs("div",{className:`volume-slider mt-2 ${e?"":"absolute right-0 top-full mt-2 p-3 bg-gray-800 rounded-lg shadow-lg z-10"}`,children:[a.jsx("input",{type:"range",min:"0",max:"1",step:"0.01",value:s,onChange:u,className:"w-24 accent-purple-500",disabled:n}),a.jsxs("div",{className:"flex justify-between text-xs text-gray-400 mt-1",children:[a.jsx("span",{children:"0%"}),a.jsx("span",{children:"100%"})]})]})]})};class yy extends E.Component{constructor(){super(...arguments);J(this,"state",{hasError:!1})}static getDerivedStateFromError(n){return{hasError:!0,error:n}}componentDidCatch(n,r){console.error("Uncaught error:",n,r),this.setState({error:n,errorInfo:r})}render(){var n;return this.state.hasError?a.jsx("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center p-4",children:a.jsxs("div",{className:"bg-white/10 backdrop-blur-lg rounded-xl p-8 max-w-2xl w-full border border-white/20",children:[a.jsx("h1",{className:"text-3xl font-bold text-white mb-4",children:"🎮 Game Crashed!"}),a.jsx("p",{className:"text-white/80 mb-6",children:"Something went wrong with the Solana RPS Game. This usually happens due to wallet connection issues or browser compatibility problems."}),a.jsxs("div",{className:"bg-red-500/20 border border-red-500/30 rounded-lg p-4 mb-6",children:[a.jsx("h3",{className:"text-red-200 font-semibold mb-2",children:"Error Details:"}),a.jsx("pre",{className:"text-red-100 text-sm overflow-auto max-h-32",children:(n=this.state.error)==null?void 0:n.toString()})]}),a.jsxs("div",{className:"bg-blue-500/20 border border-blue-500/30 rounded-lg p-4 mb-6",children:[a.jsx("h3",{className:"text-blue-200 font-semibold mb-2",children:"Quick Fixes:"}),a.jsxs("ul",{className:"text-blue-100 text-sm space-y-1",children:[a.jsx("li",{children:"• Make sure you have a Solana wallet (Phantom/Solflare) installed"}),a.jsx("li",{children:"• Try refreshing the page"}),a.jsx("li",{children:"• Switch to a supported browser (Chrome, Firefox, Edge)"}),a.jsx("li",{children:"• Check if your wallet is connected to Devnet"}),a.jsx("li",{children:"• Clear browser cache and cookies for this site"})]})]}),a.jsxs("div",{className:"flex gap-4",children:[a.jsx("button",{onClick:()=>window.location.reload(),className:"flex-1 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold py-3 px-6 rounded-lg hover:from-purple-700 hover:to-blue-700 transition-all duration-200",children:"🔄 Reload Game"}),a.jsx("button",{onClick:()=>this.setState({hasError:!1,error:void 0,errorInfo:void 0}),className:"flex-1 bg-gradient-to-r from-green-600 to-emerald-600 text-white font-semibold py-3 px-6 rounded-lg hover:from-green-700 hover:to-emerald-700 transition-all duration-200",children:"🎯 Try Again"})]}),!1]})}):this.props.children}}const wy=Oo.Devnet,ed=rm(wy),Hb=[new CN,new WN({network:wy}),new UN,new GN,new kM],Yb=new se("RPS111111111111111111111111111111111111111"),Kb=()=>a.jsx(yy,{children:a.jsx(uv,{endpoint:ed,children:a.jsx(wN,{wallets:Hb,autoConnect:!0,children:a.jsx(_N,{children:a.jsx(Vb,{})})})})}),Vb=()=>{const{connected:e,publicKey:t,signTransaction:n,sendTransaction:r}=ul(),[s,i]=E.useState(null),[o,l]=E.useState(te.HOME),[c,u]=E.useState(""),[d,h]=E.useState(null),[y,g]=E.useState(0),[S,N]=E.useState(""),[A,p]=E.useState(""),[m,f]=E.useState(""),[x,M]=E.useState(!1),[w,v]=E.useState(!1),[b,T]=E.useState({sol:0,rpsToken:0}),[C,O]=E.useState(!1),[D,I]=E.useState(!1);E.useEffect(()=>{de.playBackgroundMusic(gy.MainMenu,!0,!0)},[]),E.useEffect(()=>{(async()=>{if(e&&t&&n)try{console.log("Initializing game client for wallet:",t.toString());const L=new oo(ed,"confirmed");O(!0);try{const U=await qh(L,t);T(U)}catch(U){console.warn("Error fetching token balances:",U),T({sol:0,rpsToken:0})}O(!1);const P=new db(L,Yb,t,r,n);i(P),console.log("Game client initialized successfully")}catch(L){console.error("Error initializing game client:",L),p("Failed to initialize game client. Please check console for details.")}else i(null),p(""),T({sol:0,rpsToken:0})})()},[e,t,n,r]);const R={gameClient:s,publicKey:t,connected:e,gameId:c,gameData:d,userChoice:y,salt:S,errorMessage:A,statusMessage:m,loading:x,tokenBalance:b,loadingBalance:C,setCurrentView:l,setGameId:u,setGameData:h,setUserChoice:g,setSalt:N,setErrorMessage:p,setStatusMessage:f,setLoading:M,createGame:async(z,L)=>{if(!s){p("Game client not initialized");return}try{M(!0),f("Creating new game...");const P=await s.createGame(z,L===q.SOL);P.success?(u(P.gameId),h(P.gameData),l(te.GAME_LOBBY)):p(P.error||"Failed to create game")}catch(P){console.error("Error creating game:",P),p("Error creating game")}finally{M(!1),f("")}},joinGame:async z=>{if(!s){p("Game client not initialized");return}try{M(!0),f("Joining game...");const L=await s.joinGame(z);L.success?(u(z),h(L.gameData),l(te.GAME_LOBBY)):p(L.error||"Failed to join game")}catch(L){console.error("Error joining game:",L),p("Error joining game")}finally{M(!1),f("")}}};return a.jsxs("div",{className:"app-container",children:[a.jsxs("header",{className:"app-header",children:[a.jsx("div",{className:"logo",children:"Solana RPS Game"}),a.jsxs("div",{className:"header-actions",children:[a.jsx($b,{}),e&&t&&a.jsx(Ub,{balance:b,loading:C,onClick:()=>I(!0)}),a.jsx(uf,{}),a.jsx("button",{className:"mobile-menu-toggle",onClick:()=>v(!w),children:"☰"})]})]}),a.jsxs("nav",{className:`app-nav ${w?"open":""}`,children:[a.jsx("button",{onClick:()=>l(te.HOME),children:"Home"}),a.jsx("button",{onClick:()=>l(te.CREATE_GAME),children:"Create Game"}),a.jsx("button",{onClick:()=>l(te.JOIN_GAME),children:"Join Game"}),a.jsx("button",{onClick:()=>l(te.TOURNAMENT),children:"Tournament"}),a.jsx("button",{onClick:()=>l(te.AUTO_PLAY),children:"Auto Play"}),a.jsx("button",{onClick:()=>l(te.WELCOME),children:"Welcome"}),a.jsx("button",{onClick:()=>l(te.SECURITY),children:"Security"}),a.jsx("button",{onClick:()=>l(te.TESTING),children:"Testing"}),a.jsx("button",{onClick:()=>l(te.PROFILE),children:"Profile"})," "]}),a.jsxs("main",{className:"app-main",children:[o===te.HOME&&a.jsx(hb,{...R}),o===te.CREATE_GAME&&a.jsx(pb,{...R}),o===te.JOIN_GAME&&a.jsx(gb,{...R}),o===te.GAME_LOBBY&&a.jsx(xb,{...R}),o===te.COMMIT_CHOICE&&a.jsx(Sb,{...R}),o===te.REVEAL_CHOICE&&a.jsx(Nb,{...R}),o===te.GAME_RESULTS&&a.jsx(Mb,{...R}),o===te.AUTO_PLAY&&a.jsx(Tb,{...R}),o===te.WELCOME&&a.jsx(Ib,{...R}),o===te.SECURITY&&a.jsx(kb,{...R}),o===te.TESTING&&a.jsx(Ob,{}),o===te.TOURNAMENT&&a.jsx(Bb,{gameClient:s,currentUserPublicKey:t||null,onTournamentEnd:()=>l(te.HOME),audioService:de}),o===te.PROFILE&&a.jsx(zb,{publicKey:t,connected:e,onBackToHome:()=>l(te.HOME)})]}),a.jsx("footer",{className:"app-footer",children:a.jsx("p",{children:"Solana Rock Paper Scissors Game - Built with ♥ for Solana"})}),D&&a.jsx(Gb,{publicKey:t,balance:b,onClose:()=>I(!1),onGetFree:async()=>{if(t&&s){const z=new oo(ed,"confirmed");console.log("Free tokens feature requires wallet signing - not implemented yet");const L=await qh(z,t);T(L)}}})]})},vy=document.getElementById("root");if(!vy)throw new Error("Failed to find root element");console.log("Main.tsx loaded - React application starting");dc.createRoot(vy).render(a.jsx(W.StrictMode,{children:a.jsx(yy,{children:a.jsx(Kb,{})})}));export{E2 as D,Ft as S,DE as T,Xb as a,d0 as b,Zr as c,N2 as d,Ae as e,Xn as f,qb as l};
function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = ["assets/index-DY-jIVDY.js","assets/solana-D2Z2xLUD.js","assets/index-BwgYx0Rk.js","assets/TransportWebHID-DUFM2z6P.js"]
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}
