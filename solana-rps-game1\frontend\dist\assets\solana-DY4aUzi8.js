var ru=Object.defineProperty;var nu=(n,t,e)=>t in n?ru(n,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):n[t]=e;var de=(n,t,e)=>(nu(n,typeof t!="symbol"?t+"":t,e),e);var je=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{};function ai(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}function Is(n){if(n.__esModule)return n;var t=n.default;if(typeof t=="function"){var e=function r(){return this instanceof r?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};e.prototype=t.prototype}else e={};return Object.defineProperty(e,"__esModule",{value:!0}),Object.keys(n).forEach(function(r){var s=Object.getOwnPropertyDescriptor(n,r);Object.defineProperty(e,r,s.get?s:{enumerable:!0,get:function(){return n[r]}})}),e}var ct={},ci={};ci.byteLength=ou;ci.toByteArray=cu;ci.fromByteArray=lu;var Qe=[],qe=[],iu=typeof Uint8Array<"u"?Uint8Array:Array,Hi="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";for(var Vr=0,su=Hi.length;Vr<su;++Vr)Qe[Vr]=Hi[Vr],qe[Hi.charCodeAt(Vr)]=Vr;qe[45]=62;qe[95]=63;function la(n){var t=n.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var e=n.indexOf("=");e===-1&&(e=t);var r=e===t?0:4-e%4;return[e,r]}function ou(n){var t=la(n),e=t[0],r=t[1];return(e+r)*3/4-r}function au(n,t,e){return(t+e)*3/4-e}function cu(n){var t,e=la(n),r=e[0],s=e[1],i=new iu(au(n,r,s)),o=0,a=s>0?r-4:r,h;for(h=0;h<a;h+=4)t=qe[n.charCodeAt(h)]<<18|qe[n.charCodeAt(h+1)]<<12|qe[n.charCodeAt(h+2)]<<6|qe[n.charCodeAt(h+3)],i[o++]=t>>16&255,i[o++]=t>>8&255,i[o++]=t&255;return s===2&&(t=qe[n.charCodeAt(h)]<<2|qe[n.charCodeAt(h+1)]>>4,i[o++]=t&255),s===1&&(t=qe[n.charCodeAt(h)]<<10|qe[n.charCodeAt(h+1)]<<4|qe[n.charCodeAt(h+2)]>>2,i[o++]=t>>8&255,i[o++]=t&255),i}function uu(n){return Qe[n>>18&63]+Qe[n>>12&63]+Qe[n>>6&63]+Qe[n&63]}function fu(n,t,e){for(var r,s=[],i=t;i<e;i+=3)r=(n[i]<<16&16711680)+(n[i+1]<<8&65280)+(n[i+2]&255),s.push(uu(r));return s.join("")}function lu(n){for(var t,e=n.length,r=e%3,s=[],i=16383,o=0,a=e-r;o<a;o+=i)s.push(fu(n,o,o+i>a?a:o+i));return r===1?(t=n[e-1],s.push(Qe[t>>2]+Qe[t<<4&63]+"==")):r===2&&(t=(n[e-2]<<8)+n[e-1],s.push(Qe[t>>10]+Qe[t>>4&63]+Qe[t<<2&63]+"=")),s.join("")}var As={};/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */As.read=function(n,t,e,r,s){var i,o,a=s*8-r-1,h=(1<<a)-1,y=h>>1,m=-7,E=e?s-1:0,M=e?-1:1,k=n[t+E];for(E+=M,i=k&(1<<-m)-1,k>>=-m,m+=a;m>0;i=i*256+n[t+E],E+=M,m-=8);for(o=i&(1<<-m)-1,i>>=-m,m+=r;m>0;o=o*256+n[t+E],E+=M,m-=8);if(i===0)i=1-y;else{if(i===h)return o?NaN:(k?-1:1)*(1/0);o=o+Math.pow(2,r),i=i-y}return(k?-1:1)*o*Math.pow(2,i-r)};As.write=function(n,t,e,r,s,i){var o,a,h,y=i*8-s-1,m=(1<<y)-1,E=m>>1,M=s===23?Math.pow(2,-24)-Math.pow(2,-77):0,k=r?0:i-1,R=r?1:-1,B=t<0||t===0&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(a=isNaN(t)?1:0,o=m):(o=Math.floor(Math.log(t)/Math.LN2),t*(h=Math.pow(2,-o))<1&&(o--,h*=2),o+E>=1?t+=M/h:t+=M*Math.pow(2,1-E),t*h>=2&&(o++,h/=2),o+E>=m?(a=0,o=m):o+E>=1?(a=(t*h-1)*Math.pow(2,s),o=o+E):(a=t*Math.pow(2,E-1)*Math.pow(2,s),o=0));s>=8;n[e+k]=a&255,k+=R,a/=256,s-=8);for(o=o<<s|a,y+=s;y>0;n[e+k]=o&255,k+=R,o/=256,y-=8);n[e+k-R]|=B*128};/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */(function(n){const t=ci,e=As,r=typeof Symbol=="function"&&typeof Symbol.for=="function"?Symbol.for("nodejs.util.inspect.custom"):null;n.Buffer=a,n.SlowBuffer=K,n.INSPECT_MAX_BYTES=50;const s=**********;n.kMaxLength=s,a.TYPED_ARRAY_SUPPORT=i(),!a.TYPED_ARRAY_SUPPORT&&typeof console<"u"&&typeof console.error=="function"&&console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support.");function i(){try{const g=new Uint8Array(1),u={foo:function(){return 42}};return Object.setPrototypeOf(u,Uint8Array.prototype),Object.setPrototypeOf(g,u),g.foo()===42}catch{return!1}}Object.defineProperty(a.prototype,"parent",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.buffer}}),Object.defineProperty(a.prototype,"offset",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.byteOffset}});function o(g){if(g>s)throw new RangeError('The value "'+g+'" is invalid for option "size"');const u=new Uint8Array(g);return Object.setPrototypeOf(u,a.prototype),u}function a(g,u,f){if(typeof g=="number"){if(typeof u=="string")throw new TypeError('The "string" argument must be of type string. Received type number');return E(g)}return h(g,u,f)}a.poolSize=8192;function h(g,u,f){if(typeof g=="string")return M(g,u);if(ArrayBuffer.isView(g))return R(g);if(g==null)throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof g);if(Ne(g,ArrayBuffer)||g&&Ne(g.buffer,ArrayBuffer)||typeof SharedArrayBuffer<"u"&&(Ne(g,SharedArrayBuffer)||g&&Ne(g.buffer,SharedArrayBuffer)))return B(g,u,f);if(typeof g=="number")throw new TypeError('The "value" argument must not be of type number. Received type number');const x=g.valueOf&&g.valueOf();if(x!=null&&x!==g)return a.from(x,u,f);const T=I(g);if(T)return T;if(typeof Symbol<"u"&&Symbol.toPrimitive!=null&&typeof g[Symbol.toPrimitive]=="function")return a.from(g[Symbol.toPrimitive]("string"),u,f);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof g)}a.from=function(g,u,f){return h(g,u,f)},Object.setPrototypeOf(a.prototype,Uint8Array.prototype),Object.setPrototypeOf(a,Uint8Array);function y(g){if(typeof g!="number")throw new TypeError('"size" argument must be of type number');if(g<0)throw new RangeError('The value "'+g+'" is invalid for option "size"')}function m(g,u,f){return y(g),g<=0?o(g):u!==void 0?typeof f=="string"?o(g).fill(u,f):o(g).fill(u):o(g)}a.alloc=function(g,u,f){return m(g,u,f)};function E(g){return y(g),o(g<0?0:C(g)|0)}a.allocUnsafe=function(g){return E(g)},a.allocUnsafeSlow=function(g){return E(g)};function M(g,u){if((typeof u!="string"||u==="")&&(u="utf8"),!a.isEncoding(u))throw new TypeError("Unknown encoding: "+u);const f=q(g,u)|0;let x=o(f);const T=x.write(g,u);return T!==f&&(x=x.slice(0,T)),x}function k(g){const u=g.length<0?0:C(g.length)|0,f=o(u);for(let x=0;x<u;x+=1)f[x]=g[x]&255;return f}function R(g){if(Ne(g,Uint8Array)){const u=new Uint8Array(g);return B(u.buffer,u.byteOffset,u.byteLength)}return k(g)}function B(g,u,f){if(u<0||g.byteLength<u)throw new RangeError('"offset" is outside of buffer bounds');if(g.byteLength<u+(f||0))throw new RangeError('"length" is outside of buffer bounds');let x;return u===void 0&&f===void 0?x=new Uint8Array(g):f===void 0?x=new Uint8Array(g,u):x=new Uint8Array(g,u,f),Object.setPrototypeOf(x,a.prototype),x}function I(g){if(a.isBuffer(g)){const u=C(g.length)|0,f=o(u);return f.length===0||g.copy(f,0,0,u),f}if(g.length!==void 0)return typeof g.length!="number"||Nt(g.length)?o(0):k(g);if(g.type==="Buffer"&&Array.isArray(g.data))return k(g.data)}function C(g){if(g>=s)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+s.toString(16)+" bytes");return g|0}function K(g){return+g!=g&&(g=0),a.alloc(+g)}a.isBuffer=function(u){return u!=null&&u._isBuffer===!0&&u!==a.prototype},a.compare=function(u,f){if(Ne(u,Uint8Array)&&(u=a.from(u,u.offset,u.byteLength)),Ne(f,Uint8Array)&&(f=a.from(f,f.offset,f.byteLength)),!a.isBuffer(u)||!a.isBuffer(f))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(u===f)return 0;let x=u.length,T=f.length;for(let L=0,D=Math.min(x,T);L<D;++L)if(u[L]!==f[L]){x=u[L],T=f[L];break}return x<T?-1:T<x?1:0},a.isEncoding=function(u){switch(String(u).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},a.concat=function(u,f){if(!Array.isArray(u))throw new TypeError('"list" argument must be an Array of Buffers');if(u.length===0)return a.alloc(0);let x;if(f===void 0)for(f=0,x=0;x<u.length;++x)f+=u[x].length;const T=a.allocUnsafe(f);let L=0;for(x=0;x<u.length;++x){let D=u[x];if(Ne(D,Uint8Array))L+D.length>T.length?(a.isBuffer(D)||(D=a.from(D)),D.copy(T,L)):Uint8Array.prototype.set.call(T,D,L);else if(a.isBuffer(D))D.copy(T,L);else throw new TypeError('"list" argument must be an Array of Buffers');L+=D.length}return T};function q(g,u){if(a.isBuffer(g))return g.length;if(ArrayBuffer.isView(g)||Ne(g,ArrayBuffer))return g.byteLength;if(typeof g!="string")throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof g);const f=g.length,x=arguments.length>2&&arguments[2]===!0;if(!x&&f===0)return 0;let T=!1;for(;;)switch(u){case"ascii":case"latin1":case"binary":return f;case"utf8":case"utf-8":return Ut(g).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return f*2;case"hex":return f>>>1;case"base64":return Dt(g).length;default:if(T)return x?-1:Ut(g).length;u=(""+u).toLowerCase(),T=!0}}a.byteLength=q;function V(g,u,f){let x=!1;if((u===void 0||u<0)&&(u=0),u>this.length||((f===void 0||f>this.length)&&(f=this.length),f<=0)||(f>>>=0,u>>>=0,f<=u))return"";for(g||(g="utf8");;)switch(g){case"hex":return A(this,u,f);case"utf8":case"utf-8":return c(this,u,f);case"ascii":return v(this,u,f);case"latin1":case"binary":return S(this,u,f);case"base64":return b(this,u,f);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return P(this,u,f);default:if(x)throw new TypeError("Unknown encoding: "+g);g=(g+"").toLowerCase(),x=!0}}a.prototype._isBuffer=!0;function F(g,u,f){const x=g[u];g[u]=g[f],g[f]=x}a.prototype.swap16=function(){const u=this.length;if(u%2!==0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(let f=0;f<u;f+=2)F(this,f,f+1);return this},a.prototype.swap32=function(){const u=this.length;if(u%4!==0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(let f=0;f<u;f+=4)F(this,f,f+3),F(this,f+1,f+2);return this},a.prototype.swap64=function(){const u=this.length;if(u%8!==0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(let f=0;f<u;f+=8)F(this,f,f+7),F(this,f+1,f+6),F(this,f+2,f+5),F(this,f+3,f+4);return this},a.prototype.toString=function(){const u=this.length;return u===0?"":arguments.length===0?c(this,0,u):V.apply(this,arguments)},a.prototype.toLocaleString=a.prototype.toString,a.prototype.equals=function(u){if(!a.isBuffer(u))throw new TypeError("Argument must be a Buffer");return this===u?!0:a.compare(this,u)===0},a.prototype.inspect=function(){let u="";const f=n.INSPECT_MAX_BYTES;return u=this.toString("hex",0,f).replace(/(.{2})/g,"$1 ").trim(),this.length>f&&(u+=" ... "),"<Buffer "+u+">"},r&&(a.prototype[r]=a.prototype.inspect),a.prototype.compare=function(u,f,x,T,L){if(Ne(u,Uint8Array)&&(u=a.from(u,u.offset,u.byteLength)),!a.isBuffer(u))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof u);if(f===void 0&&(f=0),x===void 0&&(x=u?u.length:0),T===void 0&&(T=0),L===void 0&&(L=this.length),f<0||x>u.length||T<0||L>this.length)throw new RangeError("out of range index");if(T>=L&&f>=x)return 0;if(T>=L)return-1;if(f>=x)return 1;if(f>>>=0,x>>>=0,T>>>=0,L>>>=0,this===u)return 0;let D=L-T,pt=x-f;const ht=Math.min(D,pt),lt=this.slice(T,L),Ct=u.slice(f,x);for(let ft=0;ft<ht;++ft)if(lt[ft]!==Ct[ft]){D=lt[ft],pt=Ct[ft];break}return D<pt?-1:pt<D?1:0};function j(g,u,f,x,T){if(g.length===0)return-1;if(typeof f=="string"?(x=f,f=0):f>**********?f=**********:f<-2147483648&&(f=-2147483648),f=+f,Nt(f)&&(f=T?0:g.length-1),f<0&&(f=g.length+f),f>=g.length){if(T)return-1;f=g.length-1}else if(f<0)if(T)f=0;else return-1;if(typeof u=="string"&&(u=a.from(u,x)),a.isBuffer(u))return u.length===0?-1:G(g,u,f,x,T);if(typeof u=="number")return u=u&255,typeof Uint8Array.prototype.indexOf=="function"?T?Uint8Array.prototype.indexOf.call(g,u,f):Uint8Array.prototype.lastIndexOf.call(g,u,f):G(g,[u],f,x,T);throw new TypeError("val must be string, number or Buffer")}function G(g,u,f,x,T){let L=1,D=g.length,pt=u.length;if(x!==void 0&&(x=String(x).toLowerCase(),x==="ucs2"||x==="ucs-2"||x==="utf16le"||x==="utf-16le")){if(g.length<2||u.length<2)return-1;L=2,D/=2,pt/=2,f/=2}function ht(Ct,ft){return L===1?Ct[ft]:Ct.readUInt16BE(ft*L)}let lt;if(T){let Ct=-1;for(lt=f;lt<D;lt++)if(ht(g,lt)===ht(u,Ct===-1?0:lt-Ct)){if(Ct===-1&&(Ct=lt),lt-Ct+1===pt)return Ct*L}else Ct!==-1&&(lt-=lt-Ct),Ct=-1}else for(f+pt>D&&(f=D-pt),lt=f;lt>=0;lt--){let Ct=!0;for(let ft=0;ft<pt;ft++)if(ht(g,lt+ft)!==ht(u,ft)){Ct=!1;break}if(Ct)return lt}return-1}a.prototype.includes=function(u,f,x){return this.indexOf(u,f,x)!==-1},a.prototype.indexOf=function(u,f,x){return j(this,u,f,x,!0)},a.prototype.lastIndexOf=function(u,f,x){return j(this,u,f,x,!1)};function Q(g,u,f,x){f=Number(f)||0;const T=g.length-f;x?(x=Number(x),x>T&&(x=T)):x=T;const L=u.length;x>L/2&&(x=L/2);let D;for(D=0;D<x;++D){const pt=parseInt(u.substr(D*2,2),16);if(Nt(pt))return D;g[f+D]=pt}return D}function U(g,u,f,x){return Lt(Ut(u,g.length-f),g,f,x)}function $(g,u,f,x){return Lt(Ht(u),g,f,x)}function z(g,u,f,x){return Lt(Dt(u),g,f,x)}function W(g,u,f,x){return Lt(Nn(u,g.length-f),g,f,x)}a.prototype.write=function(u,f,x,T){if(f===void 0)T="utf8",x=this.length,f=0;else if(x===void 0&&typeof f=="string")T=f,x=this.length,f=0;else if(isFinite(f))f=f>>>0,isFinite(x)?(x=x>>>0,T===void 0&&(T="utf8")):(T=x,x=void 0);else throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");const L=this.length-f;if((x===void 0||x>L)&&(x=L),u.length>0&&(x<0||f<0)||f>this.length)throw new RangeError("Attempt to write outside buffer bounds");T||(T="utf8");let D=!1;for(;;)switch(T){case"hex":return Q(this,u,f,x);case"utf8":case"utf-8":return U(this,u,f,x);case"ascii":case"latin1":case"binary":return $(this,u,f,x);case"base64":return z(this,u,f,x);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return W(this,u,f,x);default:if(D)throw new TypeError("Unknown encoding: "+T);T=(""+T).toLowerCase(),D=!0}},a.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};function b(g,u,f){return u===0&&f===g.length?t.fromByteArray(g):t.fromByteArray(g.slice(u,f))}function c(g,u,f){f=Math.min(g.length,f);const x=[];let T=u;for(;T<f;){const L=g[T];let D=null,pt=L>239?4:L>223?3:L>191?2:1;if(T+pt<=f){let ht,lt,Ct,ft;switch(pt){case 1:L<128&&(D=L);break;case 2:ht=g[T+1],(ht&192)===128&&(ft=(L&31)<<6|ht&63,ft>127&&(D=ft));break;case 3:ht=g[T+1],lt=g[T+2],(ht&192)===128&&(lt&192)===128&&(ft=(L&15)<<12|(ht&63)<<6|lt&63,ft>2047&&(ft<55296||ft>57343)&&(D=ft));break;case 4:ht=g[T+1],lt=g[T+2],Ct=g[T+3],(ht&192)===128&&(lt&192)===128&&(Ct&192)===128&&(ft=(L&15)<<18|(ht&63)<<12|(lt&63)<<6|Ct&63,ft>65535&&ft<1114112&&(D=ft))}}D===null?(D=65533,pt=1):D>65535&&(D-=65536,x.push(D>>>10&1023|55296),D=56320|D&1023),x.push(D),T+=pt}return p(x)}const d=4096;function p(g){const u=g.length;if(u<=d)return String.fromCharCode.apply(String,g);let f="",x=0;for(;x<u;)f+=String.fromCharCode.apply(String,g.slice(x,x+=d));return f}function v(g,u,f){let x="";f=Math.min(g.length,f);for(let T=u;T<f;++T)x+=String.fromCharCode(g[T]&127);return x}function S(g,u,f){let x="";f=Math.min(g.length,f);for(let T=u;T<f;++T)x+=String.fromCharCode(g[T]);return x}function A(g,u,f){const x=g.length;(!u||u<0)&&(u=0),(!f||f<0||f>x)&&(f=x);let T="";for(let L=u;L<f;++L)T+=Vt[g[L]];return T}function P(g,u,f){const x=g.slice(u,f);let T="";for(let L=0;L<x.length-1;L+=2)T+=String.fromCharCode(x[L]+x[L+1]*256);return T}a.prototype.slice=function(u,f){const x=this.length;u=~~u,f=f===void 0?x:~~f,u<0?(u+=x,u<0&&(u=0)):u>x&&(u=x),f<0?(f+=x,f<0&&(f=0)):f>x&&(f=x),f<u&&(f=u);const T=this.subarray(u,f);return Object.setPrototypeOf(T,a.prototype),T};function w(g,u,f){if(g%1!==0||g<0)throw new RangeError("offset is not uint");if(g+u>f)throw new RangeError("Trying to access beyond buffer length")}a.prototype.readUintLE=a.prototype.readUIntLE=function(u,f,x){u=u>>>0,f=f>>>0,x||w(u,f,this.length);let T=this[u],L=1,D=0;for(;++D<f&&(L*=256);)T+=this[u+D]*L;return T},a.prototype.readUintBE=a.prototype.readUIntBE=function(u,f,x){u=u>>>0,f=f>>>0,x||w(u,f,this.length);let T=this[u+--f],L=1;for(;f>0&&(L*=256);)T+=this[u+--f]*L;return T},a.prototype.readUint8=a.prototype.readUInt8=function(u,f){return u=u>>>0,f||w(u,1,this.length),this[u]},a.prototype.readUint16LE=a.prototype.readUInt16LE=function(u,f){return u=u>>>0,f||w(u,2,this.length),this[u]|this[u+1]<<8},a.prototype.readUint16BE=a.prototype.readUInt16BE=function(u,f){return u=u>>>0,f||w(u,2,this.length),this[u]<<8|this[u+1]},a.prototype.readUint32LE=a.prototype.readUInt32LE=function(u,f){return u=u>>>0,f||w(u,4,this.length),(this[u]|this[u+1]<<8|this[u+2]<<16)+this[u+3]*16777216},a.prototype.readUint32BE=a.prototype.readUInt32BE=function(u,f){return u=u>>>0,f||w(u,4,this.length),this[u]*16777216+(this[u+1]<<16|this[u+2]<<8|this[u+3])},a.prototype.readBigUInt64LE=He(function(u){u=u>>>0,ur(u,"offset");const f=this[u],x=this[u+7];(f===void 0||x===void 0)&&Rt(u,this.length-8);const T=f+this[++u]*2**8+this[++u]*2**16+this[++u]*2**24,L=this[++u]+this[++u]*2**8+this[++u]*2**16+x*2**24;return BigInt(T)+(BigInt(L)<<BigInt(32))}),a.prototype.readBigUInt64BE=He(function(u){u=u>>>0,ur(u,"offset");const f=this[u],x=this[u+7];(f===void 0||x===void 0)&&Rt(u,this.length-8);const T=f*2**24+this[++u]*2**16+this[++u]*2**8+this[++u],L=this[++u]*2**24+this[++u]*2**16+this[++u]*2**8+x;return(BigInt(T)<<BigInt(32))+BigInt(L)}),a.prototype.readIntLE=function(u,f,x){u=u>>>0,f=f>>>0,x||w(u,f,this.length);let T=this[u],L=1,D=0;for(;++D<f&&(L*=256);)T+=this[u+D]*L;return L*=128,T>=L&&(T-=Math.pow(2,8*f)),T},a.prototype.readIntBE=function(u,f,x){u=u>>>0,f=f>>>0,x||w(u,f,this.length);let T=f,L=1,D=this[u+--T];for(;T>0&&(L*=256);)D+=this[u+--T]*L;return L*=128,D>=L&&(D-=Math.pow(2,8*f)),D},a.prototype.readInt8=function(u,f){return u=u>>>0,f||w(u,1,this.length),this[u]&128?(255-this[u]+1)*-1:this[u]},a.prototype.readInt16LE=function(u,f){u=u>>>0,f||w(u,2,this.length);const x=this[u]|this[u+1]<<8;return x&32768?x|4294901760:x},a.prototype.readInt16BE=function(u,f){u=u>>>0,f||w(u,2,this.length);const x=this[u+1]|this[u]<<8;return x&32768?x|4294901760:x},a.prototype.readInt32LE=function(u,f){return u=u>>>0,f||w(u,4,this.length),this[u]|this[u+1]<<8|this[u+2]<<16|this[u+3]<<24},a.prototype.readInt32BE=function(u,f){return u=u>>>0,f||w(u,4,this.length),this[u]<<24|this[u+1]<<16|this[u+2]<<8|this[u+3]},a.prototype.readBigInt64LE=He(function(u){u=u>>>0,ur(u,"offset");const f=this[u],x=this[u+7];(f===void 0||x===void 0)&&Rt(u,this.length-8);const T=this[u+4]+this[u+5]*2**8+this[u+6]*2**16+(x<<24);return(BigInt(T)<<BigInt(32))+BigInt(f+this[++u]*2**8+this[++u]*2**16+this[++u]*2**24)}),a.prototype.readBigInt64BE=He(function(u){u=u>>>0,ur(u,"offset");const f=this[u],x=this[u+7];(f===void 0||x===void 0)&&Rt(u,this.length-8);const T=(f<<24)+this[++u]*2**16+this[++u]*2**8+this[++u];return(BigInt(T)<<BigInt(32))+BigInt(this[++u]*2**24+this[++u]*2**16+this[++u]*2**8+x)}),a.prototype.readFloatLE=function(u,f){return u=u>>>0,f||w(u,4,this.length),e.read(this,u,!0,23,4)},a.prototype.readFloatBE=function(u,f){return u=u>>>0,f||w(u,4,this.length),e.read(this,u,!1,23,4)},a.prototype.readDoubleLE=function(u,f){return u=u>>>0,f||w(u,8,this.length),e.read(this,u,!0,52,8)},a.prototype.readDoubleBE=function(u,f){return u=u>>>0,f||w(u,8,this.length),e.read(this,u,!1,52,8)};function l(g,u,f,x,T,L){if(!a.isBuffer(g))throw new TypeError('"buffer" argument must be a Buffer instance');if(u>T||u<L)throw new RangeError('"value" argument is out of bounds');if(f+x>g.length)throw new RangeError("Index out of range")}a.prototype.writeUintLE=a.prototype.writeUIntLE=function(u,f,x,T){if(u=+u,f=f>>>0,x=x>>>0,!T){const pt=Math.pow(2,8*x)-1;l(this,u,f,x,pt,0)}let L=1,D=0;for(this[f]=u&255;++D<x&&(L*=256);)this[f+D]=u/L&255;return f+x},a.prototype.writeUintBE=a.prototype.writeUIntBE=function(u,f,x,T){if(u=+u,f=f>>>0,x=x>>>0,!T){const pt=Math.pow(2,8*x)-1;l(this,u,f,x,pt,0)}let L=x-1,D=1;for(this[f+L]=u&255;--L>=0&&(D*=256);)this[f+L]=u/D&255;return f+x},a.prototype.writeUint8=a.prototype.writeUInt8=function(u,f,x){return u=+u,f=f>>>0,x||l(this,u,f,1,255,0),this[f]=u&255,f+1},a.prototype.writeUint16LE=a.prototype.writeUInt16LE=function(u,f,x){return u=+u,f=f>>>0,x||l(this,u,f,2,65535,0),this[f]=u&255,this[f+1]=u>>>8,f+2},a.prototype.writeUint16BE=a.prototype.writeUInt16BE=function(u,f,x){return u=+u,f=f>>>0,x||l(this,u,f,2,65535,0),this[f]=u>>>8,this[f+1]=u&255,f+2},a.prototype.writeUint32LE=a.prototype.writeUInt32LE=function(u,f,x){return u=+u,f=f>>>0,x||l(this,u,f,4,4294967295,0),this[f+3]=u>>>24,this[f+2]=u>>>16,this[f+1]=u>>>8,this[f]=u&255,f+4},a.prototype.writeUint32BE=a.prototype.writeUInt32BE=function(u,f,x){return u=+u,f=f>>>0,x||l(this,u,f,4,4294967295,0),this[f]=u>>>24,this[f+1]=u>>>16,this[f+2]=u>>>8,this[f+3]=u&255,f+4};function _(g,u,f,x,T){Ft(u,x,T,g,f,7);let L=Number(u&BigInt(4294967295));g[f++]=L,L=L>>8,g[f++]=L,L=L>>8,g[f++]=L,L=L>>8,g[f++]=L;let D=Number(u>>BigInt(32)&BigInt(4294967295));return g[f++]=D,D=D>>8,g[f++]=D,D=D>>8,g[f++]=D,D=D>>8,g[f++]=D,f}function nt(g,u,f,x,T){Ft(u,x,T,g,f,7);let L=Number(u&BigInt(4294967295));g[f+7]=L,L=L>>8,g[f+6]=L,L=L>>8,g[f+5]=L,L=L>>8,g[f+4]=L;let D=Number(u>>BigInt(32)&BigInt(4294967295));return g[f+3]=D,D=D>>8,g[f+2]=D,D=D>>8,g[f+1]=D,D=D>>8,g[f]=D,f+8}a.prototype.writeBigUInt64LE=He(function(u,f=0){return _(this,u,f,BigInt(0),BigInt("0xffffffffffffffff"))}),a.prototype.writeBigUInt64BE=He(function(u,f=0){return nt(this,u,f,BigInt(0),BigInt("0xffffffffffffffff"))}),a.prototype.writeIntLE=function(u,f,x,T){if(u=+u,f=f>>>0,!T){const ht=Math.pow(2,8*x-1);l(this,u,f,x,ht-1,-ht)}let L=0,D=1,pt=0;for(this[f]=u&255;++L<x&&(D*=256);)u<0&&pt===0&&this[f+L-1]!==0&&(pt=1),this[f+L]=(u/D>>0)-pt&255;return f+x},a.prototype.writeIntBE=function(u,f,x,T){if(u=+u,f=f>>>0,!T){const ht=Math.pow(2,8*x-1);l(this,u,f,x,ht-1,-ht)}let L=x-1,D=1,pt=0;for(this[f+L]=u&255;--L>=0&&(D*=256);)u<0&&pt===0&&this[f+L+1]!==0&&(pt=1),this[f+L]=(u/D>>0)-pt&255;return f+x},a.prototype.writeInt8=function(u,f,x){return u=+u,f=f>>>0,x||l(this,u,f,1,127,-128),u<0&&(u=255+u+1),this[f]=u&255,f+1},a.prototype.writeInt16LE=function(u,f,x){return u=+u,f=f>>>0,x||l(this,u,f,2,32767,-32768),this[f]=u&255,this[f+1]=u>>>8,f+2},a.prototype.writeInt16BE=function(u,f,x){return u=+u,f=f>>>0,x||l(this,u,f,2,32767,-32768),this[f]=u>>>8,this[f+1]=u&255,f+2},a.prototype.writeInt32LE=function(u,f,x){return u=+u,f=f>>>0,x||l(this,u,f,4,**********,-2147483648),this[f]=u&255,this[f+1]=u>>>8,this[f+2]=u>>>16,this[f+3]=u>>>24,f+4},a.prototype.writeInt32BE=function(u,f,x){return u=+u,f=f>>>0,x||l(this,u,f,4,**********,-2147483648),u<0&&(u=4294967295+u+1),this[f]=u>>>24,this[f+1]=u>>>16,this[f+2]=u>>>8,this[f+3]=u&255,f+4},a.prototype.writeBigInt64LE=He(function(u,f=0){return _(this,u,f,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))}),a.prototype.writeBigInt64BE=He(function(u,f=0){return nt(this,u,f,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))});function J(g,u,f,x,T,L){if(f+x>g.length)throw new RangeError("Index out of range");if(f<0)throw new RangeError("Index out of range")}function it(g,u,f,x,T){return u=+u,f=f>>>0,T||J(g,u,f,4),e.write(g,u,f,x,23,4),f+4}a.prototype.writeFloatLE=function(u,f,x){return it(this,u,f,!0,x)},a.prototype.writeFloatBE=function(u,f,x){return it(this,u,f,!1,x)};function At(g,u,f,x,T){return u=+u,f=f>>>0,T||J(g,u,f,8),e.write(g,u,f,x,52,8),f+8}a.prototype.writeDoubleLE=function(u,f,x){return At(this,u,f,!0,x)},a.prototype.writeDoubleBE=function(u,f,x){return At(this,u,f,!1,x)},a.prototype.copy=function(u,f,x,T){if(!a.isBuffer(u))throw new TypeError("argument should be a Buffer");if(x||(x=0),!T&&T!==0&&(T=this.length),f>=u.length&&(f=u.length),f||(f=0),T>0&&T<x&&(T=x),T===x||u.length===0||this.length===0)return 0;if(f<0)throw new RangeError("targetStart out of bounds");if(x<0||x>=this.length)throw new RangeError("Index out of range");if(T<0)throw new RangeError("sourceEnd out of bounds");T>this.length&&(T=this.length),u.length-f<T-x&&(T=u.length-f+x);const L=T-x;return this===u&&typeof Uint8Array.prototype.copyWithin=="function"?this.copyWithin(f,x,T):Uint8Array.prototype.set.call(u,this.subarray(x,T),f),L},a.prototype.fill=function(u,f,x,T){if(typeof u=="string"){if(typeof f=="string"?(T=f,f=0,x=this.length):typeof x=="string"&&(T=x,x=this.length),T!==void 0&&typeof T!="string")throw new TypeError("encoding must be a string");if(typeof T=="string"&&!a.isEncoding(T))throw new TypeError("Unknown encoding: "+T);if(u.length===1){const D=u.charCodeAt(0);(T==="utf8"&&D<128||T==="latin1")&&(u=D)}}else typeof u=="number"?u=u&255:typeof u=="boolean"&&(u=Number(u));if(f<0||this.length<f||this.length<x)throw new RangeError("Out of range index");if(x<=f)return this;f=f>>>0,x=x===void 0?this.length:x>>>0,u||(u=0);let L;if(typeof u=="number")for(L=f;L<x;++L)this[L]=u;else{const D=a.isBuffer(u)?u:a.from(u,T),pt=D.length;if(pt===0)throw new TypeError('The value "'+u+'" is invalid for argument "value"');for(L=0;L<x-f;++L)this[L+f]=D[L%pt]}return this};const dt={};function mt(g,u,f){dt[g]=class extends f{constructor(){super(),Object.defineProperty(this,"message",{value:u.apply(this,arguments),writable:!0,configurable:!0}),this.name=`${this.name} [${g}]`,this.stack,delete this.name}get code(){return g}set code(T){Object.defineProperty(this,"code",{configurable:!0,enumerable:!0,value:T,writable:!0})}toString(){return`${this.name} [${g}]: ${this.message}`}}}mt("ERR_BUFFER_OUT_OF_BOUNDS",function(g){return g?`${g} is outside of buffer bounds`:"Attempt to access memory outside buffer bounds"},RangeError),mt("ERR_INVALID_ARG_TYPE",function(g,u){return`The "${g}" argument must be of type number. Received type ${typeof u}`},TypeError),mt("ERR_OUT_OF_RANGE",function(g,u,f){let x=`The value of "${g}" is out of range.`,T=f;return Number.isInteger(f)&&Math.abs(f)>2**32?T=Ke(String(f)):typeof f=="bigint"&&(T=String(f),(f>BigInt(2)**BigInt(32)||f<-(BigInt(2)**BigInt(32)))&&(T=Ke(T)),T+="n"),x+=` It must be ${u}. Received ${T}`,x},RangeError);function Ke(g){let u="",f=g.length;const x=g[0]==="-"?1:0;for(;f>=x+4;f-=3)u=`_${g.slice(f-3,f)}${u}`;return`${g.slice(0,f)}${u}`}function kt(g,u,f){ur(u,"offset"),(g[u]===void 0||g[u+f]===void 0)&&Rt(u,g.length-(f+1))}function Ft(g,u,f,x,T,L){if(g>f||g<u){const D=typeof u=="bigint"?"n":"";let pt;throw u===0||u===BigInt(0)?pt=`>= 0${D} and < 2${D} ** ${(L+1)*8}${D}`:pt=`>= -(2${D} ** ${(L+1)*8-1}${D}) and < 2 ** ${(L+1)*8-1}${D}`,new dt.ERR_OUT_OF_RANGE("value",pt,g)}kt(x,T,L)}function ur(g,u){if(typeof g!="number")throw new dt.ERR_INVALID_ARG_TYPE(u,"number",g)}function Rt(g,u,f){throw Math.floor(g)!==g?(ur(g,f),new dt.ERR_OUT_OF_RANGE("offset","an integer",g)):u<0?new dt.ERR_BUFFER_OUT_OF_BOUNDS:new dt.ERR_OUT_OF_RANGE("offset",`>= 0 and <= ${u}`,g)}const $t=/[^+/0-9A-Za-z-_]/g;function Un(g){if(g=g.split("=")[0],g=g.trim().replace($t,""),g.length<2)return"";for(;g.length%4!==0;)g=g+"=";return g}function Ut(g,u){u=u||1/0;let f;const x=g.length;let T=null;const L=[];for(let D=0;D<x;++D){if(f=g.charCodeAt(D),f>55295&&f<57344){if(!T){if(f>56319){(u-=3)>-1&&L.push(239,191,189);continue}else if(D+1===x){(u-=3)>-1&&L.push(239,191,189);continue}T=f;continue}if(f<56320){(u-=3)>-1&&L.push(239,191,189),T=f;continue}f=(T-55296<<10|f-56320)+65536}else T&&(u-=3)>-1&&L.push(239,191,189);if(T=null,f<128){if((u-=1)<0)break;L.push(f)}else if(f<2048){if((u-=2)<0)break;L.push(f>>6|192,f&63|128)}else if(f<65536){if((u-=3)<0)break;L.push(f>>12|224,f>>6&63|128,f&63|128)}else if(f<1114112){if((u-=4)<0)break;L.push(f>>18|240,f>>12&63|128,f>>6&63|128,f&63|128)}else throw new Error("Invalid code point")}return L}function Ht(g){const u=[];for(let f=0;f<g.length;++f)u.push(g.charCodeAt(f)&255);return u}function Nn(g,u){let f,x,T;const L=[];for(let D=0;D<g.length&&!((u-=2)<0);++D)f=g.charCodeAt(D),x=f>>8,T=f%256,L.push(T),L.push(x);return L}function Dt(g){return t.toByteArray(Un(g))}function Lt(g,u,f,x){let T;for(T=0;T<x&&!(T+f>=u.length||T>=g.length);++T)u[T+f]=g[T];return T}function Ne(g,u){return g instanceof u||g!=null&&g.constructor!=null&&g.constructor.name!=null&&g.constructor.name===u.name}function Nt(g){return g!==g}const Vt=function(){const g="0123456789abcdef",u=new Array(256);for(let f=0;f<16;++f){const x=f*16;for(let T=0;T<16;++T)u[x+T]=g[f]+g[T]}return u}();function He(g){return typeof BigInt>"u"?jt:g}function jt(){throw new Error("BigInt not supported")}})(ct);const jr=typeof globalThis=="object"&&"crypto"in globalThis?globalThis.crypto:void 0;/*! noble-hashes - MIT License (c) 2022 Paul Miller (paulmillr.com) */function ui(n){return n instanceof Uint8Array||ArrayBuffer.isView(n)&&n.constructor.name==="Uint8Array"}function xn(n){if(!Number.isSafeInteger(n)||n<0)throw new Error("positive integer expected, got "+n)}function me(n,...t){if(!ui(n))throw new Error("Uint8Array expected");if(t.length>0&&!t.includes(n.length))throw new Error("Uint8Array expected of length "+t+", got length="+n.length)}function hu(n){if(typeof n!="function"||typeof n.create!="function")throw new Error("Hash should be wrapped by utils.createHasher");xn(n.outputLen),xn(n.blockLen)}function nn(n,t=!0){if(n.destroyed)throw new Error("Hash instance has been destroyed");if(t&&n.finished)throw new Error("Hash#digest() has already been called")}function ha(n,t){me(n);const e=t.outputLen;if(n.length<e)throw new Error("digestInto() expects output buffer of length at least "+e)}function du(n){return new Uint32Array(n.buffer,n.byteOffset,Math.floor(n.byteLength/4))}function Ar(...n){for(let t=0;t<n.length;t++)n[t].fill(0)}function Vi(n){return new DataView(n.buffer,n.byteOffset,n.byteLength)}function Ye(n,t){return n<<32-t|n>>>t}const pu=new Uint8Array(new Uint32Array([287454020]).buffer)[0]===68;function gu(n){return n<<24&4278190080|n<<8&16711680|n>>>8&65280|n>>>24&255}function yu(n){for(let t=0;t<n.length;t++)n[t]=gu(n[t]);return n}const Eo=pu?n=>n:yu,da=typeof Uint8Array.from([]).toHex=="function"&&typeof Uint8Array.fromHex=="function",mu=Array.from({length:256},(n,t)=>t.toString(16).padStart(2,"0"));function Or(n){if(me(n),da)return n.toHex();let t="";for(let e=0;e<n.length;e++)t+=mu[n[e]];return t}const fr={_0:48,_9:57,A:65,F:70,a:97,f:102};function _o(n){if(n>=fr._0&&n<=fr._9)return n-fr._0;if(n>=fr.A&&n<=fr.F)return n-(fr.A-10);if(n>=fr.a&&n<=fr.f)return n-(fr.a-10)}function Bs(n){if(typeof n!="string")throw new Error("hex string expected, got "+typeof n);if(da)return Uint8Array.fromHex(n);const t=n.length,e=t/2;if(t%2)throw new Error("hex string expected, got unpadded hex of length "+t);const r=new Uint8Array(e);for(let s=0,i=0;s<e;s++,i+=2){const o=_o(n.charCodeAt(i)),a=_o(n.charCodeAt(i+1));if(o===void 0||a===void 0){const h=n[i]+n[i+1];throw new Error('hex string expected, got non-hex character "'+h+'" at index '+i)}r[s]=o*16+a}return r}function un(n){if(typeof n!="string")throw new Error("string expected");return new Uint8Array(new TextEncoder().encode(n))}function fi(n){return typeof n=="string"&&(n=un(n)),me(n),n}function Re(...n){let t=0;for(let r=0;r<n.length;r++){const s=n[r];me(s),t+=s.length}const e=new Uint8Array(t);for(let r=0,s=0;r<n.length;r++){const i=n[r];e.set(i,s),s+=i.length}return e}class Rs{}function Ms(n){const t=r=>n().update(fi(r)).digest(),e=n();return t.outputLen=e.outputLen,t.blockLen=e.blockLen,t.create=()=>n(),t}function Ts(n=32){if(jr&&typeof jr.getRandomValues=="function")return jr.getRandomValues(new Uint8Array(n));if(jr&&typeof jr.randomBytes=="function")return Uint8Array.from(jr.randomBytes(n));throw new Error("crypto.getRandomValues must be defined")}function wu(n,t,e,r){if(typeof n.setBigUint64=="function")return n.setBigUint64(t,e,r);const s=BigInt(32),i=BigInt(4294967295),o=Number(e>>s&i),a=Number(e&i),h=r?4:0,y=r?0:4;n.setUint32(t+h,o,r),n.setUint32(t+y,a,r)}function bu(n,t,e){return n&t^~n&e}function vu(n,t,e){return n&t^n&e^t&e}class pa extends Rs{constructor(t,e,r,s){super(),this.finished=!1,this.length=0,this.pos=0,this.destroyed=!1,this.blockLen=t,this.outputLen=e,this.padOffset=r,this.isLE=s,this.buffer=new Uint8Array(t),this.view=Vi(this.buffer)}update(t){nn(this),t=fi(t),me(t);const{view:e,buffer:r,blockLen:s}=this,i=t.length;for(let o=0;o<i;){const a=Math.min(s-this.pos,i-o);if(a===s){const h=Vi(t);for(;s<=i-o;o+=s)this.process(h,o);continue}r.set(t.subarray(o,o+a),this.pos),this.pos+=a,o+=a,this.pos===s&&(this.process(e,0),this.pos=0)}return this.length+=t.length,this.roundClean(),this}digestInto(t){nn(this),ha(t,this),this.finished=!0;const{buffer:e,view:r,blockLen:s,isLE:i}=this;let{pos:o}=this;e[o++]=128,Ar(this.buffer.subarray(o)),this.padOffset>s-o&&(this.process(r,0),o=0);for(let E=o;E<s;E++)e[E]=0;wu(r,s-8,BigInt(this.length*8),i),this.process(r,0);const a=Vi(t),h=this.outputLen;if(h%4)throw new Error("_sha2: outputLen should be aligned to 32bit");const y=h/4,m=this.get();if(y>m.length)throw new Error("_sha2: outputLen bigger than state");for(let E=0;E<y;E++)a.setUint32(4*E,m[E],i)}digest(){const{buffer:t,outputLen:e}=this;this.digestInto(t);const r=t.slice(0,e);return this.destroy(),r}_cloneInto(t){t||(t=new this.constructor),t.set(...this.get());const{blockLen:e,buffer:r,length:s,finished:i,destroyed:o,pos:a}=this;return t.destroyed=o,t.finished=i,t.length=s,t.pos=a,s%e&&t.buffer.set(r),t}clone(){return this._cloneInto()}}const yr=Uint32Array.from([1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225]),Ee=Uint32Array.from([1779033703,4089235720,3144134277,2227873595,1013904242,4271175723,2773480762,1595750129,1359893119,2917565137,2600822924,725511199,528734635,4215389547,1541459225,327033209]),zn=BigInt(2**32-1),Io=BigInt(32);function ku(n,t=!1){return t?{h:Number(n&zn),l:Number(n>>Io&zn)}:{h:Number(n>>Io&zn)|0,l:Number(n&zn)|0}}function ga(n,t=!1){const e=n.length;let r=new Uint32Array(e),s=new Uint32Array(e);for(let i=0;i<e;i++){const{h:o,l:a}=ku(n[i],t);[r[i],s[i]]=[o,a]}return[r,s]}const Ao=(n,t,e)=>n>>>e,Bo=(n,t,e)=>n<<32-e|t>>>e,Gr=(n,t,e)=>n>>>e|t<<32-e,Zr=(n,t,e)=>n<<32-e|t>>>e,Fn=(n,t,e)=>n<<64-e|t>>>e-32,Dn=(n,t,e)=>n>>>e-32|t<<64-e,xu=(n,t,e)=>n<<e|t>>>32-e,Su=(n,t,e)=>t<<e|n>>>32-e,Eu=(n,t,e)=>t<<e-32|n>>>64-e,_u=(n,t,e)=>n<<e-32|t>>>64-e;function lr(n,t,e,r){const s=(t>>>0)+(r>>>0);return{h:n+e+(s/2**32|0)|0,l:s|0}}const Iu=(n,t,e)=>(n>>>0)+(t>>>0)+(e>>>0),Au=(n,t,e,r)=>t+e+r+(n/2**32|0)|0,Bu=(n,t,e,r)=>(n>>>0)+(t>>>0)+(e>>>0)+(r>>>0),Ru=(n,t,e,r,s)=>t+e+r+s+(n/2**32|0)|0,Mu=(n,t,e,r,s)=>(n>>>0)+(t>>>0)+(e>>>0)+(r>>>0)+(s>>>0),Tu=(n,t,e,r,s,i)=>t+e+r+s+i+(n/2**32|0)|0,Pu=Uint32Array.from([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]),mr=new Uint32Array(64);class Lu extends pa{constructor(t=32){super(64,t,8,!1),this.A=yr[0]|0,this.B=yr[1]|0,this.C=yr[2]|0,this.D=yr[3]|0,this.E=yr[4]|0,this.F=yr[5]|0,this.G=yr[6]|0,this.H=yr[7]|0}get(){const{A:t,B:e,C:r,D:s,E:i,F:o,G:a,H:h}=this;return[t,e,r,s,i,o,a,h]}set(t,e,r,s,i,o,a,h){this.A=t|0,this.B=e|0,this.C=r|0,this.D=s|0,this.E=i|0,this.F=o|0,this.G=a|0,this.H=h|0}process(t,e){for(let E=0;E<16;E++,e+=4)mr[E]=t.getUint32(e,!1);for(let E=16;E<64;E++){const M=mr[E-15],k=mr[E-2],R=Ye(M,7)^Ye(M,18)^M>>>3,B=Ye(k,17)^Ye(k,19)^k>>>10;mr[E]=B+mr[E-7]+R+mr[E-16]|0}let{A:r,B:s,C:i,D:o,E:a,F:h,G:y,H:m}=this;for(let E=0;E<64;E++){const M=Ye(a,6)^Ye(a,11)^Ye(a,25),k=m+M+bu(a,h,y)+Pu[E]+mr[E]|0,B=(Ye(r,2)^Ye(r,13)^Ye(r,22))+vu(r,s,i)|0;m=y,y=h,h=a,a=o+k|0,o=i,i=s,s=r,r=k+B|0}r=r+this.A|0,s=s+this.B|0,i=i+this.C|0,o=o+this.D|0,a=a+this.E|0,h=h+this.F|0,y=y+this.G|0,m=m+this.H|0,this.set(r,s,i,o,a,h,y,m)}roundClean(){Ar(mr)}destroy(){this.set(0,0,0,0,0,0,0,0),Ar(this.buffer)}}const ya=ga(["0x428a2f98d728ae22","0x7137449123ef65cd","0xb5c0fbcfec4d3b2f","0xe9b5dba58189dbbc","0x3956c25bf348b538","0x59f111f1b605d019","0x923f82a4af194f9b","0xab1c5ed5da6d8118","0xd807aa98a3030242","0x12835b0145706fbe","0x243185be4ee4b28c","0x550c7dc3d5ffb4e2","0x72be5d74f27b896f","0x80deb1fe3b1696b1","0x9bdc06a725c71235","0xc19bf174cf692694","0xe49b69c19ef14ad2","0xefbe4786384f25e3","0x0fc19dc68b8cd5b5","0x240ca1cc77ac9c65","0x2de92c6f592b0275","0x4a7484aa6ea6e483","0x5cb0a9dcbd41fbd4","0x76f988da831153b5","0x983e5152ee66dfab","0xa831c66d2db43210","0xb00327c898fb213f","0xbf597fc7beef0ee4","0xc6e00bf33da88fc2","0xd5a79147930aa725","0x06ca6351e003826f","0x142929670a0e6e70","0x27b70a8546d22ffc","0x2e1b21385c26c926","0x4d2c6dfc5ac42aed","0x53380d139d95b3df","0x650a73548baf63de","0x766a0abb3c77b2a8","0x81c2c92e47edaee6","0x92722c851482353b","0xa2bfe8a14cf10364","0xa81a664bbc423001","0xc24b8b70d0f89791","0xc76c51a30654be30","0xd192e819d6ef5218","0xd69906245565a910","0xf40e35855771202a","0x106aa07032bbd1b8","0x19a4c116b8d2d0c8","0x1e376c085141ab53","0x2748774cdf8eeb99","0x34b0bcb5e19b48a8","0x391c0cb3c5c95a63","0x4ed8aa4ae3418acb","0x5b9cca4f7763e373","0x682e6ff3d6b2b8a3","0x748f82ee5defb2fc","0x78a5636f43172f60","0x84c87814a1f0ab72","0x8cc702081a6439ec","0x90befffa23631e28","0xa4506cebde82bde9","0xbef9a3f7b2c67915","0xc67178f2e372532b","0xca273eceea26619c","0xd186b8c721c0c207","0xeada7dd6cde0eb1e","0xf57d4f7fee6ed178","0x06f067aa72176fba","0x0a637dc5a2c898a6","0x113f9804bef90dae","0x1b710b35131c471b","0x28db77f523047d84","0x32caab7b40c72493","0x3c9ebe0a15c9bebc","0x431d67c49c100d4c","0x4cc5d4becb3e42b6","0x597f299cfc657e2a","0x5fcb6fab3ad6faec","0x6c44198c4a475817"].map(n=>BigInt(n))),Cu=ya[0],Ou=ya[1],wr=new Uint32Array(80),br=new Uint32Array(80);class Uu extends pa{constructor(t=64){super(128,t,16,!1),this.Ah=Ee[0]|0,this.Al=Ee[1]|0,this.Bh=Ee[2]|0,this.Bl=Ee[3]|0,this.Ch=Ee[4]|0,this.Cl=Ee[5]|0,this.Dh=Ee[6]|0,this.Dl=Ee[7]|0,this.Eh=Ee[8]|0,this.El=Ee[9]|0,this.Fh=Ee[10]|0,this.Fl=Ee[11]|0,this.Gh=Ee[12]|0,this.Gl=Ee[13]|0,this.Hh=Ee[14]|0,this.Hl=Ee[15]|0}get(){const{Ah:t,Al:e,Bh:r,Bl:s,Ch:i,Cl:o,Dh:a,Dl:h,Eh:y,El:m,Fh:E,Fl:M,Gh:k,Gl:R,Hh:B,Hl:I}=this;return[t,e,r,s,i,o,a,h,y,m,E,M,k,R,B,I]}set(t,e,r,s,i,o,a,h,y,m,E,M,k,R,B,I){this.Ah=t|0,this.Al=e|0,this.Bh=r|0,this.Bl=s|0,this.Ch=i|0,this.Cl=o|0,this.Dh=a|0,this.Dl=h|0,this.Eh=y|0,this.El=m|0,this.Fh=E|0,this.Fl=M|0,this.Gh=k|0,this.Gl=R|0,this.Hh=B|0,this.Hl=I|0}process(t,e){for(let q=0;q<16;q++,e+=4)wr[q]=t.getUint32(e),br[q]=t.getUint32(e+=4);for(let q=16;q<80;q++){const V=wr[q-15]|0,F=br[q-15]|0,j=Gr(V,F,1)^Gr(V,F,8)^Ao(V,F,7),G=Zr(V,F,1)^Zr(V,F,8)^Bo(V,F,7),Q=wr[q-2]|0,U=br[q-2]|0,$=Gr(Q,U,19)^Fn(Q,U,61)^Ao(Q,U,6),z=Zr(Q,U,19)^Dn(Q,U,61)^Bo(Q,U,6),W=Bu(G,z,br[q-7],br[q-16]),b=Ru(W,j,$,wr[q-7],wr[q-16]);wr[q]=b|0,br[q]=W|0}let{Ah:r,Al:s,Bh:i,Bl:o,Ch:a,Cl:h,Dh:y,Dl:m,Eh:E,El:M,Fh:k,Fl:R,Gh:B,Gl:I,Hh:C,Hl:K}=this;for(let q=0;q<80;q++){const V=Gr(E,M,14)^Gr(E,M,18)^Fn(E,M,41),F=Zr(E,M,14)^Zr(E,M,18)^Dn(E,M,41),j=E&k^~E&B,G=M&R^~M&I,Q=Mu(K,F,G,Ou[q],br[q]),U=Tu(Q,C,V,j,Cu[q],wr[q]),$=Q|0,z=Gr(r,s,28)^Fn(r,s,34)^Fn(r,s,39),W=Zr(r,s,28)^Dn(r,s,34)^Dn(r,s,39),b=r&i^r&a^i&a,c=s&o^s&h^o&h;C=B|0,K=I|0,B=k|0,I=R|0,k=E|0,R=M|0,{h:E,l:M}=lr(y|0,m|0,U|0,$|0),y=a|0,m=h|0,a=i|0,h=o|0,i=r|0,o=s|0;const d=Iu($,W,c);r=Au(d,U,z,b),s=d|0}({h:r,l:s}=lr(this.Ah|0,this.Al|0,r|0,s|0)),{h:i,l:o}=lr(this.Bh|0,this.Bl|0,i|0,o|0),{h:a,l:h}=lr(this.Ch|0,this.Cl|0,a|0,h|0),{h:y,l:m}=lr(this.Dh|0,this.Dl|0,y|0,m|0),{h:E,l:M}=lr(this.Eh|0,this.El|0,E|0,M|0),{h:k,l:R}=lr(this.Fh|0,this.Fl|0,k|0,R|0),{h:B,l:I}=lr(this.Gh|0,this.Gl|0,B|0,I|0),{h:C,l:K}=lr(this.Hh|0,this.Hl|0,C|0,K|0),this.set(r,s,i,o,a,h,y,m,E,M,k,R,B,I,C,K)}roundClean(){Ar(wr,br)}destroy(){Ar(this.buffer),this.set(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)}}const ma=Ms(()=>new Lu),li=Ms(()=>new Uu);/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */const Ps=BigInt(0),ds=BigInt(1);function Ur(n,t){if(typeof t!="boolean")throw new Error(n+" boolean expected, got "+t)}function Kn(n){const t=n.toString(16);return t.length&1?"0"+t:t}function wa(n){if(typeof n!="string")throw new Error("hex string expected, got "+typeof n);return n===""?Ps:BigInt("0x"+n)}function Mn(n){return wa(Or(n))}function Br(n){return me(n),wa(Or(Uint8Array.from(n).reverse()))}function Ls(n,t){return Bs(n.toString(16).padStart(t*2,"0"))}function Nr(n,t){return Ls(n,t).reverse()}function Pt(n,t,e){let r;if(typeof t=="string")try{r=Bs(t)}catch(i){throw new Error(n+" must be hex string or Uint8Array, cause: "+i)}else if(ui(t))r=Uint8Array.from(t);else throw new Error(n+" must be hex string or Uint8Array");const s=r.length;if(typeof e=="number"&&s!==e)throw new Error(n+" of length "+e+" expected, got "+s);return r}function Nu(n,t){if(n.length!==t.length)return!1;let e=0;for(let r=0;r<n.length;r++)e|=n[r]^t[r];return e===0}const ji=n=>typeof n=="bigint"&&Ps<=n;function zu(n,t,e){return ji(n)&&ji(t)&&ji(e)&&t<=n&&n<e}function xr(n,t,e,r){if(!zu(t,e,r))throw new Error("expected valid "+n+": "+e+" <= n < "+r+", got "+t)}function Fu(n){let t;for(t=0;n>Ps;n>>=ds,t+=1);return t}const hi=n=>(ds<<BigInt(n))-ds;function Du(n,t,e){if(typeof n!="number"||n<2)throw new Error("hashLen must be a number");if(typeof t!="number"||t<2)throw new Error("qByteLen must be a number");if(typeof e!="function")throw new Error("hmacFn must be a function");const r=k=>new Uint8Array(k),s=k=>Uint8Array.of(k);let i=r(n),o=r(n),a=0;const h=()=>{i.fill(1),o.fill(0),a=0},y=(...k)=>e(o,i,...k),m=(k=r(0))=>{o=y(s(0),k),i=y(),k.length!==0&&(o=y(s(1),k),i=y())},E=()=>{if(a++>=1e3)throw new Error("drbg: tried 1000 values");let k=0;const R=[];for(;k<t;){i=y();const B=i.slice();R.push(B),k+=i.length}return Re(...R)};return(k,R)=>{h(),m(k);let B;for(;!(B=R(E()));)m();return h(),B}}function Ku(n){return typeof n=="function"&&Number.isSafeInteger(n.outputLen)}function Dr(n,t,e={}){if(!n||typeof n!="object")throw new Error("expected valid options object");function r(s,i,o){const a=n[s];if(o&&a===void 0)return;const h=typeof a;if(h!==i||a===null)throw new Error(`param "${s}" is invalid: expected ${i}, got ${h}`)}Object.entries(t).forEach(([s,i])=>r(s,i,!1)),Object.entries(e).forEach(([s,i])=>r(s,i,!0))}function Yn(n){const t=new WeakMap;return(e,...r)=>{const s=t.get(e);if(s!==void 0)return s;const i=n(e,...r);return t.set(e,i),i}}/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */const Ue=BigInt(0),Se=BigInt(1),Pr=BigInt(2),qu=BigInt(3),ba=BigInt(4),va=BigInt(5),ka=BigInt(8);function zt(n,t){const e=n%t;return e>=Ue?e:t+e}function Kt(n,t,e){let r=n;for(;t-- >Ue;)r*=r,r%=e;return r}function Ro(n,t){if(n===Ue)throw new Error("invert: expected non-zero number");if(t<=Ue)throw new Error("invert: expected positive modulus, got "+t);let e=zt(n,t),r=t,s=Ue,i=Se;for(;e!==Ue;){const a=r/e,h=r%e,y=s-i*a;r=e,e=h,s=i,i=y}if(r!==Se)throw new Error("invert: does not exist");return zt(s,t)}function xa(n,t){const e=(n.ORDER+Se)/ba,r=n.pow(t,e);if(!n.eql(n.sqr(r),t))throw new Error("Cannot find square root");return r}function Wu(n,t){const e=(n.ORDER-va)/ka,r=n.mul(t,Pr),s=n.pow(r,e),i=n.mul(t,s),o=n.mul(n.mul(i,Pr),s),a=n.mul(i,n.sub(o,n.ONE));if(!n.eql(n.sqr(a),t))throw new Error("Cannot find square root");return a}function $u(n){if(n<BigInt(3))throw new Error("sqrt is not defined for small field");let t=n-Se,e=0;for(;t%Pr===Ue;)t/=Pr,e++;let r=Pr;const s=Kr(n);for(;Mo(s,r)===1;)if(r++>1e3)throw new Error("Cannot find square root: probably non-prime P");if(e===1)return xa;let i=s.pow(r,t);const o=(t+Se)/Pr;return function(h,y){if(h.is0(y))return y;if(Mo(h,y)!==1)throw new Error("Cannot find square root");let m=e,E=h.mul(h.ONE,i),M=h.pow(y,t),k=h.pow(y,o);for(;!h.eql(M,h.ONE);){if(h.is0(M))return h.ZERO;let R=1,B=h.sqr(M);for(;!h.eql(B,h.ONE);)if(R++,B=h.sqr(B),R===m)throw new Error("Cannot find square root");const I=Se<<BigInt(m-R-1),C=h.pow(E,I);m=R,E=h.sqr(C),M=h.mul(M,E),k=h.mul(k,C)}return k}}function Hu(n){return n%ba===qu?xa:n%ka===va?Wu:$u(n)}const vr=(n,t)=>(zt(n,t)&Se)===Se,Vu=["create","isValid","is0","neg","inv","sqrt","sqr","eql","add","sub","mul","pow","div","addN","subN","mulN","sqrN"];function ju(n){const t={ORDER:"bigint",MASK:"bigint",BYTES:"number",BITS:"number"},e=Vu.reduce((r,s)=>(r[s]="function",r),t);return Dr(n,e),n}function Gu(n,t,e){if(e<Ue)throw new Error("invalid exponent, negatives unsupported");if(e===Ue)return n.ONE;if(e===Se)return t;let r=n.ONE,s=t;for(;e>Ue;)e&Se&&(r=n.mul(r,s)),s=n.sqr(s),e>>=Se;return r}function Cs(n,t,e=!1){const r=new Array(t.length).fill(e?n.ZERO:void 0),s=t.reduce((o,a,h)=>n.is0(a)?o:(r[h]=o,n.mul(o,a)),n.ONE),i=n.inv(s);return t.reduceRight((o,a,h)=>n.is0(a)?o:(r[h]=n.mul(o,r[h]),n.mul(o,a)),i),r}function Mo(n,t){const e=(n.ORDER-Se)/Pr,r=n.pow(t,e),s=n.eql(r,n.ONE),i=n.eql(r,n.ZERO),o=n.eql(r,n.neg(n.ONE));if(!s&&!i&&!o)throw new Error("invalid Legendre symbol result");return s?1:i?0:-1}function Zu(n,t){t!==void 0&&xn(t);const e=t!==void 0?t:n.toString(2).length,r=Math.ceil(e/8);return{nBitLength:e,nByteLength:r}}function Kr(n,t,e=!1,r={}){if(n<=Ue)throw new Error("invalid field: expected ORDER > 0, got "+n);let s,i;if(typeof t=="object"&&t!=null){if(r.sqrt||e)throw new Error("cannot specify opts in two arguments");const m=t;m.BITS&&(s=m.BITS),m.sqrt&&(i=m.sqrt),typeof m.isLE=="boolean"&&(e=m.isLE)}else typeof t=="number"&&(s=t),r.sqrt&&(i=r.sqrt);const{nBitLength:o,nByteLength:a}=Zu(n,s);if(a>2048)throw new Error("invalid field: expected ORDER of <= 2048 bytes");let h;const y=Object.freeze({ORDER:n,isLE:e,BITS:o,BYTES:a,MASK:hi(o),ZERO:Ue,ONE:Se,create:m=>zt(m,n),isValid:m=>{if(typeof m!="bigint")throw new Error("invalid field element: expected bigint, got "+typeof m);return Ue<=m&&m<n},is0:m=>m===Ue,isValidNot0:m=>!y.is0(m)&&y.isValid(m),isOdd:m=>(m&Se)===Se,neg:m=>zt(-m,n),eql:(m,E)=>m===E,sqr:m=>zt(m*m,n),add:(m,E)=>zt(m+E,n),sub:(m,E)=>zt(m-E,n),mul:(m,E)=>zt(m*E,n),pow:(m,E)=>Gu(y,m,E),div:(m,E)=>zt(m*Ro(E,n),n),sqrN:m=>m*m,addN:(m,E)=>m+E,subN:(m,E)=>m-E,mulN:(m,E)=>m*E,inv:m=>Ro(m,n),sqrt:i||(m=>(h||(h=Hu(n)),h(y,m))),toBytes:m=>e?Nr(m,a):Ls(m,a),fromBytes:m=>{if(m.length!==a)throw new Error("Field.fromBytes: expected "+a+" bytes, got "+m.length);return e?Br(m):Mn(m)},invertBatch:m=>Cs(y,m),cmov:(m,E,M)=>M?E:m});return Object.freeze(y)}function Yu(n,t){if(!n.isOdd)throw new Error("Field doesn't have isOdd");const e=n.sqrt(t);return n.isOdd(e)?n.neg(e):e}function Sa(n){if(typeof n!="bigint")throw new Error("field order must be bigint");const t=n.toString(2).length;return Math.ceil(t/8)}function Ea(n){const t=Sa(n);return t+Math.ceil(t/2)}function Ju(n,t,e=!1){const r=n.length,s=Sa(t),i=Ea(t);if(r<16||r<i||r>1024)throw new Error("expected "+i+"-1024 bytes of input, got "+r);const o=e?Br(n):Mn(n),a=zt(o,t-Se)+Se;return e?Nr(a,s):Ls(a,s)}/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */const sn=BigInt(0),Lr=BigInt(1);function wn(n,t){const e=t.negate();return n?e:t}function _a(n,t,e){const r=t==="pz"?o=>o.pz:o=>o.ez,s=Cs(n.Fp,e.map(r));return e.map((o,a)=>o.toAffine(s[a])).map(n.fromAffine)}function Ia(n,t){if(!Number.isSafeInteger(n)||n<=0||n>t)throw new Error("invalid window size, expected [1.."+t+"], got W="+n)}function Gi(n,t){Ia(n,t);const e=Math.ceil(t/n)+1,r=2**(n-1),s=2**n,i=hi(n),o=BigInt(n);return{windows:e,windowSize:r,mask:i,maxNumber:s,shiftBy:o}}function To(n,t,e){const{windowSize:r,mask:s,maxNumber:i,shiftBy:o}=e;let a=Number(n&s),h=n>>o;a>r&&(a-=i,h+=Lr);const y=t*r,m=y+Math.abs(a)-1,E=a===0,M=a<0,k=t%2!==0;return{nextN:h,offset:m,isZero:E,isNeg:M,isNegF:k,offsetF:y}}function Xu(n,t){if(!Array.isArray(n))throw new Error("array expected");n.forEach((e,r)=>{if(!(e instanceof t))throw new Error("invalid point at index "+r)})}function Qu(n,t){if(!Array.isArray(n))throw new Error("array of scalars expected");n.forEach((e,r)=>{if(!t.isValid(e))throw new Error("invalid scalar at index "+r)})}const Zi=new WeakMap,Aa=new WeakMap;function Yi(n){return Aa.get(n)||1}function Po(n){if(n!==sn)throw new Error("invalid wNAF")}function Ba(n,t){return{constTimeNegate:wn,hasPrecomputes(e){return Yi(e)!==1},unsafeLadder(e,r,s=n.ZERO){let i=e;for(;r>sn;)r&Lr&&(s=s.add(i)),i=i.double(),r>>=Lr;return s},precomputeWindow(e,r){const{windows:s,windowSize:i}=Gi(r,t),o=[];let a=e,h=a;for(let y=0;y<s;y++){h=a,o.push(h);for(let m=1;m<i;m++)h=h.add(a),o.push(h);a=h.double()}return o},wNAF(e,r,s){let i=n.ZERO,o=n.BASE;const a=Gi(e,t);for(let h=0;h<a.windows;h++){const{nextN:y,offset:m,isZero:E,isNeg:M,isNegF:k,offsetF:R}=To(s,h,a);s=y,E?o=o.add(wn(k,r[R])):i=i.add(wn(M,r[m]))}return Po(s),{p:i,f:o}},wNAFUnsafe(e,r,s,i=n.ZERO){const o=Gi(e,t);for(let a=0;a<o.windows&&s!==sn;a++){const{nextN:h,offset:y,isZero:m,isNeg:E}=To(s,a,o);if(s=h,!m){const M=r[y];i=i.add(E?M.negate():M)}}return Po(s),i},getPrecomputes(e,r,s){let i=Zi.get(r);return i||(i=this.precomputeWindow(r,e),e!==1&&(typeof s=="function"&&(i=s(i)),Zi.set(r,i))),i},wNAFCached(e,r,s){const i=Yi(e);return this.wNAF(i,this.getPrecomputes(i,e,s),r)},wNAFCachedUnsafe(e,r,s,i){const o=Yi(e);return o===1?this.unsafeLadder(e,r,i):this.wNAFUnsafe(o,this.getPrecomputes(o,e,s),r,i)},setWindowSize(e,r){Ia(r,t),Aa.set(e,r),Zi.delete(e)}}}function tf(n,t,e,r){let s=t,i=n.ZERO,o=n.ZERO;for(;e>sn||r>sn;)e&Lr&&(i=i.add(s)),r&Lr&&(o=o.add(s)),s=s.double(),e>>=Lr,r>>=Lr;return{p1:i,p2:o}}function Os(n,t,e,r){Xu(e,n),Qu(r,t);const s=e.length,i=r.length;if(s!==i)throw new Error("arrays of points and scalars must have equal length");const o=n.ZERO,a=Fu(BigInt(s));let h=1;a>12?h=a-3:a>4?h=a-2:a>0&&(h=2);const y=hi(h),m=new Array(Number(y)+1).fill(o),E=Math.floor((t.BITS-1)/h)*h;let M=o;for(let k=E;k>=0;k-=h){m.fill(o);for(let B=0;B<i;B++){const I=r[B],C=Number(I>>BigInt(k)&y);m[C]=m[C].add(e[B])}let R=o;for(let B=m.length-1,I=o;B>0;B--)I=I.add(m[B]),R=R.add(I);if(M=M.add(R),k!==0)for(let B=0;B<h;B++)M=M.double()}return M}function Lo(n,t){if(t){if(t.ORDER!==n)throw new Error("Field.ORDER must match order: Fp == p, Fn == n");return ju(t),t}else return Kr(n)}function Ra(n,t,e={}){if(!t||typeof t!="object")throw new Error(`expected valid ${n} CURVE object`);for(const a of["p","n","h"]){const h=t[a];if(!(typeof h=="bigint"&&h>sn))throw new Error(`CURVE.${a} must be positive bigint`)}const r=Lo(t.p,e.Fp),s=Lo(t.n,e.Fn),o=["Gx","Gy","a",n==="weierstrass"?"b":"d"];for(const a of o)if(!r.isValid(t[a]))throw new Error(`CURVE.${a} must be valid field element of CURVE.Fp`);return{Fp:r,Fn:s}}/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */const Je=BigInt(0),Me=BigInt(1),Ji=BigInt(2),ef=BigInt(8),rf={zip215:!0};function nf(n,t,e,r){const s=n.sqr(e),i=n.sqr(r),o=n.add(n.mul(t.a,s),i),a=n.add(n.ONE,n.mul(t.d,n.mul(s,i)));return n.eql(o,a)}function sf(n,t={}){const{Fp:e,Fn:r}=Ra("edwards",n,t),{h:s,n:i}=n;Dr(t,{},{uvRatio:"function"});const o=Ji<<BigInt(r.BYTES*8)-Me,a=B=>e.create(B),h=t.uvRatio||((B,I)=>{try{return{isValid:!0,value:e.sqrt(e.div(B,I))}}catch{return{isValid:!1,value:Je}}});if(!nf(e,n,n.Gx,n.Gy))throw new Error("bad curve params: generator point");function y(B,I,C=!1){const K=C?Me:Je;return xr("coordinate "+B,I,K,o),I}function m(B){if(!(B instanceof k))throw new Error("ExtendedPoint expected")}const E=Yn((B,I)=>{const{ex:C,ey:K,ez:q}=B,V=B.is0();I==null&&(I=V?ef:e.inv(q));const F=a(C*I),j=a(K*I),G=a(q*I);if(V)return{x:Je,y:Me};if(G!==Me)throw new Error("invZ was invalid");return{x:F,y:j}}),M=Yn(B=>{const{a:I,d:C}=n;if(B.is0())throw new Error("bad point: ZERO");const{ex:K,ey:q,ez:V,et:F}=B,j=a(K*K),G=a(q*q),Q=a(V*V),U=a(Q*Q),$=a(j*I),z=a(Q*a($+G)),W=a(U+a(C*a(j*G)));if(z!==W)throw new Error("bad point: equation left != right (1)");const b=a(K*q),c=a(V*F);if(b!==c)throw new Error("bad point: equation left != right (2)");return!0});class k{constructor(I,C,K,q){this.ex=y("x",I),this.ey=y("y",C),this.ez=y("z",K,!0),this.et=y("t",q),Object.freeze(this)}get x(){return this.toAffine().x}get y(){return this.toAffine().y}static fromAffine(I){if(I instanceof k)throw new Error("extended point not allowed");const{x:C,y:K}=I||{};return y("x",C),y("y",K),new k(C,K,Me,a(C*K))}static normalizeZ(I){return _a(k,"ez",I)}static msm(I,C){return Os(k,r,I,C)}_setWindowSize(I){this.precompute(I)}precompute(I=8,C=!0){return R.setWindowSize(this,I),C||this.multiply(Ji),this}assertValidity(){M(this)}equals(I){m(I);const{ex:C,ey:K,ez:q}=this,{ex:V,ey:F,ez:j}=I,G=a(C*j),Q=a(V*q),U=a(K*j),$=a(F*q);return G===Q&&U===$}is0(){return this.equals(k.ZERO)}negate(){return new k(a(-this.ex),this.ey,this.ez,a(-this.et))}double(){const{a:I}=n,{ex:C,ey:K,ez:q}=this,V=a(C*C),F=a(K*K),j=a(Ji*a(q*q)),G=a(I*V),Q=C+K,U=a(a(Q*Q)-V-F),$=G+F,z=$-j,W=G-F,b=a(U*z),c=a($*W),d=a(U*W),p=a(z*$);return new k(b,c,p,d)}add(I){m(I);const{a:C,d:K}=n,{ex:q,ey:V,ez:F,et:j}=this,{ex:G,ey:Q,ez:U,et:$}=I,z=a(q*G),W=a(V*Q),b=a(j*K*$),c=a(F*U),d=a((q+V)*(G+Q)-z-W),p=c-b,v=c+b,S=a(W-C*z),A=a(d*p),P=a(v*S),w=a(d*S),l=a(p*v);return new k(A,P,l,w)}subtract(I){return this.add(I.negate())}multiply(I){const C=I;xr("scalar",C,Me,i);const{p:K,f:q}=R.wNAFCached(this,C,k.normalizeZ);return k.normalizeZ([K,q])[0]}multiplyUnsafe(I,C=k.ZERO){const K=I;return xr("scalar",K,Je,i),K===Je?k.ZERO:this.is0()||K===Me?this:R.wNAFCachedUnsafe(this,K,k.normalizeZ,C)}isSmallOrder(){return this.multiplyUnsafe(s).is0()}isTorsionFree(){return R.wNAFCachedUnsafe(this,i).is0()}toAffine(I){return E(this,I)}clearCofactor(){return s===Me?this:this.multiplyUnsafe(s)}static fromBytes(I,C=!1){return me(I),this.fromHex(I,C)}static fromHex(I,C=!1){const{d:K,a:q}=n,V=e.BYTES;I=Pt("pointHex",I,V),Ur("zip215",C);const F=I.slice(),j=I[V-1];F[V-1]=j&-129;const G=Br(F),Q=C?o:e.ORDER;xr("pointHex.y",G,Je,Q);const U=a(G*G),$=a(U-Me),z=a(K*U-q);let{isValid:W,value:b}=h($,z);if(!W)throw new Error("Point.fromHex: invalid y coordinate");const c=(b&Me)===Me,d=(j&128)!==0;if(!C&&b===Je&&d)throw new Error("Point.fromHex: x=0 and x_0=1");return d!==c&&(b=a(-b)),k.fromAffine({x:b,y:G})}static fromPrivateScalar(I){return k.BASE.multiply(I)}toBytes(){const{x:I,y:C}=this.toAffine(),K=Nr(C,e.BYTES);return K[K.length-1]|=I&Me?128:0,K}toRawBytes(){return this.toBytes()}toHex(){return Or(this.toBytes())}toString(){return`<Point ${this.is0()?"ZERO":this.toHex()}>`}}k.BASE=new k(n.Gx,n.Gy,Me,a(n.Gx*n.Gy)),k.ZERO=new k(Je,Me,Me,Je),k.Fp=e,k.Fn=r;const R=Ba(k,r.BYTES*8);return k}function of(n,t){Dr(t,{hash:"function"},{adjustScalarBytes:"function",randomBytes:"function",domain:"function",prehash:"function",mapToCurve:"function"});const{prehash:e,hash:r}=t,{BASE:s,Fp:i,Fn:o}=n,a=o.ORDER,h=t.randomBytes||Ts,y=t.adjustScalarBytes||(F=>F),m=t.domain||((F,j,G)=>{if(Ur("phflag",G),j.length||G)throw new Error("Contexts/pre-hash are not supported");return F});function E(F){return o.create(F)}function M(F){return E(Br(F))}function k(F){const j=i.BYTES;F=Pt("private key",F,j);const G=Pt("hashed private key",r(F),2*j),Q=y(G.slice(0,j)),U=G.slice(j,2*j),$=M(Q);return{head:Q,prefix:U,scalar:$}}function R(F){const{head:j,prefix:G,scalar:Q}=k(F),U=s.multiply(Q),$=U.toBytes();return{head:j,prefix:G,scalar:Q,point:U,pointBytes:$}}function B(F){return R(F).pointBytes}function I(F=Uint8Array.of(),...j){const G=Re(...j);return M(r(m(G,Pt("context",F),!!e)))}function C(F,j,G={}){F=Pt("message",F),e&&(F=e(F));const{prefix:Q,scalar:U,pointBytes:$}=R(j),z=I(G.context,Q,F),W=s.multiply(z).toBytes(),b=I(G.context,W,$,F),c=E(z+b*U);xr("signature.s",c,Je,a);const d=i.BYTES,p=Re(W,Nr(c,d));return Pt("result",p,d*2)}const K=rf;function q(F,j,G,Q=K){const{context:U,zip215:$}=Q,z=i.BYTES;F=Pt("signature",F,2*z),j=Pt("message",j),G=Pt("publicKey",G,z),$!==void 0&&Ur("zip215",$),e&&(j=e(j));const W=Br(F.slice(z,2*z));let b,c,d;try{b=n.fromHex(G,$),c=n.fromHex(F.slice(0,z),$),d=s.multiplyUnsafe(W)}catch{return!1}if(!$&&b.isSmallOrder())return!1;const p=I(U,c.toBytes(),b.toBytes(),j);return c.add(b.multiplyUnsafe(p)).subtract(d).clearCofactor().is0()}return s.precompute(8),{getPublicKey:B,sign:C,verify:q,utils:{getExtendedPublicKey:R,randomPrivateKey:()=>h(i.BYTES),precompute(F=8,j=n.BASE){return j.precompute(F,!1)}},Point:n}}function af(n){const t={a:n.a,d:n.d,p:n.Fp.ORDER,n:n.n,h:n.h,Gx:n.Gx,Gy:n.Gy},e=n.Fp,r=Kr(t.n,n.nBitLength,!0),s={Fp:e,Fn:r,uvRatio:n.uvRatio},i={hash:n.hash,randomBytes:n.randomBytes,adjustScalarBytes:n.adjustScalarBytes,domain:n.domain,prehash:n.prehash,mapToCurve:n.mapToCurve};return{CURVE:t,curveOpts:s,eddsaOpts:i}}function cf(n,t){return Object.assign({},t,{ExtendedPoint:t.Point,CURVE:n})}function Us(n){const{CURVE:t,curveOpts:e,eddsaOpts:r}=af(n),s=sf(t,e),i=of(s,r);return cf(n,i)}const uf=Mn;function kr(n,t){if(Sn(n),Sn(t),n<0||n>=1<<8*t)throw new Error("invalid I2OSP input: "+n);const e=Array.from({length:t}).fill(0);for(let r=t-1;r>=0;r--)e[r]=n&255,n>>>=8;return new Uint8Array(e)}function ff(n,t){const e=new Uint8Array(n.length);for(let r=0;r<n.length;r++)e[r]=n[r]^t[r];return e}function Sn(n){if(!Number.isSafeInteger(n))throw new Error("number expected")}function Ma(n,t,e,r){me(n),me(t),Sn(e),t.length>255&&(t=r(Re(un("H2C-OVERSIZE-DST-"),t)));const{outputLen:s,blockLen:i}=r,o=Math.ceil(e/s);if(e>65535||o>255)throw new Error("expand_message_xmd: invalid lenInBytes");const a=Re(t,kr(t.length,1)),h=kr(0,i),y=kr(e,2),m=new Array(o),E=r(Re(h,n,y,kr(0,1),a));m[0]=r(Re(E,kr(1,1),a));for(let k=1;k<=o;k++){const R=[ff(E,m[k-1]),kr(k+1,1),a];m[k]=r(Re(...R))}return Re(...m).slice(0,e)}function lf(n,t,e,r,s){if(me(n),me(t),Sn(e),t.length>255){const i=Math.ceil(2*r/8);t=s.create({dkLen:i}).update(un("H2C-OVERSIZE-DST-")).update(t).digest()}if(e>65535||t.length>255)throw new Error("expand_message_xof: invalid lenInBytes");return s.create({dkLen:e}).update(n).update(kr(e,2)).update(t).update(kr(t.length,1)).digest()}function Co(n,t,e){Dr(e,{p:"bigint",m:"number",k:"number",hash:"function"});const{p:r,k:s,m:i,hash:o,expand:a,DST:h}=e;if(!ui(h)&&typeof h!="string")throw new Error("DST must be string or uint8array");if(!Ku(e.hash))throw new Error("expected valid hash");me(n),Sn(t);const y=typeof h=="string"?un(h):h,m=r.toString(2).length,E=Math.ceil((m+s)/8),M=t*i*E;let k;if(a==="xmd")k=Ma(n,y,M,o);else if(a==="xof")k=lf(n,y,M,s,o);else if(a==="_internal_pass")k=n;else throw new Error('expand must be "xmd" or "xof"');const R=new Array(t);for(let B=0;B<t;B++){const I=new Array(i);for(let C=0;C<i;C++){const K=E*(C+B*i),q=k.subarray(K,K+E);I[C]=zt(uf(q),r)}R[B]=I}return R}function hf(n,t,e){if(typeof t!="function")throw new Error("mapToCurve() must be defined");function r(i){return n.fromAffine(t(i))}function s(i){const o=i.clearCofactor();return o.equals(n.ZERO)?n.ZERO:(o.assertValidity(),o)}return{defaults:e,hashToCurve(i,o){const a=e.DST?e.DST:{},h=Object.assign({},e,a,o),y=Co(i,2,h),m=r(y[0]),E=r(y[1]);return s(m.add(E))},encodeToCurve(i,o){const a=e.encodeDST?e.encodeDST:{},h=Object.assign({},e,a,o),y=Co(i,1,h);return s(r(y[0]))},mapToCurve(i){if(!Array.isArray(i))throw new Error("expected array of bigints");for(const o of i)if(typeof o!="bigint")throw new Error("expected array of bigints");return s(r(i))}}}/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */const gn=BigInt(0),Yr=BigInt(1),qn=BigInt(2);function df(n){return Dr(n,{adjustScalarBytes:"function",powPminus2:"function"}),Object.freeze({...n})}function pf(n){const t=df(n),{P:e,type:r,adjustScalarBytes:s,powPminus2:i,randomBytes:o}=t,a=r==="x25519";if(!a&&r!=="x448")throw new Error("invalid type");const h=o||Ts,y=a?255:448,m=a?32:56,E=BigInt(a?9:5),M=BigInt(a?121665:39081),k=a?qn**BigInt(254):qn**BigInt(447),R=a?BigInt(8)*qn**BigInt(251)-Yr:BigInt(4)*qn**BigInt(445)-Yr,B=k+R+Yr,I=U=>zt(U,e),C=K(E);function K(U){return Nr(I(U),m)}function q(U){const $=Pt("u coordinate",U,m);return a&&($[31]&=127),I(Br($))}function V(U){return Br(s(Pt("scalar",U,m)))}function F(U,$){const z=Q(q($),V(U));if(z===gn)throw new Error("invalid private or public key received");return K(z)}function j(U){return F(U,C)}function G(U,$,z){const W=I(U*($-z));return $=I($-W),z=I(z+W),{x_2:$,x_3:z}}function Q(U,$){xr("u",U,gn,e),xr("scalar",$,k,B);const z=$,W=U;let b=Yr,c=gn,d=U,p=Yr,v=gn;for(let A=BigInt(y-1);A>=gn;A--){const P=z>>A&Yr;v^=P,{x_2:b,x_3:d}=G(v,b,d),{x_2:c,x_3:p}=G(v,c,p),v=P;const w=b+c,l=I(w*w),_=b-c,nt=I(_*_),J=l-nt,it=d+p,At=d-p,dt=I(At*w),mt=I(it*_),Ke=dt+mt,kt=dt-mt;d=I(Ke*Ke),p=I(W*I(kt*kt)),b=I(l*nt),c=I(J*(l+I(M*J)))}({x_2:b,x_3:d}=G(v,b,d)),{x_2:c,x_3:p}=G(v,c,p);const S=i(c);return I(b*S)}return{scalarMult:F,scalarMultBase:j,getSharedSecret:(U,$)=>F(U,$),getPublicKey:U=>j(U),utils:{randomPrivateKey:()=>h(m)},GuBytes:C.slice()}}/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */const gf=BigInt(0),nr=BigInt(1),Jn=BigInt(2),Ta=BigInt(3),Pa=BigInt(5),Ns=BigInt(8),Tn={p:BigInt("0x7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffed"),n:BigInt("0x1000000000000000000000000000000014def9dea2f79cd65812631a5cf5d3ed"),h:Ns,a:BigInt("0x7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffec"),d:BigInt("0x52036cee2b6ffe738cc740797779e89800700a4d4141d8ab75eb4dca135978a3"),Gx:BigInt("0x216936d3cd6e53fec0a4e231fdd6dc5c692cc7609525a7b2c9562d608f25d51a"),Gy:BigInt("0x6666666666666666666666666666666666666666666666666666666666666658")};function La(n){const t=BigInt(10),e=BigInt(20),r=BigInt(40),s=BigInt(80),i=Tn.p,a=n*n%i*n%i,h=Kt(a,Jn,i)*a%i,y=Kt(h,nr,i)*n%i,m=Kt(y,Pa,i)*y%i,E=Kt(m,t,i)*m%i,M=Kt(E,e,i)*E%i,k=Kt(M,r,i)*M%i,R=Kt(k,s,i)*k%i,B=Kt(R,s,i)*k%i,I=Kt(B,t,i)*m%i;return{pow_p_5_8:Kt(I,Jn,i)*n%i,b2:a}}function Ca(n){return n[0]&=248,n[31]&=127,n[31]|=64,n}const ps=BigInt("19681161376707505956807079304988542015446066515923890162744021073123829784752");function zs(n,t){const e=Tn.p,r=zt(t*t*t,e),s=zt(r*r*t,e),i=La(n*s).pow_p_5_8;let o=zt(n*r*i,e);const a=zt(t*o*o,e),h=o,y=zt(o*ps,e),m=a===n,E=a===zt(-n,e),M=a===zt(-n*ps,e);return m&&(o=h),(E||M)&&(o=y),vr(o,e)&&(o=zt(-o,e)),{isValid:m||E,value:o}}const yf=["0100000000000000000000000000000000000000000000000000000000000000","c7176a703d4dd84fba3c0b760d10670f2a2053fa2c39ccc64ec7fd7792ac037a","0000000000000000000000000000000000000000000000000000000000000080","26e8958fc2b227b045c3f489f2ef98f0d5dfac05d3c63339b13802886d53fc05","ecffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff7f","26e8958fc2b227b045c3f489f2ef98f0d5dfac05d3c63339b13802886d53fc85","0000000000000000000000000000000000000000000000000000000000000000","c7176a703d4dd84fba3c0b760d10670f2a2053fa2c39ccc64ec7fd7792ac03fa"],tt=Kr(Tn.p,void 0,!0),En={...Tn,Fp:tt,hash:li,adjustScalarBytes:Ca,uvRatio:zs},Zt=Us(En);function Oa(n,t,e){if(t.length>255)throw new Error("Context is too big");return Re(un("SigEd25519 no Ed25519 collisions"),new Uint8Array([e?1:0,t.length]),t,n)}const mf=Us({...En,domain:Oa}),wf=Us(Object.assign({},En,{domain:Oa,prehash:li})),bf=(()=>{const n=Tn.p;return pf({P:n,type:"x25519",powPminus2:t=>{const{pow_p_5_8:e,b2:r}=La(t);return zt(Kt(e,Ta,n)*r,n)},adjustScalarBytes:Ca})})();function Ua(n){const t=Pt("pub",n),{y:e}=Zt.Point.fromHex(t),r=BigInt(1);return tt.toBytes(tt.create((r+e)*tt.inv(r-e)))}const vf=Ua;function kf(n){const t=En.hash(n.subarray(0,32));return En.adjustScalarBytes(t).subarray(0,32)}const xf=(tt.ORDER+Ta)/Ns,Sf=tt.pow(Jn,xf),Oo=tt.sqrt(tt.neg(tt.ONE));function Ef(n){const t=(tt.ORDER-Pa)/Ns,e=BigInt(486662);let r=tt.sqr(n);r=tt.mul(r,Jn);let s=tt.add(r,tt.ONE),i=tt.neg(e),o=tt.sqr(s),a=tt.mul(o,s),h=tt.mul(r,e);h=tt.mul(h,i),h=tt.add(h,o),h=tt.mul(h,i);let y=tt.sqr(a);o=tt.sqr(y),y=tt.mul(y,a),y=tt.mul(y,h),o=tt.mul(o,y);let m=tt.pow(o,t);m=tt.mul(m,y);let E=tt.mul(m,Oo);o=tt.sqr(m),o=tt.mul(o,a);let M=tt.eql(o,h),k=tt.cmov(E,m,M),R=tt.mul(i,r),B=tt.mul(m,n);B=tt.mul(B,Sf);let I=tt.mul(B,Oo),C=tt.mul(h,r);o=tt.sqr(B),o=tt.mul(o,a);let K=tt.eql(o,C),q=tt.cmov(I,B,K);o=tt.sqr(k),o=tt.mul(o,a);let V=tt.eql(o,h),F=tt.cmov(R,i,V),j=tt.cmov(q,k,V),G=tt.isOdd(j);return j=tt.cmov(j,tt.neg(j),V!==G),{xMn:F,xMd:s,yMn:j,yMd:nr}}const _f=Yu(tt,tt.neg(BigInt(486664)));function If(n){const{xMn:t,xMd:e,yMn:r,yMd:s}=Ef(n);let i=tt.mul(t,s);i=tt.mul(i,_f);let o=tt.mul(e,r),a=tt.sub(t,e),h=tt.add(t,e),y=tt.mul(o,h),m=tt.eql(y,tt.ZERO);i=tt.cmov(i,tt.ZERO,m),o=tt.cmov(o,tt.ONE,m),a=tt.cmov(a,tt.ONE,m),h=tt.cmov(h,tt.ONE,m);const[E,M]=Cs(tt,[o,h],!0);return{x:tt.mul(i,E),y:tt.mul(a,M)}}const Fs=hf(Zt.Point,n=>If(n[0]),{DST:"edwards25519_XMD:SHA-512_ELL2_RO_",encodeDST:"edwards25519_XMD:SHA-512_ELL2_NU_",p:tt.ORDER,m:1,k:128,expand:"xmd",hash:li}),Af=Fs.hashToCurve,Bf=Fs.encodeToCurve;function Xi(n){if(!(n instanceof pe))throw new Error("RistrettoPoint expected")}const gs=ps,Rf=BigInt("25063068953384623474111414158702152701244531502492656460079210482610430750235"),Mf=BigInt("54469307008909316920995813868745141605393597292927456921205312896311721017578"),Tf=BigInt("1159843021668779879193775521855586647937357759715417654439879720876111806838"),Pf=BigInt("40440834346308536858101042469323190826248399146238708352240133220865137265952"),Uo=n=>zs(nr,n),Lf=BigInt("0x7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff"),Qi=n=>Zt.CURVE.Fp.create(Br(n)&Lf);function No(n){const{d:t}=Zt.CURVE,e=Zt.CURVE.Fp.ORDER,r=Zt.CURVE.Fp.create,s=r(gs*n*n),i=r((s+nr)*Tf);let o=BigInt(-1);const a=r((o-t*s)*r(s+t));let{isValid:h,value:y}=zs(i,a),m=r(y*n);vr(m,e)||(m=r(-m)),h||(y=m),h||(o=s);const E=r(o*(s-nr)*Pf-a),M=y*y,k=r((y+y)*a),R=r(E*Rf),B=r(nr-M),I=r(nr+M);return new Zt.Point(r(k*I),r(B*R),r(R*I),r(k*B))}class pe{constructor(t){this.ep=t}static fromAffine(t){return new pe(Zt.Point.fromAffine(t))}static hashToCurve(t){t=Pt("ristrettoHash",t,64);const e=Qi(t.slice(0,32)),r=No(e),s=Qi(t.slice(32,64)),i=No(s);return new pe(r.add(i))}static fromBytes(t){return me(t),this.fromHex(t)}static fromHex(t){t=Pt("ristrettoHex",t,32);const{a:e,d:r}=Zt.CURVE,s=tt.ORDER,i=tt.create,o="RistrettoPoint.fromHex: the hex is not valid encoding of RistrettoPoint",a=Qi(t);if(!Nu(Nr(a,32),t)||vr(a,s))throw new Error(o);const h=i(a*a),y=i(nr+e*h),m=i(nr-e*h),E=i(y*y),M=i(m*m),k=i(e*r*E-M),{isValid:R,value:B}=Uo(i(k*M)),I=i(B*m),C=i(B*I*k);let K=i((a+a)*I);vr(K,s)&&(K=i(-K));const q=i(y*C),V=i(K*q);if(!R||vr(V,s)||q===gf)throw new Error(o);return new pe(new Zt.Point(K,q,nr,V))}static msm(t,e){const r=Kr(Zt.CURVE.n,Zt.CURVE.nBitLength);return Os(pe,r,t,e)}toBytes(){let{ex:t,ey:e,ez:r,et:s}=this.ep;const i=tt.ORDER,o=tt.create,a=o(o(r+e)*o(r-e)),h=o(t*e),y=o(h*h),{value:m}=Uo(o(a*y)),E=o(m*a),M=o(m*h),k=o(E*M*s);let R;if(vr(s*k,i)){let I=o(e*gs),C=o(t*gs);t=I,e=C,R=o(E*Mf)}else R=M;vr(t*k,i)&&(e=o(-e));let B=o((r-e)*R);return vr(B,i)&&(B=o(-B)),Nr(B,32)}toRawBytes(){return this.toBytes()}toHex(){return Or(this.toBytes())}toString(){return this.toHex()}equals(t){Xi(t);const{ex:e,ey:r}=this.ep,{ex:s,ey:i}=t.ep,o=tt.create,a=o(e*i)===o(r*s),h=o(r*i)===o(e*s);return a||h}add(t){return Xi(t),new pe(this.ep.add(t.ep))}subtract(t){return Xi(t),new pe(this.ep.subtract(t.ep))}multiply(t){return new pe(this.ep.multiply(t))}multiplyUnsafe(t){return new pe(this.ep.multiplyUnsafe(t))}double(){return new pe(this.ep.double())}negate(){return new pe(this.ep.negate())}}const Cf=(pe.BASE||(pe.BASE=new pe(Zt.Point.BASE)),pe.ZERO||(pe.ZERO=new pe(Zt.Point.ZERO)),pe),Na=(n,t)=>{const e=t.DST,r=typeof e=="string"?un(e):e,s=Ma(n,r,64,li);return pe.hashToCurve(s)},Of=Na,np=Object.freeze(Object.defineProperty({__proto__:null,ED25519_TORSION_SUBGROUP:yf,RistrettoPoint:Cf,ed25519:Zt,ed25519_hasher:Fs,ed25519ctx:mf,ed25519ph:wf,edwardsToMontgomery:vf,edwardsToMontgomeryPriv:kf,edwardsToMontgomeryPub:Ua,encodeToCurve:Bf,hashToCurve:Af,hashToRistretto255:Na,hash_to_ristretto255:Of,x25519:bf},Symbol.toStringTag,{value:"Module"}));var Ds={exports:{}};const Uf={},Nf=Object.freeze(Object.defineProperty({__proto__:null,default:Uf},Symbol.toStringTag,{value:"Module"})),zf=Is(Nf);Ds.exports;(function(n){(function(t,e){function r(b,c){if(!b)throw new Error(c||"Assertion failed")}function s(b,c){b.super_=c;var d=function(){};d.prototype=c.prototype,b.prototype=new d,b.prototype.constructor=b}function i(b,c,d){if(i.isBN(b))return b;this.negative=0,this.words=null,this.length=0,this.red=null,b!==null&&((c==="le"||c==="be")&&(d=c,c=10),this._init(b||0,c||10,d||"be"))}typeof t=="object"?t.exports=i:e.BN=i,i.BN=i,i.wordSize=26;var o;try{typeof window<"u"&&typeof window.Buffer<"u"?o=window.Buffer:o=zf.Buffer}catch{}i.isBN=function(c){return c instanceof i?!0:c!==null&&typeof c=="object"&&c.constructor.wordSize===i.wordSize&&Array.isArray(c.words)},i.max=function(c,d){return c.cmp(d)>0?c:d},i.min=function(c,d){return c.cmp(d)<0?c:d},i.prototype._init=function(c,d,p){if(typeof c=="number")return this._initNumber(c,d,p);if(typeof c=="object")return this._initArray(c,d,p);d==="hex"&&(d=16),r(d===(d|0)&&d>=2&&d<=36),c=c.toString().replace(/\s+/g,"");var v=0;c[0]==="-"&&(v++,this.negative=1),v<c.length&&(d===16?this._parseHex(c,v,p):(this._parseBase(c,d,v),p==="le"&&this._initArray(this.toArray(),d,p)))},i.prototype._initNumber=function(c,d,p){c<0&&(this.negative=1,c=-c),c<67108864?(this.words=[c&67108863],this.length=1):c<4503599627370496?(this.words=[c&67108863,c/67108864&67108863],this.length=2):(r(c<9007199254740992),this.words=[c&67108863,c/67108864&67108863,1],this.length=3),p==="le"&&this._initArray(this.toArray(),d,p)},i.prototype._initArray=function(c,d,p){if(r(typeof c.length=="number"),c.length<=0)return this.words=[0],this.length=1,this;this.length=Math.ceil(c.length/3),this.words=new Array(this.length);for(var v=0;v<this.length;v++)this.words[v]=0;var S,A,P=0;if(p==="be")for(v=c.length-1,S=0;v>=0;v-=3)A=c[v]|c[v-1]<<8|c[v-2]<<16,this.words[S]|=A<<P&67108863,this.words[S+1]=A>>>26-P&67108863,P+=24,P>=26&&(P-=26,S++);else if(p==="le")for(v=0,S=0;v<c.length;v+=3)A=c[v]|c[v+1]<<8|c[v+2]<<16,this.words[S]|=A<<P&67108863,this.words[S+1]=A>>>26-P&67108863,P+=24,P>=26&&(P-=26,S++);return this._strip()};function a(b,c){var d=b.charCodeAt(c);if(d>=48&&d<=57)return d-48;if(d>=65&&d<=70)return d-55;if(d>=97&&d<=102)return d-87;r(!1,"Invalid character in "+b)}function h(b,c,d){var p=a(b,d);return d-1>=c&&(p|=a(b,d-1)<<4),p}i.prototype._parseHex=function(c,d,p){this.length=Math.ceil((c.length-d)/6),this.words=new Array(this.length);for(var v=0;v<this.length;v++)this.words[v]=0;var S=0,A=0,P;if(p==="be")for(v=c.length-1;v>=d;v-=2)P=h(c,d,v)<<S,this.words[A]|=P&67108863,S>=18?(S-=18,A+=1,this.words[A]|=P>>>26):S+=8;else{var w=c.length-d;for(v=w%2===0?d+1:d;v<c.length;v+=2)P=h(c,d,v)<<S,this.words[A]|=P&67108863,S>=18?(S-=18,A+=1,this.words[A]|=P>>>26):S+=8}this._strip()};function y(b,c,d,p){for(var v=0,S=0,A=Math.min(b.length,d),P=c;P<A;P++){var w=b.charCodeAt(P)-48;v*=p,w>=49?S=w-49+10:w>=17?S=w-17+10:S=w,r(w>=0&&S<p,"Invalid character"),v+=S}return v}i.prototype._parseBase=function(c,d,p){this.words=[0],this.length=1;for(var v=0,S=1;S<=67108863;S*=d)v++;v--,S=S/d|0;for(var A=c.length-p,P=A%v,w=Math.min(A,A-P)+p,l=0,_=p;_<w;_+=v)l=y(c,_,_+v,d),this.imuln(S),this.words[0]+l<67108864?this.words[0]+=l:this._iaddn(l);if(P!==0){var nt=1;for(l=y(c,_,c.length,d),_=0;_<P;_++)nt*=d;this.imuln(nt),this.words[0]+l<67108864?this.words[0]+=l:this._iaddn(l)}this._strip()},i.prototype.copy=function(c){c.words=new Array(this.length);for(var d=0;d<this.length;d++)c.words[d]=this.words[d];c.length=this.length,c.negative=this.negative,c.red=this.red};function m(b,c){b.words=c.words,b.length=c.length,b.negative=c.negative,b.red=c.red}if(i.prototype._move=function(c){m(c,this)},i.prototype.clone=function(){var c=new i(null);return this.copy(c),c},i.prototype._expand=function(c){for(;this.length<c;)this.words[this.length++]=0;return this},i.prototype._strip=function(){for(;this.length>1&&this.words[this.length-1]===0;)this.length--;return this._normSign()},i.prototype._normSign=function(){return this.length===1&&this.words[0]===0&&(this.negative=0),this},typeof Symbol<"u"&&typeof Symbol.for=="function")try{i.prototype[Symbol.for("nodejs.util.inspect.custom")]=E}catch{i.prototype.inspect=E}else i.prototype.inspect=E;function E(){return(this.red?"<BN-R: ":"<BN: ")+this.toString(16)+">"}var M=["","0","00","000","0000","00000","000000","0000000","00000000","000000000","0000000000","00000000000","000000000000","0000000000000","00000000000000","000000000000000","0000000000000000","00000000000000000","000000000000000000","0000000000000000000","00000000000000000000","000000000000000000000","0000000000000000000000","00000000000000000000000","000000000000000000000000","0000000000000000000000000"],k=[0,0,25,16,12,11,10,9,8,8,7,7,7,7,6,6,6,6,6,6,6,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5],R=[0,0,33554432,43046721,16777216,48828125,60466176,40353607,16777216,43046721,1e7,19487171,35831808,62748517,7529536,11390625,16777216,24137569,34012224,47045881,64e6,4084101,5153632,6436343,7962624,9765625,11881376,14348907,17210368,20511149,243e5,28629151,33554432,39135393,45435424,52521875,60466176];i.prototype.toString=function(c,d){c=c||10,d=d|0||1;var p;if(c===16||c==="hex"){p="";for(var v=0,S=0,A=0;A<this.length;A++){var P=this.words[A],w=((P<<v|S)&16777215).toString(16);S=P>>>24-v&16777215,v+=2,v>=26&&(v-=26,A--),S!==0||A!==this.length-1?p=M[6-w.length]+w+p:p=w+p}for(S!==0&&(p=S.toString(16)+p);p.length%d!==0;)p="0"+p;return this.negative!==0&&(p="-"+p),p}if(c===(c|0)&&c>=2&&c<=36){var l=k[c],_=R[c];p="";var nt=this.clone();for(nt.negative=0;!nt.isZero();){var J=nt.modrn(_).toString(c);nt=nt.idivn(_),nt.isZero()?p=J+p:p=M[l-J.length]+J+p}for(this.isZero()&&(p="0"+p);p.length%d!==0;)p="0"+p;return this.negative!==0&&(p="-"+p),p}r(!1,"Base should be between 2 and 36")},i.prototype.toNumber=function(){var c=this.words[0];return this.length===2?c+=this.words[1]*67108864:this.length===3&&this.words[2]===1?c+=4503599627370496+this.words[1]*67108864:this.length>2&&r(!1,"Number can only safely store up to 53 bits"),this.negative!==0?-c:c},i.prototype.toJSON=function(){return this.toString(16,2)},o&&(i.prototype.toBuffer=function(c,d){return this.toArrayLike(o,c,d)}),i.prototype.toArray=function(c,d){return this.toArrayLike(Array,c,d)};var B=function(c,d){return c.allocUnsafe?c.allocUnsafe(d):new c(d)};i.prototype.toArrayLike=function(c,d,p){this._strip();var v=this.byteLength(),S=p||Math.max(1,v);r(v<=S,"byte array longer than desired length"),r(S>0,"Requested array length <= 0");var A=B(c,S),P=d==="le"?"LE":"BE";return this["_toArrayLike"+P](A,v),A},i.prototype._toArrayLikeLE=function(c,d){for(var p=0,v=0,S=0,A=0;S<this.length;S++){var P=this.words[S]<<A|v;c[p++]=P&255,p<c.length&&(c[p++]=P>>8&255),p<c.length&&(c[p++]=P>>16&255),A===6?(p<c.length&&(c[p++]=P>>24&255),v=0,A=0):(v=P>>>24,A+=2)}if(p<c.length)for(c[p++]=v;p<c.length;)c[p++]=0},i.prototype._toArrayLikeBE=function(c,d){for(var p=c.length-1,v=0,S=0,A=0;S<this.length;S++){var P=this.words[S]<<A|v;c[p--]=P&255,p>=0&&(c[p--]=P>>8&255),p>=0&&(c[p--]=P>>16&255),A===6?(p>=0&&(c[p--]=P>>24&255),v=0,A=0):(v=P>>>24,A+=2)}if(p>=0)for(c[p--]=v;p>=0;)c[p--]=0},Math.clz32?i.prototype._countBits=function(c){return 32-Math.clz32(c)}:i.prototype._countBits=function(c){var d=c,p=0;return d>=4096&&(p+=13,d>>>=13),d>=64&&(p+=7,d>>>=7),d>=8&&(p+=4,d>>>=4),d>=2&&(p+=2,d>>>=2),p+d},i.prototype._zeroBits=function(c){if(c===0)return 26;var d=c,p=0;return d&8191||(p+=13,d>>>=13),d&127||(p+=7,d>>>=7),d&15||(p+=4,d>>>=4),d&3||(p+=2,d>>>=2),d&1||p++,p},i.prototype.bitLength=function(){var c=this.words[this.length-1],d=this._countBits(c);return(this.length-1)*26+d};function I(b){for(var c=new Array(b.bitLength()),d=0;d<c.length;d++){var p=d/26|0,v=d%26;c[d]=b.words[p]>>>v&1}return c}i.prototype.zeroBits=function(){if(this.isZero())return 0;for(var c=0,d=0;d<this.length;d++){var p=this._zeroBits(this.words[d]);if(c+=p,p!==26)break}return c},i.prototype.byteLength=function(){return Math.ceil(this.bitLength()/8)},i.prototype.toTwos=function(c){return this.negative!==0?this.abs().inotn(c).iaddn(1):this.clone()},i.prototype.fromTwos=function(c){return this.testn(c-1)?this.notn(c).iaddn(1).ineg():this.clone()},i.prototype.isNeg=function(){return this.negative!==0},i.prototype.neg=function(){return this.clone().ineg()},i.prototype.ineg=function(){return this.isZero()||(this.negative^=1),this},i.prototype.iuor=function(c){for(;this.length<c.length;)this.words[this.length++]=0;for(var d=0;d<c.length;d++)this.words[d]=this.words[d]|c.words[d];return this._strip()},i.prototype.ior=function(c){return r((this.negative|c.negative)===0),this.iuor(c)},i.prototype.or=function(c){return this.length>c.length?this.clone().ior(c):c.clone().ior(this)},i.prototype.uor=function(c){return this.length>c.length?this.clone().iuor(c):c.clone().iuor(this)},i.prototype.iuand=function(c){var d;this.length>c.length?d=c:d=this;for(var p=0;p<d.length;p++)this.words[p]=this.words[p]&c.words[p];return this.length=d.length,this._strip()},i.prototype.iand=function(c){return r((this.negative|c.negative)===0),this.iuand(c)},i.prototype.and=function(c){return this.length>c.length?this.clone().iand(c):c.clone().iand(this)},i.prototype.uand=function(c){return this.length>c.length?this.clone().iuand(c):c.clone().iuand(this)},i.prototype.iuxor=function(c){var d,p;this.length>c.length?(d=this,p=c):(d=c,p=this);for(var v=0;v<p.length;v++)this.words[v]=d.words[v]^p.words[v];if(this!==d)for(;v<d.length;v++)this.words[v]=d.words[v];return this.length=d.length,this._strip()},i.prototype.ixor=function(c){return r((this.negative|c.negative)===0),this.iuxor(c)},i.prototype.xor=function(c){return this.length>c.length?this.clone().ixor(c):c.clone().ixor(this)},i.prototype.uxor=function(c){return this.length>c.length?this.clone().iuxor(c):c.clone().iuxor(this)},i.prototype.inotn=function(c){r(typeof c=="number"&&c>=0);var d=Math.ceil(c/26)|0,p=c%26;this._expand(d),p>0&&d--;for(var v=0;v<d;v++)this.words[v]=~this.words[v]&67108863;return p>0&&(this.words[v]=~this.words[v]&67108863>>26-p),this._strip()},i.prototype.notn=function(c){return this.clone().inotn(c)},i.prototype.setn=function(c,d){r(typeof c=="number"&&c>=0);var p=c/26|0,v=c%26;return this._expand(p+1),d?this.words[p]=this.words[p]|1<<v:this.words[p]=this.words[p]&~(1<<v),this._strip()},i.prototype.iadd=function(c){var d;if(this.negative!==0&&c.negative===0)return this.negative=0,d=this.isub(c),this.negative^=1,this._normSign();if(this.negative===0&&c.negative!==0)return c.negative=0,d=this.isub(c),c.negative=1,d._normSign();var p,v;this.length>c.length?(p=this,v=c):(p=c,v=this);for(var S=0,A=0;A<v.length;A++)d=(p.words[A]|0)+(v.words[A]|0)+S,this.words[A]=d&67108863,S=d>>>26;for(;S!==0&&A<p.length;A++)d=(p.words[A]|0)+S,this.words[A]=d&67108863,S=d>>>26;if(this.length=p.length,S!==0)this.words[this.length]=S,this.length++;else if(p!==this)for(;A<p.length;A++)this.words[A]=p.words[A];return this},i.prototype.add=function(c){var d;return c.negative!==0&&this.negative===0?(c.negative=0,d=this.sub(c),c.negative^=1,d):c.negative===0&&this.negative!==0?(this.negative=0,d=c.sub(this),this.negative=1,d):this.length>c.length?this.clone().iadd(c):c.clone().iadd(this)},i.prototype.isub=function(c){if(c.negative!==0){c.negative=0;var d=this.iadd(c);return c.negative=1,d._normSign()}else if(this.negative!==0)return this.negative=0,this.iadd(c),this.negative=1,this._normSign();var p=this.cmp(c);if(p===0)return this.negative=0,this.length=1,this.words[0]=0,this;var v,S;p>0?(v=this,S=c):(v=c,S=this);for(var A=0,P=0;P<S.length;P++)d=(v.words[P]|0)-(S.words[P]|0)+A,A=d>>26,this.words[P]=d&67108863;for(;A!==0&&P<v.length;P++)d=(v.words[P]|0)+A,A=d>>26,this.words[P]=d&67108863;if(A===0&&P<v.length&&v!==this)for(;P<v.length;P++)this.words[P]=v.words[P];return this.length=Math.max(this.length,P),v!==this&&(this.negative=1),this._strip()},i.prototype.sub=function(c){return this.clone().isub(c)};function C(b,c,d){d.negative=c.negative^b.negative;var p=b.length+c.length|0;d.length=p,p=p-1|0;var v=b.words[0]|0,S=c.words[0]|0,A=v*S,P=A&67108863,w=A/67108864|0;d.words[0]=P;for(var l=1;l<p;l++){for(var _=w>>>26,nt=w&67108863,J=Math.min(l,c.length-1),it=Math.max(0,l-b.length+1);it<=J;it++){var At=l-it|0;v=b.words[At]|0,S=c.words[it]|0,A=v*S+nt,_+=A/67108864|0,nt=A&67108863}d.words[l]=nt|0,w=_|0}return w!==0?d.words[l]=w|0:d.length--,d._strip()}var K=function(c,d,p){var v=c.words,S=d.words,A=p.words,P=0,w,l,_,nt=v[0]|0,J=nt&8191,it=nt>>>13,At=v[1]|0,dt=At&8191,mt=At>>>13,Ke=v[2]|0,kt=Ke&8191,Ft=Ke>>>13,ur=v[3]|0,Rt=ur&8191,$t=ur>>>13,Un=v[4]|0,Ut=Un&8191,Ht=Un>>>13,Nn=v[5]|0,Dt=Nn&8191,Lt=Nn>>>13,Ne=v[6]|0,Nt=Ne&8191,Vt=Ne>>>13,He=v[7]|0,jt=He&8191,g=He>>>13,u=v[8]|0,f=u&8191,x=u>>>13,T=v[9]|0,L=T&8191,D=T>>>13,pt=S[0]|0,ht=pt&8191,lt=pt>>>13,Ct=S[1]|0,ft=Ct&8191,Yt=Ct>>>13,yo=S[2]|0,Jt=yo&8191,Xt=yo>>>13,mo=S[3]|0,Qt=mo&8191,te=mo>>>13,wo=S[4]|0,ee=wo&8191,re=wo>>>13,bo=S[5]|0,ne=bo&8191,ie=bo>>>13,vo=S[6]|0,se=vo&8191,oe=vo>>>13,ko=S[7]|0,ae=ko&8191,ce=ko>>>13,xo=S[8]|0,ue=xo&8191,fe=xo>>>13,So=S[9]|0,le=So&8191,he=So>>>13;p.negative=c.negative^d.negative,p.length=19,w=Math.imul(J,ht),l=Math.imul(J,lt),l=l+Math.imul(it,ht)|0,_=Math.imul(it,lt);var Ii=(P+w|0)+((l&8191)<<13)|0;P=(_+(l>>>13)|0)+(Ii>>>26)|0,Ii&=67108863,w=Math.imul(dt,ht),l=Math.imul(dt,lt),l=l+Math.imul(mt,ht)|0,_=Math.imul(mt,lt),w=w+Math.imul(J,ft)|0,l=l+Math.imul(J,Yt)|0,l=l+Math.imul(it,ft)|0,_=_+Math.imul(it,Yt)|0;var Ai=(P+w|0)+((l&8191)<<13)|0;P=(_+(l>>>13)|0)+(Ai>>>26)|0,Ai&=67108863,w=Math.imul(kt,ht),l=Math.imul(kt,lt),l=l+Math.imul(Ft,ht)|0,_=Math.imul(Ft,lt),w=w+Math.imul(dt,ft)|0,l=l+Math.imul(dt,Yt)|0,l=l+Math.imul(mt,ft)|0,_=_+Math.imul(mt,Yt)|0,w=w+Math.imul(J,Jt)|0,l=l+Math.imul(J,Xt)|0,l=l+Math.imul(it,Jt)|0,_=_+Math.imul(it,Xt)|0;var Bi=(P+w|0)+((l&8191)<<13)|0;P=(_+(l>>>13)|0)+(Bi>>>26)|0,Bi&=67108863,w=Math.imul(Rt,ht),l=Math.imul(Rt,lt),l=l+Math.imul($t,ht)|0,_=Math.imul($t,lt),w=w+Math.imul(kt,ft)|0,l=l+Math.imul(kt,Yt)|0,l=l+Math.imul(Ft,ft)|0,_=_+Math.imul(Ft,Yt)|0,w=w+Math.imul(dt,Jt)|0,l=l+Math.imul(dt,Xt)|0,l=l+Math.imul(mt,Jt)|0,_=_+Math.imul(mt,Xt)|0,w=w+Math.imul(J,Qt)|0,l=l+Math.imul(J,te)|0,l=l+Math.imul(it,Qt)|0,_=_+Math.imul(it,te)|0;var Ri=(P+w|0)+((l&8191)<<13)|0;P=(_+(l>>>13)|0)+(Ri>>>26)|0,Ri&=67108863,w=Math.imul(Ut,ht),l=Math.imul(Ut,lt),l=l+Math.imul(Ht,ht)|0,_=Math.imul(Ht,lt),w=w+Math.imul(Rt,ft)|0,l=l+Math.imul(Rt,Yt)|0,l=l+Math.imul($t,ft)|0,_=_+Math.imul($t,Yt)|0,w=w+Math.imul(kt,Jt)|0,l=l+Math.imul(kt,Xt)|0,l=l+Math.imul(Ft,Jt)|0,_=_+Math.imul(Ft,Xt)|0,w=w+Math.imul(dt,Qt)|0,l=l+Math.imul(dt,te)|0,l=l+Math.imul(mt,Qt)|0,_=_+Math.imul(mt,te)|0,w=w+Math.imul(J,ee)|0,l=l+Math.imul(J,re)|0,l=l+Math.imul(it,ee)|0,_=_+Math.imul(it,re)|0;var Mi=(P+w|0)+((l&8191)<<13)|0;P=(_+(l>>>13)|0)+(Mi>>>26)|0,Mi&=67108863,w=Math.imul(Dt,ht),l=Math.imul(Dt,lt),l=l+Math.imul(Lt,ht)|0,_=Math.imul(Lt,lt),w=w+Math.imul(Ut,ft)|0,l=l+Math.imul(Ut,Yt)|0,l=l+Math.imul(Ht,ft)|0,_=_+Math.imul(Ht,Yt)|0,w=w+Math.imul(Rt,Jt)|0,l=l+Math.imul(Rt,Xt)|0,l=l+Math.imul($t,Jt)|0,_=_+Math.imul($t,Xt)|0,w=w+Math.imul(kt,Qt)|0,l=l+Math.imul(kt,te)|0,l=l+Math.imul(Ft,Qt)|0,_=_+Math.imul(Ft,te)|0,w=w+Math.imul(dt,ee)|0,l=l+Math.imul(dt,re)|0,l=l+Math.imul(mt,ee)|0,_=_+Math.imul(mt,re)|0,w=w+Math.imul(J,ne)|0,l=l+Math.imul(J,ie)|0,l=l+Math.imul(it,ne)|0,_=_+Math.imul(it,ie)|0;var Ti=(P+w|0)+((l&8191)<<13)|0;P=(_+(l>>>13)|0)+(Ti>>>26)|0,Ti&=67108863,w=Math.imul(Nt,ht),l=Math.imul(Nt,lt),l=l+Math.imul(Vt,ht)|0,_=Math.imul(Vt,lt),w=w+Math.imul(Dt,ft)|0,l=l+Math.imul(Dt,Yt)|0,l=l+Math.imul(Lt,ft)|0,_=_+Math.imul(Lt,Yt)|0,w=w+Math.imul(Ut,Jt)|0,l=l+Math.imul(Ut,Xt)|0,l=l+Math.imul(Ht,Jt)|0,_=_+Math.imul(Ht,Xt)|0,w=w+Math.imul(Rt,Qt)|0,l=l+Math.imul(Rt,te)|0,l=l+Math.imul($t,Qt)|0,_=_+Math.imul($t,te)|0,w=w+Math.imul(kt,ee)|0,l=l+Math.imul(kt,re)|0,l=l+Math.imul(Ft,ee)|0,_=_+Math.imul(Ft,re)|0,w=w+Math.imul(dt,ne)|0,l=l+Math.imul(dt,ie)|0,l=l+Math.imul(mt,ne)|0,_=_+Math.imul(mt,ie)|0,w=w+Math.imul(J,se)|0,l=l+Math.imul(J,oe)|0,l=l+Math.imul(it,se)|0,_=_+Math.imul(it,oe)|0;var Pi=(P+w|0)+((l&8191)<<13)|0;P=(_+(l>>>13)|0)+(Pi>>>26)|0,Pi&=67108863,w=Math.imul(jt,ht),l=Math.imul(jt,lt),l=l+Math.imul(g,ht)|0,_=Math.imul(g,lt),w=w+Math.imul(Nt,ft)|0,l=l+Math.imul(Nt,Yt)|0,l=l+Math.imul(Vt,ft)|0,_=_+Math.imul(Vt,Yt)|0,w=w+Math.imul(Dt,Jt)|0,l=l+Math.imul(Dt,Xt)|0,l=l+Math.imul(Lt,Jt)|0,_=_+Math.imul(Lt,Xt)|0,w=w+Math.imul(Ut,Qt)|0,l=l+Math.imul(Ut,te)|0,l=l+Math.imul(Ht,Qt)|0,_=_+Math.imul(Ht,te)|0,w=w+Math.imul(Rt,ee)|0,l=l+Math.imul(Rt,re)|0,l=l+Math.imul($t,ee)|0,_=_+Math.imul($t,re)|0,w=w+Math.imul(kt,ne)|0,l=l+Math.imul(kt,ie)|0,l=l+Math.imul(Ft,ne)|0,_=_+Math.imul(Ft,ie)|0,w=w+Math.imul(dt,se)|0,l=l+Math.imul(dt,oe)|0,l=l+Math.imul(mt,se)|0,_=_+Math.imul(mt,oe)|0,w=w+Math.imul(J,ae)|0,l=l+Math.imul(J,ce)|0,l=l+Math.imul(it,ae)|0,_=_+Math.imul(it,ce)|0;var Li=(P+w|0)+((l&8191)<<13)|0;P=(_+(l>>>13)|0)+(Li>>>26)|0,Li&=67108863,w=Math.imul(f,ht),l=Math.imul(f,lt),l=l+Math.imul(x,ht)|0,_=Math.imul(x,lt),w=w+Math.imul(jt,ft)|0,l=l+Math.imul(jt,Yt)|0,l=l+Math.imul(g,ft)|0,_=_+Math.imul(g,Yt)|0,w=w+Math.imul(Nt,Jt)|0,l=l+Math.imul(Nt,Xt)|0,l=l+Math.imul(Vt,Jt)|0,_=_+Math.imul(Vt,Xt)|0,w=w+Math.imul(Dt,Qt)|0,l=l+Math.imul(Dt,te)|0,l=l+Math.imul(Lt,Qt)|0,_=_+Math.imul(Lt,te)|0,w=w+Math.imul(Ut,ee)|0,l=l+Math.imul(Ut,re)|0,l=l+Math.imul(Ht,ee)|0,_=_+Math.imul(Ht,re)|0,w=w+Math.imul(Rt,ne)|0,l=l+Math.imul(Rt,ie)|0,l=l+Math.imul($t,ne)|0,_=_+Math.imul($t,ie)|0,w=w+Math.imul(kt,se)|0,l=l+Math.imul(kt,oe)|0,l=l+Math.imul(Ft,se)|0,_=_+Math.imul(Ft,oe)|0,w=w+Math.imul(dt,ae)|0,l=l+Math.imul(dt,ce)|0,l=l+Math.imul(mt,ae)|0,_=_+Math.imul(mt,ce)|0,w=w+Math.imul(J,ue)|0,l=l+Math.imul(J,fe)|0,l=l+Math.imul(it,ue)|0,_=_+Math.imul(it,fe)|0;var Ci=(P+w|0)+((l&8191)<<13)|0;P=(_+(l>>>13)|0)+(Ci>>>26)|0,Ci&=67108863,w=Math.imul(L,ht),l=Math.imul(L,lt),l=l+Math.imul(D,ht)|0,_=Math.imul(D,lt),w=w+Math.imul(f,ft)|0,l=l+Math.imul(f,Yt)|0,l=l+Math.imul(x,ft)|0,_=_+Math.imul(x,Yt)|0,w=w+Math.imul(jt,Jt)|0,l=l+Math.imul(jt,Xt)|0,l=l+Math.imul(g,Jt)|0,_=_+Math.imul(g,Xt)|0,w=w+Math.imul(Nt,Qt)|0,l=l+Math.imul(Nt,te)|0,l=l+Math.imul(Vt,Qt)|0,_=_+Math.imul(Vt,te)|0,w=w+Math.imul(Dt,ee)|0,l=l+Math.imul(Dt,re)|0,l=l+Math.imul(Lt,ee)|0,_=_+Math.imul(Lt,re)|0,w=w+Math.imul(Ut,ne)|0,l=l+Math.imul(Ut,ie)|0,l=l+Math.imul(Ht,ne)|0,_=_+Math.imul(Ht,ie)|0,w=w+Math.imul(Rt,se)|0,l=l+Math.imul(Rt,oe)|0,l=l+Math.imul($t,se)|0,_=_+Math.imul($t,oe)|0,w=w+Math.imul(kt,ae)|0,l=l+Math.imul(kt,ce)|0,l=l+Math.imul(Ft,ae)|0,_=_+Math.imul(Ft,ce)|0,w=w+Math.imul(dt,ue)|0,l=l+Math.imul(dt,fe)|0,l=l+Math.imul(mt,ue)|0,_=_+Math.imul(mt,fe)|0,w=w+Math.imul(J,le)|0,l=l+Math.imul(J,he)|0,l=l+Math.imul(it,le)|0,_=_+Math.imul(it,he)|0;var Oi=(P+w|0)+((l&8191)<<13)|0;P=(_+(l>>>13)|0)+(Oi>>>26)|0,Oi&=67108863,w=Math.imul(L,ft),l=Math.imul(L,Yt),l=l+Math.imul(D,ft)|0,_=Math.imul(D,Yt),w=w+Math.imul(f,Jt)|0,l=l+Math.imul(f,Xt)|0,l=l+Math.imul(x,Jt)|0,_=_+Math.imul(x,Xt)|0,w=w+Math.imul(jt,Qt)|0,l=l+Math.imul(jt,te)|0,l=l+Math.imul(g,Qt)|0,_=_+Math.imul(g,te)|0,w=w+Math.imul(Nt,ee)|0,l=l+Math.imul(Nt,re)|0,l=l+Math.imul(Vt,ee)|0,_=_+Math.imul(Vt,re)|0,w=w+Math.imul(Dt,ne)|0,l=l+Math.imul(Dt,ie)|0,l=l+Math.imul(Lt,ne)|0,_=_+Math.imul(Lt,ie)|0,w=w+Math.imul(Ut,se)|0,l=l+Math.imul(Ut,oe)|0,l=l+Math.imul(Ht,se)|0,_=_+Math.imul(Ht,oe)|0,w=w+Math.imul(Rt,ae)|0,l=l+Math.imul(Rt,ce)|0,l=l+Math.imul($t,ae)|0,_=_+Math.imul($t,ce)|0,w=w+Math.imul(kt,ue)|0,l=l+Math.imul(kt,fe)|0,l=l+Math.imul(Ft,ue)|0,_=_+Math.imul(Ft,fe)|0,w=w+Math.imul(dt,le)|0,l=l+Math.imul(dt,he)|0,l=l+Math.imul(mt,le)|0,_=_+Math.imul(mt,he)|0;var Ui=(P+w|0)+((l&8191)<<13)|0;P=(_+(l>>>13)|0)+(Ui>>>26)|0,Ui&=67108863,w=Math.imul(L,Jt),l=Math.imul(L,Xt),l=l+Math.imul(D,Jt)|0,_=Math.imul(D,Xt),w=w+Math.imul(f,Qt)|0,l=l+Math.imul(f,te)|0,l=l+Math.imul(x,Qt)|0,_=_+Math.imul(x,te)|0,w=w+Math.imul(jt,ee)|0,l=l+Math.imul(jt,re)|0,l=l+Math.imul(g,ee)|0,_=_+Math.imul(g,re)|0,w=w+Math.imul(Nt,ne)|0,l=l+Math.imul(Nt,ie)|0,l=l+Math.imul(Vt,ne)|0,_=_+Math.imul(Vt,ie)|0,w=w+Math.imul(Dt,se)|0,l=l+Math.imul(Dt,oe)|0,l=l+Math.imul(Lt,se)|0,_=_+Math.imul(Lt,oe)|0,w=w+Math.imul(Ut,ae)|0,l=l+Math.imul(Ut,ce)|0,l=l+Math.imul(Ht,ae)|0,_=_+Math.imul(Ht,ce)|0,w=w+Math.imul(Rt,ue)|0,l=l+Math.imul(Rt,fe)|0,l=l+Math.imul($t,ue)|0,_=_+Math.imul($t,fe)|0,w=w+Math.imul(kt,le)|0,l=l+Math.imul(kt,he)|0,l=l+Math.imul(Ft,le)|0,_=_+Math.imul(Ft,he)|0;var Ni=(P+w|0)+((l&8191)<<13)|0;P=(_+(l>>>13)|0)+(Ni>>>26)|0,Ni&=67108863,w=Math.imul(L,Qt),l=Math.imul(L,te),l=l+Math.imul(D,Qt)|0,_=Math.imul(D,te),w=w+Math.imul(f,ee)|0,l=l+Math.imul(f,re)|0,l=l+Math.imul(x,ee)|0,_=_+Math.imul(x,re)|0,w=w+Math.imul(jt,ne)|0,l=l+Math.imul(jt,ie)|0,l=l+Math.imul(g,ne)|0,_=_+Math.imul(g,ie)|0,w=w+Math.imul(Nt,se)|0,l=l+Math.imul(Nt,oe)|0,l=l+Math.imul(Vt,se)|0,_=_+Math.imul(Vt,oe)|0,w=w+Math.imul(Dt,ae)|0,l=l+Math.imul(Dt,ce)|0,l=l+Math.imul(Lt,ae)|0,_=_+Math.imul(Lt,ce)|0,w=w+Math.imul(Ut,ue)|0,l=l+Math.imul(Ut,fe)|0,l=l+Math.imul(Ht,ue)|0,_=_+Math.imul(Ht,fe)|0,w=w+Math.imul(Rt,le)|0,l=l+Math.imul(Rt,he)|0,l=l+Math.imul($t,le)|0,_=_+Math.imul($t,he)|0;var zi=(P+w|0)+((l&8191)<<13)|0;P=(_+(l>>>13)|0)+(zi>>>26)|0,zi&=67108863,w=Math.imul(L,ee),l=Math.imul(L,re),l=l+Math.imul(D,ee)|0,_=Math.imul(D,re),w=w+Math.imul(f,ne)|0,l=l+Math.imul(f,ie)|0,l=l+Math.imul(x,ne)|0,_=_+Math.imul(x,ie)|0,w=w+Math.imul(jt,se)|0,l=l+Math.imul(jt,oe)|0,l=l+Math.imul(g,se)|0,_=_+Math.imul(g,oe)|0,w=w+Math.imul(Nt,ae)|0,l=l+Math.imul(Nt,ce)|0,l=l+Math.imul(Vt,ae)|0,_=_+Math.imul(Vt,ce)|0,w=w+Math.imul(Dt,ue)|0,l=l+Math.imul(Dt,fe)|0,l=l+Math.imul(Lt,ue)|0,_=_+Math.imul(Lt,fe)|0,w=w+Math.imul(Ut,le)|0,l=l+Math.imul(Ut,he)|0,l=l+Math.imul(Ht,le)|0,_=_+Math.imul(Ht,he)|0;var Fi=(P+w|0)+((l&8191)<<13)|0;P=(_+(l>>>13)|0)+(Fi>>>26)|0,Fi&=67108863,w=Math.imul(L,ne),l=Math.imul(L,ie),l=l+Math.imul(D,ne)|0,_=Math.imul(D,ie),w=w+Math.imul(f,se)|0,l=l+Math.imul(f,oe)|0,l=l+Math.imul(x,se)|0,_=_+Math.imul(x,oe)|0,w=w+Math.imul(jt,ae)|0,l=l+Math.imul(jt,ce)|0,l=l+Math.imul(g,ae)|0,_=_+Math.imul(g,ce)|0,w=w+Math.imul(Nt,ue)|0,l=l+Math.imul(Nt,fe)|0,l=l+Math.imul(Vt,ue)|0,_=_+Math.imul(Vt,fe)|0,w=w+Math.imul(Dt,le)|0,l=l+Math.imul(Dt,he)|0,l=l+Math.imul(Lt,le)|0,_=_+Math.imul(Lt,he)|0;var Di=(P+w|0)+((l&8191)<<13)|0;P=(_+(l>>>13)|0)+(Di>>>26)|0,Di&=67108863,w=Math.imul(L,se),l=Math.imul(L,oe),l=l+Math.imul(D,se)|0,_=Math.imul(D,oe),w=w+Math.imul(f,ae)|0,l=l+Math.imul(f,ce)|0,l=l+Math.imul(x,ae)|0,_=_+Math.imul(x,ce)|0,w=w+Math.imul(jt,ue)|0,l=l+Math.imul(jt,fe)|0,l=l+Math.imul(g,ue)|0,_=_+Math.imul(g,fe)|0,w=w+Math.imul(Nt,le)|0,l=l+Math.imul(Nt,he)|0,l=l+Math.imul(Vt,le)|0,_=_+Math.imul(Vt,he)|0;var Ki=(P+w|0)+((l&8191)<<13)|0;P=(_+(l>>>13)|0)+(Ki>>>26)|0,Ki&=67108863,w=Math.imul(L,ae),l=Math.imul(L,ce),l=l+Math.imul(D,ae)|0,_=Math.imul(D,ce),w=w+Math.imul(f,ue)|0,l=l+Math.imul(f,fe)|0,l=l+Math.imul(x,ue)|0,_=_+Math.imul(x,fe)|0,w=w+Math.imul(jt,le)|0,l=l+Math.imul(jt,he)|0,l=l+Math.imul(g,le)|0,_=_+Math.imul(g,he)|0;var qi=(P+w|0)+((l&8191)<<13)|0;P=(_+(l>>>13)|0)+(qi>>>26)|0,qi&=67108863,w=Math.imul(L,ue),l=Math.imul(L,fe),l=l+Math.imul(D,ue)|0,_=Math.imul(D,fe),w=w+Math.imul(f,le)|0,l=l+Math.imul(f,he)|0,l=l+Math.imul(x,le)|0,_=_+Math.imul(x,he)|0;var Wi=(P+w|0)+((l&8191)<<13)|0;P=(_+(l>>>13)|0)+(Wi>>>26)|0,Wi&=67108863,w=Math.imul(L,le),l=Math.imul(L,he),l=l+Math.imul(D,le)|0,_=Math.imul(D,he);var $i=(P+w|0)+((l&8191)<<13)|0;return P=(_+(l>>>13)|0)+($i>>>26)|0,$i&=67108863,A[0]=Ii,A[1]=Ai,A[2]=Bi,A[3]=Ri,A[4]=Mi,A[5]=Ti,A[6]=Pi,A[7]=Li,A[8]=Ci,A[9]=Oi,A[10]=Ui,A[11]=Ni,A[12]=zi,A[13]=Fi,A[14]=Di,A[15]=Ki,A[16]=qi,A[17]=Wi,A[18]=$i,P!==0&&(A[19]=P,p.length++),p};Math.imul||(K=C);function q(b,c,d){d.negative=c.negative^b.negative,d.length=b.length+c.length;for(var p=0,v=0,S=0;S<d.length-1;S++){var A=v;v=0;for(var P=p&67108863,w=Math.min(S,c.length-1),l=Math.max(0,S-b.length+1);l<=w;l++){var _=S-l,nt=b.words[_]|0,J=c.words[l]|0,it=nt*J,At=it&67108863;A=A+(it/67108864|0)|0,At=At+P|0,P=At&67108863,A=A+(At>>>26)|0,v+=A>>>26,A&=67108863}d.words[S]=P,p=A,A=v}return p!==0?d.words[S]=p:d.length--,d._strip()}function V(b,c,d){return q(b,c,d)}i.prototype.mulTo=function(c,d){var p,v=this.length+c.length;return this.length===10&&c.length===10?p=K(this,c,d):v<63?p=C(this,c,d):v<1024?p=q(this,c,d):p=V(this,c,d),p},i.prototype.mul=function(c){var d=new i(null);return d.words=new Array(this.length+c.length),this.mulTo(c,d)},i.prototype.mulf=function(c){var d=new i(null);return d.words=new Array(this.length+c.length),V(this,c,d)},i.prototype.imul=function(c){return this.clone().mulTo(c,this)},i.prototype.imuln=function(c){var d=c<0;d&&(c=-c),r(typeof c=="number"),r(c<67108864);for(var p=0,v=0;v<this.length;v++){var S=(this.words[v]|0)*c,A=(S&67108863)+(p&67108863);p>>=26,p+=S/67108864|0,p+=A>>>26,this.words[v]=A&67108863}return p!==0&&(this.words[v]=p,this.length++),this.length=c===0?1:this.length,d?this.ineg():this},i.prototype.muln=function(c){return this.clone().imuln(c)},i.prototype.sqr=function(){return this.mul(this)},i.prototype.isqr=function(){return this.imul(this.clone())},i.prototype.pow=function(c){var d=I(c);if(d.length===0)return new i(1);for(var p=this,v=0;v<d.length&&d[v]===0;v++,p=p.sqr());if(++v<d.length)for(var S=p.sqr();v<d.length;v++,S=S.sqr())d[v]!==0&&(p=p.mul(S));return p},i.prototype.iushln=function(c){r(typeof c=="number"&&c>=0);var d=c%26,p=(c-d)/26,v=67108863>>>26-d<<26-d,S;if(d!==0){var A=0;for(S=0;S<this.length;S++){var P=this.words[S]&v,w=(this.words[S]|0)-P<<d;this.words[S]=w|A,A=P>>>26-d}A&&(this.words[S]=A,this.length++)}if(p!==0){for(S=this.length-1;S>=0;S--)this.words[S+p]=this.words[S];for(S=0;S<p;S++)this.words[S]=0;this.length+=p}return this._strip()},i.prototype.ishln=function(c){return r(this.negative===0),this.iushln(c)},i.prototype.iushrn=function(c,d,p){r(typeof c=="number"&&c>=0);var v;d?v=(d-d%26)/26:v=0;var S=c%26,A=Math.min((c-S)/26,this.length),P=67108863^67108863>>>S<<S,w=p;if(v-=A,v=Math.max(0,v),w){for(var l=0;l<A;l++)w.words[l]=this.words[l];w.length=A}if(A!==0)if(this.length>A)for(this.length-=A,l=0;l<this.length;l++)this.words[l]=this.words[l+A];else this.words[0]=0,this.length=1;var _=0;for(l=this.length-1;l>=0&&(_!==0||l>=v);l--){var nt=this.words[l]|0;this.words[l]=_<<26-S|nt>>>S,_=nt&P}return w&&_!==0&&(w.words[w.length++]=_),this.length===0&&(this.words[0]=0,this.length=1),this._strip()},i.prototype.ishrn=function(c,d,p){return r(this.negative===0),this.iushrn(c,d,p)},i.prototype.shln=function(c){return this.clone().ishln(c)},i.prototype.ushln=function(c){return this.clone().iushln(c)},i.prototype.shrn=function(c){return this.clone().ishrn(c)},i.prototype.ushrn=function(c){return this.clone().iushrn(c)},i.prototype.testn=function(c){r(typeof c=="number"&&c>=0);var d=c%26,p=(c-d)/26,v=1<<d;if(this.length<=p)return!1;var S=this.words[p];return!!(S&v)},i.prototype.imaskn=function(c){r(typeof c=="number"&&c>=0);var d=c%26,p=(c-d)/26;if(r(this.negative===0,"imaskn works only with positive numbers"),this.length<=p)return this;if(d!==0&&p++,this.length=Math.min(p,this.length),d!==0){var v=67108863^67108863>>>d<<d;this.words[this.length-1]&=v}return this._strip()},i.prototype.maskn=function(c){return this.clone().imaskn(c)},i.prototype.iaddn=function(c){return r(typeof c=="number"),r(c<67108864),c<0?this.isubn(-c):this.negative!==0?this.length===1&&(this.words[0]|0)<=c?(this.words[0]=c-(this.words[0]|0),this.negative=0,this):(this.negative=0,this.isubn(c),this.negative=1,this):this._iaddn(c)},i.prototype._iaddn=function(c){this.words[0]+=c;for(var d=0;d<this.length&&this.words[d]>=67108864;d++)this.words[d]-=67108864,d===this.length-1?this.words[d+1]=1:this.words[d+1]++;return this.length=Math.max(this.length,d+1),this},i.prototype.isubn=function(c){if(r(typeof c=="number"),r(c<67108864),c<0)return this.iaddn(-c);if(this.negative!==0)return this.negative=0,this.iaddn(c),this.negative=1,this;if(this.words[0]-=c,this.length===1&&this.words[0]<0)this.words[0]=-this.words[0],this.negative=1;else for(var d=0;d<this.length&&this.words[d]<0;d++)this.words[d]+=67108864,this.words[d+1]-=1;return this._strip()},i.prototype.addn=function(c){return this.clone().iaddn(c)},i.prototype.subn=function(c){return this.clone().isubn(c)},i.prototype.iabs=function(){return this.negative=0,this},i.prototype.abs=function(){return this.clone().iabs()},i.prototype._ishlnsubmul=function(c,d,p){var v=c.length+p,S;this._expand(v);var A,P=0;for(S=0;S<c.length;S++){A=(this.words[S+p]|0)+P;var w=(c.words[S]|0)*d;A-=w&67108863,P=(A>>26)-(w/67108864|0),this.words[S+p]=A&67108863}for(;S<this.length-p;S++)A=(this.words[S+p]|0)+P,P=A>>26,this.words[S+p]=A&67108863;if(P===0)return this._strip();for(r(P===-1),P=0,S=0;S<this.length;S++)A=-(this.words[S]|0)+P,P=A>>26,this.words[S]=A&67108863;return this.negative=1,this._strip()},i.prototype._wordDiv=function(c,d){var p=this.length-c.length,v=this.clone(),S=c,A=S.words[S.length-1]|0,P=this._countBits(A);p=26-P,p!==0&&(S=S.ushln(p),v.iushln(p),A=S.words[S.length-1]|0);var w=v.length-S.length,l;if(d!=="mod"){l=new i(null),l.length=w+1,l.words=new Array(l.length);for(var _=0;_<l.length;_++)l.words[_]=0}var nt=v.clone()._ishlnsubmul(S,1,w);nt.negative===0&&(v=nt,l&&(l.words[w]=1));for(var J=w-1;J>=0;J--){var it=(v.words[S.length+J]|0)*67108864+(v.words[S.length+J-1]|0);for(it=Math.min(it/A|0,67108863),v._ishlnsubmul(S,it,J);v.negative!==0;)it--,v.negative=0,v._ishlnsubmul(S,1,J),v.isZero()||(v.negative^=1);l&&(l.words[J]=it)}return l&&l._strip(),v._strip(),d!=="div"&&p!==0&&v.iushrn(p),{div:l||null,mod:v}},i.prototype.divmod=function(c,d,p){if(r(!c.isZero()),this.isZero())return{div:new i(0),mod:new i(0)};var v,S,A;return this.negative!==0&&c.negative===0?(A=this.neg().divmod(c,d),d!=="mod"&&(v=A.div.neg()),d!=="div"&&(S=A.mod.neg(),p&&S.negative!==0&&S.iadd(c)),{div:v,mod:S}):this.negative===0&&c.negative!==0?(A=this.divmod(c.neg(),d),d!=="mod"&&(v=A.div.neg()),{div:v,mod:A.mod}):this.negative&c.negative?(A=this.neg().divmod(c.neg(),d),d!=="div"&&(S=A.mod.neg(),p&&S.negative!==0&&S.isub(c)),{div:A.div,mod:S}):c.length>this.length||this.cmp(c)<0?{div:new i(0),mod:this}:c.length===1?d==="div"?{div:this.divn(c.words[0]),mod:null}:d==="mod"?{div:null,mod:new i(this.modrn(c.words[0]))}:{div:this.divn(c.words[0]),mod:new i(this.modrn(c.words[0]))}:this._wordDiv(c,d)},i.prototype.div=function(c){return this.divmod(c,"div",!1).div},i.prototype.mod=function(c){return this.divmod(c,"mod",!1).mod},i.prototype.umod=function(c){return this.divmod(c,"mod",!0).mod},i.prototype.divRound=function(c){var d=this.divmod(c);if(d.mod.isZero())return d.div;var p=d.div.negative!==0?d.mod.isub(c):d.mod,v=c.ushrn(1),S=c.andln(1),A=p.cmp(v);return A<0||S===1&&A===0?d.div:d.div.negative!==0?d.div.isubn(1):d.div.iaddn(1)},i.prototype.modrn=function(c){var d=c<0;d&&(c=-c),r(c<=67108863);for(var p=(1<<26)%c,v=0,S=this.length-1;S>=0;S--)v=(p*v+(this.words[S]|0))%c;return d?-v:v},i.prototype.modn=function(c){return this.modrn(c)},i.prototype.idivn=function(c){var d=c<0;d&&(c=-c),r(c<=67108863);for(var p=0,v=this.length-1;v>=0;v--){var S=(this.words[v]|0)+p*67108864;this.words[v]=S/c|0,p=S%c}return this._strip(),d?this.ineg():this},i.prototype.divn=function(c){return this.clone().idivn(c)},i.prototype.egcd=function(c){r(c.negative===0),r(!c.isZero());var d=this,p=c.clone();d.negative!==0?d=d.umod(c):d=d.clone();for(var v=new i(1),S=new i(0),A=new i(0),P=new i(1),w=0;d.isEven()&&p.isEven();)d.iushrn(1),p.iushrn(1),++w;for(var l=p.clone(),_=d.clone();!d.isZero();){for(var nt=0,J=1;!(d.words[0]&J)&&nt<26;++nt,J<<=1);if(nt>0)for(d.iushrn(nt);nt-- >0;)(v.isOdd()||S.isOdd())&&(v.iadd(l),S.isub(_)),v.iushrn(1),S.iushrn(1);for(var it=0,At=1;!(p.words[0]&At)&&it<26;++it,At<<=1);if(it>0)for(p.iushrn(it);it-- >0;)(A.isOdd()||P.isOdd())&&(A.iadd(l),P.isub(_)),A.iushrn(1),P.iushrn(1);d.cmp(p)>=0?(d.isub(p),v.isub(A),S.isub(P)):(p.isub(d),A.isub(v),P.isub(S))}return{a:A,b:P,gcd:p.iushln(w)}},i.prototype._invmp=function(c){r(c.negative===0),r(!c.isZero());var d=this,p=c.clone();d.negative!==0?d=d.umod(c):d=d.clone();for(var v=new i(1),S=new i(0),A=p.clone();d.cmpn(1)>0&&p.cmpn(1)>0;){for(var P=0,w=1;!(d.words[0]&w)&&P<26;++P,w<<=1);if(P>0)for(d.iushrn(P);P-- >0;)v.isOdd()&&v.iadd(A),v.iushrn(1);for(var l=0,_=1;!(p.words[0]&_)&&l<26;++l,_<<=1);if(l>0)for(p.iushrn(l);l-- >0;)S.isOdd()&&S.iadd(A),S.iushrn(1);d.cmp(p)>=0?(d.isub(p),v.isub(S)):(p.isub(d),S.isub(v))}var nt;return d.cmpn(1)===0?nt=v:nt=S,nt.cmpn(0)<0&&nt.iadd(c),nt},i.prototype.gcd=function(c){if(this.isZero())return c.abs();if(c.isZero())return this.abs();var d=this.clone(),p=c.clone();d.negative=0,p.negative=0;for(var v=0;d.isEven()&&p.isEven();v++)d.iushrn(1),p.iushrn(1);do{for(;d.isEven();)d.iushrn(1);for(;p.isEven();)p.iushrn(1);var S=d.cmp(p);if(S<0){var A=d;d=p,p=A}else if(S===0||p.cmpn(1)===0)break;d.isub(p)}while(!0);return p.iushln(v)},i.prototype.invm=function(c){return this.egcd(c).a.umod(c)},i.prototype.isEven=function(){return(this.words[0]&1)===0},i.prototype.isOdd=function(){return(this.words[0]&1)===1},i.prototype.andln=function(c){return this.words[0]&c},i.prototype.bincn=function(c){r(typeof c=="number");var d=c%26,p=(c-d)/26,v=1<<d;if(this.length<=p)return this._expand(p+1),this.words[p]|=v,this;for(var S=v,A=p;S!==0&&A<this.length;A++){var P=this.words[A]|0;P+=S,S=P>>>26,P&=67108863,this.words[A]=P}return S!==0&&(this.words[A]=S,this.length++),this},i.prototype.isZero=function(){return this.length===1&&this.words[0]===0},i.prototype.cmpn=function(c){var d=c<0;if(this.negative!==0&&!d)return-1;if(this.negative===0&&d)return 1;this._strip();var p;if(this.length>1)p=1;else{d&&(c=-c),r(c<=67108863,"Number is too big");var v=this.words[0]|0;p=v===c?0:v<c?-1:1}return this.negative!==0?-p|0:p},i.prototype.cmp=function(c){if(this.negative!==0&&c.negative===0)return-1;if(this.negative===0&&c.negative!==0)return 1;var d=this.ucmp(c);return this.negative!==0?-d|0:d},i.prototype.ucmp=function(c){if(this.length>c.length)return 1;if(this.length<c.length)return-1;for(var d=0,p=this.length-1;p>=0;p--){var v=this.words[p]|0,S=c.words[p]|0;if(v!==S){v<S?d=-1:v>S&&(d=1);break}}return d},i.prototype.gtn=function(c){return this.cmpn(c)===1},i.prototype.gt=function(c){return this.cmp(c)===1},i.prototype.gten=function(c){return this.cmpn(c)>=0},i.prototype.gte=function(c){return this.cmp(c)>=0},i.prototype.ltn=function(c){return this.cmpn(c)===-1},i.prototype.lt=function(c){return this.cmp(c)===-1},i.prototype.lten=function(c){return this.cmpn(c)<=0},i.prototype.lte=function(c){return this.cmp(c)<=0},i.prototype.eqn=function(c){return this.cmpn(c)===0},i.prototype.eq=function(c){return this.cmp(c)===0},i.red=function(c){return new z(c)},i.prototype.toRed=function(c){return r(!this.red,"Already a number in reduction context"),r(this.negative===0,"red works only with positives"),c.convertTo(this)._forceRed(c)},i.prototype.fromRed=function(){return r(this.red,"fromRed works only with numbers in reduction context"),this.red.convertFrom(this)},i.prototype._forceRed=function(c){return this.red=c,this},i.prototype.forceRed=function(c){return r(!this.red,"Already a number in reduction context"),this._forceRed(c)},i.prototype.redAdd=function(c){return r(this.red,"redAdd works only with red numbers"),this.red.add(this,c)},i.prototype.redIAdd=function(c){return r(this.red,"redIAdd works only with red numbers"),this.red.iadd(this,c)},i.prototype.redSub=function(c){return r(this.red,"redSub works only with red numbers"),this.red.sub(this,c)},i.prototype.redISub=function(c){return r(this.red,"redISub works only with red numbers"),this.red.isub(this,c)},i.prototype.redShl=function(c){return r(this.red,"redShl works only with red numbers"),this.red.shl(this,c)},i.prototype.redMul=function(c){return r(this.red,"redMul works only with red numbers"),this.red._verify2(this,c),this.red.mul(this,c)},i.prototype.redIMul=function(c){return r(this.red,"redMul works only with red numbers"),this.red._verify2(this,c),this.red.imul(this,c)},i.prototype.redSqr=function(){return r(this.red,"redSqr works only with red numbers"),this.red._verify1(this),this.red.sqr(this)},i.prototype.redISqr=function(){return r(this.red,"redISqr works only with red numbers"),this.red._verify1(this),this.red.isqr(this)},i.prototype.redSqrt=function(){return r(this.red,"redSqrt works only with red numbers"),this.red._verify1(this),this.red.sqrt(this)},i.prototype.redInvm=function(){return r(this.red,"redInvm works only with red numbers"),this.red._verify1(this),this.red.invm(this)},i.prototype.redNeg=function(){return r(this.red,"redNeg works only with red numbers"),this.red._verify1(this),this.red.neg(this)},i.prototype.redPow=function(c){return r(this.red&&!c.red,"redPow(normalNum)"),this.red._verify1(this),this.red.pow(this,c)};var F={k256:null,p224:null,p192:null,p25519:null};function j(b,c){this.name=b,this.p=new i(c,16),this.n=this.p.bitLength(),this.k=new i(1).iushln(this.n).isub(this.p),this.tmp=this._tmp()}j.prototype._tmp=function(){var c=new i(null);return c.words=new Array(Math.ceil(this.n/13)),c},j.prototype.ireduce=function(c){var d=c,p;do this.split(d,this.tmp),d=this.imulK(d),d=d.iadd(this.tmp),p=d.bitLength();while(p>this.n);var v=p<this.n?-1:d.ucmp(this.p);return v===0?(d.words[0]=0,d.length=1):v>0?d.isub(this.p):d.strip!==void 0?d.strip():d._strip(),d},j.prototype.split=function(c,d){c.iushrn(this.n,0,d)},j.prototype.imulK=function(c){return c.imul(this.k)};function G(){j.call(this,"k256","ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffe fffffc2f")}s(G,j),G.prototype.split=function(c,d){for(var p=4194303,v=Math.min(c.length,9),S=0;S<v;S++)d.words[S]=c.words[S];if(d.length=v,c.length<=9){c.words[0]=0,c.length=1;return}var A=c.words[9];for(d.words[d.length++]=A&p,S=10;S<c.length;S++){var P=c.words[S]|0;c.words[S-10]=(P&p)<<4|A>>>22,A=P}A>>>=22,c.words[S-10]=A,A===0&&c.length>10?c.length-=10:c.length-=9},G.prototype.imulK=function(c){c.words[c.length]=0,c.words[c.length+1]=0,c.length+=2;for(var d=0,p=0;p<c.length;p++){var v=c.words[p]|0;d+=v*977,c.words[p]=d&67108863,d=v*64+(d/67108864|0)}return c.words[c.length-1]===0&&(c.length--,c.words[c.length-1]===0&&c.length--),c};function Q(){j.call(this,"p224","ffffffff ffffffff ffffffff ffffffff 00000000 00000000 00000001")}s(Q,j);function U(){j.call(this,"p192","ffffffff ffffffff ffffffff fffffffe ffffffff ffffffff")}s(U,j);function $(){j.call(this,"25519","7fffffffffffffff ffffffffffffffff ffffffffffffffff ffffffffffffffed")}s($,j),$.prototype.imulK=function(c){for(var d=0,p=0;p<c.length;p++){var v=(c.words[p]|0)*19+d,S=v&67108863;v>>>=26,c.words[p]=S,d=v}return d!==0&&(c.words[c.length++]=d),c},i._prime=function(c){if(F[c])return F[c];var d;if(c==="k256")d=new G;else if(c==="p224")d=new Q;else if(c==="p192")d=new U;else if(c==="p25519")d=new $;else throw new Error("Unknown prime "+c);return F[c]=d,d};function z(b){if(typeof b=="string"){var c=i._prime(b);this.m=c.p,this.prime=c}else r(b.gtn(1),"modulus must be greater than 1"),this.m=b,this.prime=null}z.prototype._verify1=function(c){r(c.negative===0,"red works only with positives"),r(c.red,"red works only with red numbers")},z.prototype._verify2=function(c,d){r((c.negative|d.negative)===0,"red works only with positives"),r(c.red&&c.red===d.red,"red works only with red numbers")},z.prototype.imod=function(c){return this.prime?this.prime.ireduce(c)._forceRed(this):(m(c,c.umod(this.m)._forceRed(this)),c)},z.prototype.neg=function(c){return c.isZero()?c.clone():this.m.sub(c)._forceRed(this)},z.prototype.add=function(c,d){this._verify2(c,d);var p=c.add(d);return p.cmp(this.m)>=0&&p.isub(this.m),p._forceRed(this)},z.prototype.iadd=function(c,d){this._verify2(c,d);var p=c.iadd(d);return p.cmp(this.m)>=0&&p.isub(this.m),p},z.prototype.sub=function(c,d){this._verify2(c,d);var p=c.sub(d);return p.cmpn(0)<0&&p.iadd(this.m),p._forceRed(this)},z.prototype.isub=function(c,d){this._verify2(c,d);var p=c.isub(d);return p.cmpn(0)<0&&p.iadd(this.m),p},z.prototype.shl=function(c,d){return this._verify1(c),this.imod(c.ushln(d))},z.prototype.imul=function(c,d){return this._verify2(c,d),this.imod(c.imul(d))},z.prototype.mul=function(c,d){return this._verify2(c,d),this.imod(c.mul(d))},z.prototype.isqr=function(c){return this.imul(c,c.clone())},z.prototype.sqr=function(c){return this.mul(c,c)},z.prototype.sqrt=function(c){if(c.isZero())return c.clone();var d=this.m.andln(3);if(r(d%2===1),d===3){var p=this.m.add(new i(1)).iushrn(2);return this.pow(c,p)}for(var v=this.m.subn(1),S=0;!v.isZero()&&v.andln(1)===0;)S++,v.iushrn(1);r(!v.isZero());var A=new i(1).toRed(this),P=A.redNeg(),w=this.m.subn(1).iushrn(1),l=this.m.bitLength();for(l=new i(2*l*l).toRed(this);this.pow(l,w).cmp(P)!==0;)l.redIAdd(P);for(var _=this.pow(l,v),nt=this.pow(c,v.addn(1).iushrn(1)),J=this.pow(c,v),it=S;J.cmp(A)!==0;){for(var At=J,dt=0;At.cmp(A)!==0;dt++)At=At.redSqr();r(dt<it);var mt=this.pow(_,new i(1).iushln(it-dt-1));nt=nt.redMul(mt),_=mt.redSqr(),J=J.redMul(_),it=dt}return nt},z.prototype.invm=function(c){var d=c._invmp(this.m);return d.negative!==0?(d.negative=0,this.imod(d).redNeg()):this.imod(d)},z.prototype.pow=function(c,d){if(d.isZero())return new i(1).toRed(this);if(d.cmpn(1)===0)return c.clone();var p=4,v=new Array(1<<p);v[0]=new i(1).toRed(this),v[1]=c;for(var S=2;S<v.length;S++)v[S]=this.mul(v[S-1],c);var A=v[0],P=0,w=0,l=d.bitLength()%26;for(l===0&&(l=26),S=d.length-1;S>=0;S--){for(var _=d.words[S],nt=l-1;nt>=0;nt--){var J=_>>nt&1;if(A!==v[0]&&(A=this.sqr(A)),J===0&&P===0){w=0;continue}P<<=1,P|=J,w++,!(w!==p&&(S!==0||nt!==0))&&(A=this.mul(A,v[P]),w=0,P=0)}l=26}return A},z.prototype.convertTo=function(c){var d=c.umod(this.m);return d===c?d.clone():d},z.prototype.convertFrom=function(c){var d=c.clone();return d.red=null,d},i.mont=function(c){return new W(c)};function W(b){z.call(this,b),this.shift=this.m.bitLength(),this.shift%26!==0&&(this.shift+=26-this.shift%26),this.r=new i(1).iushln(this.shift),this.r2=this.imod(this.r.sqr()),this.rinv=this.r._invmp(this.m),this.minv=this.rinv.mul(this.r).isubn(1).div(this.m),this.minv=this.minv.umod(this.r),this.minv=this.r.sub(this.minv)}s(W,z),W.prototype.convertTo=function(c){return this.imod(c.ushln(this.shift))},W.prototype.convertFrom=function(c){var d=this.imod(c.mul(this.rinv));return d.red=null,d},W.prototype.imul=function(c,d){if(c.isZero()||d.isZero())return c.words[0]=0,c.length=1,c;var p=c.imul(d),v=p.maskn(this.shift).mul(this.minv).imaskn(this.shift).mul(this.m),S=p.isub(v).iushrn(this.shift),A=S;return S.cmp(this.m)>=0?A=S.isub(this.m):S.cmpn(0)<0&&(A=S.iadd(this.m)),A._forceRed(this)},W.prototype.mul=function(c,d){if(c.isZero()||d.isZero())return new i(0)._forceRed(this);var p=c.mul(d),v=p.maskn(this.shift).mul(this.minv).imaskn(this.shift).mul(this.m),S=p.isub(v).iushrn(this.shift),A=S;return S.cmp(this.m)>=0?A=S.isub(this.m):S.cmpn(0)<0&&(A=S.iadd(this.m)),A._forceRed(this)},W.prototype.invm=function(c){var d=this.imod(c._invmp(this.m).mul(this.r2));return d._forceRed(this)}})(n,je)})(Ds);var za=Ds.exports;const zo=ai(za);var ys={exports:{}};/*! safe-buffer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */(function(n,t){var e=ct,r=e.Buffer;function s(o,a){for(var h in o)a[h]=o[h]}r.from&&r.alloc&&r.allocUnsafe&&r.allocUnsafeSlow?n.exports=e:(s(e,t),t.Buffer=i);function i(o,a,h){return r(o,a,h)}i.prototype=Object.create(r.prototype),s(r,i),i.from=function(o,a,h){if(typeof o=="number")throw new TypeError("Argument must not be a number");return r(o,a,h)},i.alloc=function(o,a,h){if(typeof o!="number")throw new TypeError("Argument must be a number");var y=r(o);return a!==void 0?typeof h=="string"?y.fill(a,h):y.fill(a):y.fill(0),y},i.allocUnsafe=function(o){if(typeof o!="number")throw new TypeError("Argument must be a number");return r(o)},i.allocUnsafeSlow=function(o){if(typeof o!="number")throw new TypeError("Argument must be a number");return e.SlowBuffer(o)}})(ys,ys.exports);var Fa=ys.exports,Wn=Fa.Buffer;function Ff(n){if(n.length>=255)throw new TypeError("Alphabet too long");for(var t=new Uint8Array(256),e=0;e<t.length;e++)t[e]=255;for(var r=0;r<n.length;r++){var s=n.charAt(r),i=s.charCodeAt(0);if(t[i]!==255)throw new TypeError(s+" is ambiguous");t[i]=r}var o=n.length,a=n.charAt(0),h=Math.log(o)/Math.log(256),y=Math.log(256)/Math.log(o);function m(k){if((Array.isArray(k)||k instanceof Uint8Array)&&(k=Wn.from(k)),!Wn.isBuffer(k))throw new TypeError("Expected Buffer");if(k.length===0)return"";for(var R=0,B=0,I=0,C=k.length;I!==C&&k[I]===0;)I++,R++;for(var K=(C-I)*y+1>>>0,q=new Uint8Array(K);I!==C;){for(var V=k[I],F=0,j=K-1;(V!==0||F<B)&&j!==-1;j--,F++)V+=256*q[j]>>>0,q[j]=V%o>>>0,V=V/o>>>0;if(V!==0)throw new Error("Non-zero carry");B=F,I++}for(var G=K-B;G!==K&&q[G]===0;)G++;for(var Q=a.repeat(R);G<K;++G)Q+=n.charAt(q[G]);return Q}function E(k){if(typeof k!="string")throw new TypeError("Expected String");if(k.length===0)return Wn.alloc(0);for(var R=0,B=0,I=0;k[R]===a;)B++,R++;for(var C=(k.length-R)*h+1>>>0,K=new Uint8Array(C);R<k.length;){var q=k.charCodeAt(R);if(q>255)return;var V=t[q];if(V===255)return;for(var F=0,j=C-1;(V!==0||F<I)&&j!==-1;j--,F++)V+=o*K[j]>>>0,K[j]=V%256>>>0,V=V/256>>>0;if(V!==0)throw new Error("Non-zero carry");I=F,R++}for(var G=C-I;G!==C&&K[G]===0;)G++;var Q=Wn.allocUnsafe(B+(C-G));Q.fill(0,0,B);for(var U=B;G!==C;)Q[U++]=K[G++];return Q}function M(k){var R=E(k);if(R)return R;throw new Error("Non-base"+o+" character")}return{encode:m,decodeUnsafe:E,decode:M}}var Df=Ff,Kf=Df,qf="123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz",Wf=Kf(qf);const ke=ai(Wf),Fo=ma;var ve={},$n=Fa.Buffer;function $f(n){if(n.length>=255)throw new TypeError("Alphabet too long");for(var t=new Uint8Array(256),e=0;e<t.length;e++)t[e]=255;for(var r=0;r<n.length;r++){var s=n.charAt(r),i=s.charCodeAt(0);if(t[i]!==255)throw new TypeError(s+" is ambiguous");t[i]=r}var o=n.length,a=n.charAt(0),h=Math.log(o)/Math.log(256),y=Math.log(256)/Math.log(o);function m(k){if((Array.isArray(k)||k instanceof Uint8Array)&&(k=$n.from(k)),!$n.isBuffer(k))throw new TypeError("Expected Buffer");if(k.length===0)return"";for(var R=0,B=0,I=0,C=k.length;I!==C&&k[I]===0;)I++,R++;for(var K=(C-I)*y+1>>>0,q=new Uint8Array(K);I!==C;){for(var V=k[I],F=0,j=K-1;(V!==0||F<B)&&j!==-1;j--,F++)V+=256*q[j]>>>0,q[j]=V%o>>>0,V=V/o>>>0;if(V!==0)throw new Error("Non-zero carry");B=F,I++}for(var G=K-B;G!==K&&q[G]===0;)G++;for(var Q=a.repeat(R);G<K;++G)Q+=n.charAt(q[G]);return Q}function E(k){if(typeof k!="string")throw new TypeError("Expected String");if(k.length===0)return $n.alloc(0);for(var R=0,B=0,I=0;k[R]===a;)B++,R++;for(var C=(k.length-R)*h+1>>>0,K=new Uint8Array(C);R<k.length;){var q=k.charCodeAt(R);if(q>255)return;var V=t[q];if(V===255)return;for(var F=0,j=C-1;(V!==0||F<I)&&j!==-1;j--,F++)V+=o*K[j]>>>0,K[j]=V%256>>>0,V=V/256>>>0;if(V!==0)throw new Error("Non-zero carry");I=F,R++}for(var G=C-I;G!==C&&K[G]===0;)G++;var Q=$n.allocUnsafe(B+(C-G));Q.fill(0,0,B);for(var U=B;G!==C;)Q[U++]=K[G++];return Q}function M(k){var R=E(k);if(R)return R;throw new Error("Non-base"+o+" character")}return{encode:m,decodeUnsafe:E,decode:M}}var Hf=$f,Vf=Hf,jf="123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz",Gf=Vf(jf);function pr(n,t,e){return t<=n&&n<=e}function di(n){if(n===void 0)return{};if(n===Object(n))return n;throw TypeError("Could not convert argument to dictionary")}function Zf(n){for(var t=String(n),e=t.length,r=0,s=[];r<e;){var i=t.charCodeAt(r);if(i<55296||i>57343)s.push(i);else if(56320<=i&&i<=57343)s.push(65533);else if(55296<=i&&i<=56319)if(r===e-1)s.push(65533);else{var o=n.charCodeAt(r+1);if(56320<=o&&o<=57343){var a=i&1023,h=o&1023;s.push(65536+(a<<10)+h),r+=1}else s.push(65533)}r+=1}return s}function Yf(n){for(var t="",e=0;e<n.length;++e){var r=n[e];r<=65535?t+=String.fromCharCode(r):(r-=65536,t+=String.fromCharCode((r>>10)+55296,(r&1023)+56320))}return t}var Xn=-1;function Ks(n){this.tokens=[].slice.call(n)}Ks.prototype={endOfStream:function(){return!this.tokens.length},read:function(){return this.tokens.length?this.tokens.shift():Xn},prepend:function(n){if(Array.isArray(n))for(var t=n;t.length;)this.tokens.unshift(t.pop());else this.tokens.unshift(n)},push:function(n){if(Array.isArray(n))for(var t=n;t.length;)this.tokens.push(t.shift());else this.tokens.push(n)}};var on=-1;function ts(n,t){if(n)throw TypeError("Decoder error");return t||65533}var Qn="utf-8";function ti(n,t){if(!(this instanceof ti))return new ti(n,t);if(n=n!==void 0?String(n).toLowerCase():Qn,n!==Qn)throw new Error("Encoding not supported. Only utf-8 is supported");t=di(t),this._streaming=!1,this._BOMseen=!1,this._decoder=null,this._fatal=!!t.fatal,this._ignoreBOM=!!t.ignoreBOM,Object.defineProperty(this,"encoding",{value:"utf-8"}),Object.defineProperty(this,"fatal",{value:this._fatal}),Object.defineProperty(this,"ignoreBOM",{value:this._ignoreBOM})}ti.prototype={decode:function(t,e){var r;typeof t=="object"&&t instanceof ArrayBuffer?r=new Uint8Array(t):typeof t=="object"&&"buffer"in t&&t.buffer instanceof ArrayBuffer?r=new Uint8Array(t.buffer,t.byteOffset,t.byteLength):r=new Uint8Array(0),e=di(e),this._streaming||(this._decoder=new Jf({fatal:this._fatal}),this._BOMseen=!1),this._streaming=!!e.stream;for(var s=new Ks(r),i=[],o;!s.endOfStream()&&(o=this._decoder.handler(s,s.read()),o!==on);)o!==null&&(Array.isArray(o)?i.push.apply(i,o):i.push(o));if(!this._streaming){do{if(o=this._decoder.handler(s,s.read()),o===on)break;o!==null&&(Array.isArray(o)?i.push.apply(i,o):i.push(o))}while(!s.endOfStream());this._decoder=null}return i.length&&["utf-8"].indexOf(this.encoding)!==-1&&!this._ignoreBOM&&!this._BOMseen&&(i[0]===65279?(this._BOMseen=!0,i.shift()):this._BOMseen=!0),Yf(i)}};function ei(n,t){if(!(this instanceof ei))return new ei(n,t);if(n=n!==void 0?String(n).toLowerCase():Qn,n!==Qn)throw new Error("Encoding not supported. Only utf-8 is supported");t=di(t),this._streaming=!1,this._encoder=null,this._options={fatal:!!t.fatal},Object.defineProperty(this,"encoding",{value:"utf-8"})}ei.prototype={encode:function(t,e){t=t?String(t):"",e=di(e),this._streaming||(this._encoder=new Xf(this._options)),this._streaming=!!e.stream;for(var r=[],s=new Ks(Zf(t)),i;!s.endOfStream()&&(i=this._encoder.handler(s,s.read()),i!==on);)Array.isArray(i)?r.push.apply(r,i):r.push(i);if(!this._streaming){for(;i=this._encoder.handler(s,s.read()),i!==on;)Array.isArray(i)?r.push.apply(r,i):r.push(i);this._encoder=null}return new Uint8Array(r)}};function Jf(n){var t=n.fatal,e=0,r=0,s=0,i=128,o=191;this.handler=function(a,h){if(h===Xn&&s!==0)return s=0,ts(t);if(h===Xn)return on;if(s===0){if(pr(h,0,127))return h;if(pr(h,194,223))s=1,e=h-192;else if(pr(h,224,239))h===224&&(i=160),h===237&&(o=159),s=2,e=h-224;else if(pr(h,240,244))h===240&&(i=144),h===244&&(o=143),s=3,e=h-240;else return ts(t);return e=e<<6*s,null}if(!pr(h,i,o))return e=s=r=0,i=128,o=191,a.prepend(h),ts(t);if(i=128,o=191,r+=1,e+=h-128<<6*(s-r),r!==s)return null;var y=e;return e=s=r=0,y}}function Xf(n){n.fatal,this.handler=function(t,e){if(e===Xn)return on;if(pr(e,0,127))return e;var r,s;pr(e,128,2047)?(r=1,s=192):pr(e,2048,65535)?(r=2,s=224):pr(e,65536,1114111)&&(r=3,s=240);for(var i=[(e>>6*r)+s];r>0;){var o=e>>6*(r-1);i.push(128|o&63),r-=1}return i}}const Qf=Object.freeze(Object.defineProperty({__proto__:null,TextDecoder:ti,TextEncoder:ei},Symbol.toStringTag,{value:"Module"})),tl=Is(Qf);var el=je&&je.__createBinding||(Object.create?function(n,t,e,r){r===void 0&&(r=e),Object.defineProperty(n,r,{enumerable:!0,get:function(){return t[e]}})}:function(n,t,e,r){r===void 0&&(r=e),n[r]=t[e]}),rl=je&&je.__setModuleDefault||(Object.create?function(n,t){Object.defineProperty(n,"default",{enumerable:!0,value:t})}:function(n,t){n.default=t}),or=je&&je.__decorate||function(n,t,e,r){var s=arguments.length,i=s<3?t:r===null?r=Object.getOwnPropertyDescriptor(t,e):r,o;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")i=Reflect.decorate(n,t,e,r);else for(var a=n.length-1;a>=0;a--)(o=n[a])&&(i=(s<3?o(i):s>3?o(t,e,i):o(t,e))||i);return s>3&&i&&Object.defineProperty(t,e,i),i},nl=je&&je.__importStar||function(n){if(n&&n.__esModule)return n;var t={};if(n!=null)for(var e in n)e!=="default"&&Object.hasOwnProperty.call(n,e)&&el(t,n,e);return rl(t,n),t},Da=je&&je.__importDefault||function(n){return n&&n.__esModule?n:{default:n}};Object.defineProperty(ve,"__esModule",{value:!0});var Ka=ve.deserializeUnchecked=ja=ve.deserialize=Va=ve.serialize=ve.BinaryReader=ve.BinaryWriter=ve.BorshError=ve.baseDecode=ve.baseEncode=void 0;const Sr=Da(za),qa=Da(Gf),il=nl(tl),sl=typeof TextDecoder!="function"?il.TextDecoder:TextDecoder,ol=new sl("utf-8",{fatal:!0});function al(n){return typeof n=="string"&&(n=Buffer.from(n,"utf8")),qa.default.encode(Buffer.from(n))}ve.baseEncode=al;function cl(n){return Buffer.from(qa.default.decode(n))}ve.baseDecode=cl;const es=1024;class Te extends Error{constructor(t){super(t),this.fieldPath=[],this.originalMessage=t}addToFieldPath(t){this.fieldPath.splice(0,0,t),this.message=this.originalMessage+": "+this.fieldPath.join(".")}}ve.BorshError=Te;class Wa{constructor(){this.buf=Buffer.alloc(es),this.length=0}maybeResize(){this.buf.length<16+this.length&&(this.buf=Buffer.concat([this.buf,Buffer.alloc(es)]))}writeU8(t){this.maybeResize(),this.buf.writeUInt8(t,this.length),this.length+=1}writeU16(t){this.maybeResize(),this.buf.writeUInt16LE(t,this.length),this.length+=2}writeU32(t){this.maybeResize(),this.buf.writeUInt32LE(t,this.length),this.length+=4}writeU64(t){this.maybeResize(),this.writeBuffer(Buffer.from(new Sr.default(t).toArray("le",8)))}writeU128(t){this.maybeResize(),this.writeBuffer(Buffer.from(new Sr.default(t).toArray("le",16)))}writeU256(t){this.maybeResize(),this.writeBuffer(Buffer.from(new Sr.default(t).toArray("le",32)))}writeU512(t){this.maybeResize(),this.writeBuffer(Buffer.from(new Sr.default(t).toArray("le",64)))}writeBuffer(t){this.buf=Buffer.concat([Buffer.from(this.buf.subarray(0,this.length)),t,Buffer.alloc(es)]),this.length+=t.length}writeString(t){this.maybeResize();const e=Buffer.from(t,"utf8");this.writeU32(e.length),this.writeBuffer(e)}writeFixedArray(t){this.writeBuffer(Buffer.from(t))}writeArray(t,e){this.maybeResize(),this.writeU32(t.length);for(const r of t)this.maybeResize(),e(r)}toArray(){return this.buf.subarray(0,this.length)}}ve.BinaryWriter=Wa;function ar(n,t,e){const r=e.value;e.value=function(...s){try{return r.apply(this,s)}catch(i){if(i instanceof RangeError){const o=i.code;if(["ERR_BUFFER_OUT_OF_BOUNDS","ERR_OUT_OF_RANGE"].indexOf(o)>=0)throw new Te("Reached the end of buffer when deserializing")}throw i}}}class De{constructor(t){this.buf=t,this.offset=0}readU8(){const t=this.buf.readUInt8(this.offset);return this.offset+=1,t}readU16(){const t=this.buf.readUInt16LE(this.offset);return this.offset+=2,t}readU32(){const t=this.buf.readUInt32LE(this.offset);return this.offset+=4,t}readU64(){const t=this.readBuffer(8);return new Sr.default(t,"le")}readU128(){const t=this.readBuffer(16);return new Sr.default(t,"le")}readU256(){const t=this.readBuffer(32);return new Sr.default(t,"le")}readU512(){const t=this.readBuffer(64);return new Sr.default(t,"le")}readBuffer(t){if(this.offset+t>this.buf.length)throw new Te(`Expected buffer length ${t} isn't within bounds`);const e=this.buf.slice(this.offset,this.offset+t);return this.offset+=t,e}readString(){const t=this.readU32(),e=this.readBuffer(t);try{return ol.decode(e)}catch(r){throw new Te(`Error decoding UTF-8 string: ${r}`)}}readFixedArray(t){return new Uint8Array(this.readBuffer(t))}readArray(t){const e=this.readU32(),r=Array();for(let s=0;s<e;++s)r.push(t());return r}}or([ar],De.prototype,"readU8",null);or([ar],De.prototype,"readU16",null);or([ar],De.prototype,"readU32",null);or([ar],De.prototype,"readU64",null);or([ar],De.prototype,"readU128",null);or([ar],De.prototype,"readU256",null);or([ar],De.prototype,"readU512",null);or([ar],De.prototype,"readString",null);or([ar],De.prototype,"readFixedArray",null);or([ar],De.prototype,"readArray",null);ve.BinaryReader=De;function $a(n){return n.charAt(0).toUpperCase()+n.slice(1)}function Rr(n,t,e,r,s){try{if(typeof r=="string")s[`write${$a(r)}`](e);else if(r instanceof Array)if(typeof r[0]=="number"){if(e.length!==r[0])throw new Te(`Expecting byte array of length ${r[0]}, but got ${e.length} bytes`);s.writeFixedArray(e)}else if(r.length===2&&typeof r[1]=="number"){if(e.length!==r[1])throw new Te(`Expecting byte array of length ${r[1]}, but got ${e.length} bytes`);for(let i=0;i<r[1];i++)Rr(n,null,e[i],r[0],s)}else s.writeArray(e,i=>{Rr(n,t,i,r[0],s)});else if(r.kind!==void 0)switch(r.kind){case"option":{e==null?s.writeU8(0):(s.writeU8(1),Rr(n,t,e,r.type,s));break}case"map":{s.writeU32(e.size),e.forEach((i,o)=>{Rr(n,t,o,r.key,s),Rr(n,t,i,r.value,s)});break}default:throw new Te(`FieldType ${r} unrecognized`)}else Ha(n,e,s)}catch(i){throw i instanceof Te&&i.addToFieldPath(t),i}}function Ha(n,t,e){if(typeof t.borshSerialize=="function"){t.borshSerialize(e);return}const r=n.get(t.constructor);if(!r)throw new Te(`Class ${t.constructor.name} is missing in schema`);if(r.kind==="struct")r.fields.map(([s,i])=>{Rr(n,s,t[s],i,e)});else if(r.kind==="enum"){const s=t[r.field];for(let i=0;i<r.values.length;++i){const[o,a]=r.values[i];if(o===s){e.writeU8(i),Rr(n,o,t[o],a,e);break}}}else throw new Te(`Unexpected schema kind: ${r.kind} for ${t.constructor.name}`)}function ul(n,t,e=Wa){const r=new e;return Ha(n,t,r),r.toArray()}var Va=ve.serialize=ul;function Mr(n,t,e,r){try{if(typeof e=="string")return r[`read${$a(e)}`]();if(e instanceof Array){if(typeof e[0]=="number")return r.readFixedArray(e[0]);if(typeof e[1]=="number"){const s=[];for(let i=0;i<e[1];i++)s.push(Mr(n,null,e[0],r));return s}else return r.readArray(()=>Mr(n,t,e[0],r))}if(e.kind==="option")return r.readU8()?Mr(n,t,e.type,r):void 0;if(e.kind==="map"){let s=new Map;const i=r.readU32();for(let o=0;o<i;o++){const a=Mr(n,t,e.key,r),h=Mr(n,t,e.value,r);s.set(a,h)}return s}return qs(n,e,r)}catch(s){throw s instanceof Te&&s.addToFieldPath(t),s}}function qs(n,t,e){if(typeof t.borshDeserialize=="function")return t.borshDeserialize(e);const r=n.get(t);if(!r)throw new Te(`Class ${t.name} is missing in schema`);if(r.kind==="struct"){const s={};for(const[i,o]of n.get(t).fields)s[i]=Mr(n,i,o,e);return new t(s)}if(r.kind==="enum"){const s=e.readU8();if(s>=r.values.length)throw new Te(`Enum index: ${s} is out of range`);const[i,o]=r.values[s],a=Mr(n,i,o,e);return new t({[i]:a})}throw new Te(`Unexpected schema kind: ${r.kind} for ${t.constructor.name}`)}function fl(n,t,e,r=De){const s=new r(e),i=qs(n,t,s);if(s.offset<e.length)throw new Te(`Unexpected ${e.length-s.offset} bytes after deserialized data`);return i}var ja=ve.deserialize=fl;function ll(n,t,e,r=De){const s=new r(e);return qs(n,t,s)}Ka=ve.deserializeUnchecked=ll;var N={};Object.defineProperty(N,"__esModule",{value:!0});N.s16=N.s8=N.nu64be=N.u48be=N.u40be=N.u32be=N.u24be=N.u16be=be=N.nu64=N.u48=N.u40=st=N.u32=N.u24=We=N.u16=vt=N.u8=_r=N.offset=N.greedy=N.Constant=N.UTF8=N.CString=N.Blob=N.Boolean=N.BitField=N.BitStructure=N.VariantLayout=N.Union=N.UnionLayoutDiscriminator=N.UnionDiscriminator=N.Structure=N.Sequence=N.DoubleBE=N.Double=N.FloatBE=N.Float=N.NearInt64BE=N.NearInt64=N.NearUInt64BE=N.NearUInt64=N.IntBE=N.Int=N.UIntBE=N.UInt=N.OffsetLayout=N.GreedyCount=N.ExternalLayout=N.bindConstructorLayout=N.nameWithProperty=N.Layout=N.uint8ArrayToBuffer=N.checkUint8Array=void 0;N.constant=N.utf8=N.cstr=Ot=N.blob=N.unionLayoutDiscriminator=N.union=xe=N.seq=N.bits=ot=N.struct=N.f64be=N.f64=N.f32be=N.f32=N.ns64be=N.s48be=N.s40be=N.s32be=N.s24be=N.s16be=ze=N.ns64=N.s48=N.s40=N.s32=N.s24=void 0;const Ws=ct;function fn(n){if(!(n instanceof Uint8Array))throw new TypeError("b must be a Uint8Array")}N.checkUint8Array=fn;function St(n){return fn(n),Ws.Buffer.from(n.buffer,n.byteOffset,n.length)}N.uint8ArrayToBuffer=St;class It{constructor(t,e){if(!Number.isInteger(t))throw new TypeError("span must be an integer");this.span=t,this.property=e}makeDestinationObject(){return{}}getSpan(t,e){if(0>this.span)throw new RangeError("indeterminate span");return this.span}replicate(t){const e=Object.create(this.constructor.prototype);return Object.assign(e,this),e.property=t,e}fromArray(t){}}N.Layout=It;function $s(n,t){return t.property?n+"["+t.property+"]":n}N.nameWithProperty=$s;function hl(n,t){if(typeof n!="function")throw new TypeError("Class must be constructor");if(Object.prototype.hasOwnProperty.call(n,"layout_"))throw new Error("Class is already bound to a layout");if(!(t&&t instanceof It))throw new TypeError("layout must be a Layout");if(Object.prototype.hasOwnProperty.call(t,"boundConstructor_"))throw new Error("layout is already bound to a constructor");n.layout_=t,t.boundConstructor_=n,t.makeDestinationObject=()=>new n,Object.defineProperty(n.prototype,"encode",{value(e,r){return t.encode(this,e,r)},writable:!0}),Object.defineProperty(n,"decode",{value(e,r){return t.decode(e,r)},writable:!0})}N.bindConstructorLayout=hl;class Oe extends It{isCount(){throw new Error("ExternalLayout is abstract")}}N.ExternalLayout=Oe;class Ga extends Oe{constructor(t=1,e){if(!Number.isInteger(t)||0>=t)throw new TypeError("elementSpan must be a (positive) integer");super(-1,e),this.elementSpan=t}isCount(){return!0}decode(t,e=0){fn(t);const r=t.length-e;return Math.floor(r/this.elementSpan)}encode(t,e,r){return 0}}N.GreedyCount=Ga;class Hs extends Oe{constructor(t,e=0,r){if(!(t instanceof It))throw new TypeError("layout must be a Layout");if(!Number.isInteger(e))throw new TypeError("offset must be integer or undefined");super(t.span,r||t.property),this.layout=t,this.offset=e}isCount(){return this.layout instanceof $e||this.layout instanceof Ve}decode(t,e=0){return this.layout.decode(t,e+this.offset)}encode(t,e,r=0){return this.layout.encode(t,e,r+this.offset)}}N.OffsetLayout=Hs;class $e extends It{constructor(t,e){if(super(t,e),6<this.span)throw new RangeError("span must not exceed 6 bytes")}decode(t,e=0){return St(t).readUIntLE(e,this.span)}encode(t,e,r=0){return St(e).writeUIntLE(t,r,this.span),this.span}}N.UInt=$e;class Ve extends It{constructor(t,e){if(super(t,e),6<this.span)throw new RangeError("span must not exceed 6 bytes")}decode(t,e=0){return St(t).readUIntBE(e,this.span)}encode(t,e,r=0){return St(e).writeUIntBE(t,r,this.span),this.span}}N.UIntBE=Ve;class qr extends It{constructor(t,e){if(super(t,e),6<this.span)throw new RangeError("span must not exceed 6 bytes")}decode(t,e=0){return St(t).readIntLE(e,this.span)}encode(t,e,r=0){return St(e).writeIntLE(t,r,this.span),this.span}}N.Int=qr;class ln extends It{constructor(t,e){if(super(t,e),6<this.span)throw new RangeError("span must not exceed 6 bytes")}decode(t,e=0){return St(t).readIntBE(e,this.span)}encode(t,e,r=0){return St(e).writeIntBE(t,r,this.span),this.span}}N.IntBE=ln;const ms=Math.pow(2,32);function pi(n){const t=Math.floor(n/ms),e=n-t*ms;return{hi32:t,lo32:e}}function gi(n,t){return n*ms+t}class Za extends It{constructor(t){super(8,t)}decode(t,e=0){const r=St(t),s=r.readUInt32LE(e),i=r.readUInt32LE(e+4);return gi(i,s)}encode(t,e,r=0){const s=pi(t),i=St(e);return i.writeUInt32LE(s.lo32,r),i.writeUInt32LE(s.hi32,r+4),8}}N.NearUInt64=Za;class Ya extends It{constructor(t){super(8,t)}decode(t,e=0){const r=St(t),s=r.readUInt32BE(e),i=r.readUInt32BE(e+4);return gi(s,i)}encode(t,e,r=0){const s=pi(t),i=St(e);return i.writeUInt32BE(s.hi32,r),i.writeUInt32BE(s.lo32,r+4),8}}N.NearUInt64BE=Ya;class Ja extends It{constructor(t){super(8,t)}decode(t,e=0){const r=St(t),s=r.readUInt32LE(e),i=r.readInt32LE(e+4);return gi(i,s)}encode(t,e,r=0){const s=pi(t),i=St(e);return i.writeUInt32LE(s.lo32,r),i.writeInt32LE(s.hi32,r+4),8}}N.NearInt64=Ja;class Xa extends It{constructor(t){super(8,t)}decode(t,e=0){const r=St(t),s=r.readInt32BE(e),i=r.readUInt32BE(e+4);return gi(s,i)}encode(t,e,r=0){const s=pi(t),i=St(e);return i.writeInt32BE(s.hi32,r),i.writeUInt32BE(s.lo32,r+4),8}}N.NearInt64BE=Xa;class Qa extends It{constructor(t){super(4,t)}decode(t,e=0){return St(t).readFloatLE(e)}encode(t,e,r=0){return St(e).writeFloatLE(t,r),4}}N.Float=Qa;class tc extends It{constructor(t){super(4,t)}decode(t,e=0){return St(t).readFloatBE(e)}encode(t,e,r=0){return St(e).writeFloatBE(t,r),4}}N.FloatBE=tc;class ec extends It{constructor(t){super(8,t)}decode(t,e=0){return St(t).readDoubleLE(e)}encode(t,e,r=0){return St(e).writeDoubleLE(t,r),8}}N.Double=ec;class rc extends It{constructor(t){super(8,t)}decode(t,e=0){return St(t).readDoubleBE(e)}encode(t,e,r=0){return St(e).writeDoubleBE(t,r),8}}N.DoubleBE=rc;class nc extends It{constructor(t,e,r){if(!(t instanceof It))throw new TypeError("elementLayout must be a Layout");if(!(e instanceof Oe&&e.isCount()||Number.isInteger(e)&&0<=e))throw new TypeError("count must be non-negative integer or an unsigned integer ExternalLayout");let s=-1;!(e instanceof Oe)&&0<t.span&&(s=e*t.span),super(s,r),this.elementLayout=t,this.count=e}getSpan(t,e=0){if(0<=this.span)return this.span;let r=0,s=this.count;if(s instanceof Oe&&(s=s.decode(t,e)),0<this.elementLayout.span)r=s*this.elementLayout.span;else{let i=0;for(;i<s;)r+=this.elementLayout.getSpan(t,e+r),++i}return r}decode(t,e=0){const r=[];let s=0,i=this.count;for(i instanceof Oe&&(i=i.decode(t,e));s<i;)r.push(this.elementLayout.decode(t,e)),e+=this.elementLayout.getSpan(t,e),s+=1;return r}encode(t,e,r=0){const s=this.elementLayout,i=t.reduce((o,a)=>o+s.encode(a,e,r+o),0);return this.count instanceof Oe&&this.count.encode(t.length,e,r),i}}N.Sequence=nc;class ic extends It{constructor(t,e,r){if(!(Array.isArray(t)&&t.reduce((i,o)=>i&&o instanceof It,!0)))throw new TypeError("fields must be array of Layout instances");typeof e=="boolean"&&r===void 0&&(r=e,e=void 0);for(const i of t)if(0>i.span&&i.property===void 0)throw new Error("fields cannot contain unnamed variable-length layout");let s=-1;try{s=t.reduce((i,o)=>i+o.getSpan(),0)}catch{}super(s,e),this.fields=t,this.decodePrefixes=!!r}getSpan(t,e=0){if(0<=this.span)return this.span;let r=0;try{r=this.fields.reduce((s,i)=>{const o=i.getSpan(t,e);return e+=o,s+o},0)}catch{throw new RangeError("indeterminate span")}return r}decode(t,e=0){fn(t);const r=this.makeDestinationObject();for(const s of this.fields)if(s.property!==void 0&&(r[s.property]=s.decode(t,e)),e+=s.getSpan(t,e),this.decodePrefixes&&t.length===e)break;return r}encode(t,e,r=0){const s=r;let i=0,o=0;for(const a of this.fields){let h=a.span;if(o=0<h?h:0,a.property!==void 0){const y=t[a.property];y!==void 0&&(o=a.encode(y,e,r),0>h&&(h=a.getSpan(e,r)))}i=r,r+=h}return i+o-s}fromArray(t){const e=this.makeDestinationObject();for(const r of this.fields)r.property!==void 0&&0<t.length&&(e[r.property]=t.shift());return e}layoutFor(t){if(typeof t!="string")throw new TypeError("property must be string");for(const e of this.fields)if(e.property===t)return e}offsetOf(t){if(typeof t!="string")throw new TypeError("property must be string");let e=0;for(const r of this.fields){if(r.property===t)return e;0>r.span?e=-1:0<=e&&(e+=r.span)}}}N.Structure=ic;class Vs{constructor(t){this.property=t}decode(t,e){throw new Error("UnionDiscriminator is abstract")}encode(t,e,r){throw new Error("UnionDiscriminator is abstract")}}N.UnionDiscriminator=Vs;class ri extends Vs{constructor(t,e){if(!(t instanceof Oe&&t.isCount()))throw new TypeError("layout must be an unsigned integer ExternalLayout");super(e||t.property||"variant"),this.layout=t}decode(t,e){return this.layout.decode(t,e)}encode(t,e,r){return this.layout.encode(t,e,r)}}N.UnionLayoutDiscriminator=ri;class js extends It{constructor(t,e,r){let s;if(t instanceof $e||t instanceof Ve)s=new ri(new Hs(t));else if(t instanceof Oe&&t.isCount())s=new ri(t);else if(t instanceof Vs)s=t;else throw new TypeError("discr must be a UnionDiscriminator or an unsigned integer layout");if(e===void 0&&(e=null),!(e===null||e instanceof It))throw new TypeError("defaultLayout must be null or a Layout");if(e!==null){if(0>e.span)throw new Error("defaultLayout must have constant span");e.property===void 0&&(e=e.replicate("content"))}let i=-1;e&&(i=e.span,0<=i&&(t instanceof $e||t instanceof Ve)&&(i+=s.layout.span)),super(i,r),this.discriminator=s,this.usesPrefixDiscriminator=t instanceof $e||t instanceof Ve,this.defaultLayout=e,this.registry={};let o=this.defaultGetSourceVariant.bind(this);this.getSourceVariant=function(a){return o(a)},this.configGetSourceVariant=function(a){o=a.bind(this)}}getSpan(t,e=0){if(0<=this.span)return this.span;const r=this.getVariant(t,e);if(!r)throw new Error("unable to determine span for unrecognized variant");return r.getSpan(t,e)}defaultGetSourceVariant(t){if(Object.prototype.hasOwnProperty.call(t,this.discriminator.property)){if(this.defaultLayout&&this.defaultLayout.property&&Object.prototype.hasOwnProperty.call(t,this.defaultLayout.property))return;const e=this.registry[t[this.discriminator.property]];if(e&&(!e.layout||e.property&&Object.prototype.hasOwnProperty.call(t,e.property)))return e}else for(const e in this.registry){const r=this.registry[e];if(r.property&&Object.prototype.hasOwnProperty.call(t,r.property))return r}throw new Error("unable to infer src variant")}decode(t,e=0){let r;const s=this.discriminator,i=s.decode(t,e),o=this.registry[i];if(o===void 0){const a=this.defaultLayout;let h=0;this.usesPrefixDiscriminator&&(h=s.layout.span),r=this.makeDestinationObject(),r[s.property]=i,r[a.property]=a.decode(t,e+h)}else r=o.decode(t,e);return r}encode(t,e,r=0){const s=this.getSourceVariant(t);if(s===void 0){const i=this.discriminator,o=this.defaultLayout;let a=0;return this.usesPrefixDiscriminator&&(a=i.layout.span),i.encode(t[i.property],e,r),a+o.encode(t[o.property],e,r+a)}return s.encode(t,e,r)}addVariant(t,e,r){const s=new sc(this,t,e,r);return this.registry[t]=s,s}getVariant(t,e=0){let r;return t instanceof Uint8Array?r=this.discriminator.decode(t,e):r=t,this.registry[r]}}N.Union=js;class sc extends It{constructor(t,e,r,s){if(!(t instanceof js))throw new TypeError("union must be a Union");if(!Number.isInteger(e)||0>e)throw new TypeError("variant must be a (non-negative) integer");if(typeof r=="string"&&s===void 0&&(s=r,r=null),r){if(!(r instanceof It))throw new TypeError("layout must be a Layout");if(t.defaultLayout!==null&&0<=r.span&&r.span>t.defaultLayout.span)throw new Error("variant span exceeds span of containing union");if(typeof s!="string")throw new TypeError("variant must have a String property")}let i=t.span;0>t.span&&(i=r?r.span:0,0<=i&&t.usesPrefixDiscriminator&&(i+=t.discriminator.layout.span)),super(i,s),this.union=t,this.variant=e,this.layout=r||null}getSpan(t,e=0){if(0<=this.span)return this.span;let r=0;this.union.usesPrefixDiscriminator&&(r=this.union.discriminator.layout.span);let s=0;return this.layout&&(s=this.layout.getSpan(t,e+r)),r+s}decode(t,e=0){const r=this.makeDestinationObject();if(this!==this.union.getVariant(t,e))throw new Error("variant mismatch");let s=0;return this.union.usesPrefixDiscriminator&&(s=this.union.discriminator.layout.span),this.layout?r[this.property]=this.layout.decode(t,e+s):this.property?r[this.property]=!0:this.union.usesPrefixDiscriminator&&(r[this.union.discriminator.property]=this.variant),r}encode(t,e,r=0){let s=0;if(this.union.usesPrefixDiscriminator&&(s=this.union.discriminator.layout.span),this.layout&&!Object.prototype.hasOwnProperty.call(t,this.property))throw new TypeError("variant lacks property "+this.property);this.union.discriminator.encode(this.variant,e,r);let i=s;if(this.layout&&(this.layout.encode(t[this.property],e,r+s),i+=this.layout.getSpan(e,r+s),0<=this.union.span&&i>this.union.span))throw new Error("encoded variant overruns containing union");return i}fromArray(t){if(this.layout)return this.layout.fromArray(t)}}N.VariantLayout=sc;function Xr(n){return 0>n&&(n+=4294967296),n}class Gs extends It{constructor(t,e,r){if(!(t instanceof $e||t instanceof Ve))throw new TypeError("word must be a UInt or UIntBE layout");if(typeof e=="string"&&r===void 0&&(r=e,e=!1),4<t.span)throw new RangeError("word cannot exceed 32 bits");super(t.span,r),this.word=t,this.msb=!!e,this.fields=[];let s=0;this._packedSetValue=function(i){return s=Xr(i),this},this._packedGetValue=function(){return s}}decode(t,e=0){const r=this.makeDestinationObject(),s=this.word.decode(t,e);this._packedSetValue(s);for(const i of this.fields)i.property!==void 0&&(r[i.property]=i.decode(t));return r}encode(t,e,r=0){const s=this.word.decode(e,r);this._packedSetValue(s);for(const i of this.fields)if(i.property!==void 0){const o=t[i.property];o!==void 0&&i.encode(o)}return this.word.encode(this._packedGetValue(),e,r)}addField(t,e){const r=new Zs(this,t,e);return this.fields.push(r),r}addBoolean(t){const e=new oc(this,t);return this.fields.push(e),e}fieldFor(t){if(typeof t!="string")throw new TypeError("property must be string");for(const e of this.fields)if(e.property===t)return e}}N.BitStructure=Gs;class Zs{constructor(t,e,r){if(!(t instanceof Gs))throw new TypeError("container must be a BitStructure");if(!Number.isInteger(e)||0>=e)throw new TypeError("bits must be positive integer");const s=8*t.span,i=t.fields.reduce((o,a)=>o+a.bits,0);if(e+i>s)throw new Error("bits too long for span remainder ("+(s-i)+" of "+s+" remain)");this.container=t,this.bits=e,this.valueMask=(1<<e)-1,e===32&&(this.valueMask=4294967295),this.start=i,this.container.msb&&(this.start=s-i-e),this.wordMask=Xr(this.valueMask<<this.start),this.property=r}decode(t,e){const r=this.container._packedGetValue();return Xr(r&this.wordMask)>>>this.start}encode(t){if(typeof t!="number"||!Number.isInteger(t)||t!==Xr(t&this.valueMask))throw new TypeError($s("BitField.encode",this)+" value must be integer not exceeding "+this.valueMask);const e=this.container._packedGetValue(),r=Xr(t<<this.start);this.container._packedSetValue(Xr(e&~this.wordMask)|r)}}N.BitField=Zs;let oc=class extends Zs{constructor(t,e){super(t,1,e)}decode(t,e){return!!super.decode(t,e)}encode(t){typeof t=="boolean"&&(t=+t),super.encode(t)}};N.Boolean=oc;class ac extends It{constructor(t,e){if(!(t instanceof Oe&&t.isCount()||Number.isInteger(t)&&0<=t))throw new TypeError("length must be positive integer or an unsigned integer ExternalLayout");let r=-1;t instanceof Oe||(r=t),super(r,e),this.length=t}getSpan(t,e){let r=this.span;return 0>r&&(r=this.length.decode(t,e)),r}decode(t,e=0){let r=this.span;return 0>r&&(r=this.length.decode(t,e)),St(t).slice(e,e+r)}encode(t,e,r){let s=this.length;if(this.length instanceof Oe&&(s=t.length),!(t instanceof Uint8Array&&s===t.length))throw new TypeError($s("Blob.encode",this)+" requires (length "+s+") Uint8Array as src");if(r+s>e.length)throw new RangeError("encoding overruns Uint8Array");const i=St(t);return St(e).write(i.toString("hex"),r,s,"hex"),this.length instanceof Oe&&this.length.encode(s,e,r),s}}N.Blob=ac;class cc extends It{constructor(t){super(-1,t)}getSpan(t,e=0){fn(t);let r=e;for(;r<t.length&&t[r]!==0;)r+=1;return 1+r-e}decode(t,e=0){const r=this.getSpan(t,e);return St(t).slice(e,e+r-1).toString("utf-8")}encode(t,e,r=0){typeof t!="string"&&(t=String(t));const s=Ws.Buffer.from(t,"utf8"),i=s.length;if(r+i>e.length)throw new RangeError("encoding overruns Buffer");const o=St(e);return s.copy(o,r),o[r+i]=0,i+1}}N.CString=cc;class uc extends It{constructor(t,e){if(typeof t=="string"&&e===void 0&&(e=t,t=void 0),t===void 0)t=-1;else if(!Number.isInteger(t))throw new TypeError("maxSpan must be an integer");super(-1,e),this.maxSpan=t}getSpan(t,e=0){return fn(t),t.length-e}decode(t,e=0){const r=this.getSpan(t,e);if(0<=this.maxSpan&&this.maxSpan<r)throw new RangeError("text length exceeds maxSpan");return St(t).slice(e,e+r).toString("utf-8")}encode(t,e,r=0){typeof t!="string"&&(t=String(t));const s=Ws.Buffer.from(t,"utf8"),i=s.length;if(0<=this.maxSpan&&this.maxSpan<i)throw new RangeError("text length exceeds maxSpan");if(r+i>e.length)throw new RangeError("encoding overruns Buffer");return s.copy(St(e),r),i}}N.UTF8=uc;class fc extends It{constructor(t,e){super(0,e),this.value=t}decode(t,e){return this.value}encode(t,e,r){return 0}}N.Constant=fc;N.greedy=(n,t)=>new Ga(n,t);var _r=N.offset=(n,t,e)=>new Hs(n,t,e),vt=N.u8=n=>new $e(1,n),We=N.u16=n=>new $e(2,n);N.u24=n=>new $e(3,n);var st=N.u32=n=>new $e(4,n);N.u40=n=>new $e(5,n);N.u48=n=>new $e(6,n);var be=N.nu64=n=>new Za(n);N.u16be=n=>new Ve(2,n);N.u24be=n=>new Ve(3,n);N.u32be=n=>new Ve(4,n);N.u40be=n=>new Ve(5,n);N.u48be=n=>new Ve(6,n);N.nu64be=n=>new Ya(n);N.s8=n=>new qr(1,n);N.s16=n=>new qr(2,n);N.s24=n=>new qr(3,n);N.s32=n=>new qr(4,n);N.s40=n=>new qr(5,n);N.s48=n=>new qr(6,n);var ze=N.ns64=n=>new Ja(n);N.s16be=n=>new ln(2,n);N.s24be=n=>new ln(3,n);N.s32be=n=>new ln(4,n);N.s40be=n=>new ln(5,n);N.s48be=n=>new ln(6,n);N.ns64be=n=>new Xa(n);N.f32=n=>new Qa(n);N.f32be=n=>new tc(n);N.f64=n=>new ec(n);N.f64be=n=>new rc(n);var ot=N.struct=(n,t,e)=>new ic(n,t,e);N.bits=(n,t,e)=>new Gs(n,t,e);var xe=N.seq=(n,t,e)=>new nc(n,t,e);N.union=(n,t,e)=>new js(n,t,e);N.unionLayoutDiscriminator=(n,t)=>new ri(n,t);var Ot=N.blob=(n,t)=>new ac(n,t);N.cstr=n=>new cc(n);N.utf8=(n,t)=>new uc(n,t);N.constant=(n,t)=>new fc(n,t);var dl=8078e3,pl=8078001,gl=8078004,yl=8078005,ml=8078006,wl=8078011;function lc(n){return Array.isArray(n)?"%5B"+n.map(lc).join("%2C%20")+"%5D":typeof n=="bigint"?`${n}n`:encodeURIComponent(String(n!=null&&Object.getPrototypeOf(n)===null?{...n}:n))}function bl([n,t]){return`${n}=${lc(t)}`}function vl(n){const t=Object.entries(n).map(bl).join("&");return btoa(t)}function kl(n,t={}){{let e=`Solana error #${n}; Decode this error by running \`npx @solana/errors decode -- ${n}`;return Object.keys(t).length&&(e+=` '${vl(t)}'`),`${e}\``}}var tn=class extends Error{constructor(...[t,e]){let r,s;if(e){const{cause:o,...a}=e;o&&(s={cause:o}),Object.keys(a).length>0&&(r=a)}const i=kl(t,r);super(i,s);de(this,"cause",this.cause);de(this,"context");this.context={__code:t,...r},this.name="SolanaError"}};function xl(n,t){return"fixedSize"in t?t.fixedSize:t.getSizeFromValue(n)}function Sl(n){return Object.freeze({...n,encode:t=>{const e=new Uint8Array(xl(t,n));return n.write(t,e,0),e}})}function El(n){return Object.freeze({...n,decode:(t,e=0)=>n.read(t,e)[0]})}function Jr(n){return"fixedSize"in n&&typeof n.fixedSize=="number"}function _l(n,t){if(Jr(n)!==Jr(t))throw new tn(gl);if(Jr(n)&&Jr(t)&&n.fixedSize!==t.fixedSize)throw new tn(yl,{decoderFixedSize:t.fixedSize,encoderFixedSize:n.fixedSize});if(!Jr(n)&&!Jr(t)&&n.maxSize!==t.maxSize)throw new tn(ml,{decoderMaxSize:t.maxSize,encoderMaxSize:n.maxSize});return{...t,...n,decode:t.decode,encode:n.encode,read:t.read,write:n.write}}function Il(n,t,e=0){if(t.length-e<=0)throw new tn(dl,{codecDescription:n})}function Al(n,t,e,r=0){const s=e.length-r;if(s<t)throw new tn(pl,{bytesLength:s,codecDescription:n,expected:t})}function Bl(n,t,e,r){if(r<t||r>e)throw new tn(wl,{codecDescription:n,max:e,min:t,value:r})}function hc(n){return(n==null?void 0:n.endian)!==1}function Rl(n){return Sl({fixedSize:n.size,write(t,e,r){n.range&&Bl(n.name,n.range[0],n.range[1],t);const s=new ArrayBuffer(n.size);return n.set(new DataView(s),t,hc(n.config)),e.set(new Uint8Array(s),r),r+n.size}})}function Ml(n){return El({fixedSize:n.size,read(t,e=0){Il(n.name,t,e),Al(n.name,n.size,t,e);const r=new DataView(Tl(t,e,n.size));return[n.get(r,hc(n.config)),e+n.size]}})}function Tl(n,t,e){const r=n.byteOffset+(t??0),s=e??n.byteLength;return n.buffer.slice(r,r+s)}var dc=(n={})=>Rl({config:n,name:"u64",range:[0n,BigInt("0xffffffffffffffff")],set:(t,e,r)=>t.setBigUint64(0,BigInt(e),r),size:8}),Pl=(n={})=>Ml({config:n,get:(t,e)=>t.getBigUint64(0,e),name:"u64",size:8}),Ll=(n={})=>_l(dc(n),Pl(n));class Cl extends TypeError{constructor(t,e){let r;const{message:s,explanation:i,...o}=t,{path:a}=t,h=a.length===0?s:`At path: ${a.join(".")} -- ${s}`;super(i??h),i!=null&&(this.cause=h),Object.assign(this,o),this.name=this.constructor.name,this.failures=()=>r??(r=[t,...e()])}}function Ol(n){return Pn(n)&&typeof n[Symbol.iterator]=="function"}function Pn(n){return typeof n=="object"&&n!=null}function ni(n){return Pn(n)&&!Array.isArray(n)}function Ge(n){return typeof n=="symbol"?n.toString():typeof n=="string"?JSON.stringify(n):`${n}`}function Ul(n){const{done:t,value:e}=n.next();return t?void 0:e}function Nl(n,t,e,r){if(n===!0)return;n===!1?n={}:typeof n=="string"&&(n={message:n});const{path:s,branch:i}=t,{type:o}=e,{refinement:a,message:h=`Expected a value of type \`${o}\`${a?` with refinement \`${a}\``:""}, but received: \`${Ge(r)}\``}=n;return{value:r,type:o,refinement:a,key:s[s.length-1],path:s,branch:i,...n,message:h}}function*Do(n,t,e,r){Ol(n)||(n=[n]);for(const s of n){const i=Nl(s,t,e,r);i&&(yield i)}}function*Ys(n,t,e={}){const{path:r=[],branch:s=[n],coerce:i=!1,mask:o=!1}=e,a={path:r,branch:s,mask:o};i&&(n=t.coercer(n,a));let h="valid";for(const y of t.validator(n,a))y.explanation=e.message,h="not_valid",yield[y,void 0];for(let[y,m,E]of t.entries(n,a)){const M=Ys(m,E,{path:y===void 0?r:[...r,y],branch:y===void 0?s:[...s,m],coerce:i,mask:o,message:e.message});for(const k of M)k[0]?(h=k[0].refinement!=null?"not_refined":"not_valid",yield[k[0],void 0]):i&&(m=k[1],y===void 0?n=m:n instanceof Map?n.set(y,m):n instanceof Set?n.add(m):Pn(n)&&(m!==void 0||y in n)&&(n[y]=m))}if(h!=="not_valid")for(const y of t.refiner(n,a))y.explanation=e.message,h="not_refined",yield[y,void 0];h==="valid"&&(yield[void 0,n])}let cr=class{constructor(t){const{type:e,schema:r,validator:s,refiner:i,coercer:o=h=>h,entries:a=function*(){}}=t;this.type=e,this.schema=r,this.entries=a,this.coercer=o,s?this.validator=(h,y)=>{const m=s(h,y);return Do(m,y,this,h)}:this.validator=()=>[],i?this.refiner=(h,y)=>{const m=i(h,y);return Do(m,y,this,h)}:this.refiner=()=>[]}assert(t,e){return pc(t,this,e)}create(t,e){return X(t,this,e)}is(t){return gc(t,this)}mask(t,e){return zl(t,this,e)}validate(t,e={}){return Ln(t,this,e)}};function pc(n,t,e){const r=Ln(n,t,{message:e});if(r[0])throw r[0]}function X(n,t,e){const r=Ln(n,t,{coerce:!0,message:e});if(r[0])throw r[0];return r[1]}function zl(n,t,e){const r=Ln(n,t,{coerce:!0,mask:!0,message:e});if(r[0])throw r[0];return r[1]}function gc(n,t){return!Ln(n,t)[0]}function Ln(n,t,e={}){const r=Ys(n,t,e),s=Ul(r);return s[0]?[new Cl(s[0],function*(){for(const o of r)o[0]&&(yield o[0])}),void 0]:[void 0,s[1]]}function Wr(n,t){return new cr({type:n,schema:null,validator:t})}function Fl(){return Wr("any",()=>!0)}function et(n){return new cr({type:"array",schema:n,*entries(t){if(n&&Array.isArray(t))for(const[e,r]of t.entries())yield[e,r,n]},coercer(t){return Array.isArray(t)?t.slice():t},validator(t){return Array.isArray(t)||`Expected an array value, but received: ${Ge(t)}`}})}function sr(){return Wr("boolean",n=>typeof n=="boolean")}function Js(n){return Wr("instance",t=>t instanceof n||`Expected a \`${n.name}\` instance, but received: ${Ge(t)}`)}function qt(n){const t=Ge(n),e=typeof n;return new cr({type:"literal",schema:e==="string"||e==="number"||e==="boolean"?n:null,validator(r){return r===n||`Expected the literal \`${t}\`, but received: ${Ge(r)}`}})}function Dl(){return Wr("never",()=>!1)}function rt(n){return new cr({...n,validator:(t,e)=>t===null||n.validator(t,e),refiner:(t,e)=>t===null||n.refiner(t,e)})}function O(){return Wr("number",n=>typeof n=="number"&&!isNaN(n)||`Expected a number, but received: ${Ge(n)}`)}function ut(n){return new cr({...n,validator:(t,e)=>t===void 0||n.validator(t,e),refiner:(t,e)=>t===void 0||n.refiner(t,e)})}function yc(n,t){return new cr({type:"record",schema:null,*entries(e){if(Pn(e))for(const r in e){const s=e[r];yield[r,r,n],yield[r,s,t]}},validator(e){return ni(e)||`Expected an object, but received: ${Ge(e)}`},coercer(e){return ni(e)?{...e}:e}})}function Z(){return Wr("string",n=>typeof n=="string"||`Expected a string, but received: ${Ge(n)}`)}function Xs(n){const t=Dl();return new cr({type:"tuple",schema:null,*entries(e){if(Array.isArray(e)){const r=Math.max(n.length,e.length);for(let s=0;s<r;s++)yield[s,e[s],n[s]||t]}},validator(e){return Array.isArray(e)||`Expected an array, but received: ${Ge(e)}`},coercer(e){return Array.isArray(e)?e.slice():e}})}function H(n){const t=Object.keys(n);return new cr({type:"type",schema:n,*entries(e){if(Pn(e))for(const r of t)yield[r,e[r],n[r]]},validator(e){return ni(e)||`Expected an object, but received: ${Ge(e)}`},coercer(e){return ni(e)?{...e}:e}})}function Pe(n){const t=n.map(e=>e.type).join(" | ");return new cr({type:"union",schema:null,coercer(e,r){for(const s of n){const[i,o]=s.validate(e,{coerce:!0,mask:r.mask});if(!i)return o}return e},validator(e,r){const s=[];for(const i of n){const[...o]=Ys(e,i,r),[a]=o;if(a[0])for(const[h]of o)h&&s.push(h);else return[]}return[`Expected the value to satisfy a union of \`${t}\`, but received: ${Ge(e)}`,...s]}})}function hn(){return Wr("unknown",()=>!0)}function Cn(n,t,e){return new cr({...n,coercer:(r,s)=>gc(r,t)?n.coercer(e(r,s),s):n.coercer(r,s)})}var Hn,Kl=new Uint8Array(16);function mc(){if(!Hn&&(Hn=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||typeof msCrypto<"u"&&typeof msCrypto.getRandomValues=="function"&&msCrypto.getRandomValues.bind(msCrypto),!Hn))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return Hn(Kl)}const ql=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;function yi(n){return typeof n=="string"&&ql.test(n)}var we=[];for(var rs=0;rs<256;++rs)we.push((rs+256).toString(16).substr(1));function mi(n){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,e=(we[n[t+0]]+we[n[t+1]]+we[n[t+2]]+we[n[t+3]]+"-"+we[n[t+4]]+we[n[t+5]]+"-"+we[n[t+6]]+we[n[t+7]]+"-"+we[n[t+8]]+we[n[t+9]]+"-"+we[n[t+10]]+we[n[t+11]]+we[n[t+12]]+we[n[t+13]]+we[n[t+14]]+we[n[t+15]]).toLowerCase();if(!yi(e))throw TypeError("Stringified UUID is invalid");return e}var Ko,ns,is=0,ss=0;function Wl(n,t,e){var r=t&&e||0,s=t||new Array(16);n=n||{};var i=n.node||Ko,o=n.clockseq!==void 0?n.clockseq:ns;if(i==null||o==null){var a=n.random||(n.rng||mc)();i==null&&(i=Ko=[a[0]|1,a[1],a[2],a[3],a[4],a[5]]),o==null&&(o=ns=(a[6]<<8|a[7])&16383)}var h=n.msecs!==void 0?n.msecs:Date.now(),y=n.nsecs!==void 0?n.nsecs:ss+1,m=h-is+(y-ss)/1e4;if(m<0&&n.clockseq===void 0&&(o=o+1&16383),(m<0||h>is)&&n.nsecs===void 0&&(y=0),y>=1e4)throw new Error("uuid.v1(): Can't create more than 10M uuids/sec");is=h,ss=y,ns=o,h+=122192928e5;var E=((h&268435455)*1e4+y)%4294967296;s[r++]=E>>>24&255,s[r++]=E>>>16&255,s[r++]=E>>>8&255,s[r++]=E&255;var M=h/4294967296*1e4&268435455;s[r++]=M>>>8&255,s[r++]=M&255,s[r++]=M>>>24&15|16,s[r++]=M>>>16&255,s[r++]=o>>>8|128,s[r++]=o&255;for(var k=0;k<6;++k)s[r+k]=i[k];return t||mi(s)}function wc(n){if(!yi(n))throw TypeError("Invalid UUID");var t,e=new Uint8Array(16);return e[0]=(t=parseInt(n.slice(0,8),16))>>>24,e[1]=t>>>16&255,e[2]=t>>>8&255,e[3]=t&255,e[4]=(t=parseInt(n.slice(9,13),16))>>>8,e[5]=t&255,e[6]=(t=parseInt(n.slice(14,18),16))>>>8,e[7]=t&255,e[8]=(t=parseInt(n.slice(19,23),16))>>>8,e[9]=t&255,e[10]=(t=parseInt(n.slice(24,36),16))/1099511627776&255,e[11]=t/4294967296&255,e[12]=t>>>24&255,e[13]=t>>>16&255,e[14]=t>>>8&255,e[15]=t&255,e}function $l(n){n=unescape(encodeURIComponent(n));for(var t=[],e=0;e<n.length;++e)t.push(n.charCodeAt(e));return t}var Hl="6ba7b810-9dad-11d1-80b4-00c04fd430c8",Vl="6ba7b811-9dad-11d1-80b4-00c04fd430c8";function bc(n,t,e){function r(s,i,o,a){if(typeof s=="string"&&(s=$l(s)),typeof i=="string"&&(i=wc(i)),i.length!==16)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");var h=new Uint8Array(16+s.length);if(h.set(i),h.set(s,i.length),h=e(h),h[6]=h[6]&15|t,h[8]=h[8]&63|128,o){a=a||0;for(var y=0;y<16;++y)o[a+y]=h[y];return o}return mi(h)}try{r.name=n}catch{}return r.DNS=Hl,r.URL=Vl,r}function jl(n){if(typeof n=="string"){var t=unescape(encodeURIComponent(n));n=new Uint8Array(t.length);for(var e=0;e<t.length;++e)n[e]=t.charCodeAt(e)}return Gl(Zl(Yl(n),n.length*8))}function Gl(n){for(var t=[],e=n.length*32,r="0123456789abcdef",s=0;s<e;s+=8){var i=n[s>>5]>>>s%32&255,o=parseInt(r.charAt(i>>>4&15)+r.charAt(i&15),16);t.push(o)}return t}function vc(n){return(n+64>>>9<<4)+14+1}function Zl(n,t){n[t>>5]|=128<<t%32,n[vc(t)-1]=t;for(var e=1732584193,r=-271733879,s=-1732584194,i=271733878,o=0;o<n.length;o+=16){var a=e,h=r,y=s,m=i;e=_e(e,r,s,i,n[o],7,-680876936),i=_e(i,e,r,s,n[o+1],12,-389564586),s=_e(s,i,e,r,n[o+2],17,606105819),r=_e(r,s,i,e,n[o+3],22,-1044525330),e=_e(e,r,s,i,n[o+4],7,-176418897),i=_e(i,e,r,s,n[o+5],12,1200080426),s=_e(s,i,e,r,n[o+6],17,-1473231341),r=_e(r,s,i,e,n[o+7],22,-45705983),e=_e(e,r,s,i,n[o+8],7,1770035416),i=_e(i,e,r,s,n[o+9],12,-1958414417),s=_e(s,i,e,r,n[o+10],17,-42063),r=_e(r,s,i,e,n[o+11],22,-1990404162),e=_e(e,r,s,i,n[o+12],7,1804603682),i=_e(i,e,r,s,n[o+13],12,-40341101),s=_e(s,i,e,r,n[o+14],17,-1502002290),r=_e(r,s,i,e,n[o+15],22,1236535329),e=Ie(e,r,s,i,n[o+1],5,-165796510),i=Ie(i,e,r,s,n[o+6],9,-1069501632),s=Ie(s,i,e,r,n[o+11],14,643717713),r=Ie(r,s,i,e,n[o],20,-373897302),e=Ie(e,r,s,i,n[o+5],5,-701558691),i=Ie(i,e,r,s,n[o+10],9,38016083),s=Ie(s,i,e,r,n[o+15],14,-660478335),r=Ie(r,s,i,e,n[o+4],20,-405537848),e=Ie(e,r,s,i,n[o+9],5,568446438),i=Ie(i,e,r,s,n[o+14],9,-1019803690),s=Ie(s,i,e,r,n[o+3],14,-187363961),r=Ie(r,s,i,e,n[o+8],20,1163531501),e=Ie(e,r,s,i,n[o+13],5,-1444681467),i=Ie(i,e,r,s,n[o+2],9,-51403784),s=Ie(s,i,e,r,n[o+7],14,1735328473),r=Ie(r,s,i,e,n[o+12],20,-1926607734),e=Ae(e,r,s,i,n[o+5],4,-378558),i=Ae(i,e,r,s,n[o+8],11,-2022574463),s=Ae(s,i,e,r,n[o+11],16,1839030562),r=Ae(r,s,i,e,n[o+14],23,-35309556),e=Ae(e,r,s,i,n[o+1],4,-1530992060),i=Ae(i,e,r,s,n[o+4],11,1272893353),s=Ae(s,i,e,r,n[o+7],16,-155497632),r=Ae(r,s,i,e,n[o+10],23,-1094730640),e=Ae(e,r,s,i,n[o+13],4,681279174),i=Ae(i,e,r,s,n[o],11,-358537222),s=Ae(s,i,e,r,n[o+3],16,-722521979),r=Ae(r,s,i,e,n[o+6],23,76029189),e=Ae(e,r,s,i,n[o+9],4,-640364487),i=Ae(i,e,r,s,n[o+12],11,-421815835),s=Ae(s,i,e,r,n[o+15],16,530742520),r=Ae(r,s,i,e,n[o+2],23,-995338651),e=Be(e,r,s,i,n[o],6,-198630844),i=Be(i,e,r,s,n[o+7],10,1126891415),s=Be(s,i,e,r,n[o+14],15,-1416354905),r=Be(r,s,i,e,n[o+5],21,-57434055),e=Be(e,r,s,i,n[o+12],6,1700485571),i=Be(i,e,r,s,n[o+3],10,-1894986606),s=Be(s,i,e,r,n[o+10],15,-1051523),r=Be(r,s,i,e,n[o+1],21,-2054922799),e=Be(e,r,s,i,n[o+8],6,1873313359),i=Be(i,e,r,s,n[o+15],10,-30611744),s=Be(s,i,e,r,n[o+6],15,-1560198380),r=Be(r,s,i,e,n[o+13],21,1309151649),e=Be(e,r,s,i,n[o+4],6,-145523070),i=Be(i,e,r,s,n[o+11],10,-1120210379),s=Be(s,i,e,r,n[o+2],15,718787259),r=Be(r,s,i,e,n[o+9],21,-343485551),e=Er(e,a),r=Er(r,h),s=Er(s,y),i=Er(i,m)}return[e,r,s,i]}function Yl(n){if(n.length===0)return[];for(var t=n.length*8,e=new Uint32Array(vc(t)),r=0;r<t;r+=8)e[r>>5]|=(n[r/8]&255)<<r%32;return e}function Er(n,t){var e=(n&65535)+(t&65535),r=(n>>16)+(t>>16)+(e>>16);return r<<16|e&65535}function Jl(n,t){return n<<t|n>>>32-t}function wi(n,t,e,r,s,i){return Er(Jl(Er(Er(t,n),Er(r,i)),s),e)}function _e(n,t,e,r,s,i,o){return wi(t&e|~t&r,n,t,s,i,o)}function Ie(n,t,e,r,s,i,o){return wi(t&r|e&~r,n,t,s,i,o)}function Ae(n,t,e,r,s,i,o){return wi(t^e^r,n,t,s,i,o)}function Be(n,t,e,r,s,i,o){return wi(e^(t|~r),n,t,s,i,o)}var Xl=bc("v3",48,jl);function Ql(n,t,e){n=n||{};var r=n.random||(n.rng||mc)();if(r[6]=r[6]&15|64,r[8]=r[8]&63|128,t){e=e||0;for(var s=0;s<16;++s)t[e+s]=r[s];return t}return mi(r)}function th(n,t,e,r){switch(n){case 0:return t&e^~t&r;case 1:return t^e^r;case 2:return t&e^t&r^e&r;case 3:return t^e^r}}function os(n,t){return n<<t|n>>>32-t}function eh(n){var t=[1518500249,1859775393,2400959708,3395469782],e=[1732584193,4023233417,2562383102,271733878,3285377520];if(typeof n=="string"){var r=unescape(encodeURIComponent(n));n=[];for(var s=0;s<r.length;++s)n.push(r.charCodeAt(s))}else Array.isArray(n)||(n=Array.prototype.slice.call(n));n.push(128);for(var i=n.length/4+2,o=Math.ceil(i/16),a=new Array(o),h=0;h<o;++h){for(var y=new Uint32Array(16),m=0;m<16;++m)y[m]=n[h*64+m*4]<<24|n[h*64+m*4+1]<<16|n[h*64+m*4+2]<<8|n[h*64+m*4+3];a[h]=y}a[o-1][14]=(n.length-1)*8/Math.pow(2,32),a[o-1][14]=Math.floor(a[o-1][14]),a[o-1][15]=(n.length-1)*8&4294967295;for(var E=0;E<o;++E){for(var M=new Uint32Array(80),k=0;k<16;++k)M[k]=a[E][k];for(var R=16;R<80;++R)M[R]=os(M[R-3]^M[R-8]^M[R-14]^M[R-16],1);for(var B=e[0],I=e[1],C=e[2],K=e[3],q=e[4],V=0;V<80;++V){var F=Math.floor(V/20),j=os(B,5)+th(F,I,C,K)+q+t[F]+M[V]>>>0;q=K,K=C,C=os(I,30)>>>0,I=B,B=j}e[0]=e[0]+B>>>0,e[1]=e[1]+I>>>0,e[2]=e[2]+C>>>0,e[3]=e[3]+K>>>0,e[4]=e[4]+q>>>0}return[e[0]>>24&255,e[0]>>16&255,e[0]>>8&255,e[0]&255,e[1]>>24&255,e[1]>>16&255,e[1]>>8&255,e[1]&255,e[2]>>24&255,e[2]>>16&255,e[2]>>8&255,e[2]&255,e[3]>>24&255,e[3]>>16&255,e[3]>>8&255,e[3]&255,e[4]>>24&255,e[4]>>16&255,e[4]>>8&255,e[4]&255]}var rh=bc("v5",80,eh);const nh="00000000-0000-0000-0000-000000000000";function ih(n){if(!yi(n))throw TypeError("Invalid UUID");return parseInt(n.substr(14,1),16)}const sh=Object.freeze(Object.defineProperty({__proto__:null,NIL:nh,parse:wc,stringify:mi,v1:Wl,v3:Xl,v4:Ql,v5:rh,validate:yi,version:ih},Symbol.toStringTag,{value:"Module"})),kc=Is(sh),oh=kc.v4,ah=function(n,t,e,r){if(typeof n!="string")throw new TypeError(n+" must be a string");r=r||{};const s=typeof r.version=="number"?r.version:2;if(s!==1&&s!==2)throw new TypeError(s+" must be 1 or 2");const i={method:n};if(s===2&&(i.jsonrpc="2.0"),t){if(typeof t!="object"&&!Array.isArray(t))throw new TypeError(t+" must be an object, array or omitted");i.params=t}if(typeof e>"u"){const o=typeof r.generator=="function"?r.generator:function(){return oh()};i.id=o(i,r)}else s===2&&e===null?r.notificationIdNull&&(i.id=null):i.id=e;return i};var ch=ah;const uh=kc.v4,fh=ch,_n=function(n,t){if(!(this instanceof _n))return new _n(n,t);t||(t={}),this.options={reviver:typeof t.reviver<"u"?t.reviver:null,replacer:typeof t.replacer<"u"?t.replacer:null,generator:typeof t.generator<"u"?t.generator:function(){return uh()},version:typeof t.version<"u"?t.version:2,notificationIdNull:typeof t.notificationIdNull=="boolean"?t.notificationIdNull:!1},this.callServer=n};var lh=_n;_n.prototype.request=function(n,t,e,r){const s=this;let i=null;const o=Array.isArray(n)&&typeof t=="function";if(this.options.version===1&&o)throw new TypeError("JSON-RPC 1.0 does not support batching");if(o||!o&&n&&typeof n=="object"&&typeof t=="function")r=t,i=n;else{typeof e=="function"&&(r=e,e=void 0);const y=typeof r=="function";try{i=fh(n,t,e,{generator:this.options.generator,version:this.options.version,notificationIdNull:this.options.notificationIdNull})}catch(m){if(y)return r(m);throw m}if(!y)return i}let h;try{h=JSON.stringify(i,this.options.replacer)}catch(y){return r(y)}return this.callServer(h,function(y,m){s._parseResponse(y,m,r)}),i};_n.prototype._parseResponse=function(n,t,e){if(n){e(n);return}if(!t)return e();let r;try{r=JSON.parse(t,this.options.reviver)}catch(s){return e(s)}if(e.length===3)if(Array.isArray(r)){const s=function(o){return typeof o.error<"u"},i=function(o){return!s(o)};return e(null,r.filter(s),r.filter(i))}else return e(null,r.error,r.result);e(null,r)};const hh=ai(lh);var xc={exports:{}};(function(n){var t=Object.prototype.hasOwnProperty,e="~";function r(){}Object.create&&(r.prototype=Object.create(null),new r().__proto__||(e=!1));function s(h,y,m){this.fn=h,this.context=y,this.once=m||!1}function i(h,y,m,E,M){if(typeof m!="function")throw new TypeError("The listener must be a function");var k=new s(m,E||h,M),R=e?e+y:y;return h._events[R]?h._events[R].fn?h._events[R]=[h._events[R],k]:h._events[R].push(k):(h._events[R]=k,h._eventsCount++),h}function o(h,y){--h._eventsCount===0?h._events=new r:delete h._events[y]}function a(){this._events=new r,this._eventsCount=0}a.prototype.eventNames=function(){var y=[],m,E;if(this._eventsCount===0)return y;for(E in m=this._events)t.call(m,E)&&y.push(e?E.slice(1):E);return Object.getOwnPropertySymbols?y.concat(Object.getOwnPropertySymbols(m)):y},a.prototype.listeners=function(y){var m=e?e+y:y,E=this._events[m];if(!E)return[];if(E.fn)return[E.fn];for(var M=0,k=E.length,R=new Array(k);M<k;M++)R[M]=E[M].fn;return R},a.prototype.listenerCount=function(y){var m=e?e+y:y,E=this._events[m];return E?E.fn?1:E.length:0},a.prototype.emit=function(y,m,E,M,k,R){var B=e?e+y:y;if(!this._events[B])return!1;var I=this._events[B],C=arguments.length,K,q;if(I.fn){switch(I.once&&this.removeListener(y,I.fn,void 0,!0),C){case 1:return I.fn.call(I.context),!0;case 2:return I.fn.call(I.context,m),!0;case 3:return I.fn.call(I.context,m,E),!0;case 4:return I.fn.call(I.context,m,E,M),!0;case 5:return I.fn.call(I.context,m,E,M,k),!0;case 6:return I.fn.call(I.context,m,E,M,k,R),!0}for(q=1,K=new Array(C-1);q<C;q++)K[q-1]=arguments[q];I.fn.apply(I.context,K)}else{var V=I.length,F;for(q=0;q<V;q++)switch(I[q].once&&this.removeListener(y,I[q].fn,void 0,!0),C){case 1:I[q].fn.call(I[q].context);break;case 2:I[q].fn.call(I[q].context,m);break;case 3:I[q].fn.call(I[q].context,m,E);break;case 4:I[q].fn.call(I[q].context,m,E,M);break;default:if(!K)for(F=1,K=new Array(C-1);F<C;F++)K[F-1]=arguments[F];I[q].fn.apply(I[q].context,K)}}return!0},a.prototype.on=function(y,m,E){return i(this,y,m,E,!1)},a.prototype.once=function(y,m,E){return i(this,y,m,E,!0)},a.prototype.removeListener=function(y,m,E,M){var k=e?e+y:y;if(!this._events[k])return this;if(!m)return o(this,k),this;var R=this._events[k];if(R.fn)R.fn===m&&(!M||R.once)&&(!E||R.context===E)&&o(this,k);else{for(var B=0,I=[],C=R.length;B<C;B++)(R[B].fn!==m||M&&!R[B].once||E&&R[B].context!==E)&&I.push(R[B]);I.length?this._events[k]=I.length===1?I[0]:I:o(this,k)}return this},a.prototype.removeAllListeners=function(y){var m;return y?(m=e?e+y:y,this._events[m]&&o(this,m)):(this._events=new r,this._eventsCount=0),this},a.prototype.off=a.prototype.removeListener,a.prototype.addListener=a.prototype.on,a.prefixed=e,a.EventEmitter=a,n.exports=a})(xc);var dh=xc.exports;const Sc=ai(dh);var ph=class extends Sc{constructor(t,e,r){super();de(this,"socket");this.socket=new window.WebSocket(t,r),this.socket.onopen=()=>this.emit("open"),this.socket.onmessage=s=>this.emit("message",s.data),this.socket.onerror=s=>this.emit("error",s),this.socket.onclose=s=>{this.emit("close",s.code,s.reason)}}send(t,e,r){const s=r||e;try{this.socket.send(t),s()}catch(i){s(i)}}close(t,e){this.socket.close(t,e)}addEventListener(t,e,r){this.socket.addEventListener(t,e,r)}};function gh(n,t){return new ph(n,t)}var yh=class{encode(n){return JSON.stringify(n)}decode(n){return JSON.parse(n)}},mh=class extends Sc{constructor(t,e="ws://localhost:8080",{autoconnect:r=!0,reconnect:s=!0,reconnect_interval:i=1e3,max_reconnects:o=5,...a}={},h,y){super();de(this,"address");de(this,"rpc_id");de(this,"queue");de(this,"options");de(this,"autoconnect");de(this,"ready");de(this,"reconnect");de(this,"reconnect_timer_id");de(this,"reconnect_interval");de(this,"max_reconnects");de(this,"rest_options");de(this,"current_reconnects");de(this,"generate_request_id");de(this,"socket");de(this,"webSocketFactory");de(this,"dataPack");this.webSocketFactory=t,this.queue={},this.rpc_id=0,this.address=e,this.autoconnect=r,this.ready=!1,this.reconnect=s,this.reconnect_timer_id=void 0,this.reconnect_interval=i,this.max_reconnects=o,this.rest_options=a,this.current_reconnects=0,this.generate_request_id=h||(()=>typeof this.rpc_id=="number"?++this.rpc_id:Number(this.rpc_id)+1),y?this.dataPack=y:this.dataPack=new yh,this.autoconnect&&this._connect(this.address,{autoconnect:this.autoconnect,reconnect:this.reconnect,reconnect_interval:this.reconnect_interval,max_reconnects:this.max_reconnects,...this.rest_options})}connect(){this.socket||this._connect(this.address,{autoconnect:this.autoconnect,reconnect:this.reconnect,reconnect_interval:this.reconnect_interval,max_reconnects:this.max_reconnects,...this.rest_options})}call(t,e,r,s){return!s&&typeof r=="object"&&(s=r,r=null),new Promise((i,o)=>{if(!this.ready)return o(new Error("socket not ready"));const a=this.generate_request_id(t,e),h={jsonrpc:"2.0",method:t,params:e||void 0,id:a};this.socket.send(this.dataPack.encode(h),s,y=>{if(y)return o(y);this.queue[a]={promise:[i,o]},r&&(this.queue[a].timeout=setTimeout(()=>{delete this.queue[a],o(new Error("reply timeout"))},r))})})}async login(t){const e=await this.call("rpc.login",t);if(!e)throw new Error("authentication failed");return e}async listMethods(){return await this.call("__listMethods")}notify(t,e){return new Promise((r,s)=>{if(!this.ready)return s(new Error("socket not ready"));const i={jsonrpc:"2.0",method:t,params:e};this.socket.send(this.dataPack.encode(i),o=>{if(o)return s(o);r()})})}async subscribe(t){typeof t=="string"&&(t=[t]);const e=await this.call("rpc.on",t);if(typeof t=="string"&&e[t]!=="ok")throw new Error("Failed subscribing to an event '"+t+"' with: "+e[t]);return e}async unsubscribe(t){typeof t=="string"&&(t=[t]);const e=await this.call("rpc.off",t);if(typeof t=="string"&&e[t]!=="ok")throw new Error("Failed unsubscribing from an event with: "+e);return e}close(t,e){this.socket.close(t||1e3,e)}setAutoReconnect(t){this.reconnect=t}setReconnectInterval(t){this.reconnect_interval=t}setMaxReconnects(t){this.max_reconnects=t}_connect(t,e){clearTimeout(this.reconnect_timer_id),this.socket=this.webSocketFactory(t,e),this.socket.addEventListener("open",()=>{this.ready=!0,this.emit("open"),this.current_reconnects=0}),this.socket.addEventListener("message",({data:r})=>{r instanceof ArrayBuffer&&(r=ct.Buffer.from(r).toString());try{r=this.dataPack.decode(r)}catch{return}if(r.notification&&this.listeners(r.notification).length){if(!Object.keys(r.params).length)return this.emit(r.notification);const s=[r.notification];if(r.params.constructor===Object)s.push(r.params);else for(let i=0;i<r.params.length;i++)s.push(r.params[i]);return Promise.resolve().then(()=>{this.emit.apply(this,s)})}if(!this.queue[r.id])return r.method?Promise.resolve().then(()=>{this.emit(r.method,r==null?void 0:r.params)}):void 0;"error"in r=="result"in r&&this.queue[r.id].promise[1](new Error('Server response malformed. Response must include either "result" or "error", but not both.')),this.queue[r.id].timeout&&clearTimeout(this.queue[r.id].timeout),r.error?this.queue[r.id].promise[1](r.error):this.queue[r.id].promise[0](r.result),delete this.queue[r.id]}),this.socket.addEventListener("error",r=>this.emit("error",r)),this.socket.addEventListener("close",({code:r,reason:s})=>{this.ready&&setTimeout(()=>this.emit("close",r,s),0),this.ready=!1,this.socket=void 0,r!==1e3&&(this.current_reconnects++,this.reconnect&&(this.max_reconnects>this.current_reconnects||this.max_reconnects===0)&&(this.reconnect_timer_id=setTimeout(()=>this._connect(t,e),this.reconnect_interval)))})}};const wh=BigInt(0),yn=BigInt(1),bh=BigInt(2),vh=BigInt(7),kh=BigInt(256),xh=BigInt(113),Ec=[],_c=[],Ic=[];for(let n=0,t=yn,e=1,r=0;n<24;n++){[e,r]=[r,(2*e+3*r)%5],Ec.push(2*(5*r+e)),_c.push((n+1)*(n+2)/2%64);let s=wh;for(let i=0;i<7;i++)t=(t<<yn^(t>>vh)*xh)%kh,t&bh&&(s^=yn<<(yn<<BigInt(i))-yn);Ic.push(s)}const Ac=ga(Ic,!0),Sh=Ac[0],Eh=Ac[1],qo=(n,t,e)=>e>32?Eu(n,t,e):xu(n,t,e),Wo=(n,t,e)=>e>32?_u(n,t,e):Su(n,t,e);function _h(n,t=24){const e=new Uint32Array(10);for(let r=24-t;r<24;r++){for(let o=0;o<10;o++)e[o]=n[o]^n[o+10]^n[o+20]^n[o+30]^n[o+40];for(let o=0;o<10;o+=2){const a=(o+8)%10,h=(o+2)%10,y=e[h],m=e[h+1],E=qo(y,m,1)^e[a],M=Wo(y,m,1)^e[a+1];for(let k=0;k<50;k+=10)n[o+k]^=E,n[o+k+1]^=M}let s=n[2],i=n[3];for(let o=0;o<24;o++){const a=_c[o],h=qo(s,i,a),y=Wo(s,i,a),m=Ec[o];s=n[m],i=n[m+1],n[m]=h,n[m+1]=y}for(let o=0;o<50;o+=10){for(let a=0;a<10;a++)e[a]=n[o+a];for(let a=0;a<10;a++)n[o+a]^=~e[(a+2)%10]&e[(a+4)%10]}n[0]^=Sh[r],n[1]^=Eh[r]}Ar(e)}class Qs extends Rs{constructor(t,e,r,s=!1,i=24){if(super(),this.pos=0,this.posOut=0,this.finished=!1,this.destroyed=!1,this.enableXOF=!1,this.blockLen=t,this.suffix=e,this.outputLen=r,this.enableXOF=s,this.rounds=i,xn(r),!(0<t&&t<200))throw new Error("only keccak-f1600 function is supported");this.state=new Uint8Array(200),this.state32=du(this.state)}clone(){return this._cloneInto()}keccak(){Eo(this.state32),_h(this.state32,this.rounds),Eo(this.state32),this.posOut=0,this.pos=0}update(t){nn(this),t=fi(t),me(t);const{blockLen:e,state:r}=this,s=t.length;for(let i=0;i<s;){const o=Math.min(e-this.pos,s-i);for(let a=0;a<o;a++)r[this.pos++]^=t[i++];this.pos===e&&this.keccak()}return this}finish(){if(this.finished)return;this.finished=!0;const{state:t,suffix:e,pos:r,blockLen:s}=this;t[r]^=e,e&128&&r===s-1&&this.keccak(),t[s-1]^=128,this.keccak()}writeInto(t){nn(this,!1),me(t),this.finish();const e=this.state,{blockLen:r}=this;for(let s=0,i=t.length;s<i;){this.posOut>=r&&this.keccak();const o=Math.min(r-this.posOut,i-s);t.set(e.subarray(this.posOut,this.posOut+o),s),this.posOut+=o,s+=o}return t}xofInto(t){if(!this.enableXOF)throw new Error("XOF is not possible for this instance");return this.writeInto(t)}xof(t){return xn(t),this.xofInto(new Uint8Array(t))}digestInto(t){if(ha(t,this),this.finished)throw new Error("digest() was already called");return this.writeInto(t),this.destroy(),t}digest(){return this.digestInto(new Uint8Array(this.outputLen))}destroy(){this.destroyed=!0,Ar(this.state)}_cloneInto(t){const{blockLen:e,suffix:r,outputLen:s,rounds:i,enableXOF:o}=this;return t||(t=new Qs(e,r,s,o,i)),t.state32.set(this.state32),t.pos=this.pos,t.posOut=this.posOut,t.finished=this.finished,t.rounds=i,t.suffix=r,t.outputLen=s,t.enableXOF=o,t.destroyed=this.destroyed,t}}const Ih=(n,t,e)=>Ms(()=>new Qs(t,n,e)),$o=Ih(1,136,256/8);class Bc extends Rs{constructor(t,e){super(),this.finished=!1,this.destroyed=!1,hu(t);const r=fi(e);if(this.iHash=t.create(),typeof this.iHash.update!="function")throw new Error("Expected instance of class which extends utils.Hash");this.blockLen=this.iHash.blockLen,this.outputLen=this.iHash.outputLen;const s=this.blockLen,i=new Uint8Array(s);i.set(r.length>s?t.create().update(r).digest():r);for(let o=0;o<i.length;o++)i[o]^=54;this.iHash.update(i),this.oHash=t.create();for(let o=0;o<i.length;o++)i[o]^=106;this.oHash.update(i),Ar(i)}update(t){return nn(this),this.iHash.update(t),this}digestInto(t){nn(this),me(t,this.outputLen),this.finished=!0,this.iHash.digestInto(t),this.oHash.update(t),this.oHash.digestInto(t),this.destroy()}digest(){const t=new Uint8Array(this.oHash.outputLen);return this.digestInto(t),t}_cloneInto(t){t||(t=Object.create(Object.getPrototypeOf(this),{}));const{oHash:e,iHash:r,finished:s,destroyed:i,blockLen:o,outputLen:a}=this;return t=t,t.finished=s,t.destroyed=i,t.blockLen=o,t.outputLen=a,t.oHash=e._cloneInto(t.oHash),t.iHash=r._cloneInto(t.iHash),t}clone(){return this._cloneInto()}destroy(){this.destroyed=!0,this.oHash.destroy(),this.iHash.destroy()}}const Rc=(n,t,e)=>new Bc(n,t).update(e).digest();Rc.create=(n,t)=>new Bc(n,t);/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */function Ho(n){n.lowS!==void 0&&Ur("lowS",n.lowS),n.prehash!==void 0&&Ur("prehash",n.prehash)}class Ah extends Error{constructor(t=""){super(t)}}const dr={Err:Ah,_tlv:{encode:(n,t)=>{const{Err:e}=dr;if(n<0||n>256)throw new e("tlv.encode: wrong tag");if(t.length&1)throw new e("tlv.encode: unpadded data");const r=t.length/2,s=Kn(r);if(s.length/2&128)throw new e("tlv.encode: long form length too big");const i=r>127?Kn(s.length/2|128):"";return Kn(n)+i+s+t},decode(n,t){const{Err:e}=dr;let r=0;if(n<0||n>256)throw new e("tlv.encode: wrong tag");if(t.length<2||t[r++]!==n)throw new e("tlv.decode: wrong tlv");const s=t[r++],i=!!(s&128);let o=0;if(!i)o=s;else{const h=s&127;if(!h)throw new e("tlv.decode(long): indefinite length not supported");if(h>4)throw new e("tlv.decode(long): byte length is too big");const y=t.subarray(r,r+h);if(y.length!==h)throw new e("tlv.decode: length bytes not complete");if(y[0]===0)throw new e("tlv.decode(long): zero leftmost byte");for(const m of y)o=o<<8|m;if(r+=h,o<128)throw new e("tlv.decode(long): not minimal encoding")}const a=t.subarray(r,r+o);if(a.length!==o)throw new e("tlv.decode: wrong value length");return{v:a,l:t.subarray(r+o)}}},_int:{encode(n){const{Err:t}=dr;if(n<bn)throw new t("integer: negative integers are not allowed");let e=Kn(n);if(Number.parseInt(e[0],16)&8&&(e="00"+e),e.length&1)throw new t("unexpected DER parsing assertion: unpadded hex");return e},decode(n){const{Err:t}=dr;if(n[0]&128)throw new t("invalid signature integer: negative");if(n[0]===0&&!(n[1]&128))throw new t("invalid signature integer: unnecessary leading zero");return Mn(n)}},toSig(n){const{Err:t,_int:e,_tlv:r}=dr,s=Pt("signature",n),{v:i,l:o}=r.decode(48,s);if(o.length)throw new t("invalid signature: left bytes after parsing");const{v:a,l:h}=r.decode(2,i),{v:y,l:m}=r.decode(2,h);if(m.length)throw new t("invalid signature: left bytes after parsing");return{r:e.decode(a),s:e.decode(y)}},hexFromSig(n){const{_tlv:t,_int:e}=dr,r=t.encode(2,e.encode(n.r)),s=t.encode(2,e.encode(n.s)),i=r+s;return t.encode(48,i)}},bn=BigInt(0),vn=BigInt(1),Bh=BigInt(2),Vn=BigInt(3),Rh=BigInt(4);function Mh(n,t,e){function r(s){const i=n.sqr(s),o=n.mul(i,s);return n.add(n.add(o,n.mul(s,t)),e)}return r}function Mc(n,t,e){const{BYTES:r}=n;function s(i){let o;if(typeof i=="bigint")o=i;else{let a=Pt("private key",i);if(t){if(!t.includes(a.length*2))throw new Error("invalid private key");const h=new Uint8Array(r);h.set(a,h.length-a.length),a=h}try{o=n.fromBytes(a)}catch{throw new Error(`invalid private key: expected ui8a of size ${r}, got ${typeof i}`)}}if(e&&(o=n.create(o)),!n.isValidNot0(o))throw new Error("invalid private key: out of range [1..N-1]");return o}return s}function Th(n,t={}){const{Fp:e,Fn:r}=Ra("weierstrass",n,t),{h:s,n:i}=n;Dr(t,{},{allowInfinityPoint:"boolean",clearCofactor:"function",isTorsionFree:"function",fromBytes:"function",toBytes:"function",endo:"object",wrapPrivateKey:"boolean"});const{endo:o}=t;if(o&&(!e.is0(n.a)||typeof o.beta!="bigint"||typeof o.splitScalar!="function"))throw new Error('invalid endo: expected "beta": bigint and "splitScalar": function');function a(){if(!e.isOdd)throw new Error("compression is not supported: Field does not have .isOdd()")}function h(Q,U,$){const{x:z,y:W}=U.toAffine(),b=e.toBytes(z);if(Ur("isCompressed",$),$){a();const c=!e.isOdd(W);return Re(Tc(c),b)}else return Re(Uint8Array.of(4),b,e.toBytes(W))}function y(Q){me(Q);const U=e.BYTES,$=U+1,z=2*U+1,W=Q.length,b=Q[0],c=Q.subarray(1);if(W===$&&(b===2||b===3)){const d=e.fromBytes(c);if(!e.isValid(d))throw new Error("bad point: is not on curve, wrong x");const p=M(d);let v;try{v=e.sqrt(p)}catch(P){const w=P instanceof Error?": "+P.message:"";throw new Error("bad point: is not on curve, sqrt error"+w)}a();const S=e.isOdd(v);return(b&1)===1!==S&&(v=e.neg(v)),{x:d,y:v}}else if(W===z&&b===4){const d=e.fromBytes(c.subarray(U*0,U*1)),p=e.fromBytes(c.subarray(U*1,U*2));if(!k(d,p))throw new Error("bad point: is not on curve");return{x:d,y:p}}else throw new Error(`bad point: got length ${W}, expected compressed=${$} or uncompressed=${z}`)}const m=t.toBytes||h,E=t.fromBytes||y,M=Mh(e,n.a,n.b);function k(Q,U){const $=e.sqr(U),z=M(Q);return e.eql($,z)}if(!k(n.Gx,n.Gy))throw new Error("bad curve params: generator point");const R=e.mul(e.pow(n.a,Vn),Rh),B=e.mul(e.sqr(n.b),BigInt(27));if(e.is0(e.add(R,B)))throw new Error("bad curve params: a or b");function I(Q,U,$=!1){if(!e.isValid(U)||$&&e.is0(U))throw new Error(`bad point coordinate ${Q}`);return U}function C(Q){if(!(Q instanceof F))throw new Error("ProjectivePoint expected")}const K=Yn((Q,U)=>{const{px:$,py:z,pz:W}=Q;if(e.eql(W,e.ONE))return{x:$,y:z};const b=Q.is0();U==null&&(U=b?e.ONE:e.inv(W));const c=e.mul($,U),d=e.mul(z,U),p=e.mul(W,U);if(b)return{x:e.ZERO,y:e.ZERO};if(!e.eql(p,e.ONE))throw new Error("invZ was invalid");return{x:c,y:d}}),q=Yn(Q=>{if(Q.is0()){if(t.allowInfinityPoint&&!e.is0(Q.py))return;throw new Error("bad point: ZERO")}const{x:U,y:$}=Q.toAffine();if(!e.isValid(U)||!e.isValid($))throw new Error("bad point: x or y not field elements");if(!k(U,$))throw new Error("bad point: equation left != right");if(!Q.isTorsionFree())throw new Error("bad point: not in prime-order subgroup");return!0});function V(Q,U,$,z,W){return $=new F(e.mul($.px,Q),$.py,$.pz),U=wn(z,U),$=wn(W,$),U.add($)}class F{constructor(U,$,z){this.px=I("x",U),this.py=I("y",$,!0),this.pz=I("z",z),Object.freeze(this)}static fromAffine(U){const{x:$,y:z}=U||{};if(!U||!e.isValid($)||!e.isValid(z))throw new Error("invalid affine point");if(U instanceof F)throw new Error("projective point not allowed");return e.is0($)&&e.is0(z)?F.ZERO:new F($,z,e.ONE)}get x(){return this.toAffine().x}get y(){return this.toAffine().y}static normalizeZ(U){return _a(F,"pz",U)}static fromBytes(U){return me(U),F.fromHex(U)}static fromHex(U){const $=F.fromAffine(E(Pt("pointHex",U)));return $.assertValidity(),$}static fromPrivateKey(U){const $=Mc(r,t.allowedPrivateKeyLengths,t.wrapPrivateKey);return F.BASE.multiply($(U))}static msm(U,$){return Os(F,r,U,$)}precompute(U=8,$=!0){return G.setWindowSize(this,U),$||this.multiply(Vn),this}_setWindowSize(U){this.precompute(U)}assertValidity(){q(this)}hasEvenY(){const{y:U}=this.toAffine();if(!e.isOdd)throw new Error("Field doesn't support isOdd");return!e.isOdd(U)}equals(U){C(U);const{px:$,py:z,pz:W}=this,{px:b,py:c,pz:d}=U,p=e.eql(e.mul($,d),e.mul(b,W)),v=e.eql(e.mul(z,d),e.mul(c,W));return p&&v}negate(){return new F(this.px,e.neg(this.py),this.pz)}double(){const{a:U,b:$}=n,z=e.mul($,Vn),{px:W,py:b,pz:c}=this;let d=e.ZERO,p=e.ZERO,v=e.ZERO,S=e.mul(W,W),A=e.mul(b,b),P=e.mul(c,c),w=e.mul(W,b);return w=e.add(w,w),v=e.mul(W,c),v=e.add(v,v),d=e.mul(U,v),p=e.mul(z,P),p=e.add(d,p),d=e.sub(A,p),p=e.add(A,p),p=e.mul(d,p),d=e.mul(w,d),v=e.mul(z,v),P=e.mul(U,P),w=e.sub(S,P),w=e.mul(U,w),w=e.add(w,v),v=e.add(S,S),S=e.add(v,S),S=e.add(S,P),S=e.mul(S,w),p=e.add(p,S),P=e.mul(b,c),P=e.add(P,P),S=e.mul(P,w),d=e.sub(d,S),v=e.mul(P,A),v=e.add(v,v),v=e.add(v,v),new F(d,p,v)}add(U){C(U);const{px:$,py:z,pz:W}=this,{px:b,py:c,pz:d}=U;let p=e.ZERO,v=e.ZERO,S=e.ZERO;const A=n.a,P=e.mul(n.b,Vn);let w=e.mul($,b),l=e.mul(z,c),_=e.mul(W,d),nt=e.add($,z),J=e.add(b,c);nt=e.mul(nt,J),J=e.add(w,l),nt=e.sub(nt,J),J=e.add($,W);let it=e.add(b,d);return J=e.mul(J,it),it=e.add(w,_),J=e.sub(J,it),it=e.add(z,W),p=e.add(c,d),it=e.mul(it,p),p=e.add(l,_),it=e.sub(it,p),S=e.mul(A,J),p=e.mul(P,_),S=e.add(p,S),p=e.sub(l,S),S=e.add(l,S),v=e.mul(p,S),l=e.add(w,w),l=e.add(l,w),_=e.mul(A,_),J=e.mul(P,J),l=e.add(l,_),_=e.sub(w,_),_=e.mul(A,_),J=e.add(J,_),w=e.mul(l,J),v=e.add(v,w),w=e.mul(it,J),p=e.mul(nt,p),p=e.sub(p,w),w=e.mul(nt,l),S=e.mul(it,S),S=e.add(S,w),new F(p,v,S)}subtract(U){return this.add(U.negate())}is0(){return this.equals(F.ZERO)}multiply(U){const{endo:$}=t;if(!r.isValidNot0(U))throw new Error("invalid scalar: out of range");let z,W;const b=c=>G.wNAFCached(this,c,F.normalizeZ);if($){const{k1neg:c,k1:d,k2neg:p,k2:v}=$.splitScalar(U),{p:S,f:A}=b(d),{p:P,f:w}=b(v);W=A.add(w),z=V($.beta,S,P,c,p)}else{const{p:c,f:d}=b(U);z=c,W=d}return F.normalizeZ([z,W])[0]}multiplyUnsafe(U){const{endo:$}=t,z=this;if(!r.isValid(U))throw new Error("invalid scalar: out of range");if(U===bn||z.is0())return F.ZERO;if(U===vn)return z;if(G.hasPrecomputes(this))return this.multiply(U);if($){const{k1neg:W,k1:b,k2neg:c,k2:d}=$.splitScalar(U),{p1:p,p2:v}=tf(F,z,b,d);return V($.beta,p,v,W,c)}else return G.wNAFCachedUnsafe(z,U)}multiplyAndAddUnsafe(U,$,z){const W=this.multiplyUnsafe($).add(U.multiplyUnsafe(z));return W.is0()?void 0:W}toAffine(U){return K(this,U)}isTorsionFree(){const{isTorsionFree:U}=t;return s===vn?!0:U?U(F,this):G.wNAFCachedUnsafe(this,i).is0()}clearCofactor(){const{clearCofactor:U}=t;return s===vn?this:U?U(F,this):this.multiplyUnsafe(s)}toBytes(U=!0){return Ur("isCompressed",U),this.assertValidity(),m(F,this,U)}toRawBytes(U=!0){return this.toBytes(U)}toHex(U=!0){return Or(this.toBytes(U))}toString(){return`<Point ${this.is0()?"ZERO":this.toHex()}>`}}F.BASE=new F(n.Gx,n.Gy,e.ONE),F.ZERO=new F(e.ZERO,e.ONE,e.ZERO),F.Fp=e,F.Fn=r;const j=r.BITS,G=Ba(F,t.endo?Math.ceil(j/2):j);return F}function Tc(n){return Uint8Array.of(n?2:3)}function Ph(n,t,e={}){Dr(t,{hash:"function"},{hmac:"function",lowS:"boolean",randomBytes:"function",bits2int:"function",bits2int_modN:"function"});const r=t.randomBytes||Ts,s=t.hmac||((z,...W)=>Rc(t.hash,z,Re(...W))),{Fp:i,Fn:o}=n,{ORDER:a,BITS:h}=o;function y(z){const W=a>>vn;return z>W}function m(z){return y(z)?o.neg(z):z}function E(z,W){if(!o.isValidNot0(W))throw new Error(`invalid signature ${z}: out of range 1..CURVE.n`)}class M{constructor(W,b,c){E("r",W),E("s",b),this.r=W,this.s=b,c!=null&&(this.recovery=c),Object.freeze(this)}static fromCompact(W){const b=o.BYTES,c=Pt("compactSignature",W,b*2);return new M(o.fromBytes(c.subarray(0,b)),o.fromBytes(c.subarray(b,b*2)))}static fromDER(W){const{r:b,s:c}=dr.toSig(Pt("DER",W));return new M(b,c)}assertValidity(){}addRecoveryBit(W){return new M(this.r,this.s,W)}recoverPublicKey(W){const b=i.ORDER,{r:c,s:d,recovery:p}=this;if(p==null||![0,1,2,3].includes(p))throw new Error("recovery id invalid");if(a*Bh<b&&p>1)throw new Error("recovery id is ambiguous for h>1 curve");const S=p===2||p===3?c+a:c;if(!i.isValid(S))throw new Error("recovery id 2 or 3 invalid");const A=i.toBytes(S),P=n.fromHex(Re(Tc((p&1)===0),A)),w=o.inv(S),l=q(Pt("msgHash",W)),_=o.create(-l*w),nt=o.create(d*w),J=n.BASE.multiplyUnsafe(_).add(P.multiplyUnsafe(nt));if(J.is0())throw new Error("point at infinify");return J.assertValidity(),J}hasHighS(){return y(this.s)}normalizeS(){return this.hasHighS()?new M(this.r,o.neg(this.s),this.recovery):this}toBytes(W){if(W==="compact")return Re(o.toBytes(this.r),o.toBytes(this.s));if(W==="der")return Bs(dr.hexFromSig(this));throw new Error("invalid format")}toDERRawBytes(){return this.toBytes("der")}toDERHex(){return Or(this.toBytes("der"))}toCompactRawBytes(){return this.toBytes("compact")}toCompactHex(){return Or(this.toBytes("compact"))}}const k=Mc(o,e.allowedPrivateKeyLengths,e.wrapPrivateKey),R={isValidPrivateKey(z){try{return k(z),!0}catch{return!1}},normPrivateKeyToScalar:k,randomPrivateKey:()=>{const z=a;return Ju(r(Ea(z)),z)},precompute(z=8,W=n.BASE){return W.precompute(z,!1)}};function B(z,W=!0){return n.fromPrivateKey(z).toBytes(W)}function I(z){if(typeof z=="bigint")return!1;if(z instanceof n)return!0;const b=Pt("key",z).length,c=i.BYTES,d=c+1,p=2*c+1;if(!(e.allowedPrivateKeyLengths||o.BYTES===d))return b===d||b===p}function C(z,W,b=!0){if(I(z)===!0)throw new Error("first arg must be private key");if(I(W)===!1)throw new Error("second arg must be public key");return n.fromHex(W).multiply(k(z)).toBytes(b)}const K=t.bits2int||function(z){if(z.length>8192)throw new Error("input is too large");const W=Mn(z),b=z.length*8-h;return b>0?W>>BigInt(b):W},q=t.bits2int_modN||function(z){return o.create(K(z))},V=hi(h);function F(z){return xr("num < 2^"+h,z,bn,V),o.toBytes(z)}function j(z,W,b=G){if(["recovered","canonical"].some(nt=>nt in b))throw new Error("sign() legacy options not supported");const{hash:c}=t;let{lowS:d,prehash:p,extraEntropy:v}=b;d==null&&(d=!0),z=Pt("msgHash",z),Ho(b),p&&(z=Pt("prehashed msgHash",c(z)));const S=q(z),A=k(W),P=[F(A),F(S)];if(v!=null&&v!==!1){const nt=v===!0?r(i.BYTES):v;P.push(Pt("extraEntropy",nt))}const w=Re(...P),l=S;function _(nt){const J=K(nt);if(!o.isValidNot0(J))return;const it=o.inv(J),At=n.BASE.multiply(J).toAffine(),dt=o.create(At.x);if(dt===bn)return;const mt=o.create(it*o.create(l+dt*A));if(mt===bn)return;let Ke=(At.x===dt?0:2)|Number(At.y&vn),kt=mt;return d&&y(mt)&&(kt=m(mt),Ke^=1),new M(dt,kt,Ke)}return{seed:w,k2sig:_}}const G={lowS:t.lowS,prehash:!1},Q={lowS:t.lowS,prehash:!1};function U(z,W,b=G){const{seed:c,k2sig:d}=j(z,W,b);return Du(t.hash.outputLen,o.BYTES,s)(c,d)}n.BASE.precompute(8);function $(z,W,b,c=Q){const d=z;W=Pt("msgHash",W),b=Pt("publicKey",b),Ho(c);const{lowS:p,prehash:v,format:S}=c;if("strict"in c)throw new Error("options.strict was renamed to lowS");if(S!==void 0&&!["compact","der","js"].includes(S))throw new Error('format must be "compact", "der" or "js"');const A=typeof d=="string"||ui(d),P=!A&&!S&&typeof d=="object"&&d!==null&&typeof d.r=="bigint"&&typeof d.s=="bigint";if(!A&&!P)throw new Error("invalid signature, expected Uint8Array, hex string or Signature instance");let w,l;try{if(P)if(S===void 0||S==="js")w=new M(d.r,d.s);else throw new Error("invalid format");if(A){try{S!=="compact"&&(w=M.fromDER(d))}catch(kt){if(!(kt instanceof dr.Err))throw kt}!w&&S!=="der"&&(w=M.fromCompact(d))}l=n.fromHex(b)}catch{return!1}if(!w||p&&w.hasHighS())return!1;v&&(W=t.hash(W));const{r:_,s:nt}=w,J=q(W),it=o.inv(nt),At=o.create(J*it),dt=o.create(_*it),mt=n.BASE.multiplyUnsafe(At).add(l.multiplyUnsafe(dt));return mt.is0()?!1:o.create(mt.x)===_}return Object.freeze({getPublicKey:B,getSharedSecret:C,sign:U,verify:$,utils:R,Point:n,Signature:M})}function Lh(n){const t={a:n.a,b:n.b,p:n.Fp.ORDER,n:n.n,h:n.h,Gx:n.Gx,Gy:n.Gy},e=n.Fp,r=Kr(t.n,n.nBitLength),s={Fp:e,Fn:r,allowedPrivateKeyLengths:n.allowedPrivateKeyLengths,allowInfinityPoint:n.allowInfinityPoint,endo:n.endo,wrapPrivateKey:n.wrapPrivateKey,isTorsionFree:n.isTorsionFree,clearCofactor:n.clearCofactor,fromBytes:n.fromBytes,toBytes:n.toBytes};return{CURVE:t,curveOpts:s}}function Ch(n){const{CURVE:t,curveOpts:e}=Lh(n),r={hash:n.hash,hmac:n.hmac,randomBytes:n.randomBytes,lowS:n.lowS,bits2int:n.bits2int,bits2int_modN:n.bits2int_modN};return{CURVE:t,curveOpts:e,ecdsaOpts:r}}function Oh(n,t){return Object.assign({},t,{ProjectivePoint:t.Point,CURVE:n})}function Uh(n){const{CURVE:t,curveOpts:e,ecdsaOpts:r}=Ch(n),s=Th(t,e),i=Ph(s,r,e);return Oh(n,i)}/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */function Nh(n,t){const e=r=>Uh({...n,hash:r});return{...e(t),create:e}}/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */const ii={p:BigInt("0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f"),n:BigInt("0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141"),h:BigInt(1),a:BigInt(0),b:BigInt(7),Gx:BigInt("0x79be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798"),Gy:BigInt("0x483ada7726a3c4655da4fbfc0e1108a8fd17b448a68554199c47d08ffb10d4b8")};BigInt(0);const zh=BigInt(1),ws=BigInt(2),Vo=(n,t)=>(n+t/ws)/t;function Fh(n){const t=ii.p,e=BigInt(3),r=BigInt(6),s=BigInt(11),i=BigInt(22),o=BigInt(23),a=BigInt(44),h=BigInt(88),y=n*n*n%t,m=y*y*n%t,E=Kt(m,e,t)*m%t,M=Kt(E,e,t)*m%t,k=Kt(M,ws,t)*y%t,R=Kt(k,s,t)*k%t,B=Kt(R,i,t)*R%t,I=Kt(B,a,t)*B%t,C=Kt(I,h,t)*I%t,K=Kt(C,a,t)*B%t,q=Kt(K,e,t)*m%t,V=Kt(q,o,t)*R%t,F=Kt(V,r,t)*y%t,j=Kt(F,ws,t);if(!bs.eql(bs.sqr(j),n))throw new Error("Cannot find square root");return j}const bs=Kr(ii.p,void 0,void 0,{sqrt:Fh}),to=Nh({...ii,Fp:bs,lowS:!0,endo:{beta:BigInt("0x7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee"),splitScalar:n=>{const t=ii.n,e=BigInt("0x3086d221a7d46bcde86c90e49284eb15"),r=-zh*BigInt("0xe4437ed6010e88286f547fa90abfe4c3"),s=BigInt("0x114ca50f7a8e2f3f657c1108d9d44cfd8"),i=e,o=BigInt("0x100000000000000000000000000000000"),a=Vo(i*n,t),h=Vo(-r*n,t);let y=zt(n-a*e-h*s,t),m=zt(-a*r-h*i,t);const E=y>o,M=m>o;if(E&&(y=t-y),M&&(m=t-m),y>o||m>o)throw new Error("splitScalar: Endomorphism failed, k="+n);return{k1neg:E,k1:y,k2neg:M,k2:m}}}},ma),Dh=Zt.utils.randomPrivateKey,jo=()=>{const n=Zt.utils.randomPrivateKey(),t=si(n),e=new Uint8Array(64);return e.set(n),e.set(t,32),{publicKey:t,secretKey:e}},si=Zt.getPublicKey;function Go(n){try{return Zt.ExtendedPoint.fromHex(n),!0}catch{return!1}}const eo=(n,t)=>Zt.sign(n,t.slice(0,32)),Kh=Zt.verify,yt=n=>ct.Buffer.isBuffer(n)?n:n instanceof Uint8Array?ct.Buffer.from(n.buffer,n.byteOffset,n.byteLength):ct.Buffer.from(n);class ro{constructor(t){Object.assign(this,t)}encode(){return ct.Buffer.from(Va(kn,this))}static decode(t){return ja(kn,this,t)}static decodeUnchecked(t){return Ka(kn,this,t)}}class qh extends ro{constructor(t){if(super(t),this.enum="",Object.keys(t).length!==1)throw new Error("Enum can only take single value");Object.keys(t).map(e=>{this.enum=e})}}const kn=new Map;var Pc;const Lc=32,ir=32;function Wh(n){return n._bn!==void 0}let Zo=1;class Y extends ro{constructor(t){if(super({}),this._bn=void 0,Wh(t))this._bn=t._bn;else{if(typeof t=="string"){const e=ke.decode(t);if(e.length!=ir)throw new Error("Invalid public key input");this._bn=new zo(e)}else this._bn=new zo(t);if(this._bn.byteLength()>ir)throw new Error("Invalid public key input")}}static unique(){const t=new Y(Zo);return Zo+=1,new Y(t.toBuffer())}equals(t){return this._bn.eq(t._bn)}toBase58(){return ke.encode(this.toBytes())}toJSON(){return this.toBase58()}toBytes(){const t=this.toBuffer();return new Uint8Array(t.buffer,t.byteOffset,t.byteLength)}toBuffer(){const t=this._bn.toArrayLike(ct.Buffer);if(t.length===ir)return t;const e=ct.Buffer.alloc(32);return t.copy(e,32-t.length),e}get[Symbol.toStringTag](){return`PublicKey(${this.toString()})`}toString(){return this.toBase58()}static async createWithSeed(t,e,r){const s=ct.Buffer.concat([t.toBuffer(),ct.Buffer.from(e),r.toBuffer()]),i=Fo(s);return new Y(i)}static createProgramAddressSync(t,e){let r=ct.Buffer.alloc(0);t.forEach(function(i){if(i.length>Lc)throw new TypeError("Max seed length exceeded");r=ct.Buffer.concat([r,yt(i)])}),r=ct.Buffer.concat([r,e.toBuffer(),ct.Buffer.from("ProgramDerivedAddress")]);const s=Fo(r);if(Go(s))throw new Error("Invalid seeds, address must fall off the curve");return new Y(s)}static async createProgramAddress(t,e){return this.createProgramAddressSync(t,e)}static findProgramAddressSync(t,e){let r=255,s;for(;r!=0;){try{const i=t.concat(ct.Buffer.from([r]));s=this.createProgramAddressSync(i,e)}catch(i){if(i instanceof TypeError)throw i;r--;continue}return[s,r]}throw new Error("Unable to find a viable program address nonce")}static async findProgramAddress(t,e){return this.findProgramAddressSync(t,e)}static isOnCurve(t){const e=new Y(t);return Go(e.toBytes())}}Pc=Y;Y.default=new Pc("11111111111111111111111111111111");kn.set(Y,{kind:"struct",fields:[["_bn","u256"]]});class $h{constructor(t){if(this._publicKey=void 0,this._secretKey=void 0,t){const e=yt(t);if(t.length!==64)throw new Error("bad secret key size");this._publicKey=e.slice(32,64),this._secretKey=e.slice(0,32)}else this._secretKey=yt(Dh()),this._publicKey=yt(si(this._secretKey))}get publicKey(){return new Y(this._publicKey)}get secretKey(){return ct.Buffer.concat([this._secretKey,this._publicKey],64)}}const Hh=new Y("BPFLoader1111111111111111111111111111111111"),Ir=1232,bi=127,In=64;class no extends Error{constructor(t){super(`Signature ${t} has expired: block height exceeded.`),this.signature=void 0,this.signature=t}}Object.defineProperty(no.prototype,"name",{value:"TransactionExpiredBlockheightExceededError"});class io extends Error{constructor(t,e){super(`Transaction was not confirmed in ${e.toFixed(2)} seconds. It is unknown if it succeeded or failed. Check signature ${t} using the Solana Explorer or CLI tools.`),this.signature=void 0,this.signature=t}}Object.defineProperty(io.prototype,"name",{value:"TransactionExpiredTimeoutError"});class Qr extends Error{constructor(t){super(`Signature ${t} has expired: the nonce is no longer valid.`),this.signature=void 0,this.signature=t}}Object.defineProperty(Qr.prototype,"name",{value:"TransactionExpiredNonceInvalidError"});class An{constructor(t,e){this.staticAccountKeys=void 0,this.accountKeysFromLookups=void 0,this.staticAccountKeys=t,this.accountKeysFromLookups=e}keySegments(){const t=[this.staticAccountKeys];return this.accountKeysFromLookups&&(t.push(this.accountKeysFromLookups.writable),t.push(this.accountKeysFromLookups.readonly)),t}get(t){for(const e of this.keySegments()){if(t<e.length)return e[t];t-=e.length}}get length(){return this.keySegments().flat().length}compileInstructions(t){if(this.length>256)throw new Error("Account index overflow encountered during compilation");const r=new Map;this.keySegments().flat().forEach((i,o)=>{r.set(i.toBase58(),o)});const s=i=>{const o=r.get(i.toBase58());if(o===void 0)throw new Error("Encountered an unknown instruction account key during compilation");return o};return t.map(i=>({programIdIndex:s(i.programId),accountKeyIndexes:i.keys.map(o=>s(o.pubkey)),data:i.data}))}}const wt=(n="publicKey")=>Ot(32,n),Vh=(n="signature")=>Ot(64,n),Cr=(n="string")=>{const t=ot([st("length"),st("lengthPadding"),Ot(_r(st(),-8),"chars")],n),e=t.decode.bind(t),r=t.encode.bind(t),s=t;return s.decode=(i,o)=>e(i,o).chars.toString(),s.encode=(i,o,a)=>{const h={chars:ct.Buffer.from(i,"utf8")};return r(h,o,a)},s.alloc=i=>st().span+st().span+ct.Buffer.from(i,"utf8").length,s},jh=(n="authorized")=>ot([wt("staker"),wt("withdrawer")],n),Gh=(n="lockup")=>ot([ze("unixTimestamp"),ze("epoch"),wt("custodian")],n),Zh=(n="voteInit")=>ot([wt("nodePubkey"),wt("authorizedVoter"),wt("authorizedWithdrawer"),vt("commission")],n),Yh=(n="voteAuthorizeWithSeedArgs")=>ot([st("voteAuthorizationType"),wt("currentAuthorityDerivedKeyOwnerPubkey"),Cr("currentAuthorityDerivedKeySeed"),wt("newAuthorized")],n);function Cc(n,t){const e=s=>{if(s.span>=0)return s.span;if(typeof s.alloc=="function")return s.alloc(t[s.property]);if("count"in s&&"elementLayout"in s){const i=t[s.property];if(Array.isArray(i))return i.length*e(s.elementLayout)}else if("fields"in s)return Cc({layout:s},t[s.property]);return 0};let r=0;return n.layout.fields.forEach(s=>{r+=e(s)}),r}function Le(n){let t=0,e=0;for(;;){let r=n.shift();if(t|=(r&127)<<e*7,e+=1,!(r&128))break}return t}function Fe(n,t){let e=t;for(;;){let r=e&127;if(e>>=7,e==0){n.push(r);break}else r|=128,n.push(r)}}function xt(n,t){if(!n)throw new Error(t||"Assertion failed")}class vi{constructor(t,e){this.payer=void 0,this.keyMetaMap=void 0,this.payer=t,this.keyMetaMap=e}static compile(t,e){const r=new Map,s=o=>{const a=o.toBase58();let h=r.get(a);return h===void 0&&(h={isSigner:!1,isWritable:!1,isInvoked:!1},r.set(a,h)),h},i=s(e);i.isSigner=!0,i.isWritable=!0;for(const o of t){s(o.programId).isInvoked=!0;for(const a of o.keys){const h=s(a.pubkey);h.isSigner||(h.isSigner=a.isSigner),h.isWritable||(h.isWritable=a.isWritable)}}return new vi(e,r)}getMessageComponents(){const t=[...this.keyMetaMap.entries()];xt(t.length<=256,"Max static account keys length exceeded");const e=t.filter(([,h])=>h.isSigner&&h.isWritable),r=t.filter(([,h])=>h.isSigner&&!h.isWritable),s=t.filter(([,h])=>!h.isSigner&&h.isWritable),i=t.filter(([,h])=>!h.isSigner&&!h.isWritable),o={numRequiredSignatures:e.length+r.length,numReadonlySignedAccounts:r.length,numReadonlyUnsignedAccounts:i.length};{xt(e.length>0,"Expected at least one writable signer key");const[h]=e[0];xt(h===this.payer.toBase58(),"Expected first writable signer key to be the fee payer")}const a=[...e.map(([h])=>new Y(h)),...r.map(([h])=>new Y(h)),...s.map(([h])=>new Y(h)),...i.map(([h])=>new Y(h))];return[o,a]}extractTableLookup(t){const[e,r]=this.drainKeysFoundInLookupTable(t.state.addresses,o=>!o.isSigner&&!o.isInvoked&&o.isWritable),[s,i]=this.drainKeysFoundInLookupTable(t.state.addresses,o=>!o.isSigner&&!o.isInvoked&&!o.isWritable);if(!(e.length===0&&s.length===0))return[{accountKey:t.key,writableIndexes:e,readonlyIndexes:s},{writable:r,readonly:i}]}drainKeysFoundInLookupTable(t,e){const r=new Array,s=new Array;for(const[i,o]of this.keyMetaMap.entries())if(e(o)){const a=new Y(i),h=t.findIndex(y=>y.equals(a));h>=0&&(xt(h<256,"Max lookup table index exceeded"),r.push(h),s.push(a),this.keyMetaMap.delete(i))}return[r,s]}}const Oc="Reached end of buffer unexpectedly";function tr(n){if(n.length===0)throw new Error(Oc);return n.shift()}function Ce(n,...t){const[e]=t;if(t.length===2?e+(t[1]??0)>n.length:e>=n.length)throw new Error(Oc);return n.splice(...t)}class Ze{constructor(t){this.header=void 0,this.accountKeys=void 0,this.recentBlockhash=void 0,this.instructions=void 0,this.indexToProgramIds=new Map,this.header=t.header,this.accountKeys=t.accountKeys.map(e=>new Y(e)),this.recentBlockhash=t.recentBlockhash,this.instructions=t.instructions,this.instructions.forEach(e=>this.indexToProgramIds.set(e.programIdIndex,this.accountKeys[e.programIdIndex]))}get version(){return"legacy"}get staticAccountKeys(){return this.accountKeys}get compiledInstructions(){return this.instructions.map(t=>({programIdIndex:t.programIdIndex,accountKeyIndexes:t.accounts,data:ke.decode(t.data)}))}get addressTableLookups(){return[]}getAccountKeys(){return new An(this.staticAccountKeys)}static compile(t){const e=vi.compile(t.instructions,t.payerKey),[r,s]=e.getMessageComponents(),o=new An(s).compileInstructions(t.instructions).map(a=>({programIdIndex:a.programIdIndex,accounts:a.accountKeyIndexes,data:ke.encode(a.data)}));return new Ze({header:r,accountKeys:s,recentBlockhash:t.recentBlockhash,instructions:o})}isAccountSigner(t){return t<this.header.numRequiredSignatures}isAccountWritable(t){const e=this.header.numRequiredSignatures;if(t>=this.header.numRequiredSignatures){const r=t-e,i=this.accountKeys.length-e-this.header.numReadonlyUnsignedAccounts;return r<i}else{const r=e-this.header.numReadonlySignedAccounts;return t<r}}isProgramId(t){return this.indexToProgramIds.has(t)}programIds(){return[...this.indexToProgramIds.values()]}nonProgramIds(){return this.accountKeys.filter((t,e)=>!this.isProgramId(e))}serialize(){const t=this.accountKeys.length;let e=[];Fe(e,t);const r=this.instructions.map(E=>{const{accounts:M,programIdIndex:k}=E,R=Array.from(ke.decode(E.data));let B=[];Fe(B,M.length);let I=[];return Fe(I,R.length),{programIdIndex:k,keyIndicesCount:ct.Buffer.from(B),keyIndices:M,dataLength:ct.Buffer.from(I),data:R}});let s=[];Fe(s,r.length);let i=ct.Buffer.alloc(Ir);ct.Buffer.from(s).copy(i);let o=s.length;r.forEach(E=>{const k=ot([vt("programIdIndex"),Ot(E.keyIndicesCount.length,"keyIndicesCount"),xe(vt("keyIndex"),E.keyIndices.length,"keyIndices"),Ot(E.dataLength.length,"dataLength"),xe(vt("userdatum"),E.data.length,"data")]).encode(E,i,o);o+=k}),i=i.slice(0,o);const a=ot([Ot(1,"numRequiredSignatures"),Ot(1,"numReadonlySignedAccounts"),Ot(1,"numReadonlyUnsignedAccounts"),Ot(e.length,"keyCount"),xe(wt("key"),t,"keys"),wt("recentBlockhash")]),h={numRequiredSignatures:ct.Buffer.from([this.header.numRequiredSignatures]),numReadonlySignedAccounts:ct.Buffer.from([this.header.numReadonlySignedAccounts]),numReadonlyUnsignedAccounts:ct.Buffer.from([this.header.numReadonlyUnsignedAccounts]),keyCount:ct.Buffer.from(e),keys:this.accountKeys.map(E=>yt(E.toBytes())),recentBlockhash:ke.decode(this.recentBlockhash)};let y=ct.Buffer.alloc(2048);const m=a.encode(h,y);return i.copy(y,m),y.slice(0,m+i.length)}static from(t){let e=[...t];const r=tr(e);if(r!==(r&bi))throw new Error("Versioned messages must be deserialized with VersionedMessage.deserialize()");const s=tr(e),i=tr(e),o=Le(e);let a=[];for(let M=0;M<o;M++){const k=Ce(e,0,ir);a.push(new Y(ct.Buffer.from(k)))}const h=Ce(e,0,ir),y=Le(e);let m=[];for(let M=0;M<y;M++){const k=tr(e),R=Le(e),B=Ce(e,0,R),I=Le(e),C=Ce(e,0,I),K=ke.encode(ct.Buffer.from(C));m.push({programIdIndex:k,accounts:B,data:K})}const E={header:{numRequiredSignatures:r,numReadonlySignedAccounts:s,numReadonlyUnsignedAccounts:i},recentBlockhash:ke.encode(ct.Buffer.from(h)),accountKeys:a,instructions:m};return new Ze(E)}}class zr{constructor(t){this.header=void 0,this.staticAccountKeys=void 0,this.recentBlockhash=void 0,this.compiledInstructions=void 0,this.addressTableLookups=void 0,this.header=t.header,this.staticAccountKeys=t.staticAccountKeys,this.recentBlockhash=t.recentBlockhash,this.compiledInstructions=t.compiledInstructions,this.addressTableLookups=t.addressTableLookups}get version(){return 0}get numAccountKeysFromLookups(){let t=0;for(const e of this.addressTableLookups)t+=e.readonlyIndexes.length+e.writableIndexes.length;return t}getAccountKeys(t){let e;if(t&&"accountKeysFromLookups"in t&&t.accountKeysFromLookups){if(this.numAccountKeysFromLookups!=t.accountKeysFromLookups.writable.length+t.accountKeysFromLookups.readonly.length)throw new Error("Failed to get account keys because of a mismatch in the number of account keys from lookups");e=t.accountKeysFromLookups}else if(t&&"addressLookupTableAccounts"in t&&t.addressLookupTableAccounts)e=this.resolveAddressTableLookups(t.addressLookupTableAccounts);else if(this.addressTableLookups.length>0)throw new Error("Failed to get account keys because address table lookups were not resolved");return new An(this.staticAccountKeys,e)}isAccountSigner(t){return t<this.header.numRequiredSignatures}isAccountWritable(t){const e=this.header.numRequiredSignatures,r=this.staticAccountKeys.length;if(t>=r){const s=t-r,i=this.addressTableLookups.reduce((o,a)=>o+a.writableIndexes.length,0);return s<i}else if(t>=this.header.numRequiredSignatures){const s=t-e,o=r-e-this.header.numReadonlyUnsignedAccounts;return s<o}else{const s=e-this.header.numReadonlySignedAccounts;return t<s}}resolveAddressTableLookups(t){const e={writable:[],readonly:[]};for(const r of this.addressTableLookups){const s=t.find(i=>i.key.equals(r.accountKey));if(!s)throw new Error(`Failed to find address lookup table account for table key ${r.accountKey.toBase58()}`);for(const i of r.writableIndexes)if(i<s.state.addresses.length)e.writable.push(s.state.addresses[i]);else throw new Error(`Failed to find address for index ${i} in address lookup table ${r.accountKey.toBase58()}`);for(const i of r.readonlyIndexes)if(i<s.state.addresses.length)e.readonly.push(s.state.addresses[i]);else throw new Error(`Failed to find address for index ${i} in address lookup table ${r.accountKey.toBase58()}`)}return e}static compile(t){const e=vi.compile(t.instructions,t.payerKey),r=new Array,s={writable:new Array,readonly:new Array},i=t.addressLookupTableAccounts||[];for(const m of i){const E=e.extractTableLookup(m);if(E!==void 0){const[M,{writable:k,readonly:R}]=E;r.push(M),s.writable.push(...k),s.readonly.push(...R)}}const[o,a]=e.getMessageComponents(),y=new An(a,s).compileInstructions(t.instructions);return new zr({header:o,staticAccountKeys:a,recentBlockhash:t.recentBlockhash,compiledInstructions:y,addressTableLookups:r})}serialize(){const t=Array();Fe(t,this.staticAccountKeys.length);const e=this.serializeInstructions(),r=Array();Fe(r,this.compiledInstructions.length);const s=this.serializeAddressTableLookups(),i=Array();Fe(i,this.addressTableLookups.length);const o=ot([vt("prefix"),ot([vt("numRequiredSignatures"),vt("numReadonlySignedAccounts"),vt("numReadonlyUnsignedAccounts")],"header"),Ot(t.length,"staticAccountKeysLength"),xe(wt(),this.staticAccountKeys.length,"staticAccountKeys"),wt("recentBlockhash"),Ot(r.length,"instructionsLength"),Ot(e.length,"serializedInstructions"),Ot(i.length,"addressTableLookupsLength"),Ot(s.length,"serializedAddressTableLookups")]),a=new Uint8Array(Ir),y=o.encode({prefix:128,header:this.header,staticAccountKeysLength:new Uint8Array(t),staticAccountKeys:this.staticAccountKeys.map(m=>m.toBytes()),recentBlockhash:ke.decode(this.recentBlockhash),instructionsLength:new Uint8Array(r),serializedInstructions:e,addressTableLookupsLength:new Uint8Array(i),serializedAddressTableLookups:s},a);return a.slice(0,y)}serializeInstructions(){let t=0;const e=new Uint8Array(Ir);for(const r of this.compiledInstructions){const s=Array();Fe(s,r.accountKeyIndexes.length);const i=Array();Fe(i,r.data.length);const o=ot([vt("programIdIndex"),Ot(s.length,"encodedAccountKeyIndexesLength"),xe(vt(),r.accountKeyIndexes.length,"accountKeyIndexes"),Ot(i.length,"encodedDataLength"),Ot(r.data.length,"data")]);t+=o.encode({programIdIndex:r.programIdIndex,encodedAccountKeyIndexesLength:new Uint8Array(s),accountKeyIndexes:r.accountKeyIndexes,encodedDataLength:new Uint8Array(i),data:r.data},e,t)}return e.slice(0,t)}serializeAddressTableLookups(){let t=0;const e=new Uint8Array(Ir);for(const r of this.addressTableLookups){const s=Array();Fe(s,r.writableIndexes.length);const i=Array();Fe(i,r.readonlyIndexes.length);const o=ot([wt("accountKey"),Ot(s.length,"encodedWritableIndexesLength"),xe(vt(),r.writableIndexes.length,"writableIndexes"),Ot(i.length,"encodedReadonlyIndexesLength"),xe(vt(),r.readonlyIndexes.length,"readonlyIndexes")]);t+=o.encode({accountKey:r.accountKey.toBytes(),encodedWritableIndexesLength:new Uint8Array(s),writableIndexes:r.writableIndexes,encodedReadonlyIndexesLength:new Uint8Array(i),readonlyIndexes:r.readonlyIndexes},e,t)}return e.slice(0,t)}static deserialize(t){let e=[...t];const r=tr(e),s=r&bi;xt(r!==s,"Expected versioned message but received legacy message");const i=s;xt(i===0,`Expected versioned message with version 0 but found version ${i}`);const o={numRequiredSignatures:tr(e),numReadonlySignedAccounts:tr(e),numReadonlyUnsignedAccounts:tr(e)},a=[],h=Le(e);for(let R=0;R<h;R++)a.push(new Y(Ce(e,0,ir)));const y=ke.encode(Ce(e,0,ir)),m=Le(e),E=[];for(let R=0;R<m;R++){const B=tr(e),I=Le(e),C=Ce(e,0,I),K=Le(e),q=new Uint8Array(Ce(e,0,K));E.push({programIdIndex:B,accountKeyIndexes:C,data:q})}const M=Le(e),k=[];for(let R=0;R<M;R++){const B=new Y(Ce(e,0,ir)),I=Le(e),C=Ce(e,0,I),K=Le(e),q=Ce(e,0,K);k.push({accountKey:B,writableIndexes:C,readonlyIndexes:q})}return new zr({header:o,staticAccountKeys:a,recentBlockhash:y,compiledInstructions:E,addressTableLookups:k})}}const so={deserializeMessageVersion(n){const t=n[0],e=t&bi;return e===t?"legacy":e},deserialize:n=>{const t=so.deserializeMessageVersion(n);if(t==="legacy")return Ze.from(n);if(t===0)return zr.deserialize(n);throw new Error(`Transaction message version ${t} deserialization is not supported`)}};let hr=function(n){return n[n.BLOCKHEIGHT_EXCEEDED=0]="BLOCKHEIGHT_EXCEEDED",n[n.PROCESSED=1]="PROCESSED",n[n.TIMED_OUT=2]="TIMED_OUT",n[n.NONCE_INVALID=3]="NONCE_INVALID",n}({});const Jh=ct.Buffer.alloc(In).fill(0);class Bt{constructor(t){this.keys=void 0,this.programId=void 0,this.data=ct.Buffer.alloc(0),this.programId=t.programId,this.keys=t.keys,t.data&&(this.data=t.data)}toJSON(){return{keys:this.keys.map(({pubkey:t,isSigner:e,isWritable:r})=>({pubkey:t.toJSON(),isSigner:e,isWritable:r})),programId:this.programId.toJSON(),data:[...this.data]}}}class Et{get signature(){return this.signatures.length>0?this.signatures[0].signature:null}constructor(t){if(this.signatures=[],this.feePayer=void 0,this.instructions=[],this.recentBlockhash=void 0,this.lastValidBlockHeight=void 0,this.nonceInfo=void 0,this.minNonceContextSlot=void 0,this._message=void 0,this._json=void 0,!!t)if(t.feePayer&&(this.feePayer=t.feePayer),t.signatures&&(this.signatures=t.signatures),Object.prototype.hasOwnProperty.call(t,"nonceInfo")){const{minContextSlot:e,nonceInfo:r}=t;this.minNonceContextSlot=e,this.nonceInfo=r}else if(Object.prototype.hasOwnProperty.call(t,"lastValidBlockHeight")){const{blockhash:e,lastValidBlockHeight:r}=t;this.recentBlockhash=e,this.lastValidBlockHeight=r}else{const{recentBlockhash:e,nonceInfo:r}=t;r&&(this.nonceInfo=r),this.recentBlockhash=e}}toJSON(){return{recentBlockhash:this.recentBlockhash||null,feePayer:this.feePayer?this.feePayer.toJSON():null,nonceInfo:this.nonceInfo?{nonce:this.nonceInfo.nonce,nonceInstruction:this.nonceInfo.nonceInstruction.toJSON()}:null,instructions:this.instructions.map(t=>t.toJSON()),signers:this.signatures.map(({publicKey:t})=>t.toJSON())}}add(...t){if(t.length===0)throw new Error("No instructions");return t.forEach(e=>{"instructions"in e?this.instructions=this.instructions.concat(e.instructions):"data"in e&&"programId"in e&&"keys"in e?this.instructions.push(e):this.instructions.push(new Bt(e))}),this}compileMessage(){if(this._message&&JSON.stringify(this.toJSON())===JSON.stringify(this._json))return this._message;let t,e;if(this.nonceInfo?(t=this.nonceInfo.nonce,this.instructions[0]!=this.nonceInfo.nonceInstruction?e=[this.nonceInfo.nonceInstruction,...this.instructions]:e=this.instructions):(t=this.recentBlockhash,e=this.instructions),!t)throw new Error("Transaction recentBlockhash required");e.length<1&&console.warn("No instructions provided");let r;if(this.feePayer)r=this.feePayer;else if(this.signatures.length>0&&this.signatures[0].publicKey)r=this.signatures[0].publicKey;else throw new Error("Transaction fee payer required");for(let B=0;B<e.length;B++)if(e[B].programId===void 0)throw new Error(`Transaction instruction index ${B} has undefined program id`);const s=[],i=[];e.forEach(B=>{B.keys.forEach(C=>{i.push({...C})});const I=B.programId.toString();s.includes(I)||s.push(I)}),s.forEach(B=>{i.push({pubkey:new Y(B),isSigner:!1,isWritable:!1})});const o=[];i.forEach(B=>{const I=B.pubkey.toString(),C=o.findIndex(K=>K.pubkey.toString()===I);C>-1?(o[C].isWritable=o[C].isWritable||B.isWritable,o[C].isSigner=o[C].isSigner||B.isSigner):o.push(B)}),o.sort(function(B,I){if(B.isSigner!==I.isSigner)return B.isSigner?-1:1;if(B.isWritable!==I.isWritable)return B.isWritable?-1:1;const C={localeMatcher:"best fit",usage:"sort",sensitivity:"variant",ignorePunctuation:!1,numeric:!1,caseFirst:"lower"};return B.pubkey.toBase58().localeCompare(I.pubkey.toBase58(),"en",C)});const a=o.findIndex(B=>B.pubkey.equals(r));if(a>-1){const[B]=o.splice(a,1);B.isSigner=!0,B.isWritable=!0,o.unshift(B)}else o.unshift({pubkey:r,isSigner:!0,isWritable:!0});for(const B of this.signatures){const I=o.findIndex(C=>C.pubkey.equals(B.publicKey));if(I>-1)o[I].isSigner||(o[I].isSigner=!0,console.warn("Transaction references a signature that is unnecessary, only the fee payer and instruction signer accounts should sign a transaction. This behavior is deprecated and will throw an error in the next major version release."));else throw new Error(`unknown signer: ${B.publicKey.toString()}`)}let h=0,y=0,m=0;const E=[],M=[];o.forEach(({pubkey:B,isSigner:I,isWritable:C})=>{I?(E.push(B.toString()),h+=1,C||(y+=1)):(M.push(B.toString()),C||(m+=1))});const k=E.concat(M),R=e.map(B=>{const{data:I,programId:C}=B;return{programIdIndex:k.indexOf(C.toString()),accounts:B.keys.map(K=>k.indexOf(K.pubkey.toString())),data:ke.encode(I)}});return R.forEach(B=>{xt(B.programIdIndex>=0),B.accounts.forEach(I=>xt(I>=0))}),new Ze({header:{numRequiredSignatures:h,numReadonlySignedAccounts:y,numReadonlyUnsignedAccounts:m},accountKeys:k,recentBlockhash:t,instructions:R})}_compile(){const t=this.compileMessage(),e=t.accountKeys.slice(0,t.header.numRequiredSignatures);return this.signatures.length===e.length&&this.signatures.every((s,i)=>e[i].equals(s.publicKey))||(this.signatures=e.map(r=>({signature:null,publicKey:r}))),t}serializeMessage(){return this._compile().serialize()}async getEstimatedFee(t){return(await t.getFeeForMessage(this.compileMessage())).value}setSigners(...t){if(t.length===0)throw new Error("No signers");const e=new Set;this.signatures=t.filter(r=>{const s=r.toString();return e.has(s)?!1:(e.add(s),!0)}).map(r=>({signature:null,publicKey:r}))}sign(...t){if(t.length===0)throw new Error("No signers");const e=new Set,r=[];for(const i of t){const o=i.publicKey.toString();e.has(o)||(e.add(o),r.push(i))}this.signatures=r.map(i=>({signature:null,publicKey:i.publicKey}));const s=this._compile();this._partialSign(s,...r)}partialSign(...t){if(t.length===0)throw new Error("No signers");const e=new Set,r=[];for(const i of t){const o=i.publicKey.toString();e.has(o)||(e.add(o),r.push(i))}const s=this._compile();this._partialSign(s,...r)}_partialSign(t,...e){const r=t.serialize();e.forEach(s=>{const i=eo(r,s.secretKey);this._addSignature(s.publicKey,yt(i))})}addSignature(t,e){this._compile(),this._addSignature(t,e)}_addSignature(t,e){xt(e.length===64);const r=this.signatures.findIndex(s=>t.equals(s.publicKey));if(r<0)throw new Error(`unknown signer: ${t.toString()}`);this.signatures[r].signature=ct.Buffer.from(e)}verifySignatures(t=!0){return!this._getMessageSignednessErrors(this.serializeMessage(),t)}_getMessageSignednessErrors(t,e){const r={};for(const{signature:s,publicKey:i}of this.signatures)s===null?e&&(r.missing||(r.missing=[])).push(i):Kh(s,t,i.toBytes())||(r.invalid||(r.invalid=[])).push(i);return r.invalid||r.missing?r:void 0}serialize(t){const{requireAllSignatures:e,verifySignatures:r}=Object.assign({requireAllSignatures:!0,verifySignatures:!0},t),s=this.serializeMessage();if(r){const i=this._getMessageSignednessErrors(s,e);if(i){let o="Signature verification failed.";throw i.invalid&&(o+=`
Invalid signature for public key${i.invalid.length===1?"":"(s)"} [\`${i.invalid.map(a=>a.toBase58()).join("`, `")}\`].`),i.missing&&(o+=`
Missing signature for public key${i.missing.length===1?"":"(s)"} [\`${i.missing.map(a=>a.toBase58()).join("`, `")}\`].`),new Error(o)}}return this._serialize(s)}_serialize(t){const{signatures:e}=this,r=[];Fe(r,e.length);const s=r.length+e.length*64+t.length,i=ct.Buffer.alloc(s);return xt(e.length<256),ct.Buffer.from(r).copy(i,0),e.forEach(({signature:o},a)=>{o!==null&&(xt(o.length===64,"signature has invalid length"),ct.Buffer.from(o).copy(i,r.length+a*64))}),t.copy(i,r.length+e.length*64),xt(i.length<=Ir,`Transaction too large: ${i.length} > ${Ir}`),i}get keys(){return xt(this.instructions.length===1),this.instructions[0].keys.map(t=>t.pubkey)}get programId(){return xt(this.instructions.length===1),this.instructions[0].programId}get data(){return xt(this.instructions.length===1),this.instructions[0].data}static from(t){let e=[...t];const r=Le(e);let s=[];for(let i=0;i<r;i++){const o=Ce(e,0,In);s.push(ke.encode(ct.Buffer.from(o)))}return Et.populate(Ze.from(e),s)}static populate(t,e=[]){const r=new Et;return r.recentBlockhash=t.recentBlockhash,t.header.numRequiredSignatures>0&&(r.feePayer=t.accountKeys[0]),e.forEach((s,i)=>{const o={signature:s==ke.encode(Jh)?null:ke.decode(s),publicKey:t.accountKeys[i]};r.signatures.push(o)}),t.instructions.forEach(s=>{const i=s.accounts.map(o=>{const a=t.accountKeys[o];return{pubkey:a,isSigner:r.signatures.some(h=>h.publicKey.toString()===a.toString())||t.isAccountSigner(o),isWritable:t.isAccountWritable(o)}});r.instructions.push(new Bt({keys:i,programId:t.accountKeys[s.programIdIndex],data:ke.decode(s.data)}))}),r._message=t,r._json=r.toJSON(),r}}class oo{constructor(t){this.payerKey=void 0,this.instructions=void 0,this.recentBlockhash=void 0,this.payerKey=t.payerKey,this.instructions=t.instructions,this.recentBlockhash=t.recentBlockhash}static decompile(t,e){const{header:r,compiledInstructions:s,recentBlockhash:i}=t,{numRequiredSignatures:o,numReadonlySignedAccounts:a,numReadonlyUnsignedAccounts:h}=r,y=o-a;xt(y>0,"Message header is invalid");const m=t.staticAccountKeys.length-o-h;xt(m>=0,"Message header is invalid");const E=t.getAccountKeys(e),M=E.get(0);if(M===void 0)throw new Error("Failed to decompile message because no account keys were found");const k=[];for(const R of s){const B=[];for(const C of R.accountKeyIndexes){const K=E.get(C);if(K===void 0)throw new Error(`Failed to find key for account key index ${C}`);const q=C<o;let V;q?V=C<y:C<E.staticAccountKeys.length?V=C-o<m:V=C-E.staticAccountKeys.length<E.accountKeysFromLookups.writable.length,B.push({pubkey:K,isSigner:C<r.numRequiredSignatures,isWritable:V})}const I=E.get(R.programIdIndex);if(I===void 0)throw new Error(`Failed to find program id for program id index ${R.programIdIndex}`);k.push(new Bt({programId:I,data:yt(R.data),keys:B}))}return new oo({payerKey:M,instructions:k,recentBlockhash:i})}compileToLegacyMessage(){return Ze.compile({payerKey:this.payerKey,recentBlockhash:this.recentBlockhash,instructions:this.instructions})}compileToV0Message(t){return zr.compile({payerKey:this.payerKey,recentBlockhash:this.recentBlockhash,instructions:this.instructions,addressLookupTableAccounts:t})}}class ao{get version(){return this.message.version}constructor(t,e){if(this.signatures=void 0,this.message=void 0,e!==void 0)xt(e.length===t.header.numRequiredSignatures,"Expected signatures length to be equal to the number of required signatures"),this.signatures=e;else{const r=[];for(let s=0;s<t.header.numRequiredSignatures;s++)r.push(new Uint8Array(In));this.signatures=r}this.message=t}serialize(){const t=this.message.serialize(),e=Array();Fe(e,this.signatures.length);const r=ot([Ot(e.length,"encodedSignaturesLength"),xe(Vh(),this.signatures.length,"signatures"),Ot(t.length,"serializedMessage")]),s=new Uint8Array(2048),i=r.encode({encodedSignaturesLength:new Uint8Array(e),signatures:this.signatures,serializedMessage:t},s);return s.slice(0,i)}static deserialize(t){let e=[...t];const r=[],s=Le(e);for(let o=0;o<s;o++)r.push(new Uint8Array(Ce(e,0,In)));const i=so.deserialize(new Uint8Array(e));return new ao(i,r)}sign(t){const e=this.message.serialize(),r=this.message.staticAccountKeys.slice(0,this.message.header.numRequiredSignatures);for(const s of t){const i=r.findIndex(o=>o.equals(s.publicKey));xt(i>=0,`Cannot sign with non signer key ${s.publicKey.toBase58()}`),this.signatures[i]=eo(e,s.secretKey)}}addSignature(t,e){xt(e.byteLength===64,"Signature must be 64 bytes long");const s=this.message.staticAccountKeys.slice(0,this.message.header.numRequiredSignatures).findIndex(i=>i.equals(t));xt(s>=0,`Can not add signature; \`${t.toBase58()}\` is not required to sign this transaction`),this.signatures[s]=e}}const Xh=160,Qh=64,td=Xh/Qh,Uc=1e3/td,Xe=new Y("SysvarC1ock11111111111111111111111111111111"),ed=new Y("SysvarEpochSchedu1e111111111111111111111111"),rd=new Y("Sysvar1nstructions1111111111111111111111111"),Gn=new Y("SysvarRecentB1ockHashes11111111111111111111"),an=new Y("SysvarRent111111111111111111111111111111111"),nd=new Y("SysvarRewards111111111111111111111111111111"),id=new Y("SysvarS1otHashes111111111111111111111111111"),sd=new Y("SysvarS1otHistory11111111111111111111111111"),Zn=new Y("SysvarStakeHistory1111111111111111111111111");class Bn extends Error{constructor({action:t,signature:e,transactionMessage:r,logs:s}){const i=s?`Logs: 
${JSON.stringify(s.slice(-10),null,2)}. `:"",o="\nCatch the `SendTransactionError` and call `getLogs()` on it for full details.";let a;switch(t){case"send":a=`Transaction ${e} resulted in an error. 
${r}. `+i+o;break;case"simulate":a=`Simulation failed. 
Message: ${r}. 
`+i+o;break;default:a=`Unknown action '${(h=>h)(t)}'`}super(a),this.signature=void 0,this.transactionMessage=void 0,this.transactionLogs=void 0,this.signature=e,this.transactionMessage=r,this.transactionLogs=s||void 0}get transactionError(){return{message:this.transactionMessage,logs:Array.isArray(this.transactionLogs)?this.transactionLogs:void 0}}get logs(){const t=this.transactionLogs;if(!(t!=null&&typeof t=="object"&&"then"in t))return t}async getLogs(t){return Array.isArray(this.transactionLogs)||(this.transactionLogs=new Promise((e,r)=>{t.getTransaction(this.signature).then(s=>{if(s&&s.meta&&s.meta.logMessages){const i=s.meta.logMessages;this.transactionLogs=i,e(i)}else r(new Error("Log messages not found"))}).catch(r)})),await this.transactionLogs}}const od={JSON_RPC_SERVER_ERROR_BLOCK_CLEANED_UP:-32001,JSON_RPC_SERVER_ERROR_SEND_TRANSACTION_PREFLIGHT_FAILURE:-32002,JSON_RPC_SERVER_ERROR_TRANSACTION_SIGNATURE_VERIFICATION_FAILURE:-32003,JSON_RPC_SERVER_ERROR_BLOCK_NOT_AVAILABLE:-32004,JSON_RPC_SERVER_ERROR_NODE_UNHEALTHY:-32005,JSON_RPC_SERVER_ERROR_TRANSACTION_PRECOMPILE_VERIFICATION_FAILURE:-32006,JSON_RPC_SERVER_ERROR_SLOT_SKIPPED:-32007,JSON_RPC_SERVER_ERROR_NO_SNAPSHOT:-32008,JSON_RPC_SERVER_ERROR_LONG_TERM_STORAGE_SLOT_SKIPPED:-32009,JSON_RPC_SERVER_ERROR_KEY_EXCLUDED_FROM_SECONDARY_INDEX:-32010,JSON_RPC_SERVER_ERROR_TRANSACTION_HISTORY_NOT_AVAILABLE:-32011,JSON_RPC_SCAN_ERROR:-32012,JSON_RPC_SERVER_ERROR_TRANSACTION_SIGNATURE_LEN_MISMATCH:-32013,JSON_RPC_SERVER_ERROR_BLOCK_STATUS_NOT_AVAILABLE_YET:-32014,JSON_RPC_SERVER_ERROR_UNSUPPORTED_TRANSACTION_VERSION:-32015,JSON_RPC_SERVER_ERROR_MIN_CONTEXT_SLOT_NOT_REACHED:-32016};class at extends Error{constructor({code:t,message:e,data:r},s){super(s!=null?`${s}: ${e}`:e),this.code=void 0,this.data=void 0,this.code=t,this.data=r,this.name="SolanaJSONRPCError"}}async function vs(n,t,e,r){const s=r&&{skipPreflight:r.skipPreflight,preflightCommitment:r.preflightCommitment||r.commitment,maxRetries:r.maxRetries,minContextSlot:r.minContextSlot},i=await n.sendTransaction(t,e,s);let o;if(t.recentBlockhash!=null&&t.lastValidBlockHeight!=null)o=(await n.confirmTransaction({abortSignal:r==null?void 0:r.abortSignal,signature:i,blockhash:t.recentBlockhash,lastValidBlockHeight:t.lastValidBlockHeight},r&&r.commitment)).value;else if(t.minNonceContextSlot!=null&&t.nonceInfo!=null){const{nonceInstruction:a}=t.nonceInfo,h=a.keys[0].pubkey;o=(await n.confirmTransaction({abortSignal:r==null?void 0:r.abortSignal,minContextSlot:t.minNonceContextSlot,nonceAccountPubkey:h,nonceValue:t.nonceInfo.nonce,signature:i},r&&r.commitment)).value}else(r==null?void 0:r.abortSignal)!=null&&console.warn("sendAndConfirmTransaction(): A transaction with a deprecated confirmation strategy was supplied along with an `abortSignal`. Only transactions having `lastValidBlockHeight` or a combination of `nonceInfo` and `minNonceContextSlot` are abortable."),o=(await n.confirmTransaction(i,r&&r.commitment)).value;if(o.err)throw i!=null?new Bn({action:"send",signature:i,transactionMessage:`Status: (${JSON.stringify(o)})`}):new Error(`Transaction ${i} failed (${JSON.stringify(o)})`);return i}function Tr(n){return new Promise(t=>setTimeout(t,n))}function bt(n,t){const e=n.layout.span>=0?n.layout.span:Cc(n,t),r=ct.Buffer.alloc(e),s=Object.assign({instruction:n.index},t);return n.layout.encode(s,r),r}function _t(n,t){let e;try{e=n.layout.decode(t)}catch(r){throw new Error("invalid instruction; "+r)}if(e.instruction!==n.index)throw new Error(`invalid instruction; instruction index mismatch ${e.instruction} != ${n.index}`);return e}const Nc=be("lamportsPerSignature"),zc=ot([st("version"),st("state"),wt("authorizedPubkey"),wt("nonce"),ot([Nc],"feeCalculator")]),ks=zc.span;class ki{constructor(t){this.authorizedPubkey=void 0,this.nonce=void 0,this.feeCalculator=void 0,this.authorizedPubkey=t.authorizedPubkey,this.nonce=t.nonce,this.feeCalculator=t.feeCalculator}static fromAccountData(t){const e=zc.decode(yt(t),0);return new ki({authorizedPubkey:new Y(e.authorizedPubkey),nonce:new Y(e.nonce).toString(),feeCalculator:e.feeCalculator})}}function cn(n){const t=Ot(8,n),e=t.decode.bind(t),r=t.encode.bind(t),s=t,i=Ll();return s.decode=(o,a)=>{const h=e(o,a);return i.decode(h)},s.encode=(o,a,h)=>{const y=i.encode(o);return r(y,a,h)},s}class ad{constructor(){}static decodeInstructionType(t){this.checkProgramId(t.programId);const r=st("instruction").decode(t.data);let s;for(const[i,o]of Object.entries(Tt))if(o.index==r){s=i;break}if(!s)throw new Error("Instruction type incorrect; not a SystemInstruction");return s}static decodeCreateAccount(t){this.checkProgramId(t.programId),this.checkKeyLength(t.keys,2);const{lamports:e,space:r,programId:s}=_t(Tt.Create,t.data);return{fromPubkey:t.keys[0].pubkey,newAccountPubkey:t.keys[1].pubkey,lamports:e,space:r,programId:new Y(s)}}static decodeTransfer(t){this.checkProgramId(t.programId),this.checkKeyLength(t.keys,2);const{lamports:e}=_t(Tt.Transfer,t.data);return{fromPubkey:t.keys[0].pubkey,toPubkey:t.keys[1].pubkey,lamports:e}}static decodeTransferWithSeed(t){this.checkProgramId(t.programId),this.checkKeyLength(t.keys,3);const{lamports:e,seed:r,programId:s}=_t(Tt.TransferWithSeed,t.data);return{fromPubkey:t.keys[0].pubkey,basePubkey:t.keys[1].pubkey,toPubkey:t.keys[2].pubkey,lamports:e,seed:r,programId:new Y(s)}}static decodeAllocate(t){this.checkProgramId(t.programId),this.checkKeyLength(t.keys,1);const{space:e}=_t(Tt.Allocate,t.data);return{accountPubkey:t.keys[0].pubkey,space:e}}static decodeAllocateWithSeed(t){this.checkProgramId(t.programId),this.checkKeyLength(t.keys,1);const{base:e,seed:r,space:s,programId:i}=_t(Tt.AllocateWithSeed,t.data);return{accountPubkey:t.keys[0].pubkey,basePubkey:new Y(e),seed:r,space:s,programId:new Y(i)}}static decodeAssign(t){this.checkProgramId(t.programId),this.checkKeyLength(t.keys,1);const{programId:e}=_t(Tt.Assign,t.data);return{accountPubkey:t.keys[0].pubkey,programId:new Y(e)}}static decodeAssignWithSeed(t){this.checkProgramId(t.programId),this.checkKeyLength(t.keys,1);const{base:e,seed:r,programId:s}=_t(Tt.AssignWithSeed,t.data);return{accountPubkey:t.keys[0].pubkey,basePubkey:new Y(e),seed:r,programId:new Y(s)}}static decodeCreateWithSeed(t){this.checkProgramId(t.programId),this.checkKeyLength(t.keys,2);const{base:e,seed:r,lamports:s,space:i,programId:o}=_t(Tt.CreateWithSeed,t.data);return{fromPubkey:t.keys[0].pubkey,newAccountPubkey:t.keys[1].pubkey,basePubkey:new Y(e),seed:r,lamports:s,space:i,programId:new Y(o)}}static decodeNonceInitialize(t){this.checkProgramId(t.programId),this.checkKeyLength(t.keys,3);const{authorized:e}=_t(Tt.InitializeNonceAccount,t.data);return{noncePubkey:t.keys[0].pubkey,authorizedPubkey:new Y(e)}}static decodeNonceAdvance(t){return this.checkProgramId(t.programId),this.checkKeyLength(t.keys,3),_t(Tt.AdvanceNonceAccount,t.data),{noncePubkey:t.keys[0].pubkey,authorizedPubkey:t.keys[2].pubkey}}static decodeNonceWithdraw(t){this.checkProgramId(t.programId),this.checkKeyLength(t.keys,5);const{lamports:e}=_t(Tt.WithdrawNonceAccount,t.data);return{noncePubkey:t.keys[0].pubkey,toPubkey:t.keys[1].pubkey,authorizedPubkey:t.keys[4].pubkey,lamports:e}}static decodeNonceAuthorize(t){this.checkProgramId(t.programId),this.checkKeyLength(t.keys,2);const{authorized:e}=_t(Tt.AuthorizeNonceAccount,t.data);return{noncePubkey:t.keys[0].pubkey,authorizedPubkey:t.keys[1].pubkey,newAuthorizedPubkey:new Y(e)}}static checkProgramId(t){if(!t.equals(ye.programId))throw new Error("invalid instruction; programId is not SystemProgram")}static checkKeyLength(t,e){if(t.length<e)throw new Error(`invalid instruction; found ${t.length} keys, expected at least ${e}`)}}const Tt=Object.freeze({Create:{index:0,layout:ot([st("instruction"),ze("lamports"),ze("space"),wt("programId")])},Assign:{index:1,layout:ot([st("instruction"),wt("programId")])},Transfer:{index:2,layout:ot([st("instruction"),cn("lamports")])},CreateWithSeed:{index:3,layout:ot([st("instruction"),wt("base"),Cr("seed"),ze("lamports"),ze("space"),wt("programId")])},AdvanceNonceAccount:{index:4,layout:ot([st("instruction")])},WithdrawNonceAccount:{index:5,layout:ot([st("instruction"),ze("lamports")])},InitializeNonceAccount:{index:6,layout:ot([st("instruction"),wt("authorized")])},AuthorizeNonceAccount:{index:7,layout:ot([st("instruction"),wt("authorized")])},Allocate:{index:8,layout:ot([st("instruction"),ze("space")])},AllocateWithSeed:{index:9,layout:ot([st("instruction"),wt("base"),Cr("seed"),ze("space"),wt("programId")])},AssignWithSeed:{index:10,layout:ot([st("instruction"),wt("base"),Cr("seed"),wt("programId")])},TransferWithSeed:{index:11,layout:ot([st("instruction"),cn("lamports"),Cr("seed"),wt("programId")])},UpgradeNonceAccount:{index:12,layout:ot([st("instruction")])}});class ye{constructor(){}static createAccount(t){const e=Tt.Create,r=bt(e,{lamports:t.lamports,space:t.space,programId:yt(t.programId.toBuffer())});return new Bt({keys:[{pubkey:t.fromPubkey,isSigner:!0,isWritable:!0},{pubkey:t.newAccountPubkey,isSigner:!0,isWritable:!0}],programId:this.programId,data:r})}static transfer(t){let e,r;if("basePubkey"in t){const s=Tt.TransferWithSeed;e=bt(s,{lamports:BigInt(t.lamports),seed:t.seed,programId:yt(t.programId.toBuffer())}),r=[{pubkey:t.fromPubkey,isSigner:!1,isWritable:!0},{pubkey:t.basePubkey,isSigner:!0,isWritable:!1},{pubkey:t.toPubkey,isSigner:!1,isWritable:!0}]}else{const s=Tt.Transfer;e=bt(s,{lamports:BigInt(t.lamports)}),r=[{pubkey:t.fromPubkey,isSigner:!0,isWritable:!0},{pubkey:t.toPubkey,isSigner:!1,isWritable:!0}]}return new Bt({keys:r,programId:this.programId,data:e})}static assign(t){let e,r;if("basePubkey"in t){const s=Tt.AssignWithSeed;e=bt(s,{base:yt(t.basePubkey.toBuffer()),seed:t.seed,programId:yt(t.programId.toBuffer())}),r=[{pubkey:t.accountPubkey,isSigner:!1,isWritable:!0},{pubkey:t.basePubkey,isSigner:!0,isWritable:!1}]}else{const s=Tt.Assign;e=bt(s,{programId:yt(t.programId.toBuffer())}),r=[{pubkey:t.accountPubkey,isSigner:!0,isWritable:!0}]}return new Bt({keys:r,programId:this.programId,data:e})}static createAccountWithSeed(t){const e=Tt.CreateWithSeed,r=bt(e,{base:yt(t.basePubkey.toBuffer()),seed:t.seed,lamports:t.lamports,space:t.space,programId:yt(t.programId.toBuffer())});let s=[{pubkey:t.fromPubkey,isSigner:!0,isWritable:!0},{pubkey:t.newAccountPubkey,isSigner:!1,isWritable:!0}];return t.basePubkey.equals(t.fromPubkey)||s.push({pubkey:t.basePubkey,isSigner:!0,isWritable:!1}),new Bt({keys:s,programId:this.programId,data:r})}static createNonceAccount(t){const e=new Et;"basePubkey"in t&&"seed"in t?e.add(ye.createAccountWithSeed({fromPubkey:t.fromPubkey,newAccountPubkey:t.noncePubkey,basePubkey:t.basePubkey,seed:t.seed,lamports:t.lamports,space:ks,programId:this.programId})):e.add(ye.createAccount({fromPubkey:t.fromPubkey,newAccountPubkey:t.noncePubkey,lamports:t.lamports,space:ks,programId:this.programId}));const r={noncePubkey:t.noncePubkey,authorizedPubkey:t.authorizedPubkey};return e.add(this.nonceInitialize(r)),e}static nonceInitialize(t){const e=Tt.InitializeNonceAccount,r=bt(e,{authorized:yt(t.authorizedPubkey.toBuffer())}),s={keys:[{pubkey:t.noncePubkey,isSigner:!1,isWritable:!0},{pubkey:Gn,isSigner:!1,isWritable:!1},{pubkey:an,isSigner:!1,isWritable:!1}],programId:this.programId,data:r};return new Bt(s)}static nonceAdvance(t){const e=Tt.AdvanceNonceAccount,r=bt(e),s={keys:[{pubkey:t.noncePubkey,isSigner:!1,isWritable:!0},{pubkey:Gn,isSigner:!1,isWritable:!1},{pubkey:t.authorizedPubkey,isSigner:!0,isWritable:!1}],programId:this.programId,data:r};return new Bt(s)}static nonceWithdraw(t){const e=Tt.WithdrawNonceAccount,r=bt(e,{lamports:t.lamports});return new Bt({keys:[{pubkey:t.noncePubkey,isSigner:!1,isWritable:!0},{pubkey:t.toPubkey,isSigner:!1,isWritable:!0},{pubkey:Gn,isSigner:!1,isWritable:!1},{pubkey:an,isSigner:!1,isWritable:!1},{pubkey:t.authorizedPubkey,isSigner:!0,isWritable:!1}],programId:this.programId,data:r})}static nonceAuthorize(t){const e=Tt.AuthorizeNonceAccount,r=bt(e,{authorized:yt(t.newAuthorizedPubkey.toBuffer())});return new Bt({keys:[{pubkey:t.noncePubkey,isSigner:!1,isWritable:!0},{pubkey:t.authorizedPubkey,isSigner:!0,isWritable:!1}],programId:this.programId,data:r})}static allocate(t){let e,r;if("basePubkey"in t){const s=Tt.AllocateWithSeed;e=bt(s,{base:yt(t.basePubkey.toBuffer()),seed:t.seed,space:t.space,programId:yt(t.programId.toBuffer())}),r=[{pubkey:t.accountPubkey,isSigner:!1,isWritable:!0},{pubkey:t.basePubkey,isSigner:!0,isWritable:!1}]}else{const s=Tt.Allocate;e=bt(s,{space:t.space}),r=[{pubkey:t.accountPubkey,isSigner:!0,isWritable:!0}]}return new Bt({keys:r,programId:this.programId,data:e})}}ye.programId=new Y("11111111111111111111111111111111");const cd=Ir-300;class Fr{constructor(){}static getMinNumSignatures(t){return 2*(Math.ceil(t/Fr.chunkSize)+1+1)}static async load(t,e,r,s,i){{const E=await t.getMinimumBalanceForRentExemption(i.length),M=await t.getAccountInfo(r.publicKey,"confirmed");let k=null;if(M!==null){if(M.executable)return console.error("Program load failed, account is already executable"),!1;M.data.length!==i.length&&(k=k||new Et,k.add(ye.allocate({accountPubkey:r.publicKey,space:i.length}))),M.owner.equals(s)||(k=k||new Et,k.add(ye.assign({accountPubkey:r.publicKey,programId:s}))),M.lamports<E&&(k=k||new Et,k.add(ye.transfer({fromPubkey:e.publicKey,toPubkey:r.publicKey,lamports:E-M.lamports})))}else k=new Et().add(ye.createAccount({fromPubkey:e.publicKey,newAccountPubkey:r.publicKey,lamports:E>0?E:1,space:i.length,programId:s}));k!==null&&await vs(t,k,[e,r],{commitment:"confirmed"})}const o=ot([st("instruction"),st("offset"),st("bytesLength"),st("bytesLengthPadding"),xe(vt("byte"),_r(st(),-8),"bytes")]),a=Fr.chunkSize;let h=0,y=i,m=[];for(;y.length>0;){const E=y.slice(0,a),M=ct.Buffer.alloc(a+16);o.encode({instruction:0,offset:h,bytes:E,bytesLength:0,bytesLengthPadding:0},M);const k=new Et().add({keys:[{pubkey:r.publicKey,isSigner:!0,isWritable:!0}],programId:s,data:M});m.push(vs(t,k,[e,r],{commitment:"confirmed"})),t._rpcEndpoint.includes("solana.com")&&await Tr(1e3/4),h+=a,y=y.slice(a)}await Promise.all(m);{const E=ot([st("instruction")]),M=ct.Buffer.alloc(E.span);E.encode({instruction:1},M);const k=new Et().add({keys:[{pubkey:r.publicKey,isSigner:!0,isWritable:!0},{pubkey:an,isSigner:!1,isWritable:!1}],programId:s,data:M}),R="processed",B=await t.sendTransaction(k,[e,r],{preflightCommitment:R}),{context:I,value:C}=await t.confirmTransaction({signature:B,lastValidBlockHeight:k.lastValidBlockHeight,blockhash:k.recentBlockhash},R);if(C.err)throw new Error(`Transaction ${B} failed (${JSON.stringify(C)})`);for(;;){try{if(await t.getSlot({commitment:R})>I.slot)break}catch{}await new Promise(K=>setTimeout(K,Math.round(Uc/2)))}}return!0}}Fr.chunkSize=cd;const ud=new Y("BPFLoader2111111111111111111111111111111111");class fd{static getMinNumSignatures(t){return Fr.getMinNumSignatures(t)}static load(t,e,r,s,i){return Fr.load(t,e,r,i,s)}}function ld(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}var as,Yo;function hd(){if(Yo)return as;Yo=1;var n=Object.prototype.toString,t=Object.keys||function(r){var s=[];for(var i in r)s.push(i);return s};function e(r,s){var i,o,a,h,y,m,E;if(r===!0)return"true";if(r===!1)return"false";switch(typeof r){case"object":if(r===null)return null;if(r.toJSON&&typeof r.toJSON=="function")return e(r.toJSON(),s);if(E=n.call(r),E==="[object Array]"){for(a="[",o=r.length-1,i=0;i<o;i++)a+=e(r[i],!0)+",";return o>-1&&(a+=e(r[i],!0)),a+"]"}else if(E==="[object Object]"){for(h=t(r).sort(),o=h.length,a="",i=0;i<o;)y=h[i],m=e(r[y],!1),m!==void 0&&(a&&(a+=","),a+=JSON.stringify(y)+":"+m),i++;return"{"+a+"}"}else return JSON.stringify(r);case"function":case"undefined":return s?null:void 0;case"string":return JSON.stringify(r);default:return isFinite(r)?r:null}}return as=function(r){var s=e(r,!1);if(s!==void 0)return""+s},as}var dd=hd(),Jo=ld(dd);const mn=32;function cs(n){let t=0;for(;n>1;)n/=2,t++;return t}function pd(n){return n===0?1:(n--,n|=n>>1,n|=n>>2,n|=n>>4,n|=n>>8,n|=n>>16,n|=n>>32,n+1)}class Fc{constructor(t,e,r,s,i){this.slotsPerEpoch=void 0,this.leaderScheduleSlotOffset=void 0,this.warmup=void 0,this.firstNormalEpoch=void 0,this.firstNormalSlot=void 0,this.slotsPerEpoch=t,this.leaderScheduleSlotOffset=e,this.warmup=r,this.firstNormalEpoch=s,this.firstNormalSlot=i}getEpoch(t){return this.getEpochAndSlotIndex(t)[0]}getEpochAndSlotIndex(t){if(t<this.firstNormalSlot){const e=cs(pd(t+mn+1))-cs(mn)-1,r=this.getSlotsInEpoch(e),s=t-(r-mn);return[e,s]}else{const e=t-this.firstNormalSlot,r=Math.floor(e/this.slotsPerEpoch),s=this.firstNormalEpoch+r,i=e%this.slotsPerEpoch;return[s,i]}}getFirstSlotInEpoch(t){return t<=this.firstNormalEpoch?(Math.pow(2,t)-1)*mn:(t-this.firstNormalEpoch)*this.slotsPerEpoch+this.firstNormalSlot}getLastSlotInEpoch(t){return this.getFirstSlotInEpoch(t)+this.getSlotsInEpoch(t)-1}getSlotsInEpoch(t){return t<this.firstNormalEpoch?Math.pow(2,t+cs(mn)):this.slotsPerEpoch}}var gd=globalThis.fetch;class yd extends mh{constructor(t,e,r){const s=i=>{const o=gh(i,{autoconnect:!0,max_reconnects:5,reconnect:!0,reconnect_interval:1e3,...e});return"socket"in o?this.underlyingSocket=o.socket:this.underlyingSocket=o,o};super(s,t,e,r),this.underlyingSocket=void 0}call(...t){var r;const e=(r=this.underlyingSocket)==null?void 0:r.readyState;return e===1?super.call(...t):Promise.reject(new Error("Tried to call a JSON-RPC method `"+t[0]+"` but the socket was not `CONNECTING` or `OPEN` (`readyState` was "+e+")"))}notify(...t){var r;const e=(r=this.underlyingSocket)==null?void 0:r.readyState;return e===1?super.notify(...t):Promise.reject(new Error("Tried to send a JSON-RPC notification `"+t[0]+"` but the socket was not `CONNECTING` or `OPEN` (`readyState` was "+e+")"))}}function md(n,t){let e;try{e=n.layout.decode(t)}catch(r){throw new Error("invalid instruction; "+r)}if(e.typeIndex!==n.index)throw new Error(`invalid account data; account type mismatch ${e.typeIndex} != ${n.index}`);return e}const Xo=56;class xs{constructor(t){this.key=void 0,this.state=void 0,this.key=t.key,this.state=t.state}isActive(){const t=BigInt("0xffffffffffffffff");return this.state.deactivationSlot===t}static deserialize(t){const e=md(wd,t),r=t.length-Xo;xt(r>=0,"lookup table is invalid"),xt(r%32===0,"lookup table is invalid");const s=r/32,{addresses:i}=ot([xe(wt(),s,"addresses")]).decode(t.slice(Xo));return{deactivationSlot:e.deactivationSlot,lastExtendedSlot:e.lastExtendedSlot,lastExtendedSlotStartIndex:e.lastExtendedStartIndex,authority:e.authority.length!==0?new Y(e.authority[0]):void 0,addresses:i.map(o=>new Y(o))}}}const wd={index:1,layout:ot([st("typeIndex"),cn("deactivationSlot"),be("lastExtendedSlot"),vt("lastExtendedStartIndex"),vt(),xe(wt(),_r(vt(),-1),"authority")])},bd=/^[^:]+:\/\/([^:[]+|\[[^\]]+\])(:\d+)?(.*)/i;function vd(n){const t=n.match(bd);if(t==null)throw TypeError(`Failed to validate endpoint URL \`${n}\``);const[e,r,s,i]=t,o=n.startsWith("https:")?"wss:":"ws:",a=s==null?null:parseInt(s.slice(1),10),h=a==null?"":`:${a+1}`;return`${o}//${r}${h}${i}`}const Wt=Cn(Js(Y),Z(),n=>new Y(n)),Dc=Xs([Z(),qt("base64")]),co=Cn(Js(ct.Buffer),Dc,n=>ct.Buffer.from(n[0],"base64")),Kc=30*1e3;function kd(n){if(/^https?:/.test(n)===!1)throw new TypeError("Endpoint URL must start with `http:` or `https:`.");return n}function Mt(n){let t,e;if(typeof n=="string")t=n;else if(n){const{commitment:r,...s}=n;t=r,e=s}return{commitment:t,config:e}}function Qo(n){return n.map(t=>"memcmp"in t?{...t,memcmp:{...t.memcmp,encoding:t.memcmp.encoding??"base58"}}:t)}function qc(n){return Pe([H({jsonrpc:qt("2.0"),id:Z(),result:n}),H({jsonrpc:qt("2.0"),id:Z(),error:H({code:hn(),message:Z(),data:ut(Fl())})})])}const xd=qc(hn());function gt(n){return Cn(qc(n),xd,t=>"error"in t?t:{...t,result:X(t.result,n)})}function Gt(n){return gt(H({context:H({slot:O()}),value:n}))}function xi(n){return H({context:H({slot:O()}),value:n})}function us(n,t){return n===0?new zr({header:t.header,staticAccountKeys:t.accountKeys.map(e=>new Y(e)),recentBlockhash:t.recentBlockhash,compiledInstructions:t.instructions.map(e=>({programIdIndex:e.programIdIndex,accountKeyIndexes:e.accounts,data:ke.decode(e.data)})),addressTableLookups:t.addressTableLookups}):new Ze(t)}const Sd=H({foundation:O(),foundationTerm:O(),initial:O(),taper:O(),terminal:O()}),Ed=gt(et(rt(H({epoch:O(),effectiveSlot:O(),amount:O(),postBalance:O(),commission:ut(rt(O()))})))),_d=et(H({slot:O(),prioritizationFee:O()})),Id=H({total:O(),validator:O(),foundation:O(),epoch:O()}),Ad=H({epoch:O(),slotIndex:O(),slotsInEpoch:O(),absoluteSlot:O(),blockHeight:ut(O()),transactionCount:ut(O())}),Bd=H({slotsPerEpoch:O(),leaderScheduleSlotOffset:O(),warmup:sr(),firstNormalEpoch:O(),firstNormalSlot:O()}),Rd=yc(Z(),et(O())),$r=rt(Pe([H({}),Z()])),Md=H({err:$r}),Td=qt("receivedSignature"),Pd=H({"solana-core":Z(),"feature-set":ut(O())}),Ld=H({program:Z(),programId:Wt,parsed:hn()}),Cd=H({programId:Wt,accounts:et(Wt),data:Z()}),ta=Gt(H({err:rt(Pe([H({}),Z()])),logs:rt(et(Z())),accounts:ut(rt(et(rt(H({executable:sr(),owner:Z(),lamports:O(),data:et(Z()),rentEpoch:ut(O())}))))),unitsConsumed:ut(O()),returnData:ut(rt(H({programId:Z(),data:Xs([Z(),qt("base64")])}))),innerInstructions:ut(rt(et(H({index:O(),instructions:et(Pe([Ld,Cd]))}))))})),Od=Gt(H({byIdentity:yc(Z(),et(O())),range:H({firstSlot:O(),lastSlot:O()})}));function Ud(n,t,e,r,s,i){const o=e||gd;let a;i!=null&&console.warn("You have supplied an `httpAgent` when creating a `Connection` in a browser environment.It has been ignored; `httpAgent` is only used in Node environments.");let h;return r&&(h=async(m,E)=>{const M=await new Promise((k,R)=>{try{r(m,E,(B,I)=>k([B,I]))}catch(B){R(B)}});return await o(...M)}),new hh(async(m,E)=>{const M={method:"POST",body:m,agent:a,headers:Object.assign({"Content-Type":"application/json"},t||{},U0)};try{let k=5,R,B=500;for(;h?R=await h(n,M):R=await o(n,M),!(R.status!==429||s===!0||(k-=1,k===0));)console.error(`Server responded with ${R.status} ${R.statusText}.  Retrying after ${B}ms delay...`),await Tr(B),B*=2;const I=await R.text();R.ok?E(null,I):E(new Error(`${R.status} ${R.statusText}: ${I}`))}catch(k){k instanceof Error&&E(k)}},{})}function Nd(n){return(t,e)=>new Promise((r,s)=>{n.request(t,e,(i,o)=>{if(i){s(i);return}r(o)})})}function zd(n){return t=>new Promise((e,r)=>{t.length===0&&e([]);const s=t.map(i=>n.request(i.methodName,i.args));n.request(s,(i,o)=>{if(i){r(i);return}e(o)})})}const Fd=gt(Sd),Dd=gt(Id),Kd=gt(_d),qd=gt(Ad),Wd=gt(Bd),$d=gt(Rd),Hd=gt(O()),Vd=Gt(H({total:O(),circulating:O(),nonCirculating:O(),nonCirculatingAccounts:et(Wt)})),Ss=H({amount:Z(),uiAmount:rt(O()),decimals:O(),uiAmountString:ut(Z())}),jd=Gt(et(H({address:Wt,amount:Z(),uiAmount:rt(O()),decimals:O(),uiAmountString:ut(Z())}))),Gd=Gt(et(H({pubkey:Wt,account:H({executable:sr(),owner:Wt,lamports:O(),data:co,rentEpoch:O()})}))),Es=H({program:Z(),parsed:hn(),space:O()}),Zd=Gt(et(H({pubkey:Wt,account:H({executable:sr(),owner:Wt,lamports:O(),data:Es,rentEpoch:O()})}))),Yd=Gt(et(H({lamports:O(),address:Wt}))),Rn=H({executable:sr(),owner:Wt,lamports:O(),data:co,rentEpoch:O()}),Jd=H({pubkey:Wt,account:Rn}),Xd=Cn(Pe([Js(ct.Buffer),Es]),Pe([Dc,Es]),n=>Array.isArray(n)?X(n,co):n),_s=H({executable:sr(),owner:Wt,lamports:O(),data:Xd,rentEpoch:O()}),Qd=H({pubkey:Wt,account:_s}),t0=H({state:Pe([qt("active"),qt("inactive"),qt("activating"),qt("deactivating")]),active:O(),inactive:O()}),e0=gt(et(H({signature:Z(),slot:O(),err:$r,memo:rt(Z()),blockTime:ut(rt(O()))}))),r0=gt(et(H({signature:Z(),slot:O(),err:$r,memo:rt(Z()),blockTime:ut(rt(O()))}))),n0=H({subscription:O(),result:xi(Rn)}),i0=H({pubkey:Wt,account:Rn}),s0=H({subscription:O(),result:xi(i0)}),o0=H({parent:O(),slot:O(),root:O()}),a0=H({subscription:O(),result:o0}),c0=Pe([H({type:Pe([qt("firstShredReceived"),qt("completed"),qt("optimisticConfirmation"),qt("root")]),slot:O(),timestamp:O()}),H({type:qt("createdBank"),parent:O(),slot:O(),timestamp:O()}),H({type:qt("frozen"),slot:O(),timestamp:O(),stats:H({numTransactionEntries:O(),numSuccessfulTransactions:O(),numFailedTransactions:O(),maxTransactionsPerEntry:O()})}),H({type:qt("dead"),slot:O(),timestamp:O(),err:Z()})]),u0=H({subscription:O(),result:c0}),f0=H({subscription:O(),result:xi(Pe([Md,Td]))}),l0=H({subscription:O(),result:O()}),h0=H({pubkey:Z(),gossip:rt(Z()),tpu:rt(Z()),rpc:rt(Z()),version:rt(Z())}),ea=H({votePubkey:Z(),nodePubkey:Z(),activatedStake:O(),epochVoteAccount:sr(),epochCredits:et(Xs([O(),O(),O()])),commission:O(),lastVote:O(),rootSlot:rt(O())}),d0=gt(H({current:et(ea),delinquent:et(ea)})),p0=Pe([qt("processed"),qt("confirmed"),qt("finalized")]),g0=H({slot:O(),confirmations:rt(O()),err:$r,confirmationStatus:ut(p0)}),y0=Gt(et(rt(g0))),m0=gt(O()),Wc=H({accountKey:Wt,writableIndexes:et(O()),readonlyIndexes:et(O())}),uo=H({signatures:et(Z()),message:H({accountKeys:et(Z()),header:H({numRequiredSignatures:O(),numReadonlySignedAccounts:O(),numReadonlyUnsignedAccounts:O()}),instructions:et(H({accounts:et(O()),data:Z(),programIdIndex:O()})),recentBlockhash:Z(),addressTableLookups:ut(et(Wc))})}),$c=H({pubkey:Wt,signer:sr(),writable:sr(),source:ut(Pe([qt("transaction"),qt("lookupTable")]))}),Hc=H({accountKeys:et($c),signatures:et(Z())}),Vc=H({parsed:hn(),program:Z(),programId:Wt}),jc=H({accounts:et(Wt),data:Z(),programId:Wt}),w0=Pe([jc,Vc]),b0=Pe([H({parsed:hn(),program:Z(),programId:Z()}),H({accounts:et(Z()),data:Z(),programId:Z()})]),Gc=Cn(w0,b0,n=>"accounts"in n?X(n,jc):X(n,Vc)),Zc=H({signatures:et(Z()),message:H({accountKeys:et($c),instructions:et(Gc),recentBlockhash:Z(),addressTableLookups:ut(rt(et(Wc)))})}),oi=H({accountIndex:O(),mint:Z(),owner:ut(Z()),programId:ut(Z()),uiTokenAmount:Ss}),Yc=H({writable:et(Wt),readonly:et(Wt)}),Si=H({err:$r,fee:O(),innerInstructions:ut(rt(et(H({index:O(),instructions:et(H({accounts:et(O()),data:Z(),programIdIndex:O()}))})))),preBalances:et(O()),postBalances:et(O()),logMessages:ut(rt(et(Z()))),preTokenBalances:ut(rt(et(oi))),postTokenBalances:ut(rt(et(oi))),loadedAddresses:ut(Yc),computeUnitsConsumed:ut(O())}),fo=H({err:$r,fee:O(),innerInstructions:ut(rt(et(H({index:O(),instructions:et(Gc)})))),preBalances:et(O()),postBalances:et(O()),logMessages:ut(rt(et(Z()))),preTokenBalances:ut(rt(et(oi))),postTokenBalances:ut(rt(et(oi))),loadedAddresses:ut(Yc),computeUnitsConsumed:ut(O())}),dn=Pe([qt(0),qt("legacy")]),Hr=H({pubkey:Z(),lamports:O(),postBalance:rt(O()),rewardType:rt(Z()),commission:ut(rt(O()))}),v0=gt(rt(H({blockhash:Z(),previousBlockhash:Z(),parentSlot:O(),transactions:et(H({transaction:uo,meta:rt(Si),version:ut(dn)})),rewards:ut(et(Hr)),blockTime:rt(O()),blockHeight:rt(O())}))),k0=gt(rt(H({blockhash:Z(),previousBlockhash:Z(),parentSlot:O(),rewards:ut(et(Hr)),blockTime:rt(O()),blockHeight:rt(O())}))),x0=gt(rt(H({blockhash:Z(),previousBlockhash:Z(),parentSlot:O(),transactions:et(H({transaction:Hc,meta:rt(Si),version:ut(dn)})),rewards:ut(et(Hr)),blockTime:rt(O()),blockHeight:rt(O())}))),S0=gt(rt(H({blockhash:Z(),previousBlockhash:Z(),parentSlot:O(),transactions:et(H({transaction:Zc,meta:rt(fo),version:ut(dn)})),rewards:ut(et(Hr)),blockTime:rt(O()),blockHeight:rt(O())}))),E0=gt(rt(H({blockhash:Z(),previousBlockhash:Z(),parentSlot:O(),transactions:et(H({transaction:Hc,meta:rt(fo),version:ut(dn)})),rewards:ut(et(Hr)),blockTime:rt(O()),blockHeight:rt(O())}))),_0=gt(rt(H({blockhash:Z(),previousBlockhash:Z(),parentSlot:O(),rewards:ut(et(Hr)),blockTime:rt(O()),blockHeight:rt(O())}))),I0=gt(rt(H({blockhash:Z(),previousBlockhash:Z(),parentSlot:O(),transactions:et(H({transaction:uo,meta:rt(Si)})),rewards:ut(et(Hr)),blockTime:rt(O())}))),ra=gt(rt(H({blockhash:Z(),previousBlockhash:Z(),parentSlot:O(),signatures:et(Z()),blockTime:rt(O())}))),fs=gt(rt(H({slot:O(),meta:rt(Si),blockTime:ut(rt(O())),transaction:uo,version:ut(dn)}))),jn=gt(rt(H({slot:O(),transaction:Zc,meta:rt(fo),blockTime:ut(rt(O())),version:ut(dn)}))),A0=Gt(H({blockhash:Z(),lastValidBlockHeight:O()})),B0=Gt(sr()),R0=H({slot:O(),numTransactions:O(),numSlots:O(),samplePeriodSecs:O()}),M0=gt(et(R0)),T0=Gt(rt(H({feeCalculator:H({lamportsPerSignature:O()})}))),P0=gt(Z()),L0=gt(Z()),C0=H({err:$r,logs:et(Z()),signature:Z()}),O0=H({result:xi(C0),subscription:O()}),U0={"solana-client":"js/1.0.0-maintenance"};class N0{constructor(t,e){this._commitment=void 0,this._confirmTransactionInitialTimeout=void 0,this._rpcEndpoint=void 0,this._rpcWsEndpoint=void 0,this._rpcClient=void 0,this._rpcRequest=void 0,this._rpcBatchRequest=void 0,this._rpcWebSocket=void 0,this._rpcWebSocketConnected=!1,this._rpcWebSocketHeartbeat=null,this._rpcWebSocketIdleTimeout=null,this._rpcWebSocketGeneration=0,this._disableBlockhashCaching=!1,this._pollingBlockhash=!1,this._blockhashInfo={latestBlockhash:null,lastFetch:0,transactionSignatures:[],simulatedSignatures:[]},this._nextClientSubscriptionId=0,this._subscriptionDisposeFunctionsByClientSubscriptionId={},this._subscriptionHashByClientSubscriptionId={},this._subscriptionStateChangeCallbacksByHash={},this._subscriptionCallbacksByServerSubscriptionId={},this._subscriptionsByHash={},this._subscriptionsAutoDisposedByRpc=new Set,this.getBlockHeight=(()=>{const y={};return async m=>{const{commitment:E,config:M}=Mt(m),k=this._buildArgs([],E,void 0,M),R=Jo(k);return y[R]=y[R]??(async()=>{try{const B=await this._rpcRequest("getBlockHeight",k),I=X(B,gt(O()));if("error"in I)throw new at(I.error,"failed to get block height information");return I.result}finally{delete y[R]}})(),await y[R]}})();let r,s,i,o,a,h;e&&typeof e=="string"?this._commitment=e:e&&(this._commitment=e.commitment,this._confirmTransactionInitialTimeout=e.confirmTransactionInitialTimeout,r=e.wsEndpoint,s=e.httpHeaders,i=e.fetch,o=e.fetchMiddleware,a=e.disableRetryOnRateLimit,h=e.httpAgent),this._rpcEndpoint=kd(t),this._rpcWsEndpoint=r||vd(t),this._rpcClient=Ud(t,s,i,o,a,h),this._rpcRequest=Nd(this._rpcClient),this._rpcBatchRequest=zd(this._rpcClient),this._rpcWebSocket=new yd(this._rpcWsEndpoint,{autoconnect:!1,max_reconnects:1/0}),this._rpcWebSocket.on("open",this._wsOnOpen.bind(this)),this._rpcWebSocket.on("error",this._wsOnError.bind(this)),this._rpcWebSocket.on("close",this._wsOnClose.bind(this)),this._rpcWebSocket.on("accountNotification",this._wsOnAccountNotification.bind(this)),this._rpcWebSocket.on("programNotification",this._wsOnProgramAccountNotification.bind(this)),this._rpcWebSocket.on("slotNotification",this._wsOnSlotNotification.bind(this)),this._rpcWebSocket.on("slotsUpdatesNotification",this._wsOnSlotUpdatesNotification.bind(this)),this._rpcWebSocket.on("signatureNotification",this._wsOnSignatureNotification.bind(this)),this._rpcWebSocket.on("rootNotification",this._wsOnRootNotification.bind(this)),this._rpcWebSocket.on("logsNotification",this._wsOnLogsNotification.bind(this))}get commitment(){return this._commitment}get rpcEndpoint(){return this._rpcEndpoint}async getBalanceAndContext(t,e){const{commitment:r,config:s}=Mt(e),i=this._buildArgs([t.toBase58()],r,void 0,s),o=await this._rpcRequest("getBalance",i),a=X(o,Gt(O()));if("error"in a)throw new at(a.error,`failed to get balance for ${t.toBase58()}`);return a.result}async getBalance(t,e){return await this.getBalanceAndContext(t,e).then(r=>r.value).catch(r=>{throw new Error("failed to get balance of account "+t.toBase58()+": "+r)})}async getBlockTime(t){const e=await this._rpcRequest("getBlockTime",[t]),r=X(e,gt(rt(O())));if("error"in r)throw new at(r.error,`failed to get block time for slot ${t}`);return r.result}async getMinimumLedgerSlot(){const t=await this._rpcRequest("minimumLedgerSlot",[]),e=X(t,gt(O()));if("error"in e)throw new at(e.error,"failed to get minimum ledger slot");return e.result}async getFirstAvailableBlock(){const t=await this._rpcRequest("getFirstAvailableBlock",[]),e=X(t,Hd);if("error"in e)throw new at(e.error,"failed to get first available block");return e.result}async getSupply(t){let e={};typeof t=="string"?e={commitment:t}:t?e={...t,commitment:t&&t.commitment||this.commitment}:e={commitment:this.commitment};const r=await this._rpcRequest("getSupply",[e]),s=X(r,Vd);if("error"in s)throw new at(s.error,"failed to get supply");return s.result}async getTokenSupply(t,e){const r=this._buildArgs([t.toBase58()],e),s=await this._rpcRequest("getTokenSupply",r),i=X(s,Gt(Ss));if("error"in i)throw new at(i.error,"failed to get token supply");return i.result}async getTokenAccountBalance(t,e){const r=this._buildArgs([t.toBase58()],e),s=await this._rpcRequest("getTokenAccountBalance",r),i=X(s,Gt(Ss));if("error"in i)throw new at(i.error,"failed to get token account balance");return i.result}async getTokenAccountsByOwner(t,e,r){const{commitment:s,config:i}=Mt(r);let o=[t.toBase58()];"mint"in e?o.push({mint:e.mint.toBase58()}):o.push({programId:e.programId.toBase58()});const a=this._buildArgs(o,s,"base64",i),h=await this._rpcRequest("getTokenAccountsByOwner",a),y=X(h,Gd);if("error"in y)throw new at(y.error,`failed to get token accounts owned by account ${t.toBase58()}`);return y.result}async getParsedTokenAccountsByOwner(t,e,r){let s=[t.toBase58()];"mint"in e?s.push({mint:e.mint.toBase58()}):s.push({programId:e.programId.toBase58()});const i=this._buildArgs(s,r,"jsonParsed"),o=await this._rpcRequest("getTokenAccountsByOwner",i),a=X(o,Zd);if("error"in a)throw new at(a.error,`failed to get token accounts owned by account ${t.toBase58()}`);return a.result}async getLargestAccounts(t){const e={...t,commitment:t&&t.commitment||this.commitment},r=e.filter||e.commitment?[e]:[],s=await this._rpcRequest("getLargestAccounts",r),i=X(s,Yd);if("error"in i)throw new at(i.error,"failed to get largest accounts");return i.result}async getTokenLargestAccounts(t,e){const r=this._buildArgs([t.toBase58()],e),s=await this._rpcRequest("getTokenLargestAccounts",r),i=X(s,jd);if("error"in i)throw new at(i.error,"failed to get token largest accounts");return i.result}async getAccountInfoAndContext(t,e){const{commitment:r,config:s}=Mt(e),i=this._buildArgs([t.toBase58()],r,"base64",s),o=await this._rpcRequest("getAccountInfo",i),a=X(o,Gt(rt(Rn)));if("error"in a)throw new at(a.error,`failed to get info about account ${t.toBase58()}`);return a.result}async getParsedAccountInfo(t,e){const{commitment:r,config:s}=Mt(e),i=this._buildArgs([t.toBase58()],r,"jsonParsed",s),o=await this._rpcRequest("getAccountInfo",i),a=X(o,Gt(rt(_s)));if("error"in a)throw new at(a.error,`failed to get info about account ${t.toBase58()}`);return a.result}async getAccountInfo(t,e){try{return(await this.getAccountInfoAndContext(t,e)).value}catch(r){throw new Error("failed to get info about account "+t.toBase58()+": "+r)}}async getMultipleParsedAccounts(t,e){const{commitment:r,config:s}=Mt(e),i=t.map(y=>y.toBase58()),o=this._buildArgs([i],r,"jsonParsed",s),a=await this._rpcRequest("getMultipleAccounts",o),h=X(a,Gt(et(rt(_s))));if("error"in h)throw new at(h.error,`failed to get info for accounts ${i}`);return h.result}async getMultipleAccountsInfoAndContext(t,e){const{commitment:r,config:s}=Mt(e),i=t.map(y=>y.toBase58()),o=this._buildArgs([i],r,"base64",s),a=await this._rpcRequest("getMultipleAccounts",o),h=X(a,Gt(et(rt(Rn))));if("error"in h)throw new at(h.error,`failed to get info for accounts ${i}`);return h.result}async getMultipleAccountsInfo(t,e){return(await this.getMultipleAccountsInfoAndContext(t,e)).value}async getStakeActivation(t,e,r){const{commitment:s,config:i}=Mt(e),o=this._buildArgs([t.toBase58()],s,void 0,{...i,epoch:r??(i==null?void 0:i.epoch)}),a=await this._rpcRequest("getStakeActivation",o),h=X(a,gt(t0));if("error"in h)throw new at(h.error,`failed to get Stake Activation ${t.toBase58()}`);return h.result}async getProgramAccounts(t,e){const{commitment:r,config:s}=Mt(e),{encoding:i,...o}=s||{},a=this._buildArgs([t.toBase58()],r,i||"base64",{...o,...o.filters?{filters:Qo(o.filters)}:null}),h=await this._rpcRequest("getProgramAccounts",a),y=et(Jd),m=o.withContext===!0?X(h,Gt(y)):X(h,gt(y));if("error"in m)throw new at(m.error,`failed to get accounts owned by program ${t.toBase58()}`);return m.result}async getParsedProgramAccounts(t,e){const{commitment:r,config:s}=Mt(e),i=this._buildArgs([t.toBase58()],r,"jsonParsed",s),o=await this._rpcRequest("getProgramAccounts",i),a=X(o,gt(et(Qd)));if("error"in a)throw new at(a.error,`failed to get accounts owned by program ${t.toBase58()}`);return a.result}async confirmTransaction(t,e){var i;let r;if(typeof t=="string")r=t;else{const o=t;if((i=o.abortSignal)!=null&&i.aborted)return Promise.reject(o.abortSignal.reason);r=o.signature}let s;try{s=ke.decode(r)}catch{throw new Error("signature must be base58 encoded: "+r)}return xt(s.length===64,"signature has invalid length"),typeof t=="string"?await this.confirmTransactionUsingLegacyTimeoutStrategy({commitment:e||this.commitment,signature:r}):"lastValidBlockHeight"in t?await this.confirmTransactionUsingBlockHeightExceedanceStrategy({commitment:e||this.commitment,strategy:t}):await this.confirmTransactionUsingDurableNonceStrategy({commitment:e||this.commitment,strategy:t})}getCancellationPromise(t){return new Promise((e,r)=>{t!=null&&(t.aborted?r(t.reason):t.addEventListener("abort",()=>{r(t.reason)}))})}getTransactionConfirmationPromise({commitment:t,signature:e}){let r,s,i=!1;const o=new Promise((h,y)=>{try{r=this.onSignature(e,(E,M)=>{r=void 0;const k={context:M,value:E};h({__type:hr.PROCESSED,response:k})},t);const m=new Promise(E=>{r==null?E():s=this._onSubscriptionStateChange(r,M=>{M==="subscribed"&&E()})});(async()=>{if(await m,i)return;const E=await this.getSignatureStatus(e);if(i||E==null)return;const{context:M,value:k}=E;if(k!=null)if(k!=null&&k.err)y(k.err);else{switch(t){case"confirmed":case"single":case"singleGossip":{if(k.confirmationStatus==="processed")return;break}case"finalized":case"max":case"root":{if(k.confirmationStatus==="processed"||k.confirmationStatus==="confirmed")return;break}case"processed":case"recent":}i=!0,h({__type:hr.PROCESSED,response:{context:M,value:k}})}})()}catch(m){y(m)}});return{abortConfirmation:()=>{s&&(s(),s=void 0),r!=null&&(this.removeSignatureListener(r),r=void 0)},confirmationPromise:o}}async confirmTransactionUsingBlockHeightExceedanceStrategy({commitment:t,strategy:{abortSignal:e,lastValidBlockHeight:r,signature:s}}){let i=!1;const o=new Promise(E=>{const M=async()=>{try{return await this.getBlockHeight(t)}catch{return-1}};(async()=>{let k=await M();if(!i){for(;k<=r;)if(await Tr(1e3),i||(k=await M(),i))return;E({__type:hr.BLOCKHEIGHT_EXCEEDED})}})()}),{abortConfirmation:a,confirmationPromise:h}=this.getTransactionConfirmationPromise({commitment:t,signature:s}),y=this.getCancellationPromise(e);let m;try{const E=await Promise.race([y,h,o]);if(E.__type===hr.PROCESSED)m=E.response;else throw new no(s)}finally{i=!0,a()}return m}async confirmTransactionUsingDurableNonceStrategy({commitment:t,strategy:{abortSignal:e,minContextSlot:r,nonceAccountPubkey:s,nonceValue:i,signature:o}}){let a=!1;const h=new Promise(k=>{let R=i,B=null;const I=async()=>{try{const{context:C,value:K}=await this.getNonceAndContext(s,{commitment:t,minContextSlot:r});return B=C.slot,K==null?void 0:K.nonce}catch{return R}};(async()=>{if(R=await I(),!a)for(;;){if(i!==R){k({__type:hr.NONCE_INVALID,slotInWhichNonceDidAdvance:B});return}if(await Tr(2e3),a||(R=await I(),a))return}})()}),{abortConfirmation:y,confirmationPromise:m}=this.getTransactionConfirmationPromise({commitment:t,signature:o}),E=this.getCancellationPromise(e);let M;try{const k=await Promise.race([E,m,h]);if(k.__type===hr.PROCESSED)M=k.response;else{let R;for(;;){const B=await this.getSignatureStatus(o);if(B==null)break;if(B.context.slot<(k.slotInWhichNonceDidAdvance??r)){await Tr(400);continue}R=B;break}if(R!=null&&R.value){const B=t||"finalized",{confirmationStatus:I}=R.value;switch(B){case"processed":case"recent":if(I!=="processed"&&I!=="confirmed"&&I!=="finalized")throw new Qr(o);break;case"confirmed":case"single":case"singleGossip":if(I!=="confirmed"&&I!=="finalized")throw new Qr(o);break;case"finalized":case"max":case"root":if(I!=="finalized")throw new Qr(o);break;default:}M={context:R.context,value:{err:R.value.err}}}else throw new Qr(o)}}finally{a=!0,y()}return M}async confirmTransactionUsingLegacyTimeoutStrategy({commitment:t,signature:e}){let r;const s=new Promise(h=>{let y=this._confirmTransactionInitialTimeout||6e4;switch(t){case"processed":case"recent":case"single":case"confirmed":case"singleGossip":{y=this._confirmTransactionInitialTimeout||3e4;break}}r=setTimeout(()=>h({__type:hr.TIMED_OUT,timeoutMs:y}),y)}),{abortConfirmation:i,confirmationPromise:o}=this.getTransactionConfirmationPromise({commitment:t,signature:e});let a;try{const h=await Promise.race([o,s]);if(h.__type===hr.PROCESSED)a=h.response;else throw new io(e,h.timeoutMs/1e3)}finally{clearTimeout(r),i()}return a}async getClusterNodes(){const t=await this._rpcRequest("getClusterNodes",[]),e=X(t,gt(et(h0)));if("error"in e)throw new at(e.error,"failed to get cluster nodes");return e.result}async getVoteAccounts(t){const e=this._buildArgs([],t),r=await this._rpcRequest("getVoteAccounts",e),s=X(r,d0);if("error"in s)throw new at(s.error,"failed to get vote accounts");return s.result}async getSlot(t){const{commitment:e,config:r}=Mt(t),s=this._buildArgs([],e,void 0,r),i=await this._rpcRequest("getSlot",s),o=X(i,gt(O()));if("error"in o)throw new at(o.error,"failed to get slot");return o.result}async getSlotLeader(t){const{commitment:e,config:r}=Mt(t),s=this._buildArgs([],e,void 0,r),i=await this._rpcRequest("getSlotLeader",s),o=X(i,gt(Z()));if("error"in o)throw new at(o.error,"failed to get slot leader");return o.result}async getSlotLeaders(t,e){const r=[t,e],s=await this._rpcRequest("getSlotLeaders",r),i=X(s,gt(et(Wt)));if("error"in i)throw new at(i.error,"failed to get slot leaders");return i.result}async getSignatureStatus(t,e){const{context:r,value:s}=await this.getSignatureStatuses([t],e);xt(s.length===1);const i=s[0];return{context:r,value:i}}async getSignatureStatuses(t,e){const r=[t];e&&r.push(e);const s=await this._rpcRequest("getSignatureStatuses",r),i=X(s,y0);if("error"in i)throw new at(i.error,"failed to get signature status");return i.result}async getTransactionCount(t){const{commitment:e,config:r}=Mt(t),s=this._buildArgs([],e,void 0,r),i=await this._rpcRequest("getTransactionCount",s),o=X(i,gt(O()));if("error"in o)throw new at(o.error,"failed to get transaction count");return o.result}async getTotalSupply(t){return(await this.getSupply({commitment:t,excludeNonCirculatingAccountsList:!0})).value.total}async getInflationGovernor(t){const e=this._buildArgs([],t),r=await this._rpcRequest("getInflationGovernor",e),s=X(r,Fd);if("error"in s)throw new at(s.error,"failed to get inflation");return s.result}async getInflationReward(t,e,r){const{commitment:s,config:i}=Mt(r),o=this._buildArgs([t.map(y=>y.toBase58())],s,void 0,{...i,epoch:e??(i==null?void 0:i.epoch)}),a=await this._rpcRequest("getInflationReward",o),h=X(a,Ed);if("error"in h)throw new at(h.error,"failed to get inflation reward");return h.result}async getInflationRate(){const t=await this._rpcRequest("getInflationRate",[]),e=X(t,Dd);if("error"in e)throw new at(e.error,"failed to get inflation rate");return e.result}async getEpochInfo(t){const{commitment:e,config:r}=Mt(t),s=this._buildArgs([],e,void 0,r),i=await this._rpcRequest("getEpochInfo",s),o=X(i,qd);if("error"in o)throw new at(o.error,"failed to get epoch info");return o.result}async getEpochSchedule(){const t=await this._rpcRequest("getEpochSchedule",[]),e=X(t,Wd);if("error"in e)throw new at(e.error,"failed to get epoch schedule");const r=e.result;return new Fc(r.slotsPerEpoch,r.leaderScheduleSlotOffset,r.warmup,r.firstNormalEpoch,r.firstNormalSlot)}async getLeaderSchedule(){const t=await this._rpcRequest("getLeaderSchedule",[]),e=X(t,$d);if("error"in e)throw new at(e.error,"failed to get leader schedule");return e.result}async getMinimumBalanceForRentExemption(t,e){const r=this._buildArgs([t],e),s=await this._rpcRequest("getMinimumBalanceForRentExemption",r),i=X(s,m0);return"error"in i?(console.warn("Unable to fetch minimum balance for rent exemption"),0):i.result}async getRecentBlockhashAndContext(t){const{context:e,value:{blockhash:r}}=await this.getLatestBlockhashAndContext(t);return{context:e,value:{blockhash:r,feeCalculator:{get lamportsPerSignature(){throw new Error("The capability to fetch `lamportsPerSignature` using the `getRecentBlockhash` API is no longer offered by the network. Use the `getFeeForMessage` API to obtain the fee for a given message.")},toJSON(){return{}}}}}}async getRecentPerformanceSamples(t){const e=await this._rpcRequest("getRecentPerformanceSamples",t?[t]:[]),r=X(e,M0);if("error"in r)throw new at(r.error,"failed to get recent performance samples");return r.result}async getFeeCalculatorForBlockhash(t,e){const r=this._buildArgs([t],e),s=await this._rpcRequest("getFeeCalculatorForBlockhash",r),i=X(s,T0);if("error"in i)throw new at(i.error,"failed to get fee calculator");const{context:o,value:a}=i.result;return{context:o,value:a!==null?a.feeCalculator:null}}async getFeeForMessage(t,e){const r=yt(t.serialize()).toString("base64"),s=this._buildArgs([r],e),i=await this._rpcRequest("getFeeForMessage",s),o=X(i,Gt(rt(O())));if("error"in o)throw new at(o.error,"failed to get fee for message");if(o.result===null)throw new Error("invalid blockhash");return o.result}async getRecentPrioritizationFees(t){var o;const e=(o=t==null?void 0:t.lockedWritableAccounts)==null?void 0:o.map(a=>a.toBase58()),r=e!=null&&e.length?[e]:[],s=await this._rpcRequest("getRecentPrioritizationFees",r),i=X(s,Kd);if("error"in i)throw new at(i.error,"failed to get recent prioritization fees");return i.result}async getRecentBlockhash(t){try{return(await this.getRecentBlockhashAndContext(t)).value}catch(e){throw new Error("failed to get recent blockhash: "+e)}}async getLatestBlockhash(t){try{return(await this.getLatestBlockhashAndContext(t)).value}catch(e){throw new Error("failed to get recent blockhash: "+e)}}async getLatestBlockhashAndContext(t){const{commitment:e,config:r}=Mt(t),s=this._buildArgs([],e,void 0,r),i=await this._rpcRequest("getLatestBlockhash",s),o=X(i,A0);if("error"in o)throw new at(o.error,"failed to get latest blockhash");return o.result}async isBlockhashValid(t,e){const{commitment:r,config:s}=Mt(e),i=this._buildArgs([t],r,void 0,s),o=await this._rpcRequest("isBlockhashValid",i),a=X(o,B0);if("error"in a)throw new at(a.error,"failed to determine if the blockhash `"+t+"`is valid");return a.result}async getVersion(){const t=await this._rpcRequest("getVersion",[]),e=X(t,gt(Pd));if("error"in e)throw new at(e.error,"failed to get version");return e.result}async getGenesisHash(){const t=await this._rpcRequest("getGenesisHash",[]),e=X(t,gt(Z()));if("error"in e)throw new at(e.error,"failed to get genesis hash");return e.result}async getBlock(t,e){const{commitment:r,config:s}=Mt(e),i=this._buildArgsAtLeastConfirmed([t],r,void 0,s),o=await this._rpcRequest("getBlock",i);try{switch(s==null?void 0:s.transactionDetails){case"accounts":{const a=X(o,x0);if("error"in a)throw a.error;return a.result}case"none":{const a=X(o,k0);if("error"in a)throw a.error;return a.result}default:{const a=X(o,v0);if("error"in a)throw a.error;const{result:h}=a;return h?{...h,transactions:h.transactions.map(({transaction:y,meta:m,version:E})=>({meta:m,transaction:{...y,message:us(E,y.message)},version:E}))}:null}}}catch(a){throw new at(a,"failed to get confirmed block")}}async getParsedBlock(t,e){const{commitment:r,config:s}=Mt(e),i=this._buildArgsAtLeastConfirmed([t],r,"jsonParsed",s),o=await this._rpcRequest("getBlock",i);try{switch(s==null?void 0:s.transactionDetails){case"accounts":{const a=X(o,E0);if("error"in a)throw a.error;return a.result}case"none":{const a=X(o,_0);if("error"in a)throw a.error;return a.result}default:{const a=X(o,S0);if("error"in a)throw a.error;return a.result}}}catch(a){throw new at(a,"failed to get block")}}async getBlockProduction(t){let e,r;if(typeof t=="string")r=t;else if(t){const{commitment:a,...h}=t;r=a,e=h}const s=this._buildArgs([],r,"base64",e),i=await this._rpcRequest("getBlockProduction",s),o=X(i,Od);if("error"in o)throw new at(o.error,"failed to get block production information");return o.result}async getTransaction(t,e){const{commitment:r,config:s}=Mt(e),i=this._buildArgsAtLeastConfirmed([t],r,void 0,s),o=await this._rpcRequest("getTransaction",i),a=X(o,fs);if("error"in a)throw new at(a.error,"failed to get transaction");const h=a.result;return h&&{...h,transaction:{...h.transaction,message:us(h.version,h.transaction.message)}}}async getParsedTransaction(t,e){const{commitment:r,config:s}=Mt(e),i=this._buildArgsAtLeastConfirmed([t],r,"jsonParsed",s),o=await this._rpcRequest("getTransaction",i),a=X(o,jn);if("error"in a)throw new at(a.error,"failed to get transaction");return a.result}async getParsedTransactions(t,e){const{commitment:r,config:s}=Mt(e),i=t.map(h=>({methodName:"getTransaction",args:this._buildArgsAtLeastConfirmed([h],r,"jsonParsed",s)}));return(await this._rpcBatchRequest(i)).map(h=>{const y=X(h,jn);if("error"in y)throw new at(y.error,"failed to get transactions");return y.result})}async getTransactions(t,e){const{commitment:r,config:s}=Mt(e),i=t.map(h=>({methodName:"getTransaction",args:this._buildArgsAtLeastConfirmed([h],r,void 0,s)}));return(await this._rpcBatchRequest(i)).map(h=>{const y=X(h,fs);if("error"in y)throw new at(y.error,"failed to get transactions");const m=y.result;return m&&{...m,transaction:{...m.transaction,message:us(m.version,m.transaction.message)}}})}async getConfirmedBlock(t,e){const r=this._buildArgsAtLeastConfirmed([t],e),s=await this._rpcRequest("getBlock",r),i=X(s,I0);if("error"in i)throw new at(i.error,"failed to get confirmed block");const o=i.result;if(!o)throw new Error("Confirmed block "+t+" not found");const a={...o,transactions:o.transactions.map(({transaction:h,meta:y})=>{const m=new Ze(h.message);return{meta:y,transaction:{...h,message:m}}})};return{...a,transactions:a.transactions.map(({transaction:h,meta:y})=>({meta:y,transaction:Et.populate(h.message,h.signatures)}))}}async getBlocks(t,e,r){const s=this._buildArgsAtLeastConfirmed(e!==void 0?[t,e]:[t],r),i=await this._rpcRequest("getBlocks",s),o=X(i,gt(et(O())));if("error"in o)throw new at(o.error,"failed to get blocks");return o.result}async getBlockSignatures(t,e){const r=this._buildArgsAtLeastConfirmed([t],e,void 0,{transactionDetails:"signatures",rewards:!1}),s=await this._rpcRequest("getBlock",r),i=X(s,ra);if("error"in i)throw new at(i.error,"failed to get block");const o=i.result;if(!o)throw new Error("Block "+t+" not found");return o}async getConfirmedBlockSignatures(t,e){const r=this._buildArgsAtLeastConfirmed([t],e,void 0,{transactionDetails:"signatures",rewards:!1}),s=await this._rpcRequest("getBlock",r),i=X(s,ra);if("error"in i)throw new at(i.error,"failed to get confirmed block");const o=i.result;if(!o)throw new Error("Confirmed block "+t+" not found");return o}async getConfirmedTransaction(t,e){const r=this._buildArgsAtLeastConfirmed([t],e),s=await this._rpcRequest("getTransaction",r),i=X(s,fs);if("error"in i)throw new at(i.error,"failed to get transaction");const o=i.result;if(!o)return o;const a=new Ze(o.transaction.message),h=o.transaction.signatures;return{...o,transaction:Et.populate(a,h)}}async getParsedConfirmedTransaction(t,e){const r=this._buildArgsAtLeastConfirmed([t],e,"jsonParsed"),s=await this._rpcRequest("getTransaction",r),i=X(s,jn);if("error"in i)throw new at(i.error,"failed to get confirmed transaction");return i.result}async getParsedConfirmedTransactions(t,e){const r=t.map(o=>({methodName:"getTransaction",args:this._buildArgsAtLeastConfirmed([o],e,"jsonParsed")}));return(await this._rpcBatchRequest(r)).map(o=>{const a=X(o,jn);if("error"in a)throw new at(a.error,"failed to get confirmed transactions");return a.result})}async getConfirmedSignaturesForAddress(t,e,r){let s={},i=await this.getFirstAvailableBlock();for(;!("until"in s)&&(e--,!(e<=0||e<i));)try{const h=await this.getConfirmedBlockSignatures(e,"finalized");h.signatures.length>0&&(s.until=h.signatures[h.signatures.length-1].toString())}catch(h){if(h instanceof Error&&h.message.includes("skipped"))continue;throw h}let o=await this.getSlot("finalized");for(;!("before"in s)&&(r++,!(r>o));)try{const h=await this.getConfirmedBlockSignatures(r);h.signatures.length>0&&(s.before=h.signatures[h.signatures.length-1].toString())}catch(h){if(h instanceof Error&&h.message.includes("skipped"))continue;throw h}return(await this.getConfirmedSignaturesForAddress2(t,s)).map(h=>h.signature)}async getConfirmedSignaturesForAddress2(t,e,r){const s=this._buildArgsAtLeastConfirmed([t.toBase58()],r,void 0,e),i=await this._rpcRequest("getConfirmedSignaturesForAddress2",s),o=X(i,e0);if("error"in o)throw new at(o.error,"failed to get confirmed signatures for address");return o.result}async getSignaturesForAddress(t,e,r){const s=this._buildArgsAtLeastConfirmed([t.toBase58()],r,void 0,e),i=await this._rpcRequest("getSignaturesForAddress",s),o=X(i,r0);if("error"in o)throw new at(o.error,"failed to get signatures for address");return o.result}async getAddressLookupTable(t,e){const{context:r,value:s}=await this.getAccountInfoAndContext(t,e);let i=null;return s!==null&&(i=new xs({key:t,state:xs.deserialize(s.data)})),{context:r,value:i}}async getNonceAndContext(t,e){const{context:r,value:s}=await this.getAccountInfoAndContext(t,e);let i=null;return s!==null&&(i=ki.fromAccountData(s.data)),{context:r,value:i}}async getNonce(t,e){return await this.getNonceAndContext(t,e).then(r=>r.value).catch(r=>{throw new Error("failed to get nonce for account "+t.toBase58()+": "+r)})}async requestAirdrop(t,e){const r=await this._rpcRequest("requestAirdrop",[t.toBase58(),e]),s=X(r,P0);if("error"in s)throw new at(s.error,`airdrop to ${t.toBase58()} failed`);return s.result}async _blockhashWithExpiryBlockHeight(t){if(!t){for(;this._pollingBlockhash;)await Tr(100);const r=Date.now()-this._blockhashInfo.lastFetch>=Kc;if(this._blockhashInfo.latestBlockhash!==null&&!r)return this._blockhashInfo.latestBlockhash}return await this._pollNewBlockhash()}async _pollNewBlockhash(){this._pollingBlockhash=!0;try{const t=Date.now(),e=this._blockhashInfo.latestBlockhash,r=e?e.blockhash:null;for(let s=0;s<50;s++){const i=await this.getLatestBlockhash("finalized");if(r!==i.blockhash)return this._blockhashInfo={latestBlockhash:i,lastFetch:Date.now(),transactionSignatures:[],simulatedSignatures:[]},i;await Tr(Uc/2)}throw new Error(`Unable to obtain a new blockhash after ${Date.now()-t}ms`)}finally{this._pollingBlockhash=!1}}async getStakeMinimumDelegation(t){const{commitment:e,config:r}=Mt(t),s=this._buildArgs([],e,"base64",r),i=await this._rpcRequest("getStakeMinimumDelegation",s),o=X(i,Gt(O()));if("error"in o)throw new at(o.error,"failed to get stake minimum delegation");return o.result}async simulateTransaction(t,e,r){if("message"in t){const B=t.serialize(),I=ct.Buffer.from(B).toString("base64");if(Array.isArray(e)||r!==void 0)throw new Error("Invalid arguments");const C=e||{};C.encoding="base64","commitment"in C||(C.commitment=this.commitment),e&&typeof e=="object"&&"innerInstructions"in e&&(C.innerInstructions=e.innerInstructions);const K=[I,C],q=await this._rpcRequest("simulateTransaction",K),V=X(q,ta);if("error"in V)throw new Error("failed to simulate transaction: "+V.error.message);return V.result}let s;if(t instanceof Et){let R=t;s=new Et,s.feePayer=R.feePayer,s.instructions=t.instructions,s.nonceInfo=R.nonceInfo,s.signatures=R.signatures}else s=Et.populate(t),s._message=s._json=void 0;if(e!==void 0&&!Array.isArray(e))throw new Error("Invalid arguments");const i=e;if(s.nonceInfo&&i)s.sign(...i);else{let R=this._disableBlockhashCaching;for(;;){const B=await this._blockhashWithExpiryBlockHeight(R);if(s.lastValidBlockHeight=B.lastValidBlockHeight,s.recentBlockhash=B.blockhash,!i)break;if(s.sign(...i),!s.signature)throw new Error("!signature");const I=s.signature.toString("base64");if(!this._blockhashInfo.simulatedSignatures.includes(I)&&!this._blockhashInfo.transactionSignatures.includes(I)){this._blockhashInfo.simulatedSignatures.push(I);break}else R=!0}}const o=s._compile(),a=o.serialize(),y=s._serialize(a).toString("base64"),m={encoding:"base64",commitment:this.commitment};if(r){const R=(Array.isArray(r)?r:o.nonProgramIds()).map(B=>B.toBase58());m.accounts={encoding:"base64",addresses:R}}i&&(m.sigVerify=!0),e&&typeof e=="object"&&"innerInstructions"in e&&(m.innerInstructions=e.innerInstructions);const E=[y,m],M=await this._rpcRequest("simulateTransaction",E),k=X(M,ta);if("error"in k){let R;if("data"in k.error&&(R=k.error.data.logs,R&&Array.isArray(R))){const B=`
    `,I=B+R.join(B);console.error(k.error.message,I)}throw new Bn({action:"simulate",signature:"",transactionMessage:k.error.message,logs:R})}return k.result}async sendTransaction(t,e,r){if("version"in t){if(e&&Array.isArray(e))throw new Error("Invalid arguments");const o=t.serialize();return await this.sendRawTransaction(o,e)}if(e===void 0||!Array.isArray(e))throw new Error("Invalid arguments");const s=e;if(t.nonceInfo)t.sign(...s);else{let o=this._disableBlockhashCaching;for(;;){const a=await this._blockhashWithExpiryBlockHeight(o);if(t.lastValidBlockHeight=a.lastValidBlockHeight,t.recentBlockhash=a.blockhash,t.sign(...s),!t.signature)throw new Error("!signature");const h=t.signature.toString("base64");if(this._blockhashInfo.transactionSignatures.includes(h))o=!0;else{this._blockhashInfo.transactionSignatures.push(h);break}}}const i=t.serialize();return await this.sendRawTransaction(i,r)}async sendRawTransaction(t,e){const r=yt(t).toString("base64");return await this.sendEncodedTransaction(r,e)}async sendEncodedTransaction(t,e){const r={encoding:"base64"},s=e&&e.skipPreflight,i=s===!0?"processed":e&&e.preflightCommitment||this.commitment;e&&e.maxRetries!=null&&(r.maxRetries=e.maxRetries),e&&e.minContextSlot!=null&&(r.minContextSlot=e.minContextSlot),s&&(r.skipPreflight=s),i&&(r.preflightCommitment=i);const o=[t,r],a=await this._rpcRequest("sendTransaction",o),h=X(a,L0);if("error"in h){let y;throw"data"in h.error&&(y=h.error.data.logs),new Bn({action:s?"send":"simulate",signature:"",transactionMessage:h.error.message,logs:y})}return h.result}_wsOnOpen(){this._rpcWebSocketConnected=!0,this._rpcWebSocketHeartbeat=setInterval(()=>{(async()=>{try{await this._rpcWebSocket.notify("ping")}catch{}})()},5e3),this._updateSubscriptions()}_wsOnError(t){this._rpcWebSocketConnected=!1,console.error("ws error:",t.message)}_wsOnClose(t){if(this._rpcWebSocketConnected=!1,this._rpcWebSocketGeneration=(this._rpcWebSocketGeneration+1)%Number.MAX_SAFE_INTEGER,this._rpcWebSocketIdleTimeout&&(clearTimeout(this._rpcWebSocketIdleTimeout),this._rpcWebSocketIdleTimeout=null),this._rpcWebSocketHeartbeat&&(clearInterval(this._rpcWebSocketHeartbeat),this._rpcWebSocketHeartbeat=null),t===1e3){this._updateSubscriptions();return}this._subscriptionCallbacksByServerSubscriptionId={},Object.entries(this._subscriptionsByHash).forEach(([e,r])=>{this._setSubscription(e,{...r,state:"pending"})})}_setSubscription(t,e){var s;const r=(s=this._subscriptionsByHash[t])==null?void 0:s.state;if(this._subscriptionsByHash[t]=e,r!==e.state){const i=this._subscriptionStateChangeCallbacksByHash[t];i&&i.forEach(o=>{try{o(e.state)}catch{}})}}_onSubscriptionStateChange(t,e){var i;const r=this._subscriptionHashByClientSubscriptionId[t];if(r==null)return()=>{};const s=(i=this._subscriptionStateChangeCallbacksByHash)[r]||(i[r]=new Set);return s.add(e),()=>{s.delete(e),s.size===0&&delete this._subscriptionStateChangeCallbacksByHash[r]}}async _updateSubscriptions(){if(Object.keys(this._subscriptionsByHash).length===0){this._rpcWebSocketConnected&&(this._rpcWebSocketConnected=!1,this._rpcWebSocketIdleTimeout=setTimeout(()=>{this._rpcWebSocketIdleTimeout=null;try{this._rpcWebSocket.close()}catch(r){r instanceof Error&&console.log(`Error when closing socket connection: ${r.message}`)}},500));return}if(this._rpcWebSocketIdleTimeout!==null&&(clearTimeout(this._rpcWebSocketIdleTimeout),this._rpcWebSocketIdleTimeout=null,this._rpcWebSocketConnected=!0),!this._rpcWebSocketConnected){this._rpcWebSocket.connect();return}const t=this._rpcWebSocketGeneration,e=()=>t===this._rpcWebSocketGeneration;await Promise.all(Object.keys(this._subscriptionsByHash).map(async r=>{const s=this._subscriptionsByHash[r];if(s!==void 0)switch(s.state){case"pending":case"unsubscribed":if(s.callbacks.size===0){delete this._subscriptionsByHash[r],s.state==="unsubscribed"&&delete this._subscriptionCallbacksByServerSubscriptionId[s.serverSubscriptionId],await this._updateSubscriptions();return}await(async()=>{const{args:i,method:o}=s;try{this._setSubscription(r,{...s,state:"subscribing"});const a=await this._rpcWebSocket.call(o,i);this._setSubscription(r,{...s,serverSubscriptionId:a,state:"subscribed"}),this._subscriptionCallbacksByServerSubscriptionId[a]=s.callbacks,await this._updateSubscriptions()}catch(a){if(console.error(`Received ${a instanceof Error?"":"JSON-RPC "}error calling \`${o}\``,{args:i,error:a}),!e())return;this._setSubscription(r,{...s,state:"pending"}),await this._updateSubscriptions()}})();break;case"subscribed":s.callbacks.size===0&&await(async()=>{const{serverSubscriptionId:i,unsubscribeMethod:o}=s;if(this._subscriptionsAutoDisposedByRpc.has(i))this._subscriptionsAutoDisposedByRpc.delete(i);else{this._setSubscription(r,{...s,state:"unsubscribing"}),this._setSubscription(r,{...s,state:"unsubscribing"});try{await this._rpcWebSocket.call(o,[i])}catch(a){if(a instanceof Error&&console.error(`${o} error:`,a.message),!e())return;this._setSubscription(r,{...s,state:"subscribed"}),await this._updateSubscriptions();return}}this._setSubscription(r,{...s,state:"unsubscribed"}),await this._updateSubscriptions()})();break}}))}_handleServerNotification(t,e){const r=this._subscriptionCallbacksByServerSubscriptionId[t];r!==void 0&&r.forEach(s=>{try{s(...e)}catch(i){console.error(i)}})}_wsOnAccountNotification(t){const{result:e,subscription:r}=X(t,n0);this._handleServerNotification(r,[e.value,e.context])}_makeSubscription(t,e){const r=this._nextClientSubscriptionId++,s=Jo([t.method,e]),i=this._subscriptionsByHash[s];return i===void 0?this._subscriptionsByHash[s]={...t,args:e,callbacks:new Set([t.callback]),state:"pending"}:i.callbacks.add(t.callback),this._subscriptionHashByClientSubscriptionId[r]=s,this._subscriptionDisposeFunctionsByClientSubscriptionId[r]=async()=>{delete this._subscriptionDisposeFunctionsByClientSubscriptionId[r],delete this._subscriptionHashByClientSubscriptionId[r];const o=this._subscriptionsByHash[s];xt(o!==void 0,`Could not find a \`Subscription\` when tearing down client subscription #${r}`),o.callbacks.delete(t.callback),await this._updateSubscriptions()},this._updateSubscriptions(),r}onAccountChange(t,e,r){const{commitment:s,config:i}=Mt(r),o=this._buildArgs([t.toBase58()],s||this._commitment||"finalized","base64",i);return this._makeSubscription({callback:e,method:"accountSubscribe",unsubscribeMethod:"accountUnsubscribe"},o)}async removeAccountChangeListener(t){await this._unsubscribeClientSubscription(t,"account change")}_wsOnProgramAccountNotification(t){const{result:e,subscription:r}=X(t,s0);this._handleServerNotification(r,[{accountId:e.value.pubkey,accountInfo:e.value.account},e.context])}onProgramAccountChange(t,e,r,s){const{commitment:i,config:o}=Mt(r),a=this._buildArgs([t.toBase58()],i||this._commitment||"finalized","base64",o||(s?{filters:Qo(s)}:void 0));return this._makeSubscription({callback:e,method:"programSubscribe",unsubscribeMethod:"programUnsubscribe"},a)}async removeProgramAccountChangeListener(t){await this._unsubscribeClientSubscription(t,"program account change")}onLogs(t,e,r){const s=this._buildArgs([typeof t=="object"?{mentions:[t.toString()]}:t],r||this._commitment||"finalized");return this._makeSubscription({callback:e,method:"logsSubscribe",unsubscribeMethod:"logsUnsubscribe"},s)}async removeOnLogsListener(t){await this._unsubscribeClientSubscription(t,"logs")}_wsOnLogsNotification(t){const{result:e,subscription:r}=X(t,O0);this._handleServerNotification(r,[e.value,e.context])}_wsOnSlotNotification(t){const{result:e,subscription:r}=X(t,a0);this._handleServerNotification(r,[e])}onSlotChange(t){return this._makeSubscription({callback:t,method:"slotSubscribe",unsubscribeMethod:"slotUnsubscribe"},[])}async removeSlotChangeListener(t){await this._unsubscribeClientSubscription(t,"slot change")}_wsOnSlotUpdatesNotification(t){const{result:e,subscription:r}=X(t,u0);this._handleServerNotification(r,[e])}onSlotUpdate(t){return this._makeSubscription({callback:t,method:"slotsUpdatesSubscribe",unsubscribeMethod:"slotsUpdatesUnsubscribe"},[])}async removeSlotUpdateListener(t){await this._unsubscribeClientSubscription(t,"slot update")}async _unsubscribeClientSubscription(t,e){const r=this._subscriptionDisposeFunctionsByClientSubscriptionId[t];r?await r():console.warn(`Ignored unsubscribe request because an active subscription with id \`${t}\` for '${e}' events could not be found.`)}_buildArgs(t,e,r,s){const i=e||this._commitment;if(i||r||s){let o={};r&&(o.encoding=r),i&&(o.commitment=i),s&&(o=Object.assign(o,s)),t.push(o)}return t}_buildArgsAtLeastConfirmed(t,e,r,s){const i=e||this._commitment;if(i&&!["confirmed","finalized"].includes(i))throw new Error("Using Connection with default commitment: `"+this._commitment+"`, but method requires at least `confirmed`");return this._buildArgs(t,e,r,s)}_wsOnSignatureNotification(t){const{result:e,subscription:r}=X(t,f0);e.value!=="receivedSignature"&&this._subscriptionsAutoDisposedByRpc.add(r),this._handleServerNotification(r,e.value==="receivedSignature"?[{type:"received"},e.context]:[{type:"status",result:e.value},e.context])}onSignature(t,e,r){const s=this._buildArgs([t],r||this._commitment||"finalized"),i=this._makeSubscription({callback:(o,a)=>{if(o.type==="status"){e(o.result,a);try{this.removeSignatureListener(i)}catch{}}},method:"signatureSubscribe",unsubscribeMethod:"signatureUnsubscribe"},s);return i}onSignatureWithOptions(t,e,r){const{commitment:s,...i}={...r,commitment:r&&r.commitment||this._commitment||"finalized"},o=this._buildArgs([t],s,void 0,i),a=this._makeSubscription({callback:(h,y)=>{e(h,y);try{this.removeSignatureListener(a)}catch{}},method:"signatureSubscribe",unsubscribeMethod:"signatureUnsubscribe"},o);return a}async removeSignatureListener(t){await this._unsubscribeClientSubscription(t,"signature result")}_wsOnRootNotification(t){const{result:e,subscription:r}=X(t,l0);this._handleServerNotification(r,[e])}onRootChange(t){return this._makeSubscription({callback:t,method:"rootSubscribe",unsubscribeMethod:"rootUnsubscribe"},[])}async removeRootChangeListener(t){await this._unsubscribeClientSubscription(t,"root change")}}class en{constructor(t){this._keypair=void 0,this._keypair=t??jo()}static generate(){return new en(jo())}static fromSecretKey(t,e){if(t.byteLength!==64)throw new Error("bad secret key size");const r=t.slice(32,64);if(!e||!e.skipValidation){const s=t.slice(0,32),i=si(s);for(let o=0;o<32;o++)if(r[o]!==i[o])throw new Error("provided secretKey is invalid")}return new en({publicKey:r,secretKey:t})}static fromSeed(t){const e=si(t),r=new Uint8Array(64);return r.set(t),r.set(e,32),new en({publicKey:e,secretKey:r})}get publicKey(){return new Y(this._keypair.publicKey)}get secretKey(){return new Uint8Array(this._keypair.secretKey)}}const gr=Object.freeze({CreateLookupTable:{index:0,layout:ot([st("instruction"),cn("recentSlot"),vt("bumpSeed")])},FreezeLookupTable:{index:1,layout:ot([st("instruction")])},ExtendLookupTable:{index:2,layout:ot([st("instruction"),cn(),xe(wt(),_r(st(),-8),"addresses")])},DeactivateLookupTable:{index:3,layout:ot([st("instruction")])},CloseLookupTable:{index:4,layout:ot([st("instruction")])}});class z0{constructor(){}static decodeInstructionType(t){this.checkProgramId(t.programId);const r=st("instruction").decode(t.data);let s;for(const[i,o]of Object.entries(gr))if(o.index==r){s=i;break}if(!s)throw new Error("Invalid Instruction. Should be a LookupTable Instruction");return s}static decodeCreateLookupTable(t){this.checkProgramId(t.programId),this.checkKeysLength(t.keys,4);const{recentSlot:e}=_t(gr.CreateLookupTable,t.data);return{authority:t.keys[1].pubkey,payer:t.keys[2].pubkey,recentSlot:Number(e)}}static decodeExtendLookupTable(t){if(this.checkProgramId(t.programId),t.keys.length<2)throw new Error(`invalid instruction; found ${t.keys.length} keys, expected at least 2`);const{addresses:e}=_t(gr.ExtendLookupTable,t.data);return{lookupTable:t.keys[0].pubkey,authority:t.keys[1].pubkey,payer:t.keys.length>2?t.keys[2].pubkey:void 0,addresses:e.map(r=>new Y(r))}}static decodeCloseLookupTable(t){return this.checkProgramId(t.programId),this.checkKeysLength(t.keys,3),{lookupTable:t.keys[0].pubkey,authority:t.keys[1].pubkey,recipient:t.keys[2].pubkey}}static decodeFreezeLookupTable(t){return this.checkProgramId(t.programId),this.checkKeysLength(t.keys,2),{lookupTable:t.keys[0].pubkey,authority:t.keys[1].pubkey}}static decodeDeactivateLookupTable(t){return this.checkProgramId(t.programId),this.checkKeysLength(t.keys,2),{lookupTable:t.keys[0].pubkey,authority:t.keys[1].pubkey}}static checkProgramId(t){if(!t.equals(lo.programId))throw new Error("invalid instruction; programId is not AddressLookupTable Program")}static checkKeysLength(t,e){if(t.length<e)throw new Error(`invalid instruction; found ${t.length} keys, expected at least ${e}`)}}class lo{constructor(){}static createLookupTable(t){const[e,r]=Y.findProgramAddressSync([t.authority.toBuffer(),dc().encode(t.recentSlot)],this.programId),s=gr.CreateLookupTable,i=bt(s,{recentSlot:BigInt(t.recentSlot),bumpSeed:r}),o=[{pubkey:e,isSigner:!1,isWritable:!0},{pubkey:t.authority,isSigner:!0,isWritable:!1},{pubkey:t.payer,isSigner:!0,isWritable:!0},{pubkey:ye.programId,isSigner:!1,isWritable:!1}];return[new Bt({programId:this.programId,keys:o,data:i}),e]}static freezeLookupTable(t){const e=gr.FreezeLookupTable,r=bt(e),s=[{pubkey:t.lookupTable,isSigner:!1,isWritable:!0},{pubkey:t.authority,isSigner:!0,isWritable:!1}];return new Bt({programId:this.programId,keys:s,data:r})}static extendLookupTable(t){const e=gr.ExtendLookupTable,r=bt(e,{addresses:t.addresses.map(i=>i.toBytes())}),s=[{pubkey:t.lookupTable,isSigner:!1,isWritable:!0},{pubkey:t.authority,isSigner:!0,isWritable:!1}];return t.payer&&s.push({pubkey:t.payer,isSigner:!0,isWritable:!0},{pubkey:ye.programId,isSigner:!1,isWritable:!1}),new Bt({programId:this.programId,keys:s,data:r})}static deactivateLookupTable(t){const e=gr.DeactivateLookupTable,r=bt(e),s=[{pubkey:t.lookupTable,isSigner:!1,isWritable:!0},{pubkey:t.authority,isSigner:!0,isWritable:!1}];return new Bt({programId:this.programId,keys:s,data:r})}static closeLookupTable(t){const e=gr.CloseLookupTable,r=bt(e),s=[{pubkey:t.lookupTable,isSigner:!1,isWritable:!0},{pubkey:t.authority,isSigner:!0,isWritable:!1},{pubkey:t.recipient,isSigner:!1,isWritable:!0}];return new Bt({programId:this.programId,keys:s,data:r})}}lo.programId=new Y("AddressLookupTab1e1111111111111111111111111");class F0{constructor(){}static decodeInstructionType(t){this.checkProgramId(t.programId);const r=vt("instruction").decode(t.data);let s;for(const[i,o]of Object.entries(er))if(o.index==r){s=i;break}if(!s)throw new Error("Instruction type incorrect; not a ComputeBudgetInstruction");return s}static decodeRequestUnits(t){this.checkProgramId(t.programId);const{units:e,additionalFee:r}=_t(er.RequestUnits,t.data);return{units:e,additionalFee:r}}static decodeRequestHeapFrame(t){this.checkProgramId(t.programId);const{bytes:e}=_t(er.RequestHeapFrame,t.data);return{bytes:e}}static decodeSetComputeUnitLimit(t){this.checkProgramId(t.programId);const{units:e}=_t(er.SetComputeUnitLimit,t.data);return{units:e}}static decodeSetComputeUnitPrice(t){this.checkProgramId(t.programId);const{microLamports:e}=_t(er.SetComputeUnitPrice,t.data);return{microLamports:e}}static checkProgramId(t){if(!t.equals(ho.programId))throw new Error("invalid instruction; programId is not ComputeBudgetProgram")}}const er=Object.freeze({RequestUnits:{index:0,layout:ot([vt("instruction"),st("units"),st("additionalFee")])},RequestHeapFrame:{index:1,layout:ot([vt("instruction"),st("bytes")])},SetComputeUnitLimit:{index:2,layout:ot([vt("instruction"),st("units")])},SetComputeUnitPrice:{index:3,layout:ot([vt("instruction"),cn("microLamports")])}});class ho{constructor(){}static requestUnits(t){const e=er.RequestUnits,r=bt(e,t);return new Bt({keys:[],programId:this.programId,data:r})}static requestHeapFrame(t){const e=er.RequestHeapFrame,r=bt(e,t);return new Bt({keys:[],programId:this.programId,data:r})}static setComputeUnitLimit(t){const e=er.SetComputeUnitLimit,r=bt(e,t);return new Bt({keys:[],programId:this.programId,data:r})}static setComputeUnitPrice(t){const e=er.SetComputeUnitPrice,r=bt(e,{microLamports:BigInt(t.microLamports)});return new Bt({keys:[],programId:this.programId,data:r})}}ho.programId=new Y("ComputeBudget111111111111111111111111111111");const na=64,ia=32,sa=64,oa=ot([vt("numSignatures"),vt("padding"),We("signatureOffset"),We("signatureInstructionIndex"),We("publicKeyOffset"),We("publicKeyInstructionIndex"),We("messageDataOffset"),We("messageDataSize"),We("messageInstructionIndex")]);class Ei{constructor(){}static createInstructionWithPublicKey(t){const{publicKey:e,message:r,signature:s,instructionIndex:i}=t;xt(e.length===ia,`Public Key must be ${ia} bytes but received ${e.length} bytes`),xt(s.length===sa,`Signature must be ${sa} bytes but received ${s.length} bytes`);const o=oa.span,a=o+e.length,h=a+s.length,y=1,m=ct.Buffer.alloc(h+r.length),E=i??65535;return oa.encode({numSignatures:y,padding:0,signatureOffset:a,signatureInstructionIndex:E,publicKeyOffset:o,publicKeyInstructionIndex:E,messageDataOffset:h,messageDataSize:r.length,messageInstructionIndex:E},m),m.fill(e,o),m.fill(s,a),m.fill(r,h),new Bt({keys:[],programId:Ei.programId,data:m})}static createInstructionWithPrivateKey(t){const{privateKey:e,message:r,instructionIndex:s}=t;xt(e.length===na,`Private key must be ${na} bytes but received ${e.length} bytes`);try{const i=en.fromSecretKey(e),o=i.publicKey.toBytes(),a=eo(r,i.secretKey);return this.createInstructionWithPublicKey({publicKey:o,message:r,signature:a,instructionIndex:s})}catch(i){throw new Error(`Error creating instruction; ${i}`)}}}Ei.programId=new Y("Ed25519SigVerify111111111111111111111111111");const D0=(n,t)=>{const e=to.sign(n,t);return[e.toCompactRawBytes(),e.recovery]};to.utils.isValidPrivateKey;const K0=to.getPublicKey,aa=32,ls=20,ca=64,q0=11,hs=ot([vt("numSignatures"),We("signatureOffset"),vt("signatureInstructionIndex"),We("ethAddressOffset"),vt("ethAddressInstructionIndex"),We("messageDataOffset"),We("messageDataSize"),vt("messageInstructionIndex"),Ot(20,"ethAddress"),Ot(64,"signature"),vt("recoveryId")]);class rn{constructor(){}static publicKeyToEthAddress(t){xt(t.length===ca,`Public key must be ${ca} bytes but received ${t.length} bytes`);try{return ct.Buffer.from($o(yt(t))).slice(-ls)}catch(e){throw new Error(`Error constructing Ethereum address: ${e}`)}}static createInstructionWithPublicKey(t){const{publicKey:e,message:r,signature:s,recoveryId:i,instructionIndex:o}=t;return rn.createInstructionWithEthAddress({ethAddress:rn.publicKeyToEthAddress(e),message:r,signature:s,recoveryId:i,instructionIndex:o})}static createInstructionWithEthAddress(t){const{ethAddress:e,message:r,signature:s,recoveryId:i,instructionIndex:o=0}=t;let a;typeof e=="string"?e.startsWith("0x")?a=ct.Buffer.from(e.substr(2),"hex"):a=ct.Buffer.from(e,"hex"):a=e,xt(a.length===ls,`Address must be ${ls} bytes but received ${a.length} bytes`);const h=1+q0,y=h,m=h+a.length,E=m+s.length+1,M=1,k=ct.Buffer.alloc(hs.span+r.length);return hs.encode({numSignatures:M,signatureOffset:m,signatureInstructionIndex:o,ethAddressOffset:y,ethAddressInstructionIndex:o,messageDataOffset:E,messageDataSize:r.length,messageInstructionIndex:o,signature:yt(s),ethAddress:yt(a),recoveryId:i},k),k.fill(yt(r),hs.span),new Bt({keys:[],programId:rn.programId,data:k})}static createInstructionWithPrivateKey(t){const{privateKey:e,message:r,instructionIndex:s}=t;xt(e.length===aa,`Private key must be ${aa} bytes but received ${e.length} bytes`);try{const i=yt(e),o=K0(i,!1).slice(1),a=ct.Buffer.from($o(yt(r))),[h,y]=D0(a,i);return this.createInstructionWithPublicKey({publicKey:o,message:r,signature:h,recoveryId:y,instructionIndex:s})}catch(i){throw new Error(`Error creating instruction; ${i}`)}}}rn.programId=new Y("KeccakSecp256k11111111111111111111111111111");var Jc;const Xc=new Y("StakeConfig11111111111111111111111111111111");class Qc{constructor(t,e){this.staker=void 0,this.withdrawer=void 0,this.staker=t,this.withdrawer=e}}class On{constructor(t,e,r){this.unixTimestamp=void 0,this.epoch=void 0,this.custodian=void 0,this.unixTimestamp=t,this.epoch=e,this.custodian=r}}Jc=On;On.default=new Jc(0,0,Y.default);class W0{constructor(){}static decodeInstructionType(t){this.checkProgramId(t.programId);const r=st("instruction").decode(t.data);let s;for(const[i,o]of Object.entries(ge))if(o.index==r){s=i;break}if(!s)throw new Error("Instruction type incorrect; not a StakeInstruction");return s}static decodeInitialize(t){this.checkProgramId(t.programId),this.checkKeyLength(t.keys,2);const{authorized:e,lockup:r}=_t(ge.Initialize,t.data);return{stakePubkey:t.keys[0].pubkey,authorized:new Qc(new Y(e.staker),new Y(e.withdrawer)),lockup:new On(r.unixTimestamp,r.epoch,new Y(r.custodian))}}static decodeDelegate(t){return this.checkProgramId(t.programId),this.checkKeyLength(t.keys,6),_t(ge.Delegate,t.data),{stakePubkey:t.keys[0].pubkey,votePubkey:t.keys[1].pubkey,authorizedPubkey:t.keys[5].pubkey}}static decodeAuthorize(t){this.checkProgramId(t.programId),this.checkKeyLength(t.keys,3);const{newAuthorized:e,stakeAuthorizationType:r}=_t(ge.Authorize,t.data),s={stakePubkey:t.keys[0].pubkey,authorizedPubkey:t.keys[2].pubkey,newAuthorizedPubkey:new Y(e),stakeAuthorizationType:{index:r}};return t.keys.length>3&&(s.custodianPubkey=t.keys[3].pubkey),s}static decodeAuthorizeWithSeed(t){this.checkProgramId(t.programId),this.checkKeyLength(t.keys,2);const{newAuthorized:e,stakeAuthorizationType:r,authoritySeed:s,authorityOwner:i}=_t(ge.AuthorizeWithSeed,t.data),o={stakePubkey:t.keys[0].pubkey,authorityBase:t.keys[1].pubkey,authoritySeed:s,authorityOwner:new Y(i),newAuthorizedPubkey:new Y(e),stakeAuthorizationType:{index:r}};return t.keys.length>3&&(o.custodianPubkey=t.keys[3].pubkey),o}static decodeSplit(t){this.checkProgramId(t.programId),this.checkKeyLength(t.keys,3);const{lamports:e}=_t(ge.Split,t.data);return{stakePubkey:t.keys[0].pubkey,splitStakePubkey:t.keys[1].pubkey,authorizedPubkey:t.keys[2].pubkey,lamports:e}}static decodeMerge(t){return this.checkProgramId(t.programId),this.checkKeyLength(t.keys,3),_t(ge.Merge,t.data),{stakePubkey:t.keys[0].pubkey,sourceStakePubKey:t.keys[1].pubkey,authorizedPubkey:t.keys[4].pubkey}}static decodeWithdraw(t){this.checkProgramId(t.programId),this.checkKeyLength(t.keys,5);const{lamports:e}=_t(ge.Withdraw,t.data),r={stakePubkey:t.keys[0].pubkey,toPubkey:t.keys[1].pubkey,authorizedPubkey:t.keys[4].pubkey,lamports:e};return t.keys.length>5&&(r.custodianPubkey=t.keys[5].pubkey),r}static decodeDeactivate(t){return this.checkProgramId(t.programId),this.checkKeyLength(t.keys,3),_t(ge.Deactivate,t.data),{stakePubkey:t.keys[0].pubkey,authorizedPubkey:t.keys[2].pubkey}}static checkProgramId(t){if(!t.equals(_i.programId))throw new Error("invalid instruction; programId is not StakeProgram")}static checkKeyLength(t,e){if(t.length<e)throw new Error(`invalid instruction; found ${t.length} keys, expected at least ${e}`)}}const ge=Object.freeze({Initialize:{index:0,layout:ot([st("instruction"),jh(),Gh()])},Authorize:{index:1,layout:ot([st("instruction"),wt("newAuthorized"),st("stakeAuthorizationType")])},Delegate:{index:2,layout:ot([st("instruction")])},Split:{index:3,layout:ot([st("instruction"),ze("lamports")])},Withdraw:{index:4,layout:ot([st("instruction"),ze("lamports")])},Deactivate:{index:5,layout:ot([st("instruction")])},Merge:{index:7,layout:ot([st("instruction")])},AuthorizeWithSeed:{index:8,layout:ot([st("instruction"),wt("newAuthorized"),st("stakeAuthorizationType"),Cr("authoritySeed"),wt("authorityOwner")])}}),$0=Object.freeze({Staker:{index:0},Withdrawer:{index:1}});class _i{constructor(){}static initialize(t){const{stakePubkey:e,authorized:r,lockup:s}=t,i=s||On.default,o=ge.Initialize,a=bt(o,{authorized:{staker:yt(r.staker.toBuffer()),withdrawer:yt(r.withdrawer.toBuffer())},lockup:{unixTimestamp:i.unixTimestamp,epoch:i.epoch,custodian:yt(i.custodian.toBuffer())}}),h={keys:[{pubkey:e,isSigner:!1,isWritable:!0},{pubkey:an,isSigner:!1,isWritable:!1}],programId:this.programId,data:a};return new Bt(h)}static createAccountWithSeed(t){const e=new Et;e.add(ye.createAccountWithSeed({fromPubkey:t.fromPubkey,newAccountPubkey:t.stakePubkey,basePubkey:t.basePubkey,seed:t.seed,lamports:t.lamports,space:this.space,programId:this.programId}));const{stakePubkey:r,authorized:s,lockup:i}=t;return e.add(this.initialize({stakePubkey:r,authorized:s,lockup:i}))}static createAccount(t){const e=new Et;e.add(ye.createAccount({fromPubkey:t.fromPubkey,newAccountPubkey:t.stakePubkey,lamports:t.lamports,space:this.space,programId:this.programId}));const{stakePubkey:r,authorized:s,lockup:i}=t;return e.add(this.initialize({stakePubkey:r,authorized:s,lockup:i}))}static delegate(t){const{stakePubkey:e,authorizedPubkey:r,votePubkey:s}=t,i=ge.Delegate,o=bt(i);return new Et().add({keys:[{pubkey:e,isSigner:!1,isWritable:!0},{pubkey:s,isSigner:!1,isWritable:!1},{pubkey:Xe,isSigner:!1,isWritable:!1},{pubkey:Zn,isSigner:!1,isWritable:!1},{pubkey:Xc,isSigner:!1,isWritable:!1},{pubkey:r,isSigner:!0,isWritable:!1}],programId:this.programId,data:o})}static authorize(t){const{stakePubkey:e,authorizedPubkey:r,newAuthorizedPubkey:s,stakeAuthorizationType:i,custodianPubkey:o}=t,a=ge.Authorize,h=bt(a,{newAuthorized:yt(s.toBuffer()),stakeAuthorizationType:i.index}),y=[{pubkey:e,isSigner:!1,isWritable:!0},{pubkey:Xe,isSigner:!1,isWritable:!0},{pubkey:r,isSigner:!0,isWritable:!1}];return o&&y.push({pubkey:o,isSigner:!0,isWritable:!1}),new Et().add({keys:y,programId:this.programId,data:h})}static authorizeWithSeed(t){const{stakePubkey:e,authorityBase:r,authoritySeed:s,authorityOwner:i,newAuthorizedPubkey:o,stakeAuthorizationType:a,custodianPubkey:h}=t,y=ge.AuthorizeWithSeed,m=bt(y,{newAuthorized:yt(o.toBuffer()),stakeAuthorizationType:a.index,authoritySeed:s,authorityOwner:yt(i.toBuffer())}),E=[{pubkey:e,isSigner:!1,isWritable:!0},{pubkey:r,isSigner:!0,isWritable:!1},{pubkey:Xe,isSigner:!1,isWritable:!1}];return h&&E.push({pubkey:h,isSigner:!0,isWritable:!1}),new Et().add({keys:E,programId:this.programId,data:m})}static splitInstruction(t){const{stakePubkey:e,authorizedPubkey:r,splitStakePubkey:s,lamports:i}=t,o=ge.Split,a=bt(o,{lamports:i});return new Bt({keys:[{pubkey:e,isSigner:!1,isWritable:!0},{pubkey:s,isSigner:!1,isWritable:!0},{pubkey:r,isSigner:!0,isWritable:!1}],programId:this.programId,data:a})}static split(t,e){const r=new Et;return r.add(ye.createAccount({fromPubkey:t.authorizedPubkey,newAccountPubkey:t.splitStakePubkey,lamports:e,space:this.space,programId:this.programId})),r.add(this.splitInstruction(t))}static splitWithSeed(t,e){const{stakePubkey:r,authorizedPubkey:s,splitStakePubkey:i,basePubkey:o,seed:a,lamports:h}=t,y=new Et;return y.add(ye.allocate({accountPubkey:i,basePubkey:o,seed:a,space:this.space,programId:this.programId})),e&&e>0&&y.add(ye.transfer({fromPubkey:t.authorizedPubkey,toPubkey:i,lamports:e})),y.add(this.splitInstruction({stakePubkey:r,authorizedPubkey:s,splitStakePubkey:i,lamports:h}))}static merge(t){const{stakePubkey:e,sourceStakePubKey:r,authorizedPubkey:s}=t,i=ge.Merge,o=bt(i);return new Et().add({keys:[{pubkey:e,isSigner:!1,isWritable:!0},{pubkey:r,isSigner:!1,isWritable:!0},{pubkey:Xe,isSigner:!1,isWritable:!1},{pubkey:Zn,isSigner:!1,isWritable:!1},{pubkey:s,isSigner:!0,isWritable:!1}],programId:this.programId,data:o})}static withdraw(t){const{stakePubkey:e,authorizedPubkey:r,toPubkey:s,lamports:i,custodianPubkey:o}=t,a=ge.Withdraw,h=bt(a,{lamports:i}),y=[{pubkey:e,isSigner:!1,isWritable:!0},{pubkey:s,isSigner:!1,isWritable:!0},{pubkey:Xe,isSigner:!1,isWritable:!1},{pubkey:Zn,isSigner:!1,isWritable:!1},{pubkey:r,isSigner:!0,isWritable:!1}];return o&&y.push({pubkey:o,isSigner:!0,isWritable:!1}),new Et().add({keys:y,programId:this.programId,data:h})}static deactivate(t){const{stakePubkey:e,authorizedPubkey:r}=t,s=ge.Deactivate,i=bt(s);return new Et().add({keys:[{pubkey:e,isSigner:!1,isWritable:!0},{pubkey:Xe,isSigner:!1,isWritable:!1},{pubkey:r,isSigner:!0,isWritable:!1}],programId:this.programId,data:i})}}_i.programId=new Y("Stake11111111111111111111111111111111111111");_i.space=200;class tu{constructor(t,e,r,s){this.nodePubkey=void 0,this.authorizedVoter=void 0,this.authorizedWithdrawer=void 0,this.commission=void 0,this.nodePubkey=t,this.authorizedVoter=e,this.authorizedWithdrawer=r,this.commission=s}}class H0{constructor(){}static decodeInstructionType(t){this.checkProgramId(t.programId);const r=st("instruction").decode(t.data);let s;for(const[i,o]of Object.entries(rr))if(o.index==r){s=i;break}if(!s)throw new Error("Instruction type incorrect; not a VoteInstruction");return s}static decodeInitializeAccount(t){this.checkProgramId(t.programId),this.checkKeyLength(t.keys,4);const{voteInit:e}=_t(rr.InitializeAccount,t.data);return{votePubkey:t.keys[0].pubkey,nodePubkey:t.keys[3].pubkey,voteInit:new tu(new Y(e.nodePubkey),new Y(e.authorizedVoter),new Y(e.authorizedWithdrawer),e.commission)}}static decodeAuthorize(t){this.checkProgramId(t.programId),this.checkKeyLength(t.keys,3);const{newAuthorized:e,voteAuthorizationType:r}=_t(rr.Authorize,t.data);return{votePubkey:t.keys[0].pubkey,authorizedPubkey:t.keys[2].pubkey,newAuthorizedPubkey:new Y(e),voteAuthorizationType:{index:r}}}static decodeAuthorizeWithSeed(t){this.checkProgramId(t.programId),this.checkKeyLength(t.keys,3);const{voteAuthorizeWithSeedArgs:{currentAuthorityDerivedKeyOwnerPubkey:e,currentAuthorityDerivedKeySeed:r,newAuthorized:s,voteAuthorizationType:i}}=_t(rr.AuthorizeWithSeed,t.data);return{currentAuthorityDerivedKeyBasePubkey:t.keys[2].pubkey,currentAuthorityDerivedKeyOwnerPubkey:new Y(e),currentAuthorityDerivedKeySeed:r,newAuthorizedPubkey:new Y(s),voteAuthorizationType:{index:i},votePubkey:t.keys[0].pubkey}}static decodeWithdraw(t){this.checkProgramId(t.programId),this.checkKeyLength(t.keys,3);const{lamports:e}=_t(rr.Withdraw,t.data);return{votePubkey:t.keys[0].pubkey,authorizedWithdrawerPubkey:t.keys[2].pubkey,lamports:e,toPubkey:t.keys[1].pubkey}}static checkProgramId(t){if(!t.equals(pn.programId))throw new Error("invalid instruction; programId is not VoteProgram")}static checkKeyLength(t,e){if(t.length<e)throw new Error(`invalid instruction; found ${t.length} keys, expected at least ${e}`)}}const rr=Object.freeze({InitializeAccount:{index:0,layout:ot([st("instruction"),Zh()])},Authorize:{index:1,layout:ot([st("instruction"),wt("newAuthorized"),st("voteAuthorizationType")])},Withdraw:{index:3,layout:ot([st("instruction"),ze("lamports")])},UpdateValidatorIdentity:{index:4,layout:ot([st("instruction")])},AuthorizeWithSeed:{index:10,layout:ot([st("instruction"),Yh()])}}),V0=Object.freeze({Voter:{index:0},Withdrawer:{index:1}});class pn{constructor(){}static initializeAccount(t){const{votePubkey:e,nodePubkey:r,voteInit:s}=t,i=rr.InitializeAccount,o=bt(i,{voteInit:{nodePubkey:yt(s.nodePubkey.toBuffer()),authorizedVoter:yt(s.authorizedVoter.toBuffer()),authorizedWithdrawer:yt(s.authorizedWithdrawer.toBuffer()),commission:s.commission}}),a={keys:[{pubkey:e,isSigner:!1,isWritable:!0},{pubkey:an,isSigner:!1,isWritable:!1},{pubkey:Xe,isSigner:!1,isWritable:!1},{pubkey:r,isSigner:!0,isWritable:!1}],programId:this.programId,data:o};return new Bt(a)}static createAccount(t){const e=new Et;return e.add(ye.createAccount({fromPubkey:t.fromPubkey,newAccountPubkey:t.votePubkey,lamports:t.lamports,space:this.space,programId:this.programId})),e.add(this.initializeAccount({votePubkey:t.votePubkey,nodePubkey:t.voteInit.nodePubkey,voteInit:t.voteInit}))}static authorize(t){const{votePubkey:e,authorizedPubkey:r,newAuthorizedPubkey:s,voteAuthorizationType:i}=t,o=rr.Authorize,a=bt(o,{newAuthorized:yt(s.toBuffer()),voteAuthorizationType:i.index}),h=[{pubkey:e,isSigner:!1,isWritable:!0},{pubkey:Xe,isSigner:!1,isWritable:!1},{pubkey:r,isSigner:!0,isWritable:!1}];return new Et().add({keys:h,programId:this.programId,data:a})}static authorizeWithSeed(t){const{currentAuthorityDerivedKeyBasePubkey:e,currentAuthorityDerivedKeyOwnerPubkey:r,currentAuthorityDerivedKeySeed:s,newAuthorizedPubkey:i,voteAuthorizationType:o,votePubkey:a}=t,h=rr.AuthorizeWithSeed,y=bt(h,{voteAuthorizeWithSeedArgs:{currentAuthorityDerivedKeyOwnerPubkey:yt(r.toBuffer()),currentAuthorityDerivedKeySeed:s,newAuthorized:yt(i.toBuffer()),voteAuthorizationType:o.index}}),m=[{pubkey:a,isSigner:!1,isWritable:!0},{pubkey:Xe,isSigner:!1,isWritable:!1},{pubkey:e,isSigner:!0,isWritable:!1}];return new Et().add({keys:m,programId:this.programId,data:y})}static withdraw(t){const{votePubkey:e,authorizedWithdrawerPubkey:r,lamports:s,toPubkey:i}=t,o=rr.Withdraw,a=bt(o,{lamports:s}),h=[{pubkey:e,isSigner:!1,isWritable:!0},{pubkey:i,isSigner:!1,isWritable:!0},{pubkey:r,isSigner:!0,isWritable:!1}];return new Et().add({keys:h,programId:this.programId,data:a})}static safeWithdraw(t,e,r){if(t.lamports>e-r)throw new Error("Withdraw will leave vote account with insufficient funds.");return pn.withdraw(t)}static updateValidatorIdentity(t){const{votePubkey:e,authorizedWithdrawerPubkey:r,nodePubkey:s}=t,i=rr.UpdateValidatorIdentity,o=bt(i),a=[{pubkey:e,isSigner:!1,isWritable:!0},{pubkey:s,isSigner:!0,isWritable:!1},{pubkey:r,isSigner:!0,isWritable:!1}];return new Et().add({keys:a,programId:this.programId,data:o})}}pn.programId=new Y("Vote111111111111111111111111111111111111111");pn.space=3762;const eu=new Y("Va1idator1nfo111111111111111111111111111111"),j0=H({name:Z(),website:ut(Z()),details:ut(Z()),iconUrl:ut(Z()),keybaseUsername:ut(Z())});class po{constructor(t,e){this.key=void 0,this.info=void 0,this.key=t,this.info=e}static fromConfigData(t){let e=[...t];if(Le(e)!==2)return null;const s=[];for(let i=0;i<2;i++){const o=new Y(Ce(e,0,ir)),a=tr(e)===1;s.push({publicKey:o,isSigner:a})}if(s[0].publicKey.equals(eu)&&s[1].isSigner){const i=Cr().decode(ct.Buffer.from(e)),o=JSON.parse(i);return pc(o,j0),new po(s[1].publicKey,o)}return null}}const G0=new Y("Vote111111111111111111111111111111111111111"),Z0=ot([wt("nodePubkey"),wt("authorizedWithdrawer"),vt("commission"),be(),xe(ot([be("slot"),st("confirmationCount")]),_r(st(),-8),"votes"),vt("rootSlotValid"),be("rootSlot"),be(),xe(ot([be("epoch"),wt("authorizedVoter")]),_r(st(),-8),"authorizedVoters"),ot([xe(ot([wt("authorizedPubkey"),be("epochOfLastAuthorizedSwitch"),be("targetEpoch")]),32,"buf"),be("idx"),vt("isEmpty")],"priorVoters"),be(),xe(ot([be("epoch"),be("credits"),be("prevCredits")]),_r(st(),-8),"epochCredits"),ot([be("slot"),be("timestamp")],"lastTimestamp")]);class go{constructor(t){this.nodePubkey=void 0,this.authorizedWithdrawer=void 0,this.commission=void 0,this.rootSlot=void 0,this.votes=void 0,this.authorizedVoters=void 0,this.priorVoters=void 0,this.epochCredits=void 0,this.lastTimestamp=void 0,this.nodePubkey=t.nodePubkey,this.authorizedWithdrawer=t.authorizedWithdrawer,this.commission=t.commission,this.rootSlot=t.rootSlot,this.votes=t.votes,this.authorizedVoters=t.authorizedVoters,this.priorVoters=t.priorVoters,this.epochCredits=t.epochCredits,this.lastTimestamp=t.lastTimestamp}static fromAccountData(t){const r=Z0.decode(yt(t),4);let s=r.rootSlot;return r.rootSlotValid||(s=null),new go({nodePubkey:new Y(r.nodePubkey),authorizedWithdrawer:new Y(r.authorizedWithdrawer),commission:r.commission,votes:r.votes,rootSlot:s,authorizedVoters:r.authorizedVoters.map(Y0),priorVoters:J0(r.priorVoters),epochCredits:r.epochCredits,lastTimestamp:r.lastTimestamp})}}function Y0({authorizedVoter:n,epoch:t}){return{epoch:t,authorizedVoter:new Y(n)}}function ua({authorizedPubkey:n,epochOfLastAuthorizedSwitch:t,targetEpoch:e}){return{authorizedPubkey:new Y(n),epochOfLastAuthorizedSwitch:t,targetEpoch:e}}function J0({buf:n,idx:t,isEmpty:e}){return e?[]:[...n.slice(t+1).map(ua),...n.slice(0,t).map(ua)]}const fa={http:{devnet:"http://api.devnet.solana.com",testnet:"http://api.testnet.solana.com","mainnet-beta":"http://api.mainnet-beta.solana.com/"},https:{devnet:"https://api.devnet.solana.com",testnet:"https://api.testnet.solana.com","mainnet-beta":"https://api.mainnet-beta.solana.com/"}};function X0(n,t){const e=t===!1?"http":"https";if(!n)return fa[e].devnet;const r=fa[e][n];if(!r)throw new Error(`Unknown ${e} cluster: ${n}`);return r}async function Q0(n,t,e,r){let s,i;e&&Object.prototype.hasOwnProperty.call(e,"lastValidBlockHeight")||e&&Object.prototype.hasOwnProperty.call(e,"nonceValue")?(s=e,i=r):i=e;const o=i&&{skipPreflight:i.skipPreflight,preflightCommitment:i.preflightCommitment||i.commitment,minContextSlot:i.minContextSlot},a=await n.sendRawTransaction(t,o),h=i&&i.commitment,m=(await(s?n.confirmTransaction(s,h):n.confirmTransaction(a,h))).value;if(m.err)throw a!=null?new Bn({action:o!=null&&o.skipPreflight?"send":"simulate",signature:a,transactionMessage:`Status: (${JSON.stringify(m)})`}):new Error(`Raw transaction ${a} failed (${JSON.stringify(m)})`);return a}const tp=1e9,op=Object.freeze(Object.defineProperty({__proto__:null,Account:$h,AddressLookupTableAccount:xs,AddressLookupTableInstruction:z0,AddressLookupTableProgram:lo,Authorized:Qc,BLOCKHASH_CACHE_TIMEOUT_MS:Kc,BPF_LOADER_DEPRECATED_PROGRAM_ID:Hh,BPF_LOADER_PROGRAM_ID:ud,BpfLoader:fd,COMPUTE_BUDGET_INSTRUCTION_LAYOUTS:er,ComputeBudgetInstruction:F0,ComputeBudgetProgram:ho,Connection:N0,Ed25519Program:Ei,Enum:qh,EpochSchedule:Fc,FeeCalculatorLayout:Nc,Keypair:en,LAMPORTS_PER_SOL:tp,LOOKUP_TABLE_INSTRUCTION_LAYOUTS:gr,Loader:Fr,Lockup:On,MAX_SEED_LENGTH:Lc,Message:Ze,MessageAccountKeys:An,MessageV0:zr,NONCE_ACCOUNT_LENGTH:ks,NonceAccount:ki,PACKET_DATA_SIZE:Ir,PUBLIC_KEY_LENGTH:ir,PublicKey:Y,SIGNATURE_LENGTH_IN_BYTES:In,SOLANA_SCHEMA:kn,STAKE_CONFIG_ID:Xc,STAKE_INSTRUCTION_LAYOUTS:ge,SYSTEM_INSTRUCTION_LAYOUTS:Tt,SYSVAR_CLOCK_PUBKEY:Xe,SYSVAR_EPOCH_SCHEDULE_PUBKEY:ed,SYSVAR_INSTRUCTIONS_PUBKEY:rd,SYSVAR_RECENT_BLOCKHASHES_PUBKEY:Gn,SYSVAR_RENT_PUBKEY:an,SYSVAR_REWARDS_PUBKEY:nd,SYSVAR_SLOT_HASHES_PUBKEY:id,SYSVAR_SLOT_HISTORY_PUBKEY:sd,SYSVAR_STAKE_HISTORY_PUBKEY:Zn,Secp256k1Program:rn,SendTransactionError:Bn,SolanaJSONRPCError:at,SolanaJSONRPCErrorCode:od,StakeAuthorizationLayout:$0,StakeInstruction:W0,StakeProgram:_i,Struct:ro,SystemInstruction:ad,SystemProgram:ye,Transaction:Et,TransactionExpiredBlockheightExceededError:no,TransactionExpiredNonceInvalidError:Qr,TransactionExpiredTimeoutError:io,TransactionInstruction:Bt,TransactionMessage:oo,TransactionStatus:hr,VALIDATOR_INFO_KEY:eu,VERSION_PREFIX_MASK:bi,VOTE_PROGRAM_ID:G0,ValidatorInfo:po,VersionedMessage:so,VersionedTransaction:ao,VoteAccount:go,VoteAuthorizationLayout:V0,VoteInit:tu,VoteInstruction:H0,VoteProgram:pn,clusterApiUrl:X0,sendAndConfirmRawTransaction:Q0,sendAndConfirmTransaction:vs},Symbol.toStringTag,{value:"Module"}));export{N0 as C,en as K,tp as L,Y as P,In as S,Et as T,so as V,Is as a,ct as b,je as c,ao as d,np as e,ja as f,ai as g,Ot as h,op as i,ot as j,st as k,ye as l,Bt as m,vs as n,X0 as o,zf as r,Va as s,vt as u};
