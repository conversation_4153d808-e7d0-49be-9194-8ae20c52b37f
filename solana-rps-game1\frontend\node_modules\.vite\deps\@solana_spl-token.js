import {
  Keypair,
  PUBLIC_KEY_LENGTH,
  PublicKey,
  SYSVAR_RENT_PUBKEY,
  SystemProgram,
  Transaction,
  TransactionInstruction,
  init_index_browser_esm,
  require_Layout,
  sendAndConfirmTransaction
} from "./chunk-EMSKGLQ5.js";
import "./chunk-SUZE37AV.js";
import "./chunk-37HUACP4.js";
import "./chunk-E7YD6LZS.js";
import "./chunk-LG344HM7.js";
import {
  __commonJS,
  __publicField,
  __toESM
} from "./chunk-WXXH56N5.js";

// node_modules/bigint-buffer/dist/browser.js
var require_browser = __commonJS({
  "node_modules/bigint-buffer/dist/browser.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    var converter;
    function toBigIntLE2(buf) {
      {
        const reversed = Buffer.from(buf);
        reversed.reverse();
        const hex = reversed.toString("hex");
        if (hex.length === 0) {
          return BigInt(0);
        }
        return BigInt(`0x${hex}`);
      }
      return converter.toBigInt(buf, false);
    }
    exports.toBigIntLE = toBigIntLE2;
    function toBigIntBE2(buf) {
      {
        const hex = buf.toString("hex");
        if (hex.length === 0) {
          return BigInt(0);
        }
        return BigInt(`0x${hex}`);
      }
      return converter.toBigInt(buf, true);
    }
    exports.toBigIntBE = toBigIntBE2;
    function toBufferLE2(num, width) {
      {
        const hex = num.toString(16);
        const buffer = Buffer.from(hex.padStart(width * 2, "0").slice(0, width * 2), "hex");
        buffer.reverse();
        return buffer;
      }
      return converter.fromBigInt(num, Buffer.allocUnsafe(width), false);
    }
    exports.toBufferLE = toBufferLE2;
    function toBufferBE2(num, width) {
      {
        const hex = num.toString(16);
        return Buffer.from(hex.padStart(width * 2, "0").slice(0, width * 2), "hex");
      }
      return converter.fromBigInt(num, Buffer.allocUnsafe(width), true);
    }
    exports.toBufferBE = toBufferBE2;
  }
});

// node_modules/@solana/spl-token/lib/esm/actions/amountToUiAmount.js
init_index_browser_esm();

// node_modules/@solana/spl-token/lib/esm/constants.js
init_index_browser_esm();
var TOKEN_PROGRAM_ID = new PublicKey("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA");
var TOKEN_2022_PROGRAM_ID = new PublicKey("TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb");
var ASSOCIATED_TOKEN_PROGRAM_ID = new PublicKey("ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL");
var NATIVE_MINT = new PublicKey("So11111111111111111111111111111111111111112");
var NATIVE_MINT_2022 = new PublicKey("9pan9bMn5HatX4EJdBwg9VgCa7Uz5HL8N1m5D3NdXejP");
function programSupportsExtensions(programId) {
  if (programId.equals(TOKEN_PROGRAM_ID)) {
    return false;
  } else {
    return true;
  }
}

// node_modules/@solana/spl-token/lib/esm/instructions/amountToUiAmount.js
var import_buffer_layout4 = __toESM(require_Layout(), 1);

// node_modules/@solana/buffer-layout-utils/lib/esm/base.mjs
var encodeDecode = (layout) => {
  const decode = layout.decode.bind(layout);
  const encode = layout.encode.bind(layout);
  return { decode, encode };
};

// node_modules/@solana/buffer-layout-utils/lib/esm/bigint.mjs
var import_buffer_layout = __toESM(require_Layout(), 1);
var import_bigint_buffer = __toESM(require_browser(), 1);
var bigInt = (length) => (property) => {
  const layout = (0, import_buffer_layout.blob)(length, property);
  const { encode, decode } = encodeDecode(layout);
  const bigIntLayout = layout;
  bigIntLayout.decode = (buffer, offset) => {
    const src = decode(buffer, offset);
    return (0, import_bigint_buffer.toBigIntLE)(Buffer.from(src));
  };
  bigIntLayout.encode = (bigInt2, buffer, offset) => {
    const src = (0, import_bigint_buffer.toBufferLE)(bigInt2, length);
    return encode(src, buffer, offset);
  };
  return bigIntLayout;
};
var bigIntBE = (length) => (property) => {
  const layout = (0, import_buffer_layout.blob)(length, property);
  const { encode, decode } = encodeDecode(layout);
  const bigIntLayout = layout;
  bigIntLayout.decode = (buffer, offset) => {
    const src = decode(buffer, offset);
    return (0, import_bigint_buffer.toBigIntBE)(Buffer.from(src));
  };
  bigIntLayout.encode = (bigInt2, buffer, offset) => {
    const src = (0, import_bigint_buffer.toBufferBE)(bigInt2, length);
    return encode(src, buffer, offset);
  };
  return bigIntLayout;
};
var u64 = bigInt(8);
var u64be = bigIntBE(8);
var u128 = bigInt(16);
var u128be = bigIntBE(16);
var u192 = bigInt(24);
var u192be = bigIntBE(24);
var u256 = bigInt(32);
var u256be = bigIntBE(32);

// node_modules/bignumber.js/bignumber.mjs
var isNumeric = /^-?(?:\d+(?:\.\d*)?|\.\d+)(?:e[+-]?\d+)?$/i;
var mathceil = Math.ceil;
var mathfloor = Math.floor;
var bignumberError = "[BigNumber Error] ";
var tooManyDigits = bignumberError + "Number primitive has more than 15 significant digits: ";
var BASE = 1e14;
var LOG_BASE = 14;
var MAX_SAFE_INTEGER = 9007199254740991;
var POWS_TEN = [1, 10, 100, 1e3, 1e4, 1e5, 1e6, 1e7, 1e8, 1e9, 1e10, 1e11, 1e12, 1e13];
var SQRT_BASE = 1e7;
var MAX = 1e9;
function clone(configObject) {
  var div, convertBase, parseNumeric, P = BigNumber2.prototype = { constructor: BigNumber2, toString: null, valueOf: null }, ONE = new BigNumber2(1), DECIMAL_PLACES = 20, ROUNDING_MODE = 4, TO_EXP_NEG = -7, TO_EXP_POS = 21, MIN_EXP = -1e7, MAX_EXP = 1e7, CRYPTO = false, MODULO_MODE = 1, POW_PRECISION = 0, FORMAT = {
    prefix: "",
    groupSize: 3,
    secondaryGroupSize: 0,
    groupSeparator: ",",
    decimalSeparator: ".",
    fractionGroupSize: 0,
    fractionGroupSeparator: " ",
    // non-breaking space
    suffix: ""
  }, ALPHABET = "0123456789abcdefghijklmnopqrstuvwxyz", alphabetHasNormalDecimalDigits = true;
  function BigNumber2(v, b) {
    var alphabet, c, caseChanged, e2, i, isNum, len, str, x = this;
    if (!(x instanceof BigNumber2))
      return new BigNumber2(v, b);
    if (b == null) {
      if (v && v._isBigNumber === true) {
        x.s = v.s;
        if (!v.c || v.e > MAX_EXP) {
          x.c = x.e = null;
        } else if (v.e < MIN_EXP) {
          x.c = [x.e = 0];
        } else {
          x.e = v.e;
          x.c = v.c.slice();
        }
        return;
      }
      if ((isNum = typeof v == "number") && v * 0 == 0) {
        x.s = 1 / v < 0 ? (v = -v, -1) : 1;
        if (v === ~~v) {
          for (e2 = 0, i = v; i >= 10; i /= 10, e2++)
            ;
          if (e2 > MAX_EXP) {
            x.c = x.e = null;
          } else {
            x.e = e2;
            x.c = [v];
          }
          return;
        }
        str = String(v);
      } else {
        if (!isNumeric.test(str = String(v)))
          return parseNumeric(x, str, isNum);
        x.s = str.charCodeAt(0) == 45 ? (str = str.slice(1), -1) : 1;
      }
      if ((e2 = str.indexOf(".")) > -1)
        str = str.replace(".", "");
      if ((i = str.search(/e/i)) > 0) {
        if (e2 < 0)
          e2 = i;
        e2 += +str.slice(i + 1);
        str = str.substring(0, i);
      } else if (e2 < 0) {
        e2 = str.length;
      }
    } else {
      intCheck(b, 2, ALPHABET.length, "Base");
      if (b == 10 && alphabetHasNormalDecimalDigits) {
        x = new BigNumber2(v);
        return round(x, DECIMAL_PLACES + x.e + 1, ROUNDING_MODE);
      }
      str = String(v);
      if (isNum = typeof v == "number") {
        if (v * 0 != 0)
          return parseNumeric(x, str, isNum, b);
        x.s = 1 / v < 0 ? (str = str.slice(1), -1) : 1;
        if (BigNumber2.DEBUG && str.replace(/^0\.0*|\./, "").length > 15) {
          throw Error(tooManyDigits + v);
        }
      } else {
        x.s = str.charCodeAt(0) === 45 ? (str = str.slice(1), -1) : 1;
      }
      alphabet = ALPHABET.slice(0, b);
      e2 = i = 0;
      for (len = str.length; i < len; i++) {
        if (alphabet.indexOf(c = str.charAt(i)) < 0) {
          if (c == ".") {
            if (i > e2) {
              e2 = len;
              continue;
            }
          } else if (!caseChanged) {
            if (str == str.toUpperCase() && (str = str.toLowerCase()) || str == str.toLowerCase() && (str = str.toUpperCase())) {
              caseChanged = true;
              i = -1;
              e2 = 0;
              continue;
            }
          }
          return parseNumeric(x, String(v), isNum, b);
        }
      }
      isNum = false;
      str = convertBase(str, b, 10, x.s);
      if ((e2 = str.indexOf(".")) > -1)
        str = str.replace(".", "");
      else
        e2 = str.length;
    }
    for (i = 0; str.charCodeAt(i) === 48; i++)
      ;
    for (len = str.length; str.charCodeAt(--len) === 48; )
      ;
    if (str = str.slice(i, ++len)) {
      len -= i;
      if (isNum && BigNumber2.DEBUG && len > 15 && (v > MAX_SAFE_INTEGER || v !== mathfloor(v))) {
        throw Error(tooManyDigits + x.s * v);
      }
      if ((e2 = e2 - i - 1) > MAX_EXP) {
        x.c = x.e = null;
      } else if (e2 < MIN_EXP) {
        x.c = [x.e = 0];
      } else {
        x.e = e2;
        x.c = [];
        i = (e2 + 1) % LOG_BASE;
        if (e2 < 0)
          i += LOG_BASE;
        if (i < len) {
          if (i)
            x.c.push(+str.slice(0, i));
          for (len -= LOG_BASE; i < len; ) {
            x.c.push(+str.slice(i, i += LOG_BASE));
          }
          i = LOG_BASE - (str = str.slice(i)).length;
        } else {
          i -= len;
        }
        for (; i--; str += "0")
          ;
        x.c.push(+str);
      }
    } else {
      x.c = [x.e = 0];
    }
  }
  BigNumber2.clone = clone;
  BigNumber2.ROUND_UP = 0;
  BigNumber2.ROUND_DOWN = 1;
  BigNumber2.ROUND_CEIL = 2;
  BigNumber2.ROUND_FLOOR = 3;
  BigNumber2.ROUND_HALF_UP = 4;
  BigNumber2.ROUND_HALF_DOWN = 5;
  BigNumber2.ROUND_HALF_EVEN = 6;
  BigNumber2.ROUND_HALF_CEIL = 7;
  BigNumber2.ROUND_HALF_FLOOR = 8;
  BigNumber2.EUCLID = 9;
  BigNumber2.config = BigNumber2.set = function(obj) {
    var p, v;
    if (obj != null) {
      if (typeof obj == "object") {
        if (obj.hasOwnProperty(p = "DECIMAL_PLACES")) {
          v = obj[p];
          intCheck(v, 0, MAX, p);
          DECIMAL_PLACES = v;
        }
        if (obj.hasOwnProperty(p = "ROUNDING_MODE")) {
          v = obj[p];
          intCheck(v, 0, 8, p);
          ROUNDING_MODE = v;
        }
        if (obj.hasOwnProperty(p = "EXPONENTIAL_AT")) {
          v = obj[p];
          if (v && v.pop) {
            intCheck(v[0], -MAX, 0, p);
            intCheck(v[1], 0, MAX, p);
            TO_EXP_NEG = v[0];
            TO_EXP_POS = v[1];
          } else {
            intCheck(v, -MAX, MAX, p);
            TO_EXP_NEG = -(TO_EXP_POS = v < 0 ? -v : v);
          }
        }
        if (obj.hasOwnProperty(p = "RANGE")) {
          v = obj[p];
          if (v && v.pop) {
            intCheck(v[0], -MAX, -1, p);
            intCheck(v[1], 1, MAX, p);
            MIN_EXP = v[0];
            MAX_EXP = v[1];
          } else {
            intCheck(v, -MAX, MAX, p);
            if (v) {
              MIN_EXP = -(MAX_EXP = v < 0 ? -v : v);
            } else {
              throw Error(bignumberError + p + " cannot be zero: " + v);
            }
          }
        }
        if (obj.hasOwnProperty(p = "CRYPTO")) {
          v = obj[p];
          if (v === !!v) {
            if (v) {
              if (typeof crypto != "undefined" && crypto && (crypto.getRandomValues || crypto.randomBytes)) {
                CRYPTO = v;
              } else {
                CRYPTO = !v;
                throw Error(bignumberError + "crypto unavailable");
              }
            } else {
              CRYPTO = v;
            }
          } else {
            throw Error(bignumberError + p + " not true or false: " + v);
          }
        }
        if (obj.hasOwnProperty(p = "MODULO_MODE")) {
          v = obj[p];
          intCheck(v, 0, 9, p);
          MODULO_MODE = v;
        }
        if (obj.hasOwnProperty(p = "POW_PRECISION")) {
          v = obj[p];
          intCheck(v, 0, MAX, p);
          POW_PRECISION = v;
        }
        if (obj.hasOwnProperty(p = "FORMAT")) {
          v = obj[p];
          if (typeof v == "object")
            FORMAT = v;
          else
            throw Error(bignumberError + p + " not an object: " + v);
        }
        if (obj.hasOwnProperty(p = "ALPHABET")) {
          v = obj[p];
          if (typeof v == "string" && !/^.?$|[+\-.\s]|(.).*\1/.test(v)) {
            alphabetHasNormalDecimalDigits = v.slice(0, 10) == "0123456789";
            ALPHABET = v;
          } else {
            throw Error(bignumberError + p + " invalid: " + v);
          }
        }
      } else {
        throw Error(bignumberError + "Object expected: " + obj);
      }
    }
    return {
      DECIMAL_PLACES,
      ROUNDING_MODE,
      EXPONENTIAL_AT: [TO_EXP_NEG, TO_EXP_POS],
      RANGE: [MIN_EXP, MAX_EXP],
      CRYPTO,
      MODULO_MODE,
      POW_PRECISION,
      FORMAT,
      ALPHABET
    };
  };
  BigNumber2.isBigNumber = function(v) {
    if (!v || v._isBigNumber !== true)
      return false;
    if (!BigNumber2.DEBUG)
      return true;
    var i, n, c = v.c, e2 = v.e, s = v.s;
    out:
      if ({}.toString.call(c) == "[object Array]") {
        if ((s === 1 || s === -1) && e2 >= -MAX && e2 <= MAX && e2 === mathfloor(e2)) {
          if (c[0] === 0) {
            if (e2 === 0 && c.length === 1)
              return true;
            break out;
          }
          i = (e2 + 1) % LOG_BASE;
          if (i < 1)
            i += LOG_BASE;
          if (String(c[0]).length == i) {
            for (i = 0; i < c.length; i++) {
              n = c[i];
              if (n < 0 || n >= BASE || n !== mathfloor(n))
                break out;
            }
            if (n !== 0)
              return true;
          }
        }
      } else if (c === null && e2 === null && (s === null || s === 1 || s === -1)) {
        return true;
      }
    throw Error(bignumberError + "Invalid BigNumber: " + v);
  };
  BigNumber2.maximum = BigNumber2.max = function() {
    return maxOrMin(arguments, -1);
  };
  BigNumber2.minimum = BigNumber2.min = function() {
    return maxOrMin(arguments, 1);
  };
  BigNumber2.random = function() {
    var pow2_53 = 9007199254740992;
    var random53bitInt = Math.random() * pow2_53 & 2097151 ? function() {
      return mathfloor(Math.random() * pow2_53);
    } : function() {
      return (Math.random() * 1073741824 | 0) * 8388608 + (Math.random() * 8388608 | 0);
    };
    return function(dp) {
      var a, b, e2, k, v, i = 0, c = [], rand = new BigNumber2(ONE);
      if (dp == null)
        dp = DECIMAL_PLACES;
      else
        intCheck(dp, 0, MAX);
      k = mathceil(dp / LOG_BASE);
      if (CRYPTO) {
        if (crypto.getRandomValues) {
          a = crypto.getRandomValues(new Uint32Array(k *= 2));
          for (; i < k; ) {
            v = a[i] * 131072 + (a[i + 1] >>> 11);
            if (v >= 9e15) {
              b = crypto.getRandomValues(new Uint32Array(2));
              a[i] = b[0];
              a[i + 1] = b[1];
            } else {
              c.push(v % 1e14);
              i += 2;
            }
          }
          i = k / 2;
        } else if (crypto.randomBytes) {
          a = crypto.randomBytes(k *= 7);
          for (; i < k; ) {
            v = (a[i] & 31) * 281474976710656 + a[i + 1] * 1099511627776 + a[i + 2] * 4294967296 + a[i + 3] * 16777216 + (a[i + 4] << 16) + (a[i + 5] << 8) + a[i + 6];
            if (v >= 9e15) {
              crypto.randomBytes(7).copy(a, i);
            } else {
              c.push(v % 1e14);
              i += 7;
            }
          }
          i = k / 7;
        } else {
          CRYPTO = false;
          throw Error(bignumberError + "crypto unavailable");
        }
      }
      if (!CRYPTO) {
        for (; i < k; ) {
          v = random53bitInt();
          if (v < 9e15)
            c[i++] = v % 1e14;
        }
      }
      k = c[--i];
      dp %= LOG_BASE;
      if (k && dp) {
        v = POWS_TEN[LOG_BASE - dp];
        c[i] = mathfloor(k / v) * v;
      }
      for (; c[i] === 0; c.pop(), i--)
        ;
      if (i < 0) {
        c = [e2 = 0];
      } else {
        for (e2 = -1; c[0] === 0; c.splice(0, 1), e2 -= LOG_BASE)
          ;
        for (i = 1, v = c[0]; v >= 10; v /= 10, i++)
          ;
        if (i < LOG_BASE)
          e2 -= LOG_BASE - i;
      }
      rand.e = e2;
      rand.c = c;
      return rand;
    };
  }();
  BigNumber2.sum = function() {
    var i = 1, args = arguments, sum = new BigNumber2(args[0]);
    for (; i < args.length; )
      sum = sum.plus(args[i++]);
    return sum;
  };
  convertBase = /* @__PURE__ */ function() {
    var decimal = "0123456789";
    function toBaseOut(str, baseIn, baseOut, alphabet) {
      var j, arr = [0], arrL, i = 0, len = str.length;
      for (; i < len; ) {
        for (arrL = arr.length; arrL--; arr[arrL] *= baseIn)
          ;
        arr[0] += alphabet.indexOf(str.charAt(i++));
        for (j = 0; j < arr.length; j++) {
          if (arr[j] > baseOut - 1) {
            if (arr[j + 1] == null)
              arr[j + 1] = 0;
            arr[j + 1] += arr[j] / baseOut | 0;
            arr[j] %= baseOut;
          }
        }
      }
      return arr.reverse();
    }
    return function(str, baseIn, baseOut, sign, callerIsToString) {
      var alphabet, d, e2, k, r, x, xc, y, i = str.indexOf("."), dp = DECIMAL_PLACES, rm = ROUNDING_MODE;
      if (i >= 0) {
        k = POW_PRECISION;
        POW_PRECISION = 0;
        str = str.replace(".", "");
        y = new BigNumber2(baseIn);
        x = y.pow(str.length - i);
        POW_PRECISION = k;
        y.c = toBaseOut(
          toFixedPoint(coeffToString(x.c), x.e, "0"),
          10,
          baseOut,
          decimal
        );
        y.e = y.c.length;
      }
      xc = toBaseOut(str, baseIn, baseOut, callerIsToString ? (alphabet = ALPHABET, decimal) : (alphabet = decimal, ALPHABET));
      e2 = k = xc.length;
      for (; xc[--k] == 0; xc.pop())
        ;
      if (!xc[0])
        return alphabet.charAt(0);
      if (i < 0) {
        --e2;
      } else {
        x.c = xc;
        x.e = e2;
        x.s = sign;
        x = div(x, y, dp, rm, baseOut);
        xc = x.c;
        r = x.r;
        e2 = x.e;
      }
      d = e2 + dp + 1;
      i = xc[d];
      k = baseOut / 2;
      r = r || d < 0 || xc[d + 1] != null;
      r = rm < 4 ? (i != null || r) && (rm == 0 || rm == (x.s < 0 ? 3 : 2)) : i > k || i == k && (rm == 4 || r || rm == 6 && xc[d - 1] & 1 || rm == (x.s < 0 ? 8 : 7));
      if (d < 1 || !xc[0]) {
        str = r ? toFixedPoint(alphabet.charAt(1), -dp, alphabet.charAt(0)) : alphabet.charAt(0);
      } else {
        xc.length = d;
        if (r) {
          for (--baseOut; ++xc[--d] > baseOut; ) {
            xc[d] = 0;
            if (!d) {
              ++e2;
              xc = [1].concat(xc);
            }
          }
        }
        for (k = xc.length; !xc[--k]; )
          ;
        for (i = 0, str = ""; i <= k; str += alphabet.charAt(xc[i++]))
          ;
        str = toFixedPoint(str, e2, alphabet.charAt(0));
      }
      return str;
    };
  }();
  div = /* @__PURE__ */ function() {
    function multiply(x, k, base) {
      var m, temp, xlo, xhi, carry = 0, i = x.length, klo = k % SQRT_BASE, khi = k / SQRT_BASE | 0;
      for (x = x.slice(); i--; ) {
        xlo = x[i] % SQRT_BASE;
        xhi = x[i] / SQRT_BASE | 0;
        m = khi * xlo + xhi * klo;
        temp = klo * xlo + m % SQRT_BASE * SQRT_BASE + carry;
        carry = (temp / base | 0) + (m / SQRT_BASE | 0) + khi * xhi;
        x[i] = temp % base;
      }
      if (carry)
        x = [carry].concat(x);
      return x;
    }
    function compare2(a, b, aL, bL) {
      var i, cmp;
      if (aL != bL) {
        cmp = aL > bL ? 1 : -1;
      } else {
        for (i = cmp = 0; i < aL; i++) {
          if (a[i] != b[i]) {
            cmp = a[i] > b[i] ? 1 : -1;
            break;
          }
        }
      }
      return cmp;
    }
    function subtract(a, b, aL, base) {
      var i = 0;
      for (; aL--; ) {
        a[aL] -= i;
        i = a[aL] < b[aL] ? 1 : 0;
        a[aL] = i * base + a[aL] - b[aL];
      }
      for (; !a[0] && a.length > 1; a.splice(0, 1))
        ;
    }
    return function(x, y, dp, rm, base) {
      var cmp, e2, i, more, n, prod, prodL, q, qc, rem, remL, rem0, xi, xL, yc0, yL, yz, s = x.s == y.s ? 1 : -1, xc = x.c, yc = y.c;
      if (!xc || !xc[0] || !yc || !yc[0]) {
        return new BigNumber2(
          // Return NaN if either NaN, or both Infinity or 0.
          !x.s || !y.s || (xc ? yc && xc[0] == yc[0] : !yc) ? NaN : (
            // Return ±0 if x is ±0 or y is ±Infinity, or return ±Infinity as y is ±0.
            xc && xc[0] == 0 || !yc ? s * 0 : s / 0
          )
        );
      }
      q = new BigNumber2(s);
      qc = q.c = [];
      e2 = x.e - y.e;
      s = dp + e2 + 1;
      if (!base) {
        base = BASE;
        e2 = bitFloor(x.e / LOG_BASE) - bitFloor(y.e / LOG_BASE);
        s = s / LOG_BASE | 0;
      }
      for (i = 0; yc[i] == (xc[i] || 0); i++)
        ;
      if (yc[i] > (xc[i] || 0))
        e2--;
      if (s < 0) {
        qc.push(1);
        more = true;
      } else {
        xL = xc.length;
        yL = yc.length;
        i = 0;
        s += 2;
        n = mathfloor(base / (yc[0] + 1));
        if (n > 1) {
          yc = multiply(yc, n, base);
          xc = multiply(xc, n, base);
          yL = yc.length;
          xL = xc.length;
        }
        xi = yL;
        rem = xc.slice(0, yL);
        remL = rem.length;
        for (; remL < yL; rem[remL++] = 0)
          ;
        yz = yc.slice();
        yz = [0].concat(yz);
        yc0 = yc[0];
        if (yc[1] >= base / 2)
          yc0++;
        do {
          n = 0;
          cmp = compare2(yc, rem, yL, remL);
          if (cmp < 0) {
            rem0 = rem[0];
            if (yL != remL)
              rem0 = rem0 * base + (rem[1] || 0);
            n = mathfloor(rem0 / yc0);
            if (n > 1) {
              if (n >= base)
                n = base - 1;
              prod = multiply(yc, n, base);
              prodL = prod.length;
              remL = rem.length;
              while (compare2(prod, rem, prodL, remL) == 1) {
                n--;
                subtract(prod, yL < prodL ? yz : yc, prodL, base);
                prodL = prod.length;
                cmp = 1;
              }
            } else {
              if (n == 0) {
                cmp = n = 1;
              }
              prod = yc.slice();
              prodL = prod.length;
            }
            if (prodL < remL)
              prod = [0].concat(prod);
            subtract(rem, prod, remL, base);
            remL = rem.length;
            if (cmp == -1) {
              while (compare2(yc, rem, yL, remL) < 1) {
                n++;
                subtract(rem, yL < remL ? yz : yc, remL, base);
                remL = rem.length;
              }
            }
          } else if (cmp === 0) {
            n++;
            rem = [0];
          }
          qc[i++] = n;
          if (rem[0]) {
            rem[remL++] = xc[xi] || 0;
          } else {
            rem = [xc[xi]];
            remL = 1;
          }
        } while ((xi++ < xL || rem[0] != null) && s--);
        more = rem[0] != null;
        if (!qc[0])
          qc.splice(0, 1);
      }
      if (base == BASE) {
        for (i = 1, s = qc[0]; s >= 10; s /= 10, i++)
          ;
        round(q, dp + (q.e = i + e2 * LOG_BASE - 1) + 1, rm, more);
      } else {
        q.e = e2;
        q.r = +more;
      }
      return q;
    };
  }();
  function format(n, i, rm, id) {
    var c0, e2, ne, len, str;
    if (rm == null)
      rm = ROUNDING_MODE;
    else
      intCheck(rm, 0, 8);
    if (!n.c)
      return n.toString();
    c0 = n.c[0];
    ne = n.e;
    if (i == null) {
      str = coeffToString(n.c);
      str = id == 1 || id == 2 && (ne <= TO_EXP_NEG || ne >= TO_EXP_POS) ? toExponential(str, ne) : toFixedPoint(str, ne, "0");
    } else {
      n = round(new BigNumber2(n), i, rm);
      e2 = n.e;
      str = coeffToString(n.c);
      len = str.length;
      if (id == 1 || id == 2 && (i <= e2 || e2 <= TO_EXP_NEG)) {
        for (; len < i; str += "0", len++)
          ;
        str = toExponential(str, e2);
      } else {
        i -= ne;
        str = toFixedPoint(str, e2, "0");
        if (e2 + 1 > len) {
          if (--i > 0)
            for (str += "."; i--; str += "0")
              ;
        } else {
          i += e2 - len;
          if (i > 0) {
            if (e2 + 1 == len)
              str += ".";
            for (; i--; str += "0")
              ;
          }
        }
      }
    }
    return n.s < 0 && c0 ? "-" + str : str;
  }
  function maxOrMin(args, n) {
    var k, y, i = 1, x = new BigNumber2(args[0]);
    for (; i < args.length; i++) {
      y = new BigNumber2(args[i]);
      if (!y.s || (k = compare(x, y)) === n || k === 0 && x.s === n) {
        x = y;
      }
    }
    return x;
  }
  function normalise(n, c, e2) {
    var i = 1, j = c.length;
    for (; !c[--j]; c.pop())
      ;
    for (j = c[0]; j >= 10; j /= 10, i++)
      ;
    if ((e2 = i + e2 * LOG_BASE - 1) > MAX_EXP) {
      n.c = n.e = null;
    } else if (e2 < MIN_EXP) {
      n.c = [n.e = 0];
    } else {
      n.e = e2;
      n.c = c;
    }
    return n;
  }
  parseNumeric = /* @__PURE__ */ function() {
    var basePrefix = /^(-?)0([xbo])(?=\w[\w.]*$)/i, dotAfter = /^([^.]+)\.$/, dotBefore = /^\.([^.]+)$/, isInfinityOrNaN = /^-?(Infinity|NaN)$/, whitespaceOrPlus = /^\s*\+(?=[\w.])|^\s+|\s+$/g;
    return function(x, str, isNum, b) {
      var base, s = isNum ? str : str.replace(whitespaceOrPlus, "");
      if (isInfinityOrNaN.test(s)) {
        x.s = isNaN(s) ? null : s < 0 ? -1 : 1;
      } else {
        if (!isNum) {
          s = s.replace(basePrefix, function(m, p1, p2) {
            base = (p2 = p2.toLowerCase()) == "x" ? 16 : p2 == "b" ? 2 : 8;
            return !b || b == base ? p1 : m;
          });
          if (b) {
            base = b;
            s = s.replace(dotAfter, "$1").replace(dotBefore, "0.$1");
          }
          if (str != s)
            return new BigNumber2(s, base);
        }
        if (BigNumber2.DEBUG) {
          throw Error(bignumberError + "Not a" + (b ? " base " + b : "") + " number: " + str);
        }
        x.s = null;
      }
      x.c = x.e = null;
    };
  }();
  function round(x, sd, rm, r) {
    var d, i, j, k, n, ni, rd, xc = x.c, pows10 = POWS_TEN;
    if (xc) {
      out: {
        for (d = 1, k = xc[0]; k >= 10; k /= 10, d++)
          ;
        i = sd - d;
        if (i < 0) {
          i += LOG_BASE;
          j = sd;
          n = xc[ni = 0];
          rd = mathfloor(n / pows10[d - j - 1] % 10);
        } else {
          ni = mathceil((i + 1) / LOG_BASE);
          if (ni >= xc.length) {
            if (r) {
              for (; xc.length <= ni; xc.push(0))
                ;
              n = rd = 0;
              d = 1;
              i %= LOG_BASE;
              j = i - LOG_BASE + 1;
            } else {
              break out;
            }
          } else {
            n = k = xc[ni];
            for (d = 1; k >= 10; k /= 10, d++)
              ;
            i %= LOG_BASE;
            j = i - LOG_BASE + d;
            rd = j < 0 ? 0 : mathfloor(n / pows10[d - j - 1] % 10);
          }
        }
        r = r || sd < 0 || // Are there any non-zero digits after the rounding digit?
        // The expression  n % pows10[d - j - 1]  returns all digits of n to the right
        // of the digit at j, e.g. if n is 908714 and j is 2, the expression gives 714.
        xc[ni + 1] != null || (j < 0 ? n : n % pows10[d - j - 1]);
        r = rm < 4 ? (rd || r) && (rm == 0 || rm == (x.s < 0 ? 3 : 2)) : rd > 5 || rd == 5 && (rm == 4 || r || rm == 6 && // Check whether the digit to the left of the rounding digit is odd.
        (i > 0 ? j > 0 ? n / pows10[d - j] : 0 : xc[ni - 1]) % 10 & 1 || rm == (x.s < 0 ? 8 : 7));
        if (sd < 1 || !xc[0]) {
          xc.length = 0;
          if (r) {
            sd -= x.e + 1;
            xc[0] = pows10[(LOG_BASE - sd % LOG_BASE) % LOG_BASE];
            x.e = -sd || 0;
          } else {
            xc[0] = x.e = 0;
          }
          return x;
        }
        if (i == 0) {
          xc.length = ni;
          k = 1;
          ni--;
        } else {
          xc.length = ni + 1;
          k = pows10[LOG_BASE - i];
          xc[ni] = j > 0 ? mathfloor(n / pows10[d - j] % pows10[j]) * k : 0;
        }
        if (r) {
          for (; ; ) {
            if (ni == 0) {
              for (i = 1, j = xc[0]; j >= 10; j /= 10, i++)
                ;
              j = xc[0] += k;
              for (k = 1; j >= 10; j /= 10, k++)
                ;
              if (i != k) {
                x.e++;
                if (xc[0] == BASE)
                  xc[0] = 1;
              }
              break;
            } else {
              xc[ni] += k;
              if (xc[ni] != BASE)
                break;
              xc[ni--] = 0;
              k = 1;
            }
          }
        }
        for (i = xc.length; xc[--i] === 0; xc.pop())
          ;
      }
      if (x.e > MAX_EXP) {
        x.c = x.e = null;
      } else if (x.e < MIN_EXP) {
        x.c = [x.e = 0];
      }
    }
    return x;
  }
  function valueOf(n) {
    var str, e2 = n.e;
    if (e2 === null)
      return n.toString();
    str = coeffToString(n.c);
    str = e2 <= TO_EXP_NEG || e2 >= TO_EXP_POS ? toExponential(str, e2) : toFixedPoint(str, e2, "0");
    return n.s < 0 ? "-" + str : str;
  }
  P.absoluteValue = P.abs = function() {
    var x = new BigNumber2(this);
    if (x.s < 0)
      x.s = 1;
    return x;
  };
  P.comparedTo = function(y, b) {
    return compare(this, new BigNumber2(y, b));
  };
  P.decimalPlaces = P.dp = function(dp, rm) {
    var c, n, v, x = this;
    if (dp != null) {
      intCheck(dp, 0, MAX);
      if (rm == null)
        rm = ROUNDING_MODE;
      else
        intCheck(rm, 0, 8);
      return round(new BigNumber2(x), dp + x.e + 1, rm);
    }
    if (!(c = x.c))
      return null;
    n = ((v = c.length - 1) - bitFloor(this.e / LOG_BASE)) * LOG_BASE;
    if (v = c[v])
      for (; v % 10 == 0; v /= 10, n--)
        ;
    if (n < 0)
      n = 0;
    return n;
  };
  P.dividedBy = P.div = function(y, b) {
    return div(this, new BigNumber2(y, b), DECIMAL_PLACES, ROUNDING_MODE);
  };
  P.dividedToIntegerBy = P.idiv = function(y, b) {
    return div(this, new BigNumber2(y, b), 0, 1);
  };
  P.exponentiatedBy = P.pow = function(n, m) {
    var half, isModExp, i, k, more, nIsBig, nIsNeg, nIsOdd, y, x = this;
    n = new BigNumber2(n);
    if (n.c && !n.isInteger()) {
      throw Error(bignumberError + "Exponent not an integer: " + valueOf(n));
    }
    if (m != null)
      m = new BigNumber2(m);
    nIsBig = n.e > 14;
    if (!x.c || !x.c[0] || x.c[0] == 1 && !x.e && x.c.length == 1 || !n.c || !n.c[0]) {
      y = new BigNumber2(Math.pow(+valueOf(x), nIsBig ? n.s * (2 - isOdd(n)) : +valueOf(n)));
      return m ? y.mod(m) : y;
    }
    nIsNeg = n.s < 0;
    if (m) {
      if (m.c ? !m.c[0] : !m.s)
        return new BigNumber2(NaN);
      isModExp = !nIsNeg && x.isInteger() && m.isInteger();
      if (isModExp)
        x = x.mod(m);
    } else if (n.e > 9 && (x.e > 0 || x.e < -1 || (x.e == 0 ? x.c[0] > 1 || nIsBig && x.c[1] >= 24e7 : x.c[0] < 8e13 || nIsBig && x.c[0] <= 9999975e7))) {
      k = x.s < 0 && isOdd(n) ? -0 : 0;
      if (x.e > -1)
        k = 1 / k;
      return new BigNumber2(nIsNeg ? 1 / k : k);
    } else if (POW_PRECISION) {
      k = mathceil(POW_PRECISION / LOG_BASE + 2);
    }
    if (nIsBig) {
      half = new BigNumber2(0.5);
      if (nIsNeg)
        n.s = 1;
      nIsOdd = isOdd(n);
    } else {
      i = Math.abs(+valueOf(n));
      nIsOdd = i % 2;
    }
    y = new BigNumber2(ONE);
    for (; ; ) {
      if (nIsOdd) {
        y = y.times(x);
        if (!y.c)
          break;
        if (k) {
          if (y.c.length > k)
            y.c.length = k;
        } else if (isModExp) {
          y = y.mod(m);
        }
      }
      if (i) {
        i = mathfloor(i / 2);
        if (i === 0)
          break;
        nIsOdd = i % 2;
      } else {
        n = n.times(half);
        round(n, n.e + 1, 1);
        if (n.e > 14) {
          nIsOdd = isOdd(n);
        } else {
          i = +valueOf(n);
          if (i === 0)
            break;
          nIsOdd = i % 2;
        }
      }
      x = x.times(x);
      if (k) {
        if (x.c && x.c.length > k)
          x.c.length = k;
      } else if (isModExp) {
        x = x.mod(m);
      }
    }
    if (isModExp)
      return y;
    if (nIsNeg)
      y = ONE.div(y);
    return m ? y.mod(m) : k ? round(y, POW_PRECISION, ROUNDING_MODE, more) : y;
  };
  P.integerValue = function(rm) {
    var n = new BigNumber2(this);
    if (rm == null)
      rm = ROUNDING_MODE;
    else
      intCheck(rm, 0, 8);
    return round(n, n.e + 1, rm);
  };
  P.isEqualTo = P.eq = function(y, b) {
    return compare(this, new BigNumber2(y, b)) === 0;
  };
  P.isFinite = function() {
    return !!this.c;
  };
  P.isGreaterThan = P.gt = function(y, b) {
    return compare(this, new BigNumber2(y, b)) > 0;
  };
  P.isGreaterThanOrEqualTo = P.gte = function(y, b) {
    return (b = compare(this, new BigNumber2(y, b))) === 1 || b === 0;
  };
  P.isInteger = function() {
    return !!this.c && bitFloor(this.e / LOG_BASE) > this.c.length - 2;
  };
  P.isLessThan = P.lt = function(y, b) {
    return compare(this, new BigNumber2(y, b)) < 0;
  };
  P.isLessThanOrEqualTo = P.lte = function(y, b) {
    return (b = compare(this, new BigNumber2(y, b))) === -1 || b === 0;
  };
  P.isNaN = function() {
    return !this.s;
  };
  P.isNegative = function() {
    return this.s < 0;
  };
  P.isPositive = function() {
    return this.s > 0;
  };
  P.isZero = function() {
    return !!this.c && this.c[0] == 0;
  };
  P.minus = function(y, b) {
    var i, j, t, xLTy, x = this, a = x.s;
    y = new BigNumber2(y, b);
    b = y.s;
    if (!a || !b)
      return new BigNumber2(NaN);
    if (a != b) {
      y.s = -b;
      return x.plus(y);
    }
    var xe = x.e / LOG_BASE, ye = y.e / LOG_BASE, xc = x.c, yc = y.c;
    if (!xe || !ye) {
      if (!xc || !yc)
        return xc ? (y.s = -b, y) : new BigNumber2(yc ? x : NaN);
      if (!xc[0] || !yc[0]) {
        return yc[0] ? (y.s = -b, y) : new BigNumber2(xc[0] ? x : (
          // IEEE 754 (2008) 6.3: n - n = -0 when rounding to -Infinity
          ROUNDING_MODE == 3 ? -0 : 0
        ));
      }
    }
    xe = bitFloor(xe);
    ye = bitFloor(ye);
    xc = xc.slice();
    if (a = xe - ye) {
      if (xLTy = a < 0) {
        a = -a;
        t = xc;
      } else {
        ye = xe;
        t = yc;
      }
      t.reverse();
      for (b = a; b--; t.push(0))
        ;
      t.reverse();
    } else {
      j = (xLTy = (a = xc.length) < (b = yc.length)) ? a : b;
      for (a = b = 0; b < j; b++) {
        if (xc[b] != yc[b]) {
          xLTy = xc[b] < yc[b];
          break;
        }
      }
    }
    if (xLTy) {
      t = xc;
      xc = yc;
      yc = t;
      y.s = -y.s;
    }
    b = (j = yc.length) - (i = xc.length);
    if (b > 0)
      for (; b--; xc[i++] = 0)
        ;
    b = BASE - 1;
    for (; j > a; ) {
      if (xc[--j] < yc[j]) {
        for (i = j; i && !xc[--i]; xc[i] = b)
          ;
        --xc[i];
        xc[j] += BASE;
      }
      xc[j] -= yc[j];
    }
    for (; xc[0] == 0; xc.splice(0, 1), --ye)
      ;
    if (!xc[0]) {
      y.s = ROUNDING_MODE == 3 ? -1 : 1;
      y.c = [y.e = 0];
      return y;
    }
    return normalise(y, xc, ye);
  };
  P.modulo = P.mod = function(y, b) {
    var q, s, x = this;
    y = new BigNumber2(y, b);
    if (!x.c || !y.s || y.c && !y.c[0]) {
      return new BigNumber2(NaN);
    } else if (!y.c || x.c && !x.c[0]) {
      return new BigNumber2(x);
    }
    if (MODULO_MODE == 9) {
      s = y.s;
      y.s = 1;
      q = div(x, y, 0, 3);
      y.s = s;
      q.s *= s;
    } else {
      q = div(x, y, 0, MODULO_MODE);
    }
    y = x.minus(q.times(y));
    if (!y.c[0] && MODULO_MODE == 1)
      y.s = x.s;
    return y;
  };
  P.multipliedBy = P.times = function(y, b) {
    var c, e2, i, j, k, m, xcL, xlo, xhi, ycL, ylo, yhi, zc, base, sqrtBase, x = this, xc = x.c, yc = (y = new BigNumber2(y, b)).c;
    if (!xc || !yc || !xc[0] || !yc[0]) {
      if (!x.s || !y.s || xc && !xc[0] && !yc || yc && !yc[0] && !xc) {
        y.c = y.e = y.s = null;
      } else {
        y.s *= x.s;
        if (!xc || !yc) {
          y.c = y.e = null;
        } else {
          y.c = [0];
          y.e = 0;
        }
      }
      return y;
    }
    e2 = bitFloor(x.e / LOG_BASE) + bitFloor(y.e / LOG_BASE);
    y.s *= x.s;
    xcL = xc.length;
    ycL = yc.length;
    if (xcL < ycL) {
      zc = xc;
      xc = yc;
      yc = zc;
      i = xcL;
      xcL = ycL;
      ycL = i;
    }
    for (i = xcL + ycL, zc = []; i--; zc.push(0))
      ;
    base = BASE;
    sqrtBase = SQRT_BASE;
    for (i = ycL; --i >= 0; ) {
      c = 0;
      ylo = yc[i] % sqrtBase;
      yhi = yc[i] / sqrtBase | 0;
      for (k = xcL, j = i + k; j > i; ) {
        xlo = xc[--k] % sqrtBase;
        xhi = xc[k] / sqrtBase | 0;
        m = yhi * xlo + xhi * ylo;
        xlo = ylo * xlo + m % sqrtBase * sqrtBase + zc[j] + c;
        c = (xlo / base | 0) + (m / sqrtBase | 0) + yhi * xhi;
        zc[j--] = xlo % base;
      }
      zc[j] = c;
    }
    if (c) {
      ++e2;
    } else {
      zc.splice(0, 1);
    }
    return normalise(y, zc, e2);
  };
  P.negated = function() {
    var x = new BigNumber2(this);
    x.s = -x.s || null;
    return x;
  };
  P.plus = function(y, b) {
    var t, x = this, a = x.s;
    y = new BigNumber2(y, b);
    b = y.s;
    if (!a || !b)
      return new BigNumber2(NaN);
    if (a != b) {
      y.s = -b;
      return x.minus(y);
    }
    var xe = x.e / LOG_BASE, ye = y.e / LOG_BASE, xc = x.c, yc = y.c;
    if (!xe || !ye) {
      if (!xc || !yc)
        return new BigNumber2(a / 0);
      if (!xc[0] || !yc[0])
        return yc[0] ? y : new BigNumber2(xc[0] ? x : a * 0);
    }
    xe = bitFloor(xe);
    ye = bitFloor(ye);
    xc = xc.slice();
    if (a = xe - ye) {
      if (a > 0) {
        ye = xe;
        t = yc;
      } else {
        a = -a;
        t = xc;
      }
      t.reverse();
      for (; a--; t.push(0))
        ;
      t.reverse();
    }
    a = xc.length;
    b = yc.length;
    if (a - b < 0) {
      t = yc;
      yc = xc;
      xc = t;
      b = a;
    }
    for (a = 0; b; ) {
      a = (xc[--b] = xc[b] + yc[b] + a) / BASE | 0;
      xc[b] = BASE === xc[b] ? 0 : xc[b] % BASE;
    }
    if (a) {
      xc = [a].concat(xc);
      ++ye;
    }
    return normalise(y, xc, ye);
  };
  P.precision = P.sd = function(sd, rm) {
    var c, n, v, x = this;
    if (sd != null && sd !== !!sd) {
      intCheck(sd, 1, MAX);
      if (rm == null)
        rm = ROUNDING_MODE;
      else
        intCheck(rm, 0, 8);
      return round(new BigNumber2(x), sd, rm);
    }
    if (!(c = x.c))
      return null;
    v = c.length - 1;
    n = v * LOG_BASE + 1;
    if (v = c[v]) {
      for (; v % 10 == 0; v /= 10, n--)
        ;
      for (v = c[0]; v >= 10; v /= 10, n++)
        ;
    }
    if (sd && x.e + 1 > n)
      n = x.e + 1;
    return n;
  };
  P.shiftedBy = function(k) {
    intCheck(k, -MAX_SAFE_INTEGER, MAX_SAFE_INTEGER);
    return this.times("1e" + k);
  };
  P.squareRoot = P.sqrt = function() {
    var m, n, r, rep, t, x = this, c = x.c, s = x.s, e2 = x.e, dp = DECIMAL_PLACES + 4, half = new BigNumber2("0.5");
    if (s !== 1 || !c || !c[0]) {
      return new BigNumber2(!s || s < 0 && (!c || c[0]) ? NaN : c ? x : 1 / 0);
    }
    s = Math.sqrt(+valueOf(x));
    if (s == 0 || s == 1 / 0) {
      n = coeffToString(c);
      if ((n.length + e2) % 2 == 0)
        n += "0";
      s = Math.sqrt(+n);
      e2 = bitFloor((e2 + 1) / 2) - (e2 < 0 || e2 % 2);
      if (s == 1 / 0) {
        n = "5e" + e2;
      } else {
        n = s.toExponential();
        n = n.slice(0, n.indexOf("e") + 1) + e2;
      }
      r = new BigNumber2(n);
    } else {
      r = new BigNumber2(s + "");
    }
    if (r.c[0]) {
      e2 = r.e;
      s = e2 + dp;
      if (s < 3)
        s = 0;
      for (; ; ) {
        t = r;
        r = half.times(t.plus(div(x, t, dp, 1)));
        if (coeffToString(t.c).slice(0, s) === (n = coeffToString(r.c)).slice(0, s)) {
          if (r.e < e2)
            --s;
          n = n.slice(s - 3, s + 1);
          if (n == "9999" || !rep && n == "4999") {
            if (!rep) {
              round(t, t.e + DECIMAL_PLACES + 2, 0);
              if (t.times(t).eq(x)) {
                r = t;
                break;
              }
            }
            dp += 4;
            s += 4;
            rep = 1;
          } else {
            if (!+n || !+n.slice(1) && n.charAt(0) == "5") {
              round(r, r.e + DECIMAL_PLACES + 2, 1);
              m = !r.times(r).eq(x);
            }
            break;
          }
        }
      }
    }
    return round(r, r.e + DECIMAL_PLACES + 1, ROUNDING_MODE, m);
  };
  P.toExponential = function(dp, rm) {
    if (dp != null) {
      intCheck(dp, 0, MAX);
      dp++;
    }
    return format(this, dp, rm, 1);
  };
  P.toFixed = function(dp, rm) {
    if (dp != null) {
      intCheck(dp, 0, MAX);
      dp = dp + this.e + 1;
    }
    return format(this, dp, rm);
  };
  P.toFormat = function(dp, rm, format2) {
    var str, x = this;
    if (format2 == null) {
      if (dp != null && rm && typeof rm == "object") {
        format2 = rm;
        rm = null;
      } else if (dp && typeof dp == "object") {
        format2 = dp;
        dp = rm = null;
      } else {
        format2 = FORMAT;
      }
    } else if (typeof format2 != "object") {
      throw Error(bignumberError + "Argument not an object: " + format2);
    }
    str = x.toFixed(dp, rm);
    if (x.c) {
      var i, arr = str.split("."), g1 = +format2.groupSize, g2 = +format2.secondaryGroupSize, groupSeparator = format2.groupSeparator || "", intPart = arr[0], fractionPart = arr[1], isNeg = x.s < 0, intDigits = isNeg ? intPart.slice(1) : intPart, len = intDigits.length;
      if (g2) {
        i = g1;
        g1 = g2;
        g2 = i;
        len -= i;
      }
      if (g1 > 0 && len > 0) {
        i = len % g1 || g1;
        intPart = intDigits.substr(0, i);
        for (; i < len; i += g1)
          intPart += groupSeparator + intDigits.substr(i, g1);
        if (g2 > 0)
          intPart += groupSeparator + intDigits.slice(i);
        if (isNeg)
          intPart = "-" + intPart;
      }
      str = fractionPart ? intPart + (format2.decimalSeparator || "") + ((g2 = +format2.fractionGroupSize) ? fractionPart.replace(
        new RegExp("\\d{" + g2 + "}\\B", "g"),
        "$&" + (format2.fractionGroupSeparator || "")
      ) : fractionPart) : intPart;
    }
    return (format2.prefix || "") + str + (format2.suffix || "");
  };
  P.toFraction = function(md) {
    var d, d0, d1, d2, e2, exp, n, n0, n1, q, r, s, x = this, xc = x.c;
    if (md != null) {
      n = new BigNumber2(md);
      if (!n.isInteger() && (n.c || n.s !== 1) || n.lt(ONE)) {
        throw Error(bignumberError + "Argument " + (n.isInteger() ? "out of range: " : "not an integer: ") + valueOf(n));
      }
    }
    if (!xc)
      return new BigNumber2(x);
    d = new BigNumber2(ONE);
    n1 = d0 = new BigNumber2(ONE);
    d1 = n0 = new BigNumber2(ONE);
    s = coeffToString(xc);
    e2 = d.e = s.length - x.e - 1;
    d.c[0] = POWS_TEN[(exp = e2 % LOG_BASE) < 0 ? LOG_BASE + exp : exp];
    md = !md || n.comparedTo(d) > 0 ? e2 > 0 ? d : n1 : n;
    exp = MAX_EXP;
    MAX_EXP = 1 / 0;
    n = new BigNumber2(s);
    n0.c[0] = 0;
    for (; ; ) {
      q = div(n, d, 0, 1);
      d2 = d0.plus(q.times(d1));
      if (d2.comparedTo(md) == 1)
        break;
      d0 = d1;
      d1 = d2;
      n1 = n0.plus(q.times(d2 = n1));
      n0 = d2;
      d = n.minus(q.times(d2 = d));
      n = d2;
    }
    d2 = div(md.minus(d0), d1, 0, 1);
    n0 = n0.plus(d2.times(n1));
    d0 = d0.plus(d2.times(d1));
    n0.s = n1.s = x.s;
    e2 = e2 * 2;
    r = div(n1, d1, e2, ROUNDING_MODE).minus(x).abs().comparedTo(
      div(n0, d0, e2, ROUNDING_MODE).minus(x).abs()
    ) < 1 ? [n1, d1] : [n0, d0];
    MAX_EXP = exp;
    return r;
  };
  P.toNumber = function() {
    return +valueOf(this);
  };
  P.toPrecision = function(sd, rm) {
    if (sd != null)
      intCheck(sd, 1, MAX);
    return format(this, sd, rm, 2);
  };
  P.toString = function(b) {
    var str, n = this, s = n.s, e2 = n.e;
    if (e2 === null) {
      if (s) {
        str = "Infinity";
        if (s < 0)
          str = "-" + str;
      } else {
        str = "NaN";
      }
    } else {
      if (b == null) {
        str = e2 <= TO_EXP_NEG || e2 >= TO_EXP_POS ? toExponential(coeffToString(n.c), e2) : toFixedPoint(coeffToString(n.c), e2, "0");
      } else if (b === 10 && alphabetHasNormalDecimalDigits) {
        n = round(new BigNumber2(n), DECIMAL_PLACES + e2 + 1, ROUNDING_MODE);
        str = toFixedPoint(coeffToString(n.c), n.e, "0");
      } else {
        intCheck(b, 2, ALPHABET.length, "Base");
        str = convertBase(toFixedPoint(coeffToString(n.c), e2, "0"), 10, b, s, true);
      }
      if (s < 0 && n.c[0])
        str = "-" + str;
    }
    return str;
  };
  P.valueOf = P.toJSON = function() {
    return valueOf(this);
  };
  P._isBigNumber = true;
  P[Symbol.toStringTag] = "BigNumber";
  P[Symbol.for("nodejs.util.inspect.custom")] = P.valueOf;
  if (configObject != null)
    BigNumber2.set(configObject);
  return BigNumber2;
}
function bitFloor(n) {
  var i = n | 0;
  return n > 0 || n === i ? i : i - 1;
}
function coeffToString(a) {
  var s, z, i = 1, j = a.length, r = a[0] + "";
  for (; i < j; ) {
    s = a[i++] + "";
    z = LOG_BASE - s.length;
    for (; z--; s = "0" + s)
      ;
    r += s;
  }
  for (j = r.length; r.charCodeAt(--j) === 48; )
    ;
  return r.slice(0, j + 1 || 1);
}
function compare(x, y) {
  var a, b, xc = x.c, yc = y.c, i = x.s, j = y.s, k = x.e, l = y.e;
  if (!i || !j)
    return null;
  a = xc && !xc[0];
  b = yc && !yc[0];
  if (a || b)
    return a ? b ? 0 : -j : i;
  if (i != j)
    return i;
  a = i < 0;
  b = k == l;
  if (!xc || !yc)
    return b ? 0 : !xc ^ a ? 1 : -1;
  if (!b)
    return k > l ^ a ? 1 : -1;
  j = (k = xc.length) < (l = yc.length) ? k : l;
  for (i = 0; i < j; i++)
    if (xc[i] != yc[i])
      return xc[i] > yc[i] ^ a ? 1 : -1;
  return k == l ? 0 : k > l ^ a ? 1 : -1;
}
function intCheck(n, min, max, name) {
  if (n < min || n > max || n !== mathfloor(n)) {
    throw Error(bignumberError + (name || "Argument") + (typeof n == "number" ? n < min || n > max ? " out of range: " : " not an integer: " : " not a primitive number: ") + String(n));
  }
}
function isOdd(n) {
  var k = n.c.length - 1;
  return bitFloor(n.e / LOG_BASE) == k && n.c[k] % 2 != 0;
}
function toExponential(str, e2) {
  return (str.length > 1 ? str.charAt(0) + "." + str.slice(1) : str) + (e2 < 0 ? "e" : "e+") + e2;
}
function toFixedPoint(str, e2, z) {
  var len, zs;
  if (e2 < 0) {
    for (zs = z + "."; ++e2; zs += z)
      ;
    str = zs + str;
  } else {
    len = str.length;
    if (++e2 > len) {
      for (zs = z, e2 -= len; --e2; zs += z)
        ;
      str += zs;
    } else if (e2 < len) {
      str = str.slice(0, e2) + "." + str.slice(e2);
    }
  }
  return str;
}
var BigNumber = clone();
var bignumber_default = BigNumber;

// node_modules/@solana/buffer-layout-utils/lib/esm/decimal.mjs
var WAD = new bignumber_default("1e+18");

// node_modules/@solana/buffer-layout-utils/lib/esm/native.mjs
var import_buffer_layout2 = __toESM(require_Layout(), 1);
var bool = (property) => {
  const layout = (0, import_buffer_layout2.u8)(property);
  const { encode, decode } = encodeDecode(layout);
  const boolLayout = layout;
  boolLayout.decode = (buffer, offset) => {
    const src = decode(buffer, offset);
    return !!src;
  };
  boolLayout.encode = (bool2, buffer, offset) => {
    const src = Number(bool2);
    return encode(src, buffer, offset);
  };
  return boolLayout;
};

// node_modules/@solana/buffer-layout-utils/lib/esm/web3.mjs
var import_buffer_layout3 = __toESM(require_Layout(), 1);
init_index_browser_esm();
var publicKey = (property) => {
  const layout = (0, import_buffer_layout3.blob)(32, property);
  const { encode, decode } = encodeDecode(layout);
  const publicKeyLayout = layout;
  publicKeyLayout.decode = (buffer, offset) => {
    const src = decode(buffer, offset);
    return new PublicKey(src);
  };
  publicKeyLayout.encode = (publicKey2, buffer, offset) => {
    const src = publicKey2.toBuffer();
    return encode(src, buffer, offset);
  };
  return publicKeyLayout;
};

// node_modules/@solana/spl-token/lib/esm/instructions/amountToUiAmount.js
init_index_browser_esm();

// node_modules/@solana/spl-token/lib/esm/errors.js
var TokenError = class extends Error {
  constructor(message) {
    super(message);
  }
};
var TokenAccountNotFoundError = class extends TokenError {
  constructor() {
    super(...arguments);
    this.name = "TokenAccountNotFoundError";
  }
};
var TokenInvalidAccountError = class extends TokenError {
  constructor() {
    super(...arguments);
    this.name = "TokenInvalidAccountError";
  }
};
var TokenInvalidAccountDataError = class extends TokenError {
  constructor() {
    super(...arguments);
    this.name = "TokenInvalidAccountDataError";
  }
};
var TokenInvalidAccountOwnerError = class extends TokenError {
  constructor() {
    super(...arguments);
    this.name = "TokenInvalidAccountOwnerError";
  }
};
var TokenInvalidAccountSizeError = class extends TokenError {
  constructor() {
    super(...arguments);
    this.name = "TokenInvalidAccountSizeError";
  }
};
var TokenInvalidMintError = class extends TokenError {
  constructor() {
    super(...arguments);
    this.name = "TokenInvalidMintError";
  }
};
var TokenInvalidOwnerError = class extends TokenError {
  constructor() {
    super(...arguments);
    this.name = "TokenInvalidOwnerError";
  }
};
var TokenOwnerOffCurveError = class extends TokenError {
  constructor() {
    super(...arguments);
    this.name = "TokenOwnerOffCurveError";
  }
};
var TokenInvalidInstructionProgramError = class extends TokenError {
  constructor() {
    super(...arguments);
    this.name = "TokenInvalidInstructionProgramError";
  }
};
var TokenInvalidInstructionKeysError = class extends TokenError {
  constructor() {
    super(...arguments);
    this.name = "TokenInvalidInstructionKeysError";
  }
};
var TokenInvalidInstructionDataError = class extends TokenError {
  constructor() {
    super(...arguments);
    this.name = "TokenInvalidInstructionDataError";
  }
};
var TokenInvalidInstructionTypeError = class extends TokenError {
  constructor() {
    super(...arguments);
    this.name = "TokenInvalidInstructionTypeError";
  }
};
var TokenUnsupportedInstructionError = class extends TokenError {
  constructor() {
    super(...arguments);
    this.name = "TokenUnsupportedInstructionError";
  }
};
var TokenTransferHookAccountNotFound = class extends TokenError {
  constructor() {
    super(...arguments);
    this.name = "TokenTransferHookAccountNotFound";
  }
};
var TokenTransferHookInvalidSeed = class extends TokenError {
  constructor() {
    super(...arguments);
    this.name = "TokenTransferHookInvalidSeed";
  }
};
var TokenTransferHookAccountDataNotFound = class extends TokenError {
  constructor() {
    super(...arguments);
    this.name = "TokenTransferHookAccountDataNotFound";
  }
};
var TokenTransferHookInvalidPubkeyData = class extends TokenError {
  constructor() {
    super(...arguments);
    this.name = "TokenTransferHookInvalidPubkeyData";
  }
};
var TokenTransferHookPubkeyDataTooSmall = class extends TokenError {
  constructor() {
    super(...arguments);
    this.name = "TokenTransferHookPubkeyDataTooSmall";
  }
};

// node_modules/@solana/spl-token/lib/esm/instructions/types.js
var TokenInstruction;
(function(TokenInstruction2) {
  TokenInstruction2[TokenInstruction2["InitializeMint"] = 0] = "InitializeMint";
  TokenInstruction2[TokenInstruction2["InitializeAccount"] = 1] = "InitializeAccount";
  TokenInstruction2[TokenInstruction2["InitializeMultisig"] = 2] = "InitializeMultisig";
  TokenInstruction2[TokenInstruction2["Transfer"] = 3] = "Transfer";
  TokenInstruction2[TokenInstruction2["Approve"] = 4] = "Approve";
  TokenInstruction2[TokenInstruction2["Revoke"] = 5] = "Revoke";
  TokenInstruction2[TokenInstruction2["SetAuthority"] = 6] = "SetAuthority";
  TokenInstruction2[TokenInstruction2["MintTo"] = 7] = "MintTo";
  TokenInstruction2[TokenInstruction2["Burn"] = 8] = "Burn";
  TokenInstruction2[TokenInstruction2["CloseAccount"] = 9] = "CloseAccount";
  TokenInstruction2[TokenInstruction2["FreezeAccount"] = 10] = "FreezeAccount";
  TokenInstruction2[TokenInstruction2["ThawAccount"] = 11] = "ThawAccount";
  TokenInstruction2[TokenInstruction2["TransferChecked"] = 12] = "TransferChecked";
  TokenInstruction2[TokenInstruction2["ApproveChecked"] = 13] = "ApproveChecked";
  TokenInstruction2[TokenInstruction2["MintToChecked"] = 14] = "MintToChecked";
  TokenInstruction2[TokenInstruction2["BurnChecked"] = 15] = "BurnChecked";
  TokenInstruction2[TokenInstruction2["InitializeAccount2"] = 16] = "InitializeAccount2";
  TokenInstruction2[TokenInstruction2["SyncNative"] = 17] = "SyncNative";
  TokenInstruction2[TokenInstruction2["InitializeAccount3"] = 18] = "InitializeAccount3";
  TokenInstruction2[TokenInstruction2["InitializeMultisig2"] = 19] = "InitializeMultisig2";
  TokenInstruction2[TokenInstruction2["InitializeMint2"] = 20] = "InitializeMint2";
  TokenInstruction2[TokenInstruction2["GetAccountDataSize"] = 21] = "GetAccountDataSize";
  TokenInstruction2[TokenInstruction2["InitializeImmutableOwner"] = 22] = "InitializeImmutableOwner";
  TokenInstruction2[TokenInstruction2["AmountToUiAmount"] = 23] = "AmountToUiAmount";
  TokenInstruction2[TokenInstruction2["UiAmountToAmount"] = 24] = "UiAmountToAmount";
  TokenInstruction2[TokenInstruction2["InitializeMintCloseAuthority"] = 25] = "InitializeMintCloseAuthority";
  TokenInstruction2[TokenInstruction2["TransferFeeExtension"] = 26] = "TransferFeeExtension";
  TokenInstruction2[TokenInstruction2["ConfidentialTransferExtension"] = 27] = "ConfidentialTransferExtension";
  TokenInstruction2[TokenInstruction2["DefaultAccountStateExtension"] = 28] = "DefaultAccountStateExtension";
  TokenInstruction2[TokenInstruction2["Reallocate"] = 29] = "Reallocate";
  TokenInstruction2[TokenInstruction2["MemoTransferExtension"] = 30] = "MemoTransferExtension";
  TokenInstruction2[TokenInstruction2["CreateNativeMint"] = 31] = "CreateNativeMint";
  TokenInstruction2[TokenInstruction2["InitializeNonTransferableMint"] = 32] = "InitializeNonTransferableMint";
  TokenInstruction2[TokenInstruction2["InterestBearingMintExtension"] = 33] = "InterestBearingMintExtension";
  TokenInstruction2[TokenInstruction2["CpiGuardExtension"] = 34] = "CpiGuardExtension";
  TokenInstruction2[TokenInstruction2["InitializePermanentDelegate"] = 35] = "InitializePermanentDelegate";
  TokenInstruction2[TokenInstruction2["TransferHookExtension"] = 36] = "TransferHookExtension";
  TokenInstruction2[TokenInstruction2["MetadataPointerExtension"] = 39] = "MetadataPointerExtension";
  TokenInstruction2[TokenInstruction2["GroupPointerExtension"] = 40] = "GroupPointerExtension";
  TokenInstruction2[TokenInstruction2["GroupMemberPointerExtension"] = 41] = "GroupMemberPointerExtension";
  TokenInstruction2[TokenInstruction2["ScaledUiAmountExtension"] = 43] = "ScaledUiAmountExtension";
  TokenInstruction2[TokenInstruction2["PausableExtension"] = 44] = "PausableExtension";
})(TokenInstruction || (TokenInstruction = {}));

// node_modules/@solana/spl-token/lib/esm/instructions/amountToUiAmount.js
var amountToUiAmountInstructionData = (0, import_buffer_layout4.struct)([
  (0, import_buffer_layout4.u8)("instruction"),
  u64("amount")
]);
function createAmountToUiAmountInstruction(mint, amount, programId = TOKEN_PROGRAM_ID) {
  const keys = [{ pubkey: mint, isSigner: false, isWritable: false }];
  const data = Buffer.alloc(amountToUiAmountInstructionData.span);
  amountToUiAmountInstructionData.encode({
    instruction: TokenInstruction.AmountToUiAmount,
    amount: BigInt(amount)
  }, data);
  return new TransactionInstruction({ keys, programId, data });
}
function decodeAmountToUiAmountInstruction(instruction, programId = TOKEN_PROGRAM_ID) {
  if (!instruction.programId.equals(programId))
    throw new TokenInvalidInstructionProgramError();
  if (instruction.data.length !== amountToUiAmountInstructionData.span)
    throw new TokenInvalidInstructionDataError();
  const { keys: { mint }, data } = decodeAmountToUiAmountInstructionUnchecked(instruction);
  if (data.instruction !== TokenInstruction.AmountToUiAmount)
    throw new TokenInvalidInstructionTypeError();
  if (!mint)
    throw new TokenInvalidInstructionKeysError();
  return {
    programId,
    keys: {
      mint
    },
    data
  };
}
function decodeAmountToUiAmountInstructionUnchecked({ programId, keys: [mint], data }) {
  return {
    programId,
    keys: {
      mint
    },
    data: amountToUiAmountInstructionData.decode(data)
  };
}

// node_modules/@solana/spl-token/lib/esm/state/mint.js
var import_buffer_layout31 = __toESM(require_Layout(), 1);
init_index_browser_esm();

// node_modules/@solana/spl-token/lib/esm/extensions/accountType.js
var AccountType;
(function(AccountType2) {
  AccountType2[AccountType2["Uninitialized"] = 0] = "Uninitialized";
  AccountType2[AccountType2["Mint"] = 1] = "Mint";
  AccountType2[AccountType2["Account"] = 2] = "Account";
})(AccountType || (AccountType = {}));
var ACCOUNT_TYPE_SIZE = 1;

// node_modules/@solana/spl-token/lib/esm/state/account.js
var import_buffer_layout6 = __toESM(require_Layout(), 1);

// node_modules/@solana/spl-token/lib/esm/state/multisig.js
var import_buffer_layout5 = __toESM(require_Layout(), 1);
var MultisigLayout = (0, import_buffer_layout5.struct)([
  (0, import_buffer_layout5.u8)("m"),
  (0, import_buffer_layout5.u8)("n"),
  bool("isInitialized"),
  publicKey("signer1"),
  publicKey("signer2"),
  publicKey("signer3"),
  publicKey("signer4"),
  publicKey("signer5"),
  publicKey("signer6"),
  publicKey("signer7"),
  publicKey("signer8"),
  publicKey("signer9"),
  publicKey("signer10"),
  publicKey("signer11")
]);
var MULTISIG_SIZE = MultisigLayout.span;
async function getMultisig(connection, address, commitment, programId = TOKEN_PROGRAM_ID) {
  const info = await connection.getAccountInfo(address, commitment);
  return unpackMultisig(address, info, programId);
}
function unpackMultisig(address, info, programId = TOKEN_PROGRAM_ID) {
  if (!info)
    throw new TokenAccountNotFoundError();
  if (!info.owner.equals(programId))
    throw new TokenInvalidAccountOwnerError();
  if (info.data.length != MULTISIG_SIZE)
    throw new TokenInvalidAccountSizeError();
  const multisig = MultisigLayout.decode(info.data);
  return { address, ...multisig };
}
async function getMinimumBalanceForRentExemptMultisig(connection, commitment) {
  return await connection.getMinimumBalanceForRentExemption(MULTISIG_SIZE, commitment);
}

// node_modules/@solana/spl-token/lib/esm/state/account.js
var AccountState;
(function(AccountState2) {
  AccountState2[AccountState2["Uninitialized"] = 0] = "Uninitialized";
  AccountState2[AccountState2["Initialized"] = 1] = "Initialized";
  AccountState2[AccountState2["Frozen"] = 2] = "Frozen";
})(AccountState || (AccountState = {}));
var AccountLayout = (0, import_buffer_layout6.struct)([
  publicKey("mint"),
  publicKey("owner"),
  u64("amount"),
  (0, import_buffer_layout6.u32)("delegateOption"),
  publicKey("delegate"),
  (0, import_buffer_layout6.u8)("state"),
  (0, import_buffer_layout6.u32)("isNativeOption"),
  u64("isNative"),
  u64("delegatedAmount"),
  (0, import_buffer_layout6.u32)("closeAuthorityOption"),
  publicKey("closeAuthority")
]);
var ACCOUNT_SIZE = AccountLayout.span;
async function getAccount(connection, address, commitment, programId = TOKEN_PROGRAM_ID) {
  const info = await connection.getAccountInfo(address, commitment);
  return unpackAccount(address, info, programId);
}
async function getMultipleAccounts(connection, addresses, commitment, programId = TOKEN_PROGRAM_ID) {
  const infos = await connection.getMultipleAccountsInfo(addresses, commitment);
  return addresses.map((address, i) => unpackAccount(address, infos[i], programId));
}
async function getMinimumBalanceForRentExemptAccount(connection, commitment) {
  return await getMinimumBalanceForRentExemptAccountWithExtensions(connection, [], commitment);
}
async function getMinimumBalanceForRentExemptAccountWithExtensions(connection, extensions, commitment) {
  const accountLen = getAccountLen(extensions);
  return await connection.getMinimumBalanceForRentExemption(accountLen, commitment);
}
function unpackAccount(address, info, programId = TOKEN_PROGRAM_ID) {
  if (!info)
    throw new TokenAccountNotFoundError();
  if (!info.owner.equals(programId))
    throw new TokenInvalidAccountOwnerError();
  if (info.data.length < ACCOUNT_SIZE)
    throw new TokenInvalidAccountSizeError();
  const rawAccount = AccountLayout.decode(info.data.slice(0, ACCOUNT_SIZE));
  let tlvData = Buffer.alloc(0);
  if (info.data.length > ACCOUNT_SIZE) {
    if (info.data.length === MULTISIG_SIZE)
      throw new TokenInvalidAccountSizeError();
    if (info.data[ACCOUNT_SIZE] != AccountType.Account)
      throw new TokenInvalidAccountError();
    tlvData = info.data.slice(ACCOUNT_SIZE + ACCOUNT_TYPE_SIZE);
  }
  return {
    address,
    mint: rawAccount.mint,
    owner: rawAccount.owner,
    amount: rawAccount.amount,
    delegate: rawAccount.delegateOption ? rawAccount.delegate : null,
    delegatedAmount: rawAccount.delegatedAmount,
    isInitialized: rawAccount.state !== AccountState.Uninitialized,
    isFrozen: rawAccount.state === AccountState.Frozen,
    isNative: !!rawAccount.isNativeOption,
    rentExemptReserve: rawAccount.isNativeOption ? rawAccount.isNative : null,
    closeAuthority: rawAccount.closeAuthorityOption ? rawAccount.closeAuthority : null,
    tlvData
  };
}

// node_modules/@solana/spl-token/lib/esm/extensions/cpiGuard/actions.js
init_index_browser_esm();

// node_modules/@solana/spl-token/lib/esm/actions/internal.js
init_index_browser_esm();
function getSigners(signerOrMultisig, multiSigners) {
  return signerOrMultisig instanceof PublicKey ? [signerOrMultisig, multiSigners] : [signerOrMultisig.publicKey, [signerOrMultisig]];
}

// node_modules/@solana/spl-token/lib/esm/extensions/cpiGuard/instructions.js
var import_buffer_layout7 = __toESM(require_Layout(), 1);
init_index_browser_esm();

// node_modules/@solana/spl-token/lib/esm/instructions/internal.js
init_index_browser_esm();
function addSigners(keys, ownerOrAuthority, multiSigners) {
  if (multiSigners.length) {
    keys.push({ pubkey: ownerOrAuthority, isSigner: false, isWritable: false });
    for (const signer of multiSigners) {
      keys.push({
        pubkey: signer instanceof PublicKey ? signer : signer.publicKey,
        isSigner: true,
        isWritable: false
      });
    }
  } else {
    keys.push({ pubkey: ownerOrAuthority, isSigner: true, isWritable: false });
  }
  return keys;
}

// node_modules/@solana/spl-token/lib/esm/extensions/cpiGuard/instructions.js
var CpiGuardInstruction;
(function(CpiGuardInstruction2) {
  CpiGuardInstruction2[CpiGuardInstruction2["Enable"] = 0] = "Enable";
  CpiGuardInstruction2[CpiGuardInstruction2["Disable"] = 1] = "Disable";
})(CpiGuardInstruction || (CpiGuardInstruction = {}));
var cpiGuardInstructionData = (0, import_buffer_layout7.struct)([(0, import_buffer_layout7.u8)("instruction"), (0, import_buffer_layout7.u8)("cpiGuardInstruction")]);
function createEnableCpiGuardInstruction(account, authority, multiSigners = [], programId = TOKEN_2022_PROGRAM_ID) {
  return createCpiGuardInstruction(CpiGuardInstruction.Enable, account, authority, multiSigners, programId);
}
function createDisableCpiGuardInstruction(account, authority, multiSigners = [], programId = TOKEN_2022_PROGRAM_ID) {
  return createCpiGuardInstruction(CpiGuardInstruction.Disable, account, authority, multiSigners, programId);
}
function createCpiGuardInstruction(cpiGuardInstruction, account, authority, multiSigners, programId) {
  if (!programSupportsExtensions(programId)) {
    throw new TokenUnsupportedInstructionError();
  }
  const keys = addSigners([{ pubkey: account, isSigner: false, isWritable: true }], authority, multiSigners);
  const data = Buffer.alloc(cpiGuardInstructionData.span);
  cpiGuardInstructionData.encode({
    instruction: TokenInstruction.CpiGuardExtension,
    cpiGuardInstruction
  }, data);
  return new TransactionInstruction({ keys, programId, data });
}

// node_modules/@solana/spl-token/lib/esm/extensions/cpiGuard/actions.js
async function enableCpiGuard(connection, payer, account, owner, multiSigners = [], confirmOptions, programId = TOKEN_2022_PROGRAM_ID) {
  const [ownerPublicKey, signers] = getSigners(owner, multiSigners);
  const transaction = new Transaction().add(createEnableCpiGuardInstruction(account, ownerPublicKey, signers, programId));
  return await sendAndConfirmTransaction(connection, transaction, [payer, ...signers], confirmOptions);
}
async function disableCpiGuard(connection, payer, account, owner, multiSigners = [], confirmOptions, programId = TOKEN_2022_PROGRAM_ID) {
  const [ownerPublicKey, signers] = getSigners(owner, multiSigners);
  const transaction = new Transaction().add(createDisableCpiGuardInstruction(account, ownerPublicKey, signers, programId));
  return await sendAndConfirmTransaction(connection, transaction, [payer, ...signers], confirmOptions);
}

// node_modules/@solana/spl-token/lib/esm/extensions/cpiGuard/state.js
var import_buffer_layout8 = __toESM(require_Layout(), 1);
var CpiGuardLayout = (0, import_buffer_layout8.struct)([bool("lockCpi")]);
var CPI_GUARD_SIZE = CpiGuardLayout.span;
function getCpiGuard(account) {
  const extensionData = getExtensionData(ExtensionType.CpiGuard, account.tlvData);
  if (extensionData !== null) {
    return CpiGuardLayout.decode(extensionData);
  } else {
    return null;
  }
}

// node_modules/@solana/spl-token/lib/esm/extensions/defaultAccountState/actions.js
init_index_browser_esm();

// node_modules/@solana/spl-token/lib/esm/extensions/defaultAccountState/instructions.js
var import_buffer_layout9 = __toESM(require_Layout(), 1);
init_index_browser_esm();
var DefaultAccountStateInstruction;
(function(DefaultAccountStateInstruction2) {
  DefaultAccountStateInstruction2[DefaultAccountStateInstruction2["Initialize"] = 0] = "Initialize";
  DefaultAccountStateInstruction2[DefaultAccountStateInstruction2["Update"] = 1] = "Update";
})(DefaultAccountStateInstruction || (DefaultAccountStateInstruction = {}));
var defaultAccountStateInstructionData = (0, import_buffer_layout9.struct)([
  (0, import_buffer_layout9.u8)("instruction"),
  (0, import_buffer_layout9.u8)("defaultAccountStateInstruction"),
  (0, import_buffer_layout9.u8)("accountState")
]);
function createInitializeDefaultAccountStateInstruction(mint, accountState, programId = TOKEN_2022_PROGRAM_ID) {
  if (!programSupportsExtensions(programId)) {
    throw new TokenUnsupportedInstructionError();
  }
  const keys = [{ pubkey: mint, isSigner: false, isWritable: true }];
  const data = Buffer.alloc(defaultAccountStateInstructionData.span);
  defaultAccountStateInstructionData.encode({
    instruction: TokenInstruction.DefaultAccountStateExtension,
    defaultAccountStateInstruction: DefaultAccountStateInstruction.Initialize,
    accountState
  }, data);
  return new TransactionInstruction({ keys, programId, data });
}
function createUpdateDefaultAccountStateInstruction(mint, accountState, freezeAuthority, multiSigners = [], programId = TOKEN_2022_PROGRAM_ID) {
  if (!programSupportsExtensions(programId)) {
    throw new TokenUnsupportedInstructionError();
  }
  const keys = addSigners([{ pubkey: mint, isSigner: false, isWritable: true }], freezeAuthority, multiSigners);
  const data = Buffer.alloc(defaultAccountStateInstructionData.span);
  defaultAccountStateInstructionData.encode({
    instruction: TokenInstruction.DefaultAccountStateExtension,
    defaultAccountStateInstruction: DefaultAccountStateInstruction.Update,
    accountState
  }, data);
  return new TransactionInstruction({ keys, programId, data });
}

// node_modules/@solana/spl-token/lib/esm/extensions/defaultAccountState/actions.js
async function initializeDefaultAccountState(connection, payer, mint, state, confirmOptions, programId = TOKEN_2022_PROGRAM_ID) {
  const transaction = new Transaction().add(createInitializeDefaultAccountStateInstruction(mint, state, programId));
  return await sendAndConfirmTransaction(connection, transaction, [payer], confirmOptions);
}
async function updateDefaultAccountState(connection, payer, mint, state, freezeAuthority, multiSigners = [], confirmOptions, programId = TOKEN_2022_PROGRAM_ID) {
  const [freezeAuthorityPublicKey, signers] = getSigners(freezeAuthority, multiSigners);
  const transaction = new Transaction().add(createUpdateDefaultAccountStateInstruction(mint, state, freezeAuthorityPublicKey, signers, programId));
  return await sendAndConfirmTransaction(connection, transaction, [payer, ...signers], confirmOptions);
}

// node_modules/@solana/spl-token/lib/esm/extensions/defaultAccountState/state.js
var import_buffer_layout10 = __toESM(require_Layout(), 1);
var DefaultAccountStateLayout = (0, import_buffer_layout10.struct)([(0, import_buffer_layout10.u8)("state")]);
var DEFAULT_ACCOUNT_STATE_SIZE = DefaultAccountStateLayout.span;
function getDefaultAccountState(mint) {
  const extensionData = getExtensionData(ExtensionType.DefaultAccountState, mint.tlvData);
  if (extensionData !== null) {
    return DefaultAccountStateLayout.decode(extensionData);
  } else {
    return null;
  }
}

// node_modules/@solana/spl-token/lib/esm/extensions/tokenGroup/actions.js
init_index_browser_esm();

// node_modules/@solana/codecs/node_modules/@solana/errors/dist/index.browser.mjs
var SOLANA_ERROR__BLOCK_HEIGHT_EXCEEDED = 1;
var SOLANA_ERROR__INVALID_NONCE = 2;
var SOLANA_ERROR__NONCE_ACCOUNT_NOT_FOUND = 3;
var SOLANA_ERROR__BLOCKHASH_STRING_LENGTH_OUT_OF_RANGE = 4;
var SOLANA_ERROR__INVALID_BLOCKHASH_BYTE_LENGTH = 5;
var SOLANA_ERROR__LAMPORTS_OUT_OF_RANGE = 6;
var SOLANA_ERROR__MALFORMED_BIGINT_STRING = 7;
var SOLANA_ERROR__MALFORMED_NUMBER_STRING = 8;
var SOLANA_ERROR__TIMESTAMP_OUT_OF_RANGE = 9;
var SOLANA_ERROR__JSON_RPC__PARSE_ERROR = -32700;
var SOLANA_ERROR__JSON_RPC__INTERNAL_ERROR = -32603;
var SOLANA_ERROR__JSON_RPC__INVALID_PARAMS = -32602;
var SOLANA_ERROR__JSON_RPC__METHOD_NOT_FOUND = -32601;
var SOLANA_ERROR__JSON_RPC__INVALID_REQUEST = -32600;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_MIN_CONTEXT_SLOT_NOT_REACHED = -32016;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_UNSUPPORTED_TRANSACTION_VERSION = -32015;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_STATUS_NOT_AVAILABLE_YET = -32014;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_SIGNATURE_LEN_MISMATCH = -32013;
var SOLANA_ERROR__JSON_RPC__SCAN_ERROR = -32012;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_HISTORY_NOT_AVAILABLE = -32011;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_KEY_EXCLUDED_FROM_SECONDARY_INDEX = -32010;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_LONG_TERM_STORAGE_SLOT_SKIPPED = -32009;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_NO_SNAPSHOT = -32008;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_SLOT_SKIPPED = -32007;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_PRECOMPILE_VERIFICATION_FAILURE = -32006;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_NODE_UNHEALTHY = -32005;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_NOT_AVAILABLE = -32004;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_SIGNATURE_VERIFICATION_FAILURE = -32003;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_SEND_TRANSACTION_PREFLIGHT_FAILURE = -32002;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_CLEANED_UP = -32001;
var SOLANA_ERROR__ADDRESSES__INVALID_BYTE_LENGTH = 28e5;
var SOLANA_ERROR__ADDRESSES__STRING_LENGTH_OUT_OF_RANGE = 2800001;
var SOLANA_ERROR__ADDRESSES__INVALID_BASE58_ENCODED_ADDRESS = 2800002;
var SOLANA_ERROR__ADDRESSES__INVALID_ED25519_PUBLIC_KEY = 2800003;
var SOLANA_ERROR__ADDRESSES__MALFORMED_PDA = 2800004;
var SOLANA_ERROR__ADDRESSES__PDA_BUMP_SEED_OUT_OF_RANGE = 2800005;
var SOLANA_ERROR__ADDRESSES__MAX_NUMBER_OF_PDA_SEEDS_EXCEEDED = 2800006;
var SOLANA_ERROR__ADDRESSES__MAX_PDA_SEED_LENGTH_EXCEEDED = 2800007;
var SOLANA_ERROR__ADDRESSES__INVALID_SEEDS_POINT_ON_CURVE = 2800008;
var SOLANA_ERROR__ADDRESSES__FAILED_TO_FIND_VIABLE_PDA_BUMP_SEED = 2800009;
var SOLANA_ERROR__ADDRESSES__PDA_ENDS_WITH_PDA_MARKER = 2800010;
var SOLANA_ERROR__ACCOUNTS__ACCOUNT_NOT_FOUND = 323e4;
var SOLANA_ERROR__ACCOUNTS__ONE_OR_MORE_ACCOUNTS_NOT_FOUND = ********;
var SOLANA_ERROR__ACCOUNTS__FAILED_TO_DECODE_ACCOUNT = 3230002;
var SOLANA_ERROR__ACCOUNTS__EXPECTED_DECODED_ACCOUNT = 3230003;
var SOLANA_ERROR__ACCOUNTS__EXPECTED_ALL_ACCOUNTS_TO_BE_DECODED = 3230004;
var SOLANA_ERROR__SUBTLE_CRYPTO__DISALLOWED_IN_INSECURE_CONTEXT = 361e4;
var SOLANA_ERROR__SUBTLE_CRYPTO__DIGEST_UNIMPLEMENTED = 3610001;
var SOLANA_ERROR__SUBTLE_CRYPTO__ED25519_ALGORITHM_UNIMPLEMENTED = 3610002;
var SOLANA_ERROR__SUBTLE_CRYPTO__EXPORT_FUNCTION_UNIMPLEMENTED = 3610003;
var SOLANA_ERROR__SUBTLE_CRYPTO__GENERATE_FUNCTION_UNIMPLEMENTED = 3610004;
var SOLANA_ERROR__SUBTLE_CRYPTO__SIGN_FUNCTION_UNIMPLEMENTED = 3610005;
var SOLANA_ERROR__SUBTLE_CRYPTO__VERIFY_FUNCTION_UNIMPLEMENTED = 3610006;
var SOLANA_ERROR__SUBTLE_CRYPTO__CANNOT_EXPORT_NON_EXTRACTABLE_KEY = 3610007;
var SOLANA_ERROR__CRYPTO__RANDOM_VALUES_FUNCTION_UNIMPLEMENTED = 3611e3;
var SOLANA_ERROR__KEYS__INVALID_KEY_PAIR_BYTE_LENGTH = 3704e3;
var SOLANA_ERROR__KEYS__INVALID_PRIVATE_KEY_BYTE_LENGTH = 3704001;
var SOLANA_ERROR__KEYS__INVALID_SIGNATURE_BYTE_LENGTH = 3704002;
var SOLANA_ERROR__KEYS__SIGNATURE_STRING_LENGTH_OUT_OF_RANGE = 3704003;
var SOLANA_ERROR__KEYS__PUBLIC_KEY_MUST_MATCH_PRIVATE_KEY = 3704004;
var SOLANA_ERROR__INSTRUCTION__EXPECTED_TO_HAVE_ACCOUNTS = 4128e3;
var SOLANA_ERROR__INSTRUCTION__EXPECTED_TO_HAVE_DATA = 4128001;
var SOLANA_ERROR__INSTRUCTION__PROGRAM_ID_MISMATCH = 4128002;
var SOLANA_ERROR__INSTRUCTION_ERROR__UNKNOWN = 4615e3;
var SOLANA_ERROR__INSTRUCTION_ERROR__GENERIC_ERROR = 4615001;
var SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ARGUMENT = 4615002;
var SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_INSTRUCTION_DATA = 4615003;
var SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ACCOUNT_DATA = 4615004;
var SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_DATA_TOO_SMALL = 4615005;
var SOLANA_ERROR__INSTRUCTION_ERROR__INSUFFICIENT_FUNDS = 4615006;
var SOLANA_ERROR__INSTRUCTION_ERROR__INCORRECT_PROGRAM_ID = 4615007;
var SOLANA_ERROR__INSTRUCTION_ERROR__MISSING_REQUIRED_SIGNATURE = 4615008;
var SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_ALREADY_INITIALIZED = 4615009;
var SOLANA_ERROR__INSTRUCTION_ERROR__UNINITIALIZED_ACCOUNT = 4615010;
var SOLANA_ERROR__INSTRUCTION_ERROR__UNBALANCED_INSTRUCTION = 4615011;
var SOLANA_ERROR__INSTRUCTION_ERROR__MODIFIED_PROGRAM_ID = 4615012;
var SOLANA_ERROR__INSTRUCTION_ERROR__EXTERNAL_ACCOUNT_LAMPORT_SPEND = 4615013;
var SOLANA_ERROR__INSTRUCTION_ERROR__EXTERNAL_ACCOUNT_DATA_MODIFIED = 4615014;
var SOLANA_ERROR__INSTRUCTION_ERROR__READONLY_LAMPORT_CHANGE = 4615015;
var SOLANA_ERROR__INSTRUCTION_ERROR__READONLY_DATA_MODIFIED = 4615016;
var SOLANA_ERROR__INSTRUCTION_ERROR__DUPLICATE_ACCOUNT_INDEX = 4615017;
var SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_MODIFIED = 4615018;
var SOLANA_ERROR__INSTRUCTION_ERROR__RENT_EPOCH_MODIFIED = 4615019;
var SOLANA_ERROR__INSTRUCTION_ERROR__NOT_ENOUGH_ACCOUNT_KEYS = 4615020;
var SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_DATA_SIZE_CHANGED = 4615021;
var SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_NOT_EXECUTABLE = 4615022;
var SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_BORROW_FAILED = 4615023;
var SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_BORROW_OUTSTANDING = 4615024;
var SOLANA_ERROR__INSTRUCTION_ERROR__DUPLICATE_ACCOUNT_OUT_OF_SYNC = 4615025;
var SOLANA_ERROR__INSTRUCTION_ERROR__CUSTOM = 4615026;
var SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ERROR = 4615027;
var SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_DATA_MODIFIED = 4615028;
var SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_LAMPORT_CHANGE = 4615029;
var SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_ACCOUNT_NOT_RENT_EXEMPT = 4615030;
var SOLANA_ERROR__INSTRUCTION_ERROR__UNSUPPORTED_PROGRAM_ID = 4615031;
var SOLANA_ERROR__INSTRUCTION_ERROR__CALL_DEPTH = 4615032;
var SOLANA_ERROR__INSTRUCTION_ERROR__MISSING_ACCOUNT = 4615033;
var SOLANA_ERROR__INSTRUCTION_ERROR__REENTRANCY_NOT_ALLOWED = 4615034;
var SOLANA_ERROR__INSTRUCTION_ERROR__MAX_SEED_LENGTH_EXCEEDED = 4615035;
var SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_SEEDS = 4615036;
var SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_REALLOC = 4615037;
var SOLANA_ERROR__INSTRUCTION_ERROR__COMPUTATIONAL_BUDGET_EXCEEDED = 4615038;
var SOLANA_ERROR__INSTRUCTION_ERROR__PRIVILEGE_ESCALATION = 4615039;
var SOLANA_ERROR__INSTRUCTION_ERROR__PROGRAM_ENVIRONMENT_SETUP_FAILURE = 4615040;
var SOLANA_ERROR__INSTRUCTION_ERROR__PROGRAM_FAILED_TO_COMPLETE = 4615041;
var SOLANA_ERROR__INSTRUCTION_ERROR__PROGRAM_FAILED_TO_COMPILE = 4615042;
var SOLANA_ERROR__INSTRUCTION_ERROR__IMMUTABLE = 4615043;
var SOLANA_ERROR__INSTRUCTION_ERROR__INCORRECT_AUTHORITY = 4615044;
var SOLANA_ERROR__INSTRUCTION_ERROR__BORSH_IO_ERROR = 4615045;
var SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_NOT_RENT_EXEMPT = 4615046;
var SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ACCOUNT_OWNER = 4615047;
var SOLANA_ERROR__INSTRUCTION_ERROR__ARITHMETIC_OVERFLOW = 4615048;
var SOLANA_ERROR__INSTRUCTION_ERROR__UNSUPPORTED_SYSVAR = 4615049;
var SOLANA_ERROR__INSTRUCTION_ERROR__ILLEGAL_OWNER = 4615050;
var SOLANA_ERROR__INSTRUCTION_ERROR__MAX_ACCOUNTS_DATA_ALLOCATIONS_EXCEEDED = 4615051;
var SOLANA_ERROR__INSTRUCTION_ERROR__MAX_ACCOUNTS_EXCEEDED = 4615052;
var SOLANA_ERROR__INSTRUCTION_ERROR__MAX_INSTRUCTION_TRACE_LENGTH_EXCEEDED = 4615053;
var SOLANA_ERROR__INSTRUCTION_ERROR__BUILTIN_PROGRAMS_MUST_CONSUME_COMPUTE_UNITS = 4615054;
var SOLANA_ERROR__SIGNER__ADDRESS_CANNOT_HAVE_MULTIPLE_SIGNERS = 5508e3;
var SOLANA_ERROR__SIGNER__EXPECTED_KEY_PAIR_SIGNER = 5508001;
var SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_SIGNER = 5508002;
var SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_MODIFYING_SIGNER = 5508003;
var SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_PARTIAL_SIGNER = 5508004;
var SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_SIGNER = 5508005;
var SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_MODIFYING_SIGNER = 5508006;
var SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_PARTIAL_SIGNER = 5508007;
var SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_SENDING_SIGNER = 5508008;
var SOLANA_ERROR__SIGNER__TRANSACTION_CANNOT_HAVE_MULTIPLE_SENDING_SIGNERS = 5508009;
var SOLANA_ERROR__SIGNER__TRANSACTION_SENDING_SIGNER_MISSING = 5508010;
var SOLANA_ERROR__SIGNER__WALLET_MULTISIGN_UNIMPLEMENTED = 5508011;
var SOLANA_ERROR__TRANSACTION__INVOKED_PROGRAMS_CANNOT_PAY_FEES = 5663e3;
var SOLANA_ERROR__TRANSACTION__INVOKED_PROGRAMS_MUST_NOT_BE_WRITABLE = 5663001;
var SOLANA_ERROR__TRANSACTION__EXPECTED_BLOCKHASH_LIFETIME = 5663002;
var SOLANA_ERROR__TRANSACTION__EXPECTED_NONCE_LIFETIME = 5663003;
var SOLANA_ERROR__TRANSACTION__VERSION_NUMBER_OUT_OF_RANGE = 5663004;
var SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_ADDRESS_LOOKUP_TABLE_CONTENTS_MISSING = 5663005;
var SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_ADDRESS_LOOKUP_TABLE_INDEX_OUT_OF_RANGE = 5663006;
var SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_INSTRUCTION_PROGRAM_ADDRESS_NOT_FOUND = 5663007;
var SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_FEE_PAYER_MISSING = 5663008;
var SOLANA_ERROR__TRANSACTION__SIGNATURES_MISSING = 5663009;
var SOLANA_ERROR__TRANSACTION__ADDRESS_MISSING = 5663010;
var SOLANA_ERROR__TRANSACTION__FEE_PAYER_MISSING = 5663011;
var SOLANA_ERROR__TRANSACTION__FEE_PAYER_SIGNATURE_MISSING = 5663012;
var SOLANA_ERROR__TRANSACTION__INVALID_NONCE_TRANSACTION_INSTRUCTIONS_MISSING = 5663013;
var SOLANA_ERROR__TRANSACTION__INVALID_NONCE_TRANSACTION_FIRST_INSTRUCTION_MUST_BE_ADVANCE_NONCE = 5663014;
var SOLANA_ERROR__TRANSACTION__ADDRESSES_CANNOT_SIGN_TRANSACTION = 5663015;
var SOLANA_ERROR__TRANSACTION__CANNOT_ENCODE_WITH_EMPTY_SIGNATURES = 5663016;
var SOLANA_ERROR__TRANSACTION__MESSAGE_SIGNATURES_MISMATCH = 5663017;
var SOLANA_ERROR__TRANSACTION__FAILED_TO_ESTIMATE_COMPUTE_LIMIT = 5663018;
var SOLANA_ERROR__TRANSACTION_ERROR__UNKNOWN = 705e4;
var SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_IN_USE = 7050001;
var SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_LOADED_TWICE = 7050002;
var SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_NOT_FOUND = 7050003;
var SOLANA_ERROR__TRANSACTION_ERROR__PROGRAM_ACCOUNT_NOT_FOUND = 7050004;
var SOLANA_ERROR__TRANSACTION_ERROR__INSUFFICIENT_FUNDS_FOR_FEE = 7050005;
var SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ACCOUNT_FOR_FEE = 7050006;
var SOLANA_ERROR__TRANSACTION_ERROR__ALREADY_PROCESSED = 7050007;
var SOLANA_ERROR__TRANSACTION_ERROR__BLOCKHASH_NOT_FOUND = 7050008;
var SOLANA_ERROR__TRANSACTION_ERROR__CALL_CHAIN_TOO_DEEP = 7050009;
var SOLANA_ERROR__TRANSACTION_ERROR__MISSING_SIGNATURE_FOR_FEE = 7050010;
var SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ACCOUNT_INDEX = 7050011;
var SOLANA_ERROR__TRANSACTION_ERROR__SIGNATURE_FAILURE = 7050012;
var SOLANA_ERROR__TRANSACTION_ERROR__INVALID_PROGRAM_FOR_EXECUTION = 7050013;
var SOLANA_ERROR__TRANSACTION_ERROR__SANITIZE_FAILURE = 7050014;
var SOLANA_ERROR__TRANSACTION_ERROR__CLUSTER_MAINTENANCE = 7050015;
var SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_BORROW_OUTSTANDING = 7050016;
var SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_MAX_BLOCK_COST_LIMIT = 7050017;
var SOLANA_ERROR__TRANSACTION_ERROR__UNSUPPORTED_VERSION = 7050018;
var SOLANA_ERROR__TRANSACTION_ERROR__INVALID_WRITABLE_ACCOUNT = 7050019;
var SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_MAX_ACCOUNT_COST_LIMIT = 7050020;
var SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_ACCOUNT_DATA_BLOCK_LIMIT = 7050021;
var SOLANA_ERROR__TRANSACTION_ERROR__TOO_MANY_ACCOUNT_LOCKS = 7050022;
var SOLANA_ERROR__TRANSACTION_ERROR__ADDRESS_LOOKUP_TABLE_NOT_FOUND = 7050023;
var SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ADDRESS_LOOKUP_TABLE_OWNER = 7050024;
var SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ADDRESS_LOOKUP_TABLE_DATA = 7050025;
var SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ADDRESS_LOOKUP_TABLE_INDEX = 7050026;
var SOLANA_ERROR__TRANSACTION_ERROR__INVALID_RENT_PAYING_ACCOUNT = 7050027;
var SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_MAX_VOTE_COST_LIMIT = 7050028;
var SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_ACCOUNT_DATA_TOTAL_LIMIT = 7050029;
var SOLANA_ERROR__TRANSACTION_ERROR__DUPLICATE_INSTRUCTION = 7050030;
var SOLANA_ERROR__TRANSACTION_ERROR__INSUFFICIENT_FUNDS_FOR_RENT = 7050031;
var SOLANA_ERROR__TRANSACTION_ERROR__MAX_LOADED_ACCOUNTS_DATA_SIZE_EXCEEDED = 7050032;
var SOLANA_ERROR__TRANSACTION_ERROR__INVALID_LOADED_ACCOUNTS_DATA_SIZE_LIMIT = 7050033;
var SOLANA_ERROR__TRANSACTION_ERROR__RESANITIZATION_NEEDED = 7050034;
var SOLANA_ERROR__TRANSACTION_ERROR__PROGRAM_EXECUTION_TEMPORARILY_RESTRICTED = 7050035;
var SOLANA_ERROR__TRANSACTION_ERROR__UNBALANCED_TRANSACTION = 7050036;
var SOLANA_ERROR__CODECS__CANNOT_DECODE_EMPTY_BYTE_ARRAY = 8078e3;
var SOLANA_ERROR__CODECS__INVALID_BYTE_LENGTH = 8078001;
var SOLANA_ERROR__CODECS__EXPECTED_FIXED_LENGTH = 8078002;
var SOLANA_ERROR__CODECS__EXPECTED_VARIABLE_LENGTH = 8078003;
var SOLANA_ERROR__CODECS__ENCODER_DECODER_SIZE_COMPATIBILITY_MISMATCH = 8078004;
var SOLANA_ERROR__CODECS__ENCODER_DECODER_FIXED_SIZE_MISMATCH = 8078005;
var SOLANA_ERROR__CODECS__ENCODER_DECODER_MAX_SIZE_MISMATCH = 8078006;
var SOLANA_ERROR__CODECS__INVALID_NUMBER_OF_ITEMS = 8078007;
var SOLANA_ERROR__CODECS__ENUM_DISCRIMINATOR_OUT_OF_RANGE = 8078008;
var SOLANA_ERROR__CODECS__INVALID_DISCRIMINATED_UNION_VARIANT = 8078009;
var SOLANA_ERROR__CODECS__INVALID_ENUM_VARIANT = 8078010;
var SOLANA_ERROR__CODECS__NUMBER_OUT_OF_RANGE = 8078011;
var SOLANA_ERROR__CODECS__INVALID_STRING_FOR_BASE = 8078012;
var SOLANA_ERROR__CODECS__EXPECTED_POSITIVE_BYTE_LENGTH = 8078013;
var SOLANA_ERROR__CODECS__OFFSET_OUT_OF_RANGE = 8078014;
var SOLANA_ERROR__CODECS__INVALID_LITERAL_UNION_VARIANT = 8078015;
var SOLANA_ERROR__CODECS__LITERAL_UNION_DISCRIMINATOR_OUT_OF_RANGE = 8078016;
var SOLANA_ERROR__CODECS__UNION_VARIANT_OUT_OF_RANGE = 8078017;
var SOLANA_ERROR__CODECS__INVALID_CONSTANT = 8078018;
var SOLANA_ERROR__CODECS__EXPECTED_ZERO_VALUE_TO_MATCH_ITEM_FIXED_SIZE = 8078019;
var SOLANA_ERROR__CODECS__ENCODED_BYTES_MUST_NOT_INCLUDE_SENTINEL = 8078020;
var SOLANA_ERROR__CODECS__SENTINEL_MISSING_IN_DECODED_BYTES = 8078021;
var SOLANA_ERROR__CODECS__CANNOT_USE_LEXICAL_VALUES_AS_ENUM_DISCRIMINATORS = 8078022;
var SOLANA_ERROR__RPC__INTEGER_OVERFLOW = 81e5;
var SOLANA_ERROR__RPC__TRANSPORT_HTTP_HEADER_FORBIDDEN = 8100001;
var SOLANA_ERROR__RPC__TRANSPORT_HTTP_ERROR = 8100002;
var SOLANA_ERROR__RPC_SUBSCRIPTIONS__CANNOT_CREATE_SUBSCRIPTION_REQUEST = 819e4;
var SOLANA_ERROR__RPC_SUBSCRIPTIONS__EXPECTED_SERVER_SUBSCRIPTION_ID = 8190001;
var SOLANA_ERROR__RPC_SUBSCRIPTIONS__TRANSPORT_CLOSED_BEFORE_MESSAGE_BUFFERED = 8190002;
var SOLANA_ERROR__RPC_SUBSCRIPTIONS__TRANSPORT_CONNECTION_CLOSED = 8190003;
var SOLANA_ERROR__RPC_SUBSCRIPTIONS__TRANSPORT_FAILED_TO_CONNECT = 8190004;
var SOLANA_ERROR__INVARIANT_VIOLATION__WEBSOCKET_MESSAGE_ITERATOR_STATE_MISSING = 99e5;
var SOLANA_ERROR__INVARIANT_VIOLATION__WEBSOCKET_MESSAGE_ITERATOR_MUST_NOT_POLL_BEFORE_RESOLVING_EXISTING_MESSAGE_PROMISE = 9900001;
var SOLANA_ERROR__INVARIANT_VIOLATION__CACHED_ABORTABLE_ITERABLE_CACHE_ENTRY_MISSING = 9900002;
var SOLANA_ERROR__INVARIANT_VIOLATION__SWITCH_MUST_BE_EXHAUSTIVE = 9900003;
var SolanaErrorMessages = {
  [SOLANA_ERROR__ACCOUNTS__ACCOUNT_NOT_FOUND]: "Account not found at address: $address",
  [SOLANA_ERROR__ACCOUNTS__EXPECTED_ALL_ACCOUNTS_TO_BE_DECODED]: "Not all accounts were decoded. Encoded accounts found at addresses: $addresses.",
  [SOLANA_ERROR__ACCOUNTS__EXPECTED_DECODED_ACCOUNT]: "Expected decoded account at address: $address",
  [SOLANA_ERROR__ACCOUNTS__FAILED_TO_DECODE_ACCOUNT]: "Failed to decode account data at address: $address",
  [SOLANA_ERROR__ACCOUNTS__ONE_OR_MORE_ACCOUNTS_NOT_FOUND]: "Accounts not found at addresses: $addresses",
  [SOLANA_ERROR__ADDRESSES__FAILED_TO_FIND_VIABLE_PDA_BUMP_SEED]: "Unable to find a viable program address bump seed.",
  [SOLANA_ERROR__ADDRESSES__INVALID_BASE58_ENCODED_ADDRESS]: "$putativeAddress is not a base58-encoded address.",
  [SOLANA_ERROR__ADDRESSES__INVALID_BYTE_LENGTH]: "Expected base58 encoded address to decode to a byte array of length 32. Actual length: $actualLength.",
  [SOLANA_ERROR__ADDRESSES__INVALID_ED25519_PUBLIC_KEY]: "The `CryptoKey` must be an `Ed25519` public key.",
  [SOLANA_ERROR__ADDRESSES__INVALID_SEEDS_POINT_ON_CURVE]: "Invalid seeds; point must fall off the Ed25519 curve.",
  [SOLANA_ERROR__ADDRESSES__MALFORMED_PDA]: "Expected given program derived address to have the following format: [Address, ProgramDerivedAddressBump].",
  [SOLANA_ERROR__ADDRESSES__MAX_NUMBER_OF_PDA_SEEDS_EXCEEDED]: "A maximum of $maxSeeds seeds, including the bump seed, may be supplied when creating an address. Received: $actual.",
  [SOLANA_ERROR__ADDRESSES__MAX_PDA_SEED_LENGTH_EXCEEDED]: "The seed at index $index with length $actual exceeds the maximum length of $maxSeedLength bytes.",
  [SOLANA_ERROR__ADDRESSES__PDA_BUMP_SEED_OUT_OF_RANGE]: "Expected program derived address bump to be in the range [0, 255], got: $bump.",
  [SOLANA_ERROR__ADDRESSES__PDA_ENDS_WITH_PDA_MARKER]: "Program address cannot end with PDA marker.",
  [SOLANA_ERROR__ADDRESSES__STRING_LENGTH_OUT_OF_RANGE]: "Expected base58-encoded address string of length in the range [32, 44]. Actual length: $actualLength.",
  [SOLANA_ERROR__BLOCKHASH_STRING_LENGTH_OUT_OF_RANGE]: "Expected base58-encoded blockash string of length in the range [32, 44]. Actual length: $actualLength.",
  [SOLANA_ERROR__BLOCK_HEIGHT_EXCEEDED]: "The network has progressed past the last block for which this transaction could have been committed.",
  [SOLANA_ERROR__CODECS__CANNOT_DECODE_EMPTY_BYTE_ARRAY]: "Codec [$codecDescription] cannot decode empty byte arrays.",
  [SOLANA_ERROR__CODECS__CANNOT_USE_LEXICAL_VALUES_AS_ENUM_DISCRIMINATORS]: "Enum codec cannot use lexical values [$stringValues] as discriminators. Either remove all lexical values or set `useValuesAsDiscriminators` to `false`.",
  [SOLANA_ERROR__CODECS__ENCODED_BYTES_MUST_NOT_INCLUDE_SENTINEL]: "Sentinel [$hexSentinel] must not be present in encoded bytes [$hexEncodedBytes].",
  [SOLANA_ERROR__CODECS__ENCODER_DECODER_FIXED_SIZE_MISMATCH]: "Encoder and decoder must have the same fixed size, got [$encoderFixedSize] and [$decoderFixedSize].",
  [SOLANA_ERROR__CODECS__ENCODER_DECODER_MAX_SIZE_MISMATCH]: "Encoder and decoder must have the same max size, got [$encoderMaxSize] and [$decoderMaxSize].",
  [SOLANA_ERROR__CODECS__ENCODER_DECODER_SIZE_COMPATIBILITY_MISMATCH]: "Encoder and decoder must either both be fixed-size or variable-size.",
  [SOLANA_ERROR__CODECS__ENUM_DISCRIMINATOR_OUT_OF_RANGE]: "Enum discriminator out of range. Expected a number in [$formattedValidDiscriminators], got $discriminator.",
  [SOLANA_ERROR__CODECS__EXPECTED_FIXED_LENGTH]: "Expected a fixed-size codec, got a variable-size one.",
  [SOLANA_ERROR__CODECS__EXPECTED_POSITIVE_BYTE_LENGTH]: "Codec [$codecDescription] expected a positive byte length, got $bytesLength.",
  [SOLANA_ERROR__CODECS__EXPECTED_VARIABLE_LENGTH]: "Expected a variable-size codec, got a fixed-size one.",
  [SOLANA_ERROR__CODECS__EXPECTED_ZERO_VALUE_TO_MATCH_ITEM_FIXED_SIZE]: "Codec [$codecDescription] expected zero-value [$hexZeroValue] to have the same size as the provided fixed-size item [$expectedSize bytes].",
  [SOLANA_ERROR__CODECS__INVALID_BYTE_LENGTH]: "Codec [$codecDescription] expected $expected bytes, got $bytesLength.",
  [SOLANA_ERROR__CODECS__INVALID_CONSTANT]: "Expected byte array constant [$hexConstant] to be present in data [$hexData] at offset [$offset].",
  [SOLANA_ERROR__CODECS__INVALID_DISCRIMINATED_UNION_VARIANT]: "Invalid discriminated union variant. Expected one of [$variants], got $value.",
  [SOLANA_ERROR__CODECS__INVALID_ENUM_VARIANT]: "Invalid enum variant. Expected one of [$stringValues] or a number in [$formattedNumericalValues], got $variant.",
  [SOLANA_ERROR__CODECS__INVALID_LITERAL_UNION_VARIANT]: "Invalid literal union variant. Expected one of [$variants], got $value.",
  [SOLANA_ERROR__CODECS__INVALID_NUMBER_OF_ITEMS]: "Expected [$codecDescription] to have $expected items, got $actual.",
  [SOLANA_ERROR__CODECS__INVALID_STRING_FOR_BASE]: "Invalid value $value for base $base with alphabet $alphabet.",
  [SOLANA_ERROR__CODECS__LITERAL_UNION_DISCRIMINATOR_OUT_OF_RANGE]: "Literal union discriminator out of range. Expected a number between $minRange and $maxRange, got $discriminator.",
  [SOLANA_ERROR__CODECS__NUMBER_OUT_OF_RANGE]: "Codec [$codecDescription] expected number to be in the range [$min, $max], got $value.",
  [SOLANA_ERROR__CODECS__OFFSET_OUT_OF_RANGE]: "Codec [$codecDescription] expected offset to be in the range [0, $bytesLength], got $offset.",
  [SOLANA_ERROR__CODECS__SENTINEL_MISSING_IN_DECODED_BYTES]: "Expected sentinel [$hexSentinel] to be present in decoded bytes [$hexDecodedBytes].",
  [SOLANA_ERROR__CODECS__UNION_VARIANT_OUT_OF_RANGE]: "Union variant out of range. Expected an index between $minRange and $maxRange, got $variant.",
  [SOLANA_ERROR__CRYPTO__RANDOM_VALUES_FUNCTION_UNIMPLEMENTED]: "No random values implementation could be found.",
  [SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_ALREADY_INITIALIZED]: "instruction requires an uninitialized account",
  [SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_BORROW_FAILED]: "instruction tries to borrow reference for an account which is already borrowed",
  [SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_BORROW_OUTSTANDING]: "instruction left account with an outstanding borrowed reference",
  [SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_DATA_SIZE_CHANGED]: "program other than the account's owner changed the size of the account data",
  [SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_DATA_TOO_SMALL]: "account data too small for instruction",
  [SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_NOT_EXECUTABLE]: "instruction expected an executable account",
  [SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_NOT_RENT_EXEMPT]: "An account does not have enough lamports to be rent-exempt",
  [SOLANA_ERROR__INSTRUCTION_ERROR__ARITHMETIC_OVERFLOW]: "Program arithmetic overflowed",
  [SOLANA_ERROR__INSTRUCTION_ERROR__BORSH_IO_ERROR]: "Failed to serialize or deserialize account data: $encodedData",
  [SOLANA_ERROR__INSTRUCTION_ERROR__BUILTIN_PROGRAMS_MUST_CONSUME_COMPUTE_UNITS]: "Builtin programs must consume compute units",
  [SOLANA_ERROR__INSTRUCTION_ERROR__CALL_DEPTH]: "Cross-program invocation call depth too deep",
  [SOLANA_ERROR__INSTRUCTION_ERROR__COMPUTATIONAL_BUDGET_EXCEEDED]: "Computational budget exceeded",
  [SOLANA_ERROR__INSTRUCTION_ERROR__CUSTOM]: "custom program error: #$code",
  [SOLANA_ERROR__INSTRUCTION_ERROR__DUPLICATE_ACCOUNT_INDEX]: "instruction contains duplicate accounts",
  [SOLANA_ERROR__INSTRUCTION_ERROR__DUPLICATE_ACCOUNT_OUT_OF_SYNC]: "instruction modifications of multiply-passed account differ",
  [SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_ACCOUNT_NOT_RENT_EXEMPT]: "executable accounts must be rent exempt",
  [SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_DATA_MODIFIED]: "instruction changed executable accounts data",
  [SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_LAMPORT_CHANGE]: "instruction changed the balance of an executable account",
  [SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_MODIFIED]: "instruction changed executable bit of an account",
  [SOLANA_ERROR__INSTRUCTION_ERROR__EXTERNAL_ACCOUNT_DATA_MODIFIED]: "instruction modified data of an account it does not own",
  [SOLANA_ERROR__INSTRUCTION_ERROR__EXTERNAL_ACCOUNT_LAMPORT_SPEND]: "instruction spent from the balance of an account it does not own",
  [SOLANA_ERROR__INSTRUCTION_ERROR__GENERIC_ERROR]: "generic instruction error",
  [SOLANA_ERROR__INSTRUCTION_ERROR__ILLEGAL_OWNER]: "Provided owner is not allowed",
  [SOLANA_ERROR__INSTRUCTION_ERROR__IMMUTABLE]: "Account is immutable",
  [SOLANA_ERROR__INSTRUCTION_ERROR__INCORRECT_AUTHORITY]: "Incorrect authority provided",
  [SOLANA_ERROR__INSTRUCTION_ERROR__INCORRECT_PROGRAM_ID]: "incorrect program id for instruction",
  [SOLANA_ERROR__INSTRUCTION_ERROR__INSUFFICIENT_FUNDS]: "insufficient funds for instruction",
  [SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ACCOUNT_DATA]: "invalid account data for instruction",
  [SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ACCOUNT_OWNER]: "Invalid account owner",
  [SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ARGUMENT]: "invalid program argument",
  [SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ERROR]: "program returned invalid error code",
  [SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_INSTRUCTION_DATA]: "invalid instruction data",
  [SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_REALLOC]: "Failed to reallocate account data",
  [SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_SEEDS]: "Provided seeds do not result in a valid address",
  [SOLANA_ERROR__INSTRUCTION_ERROR__MAX_ACCOUNTS_DATA_ALLOCATIONS_EXCEEDED]: "Accounts data allocations exceeded the maximum allowed per transaction",
  [SOLANA_ERROR__INSTRUCTION_ERROR__MAX_ACCOUNTS_EXCEEDED]: "Max accounts exceeded",
  [SOLANA_ERROR__INSTRUCTION_ERROR__MAX_INSTRUCTION_TRACE_LENGTH_EXCEEDED]: "Max instruction trace length exceeded",
  [SOLANA_ERROR__INSTRUCTION_ERROR__MAX_SEED_LENGTH_EXCEEDED]: "Length of the seed is too long for address generation",
  [SOLANA_ERROR__INSTRUCTION_ERROR__MISSING_ACCOUNT]: "An account required by the instruction is missing",
  [SOLANA_ERROR__INSTRUCTION_ERROR__MISSING_REQUIRED_SIGNATURE]: "missing required signature for instruction",
  [SOLANA_ERROR__INSTRUCTION_ERROR__MODIFIED_PROGRAM_ID]: "instruction illegally modified the program id of an account",
  [SOLANA_ERROR__INSTRUCTION_ERROR__NOT_ENOUGH_ACCOUNT_KEYS]: "insufficient account keys for instruction",
  [SOLANA_ERROR__INSTRUCTION_ERROR__PRIVILEGE_ESCALATION]: "Cross-program invocation with unauthorized signer or writable account",
  [SOLANA_ERROR__INSTRUCTION_ERROR__PROGRAM_ENVIRONMENT_SETUP_FAILURE]: "Failed to create program execution environment",
  [SOLANA_ERROR__INSTRUCTION_ERROR__PROGRAM_FAILED_TO_COMPILE]: "Program failed to compile",
  [SOLANA_ERROR__INSTRUCTION_ERROR__PROGRAM_FAILED_TO_COMPLETE]: "Program failed to complete",
  [SOLANA_ERROR__INSTRUCTION_ERROR__READONLY_DATA_MODIFIED]: "instruction modified data of a read-only account",
  [SOLANA_ERROR__INSTRUCTION_ERROR__READONLY_LAMPORT_CHANGE]: "instruction changed the balance of a read-only account",
  [SOLANA_ERROR__INSTRUCTION_ERROR__REENTRANCY_NOT_ALLOWED]: "Cross-program invocation reentrancy not allowed for this instruction",
  [SOLANA_ERROR__INSTRUCTION_ERROR__RENT_EPOCH_MODIFIED]: "instruction modified rent epoch of an account",
  [SOLANA_ERROR__INSTRUCTION_ERROR__UNBALANCED_INSTRUCTION]: "sum of account balances before and after instruction do not match",
  [SOLANA_ERROR__INSTRUCTION_ERROR__UNINITIALIZED_ACCOUNT]: "instruction requires an initialized account",
  [SOLANA_ERROR__INSTRUCTION_ERROR__UNKNOWN]: "",
  [SOLANA_ERROR__INSTRUCTION_ERROR__UNSUPPORTED_PROGRAM_ID]: "Unsupported program id",
  [SOLANA_ERROR__INSTRUCTION_ERROR__UNSUPPORTED_SYSVAR]: "Unsupported sysvar",
  [SOLANA_ERROR__INSTRUCTION__EXPECTED_TO_HAVE_ACCOUNTS]: "The instruction does not have any accounts.",
  [SOLANA_ERROR__INSTRUCTION__EXPECTED_TO_HAVE_DATA]: "The instruction does not have any data.",
  [SOLANA_ERROR__INSTRUCTION__PROGRAM_ID_MISMATCH]: "Expected instruction to have progress address $expectedProgramAddress, got $actualProgramAddress.",
  [SOLANA_ERROR__INVALID_BLOCKHASH_BYTE_LENGTH]: "Expected base58 encoded blockhash to decode to a byte array of length 32. Actual length: $actualLength.",
  [SOLANA_ERROR__INVALID_NONCE]: "The nonce `$expectedNonceValue` is no longer valid. It has advanced to `$actualNonceValue`",
  [SOLANA_ERROR__INVARIANT_VIOLATION__CACHED_ABORTABLE_ITERABLE_CACHE_ENTRY_MISSING]: "Invariant violation: Found no abortable iterable cache entry for key `$cacheKey`. It should be impossible to hit this error; please file an issue at https://sola.na/web3invariant",
  [SOLANA_ERROR__INVARIANT_VIOLATION__SWITCH_MUST_BE_EXHAUSTIVE]: "Invariant violation: Switch statement non-exhaustive. Received unexpected value `$unexpectedValue`. It should be impossible to hit this error; please file an issue at https://sola.na/web3invariant",
  [SOLANA_ERROR__INVARIANT_VIOLATION__WEBSOCKET_MESSAGE_ITERATOR_MUST_NOT_POLL_BEFORE_RESOLVING_EXISTING_MESSAGE_PROMISE]: "Invariant violation: WebSocket message iterator state is corrupt; iterated without first resolving existing message promise. It should be impossible to hit this error; please file an issue at https://sola.na/web3invariant",
  [SOLANA_ERROR__INVARIANT_VIOLATION__WEBSOCKET_MESSAGE_ITERATOR_STATE_MISSING]: "Invariant violation: WebSocket message iterator is missing state storage. It should be impossible to hit this error; please file an issue at https://sola.na/web3invariant",
  [SOLANA_ERROR__JSON_RPC__INTERNAL_ERROR]: "JSON-RPC error: Internal JSON-RPC error ($__serverMessage)",
  [SOLANA_ERROR__JSON_RPC__INVALID_PARAMS]: "JSON-RPC error: Invalid method parameter(s) ($__serverMessage)",
  [SOLANA_ERROR__JSON_RPC__INVALID_REQUEST]: "JSON-RPC error: The JSON sent is not a valid `Request` object ($__serverMessage)",
  [SOLANA_ERROR__JSON_RPC__METHOD_NOT_FOUND]: "JSON-RPC error: The method does not exist / is not available ($__serverMessage)",
  [SOLANA_ERROR__JSON_RPC__PARSE_ERROR]: "JSON-RPC error: An error occurred on the server while parsing the JSON text ($__serverMessage)",
  [SOLANA_ERROR__JSON_RPC__SCAN_ERROR]: "$__serverMessage",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_CLEANED_UP]: "$__serverMessage",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_NOT_AVAILABLE]: "$__serverMessage",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_STATUS_NOT_AVAILABLE_YET]: "$__serverMessage",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_KEY_EXCLUDED_FROM_SECONDARY_INDEX]: "$__serverMessage",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_LONG_TERM_STORAGE_SLOT_SKIPPED]: "$__serverMessage",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_MIN_CONTEXT_SLOT_NOT_REACHED]: "Minimum context slot has not been reached",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_NODE_UNHEALTHY]: "Node is unhealthy; behind by $numSlotsBehind slots",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_NO_SNAPSHOT]: "No snapshot",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_SEND_TRANSACTION_PREFLIGHT_FAILURE]: "Transaction simulation failed",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_SLOT_SKIPPED]: "$__serverMessage",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_HISTORY_NOT_AVAILABLE]: "Transaction history is not available from this node",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_PRECOMPILE_VERIFICATION_FAILURE]: "$__serverMessage",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_SIGNATURE_LEN_MISMATCH]: "Transaction signature length mismatch",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_SIGNATURE_VERIFICATION_FAILURE]: "Transaction signature verification failure",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_UNSUPPORTED_TRANSACTION_VERSION]: "$__serverMessage",
  [SOLANA_ERROR__KEYS__INVALID_KEY_PAIR_BYTE_LENGTH]: "Key pair bytes must be of length 64, got $byteLength.",
  [SOLANA_ERROR__KEYS__INVALID_PRIVATE_KEY_BYTE_LENGTH]: "Expected private key bytes with length 32. Actual length: $actualLength.",
  [SOLANA_ERROR__KEYS__INVALID_SIGNATURE_BYTE_LENGTH]: "Expected base58-encoded signature to decode to a byte array of length 64. Actual length: $actualLength.",
  [SOLANA_ERROR__KEYS__PUBLIC_KEY_MUST_MATCH_PRIVATE_KEY]: "The provided private key does not match the provided public key.",
  [SOLANA_ERROR__KEYS__SIGNATURE_STRING_LENGTH_OUT_OF_RANGE]: "Expected base58-encoded signature string of length in the range [64, 88]. Actual length: $actualLength.",
  [SOLANA_ERROR__LAMPORTS_OUT_OF_RANGE]: "Lamports value must be in the range [0, 2e64-1]",
  [SOLANA_ERROR__MALFORMED_BIGINT_STRING]: "`$value` cannot be parsed as a `BigInt`",
  [SOLANA_ERROR__MALFORMED_NUMBER_STRING]: "`$value` cannot be parsed as a `Number`",
  [SOLANA_ERROR__NONCE_ACCOUNT_NOT_FOUND]: "No nonce account could be found at address `$nonceAccountAddress`",
  [SOLANA_ERROR__RPC_SUBSCRIPTIONS__CANNOT_CREATE_SUBSCRIPTION_REQUEST]: "Either the notification name must end in 'Notifications' or the API must supply a subscription creator function for the notification '$notificationName' to map between the notification name and the subscribe/unsubscribe method names.",
  [SOLANA_ERROR__RPC_SUBSCRIPTIONS__EXPECTED_SERVER_SUBSCRIPTION_ID]: "Failed to obtain a subscription id from the server",
  [SOLANA_ERROR__RPC_SUBSCRIPTIONS__TRANSPORT_CLOSED_BEFORE_MESSAGE_BUFFERED]: "WebSocket was closed before payload could be added to the send buffer",
  [SOLANA_ERROR__RPC_SUBSCRIPTIONS__TRANSPORT_CONNECTION_CLOSED]: "WebSocket connection closed",
  [SOLANA_ERROR__RPC_SUBSCRIPTIONS__TRANSPORT_FAILED_TO_CONNECT]: "WebSocket failed to connect",
  [SOLANA_ERROR__RPC__INTEGER_OVERFLOW]: "The $argumentLabel argument to the `$methodName` RPC method$optionalPathLabel was `$value`. This number is unsafe for use with the Solana JSON-RPC because it exceeds `Number.MAX_SAFE_INTEGER`.",
  [SOLANA_ERROR__RPC__TRANSPORT_HTTP_ERROR]: "HTTP error ($statusCode): $message",
  [SOLANA_ERROR__RPC__TRANSPORT_HTTP_HEADER_FORBIDDEN]: "HTTP header(s) forbidden: $headers. Learn more at https://developer.mozilla.org/en-US/docs/Glossary/Forbidden_header_name.",
  [SOLANA_ERROR__SIGNER__ADDRESS_CANNOT_HAVE_MULTIPLE_SIGNERS]: "Multiple distinct signers were identified for address `$address`. Please ensure that you are using the same signer instance for each address.",
  [SOLANA_ERROR__SIGNER__EXPECTED_KEY_PAIR_SIGNER]: "The provided value does not implement the `KeyPairSigner` interface",
  [SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_MODIFYING_SIGNER]: "The provided value does not implement the `MessageModifyingSigner` interface",
  [SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_PARTIAL_SIGNER]: "The provided value does not implement the `MessagePartialSigner` interface",
  [SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_SIGNER]: "The provided value does not implement any of the `MessageSigner` interfaces",
  [SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_MODIFYING_SIGNER]: "The provided value does not implement the `TransactionModifyingSigner` interface",
  [SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_PARTIAL_SIGNER]: "The provided value does not implement the `TransactionPartialSigner` interface",
  [SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_SENDING_SIGNER]: "The provided value does not implement the `TransactionSendingSigner` interface",
  [SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_SIGNER]: "The provided value does not implement any of the `TransactionSigner` interfaces",
  [SOLANA_ERROR__SIGNER__TRANSACTION_CANNOT_HAVE_MULTIPLE_SENDING_SIGNERS]: "More than one `TransactionSendingSigner` was identified.",
  [SOLANA_ERROR__SIGNER__TRANSACTION_SENDING_SIGNER_MISSING]: "No `TransactionSendingSigner` was identified. Please provide a valid `ITransactionWithSingleSendingSigner` transaction.",
  [SOLANA_ERROR__SIGNER__WALLET_MULTISIGN_UNIMPLEMENTED]: "Wallet account signers do not support signing multiple messages/transactions in a single operation",
  [SOLANA_ERROR__SUBTLE_CRYPTO__CANNOT_EXPORT_NON_EXTRACTABLE_KEY]: "Cannot export a non-extractable key.",
  [SOLANA_ERROR__SUBTLE_CRYPTO__DIGEST_UNIMPLEMENTED]: "No digest implementation could be found.",
  [SOLANA_ERROR__SUBTLE_CRYPTO__DISALLOWED_IN_INSECURE_CONTEXT]: "Cryptographic operations are only allowed in secure browser contexts. Read more here: https://developer.mozilla.org/en-US/docs/Web/Security/Secure_Contexts.",
  [SOLANA_ERROR__SUBTLE_CRYPTO__ED25519_ALGORITHM_UNIMPLEMENTED]: "This runtime does not support the generation of Ed25519 key pairs.\n\nInstall @solana/webcrypto-ed25519-polyfill and call its `install` function before generating keys in environments that do not support Ed25519.\n\nFor a list of runtimes that currently support Ed25519 operations, visit https://github.com/WICG/webcrypto-secure-curves/issues/20.",
  [SOLANA_ERROR__SUBTLE_CRYPTO__EXPORT_FUNCTION_UNIMPLEMENTED]: "No signature verification implementation could be found.",
  [SOLANA_ERROR__SUBTLE_CRYPTO__GENERATE_FUNCTION_UNIMPLEMENTED]: "No key generation implementation could be found.",
  [SOLANA_ERROR__SUBTLE_CRYPTO__SIGN_FUNCTION_UNIMPLEMENTED]: "No signing implementation could be found.",
  [SOLANA_ERROR__SUBTLE_CRYPTO__VERIFY_FUNCTION_UNIMPLEMENTED]: "No key export implementation could be found.",
  [SOLANA_ERROR__TIMESTAMP_OUT_OF_RANGE]: "Timestamp value must be in the range [-8.64e15, 8.64e15]. `$value` given",
  [SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_BORROW_OUTSTANDING]: "Transaction processing left an account with an outstanding borrowed reference",
  [SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_IN_USE]: "Account in use",
  [SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_LOADED_TWICE]: "Account loaded twice",
  [SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_NOT_FOUND]: "Attempt to debit an account but found no record of a prior credit.",
  [SOLANA_ERROR__TRANSACTION_ERROR__ADDRESS_LOOKUP_TABLE_NOT_FOUND]: "Transaction loads an address table account that doesn't exist",
  [SOLANA_ERROR__TRANSACTION_ERROR__ALREADY_PROCESSED]: "This transaction has already been processed",
  [SOLANA_ERROR__TRANSACTION_ERROR__BLOCKHASH_NOT_FOUND]: "Blockhash not found",
  [SOLANA_ERROR__TRANSACTION_ERROR__CALL_CHAIN_TOO_DEEP]: "Loader call chain is too deep",
  [SOLANA_ERROR__TRANSACTION_ERROR__CLUSTER_MAINTENANCE]: "Transactions are currently disabled due to cluster maintenance",
  [SOLANA_ERROR__TRANSACTION_ERROR__DUPLICATE_INSTRUCTION]: "Transaction contains a duplicate instruction ($index) that is not allowed",
  [SOLANA_ERROR__TRANSACTION_ERROR__INSUFFICIENT_FUNDS_FOR_FEE]: "Insufficient funds for fee",
  [SOLANA_ERROR__TRANSACTION_ERROR__INSUFFICIENT_FUNDS_FOR_RENT]: "Transaction results in an account ($accountIndex) with insufficient funds for rent",
  [SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ACCOUNT_FOR_FEE]: "This account may not be used to pay transaction fees",
  [SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ACCOUNT_INDEX]: "Transaction contains an invalid account reference",
  [SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ADDRESS_LOOKUP_TABLE_DATA]: "Transaction loads an address table account with invalid data",
  [SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ADDRESS_LOOKUP_TABLE_INDEX]: "Transaction address table lookup uses an invalid index",
  [SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ADDRESS_LOOKUP_TABLE_OWNER]: "Transaction loads an address table account with an invalid owner",
  [SOLANA_ERROR__TRANSACTION_ERROR__INVALID_LOADED_ACCOUNTS_DATA_SIZE_LIMIT]: "LoadedAccountsDataSizeLimit set for transaction must be greater than 0.",
  [SOLANA_ERROR__TRANSACTION_ERROR__INVALID_PROGRAM_FOR_EXECUTION]: "This program may not be used for executing instructions",
  [SOLANA_ERROR__TRANSACTION_ERROR__INVALID_RENT_PAYING_ACCOUNT]: "Transaction leaves an account with a lower balance than rent-exempt minimum",
  [SOLANA_ERROR__TRANSACTION_ERROR__INVALID_WRITABLE_ACCOUNT]: "Transaction loads a writable account that cannot be written",
  [SOLANA_ERROR__TRANSACTION_ERROR__MAX_LOADED_ACCOUNTS_DATA_SIZE_EXCEEDED]: "Transaction exceeded max loaded accounts data size cap",
  [SOLANA_ERROR__TRANSACTION_ERROR__MISSING_SIGNATURE_FOR_FEE]: "Transaction requires a fee but has no signature present",
  [SOLANA_ERROR__TRANSACTION_ERROR__PROGRAM_ACCOUNT_NOT_FOUND]: "Attempt to load a program that does not exist",
  [SOLANA_ERROR__TRANSACTION_ERROR__PROGRAM_EXECUTION_TEMPORARILY_RESTRICTED]: "Execution of the program referenced by account at index $accountIndex is temporarily restricted.",
  [SOLANA_ERROR__TRANSACTION_ERROR__RESANITIZATION_NEEDED]: "ResanitizationNeeded",
  [SOLANA_ERROR__TRANSACTION_ERROR__SANITIZE_FAILURE]: "Transaction failed to sanitize accounts offsets correctly",
  [SOLANA_ERROR__TRANSACTION_ERROR__SIGNATURE_FAILURE]: "Transaction did not pass signature verification",
  [SOLANA_ERROR__TRANSACTION_ERROR__TOO_MANY_ACCOUNT_LOCKS]: "Transaction locked too many accounts",
  [SOLANA_ERROR__TRANSACTION_ERROR__UNBALANCED_TRANSACTION]: "Sum of account balances before and after transaction do not match",
  [SOLANA_ERROR__TRANSACTION_ERROR__UNKNOWN]: "The transaction failed with the error `$errorName`",
  [SOLANA_ERROR__TRANSACTION_ERROR__UNSUPPORTED_VERSION]: "Transaction version is unsupported",
  [SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_ACCOUNT_DATA_BLOCK_LIMIT]: "Transaction would exceed account data limit within the block",
  [SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_ACCOUNT_DATA_TOTAL_LIMIT]: "Transaction would exceed total account data limit",
  [SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_MAX_ACCOUNT_COST_LIMIT]: "Transaction would exceed max account limit within the block",
  [SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_MAX_BLOCK_COST_LIMIT]: "Transaction would exceed max Block Cost Limit",
  [SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_MAX_VOTE_COST_LIMIT]: "Transaction would exceed max Vote Cost Limit",
  [SOLANA_ERROR__TRANSACTION__ADDRESSES_CANNOT_SIGN_TRANSACTION]: "Attempted to sign a transaction with an address that is not a signer for it",
  [SOLANA_ERROR__TRANSACTION__ADDRESS_MISSING]: "Transaction is missing an address at index: $index.",
  [SOLANA_ERROR__TRANSACTION__CANNOT_ENCODE_WITH_EMPTY_SIGNATURES]: "Transaction has no expected signers therefore it cannot be encoded",
  [SOLANA_ERROR__TRANSACTION__EXPECTED_BLOCKHASH_LIFETIME]: "Transaction does not have a blockhash lifetime",
  [SOLANA_ERROR__TRANSACTION__EXPECTED_NONCE_LIFETIME]: "Transaction is not a durable nonce transaction",
  [SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_ADDRESS_LOOKUP_TABLE_CONTENTS_MISSING]: "Contents of these address lookup tables unknown: $lookupTableAddresses",
  [SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_ADDRESS_LOOKUP_TABLE_INDEX_OUT_OF_RANGE]: "Lookup of address at index $highestRequestedIndex failed for lookup table `$lookupTableAddress`. Highest known index is $highestKnownIndex. The lookup table may have been extended since its contents were retrieved",
  [SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_FEE_PAYER_MISSING]: "No fee payer set in CompiledTransaction",
  [SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_INSTRUCTION_PROGRAM_ADDRESS_NOT_FOUND]: "Could not find program address at index $index",
  [SOLANA_ERROR__TRANSACTION__FAILED_TO_ESTIMATE_COMPUTE_LIMIT]: "Failed to estimate the compute unit consumption for this transaction message. This is likely because simulating the transaction failed. Inspect the `cause` property of this error to learn more",
  [SOLANA_ERROR__TRANSACTION__FEE_PAYER_MISSING]: "Transaction is missing a fee payer.",
  [SOLANA_ERROR__TRANSACTION__FEE_PAYER_SIGNATURE_MISSING]: "Could not determine this transaction's signature. Make sure that the transaction has been signed by its fee payer.",
  [SOLANA_ERROR__TRANSACTION__INVALID_NONCE_TRANSACTION_FIRST_INSTRUCTION_MUST_BE_ADVANCE_NONCE]: "Transaction first instruction is not advance nonce account instruction.",
  [SOLANA_ERROR__TRANSACTION__INVALID_NONCE_TRANSACTION_INSTRUCTIONS_MISSING]: "Transaction with no instructions cannot be durable nonce transaction.",
  [SOLANA_ERROR__TRANSACTION__INVOKED_PROGRAMS_CANNOT_PAY_FEES]: "This transaction includes an address (`$programAddress`) which is both invoked and set as the fee payer. Program addresses may not pay fees",
  [SOLANA_ERROR__TRANSACTION__INVOKED_PROGRAMS_MUST_NOT_BE_WRITABLE]: "This transaction includes an address (`$programAddress`) which is both invoked and marked writable. Program addresses may not be writable",
  [SOLANA_ERROR__TRANSACTION__MESSAGE_SIGNATURES_MISMATCH]: "The transaction message expected the transaction to have $signerAddressesLength signatures, got $signaturesLength.",
  [SOLANA_ERROR__TRANSACTION__SIGNATURES_MISSING]: "Transaction is missing signatures for addresses: $addresses.",
  [SOLANA_ERROR__TRANSACTION__VERSION_NUMBER_OUT_OF_RANGE]: "Transaction version must be in the range [0, 127]. `$actualVersion` given"
};
var START_INDEX = "i";
var TYPE = "t";
function getHumanReadableErrorMessage(code, context = {}) {
  const messageFormatString = SolanaErrorMessages[code];
  if (messageFormatString.length === 0) {
    return "";
  }
  let state;
  function commitStateUpTo(endIndex) {
    if (state[TYPE] === 2) {
      const variableName = messageFormatString.slice(state[START_INDEX] + 1, endIndex);
      fragments.push(
        variableName in context ? `${context[variableName]}` : `$${variableName}`
      );
    } else if (state[TYPE] === 1) {
      fragments.push(messageFormatString.slice(state[START_INDEX], endIndex));
    }
  }
  const fragments = [];
  messageFormatString.split("").forEach((char, ii) => {
    if (ii === 0) {
      state = {
        [START_INDEX]: 0,
        [TYPE]: messageFormatString[0] === "\\" ? 0 : messageFormatString[0] === "$" ? 2 : 1
        /* Text */
      };
      return;
    }
    let nextState;
    switch (state[TYPE]) {
      case 0:
        nextState = {
          [START_INDEX]: ii,
          [TYPE]: 1
          /* Text */
        };
        break;
      case 1:
        if (char === "\\") {
          nextState = {
            [START_INDEX]: ii,
            [TYPE]: 0
            /* EscapeSequence */
          };
        } else if (char === "$") {
          nextState = {
            [START_INDEX]: ii,
            [TYPE]: 2
            /* Variable */
          };
        }
        break;
      case 2:
        if (char === "\\") {
          nextState = {
            [START_INDEX]: ii,
            [TYPE]: 0
            /* EscapeSequence */
          };
        } else if (char === "$") {
          nextState = {
            [START_INDEX]: ii,
            [TYPE]: 2
            /* Variable */
          };
        } else if (!char.match(/\w/)) {
          nextState = {
            [START_INDEX]: ii,
            [TYPE]: 1
            /* Text */
          };
        }
        break;
    }
    if (nextState) {
      if (state !== nextState) {
        commitStateUpTo(ii);
      }
      state = nextState;
    }
  });
  commitStateUpTo();
  return fragments.join("");
}
function getErrorMessage(code, context = {}) {
  if (true) {
    return getHumanReadableErrorMessage(code, context);
  } else {
    let decodingAdviceMessage = `Solana error #${code}; Decode this error by running \`npx @solana/errors decode -- ${code}`;
    if (Object.keys(context).length) {
      decodingAdviceMessage += ` '${encodeContextObject(context)}'`;
    }
    return `${decodingAdviceMessage}\``;
  }
}
var SolanaError = class extends Error {
  constructor(...[code, contextAndErrorOptions]) {
    let context;
    let errorOptions;
    if (contextAndErrorOptions) {
      const { cause, ...contextRest } = contextAndErrorOptions;
      if (cause) {
        errorOptions = { cause };
      }
      if (Object.keys(contextRest).length > 0) {
        context = contextRest;
      }
    }
    const message = getErrorMessage(code, context);
    super(message, errorOptions);
    __publicField(this, "cause", this.cause);
    __publicField(this, "context");
    this.context = {
      __code: code,
      ...context
    };
    this.name = "SolanaError";
  }
};

// node_modules/@solana/codecs/node_modules/@solana/codecs-core/dist/index.browser.mjs
var padBytes = (bytes, length) => {
  if (bytes.length >= length)
    return bytes;
  const paddedBytes = new Uint8Array(length).fill(0);
  paddedBytes.set(bytes);
  return paddedBytes;
};
var fixBytes = (bytes, length) => padBytes(bytes.length <= length ? bytes : bytes.slice(0, length), length);
function getEncodedSize(value, encoder) {
  return "fixedSize" in encoder ? encoder.fixedSize : encoder.getSizeFromValue(value);
}
function createEncoder(encoder) {
  return Object.freeze({
    ...encoder,
    encode: (value) => {
      const bytes = new Uint8Array(getEncodedSize(value, encoder));
      encoder.write(value, bytes, 0);
      return bytes;
    }
  });
}
function createDecoder(decoder) {
  return Object.freeze({
    ...decoder,
    decode: (bytes, offset = 0) => decoder.read(bytes, offset)[0]
  });
}
function isFixedSize(codec) {
  return "fixedSize" in codec && typeof codec.fixedSize === "number";
}
function isVariableSize(codec) {
  return !isFixedSize(codec);
}
function combineCodec(encoder, decoder) {
  if (isFixedSize(encoder) !== isFixedSize(decoder)) {
    throw new SolanaError(SOLANA_ERROR__CODECS__ENCODER_DECODER_SIZE_COMPATIBILITY_MISMATCH);
  }
  if (isFixedSize(encoder) && isFixedSize(decoder) && encoder.fixedSize !== decoder.fixedSize) {
    throw new SolanaError(SOLANA_ERROR__CODECS__ENCODER_DECODER_FIXED_SIZE_MISMATCH, {
      decoderFixedSize: decoder.fixedSize,
      encoderFixedSize: encoder.fixedSize
    });
  }
  if (!isFixedSize(encoder) && !isFixedSize(decoder) && encoder.maxSize !== decoder.maxSize) {
    throw new SolanaError(SOLANA_ERROR__CODECS__ENCODER_DECODER_MAX_SIZE_MISMATCH, {
      decoderMaxSize: decoder.maxSize,
      encoderMaxSize: encoder.maxSize
    });
  }
  return {
    ...decoder,
    ...encoder,
    decode: decoder.decode,
    encode: encoder.encode,
    read: decoder.read,
    write: encoder.write
  };
}
function assertByteArrayIsNotEmptyForCodec(codecDescription, bytes, offset = 0) {
  if (bytes.length - offset <= 0) {
    throw new SolanaError(SOLANA_ERROR__CODECS__CANNOT_DECODE_EMPTY_BYTE_ARRAY, {
      codecDescription
    });
  }
}
function assertByteArrayHasEnoughBytesForCodec(codecDescription, expected, bytes, offset = 0) {
  const bytesLength = bytes.length - offset;
  if (bytesLength < expected) {
    throw new SolanaError(SOLANA_ERROR__CODECS__INVALID_BYTE_LENGTH, {
      bytesLength,
      codecDescription,
      expected
    });
  }
}
function addEncoderSizePrefix(encoder, prefix) {
  const write = (value, bytes, offset) => {
    const encoderBytes = encoder.encode(value);
    offset = prefix.write(encoderBytes.length, bytes, offset);
    bytes.set(encoderBytes, offset);
    return offset + encoderBytes.length;
  };
  if (isFixedSize(prefix) && isFixedSize(encoder)) {
    return createEncoder({ ...encoder, fixedSize: prefix.fixedSize + encoder.fixedSize, write });
  }
  const prefixMaxSize = isFixedSize(prefix) ? prefix.fixedSize : prefix.maxSize ?? null;
  const encoderMaxSize = isFixedSize(encoder) ? encoder.fixedSize : encoder.maxSize ?? null;
  const maxSize = prefixMaxSize !== null && encoderMaxSize !== null ? prefixMaxSize + encoderMaxSize : null;
  return createEncoder({
    ...encoder,
    ...maxSize !== null ? { maxSize } : {},
    getSizeFromValue: (value) => {
      const encoderSize = getEncodedSize(value, encoder);
      return getEncodedSize(encoderSize, prefix) + encoderSize;
    },
    write
  });
}
function addDecoderSizePrefix(decoder, prefix) {
  const read = (bytes, offset) => {
    const [bigintSize, decoderOffset] = prefix.read(bytes, offset);
    const size = Number(bigintSize);
    offset = decoderOffset;
    if (offset > 0 || bytes.length > size) {
      bytes = bytes.slice(offset, offset + size);
    }
    assertByteArrayHasEnoughBytesForCodec("addDecoderSizePrefix", size, bytes);
    return [decoder.decode(bytes), offset + size];
  };
  if (isFixedSize(prefix) && isFixedSize(decoder)) {
    return createDecoder({ ...decoder, fixedSize: prefix.fixedSize + decoder.fixedSize, read });
  }
  const prefixMaxSize = isFixedSize(prefix) ? prefix.fixedSize : prefix.maxSize ?? null;
  const decoderMaxSize = isFixedSize(decoder) ? decoder.fixedSize : decoder.maxSize ?? null;
  const maxSize = prefixMaxSize !== null && decoderMaxSize !== null ? prefixMaxSize + decoderMaxSize : null;
  return createDecoder({ ...decoder, ...maxSize !== null ? { maxSize } : {}, read });
}
function addCodecSizePrefix(codec, prefix) {
  return combineCodec(addEncoderSizePrefix(codec, prefix), addDecoderSizePrefix(codec, prefix));
}
function fixEncoderSize(encoder, fixedBytes) {
  return createEncoder({
    fixedSize: fixedBytes,
    write: (value, bytes, offset) => {
      const variableByteArray = encoder.encode(value);
      const fixedByteArray = variableByteArray.length > fixedBytes ? variableByteArray.slice(0, fixedBytes) : variableByteArray;
      bytes.set(fixedByteArray, offset);
      return offset + fixedBytes;
    }
  });
}
function fixDecoderSize(decoder, fixedBytes) {
  return createDecoder({
    fixedSize: fixedBytes,
    read: (bytes, offset) => {
      assertByteArrayHasEnoughBytesForCodec("fixCodecSize", fixedBytes, bytes, offset);
      if (offset > 0 || bytes.length > fixedBytes) {
        bytes = bytes.slice(offset, offset + fixedBytes);
      }
      if (isFixedSize(decoder)) {
        bytes = fixBytes(bytes, decoder.fixedSize);
      }
      const [value] = decoder.read(bytes, 0);
      return [value, offset + fixedBytes];
    }
  });
}
function fixCodecSize(codec, fixedBytes) {
  return combineCodec(fixEncoderSize(codec, fixedBytes), fixDecoderSize(codec, fixedBytes));
}
function transformEncoder(encoder, unmap) {
  return createEncoder({
    ...isVariableSize(encoder) ? { ...encoder, getSizeFromValue: (value) => encoder.getSizeFromValue(unmap(value)) } : encoder,
    write: (value, bytes, offset) => encoder.write(unmap(value), bytes, offset)
  });
}

// node_modules/@solana/codecs-data-structures/node_modules/@solana/errors/dist/index.browser.mjs
var SOLANA_ERROR__BLOCK_HEIGHT_EXCEEDED2 = 1;
var SOLANA_ERROR__INVALID_NONCE2 = 2;
var SOLANA_ERROR__NONCE_ACCOUNT_NOT_FOUND2 = 3;
var SOLANA_ERROR__BLOCKHASH_STRING_LENGTH_OUT_OF_RANGE2 = 4;
var SOLANA_ERROR__INVALID_BLOCKHASH_BYTE_LENGTH2 = 5;
var SOLANA_ERROR__LAMPORTS_OUT_OF_RANGE2 = 6;
var SOLANA_ERROR__MALFORMED_BIGINT_STRING2 = 7;
var SOLANA_ERROR__MALFORMED_NUMBER_STRING2 = 8;
var SOLANA_ERROR__TIMESTAMP_OUT_OF_RANGE2 = 9;
var SOLANA_ERROR__JSON_RPC__PARSE_ERROR2 = -32700;
var SOLANA_ERROR__JSON_RPC__INTERNAL_ERROR2 = -32603;
var SOLANA_ERROR__JSON_RPC__INVALID_PARAMS2 = -32602;
var SOLANA_ERROR__JSON_RPC__METHOD_NOT_FOUND2 = -32601;
var SOLANA_ERROR__JSON_RPC__INVALID_REQUEST2 = -32600;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_MIN_CONTEXT_SLOT_NOT_REACHED2 = -32016;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_UNSUPPORTED_TRANSACTION_VERSION2 = -32015;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_STATUS_NOT_AVAILABLE_YET2 = -32014;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_SIGNATURE_LEN_MISMATCH2 = -32013;
var SOLANA_ERROR__JSON_RPC__SCAN_ERROR2 = -32012;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_HISTORY_NOT_AVAILABLE2 = -32011;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_KEY_EXCLUDED_FROM_SECONDARY_INDEX2 = -32010;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_LONG_TERM_STORAGE_SLOT_SKIPPED2 = -32009;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_NO_SNAPSHOT2 = -32008;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_SLOT_SKIPPED2 = -32007;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_PRECOMPILE_VERIFICATION_FAILURE2 = -32006;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_NODE_UNHEALTHY2 = -32005;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_NOT_AVAILABLE2 = -32004;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_SIGNATURE_VERIFICATION_FAILURE2 = -32003;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_SEND_TRANSACTION_PREFLIGHT_FAILURE2 = -32002;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_CLEANED_UP2 = -32001;
var SOLANA_ERROR__ADDRESSES__INVALID_BYTE_LENGTH2 = 28e5;
var SOLANA_ERROR__ADDRESSES__STRING_LENGTH_OUT_OF_RANGE2 = 2800001;
var SOLANA_ERROR__ADDRESSES__INVALID_BASE58_ENCODED_ADDRESS2 = 2800002;
var SOLANA_ERROR__ADDRESSES__INVALID_ED25519_PUBLIC_KEY2 = 2800003;
var SOLANA_ERROR__ADDRESSES__MALFORMED_PDA2 = 2800004;
var SOLANA_ERROR__ADDRESSES__PDA_BUMP_SEED_OUT_OF_RANGE2 = 2800005;
var SOLANA_ERROR__ADDRESSES__MAX_NUMBER_OF_PDA_SEEDS_EXCEEDED2 = 2800006;
var SOLANA_ERROR__ADDRESSES__MAX_PDA_SEED_LENGTH_EXCEEDED2 = 2800007;
var SOLANA_ERROR__ADDRESSES__INVALID_SEEDS_POINT_ON_CURVE2 = 2800008;
var SOLANA_ERROR__ADDRESSES__FAILED_TO_FIND_VIABLE_PDA_BUMP_SEED2 = 2800009;
var SOLANA_ERROR__ADDRESSES__PDA_ENDS_WITH_PDA_MARKER2 = 2800010;
var SOLANA_ERROR__ACCOUNTS__ACCOUNT_NOT_FOUND2 = 323e4;
var SOLANA_ERROR__ACCOUNTS__ONE_OR_MORE_ACCOUNTS_NOT_FOUND2 = ********;
var SOLANA_ERROR__ACCOUNTS__FAILED_TO_DECODE_ACCOUNT2 = 3230002;
var SOLANA_ERROR__ACCOUNTS__EXPECTED_DECODED_ACCOUNT2 = 3230003;
var SOLANA_ERROR__ACCOUNTS__EXPECTED_ALL_ACCOUNTS_TO_BE_DECODED2 = 3230004;
var SOLANA_ERROR__SUBTLE_CRYPTO__DISALLOWED_IN_INSECURE_CONTEXT2 = 361e4;
var SOLANA_ERROR__SUBTLE_CRYPTO__DIGEST_UNIMPLEMENTED2 = 3610001;
var SOLANA_ERROR__SUBTLE_CRYPTO__ED25519_ALGORITHM_UNIMPLEMENTED2 = 3610002;
var SOLANA_ERROR__SUBTLE_CRYPTO__EXPORT_FUNCTION_UNIMPLEMENTED2 = 3610003;
var SOLANA_ERROR__SUBTLE_CRYPTO__GENERATE_FUNCTION_UNIMPLEMENTED2 = 3610004;
var SOLANA_ERROR__SUBTLE_CRYPTO__SIGN_FUNCTION_UNIMPLEMENTED2 = 3610005;
var SOLANA_ERROR__SUBTLE_CRYPTO__VERIFY_FUNCTION_UNIMPLEMENTED2 = 3610006;
var SOLANA_ERROR__SUBTLE_CRYPTO__CANNOT_EXPORT_NON_EXTRACTABLE_KEY2 = 3610007;
var SOLANA_ERROR__CRYPTO__RANDOM_VALUES_FUNCTION_UNIMPLEMENTED2 = 3611e3;
var SOLANA_ERROR__KEYS__INVALID_KEY_PAIR_BYTE_LENGTH2 = 3704e3;
var SOLANA_ERROR__KEYS__INVALID_PRIVATE_KEY_BYTE_LENGTH2 = 3704001;
var SOLANA_ERROR__KEYS__INVALID_SIGNATURE_BYTE_LENGTH2 = 3704002;
var SOLANA_ERROR__KEYS__SIGNATURE_STRING_LENGTH_OUT_OF_RANGE2 = 3704003;
var SOLANA_ERROR__KEYS__PUBLIC_KEY_MUST_MATCH_PRIVATE_KEY2 = 3704004;
var SOLANA_ERROR__INSTRUCTION__EXPECTED_TO_HAVE_ACCOUNTS2 = 4128e3;
var SOLANA_ERROR__INSTRUCTION__EXPECTED_TO_HAVE_DATA2 = 4128001;
var SOLANA_ERROR__INSTRUCTION__PROGRAM_ID_MISMATCH2 = 4128002;
var SOLANA_ERROR__INSTRUCTION_ERROR__UNKNOWN2 = 4615e3;
var SOLANA_ERROR__INSTRUCTION_ERROR__GENERIC_ERROR2 = 4615001;
var SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ARGUMENT2 = 4615002;
var SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_INSTRUCTION_DATA2 = 4615003;
var SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ACCOUNT_DATA2 = 4615004;
var SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_DATA_TOO_SMALL2 = 4615005;
var SOLANA_ERROR__INSTRUCTION_ERROR__INSUFFICIENT_FUNDS2 = 4615006;
var SOLANA_ERROR__INSTRUCTION_ERROR__INCORRECT_PROGRAM_ID2 = 4615007;
var SOLANA_ERROR__INSTRUCTION_ERROR__MISSING_REQUIRED_SIGNATURE2 = 4615008;
var SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_ALREADY_INITIALIZED2 = 4615009;
var SOLANA_ERROR__INSTRUCTION_ERROR__UNINITIALIZED_ACCOUNT2 = 4615010;
var SOLANA_ERROR__INSTRUCTION_ERROR__UNBALANCED_INSTRUCTION2 = 4615011;
var SOLANA_ERROR__INSTRUCTION_ERROR__MODIFIED_PROGRAM_ID2 = 4615012;
var SOLANA_ERROR__INSTRUCTION_ERROR__EXTERNAL_ACCOUNT_LAMPORT_SPEND2 = 4615013;
var SOLANA_ERROR__INSTRUCTION_ERROR__EXTERNAL_ACCOUNT_DATA_MODIFIED2 = 4615014;
var SOLANA_ERROR__INSTRUCTION_ERROR__READONLY_LAMPORT_CHANGE2 = 4615015;
var SOLANA_ERROR__INSTRUCTION_ERROR__READONLY_DATA_MODIFIED2 = 4615016;
var SOLANA_ERROR__INSTRUCTION_ERROR__DUPLICATE_ACCOUNT_INDEX2 = 4615017;
var SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_MODIFIED2 = 4615018;
var SOLANA_ERROR__INSTRUCTION_ERROR__RENT_EPOCH_MODIFIED2 = 4615019;
var SOLANA_ERROR__INSTRUCTION_ERROR__NOT_ENOUGH_ACCOUNT_KEYS2 = 4615020;
var SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_DATA_SIZE_CHANGED2 = 4615021;
var SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_NOT_EXECUTABLE2 = 4615022;
var SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_BORROW_FAILED2 = 4615023;
var SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_BORROW_OUTSTANDING2 = 4615024;
var SOLANA_ERROR__INSTRUCTION_ERROR__DUPLICATE_ACCOUNT_OUT_OF_SYNC2 = 4615025;
var SOLANA_ERROR__INSTRUCTION_ERROR__CUSTOM2 = 4615026;
var SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ERROR2 = 4615027;
var SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_DATA_MODIFIED2 = 4615028;
var SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_LAMPORT_CHANGE2 = 4615029;
var SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_ACCOUNT_NOT_RENT_EXEMPT2 = 4615030;
var SOLANA_ERROR__INSTRUCTION_ERROR__UNSUPPORTED_PROGRAM_ID2 = 4615031;
var SOLANA_ERROR__INSTRUCTION_ERROR__CALL_DEPTH2 = 4615032;
var SOLANA_ERROR__INSTRUCTION_ERROR__MISSING_ACCOUNT2 = 4615033;
var SOLANA_ERROR__INSTRUCTION_ERROR__REENTRANCY_NOT_ALLOWED2 = 4615034;
var SOLANA_ERROR__INSTRUCTION_ERROR__MAX_SEED_LENGTH_EXCEEDED2 = 4615035;
var SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_SEEDS2 = 4615036;
var SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_REALLOC2 = 4615037;
var SOLANA_ERROR__INSTRUCTION_ERROR__COMPUTATIONAL_BUDGET_EXCEEDED2 = 4615038;
var SOLANA_ERROR__INSTRUCTION_ERROR__PRIVILEGE_ESCALATION2 = 4615039;
var SOLANA_ERROR__INSTRUCTION_ERROR__PROGRAM_ENVIRONMENT_SETUP_FAILURE2 = 4615040;
var SOLANA_ERROR__INSTRUCTION_ERROR__PROGRAM_FAILED_TO_COMPLETE2 = 4615041;
var SOLANA_ERROR__INSTRUCTION_ERROR__PROGRAM_FAILED_TO_COMPILE2 = 4615042;
var SOLANA_ERROR__INSTRUCTION_ERROR__IMMUTABLE2 = 4615043;
var SOLANA_ERROR__INSTRUCTION_ERROR__INCORRECT_AUTHORITY2 = 4615044;
var SOLANA_ERROR__INSTRUCTION_ERROR__BORSH_IO_ERROR2 = 4615045;
var SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_NOT_RENT_EXEMPT2 = 4615046;
var SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ACCOUNT_OWNER2 = 4615047;
var SOLANA_ERROR__INSTRUCTION_ERROR__ARITHMETIC_OVERFLOW2 = 4615048;
var SOLANA_ERROR__INSTRUCTION_ERROR__UNSUPPORTED_SYSVAR2 = 4615049;
var SOLANA_ERROR__INSTRUCTION_ERROR__ILLEGAL_OWNER2 = 4615050;
var SOLANA_ERROR__INSTRUCTION_ERROR__MAX_ACCOUNTS_DATA_ALLOCATIONS_EXCEEDED2 = 4615051;
var SOLANA_ERROR__INSTRUCTION_ERROR__MAX_ACCOUNTS_EXCEEDED2 = 4615052;
var SOLANA_ERROR__INSTRUCTION_ERROR__MAX_INSTRUCTION_TRACE_LENGTH_EXCEEDED2 = 4615053;
var SOLANA_ERROR__INSTRUCTION_ERROR__BUILTIN_PROGRAMS_MUST_CONSUME_COMPUTE_UNITS2 = 4615054;
var SOLANA_ERROR__SIGNER__ADDRESS_CANNOT_HAVE_MULTIPLE_SIGNERS2 = 5508e3;
var SOLANA_ERROR__SIGNER__EXPECTED_KEY_PAIR_SIGNER2 = 5508001;
var SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_SIGNER2 = 5508002;
var SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_MODIFYING_SIGNER2 = 5508003;
var SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_PARTIAL_SIGNER2 = 5508004;
var SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_SIGNER2 = 5508005;
var SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_MODIFYING_SIGNER2 = 5508006;
var SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_PARTIAL_SIGNER2 = 5508007;
var SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_SENDING_SIGNER2 = 5508008;
var SOLANA_ERROR__SIGNER__TRANSACTION_CANNOT_HAVE_MULTIPLE_SENDING_SIGNERS2 = 5508009;
var SOLANA_ERROR__SIGNER__TRANSACTION_SENDING_SIGNER_MISSING2 = 5508010;
var SOLANA_ERROR__SIGNER__WALLET_MULTISIGN_UNIMPLEMENTED2 = 5508011;
var SOLANA_ERROR__TRANSACTION__INVOKED_PROGRAMS_CANNOT_PAY_FEES2 = 5663e3;
var SOLANA_ERROR__TRANSACTION__INVOKED_PROGRAMS_MUST_NOT_BE_WRITABLE2 = 5663001;
var SOLANA_ERROR__TRANSACTION__EXPECTED_BLOCKHASH_LIFETIME2 = 5663002;
var SOLANA_ERROR__TRANSACTION__EXPECTED_NONCE_LIFETIME2 = 5663003;
var SOLANA_ERROR__TRANSACTION__VERSION_NUMBER_OUT_OF_RANGE2 = 5663004;
var SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_ADDRESS_LOOKUP_TABLE_CONTENTS_MISSING2 = 5663005;
var SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_ADDRESS_LOOKUP_TABLE_INDEX_OUT_OF_RANGE2 = 5663006;
var SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_INSTRUCTION_PROGRAM_ADDRESS_NOT_FOUND2 = 5663007;
var SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_FEE_PAYER_MISSING2 = 5663008;
var SOLANA_ERROR__TRANSACTION__SIGNATURES_MISSING2 = 5663009;
var SOLANA_ERROR__TRANSACTION__ADDRESS_MISSING2 = 5663010;
var SOLANA_ERROR__TRANSACTION__FEE_PAYER_MISSING2 = 5663011;
var SOLANA_ERROR__TRANSACTION__FEE_PAYER_SIGNATURE_MISSING2 = 5663012;
var SOLANA_ERROR__TRANSACTION__INVALID_NONCE_TRANSACTION_INSTRUCTIONS_MISSING2 = 5663013;
var SOLANA_ERROR__TRANSACTION__INVALID_NONCE_TRANSACTION_FIRST_INSTRUCTION_MUST_BE_ADVANCE_NONCE2 = 5663014;
var SOLANA_ERROR__TRANSACTION__ADDRESSES_CANNOT_SIGN_TRANSACTION2 = 5663015;
var SOLANA_ERROR__TRANSACTION__CANNOT_ENCODE_WITH_EMPTY_SIGNATURES2 = 5663016;
var SOLANA_ERROR__TRANSACTION__MESSAGE_SIGNATURES_MISMATCH2 = 5663017;
var SOLANA_ERROR__TRANSACTION__FAILED_TO_ESTIMATE_COMPUTE_LIMIT2 = 5663018;
var SOLANA_ERROR__TRANSACTION_ERROR__UNKNOWN2 = 705e4;
var SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_IN_USE2 = 7050001;
var SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_LOADED_TWICE2 = 7050002;
var SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_NOT_FOUND2 = 7050003;
var SOLANA_ERROR__TRANSACTION_ERROR__PROGRAM_ACCOUNT_NOT_FOUND2 = 7050004;
var SOLANA_ERROR__TRANSACTION_ERROR__INSUFFICIENT_FUNDS_FOR_FEE2 = 7050005;
var SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ACCOUNT_FOR_FEE2 = 7050006;
var SOLANA_ERROR__TRANSACTION_ERROR__ALREADY_PROCESSED2 = 7050007;
var SOLANA_ERROR__TRANSACTION_ERROR__BLOCKHASH_NOT_FOUND2 = 7050008;
var SOLANA_ERROR__TRANSACTION_ERROR__CALL_CHAIN_TOO_DEEP2 = 7050009;
var SOLANA_ERROR__TRANSACTION_ERROR__MISSING_SIGNATURE_FOR_FEE2 = 7050010;
var SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ACCOUNT_INDEX2 = 7050011;
var SOLANA_ERROR__TRANSACTION_ERROR__SIGNATURE_FAILURE2 = 7050012;
var SOLANA_ERROR__TRANSACTION_ERROR__INVALID_PROGRAM_FOR_EXECUTION2 = 7050013;
var SOLANA_ERROR__TRANSACTION_ERROR__SANITIZE_FAILURE2 = 7050014;
var SOLANA_ERROR__TRANSACTION_ERROR__CLUSTER_MAINTENANCE2 = 7050015;
var SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_BORROW_OUTSTANDING2 = 7050016;
var SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_MAX_BLOCK_COST_LIMIT2 = 7050017;
var SOLANA_ERROR__TRANSACTION_ERROR__UNSUPPORTED_VERSION2 = 7050018;
var SOLANA_ERROR__TRANSACTION_ERROR__INVALID_WRITABLE_ACCOUNT2 = 7050019;
var SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_MAX_ACCOUNT_COST_LIMIT2 = 7050020;
var SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_ACCOUNT_DATA_BLOCK_LIMIT2 = 7050021;
var SOLANA_ERROR__TRANSACTION_ERROR__TOO_MANY_ACCOUNT_LOCKS2 = 7050022;
var SOLANA_ERROR__TRANSACTION_ERROR__ADDRESS_LOOKUP_TABLE_NOT_FOUND2 = 7050023;
var SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ADDRESS_LOOKUP_TABLE_OWNER2 = 7050024;
var SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ADDRESS_LOOKUP_TABLE_DATA2 = 7050025;
var SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ADDRESS_LOOKUP_TABLE_INDEX2 = 7050026;
var SOLANA_ERROR__TRANSACTION_ERROR__INVALID_RENT_PAYING_ACCOUNT2 = 7050027;
var SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_MAX_VOTE_COST_LIMIT2 = 7050028;
var SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_ACCOUNT_DATA_TOTAL_LIMIT2 = 7050029;
var SOLANA_ERROR__TRANSACTION_ERROR__DUPLICATE_INSTRUCTION2 = 7050030;
var SOLANA_ERROR__TRANSACTION_ERROR__INSUFFICIENT_FUNDS_FOR_RENT2 = 7050031;
var SOLANA_ERROR__TRANSACTION_ERROR__MAX_LOADED_ACCOUNTS_DATA_SIZE_EXCEEDED2 = 7050032;
var SOLANA_ERROR__TRANSACTION_ERROR__INVALID_LOADED_ACCOUNTS_DATA_SIZE_LIMIT2 = 7050033;
var SOLANA_ERROR__TRANSACTION_ERROR__RESANITIZATION_NEEDED2 = 7050034;
var SOLANA_ERROR__TRANSACTION_ERROR__PROGRAM_EXECUTION_TEMPORARILY_RESTRICTED2 = 7050035;
var SOLANA_ERROR__TRANSACTION_ERROR__UNBALANCED_TRANSACTION2 = 7050036;
var SOLANA_ERROR__CODECS__CANNOT_DECODE_EMPTY_BYTE_ARRAY2 = 8078e3;
var SOLANA_ERROR__CODECS__INVALID_BYTE_LENGTH2 = 8078001;
var SOLANA_ERROR__CODECS__EXPECTED_FIXED_LENGTH2 = 8078002;
var SOLANA_ERROR__CODECS__EXPECTED_VARIABLE_LENGTH2 = 8078003;
var SOLANA_ERROR__CODECS__ENCODER_DECODER_SIZE_COMPATIBILITY_MISMATCH2 = 8078004;
var SOLANA_ERROR__CODECS__ENCODER_DECODER_FIXED_SIZE_MISMATCH2 = 8078005;
var SOLANA_ERROR__CODECS__ENCODER_DECODER_MAX_SIZE_MISMATCH2 = 8078006;
var SOLANA_ERROR__CODECS__INVALID_NUMBER_OF_ITEMS2 = 8078007;
var SOLANA_ERROR__CODECS__ENUM_DISCRIMINATOR_OUT_OF_RANGE2 = 8078008;
var SOLANA_ERROR__CODECS__INVALID_DISCRIMINATED_UNION_VARIANT2 = 8078009;
var SOLANA_ERROR__CODECS__INVALID_ENUM_VARIANT2 = 8078010;
var SOLANA_ERROR__CODECS__NUMBER_OUT_OF_RANGE2 = 8078011;
var SOLANA_ERROR__CODECS__INVALID_STRING_FOR_BASE2 = 8078012;
var SOLANA_ERROR__CODECS__EXPECTED_POSITIVE_BYTE_LENGTH2 = 8078013;
var SOLANA_ERROR__CODECS__OFFSET_OUT_OF_RANGE2 = 8078014;
var SOLANA_ERROR__CODECS__INVALID_LITERAL_UNION_VARIANT2 = 8078015;
var SOLANA_ERROR__CODECS__LITERAL_UNION_DISCRIMINATOR_OUT_OF_RANGE2 = 8078016;
var SOLANA_ERROR__CODECS__UNION_VARIANT_OUT_OF_RANGE2 = 8078017;
var SOLANA_ERROR__CODECS__INVALID_CONSTANT2 = 8078018;
var SOLANA_ERROR__CODECS__EXPECTED_ZERO_VALUE_TO_MATCH_ITEM_FIXED_SIZE2 = 8078019;
var SOLANA_ERROR__CODECS__ENCODED_BYTES_MUST_NOT_INCLUDE_SENTINEL2 = 8078020;
var SOLANA_ERROR__CODECS__SENTINEL_MISSING_IN_DECODED_BYTES2 = 8078021;
var SOLANA_ERROR__CODECS__CANNOT_USE_LEXICAL_VALUES_AS_ENUM_DISCRIMINATORS2 = 8078022;
var SOLANA_ERROR__RPC__INTEGER_OVERFLOW2 = 81e5;
var SOLANA_ERROR__RPC__TRANSPORT_HTTP_HEADER_FORBIDDEN2 = 8100001;
var SOLANA_ERROR__RPC__TRANSPORT_HTTP_ERROR2 = 8100002;
var SOLANA_ERROR__RPC_SUBSCRIPTIONS__CANNOT_CREATE_SUBSCRIPTION_REQUEST2 = 819e4;
var SOLANA_ERROR__RPC_SUBSCRIPTIONS__EXPECTED_SERVER_SUBSCRIPTION_ID2 = 8190001;
var SOLANA_ERROR__RPC_SUBSCRIPTIONS__TRANSPORT_CLOSED_BEFORE_MESSAGE_BUFFERED2 = 8190002;
var SOLANA_ERROR__RPC_SUBSCRIPTIONS__TRANSPORT_CONNECTION_CLOSED2 = 8190003;
var SOLANA_ERROR__RPC_SUBSCRIPTIONS__TRANSPORT_FAILED_TO_CONNECT2 = 8190004;
var SOLANA_ERROR__INVARIANT_VIOLATION__WEBSOCKET_MESSAGE_ITERATOR_STATE_MISSING2 = 99e5;
var SOLANA_ERROR__INVARIANT_VIOLATION__WEBSOCKET_MESSAGE_ITERATOR_MUST_NOT_POLL_BEFORE_RESOLVING_EXISTING_MESSAGE_PROMISE2 = 9900001;
var SOLANA_ERROR__INVARIANT_VIOLATION__CACHED_ABORTABLE_ITERABLE_CACHE_ENTRY_MISSING2 = 9900002;
var SOLANA_ERROR__INVARIANT_VIOLATION__SWITCH_MUST_BE_EXHAUSTIVE2 = 9900003;
var SolanaErrorMessages2 = {
  [SOLANA_ERROR__ACCOUNTS__ACCOUNT_NOT_FOUND2]: "Account not found at address: $address",
  [SOLANA_ERROR__ACCOUNTS__EXPECTED_ALL_ACCOUNTS_TO_BE_DECODED2]: "Not all accounts were decoded. Encoded accounts found at addresses: $addresses.",
  [SOLANA_ERROR__ACCOUNTS__EXPECTED_DECODED_ACCOUNT2]: "Expected decoded account at address: $address",
  [SOLANA_ERROR__ACCOUNTS__FAILED_TO_DECODE_ACCOUNT2]: "Failed to decode account data at address: $address",
  [SOLANA_ERROR__ACCOUNTS__ONE_OR_MORE_ACCOUNTS_NOT_FOUND2]: "Accounts not found at addresses: $addresses",
  [SOLANA_ERROR__ADDRESSES__FAILED_TO_FIND_VIABLE_PDA_BUMP_SEED2]: "Unable to find a viable program address bump seed.",
  [SOLANA_ERROR__ADDRESSES__INVALID_BASE58_ENCODED_ADDRESS2]: "$putativeAddress is not a base58-encoded address.",
  [SOLANA_ERROR__ADDRESSES__INVALID_BYTE_LENGTH2]: "Expected base58 encoded address to decode to a byte array of length 32. Actual length: $actualLength.",
  [SOLANA_ERROR__ADDRESSES__INVALID_ED25519_PUBLIC_KEY2]: "The `CryptoKey` must be an `Ed25519` public key.",
  [SOLANA_ERROR__ADDRESSES__INVALID_SEEDS_POINT_ON_CURVE2]: "Invalid seeds; point must fall off the Ed25519 curve.",
  [SOLANA_ERROR__ADDRESSES__MALFORMED_PDA2]: "Expected given program derived address to have the following format: [Address, ProgramDerivedAddressBump].",
  [SOLANA_ERROR__ADDRESSES__MAX_NUMBER_OF_PDA_SEEDS_EXCEEDED2]: "A maximum of $maxSeeds seeds, including the bump seed, may be supplied when creating an address. Received: $actual.",
  [SOLANA_ERROR__ADDRESSES__MAX_PDA_SEED_LENGTH_EXCEEDED2]: "The seed at index $index with length $actual exceeds the maximum length of $maxSeedLength bytes.",
  [SOLANA_ERROR__ADDRESSES__PDA_BUMP_SEED_OUT_OF_RANGE2]: "Expected program derived address bump to be in the range [0, 255], got: $bump.",
  [SOLANA_ERROR__ADDRESSES__PDA_ENDS_WITH_PDA_MARKER2]: "Program address cannot end with PDA marker.",
  [SOLANA_ERROR__ADDRESSES__STRING_LENGTH_OUT_OF_RANGE2]: "Expected base58-encoded address string of length in the range [32, 44]. Actual length: $actualLength.",
  [SOLANA_ERROR__BLOCKHASH_STRING_LENGTH_OUT_OF_RANGE2]: "Expected base58-encoded blockash string of length in the range [32, 44]. Actual length: $actualLength.",
  [SOLANA_ERROR__BLOCK_HEIGHT_EXCEEDED2]: "The network has progressed past the last block for which this transaction could have been committed.",
  [SOLANA_ERROR__CODECS__CANNOT_DECODE_EMPTY_BYTE_ARRAY2]: "Codec [$codecDescription] cannot decode empty byte arrays.",
  [SOLANA_ERROR__CODECS__CANNOT_USE_LEXICAL_VALUES_AS_ENUM_DISCRIMINATORS2]: "Enum codec cannot use lexical values [$stringValues] as discriminators. Either remove all lexical values or set `useValuesAsDiscriminators` to `false`.",
  [SOLANA_ERROR__CODECS__ENCODED_BYTES_MUST_NOT_INCLUDE_SENTINEL2]: "Sentinel [$hexSentinel] must not be present in encoded bytes [$hexEncodedBytes].",
  [SOLANA_ERROR__CODECS__ENCODER_DECODER_FIXED_SIZE_MISMATCH2]: "Encoder and decoder must have the same fixed size, got [$encoderFixedSize] and [$decoderFixedSize].",
  [SOLANA_ERROR__CODECS__ENCODER_DECODER_MAX_SIZE_MISMATCH2]: "Encoder and decoder must have the same max size, got [$encoderMaxSize] and [$decoderMaxSize].",
  [SOLANA_ERROR__CODECS__ENCODER_DECODER_SIZE_COMPATIBILITY_MISMATCH2]: "Encoder and decoder must either both be fixed-size or variable-size.",
  [SOLANA_ERROR__CODECS__ENUM_DISCRIMINATOR_OUT_OF_RANGE2]: "Enum discriminator out of range. Expected a number in [$formattedValidDiscriminators], got $discriminator.",
  [SOLANA_ERROR__CODECS__EXPECTED_FIXED_LENGTH2]: "Expected a fixed-size codec, got a variable-size one.",
  [SOLANA_ERROR__CODECS__EXPECTED_POSITIVE_BYTE_LENGTH2]: "Codec [$codecDescription] expected a positive byte length, got $bytesLength.",
  [SOLANA_ERROR__CODECS__EXPECTED_VARIABLE_LENGTH2]: "Expected a variable-size codec, got a fixed-size one.",
  [SOLANA_ERROR__CODECS__EXPECTED_ZERO_VALUE_TO_MATCH_ITEM_FIXED_SIZE2]: "Codec [$codecDescription] expected zero-value [$hexZeroValue] to have the same size as the provided fixed-size item [$expectedSize bytes].",
  [SOLANA_ERROR__CODECS__INVALID_BYTE_LENGTH2]: "Codec [$codecDescription] expected $expected bytes, got $bytesLength.",
  [SOLANA_ERROR__CODECS__INVALID_CONSTANT2]: "Expected byte array constant [$hexConstant] to be present in data [$hexData] at offset [$offset].",
  [SOLANA_ERROR__CODECS__INVALID_DISCRIMINATED_UNION_VARIANT2]: "Invalid discriminated union variant. Expected one of [$variants], got $value.",
  [SOLANA_ERROR__CODECS__INVALID_ENUM_VARIANT2]: "Invalid enum variant. Expected one of [$stringValues] or a number in [$formattedNumericalValues], got $variant.",
  [SOLANA_ERROR__CODECS__INVALID_LITERAL_UNION_VARIANT2]: "Invalid literal union variant. Expected one of [$variants], got $value.",
  [SOLANA_ERROR__CODECS__INVALID_NUMBER_OF_ITEMS2]: "Expected [$codecDescription] to have $expected items, got $actual.",
  [SOLANA_ERROR__CODECS__INVALID_STRING_FOR_BASE2]: "Invalid value $value for base $base with alphabet $alphabet.",
  [SOLANA_ERROR__CODECS__LITERAL_UNION_DISCRIMINATOR_OUT_OF_RANGE2]: "Literal union discriminator out of range. Expected a number between $minRange and $maxRange, got $discriminator.",
  [SOLANA_ERROR__CODECS__NUMBER_OUT_OF_RANGE2]: "Codec [$codecDescription] expected number to be in the range [$min, $max], got $value.",
  [SOLANA_ERROR__CODECS__OFFSET_OUT_OF_RANGE2]: "Codec [$codecDescription] expected offset to be in the range [0, $bytesLength], got $offset.",
  [SOLANA_ERROR__CODECS__SENTINEL_MISSING_IN_DECODED_BYTES2]: "Expected sentinel [$hexSentinel] to be present in decoded bytes [$hexDecodedBytes].",
  [SOLANA_ERROR__CODECS__UNION_VARIANT_OUT_OF_RANGE2]: "Union variant out of range. Expected an index between $minRange and $maxRange, got $variant.",
  [SOLANA_ERROR__CRYPTO__RANDOM_VALUES_FUNCTION_UNIMPLEMENTED2]: "No random values implementation could be found.",
  [SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_ALREADY_INITIALIZED2]: "instruction requires an uninitialized account",
  [SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_BORROW_FAILED2]: "instruction tries to borrow reference for an account which is already borrowed",
  [SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_BORROW_OUTSTANDING2]: "instruction left account with an outstanding borrowed reference",
  [SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_DATA_SIZE_CHANGED2]: "program other than the account's owner changed the size of the account data",
  [SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_DATA_TOO_SMALL2]: "account data too small for instruction",
  [SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_NOT_EXECUTABLE2]: "instruction expected an executable account",
  [SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_NOT_RENT_EXEMPT2]: "An account does not have enough lamports to be rent-exempt",
  [SOLANA_ERROR__INSTRUCTION_ERROR__ARITHMETIC_OVERFLOW2]: "Program arithmetic overflowed",
  [SOLANA_ERROR__INSTRUCTION_ERROR__BORSH_IO_ERROR2]: "Failed to serialize or deserialize account data: $encodedData",
  [SOLANA_ERROR__INSTRUCTION_ERROR__BUILTIN_PROGRAMS_MUST_CONSUME_COMPUTE_UNITS2]: "Builtin programs must consume compute units",
  [SOLANA_ERROR__INSTRUCTION_ERROR__CALL_DEPTH2]: "Cross-program invocation call depth too deep",
  [SOLANA_ERROR__INSTRUCTION_ERROR__COMPUTATIONAL_BUDGET_EXCEEDED2]: "Computational budget exceeded",
  [SOLANA_ERROR__INSTRUCTION_ERROR__CUSTOM2]: "custom program error: #$code",
  [SOLANA_ERROR__INSTRUCTION_ERROR__DUPLICATE_ACCOUNT_INDEX2]: "instruction contains duplicate accounts",
  [SOLANA_ERROR__INSTRUCTION_ERROR__DUPLICATE_ACCOUNT_OUT_OF_SYNC2]: "instruction modifications of multiply-passed account differ",
  [SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_ACCOUNT_NOT_RENT_EXEMPT2]: "executable accounts must be rent exempt",
  [SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_DATA_MODIFIED2]: "instruction changed executable accounts data",
  [SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_LAMPORT_CHANGE2]: "instruction changed the balance of an executable account",
  [SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_MODIFIED2]: "instruction changed executable bit of an account",
  [SOLANA_ERROR__INSTRUCTION_ERROR__EXTERNAL_ACCOUNT_DATA_MODIFIED2]: "instruction modified data of an account it does not own",
  [SOLANA_ERROR__INSTRUCTION_ERROR__EXTERNAL_ACCOUNT_LAMPORT_SPEND2]: "instruction spent from the balance of an account it does not own",
  [SOLANA_ERROR__INSTRUCTION_ERROR__GENERIC_ERROR2]: "generic instruction error",
  [SOLANA_ERROR__INSTRUCTION_ERROR__ILLEGAL_OWNER2]: "Provided owner is not allowed",
  [SOLANA_ERROR__INSTRUCTION_ERROR__IMMUTABLE2]: "Account is immutable",
  [SOLANA_ERROR__INSTRUCTION_ERROR__INCORRECT_AUTHORITY2]: "Incorrect authority provided",
  [SOLANA_ERROR__INSTRUCTION_ERROR__INCORRECT_PROGRAM_ID2]: "incorrect program id for instruction",
  [SOLANA_ERROR__INSTRUCTION_ERROR__INSUFFICIENT_FUNDS2]: "insufficient funds for instruction",
  [SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ACCOUNT_DATA2]: "invalid account data for instruction",
  [SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ACCOUNT_OWNER2]: "Invalid account owner",
  [SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ARGUMENT2]: "invalid program argument",
  [SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ERROR2]: "program returned invalid error code",
  [SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_INSTRUCTION_DATA2]: "invalid instruction data",
  [SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_REALLOC2]: "Failed to reallocate account data",
  [SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_SEEDS2]: "Provided seeds do not result in a valid address",
  [SOLANA_ERROR__INSTRUCTION_ERROR__MAX_ACCOUNTS_DATA_ALLOCATIONS_EXCEEDED2]: "Accounts data allocations exceeded the maximum allowed per transaction",
  [SOLANA_ERROR__INSTRUCTION_ERROR__MAX_ACCOUNTS_EXCEEDED2]: "Max accounts exceeded",
  [SOLANA_ERROR__INSTRUCTION_ERROR__MAX_INSTRUCTION_TRACE_LENGTH_EXCEEDED2]: "Max instruction trace length exceeded",
  [SOLANA_ERROR__INSTRUCTION_ERROR__MAX_SEED_LENGTH_EXCEEDED2]: "Length of the seed is too long for address generation",
  [SOLANA_ERROR__INSTRUCTION_ERROR__MISSING_ACCOUNT2]: "An account required by the instruction is missing",
  [SOLANA_ERROR__INSTRUCTION_ERROR__MISSING_REQUIRED_SIGNATURE2]: "missing required signature for instruction",
  [SOLANA_ERROR__INSTRUCTION_ERROR__MODIFIED_PROGRAM_ID2]: "instruction illegally modified the program id of an account",
  [SOLANA_ERROR__INSTRUCTION_ERROR__NOT_ENOUGH_ACCOUNT_KEYS2]: "insufficient account keys for instruction",
  [SOLANA_ERROR__INSTRUCTION_ERROR__PRIVILEGE_ESCALATION2]: "Cross-program invocation with unauthorized signer or writable account",
  [SOLANA_ERROR__INSTRUCTION_ERROR__PROGRAM_ENVIRONMENT_SETUP_FAILURE2]: "Failed to create program execution environment",
  [SOLANA_ERROR__INSTRUCTION_ERROR__PROGRAM_FAILED_TO_COMPILE2]: "Program failed to compile",
  [SOLANA_ERROR__INSTRUCTION_ERROR__PROGRAM_FAILED_TO_COMPLETE2]: "Program failed to complete",
  [SOLANA_ERROR__INSTRUCTION_ERROR__READONLY_DATA_MODIFIED2]: "instruction modified data of a read-only account",
  [SOLANA_ERROR__INSTRUCTION_ERROR__READONLY_LAMPORT_CHANGE2]: "instruction changed the balance of a read-only account",
  [SOLANA_ERROR__INSTRUCTION_ERROR__REENTRANCY_NOT_ALLOWED2]: "Cross-program invocation reentrancy not allowed for this instruction",
  [SOLANA_ERROR__INSTRUCTION_ERROR__RENT_EPOCH_MODIFIED2]: "instruction modified rent epoch of an account",
  [SOLANA_ERROR__INSTRUCTION_ERROR__UNBALANCED_INSTRUCTION2]: "sum of account balances before and after instruction do not match",
  [SOLANA_ERROR__INSTRUCTION_ERROR__UNINITIALIZED_ACCOUNT2]: "instruction requires an initialized account",
  [SOLANA_ERROR__INSTRUCTION_ERROR__UNKNOWN2]: "",
  [SOLANA_ERROR__INSTRUCTION_ERROR__UNSUPPORTED_PROGRAM_ID2]: "Unsupported program id",
  [SOLANA_ERROR__INSTRUCTION_ERROR__UNSUPPORTED_SYSVAR2]: "Unsupported sysvar",
  [SOLANA_ERROR__INSTRUCTION__EXPECTED_TO_HAVE_ACCOUNTS2]: "The instruction does not have any accounts.",
  [SOLANA_ERROR__INSTRUCTION__EXPECTED_TO_HAVE_DATA2]: "The instruction does not have any data.",
  [SOLANA_ERROR__INSTRUCTION__PROGRAM_ID_MISMATCH2]: "Expected instruction to have progress address $expectedProgramAddress, got $actualProgramAddress.",
  [SOLANA_ERROR__INVALID_BLOCKHASH_BYTE_LENGTH2]: "Expected base58 encoded blockhash to decode to a byte array of length 32. Actual length: $actualLength.",
  [SOLANA_ERROR__INVALID_NONCE2]: "The nonce `$expectedNonceValue` is no longer valid. It has advanced to `$actualNonceValue`",
  [SOLANA_ERROR__INVARIANT_VIOLATION__CACHED_ABORTABLE_ITERABLE_CACHE_ENTRY_MISSING2]: "Invariant violation: Found no abortable iterable cache entry for key `$cacheKey`. It should be impossible to hit this error; please file an issue at https://sola.na/web3invariant",
  [SOLANA_ERROR__INVARIANT_VIOLATION__SWITCH_MUST_BE_EXHAUSTIVE2]: "Invariant violation: Switch statement non-exhaustive. Received unexpected value `$unexpectedValue`. It should be impossible to hit this error; please file an issue at https://sola.na/web3invariant",
  [SOLANA_ERROR__INVARIANT_VIOLATION__WEBSOCKET_MESSAGE_ITERATOR_MUST_NOT_POLL_BEFORE_RESOLVING_EXISTING_MESSAGE_PROMISE2]: "Invariant violation: WebSocket message iterator state is corrupt; iterated without first resolving existing message promise. It should be impossible to hit this error; please file an issue at https://sola.na/web3invariant",
  [SOLANA_ERROR__INVARIANT_VIOLATION__WEBSOCKET_MESSAGE_ITERATOR_STATE_MISSING2]: "Invariant violation: WebSocket message iterator is missing state storage. It should be impossible to hit this error; please file an issue at https://sola.na/web3invariant",
  [SOLANA_ERROR__JSON_RPC__INTERNAL_ERROR2]: "JSON-RPC error: Internal JSON-RPC error ($__serverMessage)",
  [SOLANA_ERROR__JSON_RPC__INVALID_PARAMS2]: "JSON-RPC error: Invalid method parameter(s) ($__serverMessage)",
  [SOLANA_ERROR__JSON_RPC__INVALID_REQUEST2]: "JSON-RPC error: The JSON sent is not a valid `Request` object ($__serverMessage)",
  [SOLANA_ERROR__JSON_RPC__METHOD_NOT_FOUND2]: "JSON-RPC error: The method does not exist / is not available ($__serverMessage)",
  [SOLANA_ERROR__JSON_RPC__PARSE_ERROR2]: "JSON-RPC error: An error occurred on the server while parsing the JSON text ($__serverMessage)",
  [SOLANA_ERROR__JSON_RPC__SCAN_ERROR2]: "$__serverMessage",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_CLEANED_UP2]: "$__serverMessage",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_NOT_AVAILABLE2]: "$__serverMessage",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_STATUS_NOT_AVAILABLE_YET2]: "$__serverMessage",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_KEY_EXCLUDED_FROM_SECONDARY_INDEX2]: "$__serverMessage",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_LONG_TERM_STORAGE_SLOT_SKIPPED2]: "$__serverMessage",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_MIN_CONTEXT_SLOT_NOT_REACHED2]: "Minimum context slot has not been reached",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_NODE_UNHEALTHY2]: "Node is unhealthy; behind by $numSlotsBehind slots",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_NO_SNAPSHOT2]: "No snapshot",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_SEND_TRANSACTION_PREFLIGHT_FAILURE2]: "Transaction simulation failed",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_SLOT_SKIPPED2]: "$__serverMessage",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_HISTORY_NOT_AVAILABLE2]: "Transaction history is not available from this node",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_PRECOMPILE_VERIFICATION_FAILURE2]: "$__serverMessage",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_SIGNATURE_LEN_MISMATCH2]: "Transaction signature length mismatch",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_SIGNATURE_VERIFICATION_FAILURE2]: "Transaction signature verification failure",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_UNSUPPORTED_TRANSACTION_VERSION2]: "$__serverMessage",
  [SOLANA_ERROR__KEYS__INVALID_KEY_PAIR_BYTE_LENGTH2]: "Key pair bytes must be of length 64, got $byteLength.",
  [SOLANA_ERROR__KEYS__INVALID_PRIVATE_KEY_BYTE_LENGTH2]: "Expected private key bytes with length 32. Actual length: $actualLength.",
  [SOLANA_ERROR__KEYS__INVALID_SIGNATURE_BYTE_LENGTH2]: "Expected base58-encoded signature to decode to a byte array of length 64. Actual length: $actualLength.",
  [SOLANA_ERROR__KEYS__PUBLIC_KEY_MUST_MATCH_PRIVATE_KEY2]: "The provided private key does not match the provided public key.",
  [SOLANA_ERROR__KEYS__SIGNATURE_STRING_LENGTH_OUT_OF_RANGE2]: "Expected base58-encoded signature string of length in the range [64, 88]. Actual length: $actualLength.",
  [SOLANA_ERROR__LAMPORTS_OUT_OF_RANGE2]: "Lamports value must be in the range [0, 2e64-1]",
  [SOLANA_ERROR__MALFORMED_BIGINT_STRING2]: "`$value` cannot be parsed as a `BigInt`",
  [SOLANA_ERROR__MALFORMED_NUMBER_STRING2]: "`$value` cannot be parsed as a `Number`",
  [SOLANA_ERROR__NONCE_ACCOUNT_NOT_FOUND2]: "No nonce account could be found at address `$nonceAccountAddress`",
  [SOLANA_ERROR__RPC_SUBSCRIPTIONS__CANNOT_CREATE_SUBSCRIPTION_REQUEST2]: "Either the notification name must end in 'Notifications' or the API must supply a subscription creator function for the notification '$notificationName' to map between the notification name and the subscribe/unsubscribe method names.",
  [SOLANA_ERROR__RPC_SUBSCRIPTIONS__EXPECTED_SERVER_SUBSCRIPTION_ID2]: "Failed to obtain a subscription id from the server",
  [SOLANA_ERROR__RPC_SUBSCRIPTIONS__TRANSPORT_CLOSED_BEFORE_MESSAGE_BUFFERED2]: "WebSocket was closed before payload could be added to the send buffer",
  [SOLANA_ERROR__RPC_SUBSCRIPTIONS__TRANSPORT_CONNECTION_CLOSED2]: "WebSocket connection closed",
  [SOLANA_ERROR__RPC_SUBSCRIPTIONS__TRANSPORT_FAILED_TO_CONNECT2]: "WebSocket failed to connect",
  [SOLANA_ERROR__RPC__INTEGER_OVERFLOW2]: "The $argumentLabel argument to the `$methodName` RPC method$optionalPathLabel was `$value`. This number is unsafe for use with the Solana JSON-RPC because it exceeds `Number.MAX_SAFE_INTEGER`.",
  [SOLANA_ERROR__RPC__TRANSPORT_HTTP_ERROR2]: "HTTP error ($statusCode): $message",
  [SOLANA_ERROR__RPC__TRANSPORT_HTTP_HEADER_FORBIDDEN2]: "HTTP header(s) forbidden: $headers. Learn more at https://developer.mozilla.org/en-US/docs/Glossary/Forbidden_header_name.",
  [SOLANA_ERROR__SIGNER__ADDRESS_CANNOT_HAVE_MULTIPLE_SIGNERS2]: "Multiple distinct signers were identified for address `$address`. Please ensure that you are using the same signer instance for each address.",
  [SOLANA_ERROR__SIGNER__EXPECTED_KEY_PAIR_SIGNER2]: "The provided value does not implement the `KeyPairSigner` interface",
  [SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_MODIFYING_SIGNER2]: "The provided value does not implement the `MessageModifyingSigner` interface",
  [SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_PARTIAL_SIGNER2]: "The provided value does not implement the `MessagePartialSigner` interface",
  [SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_SIGNER2]: "The provided value does not implement any of the `MessageSigner` interfaces",
  [SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_MODIFYING_SIGNER2]: "The provided value does not implement the `TransactionModifyingSigner` interface",
  [SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_PARTIAL_SIGNER2]: "The provided value does not implement the `TransactionPartialSigner` interface",
  [SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_SENDING_SIGNER2]: "The provided value does not implement the `TransactionSendingSigner` interface",
  [SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_SIGNER2]: "The provided value does not implement any of the `TransactionSigner` interfaces",
  [SOLANA_ERROR__SIGNER__TRANSACTION_CANNOT_HAVE_MULTIPLE_SENDING_SIGNERS2]: "More than one `TransactionSendingSigner` was identified.",
  [SOLANA_ERROR__SIGNER__TRANSACTION_SENDING_SIGNER_MISSING2]: "No `TransactionSendingSigner` was identified. Please provide a valid `ITransactionWithSingleSendingSigner` transaction.",
  [SOLANA_ERROR__SIGNER__WALLET_MULTISIGN_UNIMPLEMENTED2]: "Wallet account signers do not support signing multiple messages/transactions in a single operation",
  [SOLANA_ERROR__SUBTLE_CRYPTO__CANNOT_EXPORT_NON_EXTRACTABLE_KEY2]: "Cannot export a non-extractable key.",
  [SOLANA_ERROR__SUBTLE_CRYPTO__DIGEST_UNIMPLEMENTED2]: "No digest implementation could be found.",
  [SOLANA_ERROR__SUBTLE_CRYPTO__DISALLOWED_IN_INSECURE_CONTEXT2]: "Cryptographic operations are only allowed in secure browser contexts. Read more here: https://developer.mozilla.org/en-US/docs/Web/Security/Secure_Contexts.",
  [SOLANA_ERROR__SUBTLE_CRYPTO__ED25519_ALGORITHM_UNIMPLEMENTED2]: "This runtime does not support the generation of Ed25519 key pairs.\n\nInstall @solana/webcrypto-ed25519-polyfill and call its `install` function before generating keys in environments that do not support Ed25519.\n\nFor a list of runtimes that currently support Ed25519 operations, visit https://github.com/WICG/webcrypto-secure-curves/issues/20.",
  [SOLANA_ERROR__SUBTLE_CRYPTO__EXPORT_FUNCTION_UNIMPLEMENTED2]: "No signature verification implementation could be found.",
  [SOLANA_ERROR__SUBTLE_CRYPTO__GENERATE_FUNCTION_UNIMPLEMENTED2]: "No key generation implementation could be found.",
  [SOLANA_ERROR__SUBTLE_CRYPTO__SIGN_FUNCTION_UNIMPLEMENTED2]: "No signing implementation could be found.",
  [SOLANA_ERROR__SUBTLE_CRYPTO__VERIFY_FUNCTION_UNIMPLEMENTED2]: "No key export implementation could be found.",
  [SOLANA_ERROR__TIMESTAMP_OUT_OF_RANGE2]: "Timestamp value must be in the range [-8.64e15, 8.64e15]. `$value` given",
  [SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_BORROW_OUTSTANDING2]: "Transaction processing left an account with an outstanding borrowed reference",
  [SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_IN_USE2]: "Account in use",
  [SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_LOADED_TWICE2]: "Account loaded twice",
  [SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_NOT_FOUND2]: "Attempt to debit an account but found no record of a prior credit.",
  [SOLANA_ERROR__TRANSACTION_ERROR__ADDRESS_LOOKUP_TABLE_NOT_FOUND2]: "Transaction loads an address table account that doesn't exist",
  [SOLANA_ERROR__TRANSACTION_ERROR__ALREADY_PROCESSED2]: "This transaction has already been processed",
  [SOLANA_ERROR__TRANSACTION_ERROR__BLOCKHASH_NOT_FOUND2]: "Blockhash not found",
  [SOLANA_ERROR__TRANSACTION_ERROR__CALL_CHAIN_TOO_DEEP2]: "Loader call chain is too deep",
  [SOLANA_ERROR__TRANSACTION_ERROR__CLUSTER_MAINTENANCE2]: "Transactions are currently disabled due to cluster maintenance",
  [SOLANA_ERROR__TRANSACTION_ERROR__DUPLICATE_INSTRUCTION2]: "Transaction contains a duplicate instruction ($index) that is not allowed",
  [SOLANA_ERROR__TRANSACTION_ERROR__INSUFFICIENT_FUNDS_FOR_FEE2]: "Insufficient funds for fee",
  [SOLANA_ERROR__TRANSACTION_ERROR__INSUFFICIENT_FUNDS_FOR_RENT2]: "Transaction results in an account ($accountIndex) with insufficient funds for rent",
  [SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ACCOUNT_FOR_FEE2]: "This account may not be used to pay transaction fees",
  [SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ACCOUNT_INDEX2]: "Transaction contains an invalid account reference",
  [SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ADDRESS_LOOKUP_TABLE_DATA2]: "Transaction loads an address table account with invalid data",
  [SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ADDRESS_LOOKUP_TABLE_INDEX2]: "Transaction address table lookup uses an invalid index",
  [SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ADDRESS_LOOKUP_TABLE_OWNER2]: "Transaction loads an address table account with an invalid owner",
  [SOLANA_ERROR__TRANSACTION_ERROR__INVALID_LOADED_ACCOUNTS_DATA_SIZE_LIMIT2]: "LoadedAccountsDataSizeLimit set for transaction must be greater than 0.",
  [SOLANA_ERROR__TRANSACTION_ERROR__INVALID_PROGRAM_FOR_EXECUTION2]: "This program may not be used for executing instructions",
  [SOLANA_ERROR__TRANSACTION_ERROR__INVALID_RENT_PAYING_ACCOUNT2]: "Transaction leaves an account with a lower balance than rent-exempt minimum",
  [SOLANA_ERROR__TRANSACTION_ERROR__INVALID_WRITABLE_ACCOUNT2]: "Transaction loads a writable account that cannot be written",
  [SOLANA_ERROR__TRANSACTION_ERROR__MAX_LOADED_ACCOUNTS_DATA_SIZE_EXCEEDED2]: "Transaction exceeded max loaded accounts data size cap",
  [SOLANA_ERROR__TRANSACTION_ERROR__MISSING_SIGNATURE_FOR_FEE2]: "Transaction requires a fee but has no signature present",
  [SOLANA_ERROR__TRANSACTION_ERROR__PROGRAM_ACCOUNT_NOT_FOUND2]: "Attempt to load a program that does not exist",
  [SOLANA_ERROR__TRANSACTION_ERROR__PROGRAM_EXECUTION_TEMPORARILY_RESTRICTED2]: "Execution of the program referenced by account at index $accountIndex is temporarily restricted.",
  [SOLANA_ERROR__TRANSACTION_ERROR__RESANITIZATION_NEEDED2]: "ResanitizationNeeded",
  [SOLANA_ERROR__TRANSACTION_ERROR__SANITIZE_FAILURE2]: "Transaction failed to sanitize accounts offsets correctly",
  [SOLANA_ERROR__TRANSACTION_ERROR__SIGNATURE_FAILURE2]: "Transaction did not pass signature verification",
  [SOLANA_ERROR__TRANSACTION_ERROR__TOO_MANY_ACCOUNT_LOCKS2]: "Transaction locked too many accounts",
  [SOLANA_ERROR__TRANSACTION_ERROR__UNBALANCED_TRANSACTION2]: "Sum of account balances before and after transaction do not match",
  [SOLANA_ERROR__TRANSACTION_ERROR__UNKNOWN2]: "The transaction failed with the error `$errorName`",
  [SOLANA_ERROR__TRANSACTION_ERROR__UNSUPPORTED_VERSION2]: "Transaction version is unsupported",
  [SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_ACCOUNT_DATA_BLOCK_LIMIT2]: "Transaction would exceed account data limit within the block",
  [SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_ACCOUNT_DATA_TOTAL_LIMIT2]: "Transaction would exceed total account data limit",
  [SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_MAX_ACCOUNT_COST_LIMIT2]: "Transaction would exceed max account limit within the block",
  [SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_MAX_BLOCK_COST_LIMIT2]: "Transaction would exceed max Block Cost Limit",
  [SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_MAX_VOTE_COST_LIMIT2]: "Transaction would exceed max Vote Cost Limit",
  [SOLANA_ERROR__TRANSACTION__ADDRESSES_CANNOT_SIGN_TRANSACTION2]: "Attempted to sign a transaction with an address that is not a signer for it",
  [SOLANA_ERROR__TRANSACTION__ADDRESS_MISSING2]: "Transaction is missing an address at index: $index.",
  [SOLANA_ERROR__TRANSACTION__CANNOT_ENCODE_WITH_EMPTY_SIGNATURES2]: "Transaction has no expected signers therefore it cannot be encoded",
  [SOLANA_ERROR__TRANSACTION__EXPECTED_BLOCKHASH_LIFETIME2]: "Transaction does not have a blockhash lifetime",
  [SOLANA_ERROR__TRANSACTION__EXPECTED_NONCE_LIFETIME2]: "Transaction is not a durable nonce transaction",
  [SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_ADDRESS_LOOKUP_TABLE_CONTENTS_MISSING2]: "Contents of these address lookup tables unknown: $lookupTableAddresses",
  [SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_ADDRESS_LOOKUP_TABLE_INDEX_OUT_OF_RANGE2]: "Lookup of address at index $highestRequestedIndex failed for lookup table `$lookupTableAddress`. Highest known index is $highestKnownIndex. The lookup table may have been extended since its contents were retrieved",
  [SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_FEE_PAYER_MISSING2]: "No fee payer set in CompiledTransaction",
  [SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_INSTRUCTION_PROGRAM_ADDRESS_NOT_FOUND2]: "Could not find program address at index $index",
  [SOLANA_ERROR__TRANSACTION__FAILED_TO_ESTIMATE_COMPUTE_LIMIT2]: "Failed to estimate the compute unit consumption for this transaction message. This is likely because simulating the transaction failed. Inspect the `cause` property of this error to learn more",
  [SOLANA_ERROR__TRANSACTION__FEE_PAYER_MISSING2]: "Transaction is missing a fee payer.",
  [SOLANA_ERROR__TRANSACTION__FEE_PAYER_SIGNATURE_MISSING2]: "Could not determine this transaction's signature. Make sure that the transaction has been signed by its fee payer.",
  [SOLANA_ERROR__TRANSACTION__INVALID_NONCE_TRANSACTION_FIRST_INSTRUCTION_MUST_BE_ADVANCE_NONCE2]: "Transaction first instruction is not advance nonce account instruction.",
  [SOLANA_ERROR__TRANSACTION__INVALID_NONCE_TRANSACTION_INSTRUCTIONS_MISSING2]: "Transaction with no instructions cannot be durable nonce transaction.",
  [SOLANA_ERROR__TRANSACTION__INVOKED_PROGRAMS_CANNOT_PAY_FEES2]: "This transaction includes an address (`$programAddress`) which is both invoked and set as the fee payer. Program addresses may not pay fees",
  [SOLANA_ERROR__TRANSACTION__INVOKED_PROGRAMS_MUST_NOT_BE_WRITABLE2]: "This transaction includes an address (`$programAddress`) which is both invoked and marked writable. Program addresses may not be writable",
  [SOLANA_ERROR__TRANSACTION__MESSAGE_SIGNATURES_MISMATCH2]: "The transaction message expected the transaction to have $signerAddressesLength signatures, got $signaturesLength.",
  [SOLANA_ERROR__TRANSACTION__SIGNATURES_MISSING2]: "Transaction is missing signatures for addresses: $addresses.",
  [SOLANA_ERROR__TRANSACTION__VERSION_NUMBER_OUT_OF_RANGE2]: "Transaction version must be in the range [0, 127]. `$actualVersion` given"
};
var START_INDEX2 = "i";
var TYPE2 = "t";
function getHumanReadableErrorMessage2(code, context = {}) {
  const messageFormatString = SolanaErrorMessages2[code];
  if (messageFormatString.length === 0) {
    return "";
  }
  let state;
  function commitStateUpTo(endIndex) {
    if (state[TYPE2] === 2) {
      const variableName = messageFormatString.slice(state[START_INDEX2] + 1, endIndex);
      fragments.push(
        variableName in context ? `${context[variableName]}` : `$${variableName}`
      );
    } else if (state[TYPE2] === 1) {
      fragments.push(messageFormatString.slice(state[START_INDEX2], endIndex));
    }
  }
  const fragments = [];
  messageFormatString.split("").forEach((char, ii) => {
    if (ii === 0) {
      state = {
        [START_INDEX2]: 0,
        [TYPE2]: messageFormatString[0] === "\\" ? 0 : messageFormatString[0] === "$" ? 2 : 1
        /* Text */
      };
      return;
    }
    let nextState;
    switch (state[TYPE2]) {
      case 0:
        nextState = {
          [START_INDEX2]: ii,
          [TYPE2]: 1
          /* Text */
        };
        break;
      case 1:
        if (char === "\\") {
          nextState = {
            [START_INDEX2]: ii,
            [TYPE2]: 0
            /* EscapeSequence */
          };
        } else if (char === "$") {
          nextState = {
            [START_INDEX2]: ii,
            [TYPE2]: 2
            /* Variable */
          };
        }
        break;
      case 2:
        if (char === "\\") {
          nextState = {
            [START_INDEX2]: ii,
            [TYPE2]: 0
            /* EscapeSequence */
          };
        } else if (char === "$") {
          nextState = {
            [START_INDEX2]: ii,
            [TYPE2]: 2
            /* Variable */
          };
        } else if (!char.match(/\w/)) {
          nextState = {
            [START_INDEX2]: ii,
            [TYPE2]: 1
            /* Text */
          };
        }
        break;
    }
    if (nextState) {
      if (state !== nextState) {
        commitStateUpTo(ii);
      }
      state = nextState;
    }
  });
  commitStateUpTo();
  return fragments.join("");
}
function getErrorMessage2(code, context = {}) {
  if (true) {
    return getHumanReadableErrorMessage2(code, context);
  } else {
    let decodingAdviceMessage = `Solana error #${code}; Decode this error by running \`npx @solana/errors decode -- ${code}`;
    if (Object.keys(context).length) {
      decodingAdviceMessage += ` '${encodeContextObject(context)}'`;
    }
    return `${decodingAdviceMessage}\``;
  }
}
var SolanaError2 = class extends Error {
  constructor(...[code, contextAndErrorOptions]) {
    let context;
    let errorOptions;
    if (contextAndErrorOptions) {
      const { cause, ...contextRest } = contextAndErrorOptions;
      if (cause) {
        errorOptions = { cause };
      }
      if (Object.keys(contextRest).length > 0) {
        context = contextRest;
      }
    }
    const message = getErrorMessage2(code, context);
    super(message, errorOptions);
    __publicField(this, "cause", this.cause);
    __publicField(this, "context");
    this.context = {
      __code: code,
      ...context
    };
    this.name = "SolanaError";
  }
};

// node_modules/@solana/codecs-data-structures/node_modules/@solana/codecs-core/dist/index.browser.mjs
function getEncodedSize2(value, encoder) {
  return "fixedSize" in encoder ? encoder.fixedSize : encoder.getSizeFromValue(value);
}
function createEncoder2(encoder) {
  return Object.freeze({
    ...encoder,
    encode: (value) => {
      const bytes = new Uint8Array(getEncodedSize2(value, encoder));
      encoder.write(value, bytes, 0);
      return bytes;
    }
  });
}
function createDecoder2(decoder) {
  return Object.freeze({
    ...decoder,
    decode: (bytes, offset = 0) => decoder.read(bytes, offset)[0]
  });
}
function isFixedSize2(codec) {
  return "fixedSize" in codec && typeof codec.fixedSize === "number";
}
function isVariableSize2(codec) {
  return !isFixedSize2(codec);
}
function combineCodec2(encoder, decoder) {
  if (isFixedSize2(encoder) !== isFixedSize2(decoder)) {
    throw new SolanaError2(SOLANA_ERROR__CODECS__ENCODER_DECODER_SIZE_COMPATIBILITY_MISMATCH2);
  }
  if (isFixedSize2(encoder) && isFixedSize2(decoder) && encoder.fixedSize !== decoder.fixedSize) {
    throw new SolanaError2(SOLANA_ERROR__CODECS__ENCODER_DECODER_FIXED_SIZE_MISMATCH2, {
      decoderFixedSize: decoder.fixedSize,
      encoderFixedSize: encoder.fixedSize
    });
  }
  if (!isFixedSize2(encoder) && !isFixedSize2(decoder) && encoder.maxSize !== decoder.maxSize) {
    throw new SolanaError2(SOLANA_ERROR__CODECS__ENCODER_DECODER_MAX_SIZE_MISMATCH2, {
      decoderMaxSize: decoder.maxSize,
      encoderMaxSize: encoder.maxSize
    });
  }
  return {
    ...decoder,
    ...encoder,
    decode: decoder.decode,
    encode: encoder.encode,
    read: decoder.read,
    write: encoder.write
  };
}
function assertByteArrayIsNotEmptyForCodec2(codecDescription, bytes, offset = 0) {
  if (bytes.length - offset <= 0) {
    throw new SolanaError2(SOLANA_ERROR__CODECS__CANNOT_DECODE_EMPTY_BYTE_ARRAY2, {
      codecDescription
    });
  }
}
function assertByteArrayHasEnoughBytesForCodec2(codecDescription, expected, bytes, offset = 0) {
  const bytesLength = bytes.length - offset;
  if (bytesLength < expected) {
    throw new SolanaError2(SOLANA_ERROR__CODECS__INVALID_BYTE_LENGTH2, {
      bytesLength,
      codecDescription,
      expected
    });
  }
}
function transformEncoder2(encoder, unmap) {
  return createEncoder2({
    ...isVariableSize2(encoder) ? { ...encoder, getSizeFromValue: (value) => encoder.getSizeFromValue(unmap(value)) } : encoder,
    write: (value, bytes, offset) => encoder.write(unmap(value), bytes, offset)
  });
}
function transformDecoder(decoder, map) {
  return createDecoder2({
    ...decoder,
    read: (bytes, offset) => {
      const [value, newOffset] = decoder.read(bytes, offset);
      return [map(value, bytes, offset), newOffset];
    }
  });
}

// node_modules/@solana/codecs-data-structures/node_modules/@solana/codecs-numbers/dist/index.browser.mjs
function assertNumberIsBetweenForCodec(codecDescription, min, max, value) {
  if (value < min || value > max) {
    throw new SolanaError2(SOLANA_ERROR__CODECS__NUMBER_OUT_OF_RANGE2, {
      codecDescription,
      max,
      min,
      value
    });
  }
}
var Endian = ((Endian22) => {
  Endian22[Endian22["Little"] = 0] = "Little";
  Endian22[Endian22["Big"] = 1] = "Big";
  return Endian22;
})(Endian || {});
function isLittleEndian(config) {
  return (config == null ? void 0 : config.endian) === 1 ? false : true;
}
function numberEncoderFactory(input) {
  return createEncoder2({
    fixedSize: input.size,
    write(value, bytes, offset) {
      if (input.range) {
        assertNumberIsBetweenForCodec(input.name, input.range[0], input.range[1], value);
      }
      const arrayBuffer = new ArrayBuffer(input.size);
      input.set(new DataView(arrayBuffer), value, isLittleEndian(input.config));
      bytes.set(new Uint8Array(arrayBuffer), offset);
      return offset + input.size;
    }
  });
}
function numberDecoderFactory(input) {
  return createDecoder2({
    fixedSize: input.size,
    read(bytes, offset = 0) {
      assertByteArrayIsNotEmptyForCodec2(input.name, bytes, offset);
      assertByteArrayHasEnoughBytesForCodec2(input.name, input.size, bytes, offset);
      const view = new DataView(toArrayBuffer(bytes, offset, input.size));
      return [input.get(view, isLittleEndian(input.config)), offset + input.size];
    }
  });
}
function toArrayBuffer(bytes, offset, length) {
  const bytesOffset = bytes.byteOffset + (offset ?? 0);
  const bytesLength = length ?? bytes.byteLength;
  return bytes.buffer.slice(bytesOffset, bytesOffset + bytesLength);
}
var getU32Encoder = (config = {}) => numberEncoderFactory({
  config,
  name: "u32",
  range: [0, Number("0xffffffff")],
  set: (view, value, le) => view.setUint32(0, Number(value), le),
  size: 4
});
var getU32Decoder = (config = {}) => numberDecoderFactory({
  config,
  get: (view, le) => view.getUint32(0, le),
  name: "u32",
  size: 4
});
var getU8Encoder = () => numberEncoderFactory({
  name: "u8",
  range: [0, Number("0xff")],
  set: (view, value) => view.setUint8(0, Number(value)),
  size: 1
});
var getU8Decoder = () => numberDecoderFactory({
  get: (view) => view.getUint8(0),
  name: "u8",
  size: 1
});

// node_modules/@solana/codecs-data-structures/dist/index.browser.mjs
function assertValidNumberOfItemsForCodec(codecDescription, expected, actual) {
  if (expected !== actual) {
    throw new SolanaError2(SOLANA_ERROR__CODECS__INVALID_NUMBER_OF_ITEMS2, {
      actual,
      codecDescription,
      expected
    });
  }
}
function maxCodecSizes(sizes) {
  return sizes.reduce(
    (all, size) => all === null || size === null ? null : Math.max(all, size),
    0
  );
}
function sumCodecSizes(sizes) {
  return sizes.reduce((all, size) => all === null || size === null ? null : all + size, 0);
}
function getFixedSize(codec) {
  return isFixedSize2(codec) ? codec.fixedSize : null;
}
function getMaxSize(codec) {
  return isFixedSize2(codec) ? codec.fixedSize : codec.maxSize ?? null;
}
function getArrayEncoder(item, config = {}) {
  const size = config.size ?? getU32Encoder();
  const fixedSize = computeArrayLikeCodecSize(size, getFixedSize(item));
  const maxSize = computeArrayLikeCodecSize(size, getMaxSize(item)) ?? void 0;
  return createEncoder2({
    ...fixedSize !== null ? { fixedSize } : {
      getSizeFromValue: (array) => {
        const prefixSize = typeof size === "object" ? getEncodedSize2(array.length, size) : 0;
        return prefixSize + [...array].reduce((all, value) => all + getEncodedSize2(value, item), 0);
      },
      maxSize
    },
    write: (array, bytes, offset) => {
      if (typeof size === "number") {
        assertValidNumberOfItemsForCodec("array", size, array.length);
      }
      if (typeof size === "object") {
        offset = size.write(array.length, bytes, offset);
      }
      array.forEach((value) => {
        offset = item.write(value, bytes, offset);
      });
      return offset;
    }
  });
}
function getArrayDecoder(item, config = {}) {
  const size = config.size ?? getU32Decoder();
  const itemSize = getFixedSize(item);
  const fixedSize = computeArrayLikeCodecSize(size, itemSize);
  const maxSize = computeArrayLikeCodecSize(size, getMaxSize(item)) ?? void 0;
  return createDecoder2({
    ...fixedSize !== null ? { fixedSize } : { maxSize },
    read: (bytes, offset) => {
      const array = [];
      if (typeof size === "object" && bytes.slice(offset).length === 0) {
        return [array, offset];
      }
      if (size === "remainder") {
        while (offset < bytes.length) {
          const [value, newOffset2] = item.read(bytes, offset);
          offset = newOffset2;
          array.push(value);
        }
        return [array, offset];
      }
      const [resolvedSize, newOffset] = typeof size === "number" ? [size, offset] : size.read(bytes, offset);
      offset = newOffset;
      for (let i = 0; i < resolvedSize; i += 1) {
        const [value, newOffset2] = item.read(bytes, offset);
        offset = newOffset2;
        array.push(value);
      }
      return [array, offset];
    }
  });
}
function getArrayCodec(item, config = {}) {
  return combineCodec2(getArrayEncoder(item, config), getArrayDecoder(item, config));
}
function computeArrayLikeCodecSize(size, itemSize) {
  if (typeof size !== "number")
    return null;
  if (size === 0)
    return 0;
  return itemSize === null ? null : itemSize * size;
}
function getBooleanEncoder(config = {}) {
  return transformEncoder2(config.size ?? getU8Encoder(), (value) => value ? 1 : 0);
}
function getBytesEncoder() {
  return createEncoder2({
    getSizeFromValue: (value) => value.length,
    write: (value, bytes, offset) => {
      bytes.set(value, offset);
      return offset + value.length;
    }
  });
}
function getBytesDecoder() {
  return createDecoder2({
    read: (bytes, offset) => {
      const slice = bytes.slice(offset);
      return [slice, offset + slice.length];
    }
  });
}
function getBytesCodec() {
  return combineCodec2(getBytesEncoder(), getBytesDecoder());
}
function getConstantEncoder(constant) {
  return createEncoder2({
    fixedSize: constant.length,
    write: (_, bytes, offset) => {
      bytes.set(constant, offset);
      return offset + constant.length;
    }
  });
}
function getTupleEncoder(items) {
  const fixedSize = sumCodecSizes(items.map(getFixedSize));
  const maxSize = sumCodecSizes(items.map(getMaxSize)) ?? void 0;
  return createEncoder2({
    ...fixedSize === null ? {
      getSizeFromValue: (value) => items.map((item, index) => getEncodedSize2(value[index], item)).reduce((all, one) => all + one, 0),
      maxSize
    } : { fixedSize },
    write: (value, bytes, offset) => {
      assertValidNumberOfItemsForCodec("tuple", items.length, value.length);
      items.forEach((item, index) => {
        offset = item.write(value[index], bytes, offset);
      });
      return offset;
    }
  });
}
function getTupleDecoder(items) {
  const fixedSize = sumCodecSizes(items.map(getFixedSize));
  const maxSize = sumCodecSizes(items.map(getMaxSize)) ?? void 0;
  return createDecoder2({
    ...fixedSize === null ? { maxSize } : { fixedSize },
    read: (bytes, offset) => {
      const values = [];
      items.forEach((item) => {
        const [newValue, newOffset] = item.read(bytes, offset);
        values.push(newValue);
        offset = newOffset;
      });
      return [values, offset];
    }
  });
}
function getTupleCodec(items) {
  return combineCodec2(
    getTupleEncoder(items),
    getTupleDecoder(items)
  );
}
function getUnionEncoder(variants, getIndexFromValue) {
  const fixedSize = getUnionFixedSize(variants);
  const write = (variant, bytes, offset) => {
    const index = getIndexFromValue(variant);
    assertValidVariantIndex(variants, index);
    return variants[index].write(variant, bytes, offset);
  };
  if (fixedSize !== null) {
    return createEncoder2({ fixedSize, write });
  }
  const maxSize = getUnionMaxSize(variants);
  return createEncoder2({
    ...maxSize !== null ? { maxSize } : {},
    getSizeFromValue: (variant) => {
      const index = getIndexFromValue(variant);
      assertValidVariantIndex(variants, index);
      return getEncodedSize2(variant, variants[index]);
    },
    write
  });
}
function getUnionDecoder(variants, getIndexFromBytes) {
  const fixedSize = getUnionFixedSize(variants);
  const read = (bytes, offset) => {
    const index = getIndexFromBytes(bytes, offset);
    assertValidVariantIndex(variants, index);
    return variants[index].read(bytes, offset);
  };
  if (fixedSize !== null) {
    return createDecoder2({ fixedSize, read });
  }
  const maxSize = getUnionMaxSize(variants);
  return createDecoder2({ ...maxSize !== null ? { maxSize } : {}, read });
}
function assertValidVariantIndex(variants, index) {
  if (typeof variants[index] === "undefined") {
    throw new SolanaError2(SOLANA_ERROR__CODECS__UNION_VARIANT_OUT_OF_RANGE2, {
      maxRange: variants.length - 1,
      minRange: 0,
      variant: index
    });
  }
}
function getUnionFixedSize(variants) {
  if (variants.length === 0)
    return 0;
  if (!isFixedSize2(variants[0]))
    return null;
  const variantSize = variants[0].fixedSize;
  const sameSizedVariants = variants.every((variant) => isFixedSize2(variant) && variant.fixedSize === variantSize);
  return sameSizedVariants ? variantSize : null;
}
function getUnionMaxSize(variants) {
  return maxCodecSizes(variants.map((variant) => getMaxSize(variant)));
}
function getDiscriminatedUnionEncoder(variants, config = {}) {
  const discriminatorProperty = config.discriminator ?? "__kind";
  const prefix = config.size ?? getU8Encoder();
  return getUnionEncoder(
    variants.map(
      ([, variant], index) => transformEncoder2(getTupleEncoder([prefix, variant]), (value) => [index, value])
    ),
    (value) => getVariantDiscriminator(variants, value[discriminatorProperty])
  );
}
function getDiscriminatedUnionDecoder(variants, config = {}) {
  const discriminatorProperty = config.discriminator ?? "__kind";
  const prefix = config.size ?? getU8Decoder();
  return getUnionDecoder(
    variants.map(
      ([discriminator, variant]) => transformDecoder(getTupleDecoder([prefix, variant]), ([, value]) => ({
        [discriminatorProperty]: discriminator,
        ...value
      }))
    ),
    (bytes, offset) => Number(prefix.read(bytes, offset)[0])
  );
}
function getDiscriminatedUnionCodec(variants, config = {}) {
  return combineCodec2(
    getDiscriminatedUnionEncoder(variants, config),
    getDiscriminatedUnionDecoder(variants, config)
  );
}
function getVariantDiscriminator(variants, discriminatorValue) {
  const discriminator = variants.findIndex(([key]) => discriminatorValue === key);
  if (discriminator < 0) {
    throw new SolanaError2(SOLANA_ERROR__CODECS__INVALID_DISCRIMINATED_UNION_VARIANT2, {
      value: discriminatorValue,
      variants: variants.map(([key]) => key)
    });
  }
  return discriminator;
}
var getDataEnumCodec = getDiscriminatedUnionCodec;
function getUnitEncoder() {
  return createEncoder2({
    fixedSize: 0,
    write: (_value, _bytes, offset) => offset
  });
}
function getUnitDecoder() {
  return createDecoder2({
    fixedSize: 0,
    read: (_bytes, offset) => [void 0, offset]
  });
}
function getUnitCodec() {
  return combineCodec2(getUnitEncoder(), getUnitDecoder());
}
function getStructEncoder(fields) {
  const fieldCodecs = fields.map(([, codec]) => codec);
  const fixedSize = sumCodecSizes(fieldCodecs.map(getFixedSize));
  const maxSize = sumCodecSizes(fieldCodecs.map(getMaxSize)) ?? void 0;
  return createEncoder2({
    ...fixedSize === null ? {
      getSizeFromValue: (value) => fields.map(([key, codec]) => getEncodedSize2(value[key], codec)).reduce((all, one) => all + one, 0),
      maxSize
    } : { fixedSize },
    write: (struct58, bytes, offset) => {
      fields.forEach(([key, codec]) => {
        offset = codec.write(struct58[key], bytes, offset);
      });
      return offset;
    }
  });
}
function getStructDecoder(fields) {
  const fieldCodecs = fields.map(([, codec]) => codec);
  const fixedSize = sumCodecSizes(fieldCodecs.map(getFixedSize));
  const maxSize = sumCodecSizes(fieldCodecs.map(getMaxSize)) ?? void 0;
  return createDecoder2({
    ...fixedSize === null ? { maxSize } : { fixedSize },
    read: (bytes, offset) => {
      const struct58 = {};
      fields.forEach(([key, codec]) => {
        const [value, newOffset] = codec.read(bytes, offset);
        offset = newOffset;
        struct58[key] = value;
      });
      return [struct58, offset];
    }
  });
}
function getStructCodec(fields) {
  return combineCodec2(
    getStructEncoder(fields),
    getStructDecoder(fields)
  );
}

// node_modules/@solana/codecs/node_modules/@solana/codecs-numbers/dist/index.browser.mjs
function assertNumberIsBetweenForCodec2(codecDescription, min, max, value) {
  if (value < min || value > max) {
    throw new SolanaError(SOLANA_ERROR__CODECS__NUMBER_OUT_OF_RANGE, {
      codecDescription,
      max,
      min,
      value
    });
  }
}
var Endian2 = ((Endian22) => {
  Endian22[Endian22["Little"] = 0] = "Little";
  Endian22[Endian22["Big"] = 1] = "Big";
  return Endian22;
})(Endian2 || {});
function isLittleEndian2(config) {
  return (config == null ? void 0 : config.endian) === 1 ? false : true;
}
function numberEncoderFactory2(input) {
  return createEncoder({
    fixedSize: input.size,
    write(value, bytes, offset) {
      if (input.range) {
        assertNumberIsBetweenForCodec2(input.name, input.range[0], input.range[1], value);
      }
      const arrayBuffer = new ArrayBuffer(input.size);
      input.set(new DataView(arrayBuffer), value, isLittleEndian2(input.config));
      bytes.set(new Uint8Array(arrayBuffer), offset);
      return offset + input.size;
    }
  });
}
function numberDecoderFactory2(input) {
  return createDecoder({
    fixedSize: input.size,
    read(bytes, offset = 0) {
      assertByteArrayIsNotEmptyForCodec(input.name, bytes, offset);
      assertByteArrayHasEnoughBytesForCodec(input.name, input.size, bytes, offset);
      const view = new DataView(toArrayBuffer2(bytes, offset, input.size));
      return [input.get(view, isLittleEndian2(input.config)), offset + input.size];
    }
  });
}
function toArrayBuffer2(bytes, offset, length) {
  const bytesOffset = bytes.byteOffset + (offset ?? 0);
  const bytesLength = length ?? bytes.byteLength;
  return bytes.buffer.slice(bytesOffset, bytesOffset + bytesLength);
}
var getU32Encoder2 = (config = {}) => numberEncoderFactory2({
  config,
  name: "u32",
  range: [0, Number("0xffffffff")],
  set: (view, value, le) => view.setUint32(0, Number(value), le),
  size: 4
});
var getU32Decoder2 = (config = {}) => numberDecoderFactory2({
  config,
  get: (view, le) => view.getUint32(0, le),
  name: "u32",
  size: 4
});
var getU32Codec = (config = {}) => combineCodec(getU32Encoder2(config), getU32Decoder2(config));
var getU64Encoder = (config = {}) => numberEncoderFactory2({
  config,
  name: "u64",
  range: [0n, BigInt("0xffffffffffffffff")],
  set: (view, value, le) => view.setBigUint64(0, BigInt(value), le),
  size: 8
});
var getU64Decoder = (config = {}) => numberDecoderFactory2({
  config,
  get: (view, le) => view.getBigUint64(0, le),
  name: "u64",
  size: 8
});
var getU64Codec = (config = {}) => combineCodec(getU64Encoder(config), getU64Decoder(config));

// node_modules/@solana/codecs-strings/node_modules/@solana/errors/dist/index.browser.mjs
var SOLANA_ERROR__BLOCK_HEIGHT_EXCEEDED3 = 1;
var SOLANA_ERROR__INVALID_NONCE3 = 2;
var SOLANA_ERROR__NONCE_ACCOUNT_NOT_FOUND3 = 3;
var SOLANA_ERROR__BLOCKHASH_STRING_LENGTH_OUT_OF_RANGE3 = 4;
var SOLANA_ERROR__INVALID_BLOCKHASH_BYTE_LENGTH3 = 5;
var SOLANA_ERROR__LAMPORTS_OUT_OF_RANGE3 = 6;
var SOLANA_ERROR__MALFORMED_BIGINT_STRING3 = 7;
var SOLANA_ERROR__MALFORMED_NUMBER_STRING3 = 8;
var SOLANA_ERROR__TIMESTAMP_OUT_OF_RANGE3 = 9;
var SOLANA_ERROR__JSON_RPC__PARSE_ERROR3 = -32700;
var SOLANA_ERROR__JSON_RPC__INTERNAL_ERROR3 = -32603;
var SOLANA_ERROR__JSON_RPC__INVALID_PARAMS3 = -32602;
var SOLANA_ERROR__JSON_RPC__METHOD_NOT_FOUND3 = -32601;
var SOLANA_ERROR__JSON_RPC__INVALID_REQUEST3 = -32600;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_MIN_CONTEXT_SLOT_NOT_REACHED3 = -32016;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_UNSUPPORTED_TRANSACTION_VERSION3 = -32015;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_STATUS_NOT_AVAILABLE_YET3 = -32014;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_SIGNATURE_LEN_MISMATCH3 = -32013;
var SOLANA_ERROR__JSON_RPC__SCAN_ERROR3 = -32012;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_HISTORY_NOT_AVAILABLE3 = -32011;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_KEY_EXCLUDED_FROM_SECONDARY_INDEX3 = -32010;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_LONG_TERM_STORAGE_SLOT_SKIPPED3 = -32009;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_NO_SNAPSHOT3 = -32008;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_SLOT_SKIPPED3 = -32007;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_PRECOMPILE_VERIFICATION_FAILURE3 = -32006;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_NODE_UNHEALTHY3 = -32005;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_NOT_AVAILABLE3 = -32004;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_SIGNATURE_VERIFICATION_FAILURE3 = -32003;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_SEND_TRANSACTION_PREFLIGHT_FAILURE3 = -32002;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_CLEANED_UP3 = -32001;
var SOLANA_ERROR__ADDRESSES__INVALID_BYTE_LENGTH3 = 28e5;
var SOLANA_ERROR__ADDRESSES__STRING_LENGTH_OUT_OF_RANGE3 = 2800001;
var SOLANA_ERROR__ADDRESSES__INVALID_BASE58_ENCODED_ADDRESS3 = 2800002;
var SOLANA_ERROR__ADDRESSES__INVALID_ED25519_PUBLIC_KEY3 = 2800003;
var SOLANA_ERROR__ADDRESSES__MALFORMED_PDA3 = 2800004;
var SOLANA_ERROR__ADDRESSES__PDA_BUMP_SEED_OUT_OF_RANGE3 = 2800005;
var SOLANA_ERROR__ADDRESSES__MAX_NUMBER_OF_PDA_SEEDS_EXCEEDED3 = 2800006;
var SOLANA_ERROR__ADDRESSES__MAX_PDA_SEED_LENGTH_EXCEEDED3 = 2800007;
var SOLANA_ERROR__ADDRESSES__INVALID_SEEDS_POINT_ON_CURVE3 = 2800008;
var SOLANA_ERROR__ADDRESSES__FAILED_TO_FIND_VIABLE_PDA_BUMP_SEED3 = 2800009;
var SOLANA_ERROR__ADDRESSES__PDA_ENDS_WITH_PDA_MARKER3 = 2800010;
var SOLANA_ERROR__ACCOUNTS__ACCOUNT_NOT_FOUND3 = 323e4;
var SOLANA_ERROR__ACCOUNTS__ONE_OR_MORE_ACCOUNTS_NOT_FOUND3 = ********;
var SOLANA_ERROR__ACCOUNTS__FAILED_TO_DECODE_ACCOUNT3 = 3230002;
var SOLANA_ERROR__ACCOUNTS__EXPECTED_DECODED_ACCOUNT3 = 3230003;
var SOLANA_ERROR__ACCOUNTS__EXPECTED_ALL_ACCOUNTS_TO_BE_DECODED3 = 3230004;
var SOLANA_ERROR__SUBTLE_CRYPTO__DISALLOWED_IN_INSECURE_CONTEXT3 = 361e4;
var SOLANA_ERROR__SUBTLE_CRYPTO__DIGEST_UNIMPLEMENTED3 = 3610001;
var SOLANA_ERROR__SUBTLE_CRYPTO__ED25519_ALGORITHM_UNIMPLEMENTED3 = 3610002;
var SOLANA_ERROR__SUBTLE_CRYPTO__EXPORT_FUNCTION_UNIMPLEMENTED3 = 3610003;
var SOLANA_ERROR__SUBTLE_CRYPTO__GENERATE_FUNCTION_UNIMPLEMENTED3 = 3610004;
var SOLANA_ERROR__SUBTLE_CRYPTO__SIGN_FUNCTION_UNIMPLEMENTED3 = 3610005;
var SOLANA_ERROR__SUBTLE_CRYPTO__VERIFY_FUNCTION_UNIMPLEMENTED3 = 3610006;
var SOLANA_ERROR__SUBTLE_CRYPTO__CANNOT_EXPORT_NON_EXTRACTABLE_KEY3 = 3610007;
var SOLANA_ERROR__CRYPTO__RANDOM_VALUES_FUNCTION_UNIMPLEMENTED3 = 3611e3;
var SOLANA_ERROR__KEYS__INVALID_KEY_PAIR_BYTE_LENGTH3 = 3704e3;
var SOLANA_ERROR__KEYS__INVALID_PRIVATE_KEY_BYTE_LENGTH3 = 3704001;
var SOLANA_ERROR__KEYS__INVALID_SIGNATURE_BYTE_LENGTH3 = 3704002;
var SOLANA_ERROR__KEYS__SIGNATURE_STRING_LENGTH_OUT_OF_RANGE3 = 3704003;
var SOLANA_ERROR__KEYS__PUBLIC_KEY_MUST_MATCH_PRIVATE_KEY3 = 3704004;
var SOLANA_ERROR__INSTRUCTION__EXPECTED_TO_HAVE_ACCOUNTS3 = 4128e3;
var SOLANA_ERROR__INSTRUCTION__EXPECTED_TO_HAVE_DATA3 = 4128001;
var SOLANA_ERROR__INSTRUCTION__PROGRAM_ID_MISMATCH3 = 4128002;
var SOLANA_ERROR__INSTRUCTION_ERROR__UNKNOWN3 = 4615e3;
var SOLANA_ERROR__INSTRUCTION_ERROR__GENERIC_ERROR3 = 4615001;
var SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ARGUMENT3 = 4615002;
var SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_INSTRUCTION_DATA3 = 4615003;
var SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ACCOUNT_DATA3 = 4615004;
var SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_DATA_TOO_SMALL3 = 4615005;
var SOLANA_ERROR__INSTRUCTION_ERROR__INSUFFICIENT_FUNDS3 = 4615006;
var SOLANA_ERROR__INSTRUCTION_ERROR__INCORRECT_PROGRAM_ID3 = 4615007;
var SOLANA_ERROR__INSTRUCTION_ERROR__MISSING_REQUIRED_SIGNATURE3 = 4615008;
var SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_ALREADY_INITIALIZED3 = 4615009;
var SOLANA_ERROR__INSTRUCTION_ERROR__UNINITIALIZED_ACCOUNT3 = 4615010;
var SOLANA_ERROR__INSTRUCTION_ERROR__UNBALANCED_INSTRUCTION3 = 4615011;
var SOLANA_ERROR__INSTRUCTION_ERROR__MODIFIED_PROGRAM_ID3 = 4615012;
var SOLANA_ERROR__INSTRUCTION_ERROR__EXTERNAL_ACCOUNT_LAMPORT_SPEND3 = 4615013;
var SOLANA_ERROR__INSTRUCTION_ERROR__EXTERNAL_ACCOUNT_DATA_MODIFIED3 = 4615014;
var SOLANA_ERROR__INSTRUCTION_ERROR__READONLY_LAMPORT_CHANGE3 = 4615015;
var SOLANA_ERROR__INSTRUCTION_ERROR__READONLY_DATA_MODIFIED3 = 4615016;
var SOLANA_ERROR__INSTRUCTION_ERROR__DUPLICATE_ACCOUNT_INDEX3 = 4615017;
var SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_MODIFIED3 = 4615018;
var SOLANA_ERROR__INSTRUCTION_ERROR__RENT_EPOCH_MODIFIED3 = 4615019;
var SOLANA_ERROR__INSTRUCTION_ERROR__NOT_ENOUGH_ACCOUNT_KEYS3 = 4615020;
var SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_DATA_SIZE_CHANGED3 = 4615021;
var SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_NOT_EXECUTABLE3 = 4615022;
var SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_BORROW_FAILED3 = 4615023;
var SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_BORROW_OUTSTANDING3 = 4615024;
var SOLANA_ERROR__INSTRUCTION_ERROR__DUPLICATE_ACCOUNT_OUT_OF_SYNC3 = 4615025;
var SOLANA_ERROR__INSTRUCTION_ERROR__CUSTOM3 = 4615026;
var SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ERROR3 = 4615027;
var SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_DATA_MODIFIED3 = 4615028;
var SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_LAMPORT_CHANGE3 = 4615029;
var SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_ACCOUNT_NOT_RENT_EXEMPT3 = 4615030;
var SOLANA_ERROR__INSTRUCTION_ERROR__UNSUPPORTED_PROGRAM_ID3 = 4615031;
var SOLANA_ERROR__INSTRUCTION_ERROR__CALL_DEPTH3 = 4615032;
var SOLANA_ERROR__INSTRUCTION_ERROR__MISSING_ACCOUNT3 = 4615033;
var SOLANA_ERROR__INSTRUCTION_ERROR__REENTRANCY_NOT_ALLOWED3 = 4615034;
var SOLANA_ERROR__INSTRUCTION_ERROR__MAX_SEED_LENGTH_EXCEEDED3 = 4615035;
var SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_SEEDS3 = 4615036;
var SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_REALLOC3 = 4615037;
var SOLANA_ERROR__INSTRUCTION_ERROR__COMPUTATIONAL_BUDGET_EXCEEDED3 = 4615038;
var SOLANA_ERROR__INSTRUCTION_ERROR__PRIVILEGE_ESCALATION3 = 4615039;
var SOLANA_ERROR__INSTRUCTION_ERROR__PROGRAM_ENVIRONMENT_SETUP_FAILURE3 = 4615040;
var SOLANA_ERROR__INSTRUCTION_ERROR__PROGRAM_FAILED_TO_COMPLETE3 = 4615041;
var SOLANA_ERROR__INSTRUCTION_ERROR__PROGRAM_FAILED_TO_COMPILE3 = 4615042;
var SOLANA_ERROR__INSTRUCTION_ERROR__IMMUTABLE3 = 4615043;
var SOLANA_ERROR__INSTRUCTION_ERROR__INCORRECT_AUTHORITY3 = 4615044;
var SOLANA_ERROR__INSTRUCTION_ERROR__BORSH_IO_ERROR3 = 4615045;
var SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_NOT_RENT_EXEMPT3 = 4615046;
var SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ACCOUNT_OWNER3 = 4615047;
var SOLANA_ERROR__INSTRUCTION_ERROR__ARITHMETIC_OVERFLOW3 = 4615048;
var SOLANA_ERROR__INSTRUCTION_ERROR__UNSUPPORTED_SYSVAR3 = 4615049;
var SOLANA_ERROR__INSTRUCTION_ERROR__ILLEGAL_OWNER3 = 4615050;
var SOLANA_ERROR__INSTRUCTION_ERROR__MAX_ACCOUNTS_DATA_ALLOCATIONS_EXCEEDED3 = 4615051;
var SOLANA_ERROR__INSTRUCTION_ERROR__MAX_ACCOUNTS_EXCEEDED3 = 4615052;
var SOLANA_ERROR__INSTRUCTION_ERROR__MAX_INSTRUCTION_TRACE_LENGTH_EXCEEDED3 = 4615053;
var SOLANA_ERROR__INSTRUCTION_ERROR__BUILTIN_PROGRAMS_MUST_CONSUME_COMPUTE_UNITS3 = 4615054;
var SOLANA_ERROR__SIGNER__ADDRESS_CANNOT_HAVE_MULTIPLE_SIGNERS3 = 5508e3;
var SOLANA_ERROR__SIGNER__EXPECTED_KEY_PAIR_SIGNER3 = 5508001;
var SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_SIGNER3 = 5508002;
var SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_MODIFYING_SIGNER3 = 5508003;
var SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_PARTIAL_SIGNER3 = 5508004;
var SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_SIGNER3 = 5508005;
var SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_MODIFYING_SIGNER3 = 5508006;
var SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_PARTIAL_SIGNER3 = 5508007;
var SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_SENDING_SIGNER3 = 5508008;
var SOLANA_ERROR__SIGNER__TRANSACTION_CANNOT_HAVE_MULTIPLE_SENDING_SIGNERS3 = 5508009;
var SOLANA_ERROR__SIGNER__TRANSACTION_SENDING_SIGNER_MISSING3 = 5508010;
var SOLANA_ERROR__SIGNER__WALLET_MULTISIGN_UNIMPLEMENTED3 = 5508011;
var SOLANA_ERROR__TRANSACTION__INVOKED_PROGRAMS_CANNOT_PAY_FEES3 = 5663e3;
var SOLANA_ERROR__TRANSACTION__INVOKED_PROGRAMS_MUST_NOT_BE_WRITABLE3 = 5663001;
var SOLANA_ERROR__TRANSACTION__EXPECTED_BLOCKHASH_LIFETIME3 = 5663002;
var SOLANA_ERROR__TRANSACTION__EXPECTED_NONCE_LIFETIME3 = 5663003;
var SOLANA_ERROR__TRANSACTION__VERSION_NUMBER_OUT_OF_RANGE3 = 5663004;
var SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_ADDRESS_LOOKUP_TABLE_CONTENTS_MISSING3 = 5663005;
var SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_ADDRESS_LOOKUP_TABLE_INDEX_OUT_OF_RANGE3 = 5663006;
var SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_INSTRUCTION_PROGRAM_ADDRESS_NOT_FOUND3 = 5663007;
var SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_FEE_PAYER_MISSING3 = 5663008;
var SOLANA_ERROR__TRANSACTION__SIGNATURES_MISSING3 = 5663009;
var SOLANA_ERROR__TRANSACTION__ADDRESS_MISSING3 = 5663010;
var SOLANA_ERROR__TRANSACTION__FEE_PAYER_MISSING3 = 5663011;
var SOLANA_ERROR__TRANSACTION__FEE_PAYER_SIGNATURE_MISSING3 = 5663012;
var SOLANA_ERROR__TRANSACTION__INVALID_NONCE_TRANSACTION_INSTRUCTIONS_MISSING3 = 5663013;
var SOLANA_ERROR__TRANSACTION__INVALID_NONCE_TRANSACTION_FIRST_INSTRUCTION_MUST_BE_ADVANCE_NONCE3 = 5663014;
var SOLANA_ERROR__TRANSACTION__ADDRESSES_CANNOT_SIGN_TRANSACTION3 = 5663015;
var SOLANA_ERROR__TRANSACTION__CANNOT_ENCODE_WITH_EMPTY_SIGNATURES3 = 5663016;
var SOLANA_ERROR__TRANSACTION__MESSAGE_SIGNATURES_MISMATCH3 = 5663017;
var SOLANA_ERROR__TRANSACTION__FAILED_TO_ESTIMATE_COMPUTE_LIMIT3 = 5663018;
var SOLANA_ERROR__TRANSACTION_ERROR__UNKNOWN3 = 705e4;
var SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_IN_USE3 = 7050001;
var SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_LOADED_TWICE3 = 7050002;
var SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_NOT_FOUND3 = 7050003;
var SOLANA_ERROR__TRANSACTION_ERROR__PROGRAM_ACCOUNT_NOT_FOUND3 = 7050004;
var SOLANA_ERROR__TRANSACTION_ERROR__INSUFFICIENT_FUNDS_FOR_FEE3 = 7050005;
var SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ACCOUNT_FOR_FEE3 = 7050006;
var SOLANA_ERROR__TRANSACTION_ERROR__ALREADY_PROCESSED3 = 7050007;
var SOLANA_ERROR__TRANSACTION_ERROR__BLOCKHASH_NOT_FOUND3 = 7050008;
var SOLANA_ERROR__TRANSACTION_ERROR__CALL_CHAIN_TOO_DEEP3 = 7050009;
var SOLANA_ERROR__TRANSACTION_ERROR__MISSING_SIGNATURE_FOR_FEE3 = 7050010;
var SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ACCOUNT_INDEX3 = 7050011;
var SOLANA_ERROR__TRANSACTION_ERROR__SIGNATURE_FAILURE3 = 7050012;
var SOLANA_ERROR__TRANSACTION_ERROR__INVALID_PROGRAM_FOR_EXECUTION3 = 7050013;
var SOLANA_ERROR__TRANSACTION_ERROR__SANITIZE_FAILURE3 = 7050014;
var SOLANA_ERROR__TRANSACTION_ERROR__CLUSTER_MAINTENANCE3 = 7050015;
var SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_BORROW_OUTSTANDING3 = 7050016;
var SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_MAX_BLOCK_COST_LIMIT3 = 7050017;
var SOLANA_ERROR__TRANSACTION_ERROR__UNSUPPORTED_VERSION3 = 7050018;
var SOLANA_ERROR__TRANSACTION_ERROR__INVALID_WRITABLE_ACCOUNT3 = 7050019;
var SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_MAX_ACCOUNT_COST_LIMIT3 = 7050020;
var SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_ACCOUNT_DATA_BLOCK_LIMIT3 = 7050021;
var SOLANA_ERROR__TRANSACTION_ERROR__TOO_MANY_ACCOUNT_LOCKS3 = 7050022;
var SOLANA_ERROR__TRANSACTION_ERROR__ADDRESS_LOOKUP_TABLE_NOT_FOUND3 = 7050023;
var SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ADDRESS_LOOKUP_TABLE_OWNER3 = 7050024;
var SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ADDRESS_LOOKUP_TABLE_DATA3 = 7050025;
var SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ADDRESS_LOOKUP_TABLE_INDEX3 = 7050026;
var SOLANA_ERROR__TRANSACTION_ERROR__INVALID_RENT_PAYING_ACCOUNT3 = 7050027;
var SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_MAX_VOTE_COST_LIMIT3 = 7050028;
var SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_ACCOUNT_DATA_TOTAL_LIMIT3 = 7050029;
var SOLANA_ERROR__TRANSACTION_ERROR__DUPLICATE_INSTRUCTION3 = 7050030;
var SOLANA_ERROR__TRANSACTION_ERROR__INSUFFICIENT_FUNDS_FOR_RENT3 = 7050031;
var SOLANA_ERROR__TRANSACTION_ERROR__MAX_LOADED_ACCOUNTS_DATA_SIZE_EXCEEDED3 = 7050032;
var SOLANA_ERROR__TRANSACTION_ERROR__INVALID_LOADED_ACCOUNTS_DATA_SIZE_LIMIT3 = 7050033;
var SOLANA_ERROR__TRANSACTION_ERROR__RESANITIZATION_NEEDED3 = 7050034;
var SOLANA_ERROR__TRANSACTION_ERROR__PROGRAM_EXECUTION_TEMPORARILY_RESTRICTED3 = 7050035;
var SOLANA_ERROR__TRANSACTION_ERROR__UNBALANCED_TRANSACTION3 = 7050036;
var SOLANA_ERROR__CODECS__CANNOT_DECODE_EMPTY_BYTE_ARRAY3 = 8078e3;
var SOLANA_ERROR__CODECS__INVALID_BYTE_LENGTH3 = 8078001;
var SOLANA_ERROR__CODECS__EXPECTED_FIXED_LENGTH3 = 8078002;
var SOLANA_ERROR__CODECS__EXPECTED_VARIABLE_LENGTH3 = 8078003;
var SOLANA_ERROR__CODECS__ENCODER_DECODER_SIZE_COMPATIBILITY_MISMATCH3 = 8078004;
var SOLANA_ERROR__CODECS__ENCODER_DECODER_FIXED_SIZE_MISMATCH3 = 8078005;
var SOLANA_ERROR__CODECS__ENCODER_DECODER_MAX_SIZE_MISMATCH3 = 8078006;
var SOLANA_ERROR__CODECS__INVALID_NUMBER_OF_ITEMS3 = 8078007;
var SOLANA_ERROR__CODECS__ENUM_DISCRIMINATOR_OUT_OF_RANGE3 = 8078008;
var SOLANA_ERROR__CODECS__INVALID_DISCRIMINATED_UNION_VARIANT3 = 8078009;
var SOLANA_ERROR__CODECS__INVALID_ENUM_VARIANT3 = 8078010;
var SOLANA_ERROR__CODECS__NUMBER_OUT_OF_RANGE3 = 8078011;
var SOLANA_ERROR__CODECS__INVALID_STRING_FOR_BASE3 = 8078012;
var SOLANA_ERROR__CODECS__EXPECTED_POSITIVE_BYTE_LENGTH3 = 8078013;
var SOLANA_ERROR__CODECS__OFFSET_OUT_OF_RANGE3 = 8078014;
var SOLANA_ERROR__CODECS__INVALID_LITERAL_UNION_VARIANT3 = 8078015;
var SOLANA_ERROR__CODECS__LITERAL_UNION_DISCRIMINATOR_OUT_OF_RANGE3 = 8078016;
var SOLANA_ERROR__CODECS__UNION_VARIANT_OUT_OF_RANGE3 = 8078017;
var SOLANA_ERROR__CODECS__INVALID_CONSTANT3 = 8078018;
var SOLANA_ERROR__CODECS__EXPECTED_ZERO_VALUE_TO_MATCH_ITEM_FIXED_SIZE3 = 8078019;
var SOLANA_ERROR__CODECS__ENCODED_BYTES_MUST_NOT_INCLUDE_SENTINEL3 = 8078020;
var SOLANA_ERROR__CODECS__SENTINEL_MISSING_IN_DECODED_BYTES3 = 8078021;
var SOLANA_ERROR__CODECS__CANNOT_USE_LEXICAL_VALUES_AS_ENUM_DISCRIMINATORS3 = 8078022;
var SOLANA_ERROR__RPC__INTEGER_OVERFLOW3 = 81e5;
var SOLANA_ERROR__RPC__TRANSPORT_HTTP_HEADER_FORBIDDEN3 = 8100001;
var SOLANA_ERROR__RPC__TRANSPORT_HTTP_ERROR3 = 8100002;
var SOLANA_ERROR__RPC_SUBSCRIPTIONS__CANNOT_CREATE_SUBSCRIPTION_REQUEST3 = 819e4;
var SOLANA_ERROR__RPC_SUBSCRIPTIONS__EXPECTED_SERVER_SUBSCRIPTION_ID3 = 8190001;
var SOLANA_ERROR__RPC_SUBSCRIPTIONS__TRANSPORT_CLOSED_BEFORE_MESSAGE_BUFFERED3 = 8190002;
var SOLANA_ERROR__RPC_SUBSCRIPTIONS__TRANSPORT_CONNECTION_CLOSED3 = 8190003;
var SOLANA_ERROR__RPC_SUBSCRIPTIONS__TRANSPORT_FAILED_TO_CONNECT3 = 8190004;
var SOLANA_ERROR__INVARIANT_VIOLATION__WEBSOCKET_MESSAGE_ITERATOR_STATE_MISSING3 = 99e5;
var SOLANA_ERROR__INVARIANT_VIOLATION__WEBSOCKET_MESSAGE_ITERATOR_MUST_NOT_POLL_BEFORE_RESOLVING_EXISTING_MESSAGE_PROMISE3 = 9900001;
var SOLANA_ERROR__INVARIANT_VIOLATION__CACHED_ABORTABLE_ITERABLE_CACHE_ENTRY_MISSING3 = 9900002;
var SOLANA_ERROR__INVARIANT_VIOLATION__SWITCH_MUST_BE_EXHAUSTIVE3 = 9900003;
var SolanaErrorMessages3 = {
  [SOLANA_ERROR__ACCOUNTS__ACCOUNT_NOT_FOUND3]: "Account not found at address: $address",
  [SOLANA_ERROR__ACCOUNTS__EXPECTED_ALL_ACCOUNTS_TO_BE_DECODED3]: "Not all accounts were decoded. Encoded accounts found at addresses: $addresses.",
  [SOLANA_ERROR__ACCOUNTS__EXPECTED_DECODED_ACCOUNT3]: "Expected decoded account at address: $address",
  [SOLANA_ERROR__ACCOUNTS__FAILED_TO_DECODE_ACCOUNT3]: "Failed to decode account data at address: $address",
  [SOLANA_ERROR__ACCOUNTS__ONE_OR_MORE_ACCOUNTS_NOT_FOUND3]: "Accounts not found at addresses: $addresses",
  [SOLANA_ERROR__ADDRESSES__FAILED_TO_FIND_VIABLE_PDA_BUMP_SEED3]: "Unable to find a viable program address bump seed.",
  [SOLANA_ERROR__ADDRESSES__INVALID_BASE58_ENCODED_ADDRESS3]: "$putativeAddress is not a base58-encoded address.",
  [SOLANA_ERROR__ADDRESSES__INVALID_BYTE_LENGTH3]: "Expected base58 encoded address to decode to a byte array of length 32. Actual length: $actualLength.",
  [SOLANA_ERROR__ADDRESSES__INVALID_ED25519_PUBLIC_KEY3]: "The `CryptoKey` must be an `Ed25519` public key.",
  [SOLANA_ERROR__ADDRESSES__INVALID_SEEDS_POINT_ON_CURVE3]: "Invalid seeds; point must fall off the Ed25519 curve.",
  [SOLANA_ERROR__ADDRESSES__MALFORMED_PDA3]: "Expected given program derived address to have the following format: [Address, ProgramDerivedAddressBump].",
  [SOLANA_ERROR__ADDRESSES__MAX_NUMBER_OF_PDA_SEEDS_EXCEEDED3]: "A maximum of $maxSeeds seeds, including the bump seed, may be supplied when creating an address. Received: $actual.",
  [SOLANA_ERROR__ADDRESSES__MAX_PDA_SEED_LENGTH_EXCEEDED3]: "The seed at index $index with length $actual exceeds the maximum length of $maxSeedLength bytes.",
  [SOLANA_ERROR__ADDRESSES__PDA_BUMP_SEED_OUT_OF_RANGE3]: "Expected program derived address bump to be in the range [0, 255], got: $bump.",
  [SOLANA_ERROR__ADDRESSES__PDA_ENDS_WITH_PDA_MARKER3]: "Program address cannot end with PDA marker.",
  [SOLANA_ERROR__ADDRESSES__STRING_LENGTH_OUT_OF_RANGE3]: "Expected base58-encoded address string of length in the range [32, 44]. Actual length: $actualLength.",
  [SOLANA_ERROR__BLOCKHASH_STRING_LENGTH_OUT_OF_RANGE3]: "Expected base58-encoded blockash string of length in the range [32, 44]. Actual length: $actualLength.",
  [SOLANA_ERROR__BLOCK_HEIGHT_EXCEEDED3]: "The network has progressed past the last block for which this transaction could have been committed.",
  [SOLANA_ERROR__CODECS__CANNOT_DECODE_EMPTY_BYTE_ARRAY3]: "Codec [$codecDescription] cannot decode empty byte arrays.",
  [SOLANA_ERROR__CODECS__CANNOT_USE_LEXICAL_VALUES_AS_ENUM_DISCRIMINATORS3]: "Enum codec cannot use lexical values [$stringValues] as discriminators. Either remove all lexical values or set `useValuesAsDiscriminators` to `false`.",
  [SOLANA_ERROR__CODECS__ENCODED_BYTES_MUST_NOT_INCLUDE_SENTINEL3]: "Sentinel [$hexSentinel] must not be present in encoded bytes [$hexEncodedBytes].",
  [SOLANA_ERROR__CODECS__ENCODER_DECODER_FIXED_SIZE_MISMATCH3]: "Encoder and decoder must have the same fixed size, got [$encoderFixedSize] and [$decoderFixedSize].",
  [SOLANA_ERROR__CODECS__ENCODER_DECODER_MAX_SIZE_MISMATCH3]: "Encoder and decoder must have the same max size, got [$encoderMaxSize] and [$decoderMaxSize].",
  [SOLANA_ERROR__CODECS__ENCODER_DECODER_SIZE_COMPATIBILITY_MISMATCH3]: "Encoder and decoder must either both be fixed-size or variable-size.",
  [SOLANA_ERROR__CODECS__ENUM_DISCRIMINATOR_OUT_OF_RANGE3]: "Enum discriminator out of range. Expected a number in [$formattedValidDiscriminators], got $discriminator.",
  [SOLANA_ERROR__CODECS__EXPECTED_FIXED_LENGTH3]: "Expected a fixed-size codec, got a variable-size one.",
  [SOLANA_ERROR__CODECS__EXPECTED_POSITIVE_BYTE_LENGTH3]: "Codec [$codecDescription] expected a positive byte length, got $bytesLength.",
  [SOLANA_ERROR__CODECS__EXPECTED_VARIABLE_LENGTH3]: "Expected a variable-size codec, got a fixed-size one.",
  [SOLANA_ERROR__CODECS__EXPECTED_ZERO_VALUE_TO_MATCH_ITEM_FIXED_SIZE3]: "Codec [$codecDescription] expected zero-value [$hexZeroValue] to have the same size as the provided fixed-size item [$expectedSize bytes].",
  [SOLANA_ERROR__CODECS__INVALID_BYTE_LENGTH3]: "Codec [$codecDescription] expected $expected bytes, got $bytesLength.",
  [SOLANA_ERROR__CODECS__INVALID_CONSTANT3]: "Expected byte array constant [$hexConstant] to be present in data [$hexData] at offset [$offset].",
  [SOLANA_ERROR__CODECS__INVALID_DISCRIMINATED_UNION_VARIANT3]: "Invalid discriminated union variant. Expected one of [$variants], got $value.",
  [SOLANA_ERROR__CODECS__INVALID_ENUM_VARIANT3]: "Invalid enum variant. Expected one of [$stringValues] or a number in [$formattedNumericalValues], got $variant.",
  [SOLANA_ERROR__CODECS__INVALID_LITERAL_UNION_VARIANT3]: "Invalid literal union variant. Expected one of [$variants], got $value.",
  [SOLANA_ERROR__CODECS__INVALID_NUMBER_OF_ITEMS3]: "Expected [$codecDescription] to have $expected items, got $actual.",
  [SOLANA_ERROR__CODECS__INVALID_STRING_FOR_BASE3]: "Invalid value $value for base $base with alphabet $alphabet.",
  [SOLANA_ERROR__CODECS__LITERAL_UNION_DISCRIMINATOR_OUT_OF_RANGE3]: "Literal union discriminator out of range. Expected a number between $minRange and $maxRange, got $discriminator.",
  [SOLANA_ERROR__CODECS__NUMBER_OUT_OF_RANGE3]: "Codec [$codecDescription] expected number to be in the range [$min, $max], got $value.",
  [SOLANA_ERROR__CODECS__OFFSET_OUT_OF_RANGE3]: "Codec [$codecDescription] expected offset to be in the range [0, $bytesLength], got $offset.",
  [SOLANA_ERROR__CODECS__SENTINEL_MISSING_IN_DECODED_BYTES3]: "Expected sentinel [$hexSentinel] to be present in decoded bytes [$hexDecodedBytes].",
  [SOLANA_ERROR__CODECS__UNION_VARIANT_OUT_OF_RANGE3]: "Union variant out of range. Expected an index between $minRange and $maxRange, got $variant.",
  [SOLANA_ERROR__CRYPTO__RANDOM_VALUES_FUNCTION_UNIMPLEMENTED3]: "No random values implementation could be found.",
  [SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_ALREADY_INITIALIZED3]: "instruction requires an uninitialized account",
  [SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_BORROW_FAILED3]: "instruction tries to borrow reference for an account which is already borrowed",
  [SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_BORROW_OUTSTANDING3]: "instruction left account with an outstanding borrowed reference",
  [SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_DATA_SIZE_CHANGED3]: "program other than the account's owner changed the size of the account data",
  [SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_DATA_TOO_SMALL3]: "account data too small for instruction",
  [SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_NOT_EXECUTABLE3]: "instruction expected an executable account",
  [SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_NOT_RENT_EXEMPT3]: "An account does not have enough lamports to be rent-exempt",
  [SOLANA_ERROR__INSTRUCTION_ERROR__ARITHMETIC_OVERFLOW3]: "Program arithmetic overflowed",
  [SOLANA_ERROR__INSTRUCTION_ERROR__BORSH_IO_ERROR3]: "Failed to serialize or deserialize account data: $encodedData",
  [SOLANA_ERROR__INSTRUCTION_ERROR__BUILTIN_PROGRAMS_MUST_CONSUME_COMPUTE_UNITS3]: "Builtin programs must consume compute units",
  [SOLANA_ERROR__INSTRUCTION_ERROR__CALL_DEPTH3]: "Cross-program invocation call depth too deep",
  [SOLANA_ERROR__INSTRUCTION_ERROR__COMPUTATIONAL_BUDGET_EXCEEDED3]: "Computational budget exceeded",
  [SOLANA_ERROR__INSTRUCTION_ERROR__CUSTOM3]: "custom program error: #$code",
  [SOLANA_ERROR__INSTRUCTION_ERROR__DUPLICATE_ACCOUNT_INDEX3]: "instruction contains duplicate accounts",
  [SOLANA_ERROR__INSTRUCTION_ERROR__DUPLICATE_ACCOUNT_OUT_OF_SYNC3]: "instruction modifications of multiply-passed account differ",
  [SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_ACCOUNT_NOT_RENT_EXEMPT3]: "executable accounts must be rent exempt",
  [SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_DATA_MODIFIED3]: "instruction changed executable accounts data",
  [SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_LAMPORT_CHANGE3]: "instruction changed the balance of an executable account",
  [SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_MODIFIED3]: "instruction changed executable bit of an account",
  [SOLANA_ERROR__INSTRUCTION_ERROR__EXTERNAL_ACCOUNT_DATA_MODIFIED3]: "instruction modified data of an account it does not own",
  [SOLANA_ERROR__INSTRUCTION_ERROR__EXTERNAL_ACCOUNT_LAMPORT_SPEND3]: "instruction spent from the balance of an account it does not own",
  [SOLANA_ERROR__INSTRUCTION_ERROR__GENERIC_ERROR3]: "generic instruction error",
  [SOLANA_ERROR__INSTRUCTION_ERROR__ILLEGAL_OWNER3]: "Provided owner is not allowed",
  [SOLANA_ERROR__INSTRUCTION_ERROR__IMMUTABLE3]: "Account is immutable",
  [SOLANA_ERROR__INSTRUCTION_ERROR__INCORRECT_AUTHORITY3]: "Incorrect authority provided",
  [SOLANA_ERROR__INSTRUCTION_ERROR__INCORRECT_PROGRAM_ID3]: "incorrect program id for instruction",
  [SOLANA_ERROR__INSTRUCTION_ERROR__INSUFFICIENT_FUNDS3]: "insufficient funds for instruction",
  [SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ACCOUNT_DATA3]: "invalid account data for instruction",
  [SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ACCOUNT_OWNER3]: "Invalid account owner",
  [SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ARGUMENT3]: "invalid program argument",
  [SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ERROR3]: "program returned invalid error code",
  [SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_INSTRUCTION_DATA3]: "invalid instruction data",
  [SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_REALLOC3]: "Failed to reallocate account data",
  [SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_SEEDS3]: "Provided seeds do not result in a valid address",
  [SOLANA_ERROR__INSTRUCTION_ERROR__MAX_ACCOUNTS_DATA_ALLOCATIONS_EXCEEDED3]: "Accounts data allocations exceeded the maximum allowed per transaction",
  [SOLANA_ERROR__INSTRUCTION_ERROR__MAX_ACCOUNTS_EXCEEDED3]: "Max accounts exceeded",
  [SOLANA_ERROR__INSTRUCTION_ERROR__MAX_INSTRUCTION_TRACE_LENGTH_EXCEEDED3]: "Max instruction trace length exceeded",
  [SOLANA_ERROR__INSTRUCTION_ERROR__MAX_SEED_LENGTH_EXCEEDED3]: "Length of the seed is too long for address generation",
  [SOLANA_ERROR__INSTRUCTION_ERROR__MISSING_ACCOUNT3]: "An account required by the instruction is missing",
  [SOLANA_ERROR__INSTRUCTION_ERROR__MISSING_REQUIRED_SIGNATURE3]: "missing required signature for instruction",
  [SOLANA_ERROR__INSTRUCTION_ERROR__MODIFIED_PROGRAM_ID3]: "instruction illegally modified the program id of an account",
  [SOLANA_ERROR__INSTRUCTION_ERROR__NOT_ENOUGH_ACCOUNT_KEYS3]: "insufficient account keys for instruction",
  [SOLANA_ERROR__INSTRUCTION_ERROR__PRIVILEGE_ESCALATION3]: "Cross-program invocation with unauthorized signer or writable account",
  [SOLANA_ERROR__INSTRUCTION_ERROR__PROGRAM_ENVIRONMENT_SETUP_FAILURE3]: "Failed to create program execution environment",
  [SOLANA_ERROR__INSTRUCTION_ERROR__PROGRAM_FAILED_TO_COMPILE3]: "Program failed to compile",
  [SOLANA_ERROR__INSTRUCTION_ERROR__PROGRAM_FAILED_TO_COMPLETE3]: "Program failed to complete",
  [SOLANA_ERROR__INSTRUCTION_ERROR__READONLY_DATA_MODIFIED3]: "instruction modified data of a read-only account",
  [SOLANA_ERROR__INSTRUCTION_ERROR__READONLY_LAMPORT_CHANGE3]: "instruction changed the balance of a read-only account",
  [SOLANA_ERROR__INSTRUCTION_ERROR__REENTRANCY_NOT_ALLOWED3]: "Cross-program invocation reentrancy not allowed for this instruction",
  [SOLANA_ERROR__INSTRUCTION_ERROR__RENT_EPOCH_MODIFIED3]: "instruction modified rent epoch of an account",
  [SOLANA_ERROR__INSTRUCTION_ERROR__UNBALANCED_INSTRUCTION3]: "sum of account balances before and after instruction do not match",
  [SOLANA_ERROR__INSTRUCTION_ERROR__UNINITIALIZED_ACCOUNT3]: "instruction requires an initialized account",
  [SOLANA_ERROR__INSTRUCTION_ERROR__UNKNOWN3]: "",
  [SOLANA_ERROR__INSTRUCTION_ERROR__UNSUPPORTED_PROGRAM_ID3]: "Unsupported program id",
  [SOLANA_ERROR__INSTRUCTION_ERROR__UNSUPPORTED_SYSVAR3]: "Unsupported sysvar",
  [SOLANA_ERROR__INSTRUCTION__EXPECTED_TO_HAVE_ACCOUNTS3]: "The instruction does not have any accounts.",
  [SOLANA_ERROR__INSTRUCTION__EXPECTED_TO_HAVE_DATA3]: "The instruction does not have any data.",
  [SOLANA_ERROR__INSTRUCTION__PROGRAM_ID_MISMATCH3]: "Expected instruction to have progress address $expectedProgramAddress, got $actualProgramAddress.",
  [SOLANA_ERROR__INVALID_BLOCKHASH_BYTE_LENGTH3]: "Expected base58 encoded blockhash to decode to a byte array of length 32. Actual length: $actualLength.",
  [SOLANA_ERROR__INVALID_NONCE3]: "The nonce `$expectedNonceValue` is no longer valid. It has advanced to `$actualNonceValue`",
  [SOLANA_ERROR__INVARIANT_VIOLATION__CACHED_ABORTABLE_ITERABLE_CACHE_ENTRY_MISSING3]: "Invariant violation: Found no abortable iterable cache entry for key `$cacheKey`. It should be impossible to hit this error; please file an issue at https://sola.na/web3invariant",
  [SOLANA_ERROR__INVARIANT_VIOLATION__SWITCH_MUST_BE_EXHAUSTIVE3]: "Invariant violation: Switch statement non-exhaustive. Received unexpected value `$unexpectedValue`. It should be impossible to hit this error; please file an issue at https://sola.na/web3invariant",
  [SOLANA_ERROR__INVARIANT_VIOLATION__WEBSOCKET_MESSAGE_ITERATOR_MUST_NOT_POLL_BEFORE_RESOLVING_EXISTING_MESSAGE_PROMISE3]: "Invariant violation: WebSocket message iterator state is corrupt; iterated without first resolving existing message promise. It should be impossible to hit this error; please file an issue at https://sola.na/web3invariant",
  [SOLANA_ERROR__INVARIANT_VIOLATION__WEBSOCKET_MESSAGE_ITERATOR_STATE_MISSING3]: "Invariant violation: WebSocket message iterator is missing state storage. It should be impossible to hit this error; please file an issue at https://sola.na/web3invariant",
  [SOLANA_ERROR__JSON_RPC__INTERNAL_ERROR3]: "JSON-RPC error: Internal JSON-RPC error ($__serverMessage)",
  [SOLANA_ERROR__JSON_RPC__INVALID_PARAMS3]: "JSON-RPC error: Invalid method parameter(s) ($__serverMessage)",
  [SOLANA_ERROR__JSON_RPC__INVALID_REQUEST3]: "JSON-RPC error: The JSON sent is not a valid `Request` object ($__serverMessage)",
  [SOLANA_ERROR__JSON_RPC__METHOD_NOT_FOUND3]: "JSON-RPC error: The method does not exist / is not available ($__serverMessage)",
  [SOLANA_ERROR__JSON_RPC__PARSE_ERROR3]: "JSON-RPC error: An error occurred on the server while parsing the JSON text ($__serverMessage)",
  [SOLANA_ERROR__JSON_RPC__SCAN_ERROR3]: "$__serverMessage",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_CLEANED_UP3]: "$__serverMessage",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_NOT_AVAILABLE3]: "$__serverMessage",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_STATUS_NOT_AVAILABLE_YET3]: "$__serverMessage",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_KEY_EXCLUDED_FROM_SECONDARY_INDEX3]: "$__serverMessage",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_LONG_TERM_STORAGE_SLOT_SKIPPED3]: "$__serverMessage",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_MIN_CONTEXT_SLOT_NOT_REACHED3]: "Minimum context slot has not been reached",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_NODE_UNHEALTHY3]: "Node is unhealthy; behind by $numSlotsBehind slots",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_NO_SNAPSHOT3]: "No snapshot",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_SEND_TRANSACTION_PREFLIGHT_FAILURE3]: "Transaction simulation failed",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_SLOT_SKIPPED3]: "$__serverMessage",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_HISTORY_NOT_AVAILABLE3]: "Transaction history is not available from this node",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_PRECOMPILE_VERIFICATION_FAILURE3]: "$__serverMessage",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_SIGNATURE_LEN_MISMATCH3]: "Transaction signature length mismatch",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_SIGNATURE_VERIFICATION_FAILURE3]: "Transaction signature verification failure",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_UNSUPPORTED_TRANSACTION_VERSION3]: "$__serverMessage",
  [SOLANA_ERROR__KEYS__INVALID_KEY_PAIR_BYTE_LENGTH3]: "Key pair bytes must be of length 64, got $byteLength.",
  [SOLANA_ERROR__KEYS__INVALID_PRIVATE_KEY_BYTE_LENGTH3]: "Expected private key bytes with length 32. Actual length: $actualLength.",
  [SOLANA_ERROR__KEYS__INVALID_SIGNATURE_BYTE_LENGTH3]: "Expected base58-encoded signature to decode to a byte array of length 64. Actual length: $actualLength.",
  [SOLANA_ERROR__KEYS__PUBLIC_KEY_MUST_MATCH_PRIVATE_KEY3]: "The provided private key does not match the provided public key.",
  [SOLANA_ERROR__KEYS__SIGNATURE_STRING_LENGTH_OUT_OF_RANGE3]: "Expected base58-encoded signature string of length in the range [64, 88]. Actual length: $actualLength.",
  [SOLANA_ERROR__LAMPORTS_OUT_OF_RANGE3]: "Lamports value must be in the range [0, 2e64-1]",
  [SOLANA_ERROR__MALFORMED_BIGINT_STRING3]: "`$value` cannot be parsed as a `BigInt`",
  [SOLANA_ERROR__MALFORMED_NUMBER_STRING3]: "`$value` cannot be parsed as a `Number`",
  [SOLANA_ERROR__NONCE_ACCOUNT_NOT_FOUND3]: "No nonce account could be found at address `$nonceAccountAddress`",
  [SOLANA_ERROR__RPC_SUBSCRIPTIONS__CANNOT_CREATE_SUBSCRIPTION_REQUEST3]: "Either the notification name must end in 'Notifications' or the API must supply a subscription creator function for the notification '$notificationName' to map between the notification name and the subscribe/unsubscribe method names.",
  [SOLANA_ERROR__RPC_SUBSCRIPTIONS__EXPECTED_SERVER_SUBSCRIPTION_ID3]: "Failed to obtain a subscription id from the server",
  [SOLANA_ERROR__RPC_SUBSCRIPTIONS__TRANSPORT_CLOSED_BEFORE_MESSAGE_BUFFERED3]: "WebSocket was closed before payload could be added to the send buffer",
  [SOLANA_ERROR__RPC_SUBSCRIPTIONS__TRANSPORT_CONNECTION_CLOSED3]: "WebSocket connection closed",
  [SOLANA_ERROR__RPC_SUBSCRIPTIONS__TRANSPORT_FAILED_TO_CONNECT3]: "WebSocket failed to connect",
  [SOLANA_ERROR__RPC__INTEGER_OVERFLOW3]: "The $argumentLabel argument to the `$methodName` RPC method$optionalPathLabel was `$value`. This number is unsafe for use with the Solana JSON-RPC because it exceeds `Number.MAX_SAFE_INTEGER`.",
  [SOLANA_ERROR__RPC__TRANSPORT_HTTP_ERROR3]: "HTTP error ($statusCode): $message",
  [SOLANA_ERROR__RPC__TRANSPORT_HTTP_HEADER_FORBIDDEN3]: "HTTP header(s) forbidden: $headers. Learn more at https://developer.mozilla.org/en-US/docs/Glossary/Forbidden_header_name.",
  [SOLANA_ERROR__SIGNER__ADDRESS_CANNOT_HAVE_MULTIPLE_SIGNERS3]: "Multiple distinct signers were identified for address `$address`. Please ensure that you are using the same signer instance for each address.",
  [SOLANA_ERROR__SIGNER__EXPECTED_KEY_PAIR_SIGNER3]: "The provided value does not implement the `KeyPairSigner` interface",
  [SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_MODIFYING_SIGNER3]: "The provided value does not implement the `MessageModifyingSigner` interface",
  [SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_PARTIAL_SIGNER3]: "The provided value does not implement the `MessagePartialSigner` interface",
  [SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_SIGNER3]: "The provided value does not implement any of the `MessageSigner` interfaces",
  [SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_MODIFYING_SIGNER3]: "The provided value does not implement the `TransactionModifyingSigner` interface",
  [SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_PARTIAL_SIGNER3]: "The provided value does not implement the `TransactionPartialSigner` interface",
  [SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_SENDING_SIGNER3]: "The provided value does not implement the `TransactionSendingSigner` interface",
  [SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_SIGNER3]: "The provided value does not implement any of the `TransactionSigner` interfaces",
  [SOLANA_ERROR__SIGNER__TRANSACTION_CANNOT_HAVE_MULTIPLE_SENDING_SIGNERS3]: "More than one `TransactionSendingSigner` was identified.",
  [SOLANA_ERROR__SIGNER__TRANSACTION_SENDING_SIGNER_MISSING3]: "No `TransactionSendingSigner` was identified. Please provide a valid `ITransactionWithSingleSendingSigner` transaction.",
  [SOLANA_ERROR__SIGNER__WALLET_MULTISIGN_UNIMPLEMENTED3]: "Wallet account signers do not support signing multiple messages/transactions in a single operation",
  [SOLANA_ERROR__SUBTLE_CRYPTO__CANNOT_EXPORT_NON_EXTRACTABLE_KEY3]: "Cannot export a non-extractable key.",
  [SOLANA_ERROR__SUBTLE_CRYPTO__DIGEST_UNIMPLEMENTED3]: "No digest implementation could be found.",
  [SOLANA_ERROR__SUBTLE_CRYPTO__DISALLOWED_IN_INSECURE_CONTEXT3]: "Cryptographic operations are only allowed in secure browser contexts. Read more here: https://developer.mozilla.org/en-US/docs/Web/Security/Secure_Contexts.",
  [SOLANA_ERROR__SUBTLE_CRYPTO__ED25519_ALGORITHM_UNIMPLEMENTED3]: "This runtime does not support the generation of Ed25519 key pairs.\n\nInstall @solana/webcrypto-ed25519-polyfill and call its `install` function before generating keys in environments that do not support Ed25519.\n\nFor a list of runtimes that currently support Ed25519 operations, visit https://github.com/WICG/webcrypto-secure-curves/issues/20.",
  [SOLANA_ERROR__SUBTLE_CRYPTO__EXPORT_FUNCTION_UNIMPLEMENTED3]: "No signature verification implementation could be found.",
  [SOLANA_ERROR__SUBTLE_CRYPTO__GENERATE_FUNCTION_UNIMPLEMENTED3]: "No key generation implementation could be found.",
  [SOLANA_ERROR__SUBTLE_CRYPTO__SIGN_FUNCTION_UNIMPLEMENTED3]: "No signing implementation could be found.",
  [SOLANA_ERROR__SUBTLE_CRYPTO__VERIFY_FUNCTION_UNIMPLEMENTED3]: "No key export implementation could be found.",
  [SOLANA_ERROR__TIMESTAMP_OUT_OF_RANGE3]: "Timestamp value must be in the range [-8.64e15, 8.64e15]. `$value` given",
  [SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_BORROW_OUTSTANDING3]: "Transaction processing left an account with an outstanding borrowed reference",
  [SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_IN_USE3]: "Account in use",
  [SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_LOADED_TWICE3]: "Account loaded twice",
  [SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_NOT_FOUND3]: "Attempt to debit an account but found no record of a prior credit.",
  [SOLANA_ERROR__TRANSACTION_ERROR__ADDRESS_LOOKUP_TABLE_NOT_FOUND3]: "Transaction loads an address table account that doesn't exist",
  [SOLANA_ERROR__TRANSACTION_ERROR__ALREADY_PROCESSED3]: "This transaction has already been processed",
  [SOLANA_ERROR__TRANSACTION_ERROR__BLOCKHASH_NOT_FOUND3]: "Blockhash not found",
  [SOLANA_ERROR__TRANSACTION_ERROR__CALL_CHAIN_TOO_DEEP3]: "Loader call chain is too deep",
  [SOLANA_ERROR__TRANSACTION_ERROR__CLUSTER_MAINTENANCE3]: "Transactions are currently disabled due to cluster maintenance",
  [SOLANA_ERROR__TRANSACTION_ERROR__DUPLICATE_INSTRUCTION3]: "Transaction contains a duplicate instruction ($index) that is not allowed",
  [SOLANA_ERROR__TRANSACTION_ERROR__INSUFFICIENT_FUNDS_FOR_FEE3]: "Insufficient funds for fee",
  [SOLANA_ERROR__TRANSACTION_ERROR__INSUFFICIENT_FUNDS_FOR_RENT3]: "Transaction results in an account ($accountIndex) with insufficient funds for rent",
  [SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ACCOUNT_FOR_FEE3]: "This account may not be used to pay transaction fees",
  [SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ACCOUNT_INDEX3]: "Transaction contains an invalid account reference",
  [SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ADDRESS_LOOKUP_TABLE_DATA3]: "Transaction loads an address table account with invalid data",
  [SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ADDRESS_LOOKUP_TABLE_INDEX3]: "Transaction address table lookup uses an invalid index",
  [SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ADDRESS_LOOKUP_TABLE_OWNER3]: "Transaction loads an address table account with an invalid owner",
  [SOLANA_ERROR__TRANSACTION_ERROR__INVALID_LOADED_ACCOUNTS_DATA_SIZE_LIMIT3]: "LoadedAccountsDataSizeLimit set for transaction must be greater than 0.",
  [SOLANA_ERROR__TRANSACTION_ERROR__INVALID_PROGRAM_FOR_EXECUTION3]: "This program may not be used for executing instructions",
  [SOLANA_ERROR__TRANSACTION_ERROR__INVALID_RENT_PAYING_ACCOUNT3]: "Transaction leaves an account with a lower balance than rent-exempt minimum",
  [SOLANA_ERROR__TRANSACTION_ERROR__INVALID_WRITABLE_ACCOUNT3]: "Transaction loads a writable account that cannot be written",
  [SOLANA_ERROR__TRANSACTION_ERROR__MAX_LOADED_ACCOUNTS_DATA_SIZE_EXCEEDED3]: "Transaction exceeded max loaded accounts data size cap",
  [SOLANA_ERROR__TRANSACTION_ERROR__MISSING_SIGNATURE_FOR_FEE3]: "Transaction requires a fee but has no signature present",
  [SOLANA_ERROR__TRANSACTION_ERROR__PROGRAM_ACCOUNT_NOT_FOUND3]: "Attempt to load a program that does not exist",
  [SOLANA_ERROR__TRANSACTION_ERROR__PROGRAM_EXECUTION_TEMPORARILY_RESTRICTED3]: "Execution of the program referenced by account at index $accountIndex is temporarily restricted.",
  [SOLANA_ERROR__TRANSACTION_ERROR__RESANITIZATION_NEEDED3]: "ResanitizationNeeded",
  [SOLANA_ERROR__TRANSACTION_ERROR__SANITIZE_FAILURE3]: "Transaction failed to sanitize accounts offsets correctly",
  [SOLANA_ERROR__TRANSACTION_ERROR__SIGNATURE_FAILURE3]: "Transaction did not pass signature verification",
  [SOLANA_ERROR__TRANSACTION_ERROR__TOO_MANY_ACCOUNT_LOCKS3]: "Transaction locked too many accounts",
  [SOLANA_ERROR__TRANSACTION_ERROR__UNBALANCED_TRANSACTION3]: "Sum of account balances before and after transaction do not match",
  [SOLANA_ERROR__TRANSACTION_ERROR__UNKNOWN3]: "The transaction failed with the error `$errorName`",
  [SOLANA_ERROR__TRANSACTION_ERROR__UNSUPPORTED_VERSION3]: "Transaction version is unsupported",
  [SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_ACCOUNT_DATA_BLOCK_LIMIT3]: "Transaction would exceed account data limit within the block",
  [SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_ACCOUNT_DATA_TOTAL_LIMIT3]: "Transaction would exceed total account data limit",
  [SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_MAX_ACCOUNT_COST_LIMIT3]: "Transaction would exceed max account limit within the block",
  [SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_MAX_BLOCK_COST_LIMIT3]: "Transaction would exceed max Block Cost Limit",
  [SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_MAX_VOTE_COST_LIMIT3]: "Transaction would exceed max Vote Cost Limit",
  [SOLANA_ERROR__TRANSACTION__ADDRESSES_CANNOT_SIGN_TRANSACTION3]: "Attempted to sign a transaction with an address that is not a signer for it",
  [SOLANA_ERROR__TRANSACTION__ADDRESS_MISSING3]: "Transaction is missing an address at index: $index.",
  [SOLANA_ERROR__TRANSACTION__CANNOT_ENCODE_WITH_EMPTY_SIGNATURES3]: "Transaction has no expected signers therefore it cannot be encoded",
  [SOLANA_ERROR__TRANSACTION__EXPECTED_BLOCKHASH_LIFETIME3]: "Transaction does not have a blockhash lifetime",
  [SOLANA_ERROR__TRANSACTION__EXPECTED_NONCE_LIFETIME3]: "Transaction is not a durable nonce transaction",
  [SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_ADDRESS_LOOKUP_TABLE_CONTENTS_MISSING3]: "Contents of these address lookup tables unknown: $lookupTableAddresses",
  [SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_ADDRESS_LOOKUP_TABLE_INDEX_OUT_OF_RANGE3]: "Lookup of address at index $highestRequestedIndex failed for lookup table `$lookupTableAddress`. Highest known index is $highestKnownIndex. The lookup table may have been extended since its contents were retrieved",
  [SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_FEE_PAYER_MISSING3]: "No fee payer set in CompiledTransaction",
  [SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_INSTRUCTION_PROGRAM_ADDRESS_NOT_FOUND3]: "Could not find program address at index $index",
  [SOLANA_ERROR__TRANSACTION__FAILED_TO_ESTIMATE_COMPUTE_LIMIT3]: "Failed to estimate the compute unit consumption for this transaction message. This is likely because simulating the transaction failed. Inspect the `cause` property of this error to learn more",
  [SOLANA_ERROR__TRANSACTION__FEE_PAYER_MISSING3]: "Transaction is missing a fee payer.",
  [SOLANA_ERROR__TRANSACTION__FEE_PAYER_SIGNATURE_MISSING3]: "Could not determine this transaction's signature. Make sure that the transaction has been signed by its fee payer.",
  [SOLANA_ERROR__TRANSACTION__INVALID_NONCE_TRANSACTION_FIRST_INSTRUCTION_MUST_BE_ADVANCE_NONCE3]: "Transaction first instruction is not advance nonce account instruction.",
  [SOLANA_ERROR__TRANSACTION__INVALID_NONCE_TRANSACTION_INSTRUCTIONS_MISSING3]: "Transaction with no instructions cannot be durable nonce transaction.",
  [SOLANA_ERROR__TRANSACTION__INVOKED_PROGRAMS_CANNOT_PAY_FEES3]: "This transaction includes an address (`$programAddress`) which is both invoked and set as the fee payer. Program addresses may not pay fees",
  [SOLANA_ERROR__TRANSACTION__INVOKED_PROGRAMS_MUST_NOT_BE_WRITABLE3]: "This transaction includes an address (`$programAddress`) which is both invoked and marked writable. Program addresses may not be writable",
  [SOLANA_ERROR__TRANSACTION__MESSAGE_SIGNATURES_MISMATCH3]: "The transaction message expected the transaction to have $signerAddressesLength signatures, got $signaturesLength.",
  [SOLANA_ERROR__TRANSACTION__SIGNATURES_MISSING3]: "Transaction is missing signatures for addresses: $addresses.",
  [SOLANA_ERROR__TRANSACTION__VERSION_NUMBER_OUT_OF_RANGE3]: "Transaction version must be in the range [0, 127]. `$actualVersion` given"
};
var START_INDEX3 = "i";
var TYPE3 = "t";
function getHumanReadableErrorMessage3(code, context = {}) {
  const messageFormatString = SolanaErrorMessages3[code];
  if (messageFormatString.length === 0) {
    return "";
  }
  let state;
  function commitStateUpTo(endIndex) {
    if (state[TYPE3] === 2) {
      const variableName = messageFormatString.slice(state[START_INDEX3] + 1, endIndex);
      fragments.push(
        variableName in context ? `${context[variableName]}` : `$${variableName}`
      );
    } else if (state[TYPE3] === 1) {
      fragments.push(messageFormatString.slice(state[START_INDEX3], endIndex));
    }
  }
  const fragments = [];
  messageFormatString.split("").forEach((char, ii) => {
    if (ii === 0) {
      state = {
        [START_INDEX3]: 0,
        [TYPE3]: messageFormatString[0] === "\\" ? 0 : messageFormatString[0] === "$" ? 2 : 1
        /* Text */
      };
      return;
    }
    let nextState;
    switch (state[TYPE3]) {
      case 0:
        nextState = {
          [START_INDEX3]: ii,
          [TYPE3]: 1
          /* Text */
        };
        break;
      case 1:
        if (char === "\\") {
          nextState = {
            [START_INDEX3]: ii,
            [TYPE3]: 0
            /* EscapeSequence */
          };
        } else if (char === "$") {
          nextState = {
            [START_INDEX3]: ii,
            [TYPE3]: 2
            /* Variable */
          };
        }
        break;
      case 2:
        if (char === "\\") {
          nextState = {
            [START_INDEX3]: ii,
            [TYPE3]: 0
            /* EscapeSequence */
          };
        } else if (char === "$") {
          nextState = {
            [START_INDEX3]: ii,
            [TYPE3]: 2
            /* Variable */
          };
        } else if (!char.match(/\w/)) {
          nextState = {
            [START_INDEX3]: ii,
            [TYPE3]: 1
            /* Text */
          };
        }
        break;
    }
    if (nextState) {
      if (state !== nextState) {
        commitStateUpTo(ii);
      }
      state = nextState;
    }
  });
  commitStateUpTo();
  return fragments.join("");
}
function getErrorMessage3(code, context = {}) {
  if (true) {
    return getHumanReadableErrorMessage3(code, context);
  } else {
    let decodingAdviceMessage = `Solana error #${code}; Decode this error by running \`npx @solana/errors decode -- ${code}`;
    if (Object.keys(context).length) {
      decodingAdviceMessage += ` '${encodeContextObject(context)}'`;
    }
    return `${decodingAdviceMessage}\``;
  }
}
var SolanaError3 = class extends Error {
  constructor(...[code, contextAndErrorOptions]) {
    let context;
    let errorOptions;
    if (contextAndErrorOptions) {
      const { cause, ...contextRest } = contextAndErrorOptions;
      if (cause) {
        errorOptions = { cause };
      }
      if (Object.keys(contextRest).length > 0) {
        context = contextRest;
      }
    }
    const message = getErrorMessage3(code, context);
    super(message, errorOptions);
    __publicField(this, "cause", this.cause);
    __publicField(this, "context");
    this.context = {
      __code: code,
      ...context
    };
    this.name = "SolanaError";
  }
};

// node_modules/@solana/codecs-strings/node_modules/@solana/codecs-core/dist/index.browser.mjs
function getEncodedSize3(value, encoder) {
  return "fixedSize" in encoder ? encoder.fixedSize : encoder.getSizeFromValue(value);
}
function createEncoder3(encoder) {
  return Object.freeze({
    ...encoder,
    encode: (value) => {
      const bytes = new Uint8Array(getEncodedSize3(value, encoder));
      encoder.write(value, bytes, 0);
      return bytes;
    }
  });
}
function createDecoder3(decoder) {
  return Object.freeze({
    ...decoder,
    decode: (bytes, offset = 0) => decoder.read(bytes, offset)[0]
  });
}
function isFixedSize3(codec) {
  return "fixedSize" in codec && typeof codec.fixedSize === "number";
}
function combineCodec3(encoder, decoder) {
  if (isFixedSize3(encoder) !== isFixedSize3(decoder)) {
    throw new SolanaError3(SOLANA_ERROR__CODECS__ENCODER_DECODER_SIZE_COMPATIBILITY_MISMATCH3);
  }
  if (isFixedSize3(encoder) && isFixedSize3(decoder) && encoder.fixedSize !== decoder.fixedSize) {
    throw new SolanaError3(SOLANA_ERROR__CODECS__ENCODER_DECODER_FIXED_SIZE_MISMATCH3, {
      decoderFixedSize: decoder.fixedSize,
      encoderFixedSize: encoder.fixedSize
    });
  }
  if (!isFixedSize3(encoder) && !isFixedSize3(decoder) && encoder.maxSize !== decoder.maxSize) {
    throw new SolanaError3(SOLANA_ERROR__CODECS__ENCODER_DECODER_MAX_SIZE_MISMATCH3, {
      decoderMaxSize: decoder.maxSize,
      encoderMaxSize: encoder.maxSize
    });
  }
  return {
    ...decoder,
    ...encoder,
    decode: decoder.decode,
    encode: encoder.encode,
    read: decoder.read,
    write: encoder.write
  };
}

// node_modules/@solana/codecs-strings/dist/index.browser.mjs
var removeNullCharacters = (value) => (
  // eslint-disable-next-line no-control-regex
  value.replace(/\u0000/g, "")
);
var e = globalThis.TextDecoder;
var o = globalThis.TextEncoder;
var getUtf8Encoder = () => {
  let textEncoder;
  return createEncoder3({
    getSizeFromValue: (value) => (textEncoder || (textEncoder = new o())).encode(value).length,
    write: (value, bytes, offset) => {
      const bytesToAdd = (textEncoder || (textEncoder = new o())).encode(value);
      bytes.set(bytesToAdd, offset);
      return offset + bytesToAdd.length;
    }
  });
};
var getUtf8Decoder = () => {
  let textDecoder;
  return createDecoder3({
    read(bytes, offset) {
      const value = (textDecoder || (textDecoder = new e())).decode(bytes.slice(offset));
      return [removeNullCharacters(value), bytes.length];
    }
  });
};
var getUtf8Codec = () => combineCodec3(getUtf8Encoder(), getUtf8Decoder());

// node_modules/@solana/options/node_modules/@solana/errors/dist/index.browser.mjs
var SOLANA_ERROR__BLOCK_HEIGHT_EXCEEDED4 = 1;
var SOLANA_ERROR__INVALID_NONCE4 = 2;
var SOLANA_ERROR__NONCE_ACCOUNT_NOT_FOUND4 = 3;
var SOLANA_ERROR__BLOCKHASH_STRING_LENGTH_OUT_OF_RANGE4 = 4;
var SOLANA_ERROR__INVALID_BLOCKHASH_BYTE_LENGTH4 = 5;
var SOLANA_ERROR__LAMPORTS_OUT_OF_RANGE4 = 6;
var SOLANA_ERROR__MALFORMED_BIGINT_STRING4 = 7;
var SOLANA_ERROR__MALFORMED_NUMBER_STRING4 = 8;
var SOLANA_ERROR__TIMESTAMP_OUT_OF_RANGE4 = 9;
var SOLANA_ERROR__JSON_RPC__PARSE_ERROR4 = -32700;
var SOLANA_ERROR__JSON_RPC__INTERNAL_ERROR4 = -32603;
var SOLANA_ERROR__JSON_RPC__INVALID_PARAMS4 = -32602;
var SOLANA_ERROR__JSON_RPC__METHOD_NOT_FOUND4 = -32601;
var SOLANA_ERROR__JSON_RPC__INVALID_REQUEST4 = -32600;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_MIN_CONTEXT_SLOT_NOT_REACHED4 = -32016;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_UNSUPPORTED_TRANSACTION_VERSION4 = -32015;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_STATUS_NOT_AVAILABLE_YET4 = -32014;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_SIGNATURE_LEN_MISMATCH4 = -32013;
var SOLANA_ERROR__JSON_RPC__SCAN_ERROR4 = -32012;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_HISTORY_NOT_AVAILABLE4 = -32011;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_KEY_EXCLUDED_FROM_SECONDARY_INDEX4 = -32010;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_LONG_TERM_STORAGE_SLOT_SKIPPED4 = -32009;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_NO_SNAPSHOT4 = -32008;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_SLOT_SKIPPED4 = -32007;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_PRECOMPILE_VERIFICATION_FAILURE4 = -32006;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_NODE_UNHEALTHY4 = -32005;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_NOT_AVAILABLE4 = -32004;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_SIGNATURE_VERIFICATION_FAILURE4 = -32003;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_SEND_TRANSACTION_PREFLIGHT_FAILURE4 = -32002;
var SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_CLEANED_UP4 = -32001;
var SOLANA_ERROR__ADDRESSES__INVALID_BYTE_LENGTH4 = 28e5;
var SOLANA_ERROR__ADDRESSES__STRING_LENGTH_OUT_OF_RANGE4 = 2800001;
var SOLANA_ERROR__ADDRESSES__INVALID_BASE58_ENCODED_ADDRESS4 = 2800002;
var SOLANA_ERROR__ADDRESSES__INVALID_ED25519_PUBLIC_KEY4 = 2800003;
var SOLANA_ERROR__ADDRESSES__MALFORMED_PDA4 = 2800004;
var SOLANA_ERROR__ADDRESSES__PDA_BUMP_SEED_OUT_OF_RANGE4 = 2800005;
var SOLANA_ERROR__ADDRESSES__MAX_NUMBER_OF_PDA_SEEDS_EXCEEDED4 = 2800006;
var SOLANA_ERROR__ADDRESSES__MAX_PDA_SEED_LENGTH_EXCEEDED4 = 2800007;
var SOLANA_ERROR__ADDRESSES__INVALID_SEEDS_POINT_ON_CURVE4 = 2800008;
var SOLANA_ERROR__ADDRESSES__FAILED_TO_FIND_VIABLE_PDA_BUMP_SEED4 = 2800009;
var SOLANA_ERROR__ADDRESSES__PDA_ENDS_WITH_PDA_MARKER4 = 2800010;
var SOLANA_ERROR__ACCOUNTS__ACCOUNT_NOT_FOUND4 = 323e4;
var SOLANA_ERROR__ACCOUNTS__ONE_OR_MORE_ACCOUNTS_NOT_FOUND4 = ********;
var SOLANA_ERROR__ACCOUNTS__FAILED_TO_DECODE_ACCOUNT4 = 3230002;
var SOLANA_ERROR__ACCOUNTS__EXPECTED_DECODED_ACCOUNT4 = 3230003;
var SOLANA_ERROR__ACCOUNTS__EXPECTED_ALL_ACCOUNTS_TO_BE_DECODED4 = 3230004;
var SOLANA_ERROR__SUBTLE_CRYPTO__DISALLOWED_IN_INSECURE_CONTEXT4 = 361e4;
var SOLANA_ERROR__SUBTLE_CRYPTO__DIGEST_UNIMPLEMENTED4 = 3610001;
var SOLANA_ERROR__SUBTLE_CRYPTO__ED25519_ALGORITHM_UNIMPLEMENTED4 = 3610002;
var SOLANA_ERROR__SUBTLE_CRYPTO__EXPORT_FUNCTION_UNIMPLEMENTED4 = 3610003;
var SOLANA_ERROR__SUBTLE_CRYPTO__GENERATE_FUNCTION_UNIMPLEMENTED4 = 3610004;
var SOLANA_ERROR__SUBTLE_CRYPTO__SIGN_FUNCTION_UNIMPLEMENTED4 = 3610005;
var SOLANA_ERROR__SUBTLE_CRYPTO__VERIFY_FUNCTION_UNIMPLEMENTED4 = 3610006;
var SOLANA_ERROR__SUBTLE_CRYPTO__CANNOT_EXPORT_NON_EXTRACTABLE_KEY4 = 3610007;
var SOLANA_ERROR__CRYPTO__RANDOM_VALUES_FUNCTION_UNIMPLEMENTED4 = 3611e3;
var SOLANA_ERROR__KEYS__INVALID_KEY_PAIR_BYTE_LENGTH4 = 3704e3;
var SOLANA_ERROR__KEYS__INVALID_PRIVATE_KEY_BYTE_LENGTH4 = 3704001;
var SOLANA_ERROR__KEYS__INVALID_SIGNATURE_BYTE_LENGTH4 = 3704002;
var SOLANA_ERROR__KEYS__SIGNATURE_STRING_LENGTH_OUT_OF_RANGE4 = 3704003;
var SOLANA_ERROR__KEYS__PUBLIC_KEY_MUST_MATCH_PRIVATE_KEY4 = 3704004;
var SOLANA_ERROR__INSTRUCTION__EXPECTED_TO_HAVE_ACCOUNTS4 = 4128e3;
var SOLANA_ERROR__INSTRUCTION__EXPECTED_TO_HAVE_DATA4 = 4128001;
var SOLANA_ERROR__INSTRUCTION__PROGRAM_ID_MISMATCH4 = 4128002;
var SOLANA_ERROR__INSTRUCTION_ERROR__UNKNOWN4 = 4615e3;
var SOLANA_ERROR__INSTRUCTION_ERROR__GENERIC_ERROR4 = 4615001;
var SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ARGUMENT4 = 4615002;
var SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_INSTRUCTION_DATA4 = 4615003;
var SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ACCOUNT_DATA4 = 4615004;
var SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_DATA_TOO_SMALL4 = 4615005;
var SOLANA_ERROR__INSTRUCTION_ERROR__INSUFFICIENT_FUNDS4 = 4615006;
var SOLANA_ERROR__INSTRUCTION_ERROR__INCORRECT_PROGRAM_ID4 = 4615007;
var SOLANA_ERROR__INSTRUCTION_ERROR__MISSING_REQUIRED_SIGNATURE4 = 4615008;
var SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_ALREADY_INITIALIZED4 = 4615009;
var SOLANA_ERROR__INSTRUCTION_ERROR__UNINITIALIZED_ACCOUNT4 = 4615010;
var SOLANA_ERROR__INSTRUCTION_ERROR__UNBALANCED_INSTRUCTION4 = 4615011;
var SOLANA_ERROR__INSTRUCTION_ERROR__MODIFIED_PROGRAM_ID4 = 4615012;
var SOLANA_ERROR__INSTRUCTION_ERROR__EXTERNAL_ACCOUNT_LAMPORT_SPEND4 = 4615013;
var SOLANA_ERROR__INSTRUCTION_ERROR__EXTERNAL_ACCOUNT_DATA_MODIFIED4 = 4615014;
var SOLANA_ERROR__INSTRUCTION_ERROR__READONLY_LAMPORT_CHANGE4 = 4615015;
var SOLANA_ERROR__INSTRUCTION_ERROR__READONLY_DATA_MODIFIED4 = 4615016;
var SOLANA_ERROR__INSTRUCTION_ERROR__DUPLICATE_ACCOUNT_INDEX4 = 4615017;
var SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_MODIFIED4 = 4615018;
var SOLANA_ERROR__INSTRUCTION_ERROR__RENT_EPOCH_MODIFIED4 = 4615019;
var SOLANA_ERROR__INSTRUCTION_ERROR__NOT_ENOUGH_ACCOUNT_KEYS4 = 4615020;
var SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_DATA_SIZE_CHANGED4 = 4615021;
var SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_NOT_EXECUTABLE4 = 4615022;
var SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_BORROW_FAILED4 = 4615023;
var SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_BORROW_OUTSTANDING4 = 4615024;
var SOLANA_ERROR__INSTRUCTION_ERROR__DUPLICATE_ACCOUNT_OUT_OF_SYNC4 = 4615025;
var SOLANA_ERROR__INSTRUCTION_ERROR__CUSTOM4 = 4615026;
var SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ERROR4 = 4615027;
var SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_DATA_MODIFIED4 = 4615028;
var SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_LAMPORT_CHANGE4 = 4615029;
var SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_ACCOUNT_NOT_RENT_EXEMPT4 = 4615030;
var SOLANA_ERROR__INSTRUCTION_ERROR__UNSUPPORTED_PROGRAM_ID4 = 4615031;
var SOLANA_ERROR__INSTRUCTION_ERROR__CALL_DEPTH4 = 4615032;
var SOLANA_ERROR__INSTRUCTION_ERROR__MISSING_ACCOUNT4 = 4615033;
var SOLANA_ERROR__INSTRUCTION_ERROR__REENTRANCY_NOT_ALLOWED4 = 4615034;
var SOLANA_ERROR__INSTRUCTION_ERROR__MAX_SEED_LENGTH_EXCEEDED4 = 4615035;
var SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_SEEDS4 = 4615036;
var SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_REALLOC4 = 4615037;
var SOLANA_ERROR__INSTRUCTION_ERROR__COMPUTATIONAL_BUDGET_EXCEEDED4 = 4615038;
var SOLANA_ERROR__INSTRUCTION_ERROR__PRIVILEGE_ESCALATION4 = 4615039;
var SOLANA_ERROR__INSTRUCTION_ERROR__PROGRAM_ENVIRONMENT_SETUP_FAILURE4 = 4615040;
var SOLANA_ERROR__INSTRUCTION_ERROR__PROGRAM_FAILED_TO_COMPLETE4 = 4615041;
var SOLANA_ERROR__INSTRUCTION_ERROR__PROGRAM_FAILED_TO_COMPILE4 = 4615042;
var SOLANA_ERROR__INSTRUCTION_ERROR__IMMUTABLE4 = 4615043;
var SOLANA_ERROR__INSTRUCTION_ERROR__INCORRECT_AUTHORITY4 = 4615044;
var SOLANA_ERROR__INSTRUCTION_ERROR__BORSH_IO_ERROR4 = 4615045;
var SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_NOT_RENT_EXEMPT4 = 4615046;
var SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ACCOUNT_OWNER4 = 4615047;
var SOLANA_ERROR__INSTRUCTION_ERROR__ARITHMETIC_OVERFLOW4 = 4615048;
var SOLANA_ERROR__INSTRUCTION_ERROR__UNSUPPORTED_SYSVAR4 = 4615049;
var SOLANA_ERROR__INSTRUCTION_ERROR__ILLEGAL_OWNER4 = 4615050;
var SOLANA_ERROR__INSTRUCTION_ERROR__MAX_ACCOUNTS_DATA_ALLOCATIONS_EXCEEDED4 = 4615051;
var SOLANA_ERROR__INSTRUCTION_ERROR__MAX_ACCOUNTS_EXCEEDED4 = 4615052;
var SOLANA_ERROR__INSTRUCTION_ERROR__MAX_INSTRUCTION_TRACE_LENGTH_EXCEEDED4 = 4615053;
var SOLANA_ERROR__INSTRUCTION_ERROR__BUILTIN_PROGRAMS_MUST_CONSUME_COMPUTE_UNITS4 = 4615054;
var SOLANA_ERROR__SIGNER__ADDRESS_CANNOT_HAVE_MULTIPLE_SIGNERS4 = 5508e3;
var SOLANA_ERROR__SIGNER__EXPECTED_KEY_PAIR_SIGNER4 = 5508001;
var SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_SIGNER4 = 5508002;
var SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_MODIFYING_SIGNER4 = 5508003;
var SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_PARTIAL_SIGNER4 = 5508004;
var SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_SIGNER4 = 5508005;
var SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_MODIFYING_SIGNER4 = 5508006;
var SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_PARTIAL_SIGNER4 = 5508007;
var SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_SENDING_SIGNER4 = 5508008;
var SOLANA_ERROR__SIGNER__TRANSACTION_CANNOT_HAVE_MULTIPLE_SENDING_SIGNERS4 = 5508009;
var SOLANA_ERROR__SIGNER__TRANSACTION_SENDING_SIGNER_MISSING4 = 5508010;
var SOLANA_ERROR__SIGNER__WALLET_MULTISIGN_UNIMPLEMENTED4 = 5508011;
var SOLANA_ERROR__TRANSACTION__INVOKED_PROGRAMS_CANNOT_PAY_FEES4 = 5663e3;
var SOLANA_ERROR__TRANSACTION__INVOKED_PROGRAMS_MUST_NOT_BE_WRITABLE4 = 5663001;
var SOLANA_ERROR__TRANSACTION__EXPECTED_BLOCKHASH_LIFETIME4 = 5663002;
var SOLANA_ERROR__TRANSACTION__EXPECTED_NONCE_LIFETIME4 = 5663003;
var SOLANA_ERROR__TRANSACTION__VERSION_NUMBER_OUT_OF_RANGE4 = 5663004;
var SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_ADDRESS_LOOKUP_TABLE_CONTENTS_MISSING4 = 5663005;
var SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_ADDRESS_LOOKUP_TABLE_INDEX_OUT_OF_RANGE4 = 5663006;
var SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_INSTRUCTION_PROGRAM_ADDRESS_NOT_FOUND4 = 5663007;
var SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_FEE_PAYER_MISSING4 = 5663008;
var SOLANA_ERROR__TRANSACTION__SIGNATURES_MISSING4 = 5663009;
var SOLANA_ERROR__TRANSACTION__ADDRESS_MISSING4 = 5663010;
var SOLANA_ERROR__TRANSACTION__FEE_PAYER_MISSING4 = 5663011;
var SOLANA_ERROR__TRANSACTION__FEE_PAYER_SIGNATURE_MISSING4 = 5663012;
var SOLANA_ERROR__TRANSACTION__INVALID_NONCE_TRANSACTION_INSTRUCTIONS_MISSING4 = 5663013;
var SOLANA_ERROR__TRANSACTION__INVALID_NONCE_TRANSACTION_FIRST_INSTRUCTION_MUST_BE_ADVANCE_NONCE4 = 5663014;
var SOLANA_ERROR__TRANSACTION__ADDRESSES_CANNOT_SIGN_TRANSACTION4 = 5663015;
var SOLANA_ERROR__TRANSACTION__CANNOT_ENCODE_WITH_EMPTY_SIGNATURES4 = 5663016;
var SOLANA_ERROR__TRANSACTION__MESSAGE_SIGNATURES_MISMATCH4 = 5663017;
var SOLANA_ERROR__TRANSACTION__FAILED_TO_ESTIMATE_COMPUTE_LIMIT4 = 5663018;
var SOLANA_ERROR__TRANSACTION_ERROR__UNKNOWN4 = 705e4;
var SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_IN_USE4 = 7050001;
var SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_LOADED_TWICE4 = 7050002;
var SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_NOT_FOUND4 = 7050003;
var SOLANA_ERROR__TRANSACTION_ERROR__PROGRAM_ACCOUNT_NOT_FOUND4 = 7050004;
var SOLANA_ERROR__TRANSACTION_ERROR__INSUFFICIENT_FUNDS_FOR_FEE4 = 7050005;
var SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ACCOUNT_FOR_FEE4 = 7050006;
var SOLANA_ERROR__TRANSACTION_ERROR__ALREADY_PROCESSED4 = 7050007;
var SOLANA_ERROR__TRANSACTION_ERROR__BLOCKHASH_NOT_FOUND4 = 7050008;
var SOLANA_ERROR__TRANSACTION_ERROR__CALL_CHAIN_TOO_DEEP4 = 7050009;
var SOLANA_ERROR__TRANSACTION_ERROR__MISSING_SIGNATURE_FOR_FEE4 = 7050010;
var SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ACCOUNT_INDEX4 = 7050011;
var SOLANA_ERROR__TRANSACTION_ERROR__SIGNATURE_FAILURE4 = 7050012;
var SOLANA_ERROR__TRANSACTION_ERROR__INVALID_PROGRAM_FOR_EXECUTION4 = 7050013;
var SOLANA_ERROR__TRANSACTION_ERROR__SANITIZE_FAILURE4 = 7050014;
var SOLANA_ERROR__TRANSACTION_ERROR__CLUSTER_MAINTENANCE4 = 7050015;
var SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_BORROW_OUTSTANDING4 = 7050016;
var SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_MAX_BLOCK_COST_LIMIT4 = 7050017;
var SOLANA_ERROR__TRANSACTION_ERROR__UNSUPPORTED_VERSION4 = 7050018;
var SOLANA_ERROR__TRANSACTION_ERROR__INVALID_WRITABLE_ACCOUNT4 = 7050019;
var SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_MAX_ACCOUNT_COST_LIMIT4 = 7050020;
var SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_ACCOUNT_DATA_BLOCK_LIMIT4 = 7050021;
var SOLANA_ERROR__TRANSACTION_ERROR__TOO_MANY_ACCOUNT_LOCKS4 = 7050022;
var SOLANA_ERROR__TRANSACTION_ERROR__ADDRESS_LOOKUP_TABLE_NOT_FOUND4 = 7050023;
var SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ADDRESS_LOOKUP_TABLE_OWNER4 = 7050024;
var SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ADDRESS_LOOKUP_TABLE_DATA4 = 7050025;
var SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ADDRESS_LOOKUP_TABLE_INDEX4 = 7050026;
var SOLANA_ERROR__TRANSACTION_ERROR__INVALID_RENT_PAYING_ACCOUNT4 = 7050027;
var SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_MAX_VOTE_COST_LIMIT4 = 7050028;
var SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_ACCOUNT_DATA_TOTAL_LIMIT4 = 7050029;
var SOLANA_ERROR__TRANSACTION_ERROR__DUPLICATE_INSTRUCTION4 = 7050030;
var SOLANA_ERROR__TRANSACTION_ERROR__INSUFFICIENT_FUNDS_FOR_RENT4 = 7050031;
var SOLANA_ERROR__TRANSACTION_ERROR__MAX_LOADED_ACCOUNTS_DATA_SIZE_EXCEEDED4 = 7050032;
var SOLANA_ERROR__TRANSACTION_ERROR__INVALID_LOADED_ACCOUNTS_DATA_SIZE_LIMIT4 = 7050033;
var SOLANA_ERROR__TRANSACTION_ERROR__RESANITIZATION_NEEDED4 = 7050034;
var SOLANA_ERROR__TRANSACTION_ERROR__PROGRAM_EXECUTION_TEMPORARILY_RESTRICTED4 = 7050035;
var SOLANA_ERROR__TRANSACTION_ERROR__UNBALANCED_TRANSACTION4 = 7050036;
var SOLANA_ERROR__CODECS__CANNOT_DECODE_EMPTY_BYTE_ARRAY4 = 8078e3;
var SOLANA_ERROR__CODECS__INVALID_BYTE_LENGTH4 = 8078001;
var SOLANA_ERROR__CODECS__EXPECTED_FIXED_LENGTH4 = 8078002;
var SOLANA_ERROR__CODECS__EXPECTED_VARIABLE_LENGTH4 = 8078003;
var SOLANA_ERROR__CODECS__ENCODER_DECODER_SIZE_COMPATIBILITY_MISMATCH4 = 8078004;
var SOLANA_ERROR__CODECS__ENCODER_DECODER_FIXED_SIZE_MISMATCH4 = 8078005;
var SOLANA_ERROR__CODECS__ENCODER_DECODER_MAX_SIZE_MISMATCH4 = 8078006;
var SOLANA_ERROR__CODECS__INVALID_NUMBER_OF_ITEMS4 = 8078007;
var SOLANA_ERROR__CODECS__ENUM_DISCRIMINATOR_OUT_OF_RANGE4 = 8078008;
var SOLANA_ERROR__CODECS__INVALID_DISCRIMINATED_UNION_VARIANT4 = 8078009;
var SOLANA_ERROR__CODECS__INVALID_ENUM_VARIANT4 = 8078010;
var SOLANA_ERROR__CODECS__NUMBER_OUT_OF_RANGE4 = 8078011;
var SOLANA_ERROR__CODECS__INVALID_STRING_FOR_BASE4 = 8078012;
var SOLANA_ERROR__CODECS__EXPECTED_POSITIVE_BYTE_LENGTH4 = 8078013;
var SOLANA_ERROR__CODECS__OFFSET_OUT_OF_RANGE4 = 8078014;
var SOLANA_ERROR__CODECS__INVALID_LITERAL_UNION_VARIANT4 = 8078015;
var SOLANA_ERROR__CODECS__LITERAL_UNION_DISCRIMINATOR_OUT_OF_RANGE4 = 8078016;
var SOLANA_ERROR__CODECS__UNION_VARIANT_OUT_OF_RANGE4 = 8078017;
var SOLANA_ERROR__CODECS__INVALID_CONSTANT4 = 8078018;
var SOLANA_ERROR__CODECS__EXPECTED_ZERO_VALUE_TO_MATCH_ITEM_FIXED_SIZE4 = 8078019;
var SOLANA_ERROR__CODECS__ENCODED_BYTES_MUST_NOT_INCLUDE_SENTINEL4 = 8078020;
var SOLANA_ERROR__CODECS__SENTINEL_MISSING_IN_DECODED_BYTES4 = 8078021;
var SOLANA_ERROR__CODECS__CANNOT_USE_LEXICAL_VALUES_AS_ENUM_DISCRIMINATORS4 = 8078022;
var SOLANA_ERROR__RPC__INTEGER_OVERFLOW4 = 81e5;
var SOLANA_ERROR__RPC__TRANSPORT_HTTP_HEADER_FORBIDDEN4 = 8100001;
var SOLANA_ERROR__RPC__TRANSPORT_HTTP_ERROR4 = 8100002;
var SOLANA_ERROR__RPC_SUBSCRIPTIONS__CANNOT_CREATE_SUBSCRIPTION_REQUEST4 = 819e4;
var SOLANA_ERROR__RPC_SUBSCRIPTIONS__EXPECTED_SERVER_SUBSCRIPTION_ID4 = 8190001;
var SOLANA_ERROR__RPC_SUBSCRIPTIONS__TRANSPORT_CLOSED_BEFORE_MESSAGE_BUFFERED4 = 8190002;
var SOLANA_ERROR__RPC_SUBSCRIPTIONS__TRANSPORT_CONNECTION_CLOSED4 = 8190003;
var SOLANA_ERROR__RPC_SUBSCRIPTIONS__TRANSPORT_FAILED_TO_CONNECT4 = 8190004;
var SOLANA_ERROR__INVARIANT_VIOLATION__WEBSOCKET_MESSAGE_ITERATOR_STATE_MISSING4 = 99e5;
var SOLANA_ERROR__INVARIANT_VIOLATION__WEBSOCKET_MESSAGE_ITERATOR_MUST_NOT_POLL_BEFORE_RESOLVING_EXISTING_MESSAGE_PROMISE4 = 9900001;
var SOLANA_ERROR__INVARIANT_VIOLATION__CACHED_ABORTABLE_ITERABLE_CACHE_ENTRY_MISSING4 = 9900002;
var SOLANA_ERROR__INVARIANT_VIOLATION__SWITCH_MUST_BE_EXHAUSTIVE4 = 9900003;
var SolanaErrorMessages4 = {
  [SOLANA_ERROR__ACCOUNTS__ACCOUNT_NOT_FOUND4]: "Account not found at address: $address",
  [SOLANA_ERROR__ACCOUNTS__EXPECTED_ALL_ACCOUNTS_TO_BE_DECODED4]: "Not all accounts were decoded. Encoded accounts found at addresses: $addresses.",
  [SOLANA_ERROR__ACCOUNTS__EXPECTED_DECODED_ACCOUNT4]: "Expected decoded account at address: $address",
  [SOLANA_ERROR__ACCOUNTS__FAILED_TO_DECODE_ACCOUNT4]: "Failed to decode account data at address: $address",
  [SOLANA_ERROR__ACCOUNTS__ONE_OR_MORE_ACCOUNTS_NOT_FOUND4]: "Accounts not found at addresses: $addresses",
  [SOLANA_ERROR__ADDRESSES__FAILED_TO_FIND_VIABLE_PDA_BUMP_SEED4]: "Unable to find a viable program address bump seed.",
  [SOLANA_ERROR__ADDRESSES__INVALID_BASE58_ENCODED_ADDRESS4]: "$putativeAddress is not a base58-encoded address.",
  [SOLANA_ERROR__ADDRESSES__INVALID_BYTE_LENGTH4]: "Expected base58 encoded address to decode to a byte array of length 32. Actual length: $actualLength.",
  [SOLANA_ERROR__ADDRESSES__INVALID_ED25519_PUBLIC_KEY4]: "The `CryptoKey` must be an `Ed25519` public key.",
  [SOLANA_ERROR__ADDRESSES__INVALID_SEEDS_POINT_ON_CURVE4]: "Invalid seeds; point must fall off the Ed25519 curve.",
  [SOLANA_ERROR__ADDRESSES__MALFORMED_PDA4]: "Expected given program derived address to have the following format: [Address, ProgramDerivedAddressBump].",
  [SOLANA_ERROR__ADDRESSES__MAX_NUMBER_OF_PDA_SEEDS_EXCEEDED4]: "A maximum of $maxSeeds seeds, including the bump seed, may be supplied when creating an address. Received: $actual.",
  [SOLANA_ERROR__ADDRESSES__MAX_PDA_SEED_LENGTH_EXCEEDED4]: "The seed at index $index with length $actual exceeds the maximum length of $maxSeedLength bytes.",
  [SOLANA_ERROR__ADDRESSES__PDA_BUMP_SEED_OUT_OF_RANGE4]: "Expected program derived address bump to be in the range [0, 255], got: $bump.",
  [SOLANA_ERROR__ADDRESSES__PDA_ENDS_WITH_PDA_MARKER4]: "Program address cannot end with PDA marker.",
  [SOLANA_ERROR__ADDRESSES__STRING_LENGTH_OUT_OF_RANGE4]: "Expected base58-encoded address string of length in the range [32, 44]. Actual length: $actualLength.",
  [SOLANA_ERROR__BLOCKHASH_STRING_LENGTH_OUT_OF_RANGE4]: "Expected base58-encoded blockash string of length in the range [32, 44]. Actual length: $actualLength.",
  [SOLANA_ERROR__BLOCK_HEIGHT_EXCEEDED4]: "The network has progressed past the last block for which this transaction could have been committed.",
  [SOLANA_ERROR__CODECS__CANNOT_DECODE_EMPTY_BYTE_ARRAY4]: "Codec [$codecDescription] cannot decode empty byte arrays.",
  [SOLANA_ERROR__CODECS__CANNOT_USE_LEXICAL_VALUES_AS_ENUM_DISCRIMINATORS4]: "Enum codec cannot use lexical values [$stringValues] as discriminators. Either remove all lexical values or set `useValuesAsDiscriminators` to `false`.",
  [SOLANA_ERROR__CODECS__ENCODED_BYTES_MUST_NOT_INCLUDE_SENTINEL4]: "Sentinel [$hexSentinel] must not be present in encoded bytes [$hexEncodedBytes].",
  [SOLANA_ERROR__CODECS__ENCODER_DECODER_FIXED_SIZE_MISMATCH4]: "Encoder and decoder must have the same fixed size, got [$encoderFixedSize] and [$decoderFixedSize].",
  [SOLANA_ERROR__CODECS__ENCODER_DECODER_MAX_SIZE_MISMATCH4]: "Encoder and decoder must have the same max size, got [$encoderMaxSize] and [$decoderMaxSize].",
  [SOLANA_ERROR__CODECS__ENCODER_DECODER_SIZE_COMPATIBILITY_MISMATCH4]: "Encoder and decoder must either both be fixed-size or variable-size.",
  [SOLANA_ERROR__CODECS__ENUM_DISCRIMINATOR_OUT_OF_RANGE4]: "Enum discriminator out of range. Expected a number in [$formattedValidDiscriminators], got $discriminator.",
  [SOLANA_ERROR__CODECS__EXPECTED_FIXED_LENGTH4]: "Expected a fixed-size codec, got a variable-size one.",
  [SOLANA_ERROR__CODECS__EXPECTED_POSITIVE_BYTE_LENGTH4]: "Codec [$codecDescription] expected a positive byte length, got $bytesLength.",
  [SOLANA_ERROR__CODECS__EXPECTED_VARIABLE_LENGTH4]: "Expected a variable-size codec, got a fixed-size one.",
  [SOLANA_ERROR__CODECS__EXPECTED_ZERO_VALUE_TO_MATCH_ITEM_FIXED_SIZE4]: "Codec [$codecDescription] expected zero-value [$hexZeroValue] to have the same size as the provided fixed-size item [$expectedSize bytes].",
  [SOLANA_ERROR__CODECS__INVALID_BYTE_LENGTH4]: "Codec [$codecDescription] expected $expected bytes, got $bytesLength.",
  [SOLANA_ERROR__CODECS__INVALID_CONSTANT4]: "Expected byte array constant [$hexConstant] to be present in data [$hexData] at offset [$offset].",
  [SOLANA_ERROR__CODECS__INVALID_DISCRIMINATED_UNION_VARIANT4]: "Invalid discriminated union variant. Expected one of [$variants], got $value.",
  [SOLANA_ERROR__CODECS__INVALID_ENUM_VARIANT4]: "Invalid enum variant. Expected one of [$stringValues] or a number in [$formattedNumericalValues], got $variant.",
  [SOLANA_ERROR__CODECS__INVALID_LITERAL_UNION_VARIANT4]: "Invalid literal union variant. Expected one of [$variants], got $value.",
  [SOLANA_ERROR__CODECS__INVALID_NUMBER_OF_ITEMS4]: "Expected [$codecDescription] to have $expected items, got $actual.",
  [SOLANA_ERROR__CODECS__INVALID_STRING_FOR_BASE4]: "Invalid value $value for base $base with alphabet $alphabet.",
  [SOLANA_ERROR__CODECS__LITERAL_UNION_DISCRIMINATOR_OUT_OF_RANGE4]: "Literal union discriminator out of range. Expected a number between $minRange and $maxRange, got $discriminator.",
  [SOLANA_ERROR__CODECS__NUMBER_OUT_OF_RANGE4]: "Codec [$codecDescription] expected number to be in the range [$min, $max], got $value.",
  [SOLANA_ERROR__CODECS__OFFSET_OUT_OF_RANGE4]: "Codec [$codecDescription] expected offset to be in the range [0, $bytesLength], got $offset.",
  [SOLANA_ERROR__CODECS__SENTINEL_MISSING_IN_DECODED_BYTES4]: "Expected sentinel [$hexSentinel] to be present in decoded bytes [$hexDecodedBytes].",
  [SOLANA_ERROR__CODECS__UNION_VARIANT_OUT_OF_RANGE4]: "Union variant out of range. Expected an index between $minRange and $maxRange, got $variant.",
  [SOLANA_ERROR__CRYPTO__RANDOM_VALUES_FUNCTION_UNIMPLEMENTED4]: "No random values implementation could be found.",
  [SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_ALREADY_INITIALIZED4]: "instruction requires an uninitialized account",
  [SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_BORROW_FAILED4]: "instruction tries to borrow reference for an account which is already borrowed",
  [SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_BORROW_OUTSTANDING4]: "instruction left account with an outstanding borrowed reference",
  [SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_DATA_SIZE_CHANGED4]: "program other than the account's owner changed the size of the account data",
  [SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_DATA_TOO_SMALL4]: "account data too small for instruction",
  [SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_NOT_EXECUTABLE4]: "instruction expected an executable account",
  [SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_NOT_RENT_EXEMPT4]: "An account does not have enough lamports to be rent-exempt",
  [SOLANA_ERROR__INSTRUCTION_ERROR__ARITHMETIC_OVERFLOW4]: "Program arithmetic overflowed",
  [SOLANA_ERROR__INSTRUCTION_ERROR__BORSH_IO_ERROR4]: "Failed to serialize or deserialize account data: $encodedData",
  [SOLANA_ERROR__INSTRUCTION_ERROR__BUILTIN_PROGRAMS_MUST_CONSUME_COMPUTE_UNITS4]: "Builtin programs must consume compute units",
  [SOLANA_ERROR__INSTRUCTION_ERROR__CALL_DEPTH4]: "Cross-program invocation call depth too deep",
  [SOLANA_ERROR__INSTRUCTION_ERROR__COMPUTATIONAL_BUDGET_EXCEEDED4]: "Computational budget exceeded",
  [SOLANA_ERROR__INSTRUCTION_ERROR__CUSTOM4]: "custom program error: #$code",
  [SOLANA_ERROR__INSTRUCTION_ERROR__DUPLICATE_ACCOUNT_INDEX4]: "instruction contains duplicate accounts",
  [SOLANA_ERROR__INSTRUCTION_ERROR__DUPLICATE_ACCOUNT_OUT_OF_SYNC4]: "instruction modifications of multiply-passed account differ",
  [SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_ACCOUNT_NOT_RENT_EXEMPT4]: "executable accounts must be rent exempt",
  [SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_DATA_MODIFIED4]: "instruction changed executable accounts data",
  [SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_LAMPORT_CHANGE4]: "instruction changed the balance of an executable account",
  [SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_MODIFIED4]: "instruction changed executable bit of an account",
  [SOLANA_ERROR__INSTRUCTION_ERROR__EXTERNAL_ACCOUNT_DATA_MODIFIED4]: "instruction modified data of an account it does not own",
  [SOLANA_ERROR__INSTRUCTION_ERROR__EXTERNAL_ACCOUNT_LAMPORT_SPEND4]: "instruction spent from the balance of an account it does not own",
  [SOLANA_ERROR__INSTRUCTION_ERROR__GENERIC_ERROR4]: "generic instruction error",
  [SOLANA_ERROR__INSTRUCTION_ERROR__ILLEGAL_OWNER4]: "Provided owner is not allowed",
  [SOLANA_ERROR__INSTRUCTION_ERROR__IMMUTABLE4]: "Account is immutable",
  [SOLANA_ERROR__INSTRUCTION_ERROR__INCORRECT_AUTHORITY4]: "Incorrect authority provided",
  [SOLANA_ERROR__INSTRUCTION_ERROR__INCORRECT_PROGRAM_ID4]: "incorrect program id for instruction",
  [SOLANA_ERROR__INSTRUCTION_ERROR__INSUFFICIENT_FUNDS4]: "insufficient funds for instruction",
  [SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ACCOUNT_DATA4]: "invalid account data for instruction",
  [SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ACCOUNT_OWNER4]: "Invalid account owner",
  [SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ARGUMENT4]: "invalid program argument",
  [SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ERROR4]: "program returned invalid error code",
  [SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_INSTRUCTION_DATA4]: "invalid instruction data",
  [SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_REALLOC4]: "Failed to reallocate account data",
  [SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_SEEDS4]: "Provided seeds do not result in a valid address",
  [SOLANA_ERROR__INSTRUCTION_ERROR__MAX_ACCOUNTS_DATA_ALLOCATIONS_EXCEEDED4]: "Accounts data allocations exceeded the maximum allowed per transaction",
  [SOLANA_ERROR__INSTRUCTION_ERROR__MAX_ACCOUNTS_EXCEEDED4]: "Max accounts exceeded",
  [SOLANA_ERROR__INSTRUCTION_ERROR__MAX_INSTRUCTION_TRACE_LENGTH_EXCEEDED4]: "Max instruction trace length exceeded",
  [SOLANA_ERROR__INSTRUCTION_ERROR__MAX_SEED_LENGTH_EXCEEDED4]: "Length of the seed is too long for address generation",
  [SOLANA_ERROR__INSTRUCTION_ERROR__MISSING_ACCOUNT4]: "An account required by the instruction is missing",
  [SOLANA_ERROR__INSTRUCTION_ERROR__MISSING_REQUIRED_SIGNATURE4]: "missing required signature for instruction",
  [SOLANA_ERROR__INSTRUCTION_ERROR__MODIFIED_PROGRAM_ID4]: "instruction illegally modified the program id of an account",
  [SOLANA_ERROR__INSTRUCTION_ERROR__NOT_ENOUGH_ACCOUNT_KEYS4]: "insufficient account keys for instruction",
  [SOLANA_ERROR__INSTRUCTION_ERROR__PRIVILEGE_ESCALATION4]: "Cross-program invocation with unauthorized signer or writable account",
  [SOLANA_ERROR__INSTRUCTION_ERROR__PROGRAM_ENVIRONMENT_SETUP_FAILURE4]: "Failed to create program execution environment",
  [SOLANA_ERROR__INSTRUCTION_ERROR__PROGRAM_FAILED_TO_COMPILE4]: "Program failed to compile",
  [SOLANA_ERROR__INSTRUCTION_ERROR__PROGRAM_FAILED_TO_COMPLETE4]: "Program failed to complete",
  [SOLANA_ERROR__INSTRUCTION_ERROR__READONLY_DATA_MODIFIED4]: "instruction modified data of a read-only account",
  [SOLANA_ERROR__INSTRUCTION_ERROR__READONLY_LAMPORT_CHANGE4]: "instruction changed the balance of a read-only account",
  [SOLANA_ERROR__INSTRUCTION_ERROR__REENTRANCY_NOT_ALLOWED4]: "Cross-program invocation reentrancy not allowed for this instruction",
  [SOLANA_ERROR__INSTRUCTION_ERROR__RENT_EPOCH_MODIFIED4]: "instruction modified rent epoch of an account",
  [SOLANA_ERROR__INSTRUCTION_ERROR__UNBALANCED_INSTRUCTION4]: "sum of account balances before and after instruction do not match",
  [SOLANA_ERROR__INSTRUCTION_ERROR__UNINITIALIZED_ACCOUNT4]: "instruction requires an initialized account",
  [SOLANA_ERROR__INSTRUCTION_ERROR__UNKNOWN4]: "",
  [SOLANA_ERROR__INSTRUCTION_ERROR__UNSUPPORTED_PROGRAM_ID4]: "Unsupported program id",
  [SOLANA_ERROR__INSTRUCTION_ERROR__UNSUPPORTED_SYSVAR4]: "Unsupported sysvar",
  [SOLANA_ERROR__INSTRUCTION__EXPECTED_TO_HAVE_ACCOUNTS4]: "The instruction does not have any accounts.",
  [SOLANA_ERROR__INSTRUCTION__EXPECTED_TO_HAVE_DATA4]: "The instruction does not have any data.",
  [SOLANA_ERROR__INSTRUCTION__PROGRAM_ID_MISMATCH4]: "Expected instruction to have progress address $expectedProgramAddress, got $actualProgramAddress.",
  [SOLANA_ERROR__INVALID_BLOCKHASH_BYTE_LENGTH4]: "Expected base58 encoded blockhash to decode to a byte array of length 32. Actual length: $actualLength.",
  [SOLANA_ERROR__INVALID_NONCE4]: "The nonce `$expectedNonceValue` is no longer valid. It has advanced to `$actualNonceValue`",
  [SOLANA_ERROR__INVARIANT_VIOLATION__CACHED_ABORTABLE_ITERABLE_CACHE_ENTRY_MISSING4]: "Invariant violation: Found no abortable iterable cache entry for key `$cacheKey`. It should be impossible to hit this error; please file an issue at https://sola.na/web3invariant",
  [SOLANA_ERROR__INVARIANT_VIOLATION__SWITCH_MUST_BE_EXHAUSTIVE4]: "Invariant violation: Switch statement non-exhaustive. Received unexpected value `$unexpectedValue`. It should be impossible to hit this error; please file an issue at https://sola.na/web3invariant",
  [SOLANA_ERROR__INVARIANT_VIOLATION__WEBSOCKET_MESSAGE_ITERATOR_MUST_NOT_POLL_BEFORE_RESOLVING_EXISTING_MESSAGE_PROMISE4]: "Invariant violation: WebSocket message iterator state is corrupt; iterated without first resolving existing message promise. It should be impossible to hit this error; please file an issue at https://sola.na/web3invariant",
  [SOLANA_ERROR__INVARIANT_VIOLATION__WEBSOCKET_MESSAGE_ITERATOR_STATE_MISSING4]: "Invariant violation: WebSocket message iterator is missing state storage. It should be impossible to hit this error; please file an issue at https://sola.na/web3invariant",
  [SOLANA_ERROR__JSON_RPC__INTERNAL_ERROR4]: "JSON-RPC error: Internal JSON-RPC error ($__serverMessage)",
  [SOLANA_ERROR__JSON_RPC__INVALID_PARAMS4]: "JSON-RPC error: Invalid method parameter(s) ($__serverMessage)",
  [SOLANA_ERROR__JSON_RPC__INVALID_REQUEST4]: "JSON-RPC error: The JSON sent is not a valid `Request` object ($__serverMessage)",
  [SOLANA_ERROR__JSON_RPC__METHOD_NOT_FOUND4]: "JSON-RPC error: The method does not exist / is not available ($__serverMessage)",
  [SOLANA_ERROR__JSON_RPC__PARSE_ERROR4]: "JSON-RPC error: An error occurred on the server while parsing the JSON text ($__serverMessage)",
  [SOLANA_ERROR__JSON_RPC__SCAN_ERROR4]: "$__serverMessage",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_CLEANED_UP4]: "$__serverMessage",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_NOT_AVAILABLE4]: "$__serverMessage",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_STATUS_NOT_AVAILABLE_YET4]: "$__serverMessage",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_KEY_EXCLUDED_FROM_SECONDARY_INDEX4]: "$__serverMessage",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_LONG_TERM_STORAGE_SLOT_SKIPPED4]: "$__serverMessage",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_MIN_CONTEXT_SLOT_NOT_REACHED4]: "Minimum context slot has not been reached",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_NODE_UNHEALTHY4]: "Node is unhealthy; behind by $numSlotsBehind slots",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_NO_SNAPSHOT4]: "No snapshot",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_SEND_TRANSACTION_PREFLIGHT_FAILURE4]: "Transaction simulation failed",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_SLOT_SKIPPED4]: "$__serverMessage",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_HISTORY_NOT_AVAILABLE4]: "Transaction history is not available from this node",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_PRECOMPILE_VERIFICATION_FAILURE4]: "$__serverMessage",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_SIGNATURE_LEN_MISMATCH4]: "Transaction signature length mismatch",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_SIGNATURE_VERIFICATION_FAILURE4]: "Transaction signature verification failure",
  [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_UNSUPPORTED_TRANSACTION_VERSION4]: "$__serverMessage",
  [SOLANA_ERROR__KEYS__INVALID_KEY_PAIR_BYTE_LENGTH4]: "Key pair bytes must be of length 64, got $byteLength.",
  [SOLANA_ERROR__KEYS__INVALID_PRIVATE_KEY_BYTE_LENGTH4]: "Expected private key bytes with length 32. Actual length: $actualLength.",
  [SOLANA_ERROR__KEYS__INVALID_SIGNATURE_BYTE_LENGTH4]: "Expected base58-encoded signature to decode to a byte array of length 64. Actual length: $actualLength.",
  [SOLANA_ERROR__KEYS__PUBLIC_KEY_MUST_MATCH_PRIVATE_KEY4]: "The provided private key does not match the provided public key.",
  [SOLANA_ERROR__KEYS__SIGNATURE_STRING_LENGTH_OUT_OF_RANGE4]: "Expected base58-encoded signature string of length in the range [64, 88]. Actual length: $actualLength.",
  [SOLANA_ERROR__LAMPORTS_OUT_OF_RANGE4]: "Lamports value must be in the range [0, 2e64-1]",
  [SOLANA_ERROR__MALFORMED_BIGINT_STRING4]: "`$value` cannot be parsed as a `BigInt`",
  [SOLANA_ERROR__MALFORMED_NUMBER_STRING4]: "`$value` cannot be parsed as a `Number`",
  [SOLANA_ERROR__NONCE_ACCOUNT_NOT_FOUND4]: "No nonce account could be found at address `$nonceAccountAddress`",
  [SOLANA_ERROR__RPC_SUBSCRIPTIONS__CANNOT_CREATE_SUBSCRIPTION_REQUEST4]: "Either the notification name must end in 'Notifications' or the API must supply a subscription creator function for the notification '$notificationName' to map between the notification name and the subscribe/unsubscribe method names.",
  [SOLANA_ERROR__RPC_SUBSCRIPTIONS__EXPECTED_SERVER_SUBSCRIPTION_ID4]: "Failed to obtain a subscription id from the server",
  [SOLANA_ERROR__RPC_SUBSCRIPTIONS__TRANSPORT_CLOSED_BEFORE_MESSAGE_BUFFERED4]: "WebSocket was closed before payload could be added to the send buffer",
  [SOLANA_ERROR__RPC_SUBSCRIPTIONS__TRANSPORT_CONNECTION_CLOSED4]: "WebSocket connection closed",
  [SOLANA_ERROR__RPC_SUBSCRIPTIONS__TRANSPORT_FAILED_TO_CONNECT4]: "WebSocket failed to connect",
  [SOLANA_ERROR__RPC__INTEGER_OVERFLOW4]: "The $argumentLabel argument to the `$methodName` RPC method$optionalPathLabel was `$value`. This number is unsafe for use with the Solana JSON-RPC because it exceeds `Number.MAX_SAFE_INTEGER`.",
  [SOLANA_ERROR__RPC__TRANSPORT_HTTP_ERROR4]: "HTTP error ($statusCode): $message",
  [SOLANA_ERROR__RPC__TRANSPORT_HTTP_HEADER_FORBIDDEN4]: "HTTP header(s) forbidden: $headers. Learn more at https://developer.mozilla.org/en-US/docs/Glossary/Forbidden_header_name.",
  [SOLANA_ERROR__SIGNER__ADDRESS_CANNOT_HAVE_MULTIPLE_SIGNERS4]: "Multiple distinct signers were identified for address `$address`. Please ensure that you are using the same signer instance for each address.",
  [SOLANA_ERROR__SIGNER__EXPECTED_KEY_PAIR_SIGNER4]: "The provided value does not implement the `KeyPairSigner` interface",
  [SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_MODIFYING_SIGNER4]: "The provided value does not implement the `MessageModifyingSigner` interface",
  [SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_PARTIAL_SIGNER4]: "The provided value does not implement the `MessagePartialSigner` interface",
  [SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_SIGNER4]: "The provided value does not implement any of the `MessageSigner` interfaces",
  [SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_MODIFYING_SIGNER4]: "The provided value does not implement the `TransactionModifyingSigner` interface",
  [SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_PARTIAL_SIGNER4]: "The provided value does not implement the `TransactionPartialSigner` interface",
  [SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_SENDING_SIGNER4]: "The provided value does not implement the `TransactionSendingSigner` interface",
  [SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_SIGNER4]: "The provided value does not implement any of the `TransactionSigner` interfaces",
  [SOLANA_ERROR__SIGNER__TRANSACTION_CANNOT_HAVE_MULTIPLE_SENDING_SIGNERS4]: "More than one `TransactionSendingSigner` was identified.",
  [SOLANA_ERROR__SIGNER__TRANSACTION_SENDING_SIGNER_MISSING4]: "No `TransactionSendingSigner` was identified. Please provide a valid `ITransactionWithSingleSendingSigner` transaction.",
  [SOLANA_ERROR__SIGNER__WALLET_MULTISIGN_UNIMPLEMENTED4]: "Wallet account signers do not support signing multiple messages/transactions in a single operation",
  [SOLANA_ERROR__SUBTLE_CRYPTO__CANNOT_EXPORT_NON_EXTRACTABLE_KEY4]: "Cannot export a non-extractable key.",
  [SOLANA_ERROR__SUBTLE_CRYPTO__DIGEST_UNIMPLEMENTED4]: "No digest implementation could be found.",
  [SOLANA_ERROR__SUBTLE_CRYPTO__DISALLOWED_IN_INSECURE_CONTEXT4]: "Cryptographic operations are only allowed in secure browser contexts. Read more here: https://developer.mozilla.org/en-US/docs/Web/Security/Secure_Contexts.",
  [SOLANA_ERROR__SUBTLE_CRYPTO__ED25519_ALGORITHM_UNIMPLEMENTED4]: "This runtime does not support the generation of Ed25519 key pairs.\n\nInstall @solana/webcrypto-ed25519-polyfill and call its `install` function before generating keys in environments that do not support Ed25519.\n\nFor a list of runtimes that currently support Ed25519 operations, visit https://github.com/WICG/webcrypto-secure-curves/issues/20.",
  [SOLANA_ERROR__SUBTLE_CRYPTO__EXPORT_FUNCTION_UNIMPLEMENTED4]: "No signature verification implementation could be found.",
  [SOLANA_ERROR__SUBTLE_CRYPTO__GENERATE_FUNCTION_UNIMPLEMENTED4]: "No key generation implementation could be found.",
  [SOLANA_ERROR__SUBTLE_CRYPTO__SIGN_FUNCTION_UNIMPLEMENTED4]: "No signing implementation could be found.",
  [SOLANA_ERROR__SUBTLE_CRYPTO__VERIFY_FUNCTION_UNIMPLEMENTED4]: "No key export implementation could be found.",
  [SOLANA_ERROR__TIMESTAMP_OUT_OF_RANGE4]: "Timestamp value must be in the range [-8.64e15, 8.64e15]. `$value` given",
  [SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_BORROW_OUTSTANDING4]: "Transaction processing left an account with an outstanding borrowed reference",
  [SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_IN_USE4]: "Account in use",
  [SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_LOADED_TWICE4]: "Account loaded twice",
  [SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_NOT_FOUND4]: "Attempt to debit an account but found no record of a prior credit.",
  [SOLANA_ERROR__TRANSACTION_ERROR__ADDRESS_LOOKUP_TABLE_NOT_FOUND4]: "Transaction loads an address table account that doesn't exist",
  [SOLANA_ERROR__TRANSACTION_ERROR__ALREADY_PROCESSED4]: "This transaction has already been processed",
  [SOLANA_ERROR__TRANSACTION_ERROR__BLOCKHASH_NOT_FOUND4]: "Blockhash not found",
  [SOLANA_ERROR__TRANSACTION_ERROR__CALL_CHAIN_TOO_DEEP4]: "Loader call chain is too deep",
  [SOLANA_ERROR__TRANSACTION_ERROR__CLUSTER_MAINTENANCE4]: "Transactions are currently disabled due to cluster maintenance",
  [SOLANA_ERROR__TRANSACTION_ERROR__DUPLICATE_INSTRUCTION4]: "Transaction contains a duplicate instruction ($index) that is not allowed",
  [SOLANA_ERROR__TRANSACTION_ERROR__INSUFFICIENT_FUNDS_FOR_FEE4]: "Insufficient funds for fee",
  [SOLANA_ERROR__TRANSACTION_ERROR__INSUFFICIENT_FUNDS_FOR_RENT4]: "Transaction results in an account ($accountIndex) with insufficient funds for rent",
  [SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ACCOUNT_FOR_FEE4]: "This account may not be used to pay transaction fees",
  [SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ACCOUNT_INDEX4]: "Transaction contains an invalid account reference",
  [SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ADDRESS_LOOKUP_TABLE_DATA4]: "Transaction loads an address table account with invalid data",
  [SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ADDRESS_LOOKUP_TABLE_INDEX4]: "Transaction address table lookup uses an invalid index",
  [SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ADDRESS_LOOKUP_TABLE_OWNER4]: "Transaction loads an address table account with an invalid owner",
  [SOLANA_ERROR__TRANSACTION_ERROR__INVALID_LOADED_ACCOUNTS_DATA_SIZE_LIMIT4]: "LoadedAccountsDataSizeLimit set for transaction must be greater than 0.",
  [SOLANA_ERROR__TRANSACTION_ERROR__INVALID_PROGRAM_FOR_EXECUTION4]: "This program may not be used for executing instructions",
  [SOLANA_ERROR__TRANSACTION_ERROR__INVALID_RENT_PAYING_ACCOUNT4]: "Transaction leaves an account with a lower balance than rent-exempt minimum",
  [SOLANA_ERROR__TRANSACTION_ERROR__INVALID_WRITABLE_ACCOUNT4]: "Transaction loads a writable account that cannot be written",
  [SOLANA_ERROR__TRANSACTION_ERROR__MAX_LOADED_ACCOUNTS_DATA_SIZE_EXCEEDED4]: "Transaction exceeded max loaded accounts data size cap",
  [SOLANA_ERROR__TRANSACTION_ERROR__MISSING_SIGNATURE_FOR_FEE4]: "Transaction requires a fee but has no signature present",
  [SOLANA_ERROR__TRANSACTION_ERROR__PROGRAM_ACCOUNT_NOT_FOUND4]: "Attempt to load a program that does not exist",
  [SOLANA_ERROR__TRANSACTION_ERROR__PROGRAM_EXECUTION_TEMPORARILY_RESTRICTED4]: "Execution of the program referenced by account at index $accountIndex is temporarily restricted.",
  [SOLANA_ERROR__TRANSACTION_ERROR__RESANITIZATION_NEEDED4]: "ResanitizationNeeded",
  [SOLANA_ERROR__TRANSACTION_ERROR__SANITIZE_FAILURE4]: "Transaction failed to sanitize accounts offsets correctly",
  [SOLANA_ERROR__TRANSACTION_ERROR__SIGNATURE_FAILURE4]: "Transaction did not pass signature verification",
  [SOLANA_ERROR__TRANSACTION_ERROR__TOO_MANY_ACCOUNT_LOCKS4]: "Transaction locked too many accounts",
  [SOLANA_ERROR__TRANSACTION_ERROR__UNBALANCED_TRANSACTION4]: "Sum of account balances before and after transaction do not match",
  [SOLANA_ERROR__TRANSACTION_ERROR__UNKNOWN4]: "The transaction failed with the error `$errorName`",
  [SOLANA_ERROR__TRANSACTION_ERROR__UNSUPPORTED_VERSION4]: "Transaction version is unsupported",
  [SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_ACCOUNT_DATA_BLOCK_LIMIT4]: "Transaction would exceed account data limit within the block",
  [SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_ACCOUNT_DATA_TOTAL_LIMIT4]: "Transaction would exceed total account data limit",
  [SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_MAX_ACCOUNT_COST_LIMIT4]: "Transaction would exceed max account limit within the block",
  [SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_MAX_BLOCK_COST_LIMIT4]: "Transaction would exceed max Block Cost Limit",
  [SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_MAX_VOTE_COST_LIMIT4]: "Transaction would exceed max Vote Cost Limit",
  [SOLANA_ERROR__TRANSACTION__ADDRESSES_CANNOT_SIGN_TRANSACTION4]: "Attempted to sign a transaction with an address that is not a signer for it",
  [SOLANA_ERROR__TRANSACTION__ADDRESS_MISSING4]: "Transaction is missing an address at index: $index.",
  [SOLANA_ERROR__TRANSACTION__CANNOT_ENCODE_WITH_EMPTY_SIGNATURES4]: "Transaction has no expected signers therefore it cannot be encoded",
  [SOLANA_ERROR__TRANSACTION__EXPECTED_BLOCKHASH_LIFETIME4]: "Transaction does not have a blockhash lifetime",
  [SOLANA_ERROR__TRANSACTION__EXPECTED_NONCE_LIFETIME4]: "Transaction is not a durable nonce transaction",
  [SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_ADDRESS_LOOKUP_TABLE_CONTENTS_MISSING4]: "Contents of these address lookup tables unknown: $lookupTableAddresses",
  [SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_ADDRESS_LOOKUP_TABLE_INDEX_OUT_OF_RANGE4]: "Lookup of address at index $highestRequestedIndex failed for lookup table `$lookupTableAddress`. Highest known index is $highestKnownIndex. The lookup table may have been extended since its contents were retrieved",
  [SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_FEE_PAYER_MISSING4]: "No fee payer set in CompiledTransaction",
  [SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_INSTRUCTION_PROGRAM_ADDRESS_NOT_FOUND4]: "Could not find program address at index $index",
  [SOLANA_ERROR__TRANSACTION__FAILED_TO_ESTIMATE_COMPUTE_LIMIT4]: "Failed to estimate the compute unit consumption for this transaction message. This is likely because simulating the transaction failed. Inspect the `cause` property of this error to learn more",
  [SOLANA_ERROR__TRANSACTION__FEE_PAYER_MISSING4]: "Transaction is missing a fee payer.",
  [SOLANA_ERROR__TRANSACTION__FEE_PAYER_SIGNATURE_MISSING4]: "Could not determine this transaction's signature. Make sure that the transaction has been signed by its fee payer.",
  [SOLANA_ERROR__TRANSACTION__INVALID_NONCE_TRANSACTION_FIRST_INSTRUCTION_MUST_BE_ADVANCE_NONCE4]: "Transaction first instruction is not advance nonce account instruction.",
  [SOLANA_ERROR__TRANSACTION__INVALID_NONCE_TRANSACTION_INSTRUCTIONS_MISSING4]: "Transaction with no instructions cannot be durable nonce transaction.",
  [SOLANA_ERROR__TRANSACTION__INVOKED_PROGRAMS_CANNOT_PAY_FEES4]: "This transaction includes an address (`$programAddress`) which is both invoked and set as the fee payer. Program addresses may not pay fees",
  [SOLANA_ERROR__TRANSACTION__INVOKED_PROGRAMS_MUST_NOT_BE_WRITABLE4]: "This transaction includes an address (`$programAddress`) which is both invoked and marked writable. Program addresses may not be writable",
  [SOLANA_ERROR__TRANSACTION__MESSAGE_SIGNATURES_MISMATCH4]: "The transaction message expected the transaction to have $signerAddressesLength signatures, got $signaturesLength.",
  [SOLANA_ERROR__TRANSACTION__SIGNATURES_MISSING4]: "Transaction is missing signatures for addresses: $addresses.",
  [SOLANA_ERROR__TRANSACTION__VERSION_NUMBER_OUT_OF_RANGE4]: "Transaction version must be in the range [0, 127]. `$actualVersion` given"
};
var START_INDEX4 = "i";
var TYPE4 = "t";
function getHumanReadableErrorMessage4(code, context = {}) {
  const messageFormatString = SolanaErrorMessages4[code];
  if (messageFormatString.length === 0) {
    return "";
  }
  let state;
  function commitStateUpTo(endIndex) {
    if (state[TYPE4] === 2) {
      const variableName = messageFormatString.slice(state[START_INDEX4] + 1, endIndex);
      fragments.push(
        variableName in context ? `${context[variableName]}` : `$${variableName}`
      );
    } else if (state[TYPE4] === 1) {
      fragments.push(messageFormatString.slice(state[START_INDEX4], endIndex));
    }
  }
  const fragments = [];
  messageFormatString.split("").forEach((char, ii) => {
    if (ii === 0) {
      state = {
        [START_INDEX4]: 0,
        [TYPE4]: messageFormatString[0] === "\\" ? 0 : messageFormatString[0] === "$" ? 2 : 1
        /* Text */
      };
      return;
    }
    let nextState;
    switch (state[TYPE4]) {
      case 0:
        nextState = {
          [START_INDEX4]: ii,
          [TYPE4]: 1
          /* Text */
        };
        break;
      case 1:
        if (char === "\\") {
          nextState = {
            [START_INDEX4]: ii,
            [TYPE4]: 0
            /* EscapeSequence */
          };
        } else if (char === "$") {
          nextState = {
            [START_INDEX4]: ii,
            [TYPE4]: 2
            /* Variable */
          };
        }
        break;
      case 2:
        if (char === "\\") {
          nextState = {
            [START_INDEX4]: ii,
            [TYPE4]: 0
            /* EscapeSequence */
          };
        } else if (char === "$") {
          nextState = {
            [START_INDEX4]: ii,
            [TYPE4]: 2
            /* Variable */
          };
        } else if (!char.match(/\w/)) {
          nextState = {
            [START_INDEX4]: ii,
            [TYPE4]: 1
            /* Text */
          };
        }
        break;
    }
    if (nextState) {
      if (state !== nextState) {
        commitStateUpTo(ii);
      }
      state = nextState;
    }
  });
  commitStateUpTo();
  return fragments.join("");
}
function getErrorMessage4(code, context = {}) {
  if (true) {
    return getHumanReadableErrorMessage4(code, context);
  } else {
    let decodingAdviceMessage = `Solana error #${code}; Decode this error by running \`npx @solana/errors decode -- ${code}`;
    if (Object.keys(context).length) {
      decodingAdviceMessage += ` '${encodeContextObject(context)}'`;
    }
    return `${decodingAdviceMessage}\``;
  }
}
var SolanaError4 = class extends Error {
  constructor(...[code, contextAndErrorOptions]) {
    let context;
    let errorOptions;
    if (contextAndErrorOptions) {
      const { cause, ...contextRest } = contextAndErrorOptions;
      if (cause) {
        errorOptions = { cause };
      }
      if (Object.keys(contextRest).length > 0) {
        context = contextRest;
      }
    }
    const message = getErrorMessage4(code, context);
    super(message, errorOptions);
    __publicField(this, "cause", this.cause);
    __publicField(this, "context");
    this.context = {
      __code: code,
      ...context
    };
    this.name = "SolanaError";
  }
};

// node_modules/@solana/options/node_modules/@solana/codecs-core/dist/index.browser.mjs
function getEncodedSize4(value, encoder) {
  return "fixedSize" in encoder ? encoder.fixedSize : encoder.getSizeFromValue(value);
}
function createEncoder4(encoder) {
  return Object.freeze({
    ...encoder,
    encode: (value) => {
      const bytes = new Uint8Array(getEncodedSize4(value, encoder));
      encoder.write(value, bytes, 0);
      return bytes;
    }
  });
}
function isFixedSize4(codec) {
  return "fixedSize" in codec && typeof codec.fixedSize === "number";
}
function assertIsFixedSize2(codec) {
  if (!isFixedSize4(codec)) {
    throw new SolanaError4(SOLANA_ERROR__CODECS__EXPECTED_FIXED_LENGTH4);
  }
}
function isVariableSize3(codec) {
  return !isFixedSize4(codec);
}
function fixEncoderSize3(encoder, fixedBytes) {
  return createEncoder4({
    fixedSize: fixedBytes,
    write: (value, bytes, offset) => {
      const variableByteArray = encoder.encode(value);
      const fixedByteArray = variableByteArray.length > fixedBytes ? variableByteArray.slice(0, fixedBytes) : variableByteArray;
      bytes.set(fixedByteArray, offset);
      return offset + fixedBytes;
    }
  });
}
function transformEncoder3(encoder, unmap) {
  return createEncoder4({
    ...isVariableSize3(encoder) ? { ...encoder, getSizeFromValue: (value) => encoder.getSizeFromValue(unmap(value)) } : encoder,
    write: (value, bytes, offset) => encoder.write(unmap(value), bytes, offset)
  });
}

// node_modules/@solana/options/node_modules/@solana/codecs-numbers/dist/index.browser.mjs
function assertNumberIsBetweenForCodec3(codecDescription, min, max, value) {
  if (value < min || value > max) {
    throw new SolanaError4(SOLANA_ERROR__CODECS__NUMBER_OUT_OF_RANGE4, {
      codecDescription,
      max,
      min,
      value
    });
  }
}
var Endian3 = ((Endian22) => {
  Endian22[Endian22["Little"] = 0] = "Little";
  Endian22[Endian22["Big"] = 1] = "Big";
  return Endian22;
})(Endian3 || {});
function isLittleEndian3(config) {
  return (config == null ? void 0 : config.endian) === 1 ? false : true;
}
function numberEncoderFactory3(input) {
  return createEncoder4({
    fixedSize: input.size,
    write(value, bytes, offset) {
      if (input.range) {
        assertNumberIsBetweenForCodec3(input.name, input.range[0], input.range[1], value);
      }
      const arrayBuffer = new ArrayBuffer(input.size);
      input.set(new DataView(arrayBuffer), value, isLittleEndian3(input.config));
      bytes.set(new Uint8Array(arrayBuffer), offset);
      return offset + input.size;
    }
  });
}
var getU8Encoder2 = () => numberEncoderFactory3({
  name: "u8",
  range: [0, Number("0xff")],
  set: (view, value) => view.setUint8(0, Number(value)),
  size: 1
});

// node_modules/@solana/options/dist/index.browser.mjs
var some = (value) => ({ __option: "Some", value });
var none = () => ({ __option: "None" });
var isOption = (input) => !!(input && typeof input === "object" && "__option" in input && (input.__option === "Some" && "value" in input || input.__option === "None"));
var isSome = (option) => option.__option === "Some";
var wrapNullable = (nullable) => nullable !== null ? some(nullable) : none();
function getOptionEncoder(item, config = {}) {
  const prefix = (() => {
    if (config.prefix === null) {
      return transformEncoder3(getUnitEncoder(), (_boolean) => void 0);
    }
    return getBooleanEncoder({ size: config.prefix ?? getU8Encoder2() });
  })();
  const noneValue = (() => {
    if (config.noneValue === "zeroes") {
      assertIsFixedSize2(item);
      return fixEncoderSize3(getUnitEncoder(), item.fixedSize);
    }
    if (!config.noneValue) {
      return getUnitEncoder();
    }
    return getConstantEncoder(config.noneValue);
  })();
  return getUnionEncoder(
    [
      transformEncoder3(getTupleEncoder([prefix, noneValue]), (_value) => [
        false,
        void 0
      ]),
      transformEncoder3(getTupleEncoder([prefix, item]), (value) => [
        true,
        isOption(value) && isSome(value) ? value.value : value
      ])
    ],
    (variant) => {
      const option = isOption(variant) ? variant : wrapNullable(variant);
      return Number(isSome(option));
    }
  );
}

// node_modules/@solana/spl-token-group/lib/esm/instruction.js
init_index_browser_esm();
function getInstructionEncoder(discriminator, dataEncoder) {
  return transformEncoder(getTupleEncoder([getBytesEncoder(), dataEncoder]), (data) => [
    discriminator,
    data
  ]);
}
function getPublicKeyEncoder() {
  return transformEncoder(fixEncoderSize(getBytesEncoder(), 32), (publicKey2) => publicKey2.toBytes());
}
function createInitializeGroupInstruction(args) {
  const { programId, group, mint, mintAuthority, updateAuthority, maxSize } = args;
  return new TransactionInstruction({
    programId,
    keys: [
      { isSigner: false, isWritable: true, pubkey: group },
      { isSigner: false, isWritable: false, pubkey: mint },
      { isSigner: true, isWritable: false, pubkey: mintAuthority }
    ],
    data: Buffer.from(getInstructionEncoder(new Uint8Array([
      /* await splDiscriminate('spl_token_group_interface:initialize_token_group') */
      121,
      113,
      108,
      39,
      54,
      51,
      0,
      4
    ]), getStructEncoder([
      ["updateAuthority", getPublicKeyEncoder()],
      ["maxSize", getU64Encoder()]
    ])).encode({ updateAuthority: updateAuthority ?? SystemProgram.programId, maxSize }))
  });
}
function createUpdateGroupMaxSizeInstruction(args) {
  const { programId, group, updateAuthority, maxSize } = args;
  return new TransactionInstruction({
    programId,
    keys: [
      { isSigner: false, isWritable: true, pubkey: group },
      { isSigner: true, isWritable: false, pubkey: updateAuthority }
    ],
    data: Buffer.from(getInstructionEncoder(new Uint8Array([
      /* await splDiscriminate('spl_token_group_interface:update_group_max_size') */
      108,
      37,
      171,
      143,
      248,
      30,
      18,
      110
    ]), getStructEncoder([["maxSize", getU64Encoder()]])).encode({ maxSize }))
  });
}
function createUpdateGroupAuthorityInstruction(args) {
  const { programId, group, currentAuthority, newAuthority } = args;
  return new TransactionInstruction({
    programId,
    keys: [
      { isSigner: false, isWritable: true, pubkey: group },
      { isSigner: true, isWritable: false, pubkey: currentAuthority }
    ],
    data: Buffer.from(getInstructionEncoder(new Uint8Array([
      /* await splDiscriminate('spl_token_group_interface:update_authority') */
      161,
      105,
      88,
      1,
      237,
      221,
      216,
      203
    ]), getStructEncoder([["newAuthority", getPublicKeyEncoder()]])).encode({ newAuthority: newAuthority ?? SystemProgram.programId }))
  });
}
function createInitializeMemberInstruction(args) {
  const { programId, member, memberMint, memberMintAuthority, group, groupUpdateAuthority } = args;
  return new TransactionInstruction({
    programId,
    keys: [
      { isSigner: false, isWritable: true, pubkey: member },
      { isSigner: false, isWritable: false, pubkey: memberMint },
      { isSigner: true, isWritable: false, pubkey: memberMintAuthority },
      { isSigner: false, isWritable: true, pubkey: group },
      { isSigner: true, isWritable: false, pubkey: groupUpdateAuthority }
    ],
    data: Buffer.from(getInstructionEncoder(new Uint8Array([
      /* await splDiscriminate('spl_token_group_interface:initialize_member') */
      152,
      32,
      222,
      176,
      223,
      237,
      116,
      134
    ]), getStructEncoder([])).encode({}))
  });
}

// node_modules/@solana/spl-token-group/lib/esm/state/tokenGroup.js
init_index_browser_esm();
var tokenGroupCodec = getStructCodec([
  ["updateAuthority", fixCodecSize(getBytesCodec(), 32)],
  ["mint", fixCodecSize(getBytesCodec(), 32)],
  ["size", getU64Codec()],
  ["maxSize", getU64Codec()]
]);
var TOKEN_GROUP_SIZE = tokenGroupCodec.fixedSize;
function isNonePubkey(buffer) {
  for (let i = 0; i < buffer.length; i++) {
    if (buffer[i] !== 0) {
      return false;
    }
  }
  return true;
}
function unpackTokenGroup(buffer) {
  const data = tokenGroupCodec.decode(buffer);
  return isNonePubkey(data.updateAuthority) ? {
    mint: new PublicKey(data.mint),
    size: data.size,
    maxSize: data.maxSize
  } : {
    updateAuthority: new PublicKey(data.updateAuthority),
    mint: new PublicKey(data.mint),
    size: data.size,
    maxSize: data.maxSize
  };
}

// node_modules/@solana/spl-token-group/lib/esm/state/tokenGroupMember.js
init_index_browser_esm();
var tokenGroupMemberCodec = getStructCodec([
  ["mint", fixCodecSize(getBytesCodec(), 32)],
  ["group", fixCodecSize(getBytesCodec(), 32)],
  ["memberNumber", getU64Codec()]
]);
var TOKEN_GROUP_MEMBER_SIZE = tokenGroupMemberCodec.fixedSize;
function unpackTokenGroupMember(buffer) {
  const data = tokenGroupMemberCodec.decode(buffer);
  return {
    mint: new PublicKey(data.mint),
    group: new PublicKey(data.group),
    memberNumber: data.memberNumber
  };
}

// node_modules/@solana/spl-token/lib/esm/extensions/tokenGroup/actions.js
async function tokenGroupInitializeGroup(connection, payer, mint, mintAuthority, updateAuthority, maxSize, multiSigners = [], confirmOptions, programId = TOKEN_2022_PROGRAM_ID) {
  const [mintAuthorityPublicKey, signers] = getSigners(mintAuthority, multiSigners);
  const transaction = new Transaction().add(createInitializeGroupInstruction({
    programId,
    group: mint,
    mint,
    mintAuthority: mintAuthorityPublicKey,
    updateAuthority,
    maxSize
  }));
  return await sendAndConfirmTransaction(connection, transaction, [payer, ...signers], confirmOptions);
}
async function tokenGroupInitializeGroupWithRentTransfer(connection, payer, mint, mintAuthority, updateAuthority, maxSize, multiSigners = [], confirmOptions, programId = TOKEN_2022_PROGRAM_ID) {
  const [mintAuthorityPublicKey, signers] = getSigners(mintAuthority, multiSigners);
  const lamports = await connection.getMinimumBalanceForRentExemption(TOKEN_GROUP_SIZE);
  const transaction = new Transaction().add(SystemProgram.transfer({
    fromPubkey: payer.publicKey,
    toPubkey: mint,
    lamports
  }), createInitializeGroupInstruction({
    programId,
    group: mint,
    mint,
    mintAuthority: mintAuthorityPublicKey,
    updateAuthority,
    maxSize
  }));
  return await sendAndConfirmTransaction(connection, transaction, [payer, ...signers], confirmOptions);
}
async function tokenGroupUpdateGroupMaxSize(connection, payer, mint, updateAuthority, maxSize, multiSigners = [], confirmOptions, programId = TOKEN_2022_PROGRAM_ID) {
  const [updateAuthorityPublicKey, signers] = getSigners(updateAuthority, multiSigners);
  const transaction = new Transaction().add(createUpdateGroupMaxSizeInstruction({
    programId,
    group: mint,
    updateAuthority: updateAuthorityPublicKey,
    maxSize
  }));
  return await sendAndConfirmTransaction(connection, transaction, [payer, ...signers], confirmOptions);
}
async function tokenGroupUpdateGroupAuthority(connection, payer, mint, updateAuthority, newAuthority, multiSigners = [], confirmOptions, programId = TOKEN_2022_PROGRAM_ID) {
  const [updateAuthorityPublicKey, signers] = getSigners(updateAuthority, multiSigners);
  const transaction = new Transaction().add(createUpdateGroupAuthorityInstruction({
    programId,
    group: mint,
    currentAuthority: updateAuthorityPublicKey,
    newAuthority
  }));
  return await sendAndConfirmTransaction(connection, transaction, [payer, ...signers], confirmOptions);
}
async function tokenGroupMemberInitialize(connection, payer, mint, mintAuthority, group, groupUpdateAuthority, multiSigners = [], confirmOptions, programId = TOKEN_2022_PROGRAM_ID) {
  const [mintAuthorityPublicKey, signers] = getSigners(mintAuthority, multiSigners);
  const transaction = new Transaction().add(createInitializeMemberInstruction({
    programId,
    member: mint,
    memberMint: mint,
    memberMintAuthority: mintAuthorityPublicKey,
    group,
    groupUpdateAuthority
  }));
  return await sendAndConfirmTransaction(connection, transaction, [payer, ...signers], confirmOptions);
}
async function tokenGroupMemberInitializeWithRentTransfer(connection, payer, mint, mintAuthority, group, groupUpdateAuthority, multiSigners = [], confirmOptions, programId = TOKEN_2022_PROGRAM_ID) {
  const [mintAuthorityPublicKey, signers] = getSigners(mintAuthority, multiSigners);
  const lamports = await connection.getMinimumBalanceForRentExemption(TOKEN_GROUP_MEMBER_SIZE);
  const transaction = new Transaction().add(SystemProgram.transfer({
    fromPubkey: payer.publicKey,
    toPubkey: mint,
    lamports
  }), createInitializeMemberInstruction({
    programId,
    member: mint,
    memberMint: mint,
    memberMintAuthority: mintAuthorityPublicKey,
    group,
    groupUpdateAuthority
  }));
  return await sendAndConfirmTransaction(connection, transaction, [payer, ...signers], confirmOptions);
}

// node_modules/@solana/spl-token/lib/esm/extensions/tokenGroup/state.js
init_index_browser_esm();
function getTokenGroupState(mint) {
  const extensionData = getExtensionData(ExtensionType.TokenGroup, mint.tlvData);
  if (extensionData !== null) {
    const { updateAuthority, mint: mint2, size, maxSize } = unpackTokenGroup(extensionData);
    return {
      updateAuthority: (updateAuthority == null ? void 0 : updateAuthority.equals(PublicKey.default)) ? void 0 : updateAuthority,
      mint: mint2,
      size,
      maxSize
    };
  } else {
    return null;
  }
}
function getTokenGroupMemberState(mint) {
  const extensionData = getExtensionData(ExtensionType.TokenGroupMember, mint.tlvData);
  if (extensionData !== null) {
    const { mint: mint2, group, memberNumber } = unpackTokenGroupMember(extensionData);
    return {
      mint: mint2,
      group,
      memberNumber
    };
  } else {
    return null;
  }
}

// node_modules/@solana/spl-token/lib/esm/extensions/groupMemberPointer/state.js
var import_buffer_layout11 = __toESM(require_Layout(), 1);
init_index_browser_esm();
var GroupMemberPointerLayout = (0, import_buffer_layout11.struct)([
  publicKey("authority"),
  publicKey("memberAddress")
]);
var GROUP_MEMBER_POINTER_SIZE = GroupMemberPointerLayout.span;
function getGroupMemberPointerState(mint) {
  const extensionData = getExtensionData(ExtensionType.GroupMemberPointer, mint.tlvData);
  if (extensionData !== null) {
    const { authority, memberAddress } = GroupMemberPointerLayout.decode(extensionData);
    return {
      authority: authority.equals(PublicKey.default) ? null : authority,
      memberAddress: memberAddress.equals(PublicKey.default) ? null : memberAddress
    };
  } else {
    return null;
  }
}

// node_modules/@solana/spl-token/lib/esm/extensions/groupPointer/state.js
var import_buffer_layout12 = __toESM(require_Layout(), 1);
init_index_browser_esm();
var GroupPointerLayout = (0, import_buffer_layout12.struct)([
  publicKey("authority"),
  publicKey("groupAddress")
]);
var GROUP_POINTER_SIZE = GroupPointerLayout.span;
function getGroupPointerState(mint) {
  const extensionData = getExtensionData(ExtensionType.GroupPointer, mint.tlvData);
  if (extensionData !== null) {
    const { authority, groupAddress } = GroupPointerLayout.decode(extensionData);
    return {
      authority: authority.equals(PublicKey.default) ? null : authority,
      groupAddress: groupAddress.equals(PublicKey.default) ? null : groupAddress
    };
  } else {
    return null;
  }
}

// node_modules/@solana/spl-token/lib/esm/extensions/immutableOwner.js
var import_buffer_layout13 = __toESM(require_Layout(), 1);
var ImmutableOwnerLayout = (0, import_buffer_layout13.struct)([]);
var IMMUTABLE_OWNER_SIZE = ImmutableOwnerLayout.span;
function getImmutableOwner(account) {
  const extensionData = getExtensionData(ExtensionType.ImmutableOwner, account.tlvData);
  if (extensionData !== null) {
    return ImmutableOwnerLayout.decode(extensionData);
  } else {
    return null;
  }
}

// node_modules/@solana/spl-token/lib/esm/extensions/interestBearingMint/state.js
var import_buffer_layout14 = __toESM(require_Layout(), 1);
var InterestBearingMintConfigStateLayout = (0, import_buffer_layout14.struct)([
  publicKey("rateAuthority"),
  (0, import_buffer_layout14.ns64)("initializationTimestamp"),
  (0, import_buffer_layout14.s16)("preUpdateAverageRate"),
  (0, import_buffer_layout14.ns64)("lastUpdateTimestamp"),
  (0, import_buffer_layout14.s16)("currentRate")
]);
var INTEREST_BEARING_MINT_CONFIG_STATE_SIZE = InterestBearingMintConfigStateLayout.span;
function getInterestBearingMintConfigState(mint) {
  const extensionData = getExtensionData(ExtensionType.InterestBearingConfig, mint.tlvData);
  if (extensionData !== null) {
    return InterestBearingMintConfigStateLayout.decode(extensionData);
  }
  return null;
}

// node_modules/@solana/spl-token/lib/esm/extensions/memoTransfer/actions.js
init_index_browser_esm();

// node_modules/@solana/spl-token/lib/esm/extensions/memoTransfer/instructions.js
var import_buffer_layout15 = __toESM(require_Layout(), 1);
init_index_browser_esm();
var MemoTransferInstruction;
(function(MemoTransferInstruction2) {
  MemoTransferInstruction2[MemoTransferInstruction2["Enable"] = 0] = "Enable";
  MemoTransferInstruction2[MemoTransferInstruction2["Disable"] = 1] = "Disable";
})(MemoTransferInstruction || (MemoTransferInstruction = {}));
var memoTransferInstructionData = (0, import_buffer_layout15.struct)([
  (0, import_buffer_layout15.u8)("instruction"),
  (0, import_buffer_layout15.u8)("memoTransferInstruction")
]);
function createEnableRequiredMemoTransfersInstruction(account, authority, multiSigners = [], programId = TOKEN_2022_PROGRAM_ID) {
  return createMemoTransferInstruction(MemoTransferInstruction.Enable, account, authority, multiSigners, programId);
}
function createDisableRequiredMemoTransfersInstruction(account, authority, multiSigners = [], programId = TOKEN_2022_PROGRAM_ID) {
  return createMemoTransferInstruction(MemoTransferInstruction.Disable, account, authority, multiSigners, programId);
}
function createMemoTransferInstruction(memoTransferInstruction, account, authority, multiSigners, programId) {
  if (!programSupportsExtensions(programId)) {
    throw new TokenUnsupportedInstructionError();
  }
  const keys = addSigners([{ pubkey: account, isSigner: false, isWritable: true }], authority, multiSigners);
  const data = Buffer.alloc(memoTransferInstructionData.span);
  memoTransferInstructionData.encode({
    instruction: TokenInstruction.MemoTransferExtension,
    memoTransferInstruction
  }, data);
  return new TransactionInstruction({ keys, programId, data });
}

// node_modules/@solana/spl-token/lib/esm/extensions/memoTransfer/actions.js
async function enableRequiredMemoTransfers(connection, payer, account, owner, multiSigners = [], confirmOptions, programId = TOKEN_2022_PROGRAM_ID) {
  const [ownerPublicKey, signers] = getSigners(owner, multiSigners);
  const transaction = new Transaction().add(createEnableRequiredMemoTransfersInstruction(account, ownerPublicKey, signers, programId));
  return await sendAndConfirmTransaction(connection, transaction, [payer, ...signers], confirmOptions);
}
async function disableRequiredMemoTransfers(connection, payer, account, owner, multiSigners = [], confirmOptions, programId = TOKEN_2022_PROGRAM_ID) {
  const [ownerPublicKey, signers] = getSigners(owner, multiSigners);
  const transaction = new Transaction().add(createDisableRequiredMemoTransfersInstruction(account, ownerPublicKey, signers, programId));
  return await sendAndConfirmTransaction(connection, transaction, [payer, ...signers], confirmOptions);
}

// node_modules/@solana/spl-token/lib/esm/extensions/memoTransfer/state.js
var import_buffer_layout16 = __toESM(require_Layout(), 1);
var MemoTransferLayout = (0, import_buffer_layout16.struct)([bool("requireIncomingTransferMemos")]);
var MEMO_TRANSFER_SIZE = MemoTransferLayout.span;
function getMemoTransfer(account) {
  const extensionData = getExtensionData(ExtensionType.MemoTransfer, account.tlvData);
  if (extensionData !== null) {
    return MemoTransferLayout.decode(extensionData);
  } else {
    return null;
  }
}

// node_modules/@solana/spl-token/lib/esm/extensions/metadataPointer/state.js
var import_buffer_layout17 = __toESM(require_Layout(), 1);
init_index_browser_esm();
var MetadataPointerLayout = (0, import_buffer_layout17.struct)([
  publicKey("authority"),
  publicKey("metadataAddress")
]);
var METADATA_POINTER_SIZE = MetadataPointerLayout.span;
function getMetadataPointerState(mint) {
  const extensionData = getExtensionData(ExtensionType.MetadataPointer, mint.tlvData);
  if (extensionData !== null) {
    const { authority, metadataAddress } = MetadataPointerLayout.decode(extensionData);
    return {
      authority: authority.equals(PublicKey.default) ? null : authority,
      metadataAddress: metadataAddress.equals(PublicKey.default) ? null : metadataAddress
    };
  } else {
    return null;
  }
}

// node_modules/@solana/spl-token/lib/esm/extensions/mintCloseAuthority.js
var import_buffer_layout18 = __toESM(require_Layout(), 1);
var MintCloseAuthorityLayout = (0, import_buffer_layout18.struct)([publicKey("closeAuthority")]);
var MINT_CLOSE_AUTHORITY_SIZE = MintCloseAuthorityLayout.span;
function getMintCloseAuthority(mint) {
  const extensionData = getExtensionData(ExtensionType.MintCloseAuthority, mint.tlvData);
  if (extensionData !== null) {
    return MintCloseAuthorityLayout.decode(extensionData);
  } else {
    return null;
  }
}

// node_modules/@solana/spl-token/lib/esm/extensions/nonTransferable.js
var import_buffer_layout19 = __toESM(require_Layout(), 1);
var NonTransferableLayout = (0, import_buffer_layout19.struct)([]);
var NON_TRANSFERABLE_SIZE = NonTransferableLayout.span;
var NON_TRANSFERABLE_ACCOUNT_SIZE = NonTransferableLayout.span;
function getNonTransferable(mint) {
  const extensionData = getExtensionData(ExtensionType.NonTransferable, mint.tlvData);
  if (extensionData !== null) {
    return NonTransferableLayout.decode(extensionData);
  } else {
    return null;
  }
}
function getNonTransferableAccount(account) {
  const extensionData = getExtensionData(ExtensionType.NonTransferableAccount, account.tlvData);
  if (extensionData !== null) {
    return NonTransferableLayout.decode(extensionData);
  } else {
    return null;
  }
}

// node_modules/@solana/spl-token/lib/esm/extensions/pausable/actions.js
init_index_browser_esm();

// node_modules/@solana/spl-token/lib/esm/extensions/pausable/instructions.js
var import_buffer_layout20 = __toESM(require_Layout(), 1);
init_index_browser_esm();
var PausableInstruction;
(function(PausableInstruction2) {
  PausableInstruction2[PausableInstruction2["Initialize"] = 0] = "Initialize";
  PausableInstruction2[PausableInstruction2["Pause"] = 1] = "Pause";
  PausableInstruction2[PausableInstruction2["Resume"] = 2] = "Resume";
})(PausableInstruction || (PausableInstruction = {}));
var initializePausableConfigInstructionData = (0, import_buffer_layout20.struct)([
  (0, import_buffer_layout20.u8)("instruction"),
  (0, import_buffer_layout20.u8)("pausableInstruction"),
  publicKey("authority")
]);
function createInitializePausableConfigInstruction(mint, authority, programId = TOKEN_2022_PROGRAM_ID) {
  if (!programSupportsExtensions(programId)) {
    throw new TokenUnsupportedInstructionError();
  }
  const keys = [{ pubkey: mint, isSigner: false, isWritable: true }];
  const data = Buffer.alloc(initializePausableConfigInstructionData.span);
  initializePausableConfigInstructionData.encode({
    instruction: TokenInstruction.PausableExtension,
    pausableInstruction: PausableInstruction.Initialize,
    authority: authority ?? PublicKey.default
  }, data);
  return new TransactionInstruction({ keys, programId, data });
}
var pauseInstructionData = (0, import_buffer_layout20.struct)([(0, import_buffer_layout20.u8)("instruction"), (0, import_buffer_layout20.u8)("pausableInstruction")]);
function createPauseInstruction(mint, authority, multiSigners = [], programId = TOKEN_2022_PROGRAM_ID) {
  if (!programSupportsExtensions(programId)) {
    throw new TokenUnsupportedInstructionError();
  }
  const keys = addSigners([{ pubkey: mint, isSigner: false, isWritable: true }], authority, multiSigners);
  const data = Buffer.alloc(pauseInstructionData.span);
  pauseInstructionData.encode({
    instruction: TokenInstruction.PausableExtension,
    pausableInstruction: PausableInstruction.Pause
  }, data);
  return new TransactionInstruction({ keys, programId, data });
}
var resumeInstructionData = (0, import_buffer_layout20.struct)([(0, import_buffer_layout20.u8)("instruction"), (0, import_buffer_layout20.u8)("pausableInstruction")]);
function createResumeInstruction(mint, authority, multiSigners = [], programId = TOKEN_2022_PROGRAM_ID) {
  if (!programSupportsExtensions(programId)) {
    throw new TokenUnsupportedInstructionError();
  }
  const keys = addSigners([{ pubkey: mint, isSigner: false, isWritable: true }], authority, multiSigners);
  const data = Buffer.alloc(resumeInstructionData.span);
  resumeInstructionData.encode({
    instruction: TokenInstruction.PausableExtension,
    pausableInstruction: PausableInstruction.Resume
  }, data);
  return new TransactionInstruction({ keys, programId, data });
}

// node_modules/@solana/spl-token/lib/esm/extensions/pausable/actions.js
async function pause(connection, payer, mint, owner, multiSigners = [], confirmOptions, programId = TOKEN_2022_PROGRAM_ID) {
  const [ownerPublicKey, signers] = getSigners(owner, multiSigners);
  const transaction = new Transaction().add(createPauseInstruction(mint, ownerPublicKey, multiSigners, programId));
  return await sendAndConfirmTransaction(connection, transaction, [payer, ...signers], confirmOptions);
}
async function resume(connection, payer, mint, owner, multiSigners = [], confirmOptions, programId = TOKEN_2022_PROGRAM_ID) {
  const [ownerPublicKey, signers] = getSigners(owner, multiSigners);
  const transaction = new Transaction().add(createResumeInstruction(mint, ownerPublicKey, multiSigners, programId));
  return await sendAndConfirmTransaction(connection, transaction, [payer, ...signers], confirmOptions);
}

// node_modules/@solana/spl-token/lib/esm/extensions/pausable/state.js
var import_buffer_layout21 = __toESM(require_Layout(), 1);
var PausableConfigLayout = (0, import_buffer_layout21.struct)([publicKey("authority"), bool("paused")]);
var PAUSABLE_CONFIG_SIZE = PausableConfigLayout.span;
function getPausableConfig(mint) {
  const extensionData = getExtensionData(ExtensionType.PausableConfig, mint.tlvData);
  if (extensionData !== null) {
    return PausableConfigLayout.decode(extensionData);
  } else {
    return null;
  }
}
var PausableAccountLayout = (0, import_buffer_layout21.struct)([]);
var PAUSABLE_ACCOUNT_SIZE = PausableAccountLayout.span;
function getPausableAccount(account) {
  const extensionData = getExtensionData(ExtensionType.PausableAccount, account.tlvData);
  if (extensionData !== null) {
    return PausableAccountLayout.decode(extensionData);
  } else {
    return null;
  }
}

// node_modules/@solana/spl-token/lib/esm/extensions/permanentDelegate.js
var import_buffer_layout22 = __toESM(require_Layout(), 1);
var PermanentDelegateLayout = (0, import_buffer_layout22.struct)([publicKey("delegate")]);
var PERMANENT_DELEGATE_SIZE = PermanentDelegateLayout.span;
function getPermanentDelegate(mint) {
  const extensionData = getExtensionData(ExtensionType.PermanentDelegate, mint.tlvData);
  if (extensionData !== null) {
    return PermanentDelegateLayout.decode(extensionData);
  } else {
    return null;
  }
}

// node_modules/@solana/spl-token/lib/esm/extensions/scaledUiAmount/actions.js
init_index_browser_esm();

// node_modules/@solana/spl-token/lib/esm/extensions/scaledUiAmount/instructions.js
var import_buffer_layout23 = __toESM(require_Layout(), 1);
init_index_browser_esm();
var ScaledUiAmountInstruction;
(function(ScaledUiAmountInstruction2) {
  ScaledUiAmountInstruction2[ScaledUiAmountInstruction2["Initialize"] = 0] = "Initialize";
  ScaledUiAmountInstruction2[ScaledUiAmountInstruction2["UpdateMultiplier"] = 1] = "UpdateMultiplier";
})(ScaledUiAmountInstruction || (ScaledUiAmountInstruction = {}));
var initializeScaledUiAmountConfigInstructionData = (0, import_buffer_layout23.struct)([
  (0, import_buffer_layout23.u8)("instruction"),
  (0, import_buffer_layout23.u8)("scaledUiAmountInstruction"),
  publicKey("authority"),
  (0, import_buffer_layout23.f64)("multiplier")
]);
function createInitializeScaledUiAmountConfigInstruction(mint, authority, multiplier, programId = TOKEN_2022_PROGRAM_ID) {
  if (!programSupportsExtensions(programId)) {
    throw new TokenUnsupportedInstructionError();
  }
  const keys = [{ pubkey: mint, isSigner: false, isWritable: true }];
  const data = Buffer.alloc(initializeScaledUiAmountConfigInstructionData.span);
  initializeScaledUiAmountConfigInstructionData.encode({
    instruction: TokenInstruction.ScaledUiAmountExtension,
    scaledUiAmountInstruction: ScaledUiAmountInstruction.Initialize,
    authority: authority ?? PublicKey.default,
    multiplier
  }, data);
  return new TransactionInstruction({ keys, programId, data });
}
var updateMultiplierData = (0, import_buffer_layout23.struct)([
  (0, import_buffer_layout23.u8)("instruction"),
  (0, import_buffer_layout23.u8)("scaledUiAmountInstruction"),
  (0, import_buffer_layout23.f64)("multiplier"),
  u64("effectiveTimestamp")
]);
function createUpdateMultiplierDataInstruction(mint, authority, multiplier, effectiveTimestamp, multiSigners = [], programId = TOKEN_2022_PROGRAM_ID) {
  if (!programSupportsExtensions(programId)) {
    throw new TokenUnsupportedInstructionError();
  }
  const keys = addSigners([{ pubkey: mint, isSigner: false, isWritable: true }], authority, multiSigners);
  const data = Buffer.alloc(updateMultiplierData.span);
  updateMultiplierData.encode({
    instruction: TokenInstruction.ScaledUiAmountExtension,
    scaledUiAmountInstruction: ScaledUiAmountInstruction.UpdateMultiplier,
    multiplier,
    effectiveTimestamp
  }, data);
  return new TransactionInstruction({ keys, programId, data });
}

// node_modules/@solana/spl-token/lib/esm/extensions/scaledUiAmount/actions.js
async function updateMultiplier(connection, payer, mint, owner, multiplier, effectiveTimestamp, multiSigners = [], confirmOptions, programId = TOKEN_2022_PROGRAM_ID) {
  const [ownerPublicKey, signers] = getSigners(owner, multiSigners);
  const transaction = new Transaction().add(createUpdateMultiplierDataInstruction(mint, ownerPublicKey, multiplier, effectiveTimestamp, multiSigners, programId));
  return await sendAndConfirmTransaction(connection, transaction, [payer, ...signers], confirmOptions);
}

// node_modules/@solana/spl-token/lib/esm/extensions/scaledUiAmount/state.js
var import_buffer_layout24 = __toESM(require_Layout(), 1);
var ScaledUiAmountConfigLayout = (0, import_buffer_layout24.struct)([
  publicKey("authority"),
  (0, import_buffer_layout24.f64)("multiplier"),
  u64("newMultiplierEffectiveTimestamp"),
  (0, import_buffer_layout24.f64)("newMultiplier")
]);
var SCALED_UI_AMOUNT_CONFIG_SIZE = ScaledUiAmountConfigLayout.span;
function getScaledUiAmountConfig(mint) {
  const extensionData = getExtensionData(ExtensionType.ScaledUiAmountConfig, mint.tlvData);
  if (extensionData !== null) {
    return ScaledUiAmountConfigLayout.decode(extensionData);
  }
  return null;
}

// node_modules/@solana/spl-token/lib/esm/extensions/transferFee/actions.js
init_index_browser_esm();

// node_modules/@solana/spl-token/lib/esm/extensions/transferFee/instructions.js
var import_buffer_layout26 = __toESM(require_Layout(), 1);
init_index_browser_esm();

// node_modules/@solana/spl-token/lib/esm/serialization.js
var import_buffer_layout25 = __toESM(require_Layout(), 1);
var COptionPublicKeyLayout = class extends import_buffer_layout25.Layout {
  constructor(property) {
    super(-1, property);
    this.publicKeyLayout = publicKey();
  }
  decode(buffer, offset = 0) {
    const option = buffer[offset];
    if (option === 0) {
      return null;
    }
    return this.publicKeyLayout.decode(buffer, offset + 1);
  }
  encode(src, buffer, offset = 0) {
    if (src === null) {
      buffer[offset] = 0;
      return 1;
    } else {
      buffer[offset] = 1;
      this.publicKeyLayout.encode(src, buffer, offset + 1);
      return 33;
    }
  }
  getSpan(buffer, offset = 0) {
    if (buffer) {
      const option = buffer[offset];
      return option === 0 ? 1 : 1 + this.publicKeyLayout.span;
    }
    throw new RangeError("Buffer must be provided");
  }
};

// node_modules/@solana/spl-token/lib/esm/extensions/transferFee/instructions.js
var TransferFeeInstruction;
(function(TransferFeeInstruction2) {
  TransferFeeInstruction2[TransferFeeInstruction2["InitializeTransferFeeConfig"] = 0] = "InitializeTransferFeeConfig";
  TransferFeeInstruction2[TransferFeeInstruction2["TransferCheckedWithFee"] = 1] = "TransferCheckedWithFee";
  TransferFeeInstruction2[TransferFeeInstruction2["WithdrawWithheldTokensFromMint"] = 2] = "WithdrawWithheldTokensFromMint";
  TransferFeeInstruction2[TransferFeeInstruction2["WithdrawWithheldTokensFromAccounts"] = 3] = "WithdrawWithheldTokensFromAccounts";
  TransferFeeInstruction2[TransferFeeInstruction2["HarvestWithheldTokensToMint"] = 4] = "HarvestWithheldTokensToMint";
  TransferFeeInstruction2[TransferFeeInstruction2["SetTransferFee"] = 5] = "SetTransferFee";
})(TransferFeeInstruction || (TransferFeeInstruction = {}));
var initializeTransferFeeConfigInstructionData = (0, import_buffer_layout26.struct)([
  (0, import_buffer_layout26.u8)("instruction"),
  (0, import_buffer_layout26.u8)("transferFeeInstruction"),
  new COptionPublicKeyLayout("transferFeeConfigAuthority"),
  new COptionPublicKeyLayout("withdrawWithheldAuthority"),
  (0, import_buffer_layout26.u16)("transferFeeBasisPoints"),
  u64("maximumFee")
]);
function createInitializeTransferFeeConfigInstruction(mint, transferFeeConfigAuthority, withdrawWithheldAuthority, transferFeeBasisPoints, maximumFee, programId = TOKEN_2022_PROGRAM_ID) {
  if (!programSupportsExtensions(programId)) {
    throw new TokenUnsupportedInstructionError();
  }
  const keys = [{ pubkey: mint, isSigner: false, isWritable: true }];
  const data = Buffer.alloc(78);
  initializeTransferFeeConfigInstructionData.encode({
    instruction: TokenInstruction.TransferFeeExtension,
    transferFeeInstruction: TransferFeeInstruction.InitializeTransferFeeConfig,
    transferFeeConfigAuthority,
    withdrawWithheldAuthority,
    transferFeeBasisPoints,
    maximumFee
  }, data);
  return new TransactionInstruction({
    keys,
    programId,
    data: data.subarray(0, initializeTransferFeeConfigInstructionData.getSpan(data))
  });
}
function decodeInitializeTransferFeeConfigInstruction(instruction, programId) {
  if (!instruction.programId.equals(programId))
    throw new TokenInvalidInstructionProgramError();
  if (instruction.data.length !== initializeTransferFeeConfigInstructionData.getSpan(instruction.data))
    throw new TokenInvalidInstructionDataError();
  const { keys: { mint }, data } = decodeInitializeTransferFeeConfigInstructionUnchecked(instruction);
  if (data.instruction !== TokenInstruction.TransferFeeExtension || data.transferFeeInstruction !== TransferFeeInstruction.InitializeTransferFeeConfig)
    throw new TokenInvalidInstructionTypeError();
  if (!mint)
    throw new TokenInvalidInstructionKeysError();
  return {
    programId,
    keys: {
      mint
    },
    data
  };
}
function decodeInitializeTransferFeeConfigInstructionUnchecked({ programId, keys: [mint], data }) {
  const { instruction, transferFeeInstruction, transferFeeConfigAuthority, withdrawWithheldAuthority, transferFeeBasisPoints, maximumFee } = initializeTransferFeeConfigInstructionData.decode(data);
  return {
    programId,
    keys: {
      mint
    },
    data: {
      instruction,
      transferFeeInstruction,
      transferFeeConfigAuthority,
      withdrawWithheldAuthority,
      transferFeeBasisPoints,
      maximumFee
    }
  };
}
var transferCheckedWithFeeInstructionData = (0, import_buffer_layout26.struct)([
  (0, import_buffer_layout26.u8)("instruction"),
  (0, import_buffer_layout26.u8)("transferFeeInstruction"),
  u64("amount"),
  (0, import_buffer_layout26.u8)("decimals"),
  u64("fee")
]);
function createTransferCheckedWithFeeInstruction(source, mint, destination, authority, amount, decimals, fee, multiSigners = [], programId = TOKEN_2022_PROGRAM_ID) {
  if (!programSupportsExtensions(programId)) {
    throw new TokenUnsupportedInstructionError();
  }
  const data = Buffer.alloc(transferCheckedWithFeeInstructionData.span);
  transferCheckedWithFeeInstructionData.encode({
    instruction: TokenInstruction.TransferFeeExtension,
    transferFeeInstruction: TransferFeeInstruction.TransferCheckedWithFee,
    amount,
    decimals,
    fee
  }, data);
  const keys = addSigners([
    { pubkey: source, isSigner: false, isWritable: true },
    { pubkey: mint, isSigner: false, isWritable: false },
    { pubkey: destination, isSigner: false, isWritable: true }
  ], authority, multiSigners);
  return new TransactionInstruction({ keys, programId, data });
}
function decodeTransferCheckedWithFeeInstruction(instruction, programId) {
  if (!instruction.programId.equals(programId))
    throw new TokenInvalidInstructionProgramError();
  if (instruction.data.length !== transferCheckedWithFeeInstructionData.span)
    throw new TokenInvalidInstructionDataError();
  const { keys: { source, mint, destination, authority, signers }, data } = decodeTransferCheckedWithFeeInstructionUnchecked(instruction);
  if (data.instruction !== TokenInstruction.TransferFeeExtension || data.transferFeeInstruction !== TransferFeeInstruction.TransferCheckedWithFee)
    throw new TokenInvalidInstructionTypeError();
  if (!mint)
    throw new TokenInvalidInstructionKeysError();
  return {
    programId,
    keys: {
      source,
      mint,
      destination,
      authority,
      signers: signers ? signers : null
    },
    data
  };
}
function decodeTransferCheckedWithFeeInstructionUnchecked({ programId, keys: [source, mint, destination, authority, ...signers], data }) {
  const { instruction, transferFeeInstruction, amount, decimals, fee } = transferCheckedWithFeeInstructionData.decode(data);
  return {
    programId,
    keys: {
      source,
      mint,
      destination,
      authority,
      signers
    },
    data: {
      instruction,
      transferFeeInstruction,
      amount,
      decimals,
      fee
    }
  };
}
var withdrawWithheldTokensFromMintInstructionData = (0, import_buffer_layout26.struct)([
  (0, import_buffer_layout26.u8)("instruction"),
  (0, import_buffer_layout26.u8)("transferFeeInstruction")
]);
function createWithdrawWithheldTokensFromMintInstruction(mint, destination, authority, signers = [], programId = TOKEN_2022_PROGRAM_ID) {
  if (!programSupportsExtensions(programId)) {
    throw new TokenUnsupportedInstructionError();
  }
  const data = Buffer.alloc(withdrawWithheldTokensFromMintInstructionData.span);
  withdrawWithheldTokensFromMintInstructionData.encode({
    instruction: TokenInstruction.TransferFeeExtension,
    transferFeeInstruction: TransferFeeInstruction.WithdrawWithheldTokensFromMint
  }, data);
  const keys = addSigners([
    { pubkey: mint, isSigner: false, isWritable: true },
    { pubkey: destination, isSigner: false, isWritable: true }
  ], authority, signers);
  return new TransactionInstruction({ keys, programId, data });
}
function decodeWithdrawWithheldTokensFromMintInstruction(instruction, programId) {
  if (!instruction.programId.equals(programId))
    throw new TokenInvalidInstructionProgramError();
  if (instruction.data.length !== withdrawWithheldTokensFromMintInstructionData.span)
    throw new TokenInvalidInstructionDataError();
  const { keys: { mint, destination, authority, signers }, data } = decodeWithdrawWithheldTokensFromMintInstructionUnchecked(instruction);
  if (data.instruction !== TokenInstruction.TransferFeeExtension || data.transferFeeInstruction !== TransferFeeInstruction.WithdrawWithheldTokensFromMint)
    throw new TokenInvalidInstructionTypeError();
  if (!mint)
    throw new TokenInvalidInstructionKeysError();
  return {
    programId,
    keys: {
      mint,
      destination,
      authority,
      signers: signers ? signers : null
    },
    data
  };
}
function decodeWithdrawWithheldTokensFromMintInstructionUnchecked({ programId, keys: [mint, destination, authority, ...signers], data }) {
  const { instruction, transferFeeInstruction } = withdrawWithheldTokensFromMintInstructionData.decode(data);
  return {
    programId,
    keys: {
      mint,
      destination,
      authority,
      signers
    },
    data: {
      instruction,
      transferFeeInstruction
    }
  };
}
var withdrawWithheldTokensFromAccountsInstructionData = (0, import_buffer_layout26.struct)([
  (0, import_buffer_layout26.u8)("instruction"),
  (0, import_buffer_layout26.u8)("transferFeeInstruction"),
  (0, import_buffer_layout26.u8)("numTokenAccounts")
]);
function createWithdrawWithheldTokensFromAccountsInstruction(mint, destination, authority, signers, sources, programId = TOKEN_2022_PROGRAM_ID) {
  if (!programSupportsExtensions(programId)) {
    throw new TokenUnsupportedInstructionError();
  }
  const data = Buffer.alloc(withdrawWithheldTokensFromAccountsInstructionData.span);
  withdrawWithheldTokensFromAccountsInstructionData.encode({
    instruction: TokenInstruction.TransferFeeExtension,
    transferFeeInstruction: TransferFeeInstruction.WithdrawWithheldTokensFromAccounts,
    numTokenAccounts: sources.length
  }, data);
  const keys = addSigners([
    { pubkey: mint, isSigner: false, isWritable: true },
    { pubkey: destination, isSigner: false, isWritable: true }
  ], authority, signers);
  for (const source of sources) {
    keys.push({ pubkey: source, isSigner: false, isWritable: true });
  }
  return new TransactionInstruction({ keys, programId, data });
}
function decodeWithdrawWithheldTokensFromAccountsInstruction(instruction, programId) {
  if (!instruction.programId.equals(programId))
    throw new TokenInvalidInstructionProgramError();
  if (instruction.data.length !== withdrawWithheldTokensFromAccountsInstructionData.span)
    throw new TokenInvalidInstructionDataError();
  const { keys: { mint, destination, authority, signers, sources }, data } = decodeWithdrawWithheldTokensFromAccountsInstructionUnchecked(instruction);
  if (data.instruction !== TokenInstruction.TransferFeeExtension || data.transferFeeInstruction !== TransferFeeInstruction.WithdrawWithheldTokensFromAccounts)
    throw new TokenInvalidInstructionTypeError();
  if (!mint)
    throw new TokenInvalidInstructionKeysError();
  return {
    programId,
    keys: {
      mint,
      destination,
      authority,
      signers: signers ? signers : null,
      sources: sources ? sources : null
    },
    data
  };
}
function decodeWithdrawWithheldTokensFromAccountsInstructionUnchecked({ programId, keys, data }) {
  const { instruction, transferFeeInstruction, numTokenAccounts } = withdrawWithheldTokensFromAccountsInstructionData.decode(data);
  const [mint, destination, authority, signers, sources] = [
    keys[0],
    keys[1],
    keys[2],
    keys.slice(3, 3 + numTokenAccounts),
    keys.slice(-1 * numTokenAccounts)
  ];
  return {
    programId,
    keys: {
      mint,
      destination,
      authority,
      signers,
      sources
    },
    data: {
      instruction,
      transferFeeInstruction,
      numTokenAccounts
    }
  };
}
var harvestWithheldTokensToMintInstructionData = (0, import_buffer_layout26.struct)([
  (0, import_buffer_layout26.u8)("instruction"),
  (0, import_buffer_layout26.u8)("transferFeeInstruction")
]);
function createHarvestWithheldTokensToMintInstruction(mint, sources, programId = TOKEN_2022_PROGRAM_ID) {
  if (!programSupportsExtensions(programId)) {
    throw new TokenUnsupportedInstructionError();
  }
  const data = Buffer.alloc(harvestWithheldTokensToMintInstructionData.span);
  harvestWithheldTokensToMintInstructionData.encode({
    instruction: TokenInstruction.TransferFeeExtension,
    transferFeeInstruction: TransferFeeInstruction.HarvestWithheldTokensToMint
  }, data);
  const keys = [];
  keys.push({ pubkey: mint, isSigner: false, isWritable: true });
  for (const source of sources) {
    keys.push({ pubkey: source, isSigner: false, isWritable: true });
  }
  return new TransactionInstruction({ keys, programId, data });
}
function decodeHarvestWithheldTokensToMintInstruction(instruction, programId) {
  if (!instruction.programId.equals(programId))
    throw new TokenInvalidInstructionProgramError();
  if (instruction.data.length !== harvestWithheldTokensToMintInstructionData.span)
    throw new TokenInvalidInstructionDataError();
  const { keys: { mint, sources }, data } = decodeHarvestWithheldTokensToMintInstructionUnchecked(instruction);
  if (data.instruction !== TokenInstruction.TransferFeeExtension || data.transferFeeInstruction !== TransferFeeInstruction.HarvestWithheldTokensToMint)
    throw new TokenInvalidInstructionTypeError();
  if (!mint)
    throw new TokenInvalidInstructionKeysError();
  return {
    programId,
    keys: {
      mint,
      sources
    },
    data
  };
}
function decodeHarvestWithheldTokensToMintInstructionUnchecked({ programId, keys: [mint, ...sources], data }) {
  const { instruction, transferFeeInstruction } = harvestWithheldTokensToMintInstructionData.decode(data);
  return {
    programId,
    keys: {
      mint,
      sources
    },
    data: {
      instruction,
      transferFeeInstruction
    }
  };
}
var setTransferFeeInstructionData = (0, import_buffer_layout26.struct)([
  (0, import_buffer_layout26.u8)("instruction"),
  (0, import_buffer_layout26.u8)("transferFeeInstruction"),
  (0, import_buffer_layout26.u16)("transferFeeBasisPoints"),
  u64("maximumFee")
]);
function createSetTransferFeeInstruction(mint, authority, signers, transferFeeBasisPoints, maximumFee, programId = TOKEN_2022_PROGRAM_ID) {
  if (!programSupportsExtensions(programId)) {
    throw new TokenUnsupportedInstructionError();
  }
  const data = Buffer.alloc(setTransferFeeInstructionData.span);
  setTransferFeeInstructionData.encode({
    instruction: TokenInstruction.TransferFeeExtension,
    transferFeeInstruction: TransferFeeInstruction.SetTransferFee,
    transferFeeBasisPoints,
    maximumFee
  }, data);
  const keys = addSigners([{ pubkey: mint, isSigner: false, isWritable: true }], authority, signers);
  return new TransactionInstruction({ keys, programId, data });
}
function decodeSetTransferFeeInstruction(instruction, programId) {
  if (!instruction.programId.equals(programId))
    throw new TokenInvalidInstructionProgramError();
  if (instruction.data.length !== setTransferFeeInstructionData.span)
    throw new TokenInvalidInstructionDataError();
  const { keys: { mint, authority, signers }, data } = decodeSetTransferFeeInstructionUnchecked(instruction);
  if (data.instruction !== TokenInstruction.TransferFeeExtension || data.transferFeeInstruction !== TransferFeeInstruction.SetTransferFee)
    throw new TokenInvalidInstructionTypeError();
  if (!mint)
    throw new TokenInvalidInstructionKeysError();
  return {
    programId,
    keys: {
      mint,
      authority,
      signers: signers ? signers : null
    },
    data
  };
}
function decodeSetTransferFeeInstructionUnchecked({ programId, keys: [mint, authority, ...signers], data }) {
  const { instruction, transferFeeInstruction, transferFeeBasisPoints, maximumFee } = setTransferFeeInstructionData.decode(data);
  return {
    programId,
    keys: {
      mint,
      authority,
      signers
    },
    data: {
      instruction,
      transferFeeInstruction,
      transferFeeBasisPoints,
      maximumFee
    }
  };
}

// node_modules/@solana/spl-token/lib/esm/extensions/transferFee/actions.js
async function transferCheckedWithFee(connection, payer, source, mint, destination, owner, amount, decimals, fee, multiSigners = [], confirmOptions, programId = TOKEN_2022_PROGRAM_ID) {
  const [ownerPublicKey, signers] = getSigners(owner, multiSigners);
  const transaction = new Transaction().add(createTransferCheckedWithFeeInstruction(source, mint, destination, ownerPublicKey, amount, decimals, fee, multiSigners, programId));
  return await sendAndConfirmTransaction(connection, transaction, [payer, ...signers], confirmOptions);
}
async function withdrawWithheldTokensFromMint(connection, payer, mint, destination, authority, multiSigners = [], confirmOptions, programId = TOKEN_2022_PROGRAM_ID) {
  const [authorityPublicKey, signers] = getSigners(authority, multiSigners);
  const transaction = new Transaction().add(createWithdrawWithheldTokensFromMintInstruction(mint, destination, authorityPublicKey, signers, programId));
  return await sendAndConfirmTransaction(connection, transaction, [payer, ...signers], confirmOptions);
}
async function withdrawWithheldTokensFromAccounts(connection, payer, mint, destination, authority, multiSigners, sources, confirmOptions, programId = TOKEN_2022_PROGRAM_ID) {
  const [authorityPublicKey, signers] = getSigners(authority, multiSigners);
  const transaction = new Transaction().add(createWithdrawWithheldTokensFromAccountsInstruction(mint, destination, authorityPublicKey, signers, sources, programId));
  return await sendAndConfirmTransaction(connection, transaction, [payer, ...signers], confirmOptions);
}
async function harvestWithheldTokensToMint(connection, payer, mint, sources, confirmOptions, programId = TOKEN_2022_PROGRAM_ID) {
  const transaction = new Transaction().add(createHarvestWithheldTokensToMintInstruction(mint, sources, programId));
  return await sendAndConfirmTransaction(connection, transaction, [payer], confirmOptions);
}
async function setTransferFee(connection, payer, mint, authority, multiSigners, transferFeeBasisPoints, maximumFee, confirmOptions, programId = TOKEN_2022_PROGRAM_ID) {
  const [authorityPublicKey, signers] = getSigners(authority, multiSigners);
  const transaction = new Transaction().add(createSetTransferFeeInstruction(mint, authorityPublicKey, signers, transferFeeBasisPoints, maximumFee, programId));
  return await sendAndConfirmTransaction(connection, transaction, [payer, ...signers], confirmOptions);
}

// node_modules/@solana/spl-token/lib/esm/extensions/transferFee/state.js
var import_buffer_layout27 = __toESM(require_Layout(), 1);
var MAX_FEE_BASIS_POINTS = 1e4;
var ONE_IN_BASIS_POINTS = BigInt(MAX_FEE_BASIS_POINTS);
function transferFeeLayout(property) {
  return (0, import_buffer_layout27.struct)([u64("epoch"), u64("maximumFee"), (0, import_buffer_layout27.u16)("transferFeeBasisPoints")], property);
}
function calculateFee(transferFee, preFeeAmount) {
  const transferFeeBasisPoints = transferFee.transferFeeBasisPoints;
  if (transferFeeBasisPoints === 0 || preFeeAmount === BigInt(0)) {
    return BigInt(0);
  } else {
    const numerator = preFeeAmount * BigInt(transferFeeBasisPoints);
    const rawFee = (numerator + ONE_IN_BASIS_POINTS - BigInt(1)) / ONE_IN_BASIS_POINTS;
    const fee = rawFee > transferFee.maximumFee ? transferFee.maximumFee : rawFee;
    return BigInt(fee);
  }
}
var TransferFeeConfigLayout = (0, import_buffer_layout27.struct)([
  publicKey("transferFeeConfigAuthority"),
  publicKey("withdrawWithheldAuthority"),
  u64("withheldAmount"),
  transferFeeLayout("olderTransferFee"),
  transferFeeLayout("newerTransferFee")
]);
var TRANSFER_FEE_CONFIG_SIZE = TransferFeeConfigLayout.span;
function getEpochFee(transferFeeConfig, epoch) {
  if (epoch >= transferFeeConfig.newerTransferFee.epoch) {
    return transferFeeConfig.newerTransferFee;
  } else {
    return transferFeeConfig.olderTransferFee;
  }
}
function calculateEpochFee(transferFeeConfig, epoch, preFeeAmount) {
  const transferFee = getEpochFee(transferFeeConfig, epoch);
  return calculateFee(transferFee, preFeeAmount);
}
var TransferFeeAmountLayout = (0, import_buffer_layout27.struct)([u64("withheldAmount")]);
var TRANSFER_FEE_AMOUNT_SIZE = TransferFeeAmountLayout.span;
function getTransferFeeConfig(mint) {
  const extensionData = getExtensionData(ExtensionType.TransferFeeConfig, mint.tlvData);
  if (extensionData !== null) {
    return TransferFeeConfigLayout.decode(extensionData);
  } else {
    return null;
  }
}
function getTransferFeeAmount(account) {
  const extensionData = getExtensionData(ExtensionType.TransferFeeAmount, account.tlvData);
  if (extensionData !== null) {
    return TransferFeeAmountLayout.decode(extensionData);
  } else {
    return null;
  }
}

// node_modules/@solana/spl-token/lib/esm/extensions/transferHook/actions.js
init_index_browser_esm();

// node_modules/@solana/spl-token/lib/esm/extensions/transferHook/instructions.js
var import_buffer_layout30 = __toESM(require_Layout(), 1);
init_index_browser_esm();

// node_modules/@solana/spl-token/lib/esm/instructions/transferChecked.js
var import_buffer_layout28 = __toESM(require_Layout(), 1);
init_index_browser_esm();
var transferCheckedInstructionData = (0, import_buffer_layout28.struct)([
  (0, import_buffer_layout28.u8)("instruction"),
  u64("amount"),
  (0, import_buffer_layout28.u8)("decimals")
]);
function createTransferCheckedInstruction(source, mint, destination, owner, amount, decimals, multiSigners = [], programId = TOKEN_PROGRAM_ID) {
  const keys = addSigners([
    { pubkey: source, isSigner: false, isWritable: true },
    { pubkey: mint, isSigner: false, isWritable: false },
    { pubkey: destination, isSigner: false, isWritable: true }
  ], owner, multiSigners);
  const data = Buffer.alloc(transferCheckedInstructionData.span);
  transferCheckedInstructionData.encode({
    instruction: TokenInstruction.TransferChecked,
    amount: BigInt(amount),
    decimals
  }, data);
  return new TransactionInstruction({ keys, programId, data });
}
function decodeTransferCheckedInstruction(instruction, programId = TOKEN_PROGRAM_ID) {
  if (!instruction.programId.equals(programId))
    throw new TokenInvalidInstructionProgramError();
  if (instruction.data.length !== transferCheckedInstructionData.span)
    throw new TokenInvalidInstructionDataError();
  const { keys: { source, mint, destination, owner, multiSigners }, data } = decodeTransferCheckedInstructionUnchecked(instruction);
  if (data.instruction !== TokenInstruction.TransferChecked)
    throw new TokenInvalidInstructionTypeError();
  if (!source || !mint || !destination || !owner)
    throw new TokenInvalidInstructionKeysError();
  return {
    programId,
    keys: {
      source,
      mint,
      destination,
      owner,
      multiSigners
    },
    data
  };
}
function decodeTransferCheckedInstructionUnchecked({ programId, keys: [source, mint, destination, owner, ...multiSigners], data }) {
  return {
    programId,
    keys: {
      source,
      mint,
      destination,
      owner,
      multiSigners
    },
    data: transferCheckedInstructionData.decode(data)
  };
}

// node_modules/@solana/spl-token/lib/esm/extensions/transferHook/state.js
var import_buffer_layout29 = __toESM(require_Layout(), 1);
init_index_browser_esm();

// node_modules/@solana/spl-token/lib/esm/extensions/transferHook/seeds.js
var DISCRIMINATOR_SPAN = 1;
var LITERAL_LENGTH_SPAN = 1;
var INSTRUCTION_ARG_OFFSET_SPAN = 1;
var INSTRUCTION_ARG_LENGTH_SPAN = 1;
var ACCOUNT_KEY_INDEX_SPAN = 1;
var ACCOUNT_DATA_ACCOUNT_INDEX_SPAN = 1;
var ACCOUNT_DATA_OFFSET_SPAN = 1;
var ACCOUNT_DATA_LENGTH_SPAN = 1;
function unpackSeedLiteral(seeds) {
  if (seeds.length < 1) {
    throw new TokenTransferHookInvalidSeed();
  }
  const [length, ...rest] = seeds;
  if (rest.length < length) {
    throw new TokenTransferHookInvalidSeed();
  }
  return {
    data: Buffer.from(rest.slice(0, length)),
    packedLength: DISCRIMINATOR_SPAN + LITERAL_LENGTH_SPAN + length
  };
}
function unpackSeedInstructionArg(seeds, instructionData) {
  if (seeds.length < 2) {
    throw new TokenTransferHookInvalidSeed();
  }
  const [index, length] = seeds;
  if (instructionData.length < length + index) {
    throw new TokenTransferHookInvalidSeed();
  }
  return {
    data: instructionData.subarray(index, index + length),
    packedLength: DISCRIMINATOR_SPAN + INSTRUCTION_ARG_OFFSET_SPAN + INSTRUCTION_ARG_LENGTH_SPAN
  };
}
function unpackSeedAccountKey(seeds, previousMetas) {
  if (seeds.length < 1) {
    throw new TokenTransferHookInvalidSeed();
  }
  const [index] = seeds;
  if (previousMetas.length <= index) {
    throw new TokenTransferHookInvalidSeed();
  }
  return {
    data: previousMetas[index].pubkey.toBuffer(),
    packedLength: DISCRIMINATOR_SPAN + ACCOUNT_KEY_INDEX_SPAN
  };
}
async function unpackSeedAccountData(seeds, previousMetas, connection) {
  if (seeds.length < 3) {
    throw new TokenTransferHookInvalidSeed();
  }
  const [accountIndex, dataIndex, length] = seeds;
  if (previousMetas.length <= accountIndex) {
    throw new TokenTransferHookInvalidSeed();
  }
  const accountInfo = await connection.getAccountInfo(previousMetas[accountIndex].pubkey);
  if (accountInfo == null) {
    throw new TokenTransferHookAccountDataNotFound();
  }
  if (accountInfo.data.length < dataIndex + length) {
    throw new TokenTransferHookInvalidSeed();
  }
  return {
    data: accountInfo.data.subarray(dataIndex, dataIndex + length),
    packedLength: DISCRIMINATOR_SPAN + ACCOUNT_DATA_ACCOUNT_INDEX_SPAN + ACCOUNT_DATA_OFFSET_SPAN + ACCOUNT_DATA_LENGTH_SPAN
  };
}
async function unpackFirstSeed(seeds, previousMetas, instructionData, connection) {
  const [discriminator, ...rest] = seeds;
  const remaining = new Uint8Array(rest);
  switch (discriminator) {
    case 0:
      return null;
    case 1:
      return unpackSeedLiteral(remaining);
    case 2:
      return unpackSeedInstructionArg(remaining, instructionData);
    case 3:
      return unpackSeedAccountKey(remaining, previousMetas);
    case 4:
      return unpackSeedAccountData(remaining, previousMetas, connection);
    default:
      throw new TokenTransferHookInvalidSeed();
  }
}
async function unpackSeeds(seeds, previousMetas, instructionData, connection) {
  const unpackedSeeds = [];
  let i = 0;
  while (i < 32) {
    const seed = await unpackFirstSeed(seeds.slice(i), previousMetas, instructionData, connection);
    if (seed == null) {
      break;
    }
    unpackedSeeds.push(seed.data);
    i += seed.packedLength;
  }
  return unpackedSeeds;
}

// node_modules/@solana/spl-token/lib/esm/extensions/transferHook/pubkeyData.js
init_index_browser_esm();
async function unpackPubkeyData(keyDataConfig, previousMetas, instructionData, connection) {
  const [discriminator, ...rest] = keyDataConfig;
  const remaining = new Uint8Array(rest);
  switch (discriminator) {
    case 1:
      return unpackPubkeyDataFromInstructionData(remaining, instructionData);
    case 2:
      return unpackPubkeyDataFromAccountData(remaining, previousMetas, connection);
    default:
      throw new TokenTransferHookInvalidPubkeyData();
  }
}
function unpackPubkeyDataFromInstructionData(remaining, instructionData) {
  if (remaining.length < 1) {
    throw new TokenTransferHookInvalidPubkeyData();
  }
  const dataIndex = remaining[0];
  if (instructionData.length < dataIndex + PUBLIC_KEY_LENGTH) {
    throw new TokenTransferHookPubkeyDataTooSmall();
  }
  return new PublicKey(instructionData.subarray(dataIndex, dataIndex + PUBLIC_KEY_LENGTH));
}
async function unpackPubkeyDataFromAccountData(remaining, previousMetas, connection) {
  if (remaining.length < 2) {
    throw new TokenTransferHookInvalidPubkeyData();
  }
  const [accountIndex, dataIndex] = remaining;
  if (previousMetas.length <= accountIndex) {
    throw new TokenTransferHookAccountDataNotFound();
  }
  const accountInfo = await connection.getAccountInfo(previousMetas[accountIndex].pubkey);
  if (accountInfo == null) {
    throw new TokenTransferHookAccountNotFound();
  }
  if (accountInfo.data.length < dataIndex + PUBLIC_KEY_LENGTH) {
    throw new TokenTransferHookPubkeyDataTooSmall();
  }
  return new PublicKey(accountInfo.data.subarray(dataIndex, dataIndex + PUBLIC_KEY_LENGTH));
}

// node_modules/@solana/spl-token/lib/esm/extensions/transferHook/state.js
var TransferHookLayout = (0, import_buffer_layout29.struct)([publicKey("authority"), publicKey("programId")]);
var TRANSFER_HOOK_SIZE = TransferHookLayout.span;
function getTransferHook(mint) {
  const extensionData = getExtensionData(ExtensionType.TransferHook, mint.tlvData);
  if (extensionData !== null) {
    return TransferHookLayout.decode(extensionData);
  } else {
    return null;
  }
}
var TransferHookAccountLayout = (0, import_buffer_layout29.struct)([bool("transferring")]);
var TRANSFER_HOOK_ACCOUNT_SIZE = TransferHookAccountLayout.span;
function getTransferHookAccount(account) {
  const extensionData = getExtensionData(ExtensionType.TransferHookAccount, account.tlvData);
  if (extensionData !== null) {
    return TransferHookAccountLayout.decode(extensionData);
  } else {
    return null;
  }
}
function getExtraAccountMetaAddress(mint, programId) {
  const seeds = [Buffer.from("extra-account-metas"), mint.toBuffer()];
  return PublicKey.findProgramAddressSync(seeds, programId)[0];
}
var ExtraAccountMetaLayout = (0, import_buffer_layout29.struct)([
  (0, import_buffer_layout29.u8)("discriminator"),
  (0, import_buffer_layout29.blob)(32, "addressConfig"),
  bool("isSigner"),
  bool("isWritable")
]);
var ExtraAccountMetaListLayout = (0, import_buffer_layout29.struct)([
  (0, import_buffer_layout29.u32)("count"),
  (0, import_buffer_layout29.seq)(ExtraAccountMetaLayout, (0, import_buffer_layout29.greedy)(ExtraAccountMetaLayout.span), "extraAccounts")
]);
var ExtraAccountMetaAccountDataLayout = (0, import_buffer_layout29.struct)([
  u64("instructionDiscriminator"),
  (0, import_buffer_layout29.u32)("length"),
  ExtraAccountMetaListLayout.replicate("extraAccountsList")
]);
function getExtraAccountMetas(account) {
  const extraAccountsList = ExtraAccountMetaAccountDataLayout.decode(account.data).extraAccountsList;
  return extraAccountsList.extraAccounts.slice(0, extraAccountsList.count);
}
async function resolveExtraAccountMeta(connection, extraMeta, previousMetas, instructionData, transferHookProgramId) {
  if (extraMeta.discriminator === 0) {
    return {
      pubkey: new PublicKey(extraMeta.addressConfig),
      isSigner: extraMeta.isSigner,
      isWritable: extraMeta.isWritable
    };
  } else if (extraMeta.discriminator === 2) {
    const pubkey2 = await unpackPubkeyData(extraMeta.addressConfig, previousMetas, instructionData, connection);
    return {
      pubkey: pubkey2,
      isSigner: extraMeta.isSigner,
      isWritable: extraMeta.isWritable
    };
  }
  let programId = PublicKey.default;
  if (extraMeta.discriminator === 1) {
    programId = transferHookProgramId;
  } else {
    const accountIndex = extraMeta.discriminator - (1 << 7);
    if (previousMetas.length <= accountIndex) {
      throw new TokenTransferHookAccountNotFound();
    }
    programId = previousMetas[accountIndex].pubkey;
  }
  const seeds = await unpackSeeds(extraMeta.addressConfig, previousMetas, instructionData, connection);
  const pubkey = PublicKey.findProgramAddressSync(seeds, programId)[0];
  return { pubkey, isSigner: extraMeta.isSigner, isWritable: extraMeta.isWritable };
}

// node_modules/@solana/spl-token/lib/esm/extensions/transferHook/instructions.js
var TransferHookInstruction;
(function(TransferHookInstruction2) {
  TransferHookInstruction2[TransferHookInstruction2["Initialize"] = 0] = "Initialize";
  TransferHookInstruction2[TransferHookInstruction2["Update"] = 1] = "Update";
})(TransferHookInstruction || (TransferHookInstruction = {}));
var initializeTransferHookInstructionData = (0, import_buffer_layout30.struct)([
  (0, import_buffer_layout30.u8)("instruction"),
  (0, import_buffer_layout30.u8)("transferHookInstruction"),
  publicKey("authority"),
  publicKey("transferHookProgramId")
]);
function createInitializeTransferHookInstruction(mint, authority, transferHookProgramId, programId) {
  if (!programSupportsExtensions(programId)) {
    throw new TokenUnsupportedInstructionError();
  }
  const keys = [{ pubkey: mint, isSigner: false, isWritable: true }];
  const data = Buffer.alloc(initializeTransferHookInstructionData.span);
  initializeTransferHookInstructionData.encode({
    instruction: TokenInstruction.TransferHookExtension,
    transferHookInstruction: TransferHookInstruction.Initialize,
    authority,
    transferHookProgramId
  }, data);
  return new TransactionInstruction({ keys, programId, data });
}
var updateTransferHookInstructionData = (0, import_buffer_layout30.struct)([
  (0, import_buffer_layout30.u8)("instruction"),
  (0, import_buffer_layout30.u8)("transferHookInstruction"),
  publicKey("transferHookProgramId")
]);
function createUpdateTransferHookInstruction(mint, authority, transferHookProgramId, multiSigners = [], programId = TOKEN_2022_PROGRAM_ID) {
  if (!programSupportsExtensions(programId)) {
    throw new TokenUnsupportedInstructionError();
  }
  const keys = addSigners([{ pubkey: mint, isSigner: false, isWritable: true }], authority, multiSigners);
  const data = Buffer.alloc(updateTransferHookInstructionData.span);
  updateTransferHookInstructionData.encode({
    instruction: TokenInstruction.TransferHookExtension,
    transferHookInstruction: TransferHookInstruction.Update,
    transferHookProgramId
  }, data);
  return new TransactionInstruction({ keys, programId, data });
}
function deEscalateAccountMeta(accountMeta, accountMetas) {
  const maybeHighestPrivileges = accountMetas.filter((x) => x.pubkey.equals(accountMeta.pubkey)).reduce((acc, x) => {
    if (!acc)
      return { isSigner: x.isSigner, isWritable: x.isWritable };
    return { isSigner: acc.isSigner || x.isSigner, isWritable: acc.isWritable || x.isWritable };
  }, void 0);
  if (maybeHighestPrivileges) {
    const { isSigner, isWritable } = maybeHighestPrivileges;
    if (!isSigner && isSigner !== accountMeta.isSigner) {
      accountMeta.isSigner = false;
    }
    if (!isWritable && isWritable !== accountMeta.isWritable) {
      accountMeta.isWritable = false;
    }
  }
  return accountMeta;
}
function createExecuteInstruction(programId, source, mint, destination, owner, validateStatePubkey, amount) {
  const keys = [source, mint, destination, owner, validateStatePubkey].map((pubkey) => ({
    pubkey,
    isSigner: false,
    isWritable: false
  }));
  const data = Buffer.alloc(16);
  data.set(Buffer.from([105, 37, 101, 197, 75, 251, 102, 26]), 0);
  data.writeBigUInt64LE(BigInt(amount), 8);
  return new TransactionInstruction({ keys, programId, data });
}
async function addExtraAccountMetasForExecute(connection, instruction, programId, source, mint, destination, owner, amount, commitment) {
  const validateStatePubkey = getExtraAccountMetaAddress(mint, programId);
  const validateStateAccount = await connection.getAccountInfo(validateStatePubkey, commitment);
  if (validateStateAccount == null) {
    return instruction;
  }
  const validateStateData = getExtraAccountMetas(validateStateAccount);
  if (![source, mint, destination, owner].every((key) => instruction.keys.some((meta) => meta.pubkey.equals(key)))) {
    throw new Error("Missing required account in instruction");
  }
  const executeInstruction = createExecuteInstruction(programId, source, mint, destination, owner, validateStatePubkey, BigInt(amount));
  for (const extraAccountMeta of validateStateData) {
    executeInstruction.keys.push(deEscalateAccountMeta(await resolveExtraAccountMeta(connection, extraAccountMeta, executeInstruction.keys, executeInstruction.data, executeInstruction.programId), executeInstruction.keys));
  }
  instruction.keys.push(...executeInstruction.keys.slice(5));
  instruction.keys.push({ pubkey: programId, isSigner: false, isWritable: false });
  instruction.keys.push({ pubkey: validateStatePubkey, isSigner: false, isWritable: false });
}
async function createTransferCheckedWithTransferHookInstruction(connection, source, mint, destination, owner, amount, decimals, multiSigners = [], commitment, programId = TOKEN_PROGRAM_ID) {
  const instruction = createTransferCheckedInstruction(source, mint, destination, owner, amount, decimals, multiSigners, programId);
  const mintInfo = await getMint(connection, mint, commitment, programId);
  const transferHook = getTransferHook(mintInfo);
  if (transferHook) {
    await addExtraAccountMetasForExecute(connection, instruction, transferHook.programId, source, mint, destination, owner, amount, commitment);
  }
  return instruction;
}
async function createTransferCheckedWithFeeAndTransferHookInstruction(connection, source, mint, destination, owner, amount, decimals, fee, multiSigners = [], commitment, programId = TOKEN_PROGRAM_ID) {
  const instruction = createTransferCheckedWithFeeInstruction(source, mint, destination, owner, amount, decimals, fee, multiSigners, programId);
  const mintInfo = await getMint(connection, mint, commitment, programId);
  const transferHook = getTransferHook(mintInfo);
  if (transferHook) {
    await addExtraAccountMetasForExecute(connection, instruction, transferHook.programId, source, mint, destination, owner, amount, commitment);
  }
  return instruction;
}

// node_modules/@solana/spl-token/lib/esm/extensions/transferHook/actions.js
async function initializeTransferHook(connection, payer, mint, authority, transferHookProgramId, confirmOptions, programId = TOKEN_2022_PROGRAM_ID) {
  const transaction = new Transaction().add(createInitializeTransferHookInstruction(mint, authority, transferHookProgramId, programId));
  return await sendAndConfirmTransaction(connection, transaction, [payer], confirmOptions);
}
async function updateTransferHook(connection, payer, mint, transferHookProgramId, authority, multiSigners = [], confirmOptions, programId = TOKEN_2022_PROGRAM_ID) {
  const [authorityPublicKey, signers] = getSigners(authority, multiSigners);
  const transaction = new Transaction().add(createUpdateTransferHookInstruction(mint, authorityPublicKey, transferHookProgramId, signers, programId));
  return await sendAndConfirmTransaction(connection, transaction, [payer, ...signers], confirmOptions);
}
async function transferCheckedWithTransferHook(connection, payer, source, mint, destination, authority, amount, decimals, multiSigners = [], confirmOptions, programId = TOKEN_PROGRAM_ID) {
  const [authorityPublicKey, signers] = getSigners(authority, multiSigners);
  const transaction = new Transaction().add(await createTransferCheckedWithTransferHookInstruction(connection, source, mint, destination, authorityPublicKey, amount, decimals, signers, confirmOptions == null ? void 0 : confirmOptions.commitment, programId));
  return await sendAndConfirmTransaction(connection, transaction, [payer, ...signers], confirmOptions);
}
async function transferCheckedWithFeeAndTransferHook(connection, payer, source, mint, destination, authority, amount, decimals, fee, multiSigners = [], confirmOptions, programId = TOKEN_PROGRAM_ID) {
  const [authorityPublicKey, signers] = getSigners(authority, multiSigners);
  const transaction = new Transaction().add(await createTransferCheckedWithFeeAndTransferHookInstruction(connection, source, mint, destination, authorityPublicKey, amount, decimals, fee, signers, confirmOptions == null ? void 0 : confirmOptions.commitment, programId));
  return await sendAndConfirmTransaction(connection, transaction, [payer, ...signers], confirmOptions);
}

// node_modules/@solana/spl-token/lib/esm/extensions/extensionType.js
var ExtensionType;
(function(ExtensionType2) {
  ExtensionType2[ExtensionType2["Uninitialized"] = 0] = "Uninitialized";
  ExtensionType2[ExtensionType2["TransferFeeConfig"] = 1] = "TransferFeeConfig";
  ExtensionType2[ExtensionType2["TransferFeeAmount"] = 2] = "TransferFeeAmount";
  ExtensionType2[ExtensionType2["MintCloseAuthority"] = 3] = "MintCloseAuthority";
  ExtensionType2[ExtensionType2["ConfidentialTransferMint"] = 4] = "ConfidentialTransferMint";
  ExtensionType2[ExtensionType2["ConfidentialTransferAccount"] = 5] = "ConfidentialTransferAccount";
  ExtensionType2[ExtensionType2["DefaultAccountState"] = 6] = "DefaultAccountState";
  ExtensionType2[ExtensionType2["ImmutableOwner"] = 7] = "ImmutableOwner";
  ExtensionType2[ExtensionType2["MemoTransfer"] = 8] = "MemoTransfer";
  ExtensionType2[ExtensionType2["NonTransferable"] = 9] = "NonTransferable";
  ExtensionType2[ExtensionType2["InterestBearingConfig"] = 10] = "InterestBearingConfig";
  ExtensionType2[ExtensionType2["CpiGuard"] = 11] = "CpiGuard";
  ExtensionType2[ExtensionType2["PermanentDelegate"] = 12] = "PermanentDelegate";
  ExtensionType2[ExtensionType2["NonTransferableAccount"] = 13] = "NonTransferableAccount";
  ExtensionType2[ExtensionType2["TransferHook"] = 14] = "TransferHook";
  ExtensionType2[ExtensionType2["TransferHookAccount"] = 15] = "TransferHookAccount";
  ExtensionType2[ExtensionType2["MetadataPointer"] = 18] = "MetadataPointer";
  ExtensionType2[ExtensionType2["TokenMetadata"] = 19] = "TokenMetadata";
  ExtensionType2[ExtensionType2["GroupPointer"] = 20] = "GroupPointer";
  ExtensionType2[ExtensionType2["TokenGroup"] = 21] = "TokenGroup";
  ExtensionType2[ExtensionType2["GroupMemberPointer"] = 22] = "GroupMemberPointer";
  ExtensionType2[ExtensionType2["TokenGroupMember"] = 23] = "TokenGroupMember";
  ExtensionType2[ExtensionType2["ScaledUiAmountConfig"] = 25] = "ScaledUiAmountConfig";
  ExtensionType2[ExtensionType2["PausableConfig"] = 26] = "PausableConfig";
  ExtensionType2[ExtensionType2["PausableAccount"] = 27] = "PausableAccount";
})(ExtensionType || (ExtensionType = {}));
var TYPE_SIZE = 2;
var LENGTH_SIZE = 2;
function addTypeAndLengthToLen(len) {
  return len + TYPE_SIZE + LENGTH_SIZE;
}
function isVariableLengthExtension(e2) {
  switch (e2) {
    case ExtensionType.TokenMetadata:
      return true;
    default:
      return false;
  }
}
function getTypeLen(e2) {
  switch (e2) {
    case ExtensionType.Uninitialized:
      return 0;
    case ExtensionType.TransferFeeConfig:
      return TRANSFER_FEE_CONFIG_SIZE;
    case ExtensionType.TransferFeeAmount:
      return TRANSFER_FEE_AMOUNT_SIZE;
    case ExtensionType.MintCloseAuthority:
      return MINT_CLOSE_AUTHORITY_SIZE;
    case ExtensionType.ConfidentialTransferMint:
      return 65;
    case ExtensionType.ConfidentialTransferAccount:
      return 295;
    case ExtensionType.CpiGuard:
      return CPI_GUARD_SIZE;
    case ExtensionType.DefaultAccountState:
      return DEFAULT_ACCOUNT_STATE_SIZE;
    case ExtensionType.ImmutableOwner:
      return IMMUTABLE_OWNER_SIZE;
    case ExtensionType.MemoTransfer:
      return MEMO_TRANSFER_SIZE;
    case ExtensionType.MetadataPointer:
      return METADATA_POINTER_SIZE;
    case ExtensionType.NonTransferable:
      return NON_TRANSFERABLE_SIZE;
    case ExtensionType.InterestBearingConfig:
      return INTEREST_BEARING_MINT_CONFIG_STATE_SIZE;
    case ExtensionType.PermanentDelegate:
      return PERMANENT_DELEGATE_SIZE;
    case ExtensionType.NonTransferableAccount:
      return NON_TRANSFERABLE_ACCOUNT_SIZE;
    case ExtensionType.TransferHook:
      return TRANSFER_HOOK_SIZE;
    case ExtensionType.TransferHookAccount:
      return TRANSFER_HOOK_ACCOUNT_SIZE;
    case ExtensionType.GroupPointer:
      return GROUP_POINTER_SIZE;
    case ExtensionType.GroupMemberPointer:
      return GROUP_MEMBER_POINTER_SIZE;
    case ExtensionType.TokenGroup:
      return TOKEN_GROUP_SIZE;
    case ExtensionType.TokenGroupMember:
      return TOKEN_GROUP_MEMBER_SIZE;
    case ExtensionType.ScaledUiAmountConfig:
      return SCALED_UI_AMOUNT_CONFIG_SIZE;
    case ExtensionType.PausableConfig:
      return PAUSABLE_CONFIG_SIZE;
    case ExtensionType.PausableAccount:
      return PAUSABLE_ACCOUNT_SIZE;
    case ExtensionType.TokenMetadata:
      throw Error(`Cannot get type length for variable extension type: ${e2}`);
    default:
      throw Error(`Unknown extension type: ${e2}`);
  }
}
function isMintExtension(e2) {
  switch (e2) {
    case ExtensionType.TransferFeeConfig:
    case ExtensionType.MintCloseAuthority:
    case ExtensionType.ConfidentialTransferMint:
    case ExtensionType.DefaultAccountState:
    case ExtensionType.NonTransferable:
    case ExtensionType.InterestBearingConfig:
    case ExtensionType.PermanentDelegate:
    case ExtensionType.TransferHook:
    case ExtensionType.MetadataPointer:
    case ExtensionType.TokenMetadata:
    case ExtensionType.GroupPointer:
    case ExtensionType.GroupMemberPointer:
    case ExtensionType.TokenGroup:
    case ExtensionType.TokenGroupMember:
    case ExtensionType.ScaledUiAmountConfig:
    case ExtensionType.PausableConfig:
      return true;
    case ExtensionType.Uninitialized:
    case ExtensionType.TransferFeeAmount:
    case ExtensionType.ConfidentialTransferAccount:
    case ExtensionType.ImmutableOwner:
    case ExtensionType.MemoTransfer:
    case ExtensionType.CpiGuard:
    case ExtensionType.NonTransferableAccount:
    case ExtensionType.TransferHookAccount:
    case ExtensionType.PausableAccount:
      return false;
    default:
      throw Error(`Unknown extension type: ${e2}`);
  }
}
function isAccountExtension(e2) {
  switch (e2) {
    case ExtensionType.TransferFeeAmount:
    case ExtensionType.ConfidentialTransferAccount:
    case ExtensionType.ImmutableOwner:
    case ExtensionType.MemoTransfer:
    case ExtensionType.CpiGuard:
    case ExtensionType.NonTransferableAccount:
    case ExtensionType.TransferHookAccount:
    case ExtensionType.PausableAccount:
      return true;
    case ExtensionType.Uninitialized:
    case ExtensionType.TransferFeeConfig:
    case ExtensionType.MintCloseAuthority:
    case ExtensionType.ConfidentialTransferMint:
    case ExtensionType.DefaultAccountState:
    case ExtensionType.NonTransferable:
    case ExtensionType.InterestBearingConfig:
    case ExtensionType.PermanentDelegate:
    case ExtensionType.TransferHook:
    case ExtensionType.MetadataPointer:
    case ExtensionType.TokenMetadata:
    case ExtensionType.GroupPointer:
    case ExtensionType.GroupMemberPointer:
    case ExtensionType.TokenGroup:
    case ExtensionType.TokenGroupMember:
    case ExtensionType.ScaledUiAmountConfig:
    case ExtensionType.PausableConfig:
      return false;
    default:
      throw Error(`Unknown extension type: ${e2}`);
  }
}
function getAccountTypeOfMintType(e2) {
  switch (e2) {
    case ExtensionType.TransferFeeConfig:
      return ExtensionType.TransferFeeAmount;
    case ExtensionType.ConfidentialTransferMint:
      return ExtensionType.ConfidentialTransferAccount;
    case ExtensionType.NonTransferable:
      return ExtensionType.NonTransferableAccount;
    case ExtensionType.TransferHook:
      return ExtensionType.TransferHookAccount;
    case ExtensionType.PausableConfig:
      return ExtensionType.PausableAccount;
    case ExtensionType.TransferFeeAmount:
    case ExtensionType.ConfidentialTransferAccount:
    case ExtensionType.CpiGuard:
    case ExtensionType.DefaultAccountState:
    case ExtensionType.ImmutableOwner:
    case ExtensionType.MemoTransfer:
    case ExtensionType.MintCloseAuthority:
    case ExtensionType.MetadataPointer:
    case ExtensionType.TokenMetadata:
    case ExtensionType.Uninitialized:
    case ExtensionType.InterestBearingConfig:
    case ExtensionType.PermanentDelegate:
    case ExtensionType.NonTransferableAccount:
    case ExtensionType.TransferHookAccount:
    case ExtensionType.GroupPointer:
    case ExtensionType.GroupMemberPointer:
    case ExtensionType.TokenGroup:
    case ExtensionType.TokenGroupMember:
    case ExtensionType.ScaledUiAmountConfig:
    case ExtensionType.PausableAccount:
      return ExtensionType.Uninitialized;
  }
}
function getLen(extensionTypes, baseSize, variableLengthExtensions = {}) {
  if (extensionTypes.length === 0 && Object.keys(variableLengthExtensions).length === 0) {
    return baseSize;
  } else {
    const accountLength = ACCOUNT_SIZE + ACCOUNT_TYPE_SIZE + extensionTypes.filter((element, i) => i === extensionTypes.indexOf(element)).map((element) => addTypeAndLengthToLen(getTypeLen(element))).reduce((a, b) => a + b, 0) + Object.entries(variableLengthExtensions).map(([extension, len]) => {
      if (!isVariableLengthExtension(Number(extension))) {
        throw Error(`Extension ${extension} is not variable length`);
      }
      return addTypeAndLengthToLen(len);
    }).reduce((a, b) => a + b, 0);
    if (accountLength === MULTISIG_SIZE) {
      return accountLength + TYPE_SIZE;
    } else {
      return accountLength;
    }
  }
}
function getMintLen(extensionTypes, variableLengthExtensions = {}) {
  return getLen(extensionTypes, MINT_SIZE, variableLengthExtensions);
}
function getAccountLen(extensionTypes) {
  return getLen(extensionTypes, ACCOUNT_SIZE);
}
function getExtensionData(extension, tlvData) {
  let extensionTypeIndex = 0;
  while (addTypeAndLengthToLen(extensionTypeIndex) <= tlvData.length) {
    const entryType = tlvData.readUInt16LE(extensionTypeIndex);
    const entryLength = tlvData.readUInt16LE(extensionTypeIndex + TYPE_SIZE);
    const typeIndex = addTypeAndLengthToLen(extensionTypeIndex);
    if (entryType == extension) {
      return tlvData.slice(typeIndex, typeIndex + entryLength);
    }
    extensionTypeIndex = typeIndex + entryLength;
  }
  return null;
}
function getExtensionTypes(tlvData) {
  const extensionTypes = [];
  let extensionTypeIndex = 0;
  while (extensionTypeIndex < tlvData.length) {
    const entryType = tlvData.readUInt16LE(extensionTypeIndex);
    extensionTypes.push(entryType);
    const entryLength = tlvData.readUInt16LE(extensionTypeIndex + TYPE_SIZE);
    extensionTypeIndex += addTypeAndLengthToLen(entryLength);
  }
  return extensionTypes;
}
function getAccountLenForMint(mint) {
  const extensionTypes = getExtensionTypes(mint.tlvData);
  const accountExtensions = extensionTypes.map(getAccountTypeOfMintType);
  return getAccountLen(accountExtensions);
}
function getNewAccountLenForExtensionLen(info, address, extensionType, extensionLen, programId = TOKEN_2022_PROGRAM_ID) {
  const mint = unpackMint(address, info, programId);
  const extensionData = getExtensionData(extensionType, mint.tlvData);
  const currentExtensionLen = extensionData ? addTypeAndLengthToLen(extensionData.length) : 0;
  const newExtensionLen = addTypeAndLengthToLen(extensionLen);
  return info.data.length + newExtensionLen - currentExtensionLen;
}

// node_modules/@solana/spl-token/lib/esm/state/mint.js
var MintLayout = (0, import_buffer_layout31.struct)([
  (0, import_buffer_layout31.u32)("mintAuthorityOption"),
  publicKey("mintAuthority"),
  u64("supply"),
  (0, import_buffer_layout31.u8)("decimals"),
  bool("isInitialized"),
  (0, import_buffer_layout31.u32)("freezeAuthorityOption"),
  publicKey("freezeAuthority")
]);
var MINT_SIZE = MintLayout.span;
async function getMint(connection, address, commitment, programId = TOKEN_PROGRAM_ID) {
  const info = await connection.getAccountInfo(address, commitment);
  return unpackMint(address, info, programId);
}
function unpackMint(address, info, programId = TOKEN_PROGRAM_ID) {
  if (!info)
    throw new TokenAccountNotFoundError();
  if (!info.owner.equals(programId))
    throw new TokenInvalidAccountOwnerError();
  if (info.data.length < MINT_SIZE)
    throw new TokenInvalidAccountSizeError();
  const rawMint = MintLayout.decode(info.data.slice(0, MINT_SIZE));
  let tlvData = Buffer.alloc(0);
  if (info.data.length > MINT_SIZE) {
    if (info.data.length <= ACCOUNT_SIZE)
      throw new TokenInvalidAccountSizeError();
    if (info.data.length === MULTISIG_SIZE)
      throw new TokenInvalidAccountSizeError();
    if (info.data[ACCOUNT_SIZE] != AccountType.Mint)
      throw new TokenInvalidMintError();
    tlvData = info.data.slice(ACCOUNT_SIZE + ACCOUNT_TYPE_SIZE);
  }
  return {
    address,
    mintAuthority: rawMint.mintAuthorityOption ? rawMint.mintAuthority : null,
    supply: rawMint.supply,
    decimals: rawMint.decimals,
    isInitialized: rawMint.isInitialized,
    freezeAuthority: rawMint.freezeAuthorityOption ? rawMint.freezeAuthority : null,
    tlvData
  };
}
async function getMinimumBalanceForRentExemptMint(connection, commitment) {
  return await getMinimumBalanceForRentExemptMintWithExtensions(connection, [], commitment);
}
async function getMinimumBalanceForRentExemptMintWithExtensions(connection, extensions, commitment) {
  const mintLen = getMintLen(extensions);
  return await connection.getMinimumBalanceForRentExemption(mintLen, commitment);
}
async function getAssociatedTokenAddress(mint, owner, allowOwnerOffCurve = false, programId = TOKEN_PROGRAM_ID, associatedTokenProgramId = ASSOCIATED_TOKEN_PROGRAM_ID) {
  if (!allowOwnerOffCurve && !PublicKey.isOnCurve(owner.toBuffer()))
    throw new TokenOwnerOffCurveError();
  const [address] = await PublicKey.findProgramAddress([owner.toBuffer(), programId.toBuffer(), mint.toBuffer()], associatedTokenProgramId);
  return address;
}
function getAssociatedTokenAddressSync(mint, owner, allowOwnerOffCurve = false, programId = TOKEN_PROGRAM_ID, associatedTokenProgramId = ASSOCIATED_TOKEN_PROGRAM_ID) {
  if (!allowOwnerOffCurve && !PublicKey.isOnCurve(owner.toBuffer()))
    throw new TokenOwnerOffCurveError();
  const [address] = PublicKey.findProgramAddressSync([owner.toBuffer(), programId.toBuffer(), mint.toBuffer()], associatedTokenProgramId);
  return address;
}

// node_modules/@solana/spl-token/lib/esm/actions/amountToUiAmount.js
async function amountToUiAmount(connection, payer, mint, amount, programId = TOKEN_PROGRAM_ID) {
  const transaction = new Transaction().add(createAmountToUiAmountInstruction(mint, amount, programId));
  const { returnData, err } = (await connection.simulateTransaction(transaction, [payer], false)).value;
  if (returnData == null ? void 0 : returnData.data) {
    return Buffer.from(returnData.data[0], returnData.data[1]).toString("utf-8");
  }
  return err;
}
function calculateExponentForTimesAndRate(t1, t2, r) {
  const ONE_IN_BASIS_POINTS2 = 1e4;
  const SECONDS_PER_YEAR = 60 * 60 * 24 * 365.24;
  const timespan = t2 - t1;
  const numerator = r * timespan;
  const exponent = numerator / (SECONDS_PER_YEAR * ONE_IN_BASIS_POINTS2);
  return Math.exp(exponent);
}
async function getSysvarClockTimestamp(connection) {
  const info = await connection.getParsedAccountInfo(new PublicKey("SysvarC1ock11111111111111111111111111111111"));
  if (!info) {
    throw new Error("Failed to fetch sysvar clock");
  }
  if (typeof info.value === "object" && info.value && "data" in info.value && "parsed" in info.value.data) {
    return info.value.data.parsed.info.unixTimestamp;
  }
  throw new Error("Failed to parse sysvar clock");
}
function amountToUiAmountWithoutSimulation(amount, decimals, currentTimestamp, lastUpdateTimestamp, initializationTimestamp, preUpdateAverageRate, currentRate) {
  const preUpdateExp = calculateExponentForTimesAndRate(initializationTimestamp, lastUpdateTimestamp, preUpdateAverageRate);
  const postUpdateExp = calculateExponentForTimesAndRate(lastUpdateTimestamp, currentTimestamp, currentRate);
  const totalScale = preUpdateExp * postUpdateExp;
  const scaledAmount = Number(amount) * totalScale;
  const decimalFactor = Math.pow(10, decimals);
  return (Math.trunc(scaledAmount) / decimalFactor).toString();
}
async function amountToUiAmountForMintWithoutSimulation(connection, mint, amount) {
  const accountInfo = await connection.getAccountInfo(mint);
  const programId = accountInfo == null ? void 0 : accountInfo.owner;
  if (programId !== TOKEN_PROGRAM_ID && programId !== TOKEN_2022_PROGRAM_ID) {
    throw new Error("Invalid program ID");
  }
  const mintInfo = unpackMint(mint, accountInfo, programId);
  const interestBearingMintConfigState = getInterestBearingMintConfigState(mintInfo);
  if (!interestBearingMintConfigState) {
    const amountNumber = Number(amount);
    const decimalsFactor = Math.pow(10, mintInfo.decimals);
    return (amountNumber / decimalsFactor).toString();
  }
  const timestamp = await getSysvarClockTimestamp(connection);
  return amountToUiAmountWithoutSimulation(amount, mintInfo.decimals, timestamp, Number(interestBearingMintConfigState.lastUpdateTimestamp), Number(interestBearingMintConfigState.initializationTimestamp), interestBearingMintConfigState.preUpdateAverageRate, interestBearingMintConfigState.currentRate);
}
function uiAmountToAmountWithoutSimulation(uiAmount, decimals, currentTimestamp, lastUpdateTimestamp, initializationTimestamp, preUpdateAverageRate, currentRate) {
  const uiAmountNumber = parseFloat(uiAmount);
  const decimalsFactor = Math.pow(10, decimals);
  const uiAmountScaled = uiAmountNumber * decimalsFactor;
  const preUpdateExp = calculateExponentForTimesAndRate(initializationTimestamp, lastUpdateTimestamp, preUpdateAverageRate);
  const postUpdateExp = calculateExponentForTimesAndRate(lastUpdateTimestamp, currentTimestamp, currentRate);
  const totalScale = preUpdateExp * postUpdateExp;
  const originalPrincipal = uiAmountScaled / totalScale;
  return BigInt(Math.trunc(originalPrincipal));
}
async function uiAmountToAmountForMintWithoutSimulation(connection, mint, uiAmount) {
  const accountInfo = await connection.getAccountInfo(mint);
  const programId = accountInfo == null ? void 0 : accountInfo.owner;
  if (programId !== TOKEN_PROGRAM_ID && programId !== TOKEN_2022_PROGRAM_ID) {
    throw new Error("Invalid program ID");
  }
  const mintInfo = unpackMint(mint, accountInfo, programId);
  const interestBearingMintConfigState = getInterestBearingMintConfigState(mintInfo);
  if (!interestBearingMintConfigState) {
    const uiAmountScaled = parseFloat(uiAmount) * Math.pow(10, mintInfo.decimals);
    return BigInt(Math.trunc(uiAmountScaled));
  }
  const timestamp = await getSysvarClockTimestamp(connection);
  return uiAmountToAmountWithoutSimulation(uiAmount, mintInfo.decimals, timestamp, Number(interestBearingMintConfigState.lastUpdateTimestamp), Number(interestBearingMintConfigState.initializationTimestamp), interestBearingMintConfigState.preUpdateAverageRate, interestBearingMintConfigState.currentRate);
}

// node_modules/@solana/spl-token/lib/esm/actions/approve.js
init_index_browser_esm();

// node_modules/@solana/spl-token/lib/esm/instructions/approve.js
var import_buffer_layout32 = __toESM(require_Layout(), 1);
init_index_browser_esm();
var approveInstructionData = (0, import_buffer_layout32.struct)([(0, import_buffer_layout32.u8)("instruction"), u64("amount")]);
function createApproveInstruction(account, delegate, owner, amount, multiSigners = [], programId = TOKEN_PROGRAM_ID) {
  const keys = addSigners([
    { pubkey: account, isSigner: false, isWritable: true },
    { pubkey: delegate, isSigner: false, isWritable: false }
  ], owner, multiSigners);
  const data = Buffer.alloc(approveInstructionData.span);
  approveInstructionData.encode({
    instruction: TokenInstruction.Approve,
    amount: BigInt(amount)
  }, data);
  return new TransactionInstruction({ keys, programId, data });
}
function decodeApproveInstruction(instruction, programId = TOKEN_PROGRAM_ID) {
  if (!instruction.programId.equals(programId))
    throw new TokenInvalidInstructionProgramError();
  if (instruction.data.length !== approveInstructionData.span)
    throw new TokenInvalidInstructionDataError();
  const { keys: { account, delegate, owner, multiSigners }, data } = decodeApproveInstructionUnchecked(instruction);
  if (data.instruction !== TokenInstruction.Approve)
    throw new TokenInvalidInstructionTypeError();
  if (!account || !delegate || !owner)
    throw new TokenInvalidInstructionKeysError();
  return {
    programId,
    keys: {
      account,
      delegate,
      owner,
      multiSigners
    },
    data
  };
}
function decodeApproveInstructionUnchecked({ programId, keys: [account, delegate, owner, ...multiSigners], data }) {
  return {
    programId,
    keys: {
      account,
      delegate,
      owner,
      multiSigners
    },
    data: approveInstructionData.decode(data)
  };
}

// node_modules/@solana/spl-token/lib/esm/actions/approve.js
async function approve(connection, payer, account, delegate, owner, amount, multiSigners = [], confirmOptions, programId = TOKEN_PROGRAM_ID) {
  const [ownerPublicKey, signers] = getSigners(owner, multiSigners);
  const transaction = new Transaction().add(createApproveInstruction(account, delegate, ownerPublicKey, amount, multiSigners, programId));
  return await sendAndConfirmTransaction(connection, transaction, [payer, ...signers], confirmOptions);
}

// node_modules/@solana/spl-token/lib/esm/actions/approveChecked.js
init_index_browser_esm();

// node_modules/@solana/spl-token/lib/esm/instructions/approveChecked.js
var import_buffer_layout33 = __toESM(require_Layout(), 1);
init_index_browser_esm();
var approveCheckedInstructionData = (0, import_buffer_layout33.struct)([
  (0, import_buffer_layout33.u8)("instruction"),
  u64("amount"),
  (0, import_buffer_layout33.u8)("decimals")
]);
function createApproveCheckedInstruction(account, mint, delegate, owner, amount, decimals, multiSigners = [], programId = TOKEN_PROGRAM_ID) {
  const keys = addSigners([
    { pubkey: account, isSigner: false, isWritable: true },
    { pubkey: mint, isSigner: false, isWritable: false },
    { pubkey: delegate, isSigner: false, isWritable: false }
  ], owner, multiSigners);
  const data = Buffer.alloc(approveCheckedInstructionData.span);
  approveCheckedInstructionData.encode({
    instruction: TokenInstruction.ApproveChecked,
    amount: BigInt(amount),
    decimals
  }, data);
  return new TransactionInstruction({ keys, programId, data });
}
function decodeApproveCheckedInstruction(instruction, programId = TOKEN_PROGRAM_ID) {
  if (!instruction.programId.equals(programId))
    throw new TokenInvalidInstructionProgramError();
  if (instruction.data.length !== approveCheckedInstructionData.span)
    throw new TokenInvalidInstructionDataError();
  const { keys: { account, mint, delegate, owner, multiSigners }, data } = decodeApproveCheckedInstructionUnchecked(instruction);
  if (data.instruction !== TokenInstruction.ApproveChecked)
    throw new TokenInvalidInstructionTypeError();
  if (!account || !mint || !delegate || !owner)
    throw new TokenInvalidInstructionKeysError();
  return {
    programId,
    keys: {
      account,
      mint,
      delegate,
      owner,
      multiSigners
    },
    data
  };
}
function decodeApproveCheckedInstructionUnchecked({ programId, keys: [account, mint, delegate, owner, ...multiSigners], data }) {
  return {
    programId,
    keys: {
      account,
      mint,
      delegate,
      owner,
      multiSigners
    },
    data: approveCheckedInstructionData.decode(data)
  };
}

// node_modules/@solana/spl-token/lib/esm/actions/approveChecked.js
async function approveChecked(connection, payer, mint, account, delegate, owner, amount, decimals, multiSigners = [], confirmOptions, programId = TOKEN_PROGRAM_ID) {
  const [ownerPublicKey, signers] = getSigners(owner, multiSigners);
  const transaction = new Transaction().add(createApproveCheckedInstruction(account, mint, delegate, ownerPublicKey, amount, decimals, multiSigners, programId));
  return await sendAndConfirmTransaction(connection, transaction, [payer, ...signers], confirmOptions);
}

// node_modules/@solana/spl-token/lib/esm/actions/burn.js
init_index_browser_esm();

// node_modules/@solana/spl-token/lib/esm/instructions/burn.js
var import_buffer_layout34 = __toESM(require_Layout(), 1);
init_index_browser_esm();
var burnInstructionData = (0, import_buffer_layout34.struct)([(0, import_buffer_layout34.u8)("instruction"), u64("amount")]);
function createBurnInstruction(account, mint, owner, amount, multiSigners = [], programId = TOKEN_PROGRAM_ID) {
  const keys = addSigners([
    { pubkey: account, isSigner: false, isWritable: true },
    { pubkey: mint, isSigner: false, isWritable: true }
  ], owner, multiSigners);
  const data = Buffer.alloc(burnInstructionData.span);
  burnInstructionData.encode({
    instruction: TokenInstruction.Burn,
    amount: BigInt(amount)
  }, data);
  return new TransactionInstruction({ keys, programId, data });
}
function decodeBurnInstruction(instruction, programId = TOKEN_PROGRAM_ID) {
  if (!instruction.programId.equals(programId))
    throw new TokenInvalidInstructionProgramError();
  if (instruction.data.length !== burnInstructionData.span)
    throw new TokenInvalidInstructionDataError();
  const { keys: { account, mint, owner, multiSigners }, data } = decodeBurnInstructionUnchecked(instruction);
  if (data.instruction !== TokenInstruction.Burn)
    throw new TokenInvalidInstructionTypeError();
  if (!account || !mint || !owner)
    throw new TokenInvalidInstructionKeysError();
  return {
    programId,
    keys: {
      account,
      mint,
      owner,
      multiSigners
    },
    data
  };
}
function decodeBurnInstructionUnchecked({ programId, keys: [account, mint, owner, ...multiSigners], data }) {
  return {
    programId,
    keys: {
      account,
      mint,
      owner,
      multiSigners
    },
    data: burnInstructionData.decode(data)
  };
}

// node_modules/@solana/spl-token/lib/esm/actions/burn.js
async function burn(connection, payer, account, mint, owner, amount, multiSigners = [], confirmOptions, programId = TOKEN_PROGRAM_ID) {
  const [ownerPublicKey, signers] = getSigners(owner, multiSigners);
  const transaction = new Transaction().add(createBurnInstruction(account, mint, ownerPublicKey, amount, multiSigners, programId));
  return await sendAndConfirmTransaction(connection, transaction, [payer, ...signers], confirmOptions);
}

// node_modules/@solana/spl-token/lib/esm/actions/burnChecked.js
init_index_browser_esm();

// node_modules/@solana/spl-token/lib/esm/instructions/burnChecked.js
var import_buffer_layout35 = __toESM(require_Layout(), 1);
init_index_browser_esm();
var burnCheckedInstructionData = (0, import_buffer_layout35.struct)([
  (0, import_buffer_layout35.u8)("instruction"),
  u64("amount"),
  (0, import_buffer_layout35.u8)("decimals")
]);
function createBurnCheckedInstruction(account, mint, owner, amount, decimals, multiSigners = [], programId = TOKEN_PROGRAM_ID) {
  const keys = addSigners([
    { pubkey: account, isSigner: false, isWritable: true },
    { pubkey: mint, isSigner: false, isWritable: true }
  ], owner, multiSigners);
  const data = Buffer.alloc(burnCheckedInstructionData.span);
  burnCheckedInstructionData.encode({
    instruction: TokenInstruction.BurnChecked,
    amount: BigInt(amount),
    decimals
  }, data);
  return new TransactionInstruction({ keys, programId, data });
}
function decodeBurnCheckedInstruction(instruction, programId = TOKEN_PROGRAM_ID) {
  if (!instruction.programId.equals(programId))
    throw new TokenInvalidInstructionProgramError();
  if (instruction.data.length !== burnCheckedInstructionData.span)
    throw new TokenInvalidInstructionDataError();
  const { keys: { account, mint, owner, multiSigners }, data } = decodeBurnCheckedInstructionUnchecked(instruction);
  if (data.instruction !== TokenInstruction.BurnChecked)
    throw new TokenInvalidInstructionTypeError();
  if (!account || !mint || !owner)
    throw new TokenInvalidInstructionKeysError();
  return {
    programId,
    keys: {
      account,
      mint,
      owner,
      multiSigners
    },
    data
  };
}
function decodeBurnCheckedInstructionUnchecked({ programId, keys: [account, mint, owner, ...multiSigners], data }) {
  return {
    programId,
    keys: {
      account,
      mint,
      owner,
      multiSigners
    },
    data: burnCheckedInstructionData.decode(data)
  };
}

// node_modules/@solana/spl-token/lib/esm/actions/burnChecked.js
async function burnChecked(connection, payer, account, mint, owner, amount, decimals, multiSigners = [], confirmOptions, programId = TOKEN_PROGRAM_ID) {
  const [ownerPublicKey, signers] = getSigners(owner, multiSigners);
  const transaction = new Transaction().add(createBurnCheckedInstruction(account, mint, ownerPublicKey, amount, decimals, multiSigners, programId));
  return await sendAndConfirmTransaction(connection, transaction, [payer, ...signers], confirmOptions);
}

// node_modules/@solana/spl-token/lib/esm/actions/closeAccount.js
init_index_browser_esm();

// node_modules/@solana/spl-token/lib/esm/instructions/closeAccount.js
var import_buffer_layout36 = __toESM(require_Layout(), 1);
init_index_browser_esm();
var closeAccountInstructionData = (0, import_buffer_layout36.struct)([(0, import_buffer_layout36.u8)("instruction")]);
function createCloseAccountInstruction(account, destination, authority, multiSigners = [], programId = TOKEN_PROGRAM_ID) {
  const keys = addSigners([
    { pubkey: account, isSigner: false, isWritable: true },
    { pubkey: destination, isSigner: false, isWritable: true }
  ], authority, multiSigners);
  const data = Buffer.alloc(closeAccountInstructionData.span);
  closeAccountInstructionData.encode({ instruction: TokenInstruction.CloseAccount }, data);
  return new TransactionInstruction({ keys, programId, data });
}
function decodeCloseAccountInstruction(instruction, programId = TOKEN_PROGRAM_ID) {
  if (!instruction.programId.equals(programId))
    throw new TokenInvalidInstructionProgramError();
  if (instruction.data.length !== closeAccountInstructionData.span)
    throw new TokenInvalidInstructionDataError();
  const { keys: { account, destination, authority, multiSigners }, data } = decodeCloseAccountInstructionUnchecked(instruction);
  if (data.instruction !== TokenInstruction.CloseAccount)
    throw new TokenInvalidInstructionTypeError();
  if (!account || !destination || !authority)
    throw new TokenInvalidInstructionKeysError();
  return {
    programId,
    keys: {
      account,
      destination,
      authority,
      multiSigners
    },
    data
  };
}
function decodeCloseAccountInstructionUnchecked({ programId, keys: [account, destination, authority, ...multiSigners], data }) {
  return {
    programId,
    keys: {
      account,
      destination,
      authority,
      multiSigners
    },
    data: closeAccountInstructionData.decode(data)
  };
}

// node_modules/@solana/spl-token/lib/esm/actions/closeAccount.js
async function closeAccount(connection, payer, account, destination, authority, multiSigners = [], confirmOptions, programId = TOKEN_PROGRAM_ID) {
  const [authorityPublicKey, signers] = getSigners(authority, multiSigners);
  const transaction = new Transaction().add(createCloseAccountInstruction(account, destination, authorityPublicKey, multiSigners, programId));
  return await sendAndConfirmTransaction(connection, transaction, [payer, ...signers], confirmOptions);
}

// node_modules/@solana/spl-token/lib/esm/actions/createAccount.js
init_index_browser_esm();

// node_modules/@solana/spl-token/lib/esm/instructions/initializeAccount.js
var import_buffer_layout37 = __toESM(require_Layout(), 1);
init_index_browser_esm();
var initializeAccountInstructionData = (0, import_buffer_layout37.struct)([(0, import_buffer_layout37.u8)("instruction")]);
function createInitializeAccountInstruction(account, mint, owner, programId = TOKEN_PROGRAM_ID) {
  const keys = [
    { pubkey: account, isSigner: false, isWritable: true },
    { pubkey: mint, isSigner: false, isWritable: false },
    { pubkey: owner, isSigner: false, isWritable: false },
    { pubkey: SYSVAR_RENT_PUBKEY, isSigner: false, isWritable: false }
  ];
  const data = Buffer.alloc(initializeAccountInstructionData.span);
  initializeAccountInstructionData.encode({ instruction: TokenInstruction.InitializeAccount }, data);
  return new TransactionInstruction({ keys, programId, data });
}
function decodeInitializeAccountInstruction(instruction, programId = TOKEN_PROGRAM_ID) {
  if (!instruction.programId.equals(programId))
    throw new TokenInvalidInstructionProgramError();
  if (instruction.data.length !== initializeAccountInstructionData.span)
    throw new TokenInvalidInstructionDataError();
  const { keys: { account, mint, owner, rent }, data } = decodeInitializeAccountInstructionUnchecked(instruction);
  if (data.instruction !== TokenInstruction.InitializeAccount)
    throw new TokenInvalidInstructionTypeError();
  if (!account || !mint || !owner || !rent)
    throw new TokenInvalidInstructionKeysError();
  return {
    programId,
    keys: {
      account,
      mint,
      owner,
      rent
    },
    data
  };
}
function decodeInitializeAccountInstructionUnchecked({ programId, keys: [account, mint, owner, rent], data }) {
  return {
    programId,
    keys: {
      account,
      mint,
      owner,
      rent
    },
    data: initializeAccountInstructionData.decode(data)
  };
}

// node_modules/@solana/spl-token/lib/esm/actions/createAssociatedTokenAccount.js
init_index_browser_esm();

// node_modules/@solana/spl-token/lib/esm/instructions/associatedTokenAccount.js
init_index_browser_esm();
function createAssociatedTokenAccountInstruction(payer, associatedToken, owner, mint, programId = TOKEN_PROGRAM_ID, associatedTokenProgramId = ASSOCIATED_TOKEN_PROGRAM_ID) {
  return buildAssociatedTokenAccountInstruction(payer, associatedToken, owner, mint, Buffer.alloc(0), programId, associatedTokenProgramId);
}
function createAssociatedTokenAccountIdempotentInstruction(payer, associatedToken, owner, mint, programId = TOKEN_PROGRAM_ID, associatedTokenProgramId = ASSOCIATED_TOKEN_PROGRAM_ID) {
  return buildAssociatedTokenAccountInstruction(payer, associatedToken, owner, mint, Buffer.from([1]), programId, associatedTokenProgramId);
}
function createAssociatedTokenAccountIdempotentInstructionWithDerivation(payer, owner, mint, allowOwnerOffCurve = true, programId = TOKEN_PROGRAM_ID, associatedTokenProgramId = ASSOCIATED_TOKEN_PROGRAM_ID) {
  const associatedToken = getAssociatedTokenAddressSync(mint, owner, allowOwnerOffCurve);
  return createAssociatedTokenAccountIdempotentInstruction(payer, associatedToken, owner, mint, programId, associatedTokenProgramId);
}
function buildAssociatedTokenAccountInstruction(payer, associatedToken, owner, mint, instructionData, programId = TOKEN_PROGRAM_ID, associatedTokenProgramId = ASSOCIATED_TOKEN_PROGRAM_ID) {
  const keys = [
    { pubkey: payer, isSigner: true, isWritable: true },
    { pubkey: associatedToken, isSigner: false, isWritable: true },
    { pubkey: owner, isSigner: false, isWritable: false },
    { pubkey: mint, isSigner: false, isWritable: false },
    { pubkey: SystemProgram.programId, isSigner: false, isWritable: false },
    { pubkey: programId, isSigner: false, isWritable: false }
  ];
  return new TransactionInstruction({
    keys,
    programId: associatedTokenProgramId,
    data: instructionData
  });
}
function createRecoverNestedInstruction(nestedAssociatedToken, nestedMint, destinationAssociatedToken, ownerAssociatedToken, ownerMint, owner, programId = TOKEN_PROGRAM_ID, associatedTokenProgramId = ASSOCIATED_TOKEN_PROGRAM_ID) {
  const keys = [
    { pubkey: nestedAssociatedToken, isSigner: false, isWritable: true },
    { pubkey: nestedMint, isSigner: false, isWritable: false },
    { pubkey: destinationAssociatedToken, isSigner: false, isWritable: true },
    { pubkey: ownerAssociatedToken, isSigner: false, isWritable: true },
    { pubkey: ownerMint, isSigner: false, isWritable: false },
    { pubkey: owner, isSigner: true, isWritable: true },
    { pubkey: programId, isSigner: false, isWritable: false }
  ];
  return new TransactionInstruction({
    keys,
    programId: associatedTokenProgramId,
    data: Buffer.from([2])
  });
}

// node_modules/@solana/spl-token/lib/esm/actions/createAssociatedTokenAccount.js
async function createAssociatedTokenAccount(connection, payer, mint, owner, confirmOptions, programId = TOKEN_PROGRAM_ID, associatedTokenProgramId = ASSOCIATED_TOKEN_PROGRAM_ID, allowOwnerOffCurve = false) {
  const associatedToken = getAssociatedTokenAddressSync(mint, owner, allowOwnerOffCurve, programId, associatedTokenProgramId);
  const transaction = new Transaction().add(createAssociatedTokenAccountInstruction(payer.publicKey, associatedToken, owner, mint, programId, associatedTokenProgramId));
  await sendAndConfirmTransaction(connection, transaction, [payer], confirmOptions);
  return associatedToken;
}

// node_modules/@solana/spl-token/lib/esm/actions/createAccount.js
async function createAccount(connection, payer, mint, owner, keypair, confirmOptions, programId = TOKEN_PROGRAM_ID) {
  if (!keypair)
    return await createAssociatedTokenAccount(connection, payer, mint, owner, confirmOptions, programId);
  const mintState = await getMint(connection, mint, confirmOptions == null ? void 0 : confirmOptions.commitment, programId);
  const space = getAccountLenForMint(mintState);
  const lamports = await connection.getMinimumBalanceForRentExemption(space);
  const transaction = new Transaction().add(SystemProgram.createAccount({
    fromPubkey: payer.publicKey,
    newAccountPubkey: keypair.publicKey,
    space,
    lamports,
    programId
  }), createInitializeAccountInstruction(keypair.publicKey, mint, owner, programId));
  await sendAndConfirmTransaction(connection, transaction, [payer, keypair], confirmOptions);
  return keypair.publicKey;
}

// node_modules/@solana/spl-token/lib/esm/actions/createAssociatedTokenAccountIdempotent.js
init_index_browser_esm();
async function createAssociatedTokenAccountIdempotent(connection, payer, mint, owner, confirmOptions, programId = TOKEN_PROGRAM_ID, associatedTokenProgramId = ASSOCIATED_TOKEN_PROGRAM_ID, allowOwnerOffCurve = false) {
  const associatedToken = getAssociatedTokenAddressSync(mint, owner, allowOwnerOffCurve, programId, associatedTokenProgramId);
  const transaction = new Transaction().add(createAssociatedTokenAccountIdempotentInstruction(payer.publicKey, associatedToken, owner, mint, programId, associatedTokenProgramId));
  await sendAndConfirmTransaction(connection, transaction, [payer], confirmOptions);
  return associatedToken;
}

// node_modules/@solana/spl-token/lib/esm/actions/createMint.js
init_index_browser_esm();

// node_modules/@solana/spl-token/lib/esm/instructions/initializeMint2.js
var import_buffer_layout38 = __toESM(require_Layout(), 1);
init_index_browser_esm();
var initializeMint2InstructionData = (0, import_buffer_layout38.struct)([
  (0, import_buffer_layout38.u8)("instruction"),
  (0, import_buffer_layout38.u8)("decimals"),
  publicKey("mintAuthority"),
  new COptionPublicKeyLayout("freezeAuthority")
]);
function createInitializeMint2Instruction(mint, decimals, mintAuthority, freezeAuthority, programId = TOKEN_PROGRAM_ID) {
  const keys = [{ pubkey: mint, isSigner: false, isWritable: true }];
  const data = Buffer.alloc(67);
  initializeMint2InstructionData.encode({
    instruction: TokenInstruction.InitializeMint2,
    decimals,
    mintAuthority,
    freezeAuthority
  }, data);
  return new TransactionInstruction({
    keys,
    programId,
    data: data.subarray(0, initializeMint2InstructionData.getSpan(data))
  });
}
function decodeInitializeMint2Instruction(instruction, programId = TOKEN_PROGRAM_ID) {
  if (!instruction.programId.equals(programId))
    throw new TokenInvalidInstructionProgramError();
  if (instruction.data.length !== initializeMint2InstructionData.getSpan(instruction.data))
    throw new TokenInvalidInstructionDataError();
  const { keys: { mint }, data } = decodeInitializeMint2InstructionUnchecked(instruction);
  if (data.instruction !== TokenInstruction.InitializeMint2)
    throw new TokenInvalidInstructionTypeError();
  if (!mint)
    throw new TokenInvalidInstructionKeysError();
  return {
    programId,
    keys: {
      mint
    },
    data
  };
}
function decodeInitializeMint2InstructionUnchecked({ programId, keys: [mint], data }) {
  const { instruction, decimals, mintAuthority, freezeAuthority } = initializeMint2InstructionData.decode(data);
  return {
    programId,
    keys: {
      mint
    },
    data: {
      instruction,
      decimals,
      mintAuthority,
      freezeAuthority
    }
  };
}

// node_modules/@solana/spl-token/lib/esm/actions/createMint.js
async function createMint(connection, payer, mintAuthority, freezeAuthority, decimals, keypair = Keypair.generate(), confirmOptions, programId = TOKEN_PROGRAM_ID) {
  const lamports = await getMinimumBalanceForRentExemptMint(connection);
  const transaction = new Transaction().add(SystemProgram.createAccount({
    fromPubkey: payer.publicKey,
    newAccountPubkey: keypair.publicKey,
    space: MINT_SIZE,
    lamports,
    programId
  }), createInitializeMint2Instruction(keypair.publicKey, decimals, mintAuthority, freezeAuthority, programId));
  await sendAndConfirmTransaction(connection, transaction, [payer, keypair], confirmOptions);
  return keypair.publicKey;
}

// node_modules/@solana/spl-token/lib/esm/actions/createMultisig.js
init_index_browser_esm();

// node_modules/@solana/spl-token/lib/esm/instructions/initializeMultisig.js
var import_buffer_layout39 = __toESM(require_Layout(), 1);
init_index_browser_esm();
var initializeMultisigInstructionData = (0, import_buffer_layout39.struct)([
  (0, import_buffer_layout39.u8)("instruction"),
  (0, import_buffer_layout39.u8)("m")
]);
function createInitializeMultisigInstruction(account, signers, m, programId = TOKEN_PROGRAM_ID) {
  const keys = [
    { pubkey: account, isSigner: false, isWritable: true },
    { pubkey: SYSVAR_RENT_PUBKEY, isSigner: false, isWritable: false }
  ];
  for (const signer of signers) {
    keys.push({
      pubkey: signer instanceof PublicKey ? signer : signer.publicKey,
      isSigner: false,
      isWritable: false
    });
  }
  const data = Buffer.alloc(initializeMultisigInstructionData.span);
  initializeMultisigInstructionData.encode({
    instruction: TokenInstruction.InitializeMultisig,
    m
  }, data);
  return new TransactionInstruction({ keys, programId, data });
}
function decodeInitializeMultisigInstruction(instruction, programId = TOKEN_PROGRAM_ID) {
  if (!instruction.programId.equals(programId))
    throw new TokenInvalidInstructionProgramError();
  if (instruction.data.length !== initializeMultisigInstructionData.span)
    throw new TokenInvalidInstructionDataError();
  const { keys: { account, rent, signers }, data } = decodeInitializeMultisigInstructionUnchecked(instruction);
  if (data.instruction !== TokenInstruction.InitializeMultisig)
    throw new TokenInvalidInstructionTypeError();
  if (!account || !rent || !signers.length)
    throw new TokenInvalidInstructionKeysError();
  return {
    programId,
    keys: {
      account,
      rent,
      signers
    },
    data
  };
}
function decodeInitializeMultisigInstructionUnchecked({ programId, keys: [account, rent, ...signers], data }) {
  return {
    programId,
    keys: {
      account,
      rent,
      signers
    },
    data: initializeMultisigInstructionData.decode(data)
  };
}

// node_modules/@solana/spl-token/lib/esm/actions/createMultisig.js
async function createMultisig(connection, payer, signers, m, keypair = Keypair.generate(), confirmOptions, programId = TOKEN_PROGRAM_ID) {
  const lamports = await getMinimumBalanceForRentExemptMultisig(connection);
  const transaction = new Transaction().add(SystemProgram.createAccount({
    fromPubkey: payer.publicKey,
    newAccountPubkey: keypair.publicKey,
    space: MULTISIG_SIZE,
    lamports,
    programId
  }), createInitializeMultisigInstruction(keypair.publicKey, signers, m, programId));
  await sendAndConfirmTransaction(connection, transaction, [payer, keypair], confirmOptions);
  return keypair.publicKey;
}

// node_modules/@solana/spl-token/lib/esm/actions/createNativeMint.js
init_index_browser_esm();

// node_modules/@solana/spl-token/lib/esm/instructions/createNativeMint.js
var import_buffer_layout40 = __toESM(require_Layout(), 1);
init_index_browser_esm();
var createNativeMintInstructionData = (0, import_buffer_layout40.struct)([(0, import_buffer_layout40.u8)("instruction")]);
function createCreateNativeMintInstruction(payer, nativeMintId = NATIVE_MINT_2022, programId = TOKEN_2022_PROGRAM_ID) {
  if (!programSupportsExtensions(programId)) {
    throw new TokenUnsupportedInstructionError();
  }
  const keys = [
    { pubkey: payer, isSigner: true, isWritable: true },
    { pubkey: nativeMintId, isSigner: false, isWritable: true },
    { pubkey: SystemProgram.programId, isSigner: false, isWritable: false }
  ];
  const data = Buffer.alloc(createNativeMintInstructionData.span);
  createNativeMintInstructionData.encode({ instruction: TokenInstruction.CreateNativeMint }, data);
  return new TransactionInstruction({ keys, programId, data });
}

// node_modules/@solana/spl-token/lib/esm/actions/createNativeMint.js
async function createNativeMint(connection, payer, confirmOptions, nativeMint = NATIVE_MINT_2022, programId = TOKEN_2022_PROGRAM_ID) {
  const transaction = new Transaction().add(createCreateNativeMintInstruction(payer.publicKey, nativeMint, programId));
  await sendAndConfirmTransaction(connection, transaction, [payer], confirmOptions);
}

// node_modules/@solana/spl-token/lib/esm/actions/createWrappedNativeAccount.js
init_index_browser_esm();

// node_modules/@solana/spl-token/lib/esm/instructions/syncNative.js
var import_buffer_layout41 = __toESM(require_Layout(), 1);
init_index_browser_esm();
var syncNativeInstructionData = (0, import_buffer_layout41.struct)([(0, import_buffer_layout41.u8)("instruction")]);
function createSyncNativeInstruction(account, programId = TOKEN_PROGRAM_ID) {
  const keys = [{ pubkey: account, isSigner: false, isWritable: true }];
  const data = Buffer.alloc(syncNativeInstructionData.span);
  syncNativeInstructionData.encode({ instruction: TokenInstruction.SyncNative }, data);
  return new TransactionInstruction({ keys, programId, data });
}
function decodeSyncNativeInstruction(instruction, programId = TOKEN_PROGRAM_ID) {
  if (!instruction.programId.equals(programId))
    throw new TokenInvalidInstructionProgramError();
  if (instruction.data.length !== syncNativeInstructionData.span)
    throw new TokenInvalidInstructionDataError();
  const { keys: { account }, data } = decodeSyncNativeInstructionUnchecked(instruction);
  if (data.instruction !== TokenInstruction.SyncNative)
    throw new TokenInvalidInstructionTypeError();
  if (!account)
    throw new TokenInvalidInstructionKeysError();
  return {
    programId,
    keys: {
      account
    },
    data
  };
}
function decodeSyncNativeInstructionUnchecked({ programId, keys: [account], data }) {
  return {
    programId,
    keys: {
      account
    },
    data: syncNativeInstructionData.decode(data)
  };
}

// node_modules/@solana/spl-token/lib/esm/actions/createWrappedNativeAccount.js
async function createWrappedNativeAccount(connection, payer, owner, amount, keypair, confirmOptions, programId = TOKEN_PROGRAM_ID, nativeMint = NATIVE_MINT) {
  if (!amount)
    return await createAccount(connection, payer, nativeMint, owner, keypair, confirmOptions, programId);
  if (!keypair) {
    const associatedToken = getAssociatedTokenAddressSync(nativeMint, owner, false, programId, ASSOCIATED_TOKEN_PROGRAM_ID);
    const transaction2 = new Transaction().add(createAssociatedTokenAccountInstruction(payer.publicKey, associatedToken, owner, nativeMint, programId, ASSOCIATED_TOKEN_PROGRAM_ID), SystemProgram.transfer({
      fromPubkey: payer.publicKey,
      toPubkey: associatedToken,
      lamports: amount
    }), createSyncNativeInstruction(associatedToken, programId));
    await sendAndConfirmTransaction(connection, transaction2, [payer], confirmOptions);
    return associatedToken;
  }
  const lamports = await getMinimumBalanceForRentExemptAccount(connection);
  const transaction = new Transaction().add(SystemProgram.createAccount({
    fromPubkey: payer.publicKey,
    newAccountPubkey: keypair.publicKey,
    space: ACCOUNT_SIZE,
    lamports,
    programId
  }), SystemProgram.transfer({
    fromPubkey: payer.publicKey,
    toPubkey: keypair.publicKey,
    lamports: amount
  }), createInitializeAccountInstruction(keypair.publicKey, nativeMint, owner, programId));
  await sendAndConfirmTransaction(connection, transaction, [payer, keypair], confirmOptions);
  return keypair.publicKey;
}

// node_modules/@solana/spl-token/lib/esm/actions/freezeAccount.js
init_index_browser_esm();

// node_modules/@solana/spl-token/lib/esm/instructions/freezeAccount.js
var import_buffer_layout42 = __toESM(require_Layout(), 1);
init_index_browser_esm();
var freezeAccountInstructionData = (0, import_buffer_layout42.struct)([(0, import_buffer_layout42.u8)("instruction")]);
function createFreezeAccountInstruction(account, mint, authority, multiSigners = [], programId = TOKEN_PROGRAM_ID) {
  const keys = addSigners([
    { pubkey: account, isSigner: false, isWritable: true },
    { pubkey: mint, isSigner: false, isWritable: false }
  ], authority, multiSigners);
  const data = Buffer.alloc(freezeAccountInstructionData.span);
  freezeAccountInstructionData.encode({ instruction: TokenInstruction.FreezeAccount }, data);
  return new TransactionInstruction({ keys, programId, data });
}
function decodeFreezeAccountInstruction(instruction, programId = TOKEN_PROGRAM_ID) {
  if (!instruction.programId.equals(programId))
    throw new TokenInvalidInstructionProgramError();
  if (instruction.data.length !== freezeAccountInstructionData.span)
    throw new TokenInvalidInstructionDataError();
  const { keys: { account, mint, authority, multiSigners }, data } = decodeFreezeAccountInstructionUnchecked(instruction);
  if (data.instruction !== TokenInstruction.FreezeAccount)
    throw new TokenInvalidInstructionTypeError();
  if (!account || !mint || !authority)
    throw new TokenInvalidInstructionKeysError();
  return {
    programId,
    keys: {
      account,
      mint,
      authority,
      multiSigners
    },
    data
  };
}
function decodeFreezeAccountInstructionUnchecked({ programId, keys: [account, mint, authority, ...multiSigners], data }) {
  return {
    programId,
    keys: {
      account,
      mint,
      authority,
      multiSigners
    },
    data: freezeAccountInstructionData.decode(data)
  };
}

// node_modules/@solana/spl-token/lib/esm/actions/freezeAccount.js
async function freezeAccount(connection, payer, account, mint, authority, multiSigners = [], confirmOptions, programId = TOKEN_PROGRAM_ID) {
  const [authorityPublicKey, signers] = getSigners(authority, multiSigners);
  const transaction = new Transaction().add(createFreezeAccountInstruction(account, mint, authorityPublicKey, multiSigners, programId));
  return await sendAndConfirmTransaction(connection, transaction, [payer, ...signers], confirmOptions);
}

// node_modules/@solana/spl-token/lib/esm/actions/getOrCreateAssociatedTokenAccount.js
init_index_browser_esm();
async function getOrCreateAssociatedTokenAccount(connection, payer, mint, owner, allowOwnerOffCurve = false, commitment, confirmOptions, programId = TOKEN_PROGRAM_ID, associatedTokenProgramId = ASSOCIATED_TOKEN_PROGRAM_ID) {
  const associatedToken = getAssociatedTokenAddressSync(mint, owner, allowOwnerOffCurve, programId, associatedTokenProgramId);
  let account;
  try {
    account = await getAccount(connection, associatedToken, commitment, programId);
  } catch (error) {
    if (error instanceof TokenAccountNotFoundError || error instanceof TokenInvalidAccountOwnerError) {
      try {
        const transaction = new Transaction().add(createAssociatedTokenAccountInstruction(payer.publicKey, associatedToken, owner, mint, programId, associatedTokenProgramId));
        await sendAndConfirmTransaction(connection, transaction, [payer], confirmOptions);
      } catch (error2) {
      }
      account = await getAccount(connection, associatedToken, commitment, programId);
    } else {
      throw error;
    }
  }
  if (!account.mint.equals(mint))
    throw new TokenInvalidMintError();
  if (!account.owner.equals(owner))
    throw new TokenInvalidOwnerError();
  return account;
}

// node_modules/@solana/spl-token/lib/esm/actions/mintTo.js
init_index_browser_esm();

// node_modules/@solana/spl-token/lib/esm/instructions/mintTo.js
var import_buffer_layout43 = __toESM(require_Layout(), 1);
init_index_browser_esm();
var mintToInstructionData = (0, import_buffer_layout43.struct)([(0, import_buffer_layout43.u8)("instruction"), u64("amount")]);
function createMintToInstruction(mint, destination, authority, amount, multiSigners = [], programId = TOKEN_PROGRAM_ID) {
  const keys = addSigners([
    { pubkey: mint, isSigner: false, isWritable: true },
    { pubkey: destination, isSigner: false, isWritable: true }
  ], authority, multiSigners);
  const data = Buffer.alloc(mintToInstructionData.span);
  mintToInstructionData.encode({
    instruction: TokenInstruction.MintTo,
    amount: BigInt(amount)
  }, data);
  return new TransactionInstruction({ keys, programId, data });
}
function decodeMintToInstruction(instruction, programId = TOKEN_PROGRAM_ID) {
  if (!instruction.programId.equals(programId))
    throw new TokenInvalidInstructionProgramError();
  if (instruction.data.length !== mintToInstructionData.span)
    throw new TokenInvalidInstructionDataError();
  const { keys: { mint, destination, authority, multiSigners }, data } = decodeMintToInstructionUnchecked(instruction);
  if (data.instruction !== TokenInstruction.MintTo)
    throw new TokenInvalidInstructionTypeError();
  if (!mint || !destination || !authority)
    throw new TokenInvalidInstructionKeysError();
  return {
    programId,
    keys: {
      mint,
      destination,
      authority,
      multiSigners
    },
    data
  };
}
function decodeMintToInstructionUnchecked({ programId, keys: [mint, destination, authority, ...multiSigners], data }) {
  return {
    programId,
    keys: {
      mint,
      destination,
      authority,
      multiSigners
    },
    data: mintToInstructionData.decode(data)
  };
}

// node_modules/@solana/spl-token/lib/esm/actions/mintTo.js
async function mintTo(connection, payer, mint, destination, authority, amount, multiSigners = [], confirmOptions, programId = TOKEN_PROGRAM_ID) {
  const [authorityPublicKey, signers] = getSigners(authority, multiSigners);
  const transaction = new Transaction().add(createMintToInstruction(mint, destination, authorityPublicKey, amount, multiSigners, programId));
  return await sendAndConfirmTransaction(connection, transaction, [payer, ...signers], confirmOptions);
}

// node_modules/@solana/spl-token/lib/esm/actions/mintToChecked.js
init_index_browser_esm();

// node_modules/@solana/spl-token/lib/esm/instructions/mintToChecked.js
var import_buffer_layout44 = __toESM(require_Layout(), 1);
init_index_browser_esm();
var mintToCheckedInstructionData = (0, import_buffer_layout44.struct)([
  (0, import_buffer_layout44.u8)("instruction"),
  u64("amount"),
  (0, import_buffer_layout44.u8)("decimals")
]);
function createMintToCheckedInstruction(mint, destination, authority, amount, decimals, multiSigners = [], programId = TOKEN_PROGRAM_ID) {
  const keys = addSigners([
    { pubkey: mint, isSigner: false, isWritable: true },
    { pubkey: destination, isSigner: false, isWritable: true }
  ], authority, multiSigners);
  const data = Buffer.alloc(mintToCheckedInstructionData.span);
  mintToCheckedInstructionData.encode({
    instruction: TokenInstruction.MintToChecked,
    amount: BigInt(amount),
    decimals
  }, data);
  return new TransactionInstruction({ keys, programId, data });
}
function decodeMintToCheckedInstruction(instruction, programId = TOKEN_PROGRAM_ID) {
  if (!instruction.programId.equals(programId))
    throw new TokenInvalidInstructionProgramError();
  if (instruction.data.length !== mintToCheckedInstructionData.span)
    throw new TokenInvalidInstructionDataError();
  const { keys: { mint, destination, authority, multiSigners }, data } = decodeMintToCheckedInstructionUnchecked(instruction);
  if (data.instruction !== TokenInstruction.MintToChecked)
    throw new TokenInvalidInstructionTypeError();
  if (!mint || !destination || !authority)
    throw new TokenInvalidInstructionKeysError();
  return {
    programId,
    keys: {
      mint,
      destination,
      authority,
      multiSigners
    },
    data
  };
}
function decodeMintToCheckedInstructionUnchecked({ programId, keys: [mint, destination, authority, ...multiSigners], data }) {
  return {
    programId,
    keys: {
      mint,
      destination,
      authority,
      multiSigners
    },
    data: mintToCheckedInstructionData.decode(data)
  };
}

// node_modules/@solana/spl-token/lib/esm/actions/mintToChecked.js
async function mintToChecked(connection, payer, mint, destination, authority, amount, decimals, multiSigners = [], confirmOptions, programId = TOKEN_PROGRAM_ID) {
  const [authorityPublicKey, signers] = getSigners(authority, multiSigners);
  const transaction = new Transaction().add(createMintToCheckedInstruction(mint, destination, authorityPublicKey, amount, decimals, multiSigners, programId));
  return await sendAndConfirmTransaction(connection, transaction, [payer, ...signers], confirmOptions);
}

// node_modules/@solana/spl-token/lib/esm/actions/recoverNested.js
init_index_browser_esm();
async function recoverNested(connection, payer, owner, mint, nestedMint, confirmOptions, programId = TOKEN_PROGRAM_ID, associatedTokenProgramId = ASSOCIATED_TOKEN_PROGRAM_ID) {
  const ownerAssociatedToken = getAssociatedTokenAddressSync(mint, owner.publicKey, false, programId, associatedTokenProgramId);
  const destinationAssociatedToken = getAssociatedTokenAddressSync(nestedMint, owner.publicKey, false, programId, associatedTokenProgramId);
  const nestedAssociatedToken = getAssociatedTokenAddressSync(nestedMint, ownerAssociatedToken, true, programId, associatedTokenProgramId);
  const transaction = new Transaction().add(createRecoverNestedInstruction(nestedAssociatedToken, nestedMint, destinationAssociatedToken, ownerAssociatedToken, mint, owner.publicKey, programId, associatedTokenProgramId));
  return await sendAndConfirmTransaction(connection, transaction, [payer, owner], confirmOptions);
}

// node_modules/@solana/spl-token/lib/esm/actions/revoke.js
init_index_browser_esm();

// node_modules/@solana/spl-token/lib/esm/instructions/revoke.js
var import_buffer_layout45 = __toESM(require_Layout(), 1);
init_index_browser_esm();
var revokeInstructionData = (0, import_buffer_layout45.struct)([(0, import_buffer_layout45.u8)("instruction")]);
function createRevokeInstruction(account, owner, multiSigners = [], programId = TOKEN_PROGRAM_ID) {
  const keys = addSigners([{ pubkey: account, isSigner: false, isWritable: true }], owner, multiSigners);
  const data = Buffer.alloc(revokeInstructionData.span);
  revokeInstructionData.encode({ instruction: TokenInstruction.Revoke }, data);
  return new TransactionInstruction({ keys, programId, data });
}
function decodeRevokeInstruction(instruction, programId = TOKEN_PROGRAM_ID) {
  if (!instruction.programId.equals(programId))
    throw new TokenInvalidInstructionProgramError();
  if (instruction.data.length !== revokeInstructionData.span)
    throw new TokenInvalidInstructionDataError();
  const { keys: { account, owner, multiSigners }, data } = decodeRevokeInstructionUnchecked(instruction);
  if (data.instruction !== TokenInstruction.Revoke)
    throw new TokenInvalidInstructionTypeError();
  if (!account || !owner)
    throw new TokenInvalidInstructionKeysError();
  return {
    programId,
    keys: {
      account,
      owner,
      multiSigners
    },
    data
  };
}
function decodeRevokeInstructionUnchecked({ programId, keys: [account, owner, ...multiSigners], data }) {
  return {
    programId,
    keys: {
      account,
      owner,
      multiSigners
    },
    data: revokeInstructionData.decode(data)
  };
}

// node_modules/@solana/spl-token/lib/esm/actions/revoke.js
async function revoke(connection, payer, account, owner, multiSigners = [], confirmOptions, programId = TOKEN_PROGRAM_ID) {
  const [ownerPublicKey, signers] = getSigners(owner, multiSigners);
  const transaction = new Transaction().add(createRevokeInstruction(account, ownerPublicKey, multiSigners, programId));
  return await sendAndConfirmTransaction(connection, transaction, [payer, ...signers], confirmOptions);
}

// node_modules/@solana/spl-token/lib/esm/actions/setAuthority.js
init_index_browser_esm();

// node_modules/@solana/spl-token/lib/esm/instructions/setAuthority.js
var import_buffer_layout46 = __toESM(require_Layout(), 1);
init_index_browser_esm();
var AuthorityType;
(function(AuthorityType2) {
  AuthorityType2[AuthorityType2["MintTokens"] = 0] = "MintTokens";
  AuthorityType2[AuthorityType2["FreezeAccount"] = 1] = "FreezeAccount";
  AuthorityType2[AuthorityType2["AccountOwner"] = 2] = "AccountOwner";
  AuthorityType2[AuthorityType2["CloseAccount"] = 3] = "CloseAccount";
  AuthorityType2[AuthorityType2["TransferFeeConfig"] = 4] = "TransferFeeConfig";
  AuthorityType2[AuthorityType2["WithheldWithdraw"] = 5] = "WithheldWithdraw";
  AuthorityType2[AuthorityType2["CloseMint"] = 6] = "CloseMint";
  AuthorityType2[AuthorityType2["InterestRate"] = 7] = "InterestRate";
  AuthorityType2[AuthorityType2["PermanentDelegate"] = 8] = "PermanentDelegate";
  AuthorityType2[AuthorityType2["ConfidentialTransferMint"] = 9] = "ConfidentialTransferMint";
  AuthorityType2[AuthorityType2["TransferHookProgramId"] = 10] = "TransferHookProgramId";
  AuthorityType2[AuthorityType2["ConfidentialTransferFeeConfig"] = 11] = "ConfidentialTransferFeeConfig";
  AuthorityType2[AuthorityType2["MetadataPointer"] = 12] = "MetadataPointer";
  AuthorityType2[AuthorityType2["GroupPointer"] = 13] = "GroupPointer";
  AuthorityType2[AuthorityType2["GroupMemberPointer"] = 14] = "GroupMemberPointer";
  AuthorityType2[AuthorityType2["ScaledUiAmountConfig"] = 15] = "ScaledUiAmountConfig";
  AuthorityType2[AuthorityType2["PausableConfig"] = 16] = "PausableConfig";
})(AuthorityType || (AuthorityType = {}));
var setAuthorityInstructionData = (0, import_buffer_layout46.struct)([
  (0, import_buffer_layout46.u8)("instruction"),
  (0, import_buffer_layout46.u8)("authorityType"),
  new COptionPublicKeyLayout("newAuthority")
]);
function createSetAuthorityInstruction(account, currentAuthority, authorityType, newAuthority, multiSigners = [], programId = TOKEN_PROGRAM_ID) {
  const keys = addSigners([{ pubkey: account, isSigner: false, isWritable: true }], currentAuthority, multiSigners);
  const data = Buffer.alloc(35);
  setAuthorityInstructionData.encode({
    instruction: TokenInstruction.SetAuthority,
    authorityType,
    newAuthority
  }, data);
  return new TransactionInstruction({
    keys,
    programId,
    data: data.subarray(0, setAuthorityInstructionData.getSpan(data))
  });
}
function decodeSetAuthorityInstruction(instruction, programId = TOKEN_PROGRAM_ID) {
  if (!instruction.programId.equals(programId))
    throw new TokenInvalidInstructionProgramError();
  if (instruction.data.length !== setAuthorityInstructionData.getSpan(instruction.data))
    throw new TokenInvalidInstructionDataError();
  const { keys: { account, currentAuthority, multiSigners }, data } = decodeSetAuthorityInstructionUnchecked(instruction);
  if (data.instruction !== TokenInstruction.SetAuthority)
    throw new TokenInvalidInstructionTypeError();
  if (!account || !currentAuthority)
    throw new TokenInvalidInstructionKeysError();
  return {
    programId,
    keys: {
      account,
      currentAuthority,
      multiSigners
    },
    data
  };
}
function decodeSetAuthorityInstructionUnchecked({ programId, keys: [account, currentAuthority, ...multiSigners], data }) {
  const { instruction, authorityType, newAuthority } = setAuthorityInstructionData.decode(data);
  return {
    programId,
    keys: {
      account,
      currentAuthority,
      multiSigners
    },
    data: {
      instruction,
      authorityType,
      newAuthority
    }
  };
}

// node_modules/@solana/spl-token/lib/esm/actions/setAuthority.js
async function setAuthority(connection, payer, account, currentAuthority, authorityType, newAuthority, multiSigners = [], confirmOptions, programId = TOKEN_PROGRAM_ID) {
  const [currentAuthorityPublicKey, signers] = getSigners(currentAuthority, multiSigners);
  const transaction = new Transaction().add(createSetAuthorityInstruction(account, currentAuthorityPublicKey, authorityType, newAuthority, multiSigners, programId));
  return await sendAndConfirmTransaction(connection, transaction, [payer, ...signers], confirmOptions);
}

// node_modules/@solana/spl-token/lib/esm/actions/syncNative.js
init_index_browser_esm();
async function syncNative(connection, payer, account, confirmOptions, programId = TOKEN_PROGRAM_ID) {
  const transaction = new Transaction().add(createSyncNativeInstruction(account, programId));
  return await sendAndConfirmTransaction(connection, transaction, [payer], confirmOptions);
}

// node_modules/@solana/spl-token/lib/esm/actions/thawAccount.js
init_index_browser_esm();

// node_modules/@solana/spl-token/lib/esm/instructions/thawAccount.js
var import_buffer_layout47 = __toESM(require_Layout(), 1);
init_index_browser_esm();
var thawAccountInstructionData = (0, import_buffer_layout47.struct)([(0, import_buffer_layout47.u8)("instruction")]);
function createThawAccountInstruction(account, mint, authority, multiSigners = [], programId = TOKEN_PROGRAM_ID) {
  const keys = addSigners([
    { pubkey: account, isSigner: false, isWritable: true },
    { pubkey: mint, isSigner: false, isWritable: false }
  ], authority, multiSigners);
  const data = Buffer.alloc(thawAccountInstructionData.span);
  thawAccountInstructionData.encode({ instruction: TokenInstruction.ThawAccount }, data);
  return new TransactionInstruction({ keys, programId, data });
}
function decodeThawAccountInstruction(instruction, programId = TOKEN_PROGRAM_ID) {
  if (!instruction.programId.equals(programId))
    throw new TokenInvalidInstructionProgramError();
  if (instruction.data.length !== thawAccountInstructionData.span)
    throw new TokenInvalidInstructionDataError();
  const { keys: { account, mint, authority, multiSigners }, data } = decodeThawAccountInstructionUnchecked(instruction);
  if (data.instruction !== TokenInstruction.ThawAccount)
    throw new TokenInvalidInstructionTypeError();
  if (!account || !mint || !authority)
    throw new TokenInvalidInstructionKeysError();
  return {
    programId,
    keys: {
      account,
      mint,
      authority,
      multiSigners
    },
    data
  };
}
function decodeThawAccountInstructionUnchecked({ programId, keys: [account, mint, authority, ...multiSigners], data }) {
  return {
    programId,
    keys: {
      account,
      mint,
      authority,
      multiSigners
    },
    data: thawAccountInstructionData.decode(data)
  };
}

// node_modules/@solana/spl-token/lib/esm/actions/thawAccount.js
async function thawAccount(connection, payer, account, mint, authority, multiSigners = [], confirmOptions, programId = TOKEN_PROGRAM_ID) {
  const [authorityPublicKey, signers] = getSigners(authority, multiSigners);
  const transaction = new Transaction().add(createThawAccountInstruction(account, mint, authorityPublicKey, multiSigners, programId));
  return await sendAndConfirmTransaction(connection, transaction, [payer, ...signers], confirmOptions);
}

// node_modules/@solana/spl-token/lib/esm/actions/transfer.js
init_index_browser_esm();

// node_modules/@solana/spl-token/lib/esm/instructions/transfer.js
var import_buffer_layout48 = __toESM(require_Layout(), 1);
init_index_browser_esm();
var transferInstructionData = (0, import_buffer_layout48.struct)([(0, import_buffer_layout48.u8)("instruction"), u64("amount")]);
function createTransferInstruction(source, destination, owner, amount, multiSigners = [], programId = TOKEN_PROGRAM_ID) {
  const keys = addSigners([
    { pubkey: source, isSigner: false, isWritable: true },
    { pubkey: destination, isSigner: false, isWritable: true }
  ], owner, multiSigners);
  const data = Buffer.alloc(transferInstructionData.span);
  transferInstructionData.encode({
    instruction: TokenInstruction.Transfer,
    amount: BigInt(amount)
  }, data);
  return new TransactionInstruction({ keys, programId, data });
}
function decodeTransferInstruction(instruction, programId = TOKEN_PROGRAM_ID) {
  if (!instruction.programId.equals(programId))
    throw new TokenInvalidInstructionProgramError();
  if (instruction.data.length !== transferInstructionData.span)
    throw new TokenInvalidInstructionDataError();
  const { keys: { source, destination, owner, multiSigners }, data } = decodeTransferInstructionUnchecked(instruction);
  if (data.instruction !== TokenInstruction.Transfer)
    throw new TokenInvalidInstructionTypeError();
  if (!source || !destination || !owner)
    throw new TokenInvalidInstructionKeysError();
  return {
    programId,
    keys: {
      source,
      destination,
      owner,
      multiSigners
    },
    data
  };
}
function decodeTransferInstructionUnchecked({ programId, keys: [source, destination, owner, ...multiSigners], data }) {
  return {
    programId,
    keys: {
      source,
      destination,
      owner,
      multiSigners
    },
    data: transferInstructionData.decode(data)
  };
}

// node_modules/@solana/spl-token/lib/esm/actions/transfer.js
async function transfer(connection, payer, source, destination, owner, amount, multiSigners = [], confirmOptions, programId = TOKEN_PROGRAM_ID) {
  const [ownerPublicKey, signers] = getSigners(owner, multiSigners);
  const transaction = new Transaction().add(createTransferInstruction(source, destination, ownerPublicKey, amount, multiSigners, programId));
  return await sendAndConfirmTransaction(connection, transaction, [payer, ...signers], confirmOptions);
}

// node_modules/@solana/spl-token/lib/esm/actions/transferChecked.js
init_index_browser_esm();
async function transferChecked(connection, payer, source, mint, destination, owner, amount, decimals, multiSigners = [], confirmOptions, programId = TOKEN_PROGRAM_ID) {
  const [ownerPublicKey, signers] = getSigners(owner, multiSigners);
  const transaction = new Transaction().add(createTransferCheckedInstruction(source, mint, destination, ownerPublicKey, amount, decimals, multiSigners, programId));
  return await sendAndConfirmTransaction(connection, transaction, [payer, ...signers], confirmOptions);
}

// node_modules/@solana/spl-token/lib/esm/actions/uiAmountToAmount.js
init_index_browser_esm();

// node_modules/@solana/spl-token/lib/esm/instructions/uiAmountToAmount.js
var import_buffer_layout49 = __toESM(require_Layout(), 1);
init_index_browser_esm();
function createUiAmountToAmountInstruction(mint, amount, programId = TOKEN_PROGRAM_ID) {
  const keys = [{ pubkey: mint, isSigner: false, isWritable: false }];
  const buf = Buffer.from(amount, "utf8");
  const uiAmountToAmountInstructionData = (0, import_buffer_layout49.struct)([
    (0, import_buffer_layout49.u8)("instruction"),
    (0, import_buffer_layout49.blob)(buf.length, "amount")
  ]);
  const data = Buffer.alloc(uiAmountToAmountInstructionData.span);
  uiAmountToAmountInstructionData.encode({
    instruction: TokenInstruction.UiAmountToAmount,
    amount: buf
  }, data);
  return new TransactionInstruction({ keys, programId, data });
}
function decodeUiAmountToAmountInstruction(instruction, programId = TOKEN_PROGRAM_ID) {
  if (!instruction.programId.equals(programId))
    throw new TokenInvalidInstructionProgramError();
  const uiAmountToAmountInstructionData = (0, import_buffer_layout49.struct)([
    (0, import_buffer_layout49.u8)("instruction"),
    (0, import_buffer_layout49.blob)(instruction.data.length - 1, "amount")
  ]);
  if (instruction.data.length !== uiAmountToAmountInstructionData.span)
    throw new TokenInvalidInstructionDataError();
  const { keys: { mint }, data } = decodeUiAmountToAmountInstructionUnchecked(instruction);
  if (data.instruction !== TokenInstruction.UiAmountToAmount)
    throw new TokenInvalidInstructionTypeError();
  if (!mint)
    throw new TokenInvalidInstructionKeysError();
  return {
    programId,
    keys: {
      mint
    },
    data
  };
}
function decodeUiAmountToAmountInstructionUnchecked({ programId, keys: [mint], data }) {
  const uiAmountToAmountInstructionData = (0, import_buffer_layout49.struct)([
    (0, import_buffer_layout49.u8)("instruction"),
    (0, import_buffer_layout49.blob)(data.length - 1, "amount")
  ]);
  return {
    programId,
    keys: {
      mint
    },
    data: uiAmountToAmountInstructionData.decode(data)
  };
}

// node_modules/@solana/spl-token/lib/esm/actions/uiAmountToAmount.js
async function uiAmountToAmount(connection, payer, mint, amount, programId = TOKEN_PROGRAM_ID) {
  const transaction = new Transaction().add(createUiAmountToAmountInstruction(mint, amount, programId));
  const { returnData, err } = (await connection.simulateTransaction(transaction, [payer], false)).value;
  if (returnData) {
    const data = Buffer.from(returnData.data[0], returnData.data[1]);
    return u64().decode(data);
  }
  return err;
}

// node_modules/@solana/spl-token/lib/esm/extensions/groupMemberPointer/instructions.js
var import_buffer_layout50 = __toESM(require_Layout(), 1);
init_index_browser_esm();
var GroupMemberPointerInstruction;
(function(GroupMemberPointerInstruction2) {
  GroupMemberPointerInstruction2[GroupMemberPointerInstruction2["Initialize"] = 0] = "Initialize";
  GroupMemberPointerInstruction2[GroupMemberPointerInstruction2["Update"] = 1] = "Update";
})(GroupMemberPointerInstruction || (GroupMemberPointerInstruction = {}));
var initializeGroupMemberPointerData = (0, import_buffer_layout50.struct)([
  // prettier-ignore
  (0, import_buffer_layout50.u8)("instruction"),
  (0, import_buffer_layout50.u8)("groupMemberPointerInstruction"),
  publicKey("authority"),
  publicKey("memberAddress")
]);
function createInitializeGroupMemberPointerInstruction(mint, authority, memberAddress, programId = TOKEN_2022_PROGRAM_ID) {
  if (!programSupportsExtensions(programId)) {
    throw new TokenUnsupportedInstructionError();
  }
  const keys = [{ pubkey: mint, isSigner: false, isWritable: true }];
  const data = Buffer.alloc(initializeGroupMemberPointerData.span);
  initializeGroupMemberPointerData.encode({
    instruction: TokenInstruction.GroupMemberPointerExtension,
    groupMemberPointerInstruction: GroupMemberPointerInstruction.Initialize,
    authority: authority ?? PublicKey.default,
    memberAddress: memberAddress ?? PublicKey.default
  }, data);
  return new TransactionInstruction({ keys, programId, data });
}
var updateGroupMemberPointerData = (0, import_buffer_layout50.struct)([
  // prettier-ignore
  (0, import_buffer_layout50.u8)("instruction"),
  (0, import_buffer_layout50.u8)("groupMemberPointerInstruction"),
  publicKey("memberAddress")
]);
function createUpdateGroupMemberPointerInstruction(mint, authority, memberAddress, multiSigners = [], programId = TOKEN_2022_PROGRAM_ID) {
  if (!programSupportsExtensions(programId)) {
    throw new TokenUnsupportedInstructionError();
  }
  const keys = addSigners([{ pubkey: mint, isSigner: false, isWritable: true }], authority, multiSigners);
  const data = Buffer.alloc(updateGroupMemberPointerData.span);
  updateGroupMemberPointerData.encode({
    instruction: TokenInstruction.GroupMemberPointerExtension,
    groupMemberPointerInstruction: GroupMemberPointerInstruction.Update,
    memberAddress: memberAddress ?? PublicKey.default
  }, data);
  return new TransactionInstruction({ keys, programId, data });
}

// node_modules/@solana/spl-token/lib/esm/extensions/groupPointer/instructions.js
var import_buffer_layout51 = __toESM(require_Layout(), 1);
init_index_browser_esm();
var GroupPointerInstruction;
(function(GroupPointerInstruction2) {
  GroupPointerInstruction2[GroupPointerInstruction2["Initialize"] = 0] = "Initialize";
  GroupPointerInstruction2[GroupPointerInstruction2["Update"] = 1] = "Update";
})(GroupPointerInstruction || (GroupPointerInstruction = {}));
var initializeGroupPointerData = (0, import_buffer_layout51.struct)([
  // prettier-ignore
  (0, import_buffer_layout51.u8)("instruction"),
  (0, import_buffer_layout51.u8)("groupPointerInstruction"),
  publicKey("authority"),
  publicKey("groupAddress")
]);
function createInitializeGroupPointerInstruction(mint, authority, groupAddress, programId = TOKEN_2022_PROGRAM_ID) {
  if (!programSupportsExtensions(programId)) {
    throw new TokenUnsupportedInstructionError();
  }
  const keys = [{ pubkey: mint, isSigner: false, isWritable: true }];
  const data = Buffer.alloc(initializeGroupPointerData.span);
  initializeGroupPointerData.encode({
    instruction: TokenInstruction.GroupPointerExtension,
    groupPointerInstruction: GroupPointerInstruction.Initialize,
    authority: authority ?? PublicKey.default,
    groupAddress: groupAddress ?? PublicKey.default
  }, data);
  return new TransactionInstruction({ keys, programId, data });
}
var updateGroupPointerData = (0, import_buffer_layout51.struct)([
  // prettier-ignore
  (0, import_buffer_layout51.u8)("instruction"),
  (0, import_buffer_layout51.u8)("groupPointerInstruction"),
  publicKey("groupAddress")
]);
function createUpdateGroupPointerInstruction(mint, authority, groupAddress, multiSigners = [], programId = TOKEN_2022_PROGRAM_ID) {
  if (!programSupportsExtensions(programId)) {
    throw new TokenUnsupportedInstructionError();
  }
  const keys = addSigners([{ pubkey: mint, isSigner: false, isWritable: true }], authority, multiSigners);
  const data = Buffer.alloc(updateGroupPointerData.span);
  updateGroupPointerData.encode({
    instruction: TokenInstruction.GroupPointerExtension,
    groupPointerInstruction: GroupPointerInstruction.Update,
    groupAddress: groupAddress ?? PublicKey.default
  }, data);
  return new TransactionInstruction({ keys, programId, data });
}

// node_modules/@solana/spl-token/lib/esm/extensions/interestBearingMint/actions.js
init_index_browser_esm();

// node_modules/@solana/spl-token/lib/esm/instructions/initializeMint.js
var import_buffer_layout52 = __toESM(require_Layout(), 1);
init_index_browser_esm();
var initializeMintInstructionData = (0, import_buffer_layout52.struct)([
  (0, import_buffer_layout52.u8)("instruction"),
  (0, import_buffer_layout52.u8)("decimals"),
  publicKey("mintAuthority"),
  new COptionPublicKeyLayout("freezeAuthority")
]);
function createInitializeMintInstruction(mint, decimals, mintAuthority, freezeAuthority, programId = TOKEN_PROGRAM_ID) {
  const keys = [
    { pubkey: mint, isSigner: false, isWritable: true },
    { pubkey: SYSVAR_RENT_PUBKEY, isSigner: false, isWritable: false }
  ];
  const data = Buffer.alloc(67);
  initializeMintInstructionData.encode({
    instruction: TokenInstruction.InitializeMint,
    decimals,
    mintAuthority,
    freezeAuthority
  }, data);
  return new TransactionInstruction({
    keys,
    programId,
    data: data.subarray(0, initializeMintInstructionData.getSpan(data))
  });
}
function decodeInitializeMintInstruction(instruction, programId = TOKEN_PROGRAM_ID) {
  if (!instruction.programId.equals(programId))
    throw new TokenInvalidInstructionProgramError();
  if (instruction.data.length !== initializeMintInstructionData.getSpan(instruction.data))
    throw new TokenInvalidInstructionDataError();
  const { keys: { mint, rent }, data } = decodeInitializeMintInstructionUnchecked(instruction);
  if (data.instruction !== TokenInstruction.InitializeMint)
    throw new TokenInvalidInstructionTypeError();
  if (!mint || !rent)
    throw new TokenInvalidInstructionKeysError();
  return {
    programId,
    keys: {
      mint,
      rent
    },
    data
  };
}
function decodeInitializeMintInstructionUnchecked({ programId, keys: [mint, rent], data }) {
  const { instruction, decimals, mintAuthority, freezeAuthority } = initializeMintInstructionData.decode(data);
  return {
    programId,
    keys: {
      mint,
      rent
    },
    data: {
      instruction,
      decimals,
      mintAuthority,
      freezeAuthority
    }
  };
}

// node_modules/@solana/spl-token/lib/esm/extensions/interestBearingMint/instructions.js
var import_buffer_layout53 = __toESM(require_Layout(), 1);
init_index_browser_esm();
var InterestBearingMintInstruction;
(function(InterestBearingMintInstruction2) {
  InterestBearingMintInstruction2[InterestBearingMintInstruction2["Initialize"] = 0] = "Initialize";
  InterestBearingMintInstruction2[InterestBearingMintInstruction2["UpdateRate"] = 1] = "UpdateRate";
})(InterestBearingMintInstruction || (InterestBearingMintInstruction = {}));
var interestBearingMintInitializeInstructionData = (0, import_buffer_layout53.struct)([
  (0, import_buffer_layout53.u8)("instruction"),
  (0, import_buffer_layout53.u8)("interestBearingMintInstruction"),
  // TODO: Make this an optional public key
  publicKey("rateAuthority"),
  (0, import_buffer_layout53.s16)("rate")
]);
var interestBearingMintUpdateRateInstructionData = (0, import_buffer_layout53.struct)([
  (0, import_buffer_layout53.u8)("instruction"),
  (0, import_buffer_layout53.u8)("interestBearingMintInstruction"),
  (0, import_buffer_layout53.s16)("rate")
]);
function createInitializeInterestBearingMintInstruction(mint, rateAuthority, rate, programId = TOKEN_2022_PROGRAM_ID) {
  const keys = [{ pubkey: mint, isSigner: false, isWritable: true }];
  const data = Buffer.alloc(interestBearingMintInitializeInstructionData.span);
  interestBearingMintInitializeInstructionData.encode({
    instruction: TokenInstruction.InterestBearingMintExtension,
    interestBearingMintInstruction: InterestBearingMintInstruction.Initialize,
    rateAuthority,
    rate
  }, data);
  return new TransactionInstruction({ keys, programId, data });
}
function createUpdateRateInterestBearingMintInstruction(mint, rateAuthority, rate, multiSigners = [], programId = TOKEN_2022_PROGRAM_ID) {
  const keys = addSigners([
    { pubkey: mint, isSigner: false, isWritable: true },
    { pubkey: rateAuthority, isSigner: !multiSigners.length, isWritable: false }
  ], rateAuthority, multiSigners);
  const data = Buffer.alloc(interestBearingMintUpdateRateInstructionData.span);
  interestBearingMintUpdateRateInstructionData.encode({
    instruction: TokenInstruction.InterestBearingMintExtension,
    interestBearingMintInstruction: InterestBearingMintInstruction.UpdateRate,
    rate
  }, data);
  return new TransactionInstruction({ keys, programId, data });
}

// node_modules/@solana/spl-token/lib/esm/extensions/interestBearingMint/actions.js
async function createInterestBearingMint(connection, payer, mintAuthority, freezeAuthority, rateAuthority, rate, decimals, keypair = Keypair.generate(), confirmOptions, programId = TOKEN_2022_PROGRAM_ID) {
  const mintLen = getMintLen([ExtensionType.InterestBearingConfig]);
  const lamports = await connection.getMinimumBalanceForRentExemption(mintLen);
  const transaction = new Transaction().add(SystemProgram.createAccount({
    fromPubkey: payer.publicKey,
    newAccountPubkey: keypair.publicKey,
    space: mintLen,
    lamports,
    programId
  }), createInitializeInterestBearingMintInstruction(keypair.publicKey, rateAuthority, rate, programId), createInitializeMintInstruction(keypair.publicKey, decimals, mintAuthority, freezeAuthority, programId));
  await sendAndConfirmTransaction(connection, transaction, [payer, keypair], confirmOptions);
  return keypair.publicKey;
}
async function updateRateInterestBearingMint(connection, payer, mint, rateAuthority, rate, multiSigners = [], confirmOptions, programId = TOKEN_2022_PROGRAM_ID) {
  const [rateAuthorityPublicKey, signers] = getSigners(rateAuthority, multiSigners);
  const transaction = new Transaction().add(createUpdateRateInterestBearingMintInstruction(mint, rateAuthorityPublicKey, rate, signers, programId));
  return await sendAndConfirmTransaction(connection, transaction, [payer, rateAuthority, ...signers], confirmOptions);
}

// node_modules/@solana/spl-token/lib/esm/extensions/metadataPointer/instructions.js
var import_buffer_layout54 = __toESM(require_Layout(), 1);
init_index_browser_esm();
var MetadataPointerInstruction;
(function(MetadataPointerInstruction2) {
  MetadataPointerInstruction2[MetadataPointerInstruction2["Initialize"] = 0] = "Initialize";
  MetadataPointerInstruction2[MetadataPointerInstruction2["Update"] = 1] = "Update";
})(MetadataPointerInstruction || (MetadataPointerInstruction = {}));
var initializeMetadataPointerData = (0, import_buffer_layout54.struct)([
  // prettier-ignore
  (0, import_buffer_layout54.u8)("instruction"),
  (0, import_buffer_layout54.u8)("metadataPointerInstruction"),
  publicKey("authority"),
  publicKey("metadataAddress")
]);
function createInitializeMetadataPointerInstruction(mint, authority, metadataAddress, programId) {
  if (!programSupportsExtensions(programId)) {
    throw new TokenUnsupportedInstructionError();
  }
  const keys = [{ pubkey: mint, isSigner: false, isWritable: true }];
  const data = Buffer.alloc(initializeMetadataPointerData.span);
  initializeMetadataPointerData.encode({
    instruction: TokenInstruction.MetadataPointerExtension,
    metadataPointerInstruction: MetadataPointerInstruction.Initialize,
    authority: authority ?? PublicKey.default,
    metadataAddress: metadataAddress ?? PublicKey.default
  }, data);
  return new TransactionInstruction({ keys, programId, data });
}
var updateMetadataPointerData = (0, import_buffer_layout54.struct)([
  // prettier-ignore
  (0, import_buffer_layout54.u8)("instruction"),
  (0, import_buffer_layout54.u8)("metadataPointerInstruction"),
  publicKey("metadataAddress")
]);
function createUpdateMetadataPointerInstruction(mint, authority, metadataAddress, multiSigners = [], programId = TOKEN_2022_PROGRAM_ID) {
  if (!programSupportsExtensions(programId)) {
    throw new TokenUnsupportedInstructionError();
  }
  const keys = addSigners([{ pubkey: mint, isSigner: false, isWritable: true }], authority, multiSigners);
  const data = Buffer.alloc(updateMetadataPointerData.span);
  updateMetadataPointerData.encode({
    instruction: TokenInstruction.MetadataPointerExtension,
    metadataPointerInstruction: MetadataPointerInstruction.Update,
    metadataAddress: metadataAddress ?? PublicKey.default
  }, data);
  return new TransactionInstruction({ keys, programId, data });
}

// node_modules/@solana/spl-token/lib/esm/extensions/tokenMetadata/actions.js
init_index_browser_esm();

// node_modules/@solana/spl-token-metadata/lib/esm/field.js
var Field;
(function(Field2) {
  Field2[Field2["Name"] = 0] = "Name";
  Field2[Field2["Symbol"] = 1] = "Symbol";
  Field2[Field2["Uri"] = 2] = "Uri";
})(Field || (Field = {}));
var getFieldCodec = () => [
  ["Name", getUnitCodec()],
  ["Symbol", getUnitCodec()],
  ["Uri", getUnitCodec()],
  ["Key", getStructCodec([["value", getTupleCodec([addCodecSizePrefix(getUtf8Codec(), getU32Codec())])]])]
];
function getFieldConfig(field) {
  if (field === Field.Name || field === "Name" || field === "name") {
    return { __kind: "Name" };
  } else if (field === Field.Symbol || field === "Symbol" || field === "symbol") {
    return { __kind: "Symbol" };
  } else if (field === Field.Uri || field === "Uri" || field === "uri") {
    return { __kind: "Uri" };
  } else {
    return { __kind: "Key", value: [field] };
  }
}

// node_modules/@solana/spl-token-metadata/lib/esm/instruction.js
init_index_browser_esm();
function getInstructionEncoder2(discriminator, dataEncoder) {
  return transformEncoder(getTupleEncoder([getBytesEncoder(), dataEncoder]), (data) => [
    discriminator,
    data
  ]);
}
function getPublicKeyEncoder2() {
  return transformEncoder(fixEncoderSize(getBytesEncoder(), 32), (publicKey2) => publicKey2.toBytes());
}
function getStringEncoder() {
  return addEncoderSizePrefix(getUtf8Encoder(), getU32Encoder2());
}
function createInitializeInstruction(args) {
  const { programId, metadata, updateAuthority, mint, mintAuthority, name, symbol, uri } = args;
  return new TransactionInstruction({
    programId,
    keys: [
      { isSigner: false, isWritable: true, pubkey: metadata },
      { isSigner: false, isWritable: false, pubkey: updateAuthority },
      { isSigner: false, isWritable: false, pubkey: mint },
      { isSigner: true, isWritable: false, pubkey: mintAuthority }
    ],
    data: Buffer.from(getInstructionEncoder2(new Uint8Array([
      /* await splDiscriminate('spl_token_metadata_interface:initialize_account') */
      210,
      225,
      30,
      162,
      88,
      184,
      77,
      141
    ]), getStructEncoder([
      ["name", getStringEncoder()],
      ["symbol", getStringEncoder()],
      ["uri", getStringEncoder()]
    ])).encode({ name, symbol, uri }))
  });
}
function createUpdateFieldInstruction(args) {
  const { programId, metadata, updateAuthority, field, value } = args;
  return new TransactionInstruction({
    programId,
    keys: [
      { isSigner: false, isWritable: true, pubkey: metadata },
      { isSigner: true, isWritable: false, pubkey: updateAuthority }
    ],
    data: Buffer.from(getInstructionEncoder2(new Uint8Array([
      /* await splDiscriminate('spl_token_metadata_interface:updating_field') */
      221,
      233,
      49,
      45,
      181,
      202,
      220,
      200
    ]), getStructEncoder([
      ["field", getDataEnumCodec(getFieldCodec())],
      ["value", getStringEncoder()]
    ])).encode({ field: getFieldConfig(field), value }))
  });
}
function createRemoveKeyInstruction(args) {
  const { programId, metadata, updateAuthority, key, idempotent } = args;
  return new TransactionInstruction({
    programId,
    keys: [
      { isSigner: false, isWritable: true, pubkey: metadata },
      { isSigner: true, isWritable: false, pubkey: updateAuthority }
    ],
    data: Buffer.from(getInstructionEncoder2(new Uint8Array([
      /* await splDiscriminate('spl_token_metadata_interface:remove_key_ix') */
      234,
      18,
      32,
      56,
      89,
      141,
      37,
      181
    ]), getStructEncoder([
      ["idempotent", getBooleanEncoder()],
      ["key", getStringEncoder()]
    ])).encode({ idempotent, key }))
  });
}
function createUpdateAuthorityInstruction(args) {
  const { programId, metadata, oldAuthority, newAuthority } = args;
  return new TransactionInstruction({
    programId,
    keys: [
      { isSigner: false, isWritable: true, pubkey: metadata },
      { isSigner: true, isWritable: false, pubkey: oldAuthority }
    ],
    data: Buffer.from(getInstructionEncoder2(new Uint8Array([
      /* await splDiscriminate('spl_token_metadata_interface:update_the_authority') */
      215,
      228,
      166,
      228,
      84,
      100,
      86,
      123
    ]), getStructEncoder([["newAuthority", getPublicKeyEncoder2()]])).encode({ newAuthority: newAuthority ?? SystemProgram.programId }))
  });
}
function createEmitInstruction(args) {
  const { programId, metadata, start, end } = args;
  return new TransactionInstruction({
    programId,
    keys: [{ isSigner: false, isWritable: false, pubkey: metadata }],
    data: Buffer.from(getInstructionEncoder2(new Uint8Array([
      /* await splDiscriminate('spl_token_metadata_interface:emitter') */
      250,
      166,
      180,
      250,
      13,
      12,
      184,
      70
    ]), getStructEncoder([
      ["start", getOptionEncoder(getU64Encoder())],
      ["end", getOptionEncoder(getU64Encoder())]
    ])).encode({ start: start ?? null, end: end ?? null }))
  });
}

// node_modules/@solana/spl-token-metadata/lib/esm/state.js
init_index_browser_esm();
var TOKEN_METADATA_DISCRIMINATOR = Buffer.from([112, 132, 90, 90, 11, 88, 157, 87]);
function getStringCodec() {
  return addCodecSizePrefix(getUtf8Codec(), getU32Codec());
}
var tokenMetadataCodec = getStructCodec([
  ["updateAuthority", fixCodecSize(getBytesCodec(), 32)],
  ["mint", fixCodecSize(getBytesCodec(), 32)],
  ["name", getStringCodec()],
  ["symbol", getStringCodec()],
  ["uri", getStringCodec()],
  ["additionalMetadata", getArrayCodec(getTupleCodec([getStringCodec(), getStringCodec()]))]
]);
function isNonePubkey2(buffer) {
  for (let i = 0; i < buffer.length; i++) {
    if (buffer[i] !== 0) {
      return false;
    }
  }
  return true;
}
function pack(meta) {
  const updateAuthority = meta.updateAuthority ?? PublicKey.default;
  return tokenMetadataCodec.encode({
    ...meta,
    updateAuthority: updateAuthority.toBuffer(),
    mint: meta.mint.toBuffer()
  });
}
function unpack(buffer) {
  const data = tokenMetadataCodec.decode(buffer);
  return isNonePubkey2(data.updateAuthority) ? {
    mint: new PublicKey(data.mint),
    name: data.name,
    symbol: data.symbol,
    uri: data.uri,
    additionalMetadata: data.additionalMetadata
  } : {
    updateAuthority: new PublicKey(data.updateAuthority),
    mint: new PublicKey(data.mint),
    name: data.name,
    symbol: data.symbol,
    uri: data.uri,
    additionalMetadata: data.additionalMetadata
  };
}

// node_modules/@solana/spl-token/lib/esm/extensions/tokenMetadata/state.js
var getNormalizedTokenMetadataField = (field) => {
  if (field === Field.Name || field === "Name" || field === "name") {
    return "name";
  }
  if (field === Field.Symbol || field === "Symbol" || field === "symbol") {
    return "symbol";
  }
  if (field === Field.Uri || field === "Uri" || field === "uri") {
    return "uri";
  }
  return field;
};
function updateTokenMetadata(current, key, value) {
  const field = getNormalizedTokenMetadataField(key);
  if (field === "mint" || field === "updateAuthority") {
    throw new Error(`Cannot update ${field} via this instruction`);
  }
  if (["name", "symbol", "uri"].includes(field)) {
    return {
      ...current,
      [field]: value
    };
  }
  const additionalMetadata = [...current.additionalMetadata];
  const i = current.additionalMetadata.findIndex((x) => x[0] === field);
  if (i === -1) {
    additionalMetadata.push([field, value]);
  } else {
    additionalMetadata[i] = [field, value];
  }
  return {
    ...current,
    additionalMetadata
  };
}
async function getTokenMetadata(connection, address, commitment, programId = TOKEN_2022_PROGRAM_ID) {
  const mintInfo = await getMint(connection, address, commitment, programId);
  const data = getExtensionData(ExtensionType.TokenMetadata, mintInfo.tlvData);
  if (data === null) {
    return null;
  }
  return unpack(data);
}

// node_modules/@solana/spl-token/lib/esm/extensions/tokenMetadata/actions.js
async function getAdditionalRentForNewMetadata(connection, address, tokenMetadata, programId = TOKEN_2022_PROGRAM_ID) {
  const info = await connection.getAccountInfo(address);
  if (!info) {
    throw new TokenAccountNotFoundError();
  }
  const extensionLen = pack(tokenMetadata).length;
  const newAccountLen = getNewAccountLenForExtensionLen(info, address, ExtensionType.TokenMetadata, extensionLen, programId);
  if (newAccountLen <= info.data.length) {
    return 0;
  }
  const newRentExemptMinimum = await connection.getMinimumBalanceForRentExemption(newAccountLen);
  return newRentExemptMinimum - info.lamports;
}
async function getAdditionalRentForUpdatedMetadata(connection, address, field, value, programId = TOKEN_2022_PROGRAM_ID) {
  const info = await connection.getAccountInfo(address);
  if (!info) {
    throw new TokenAccountNotFoundError();
  }
  const mint = unpackMint(address, info, programId);
  const extensionData = getExtensionData(ExtensionType.TokenMetadata, mint.tlvData);
  if (extensionData === null) {
    throw new Error("TokenMetadata extension not initialized");
  }
  const updatedTokenMetadata = updateTokenMetadata(unpack(extensionData), field, value);
  const extensionLen = pack(updatedTokenMetadata).length;
  const newAccountLen = getNewAccountLenForExtensionLen(info, address, ExtensionType.TokenMetadata, extensionLen, programId);
  if (newAccountLen <= info.data.length) {
    return 0;
  }
  const newRentExemptMinimum = await connection.getMinimumBalanceForRentExemption(newAccountLen);
  return newRentExemptMinimum - info.lamports;
}
async function tokenMetadataInitialize(connection, payer, mint, updateAuthority, mintAuthority, name, symbol, uri, multiSigners = [], confirmOptions, programId = TOKEN_2022_PROGRAM_ID) {
  const [mintAuthorityPublicKey, signers] = getSigners(mintAuthority, multiSigners);
  const transaction = new Transaction().add(createInitializeInstruction({
    programId,
    metadata: mint,
    updateAuthority,
    mint,
    mintAuthority: mintAuthorityPublicKey,
    name,
    symbol,
    uri
  }));
  return await sendAndConfirmTransaction(connection, transaction, [payer, ...signers], confirmOptions);
}
async function tokenMetadataInitializeWithRentTransfer(connection, payer, mint, updateAuthority, mintAuthority, name, symbol, uri, multiSigners = [], confirmOptions, programId = TOKEN_2022_PROGRAM_ID) {
  const [mintAuthorityPublicKey, signers] = getSigners(mintAuthority, multiSigners);
  const transaction = new Transaction();
  const lamports = await getAdditionalRentForNewMetadata(connection, mint, {
    updateAuthority,
    mint,
    name,
    symbol,
    uri,
    additionalMetadata: []
  }, programId);
  if (lamports > 0) {
    transaction.add(SystemProgram.transfer({ fromPubkey: payer.publicKey, toPubkey: mint, lamports }));
  }
  transaction.add(createInitializeInstruction({
    programId,
    metadata: mint,
    updateAuthority,
    mint,
    mintAuthority: mintAuthorityPublicKey,
    name,
    symbol,
    uri
  }));
  return await sendAndConfirmTransaction(connection, transaction, [payer, ...signers], confirmOptions);
}
async function tokenMetadataUpdateField(connection, payer, mint, updateAuthority, field, value, multiSigners = [], confirmOptions, programId = TOKEN_2022_PROGRAM_ID) {
  const [updateAuthorityPublicKey, signers] = getSigners(updateAuthority, multiSigners);
  const transaction = new Transaction().add(createUpdateFieldInstruction({
    programId,
    metadata: mint,
    updateAuthority: updateAuthorityPublicKey,
    field,
    value
  }));
  return await sendAndConfirmTransaction(connection, transaction, [payer, ...signers], confirmOptions);
}
async function tokenMetadataUpdateFieldWithRentTransfer(connection, payer, mint, updateAuthority, field, value, multiSigners = [], confirmOptions, programId = TOKEN_2022_PROGRAM_ID) {
  const [updateAuthorityPublicKey, signers] = getSigners(updateAuthority, multiSigners);
  const transaction = new Transaction();
  const lamports = await getAdditionalRentForUpdatedMetadata(connection, mint, field, value, programId);
  if (lamports > 0) {
    transaction.add(SystemProgram.transfer({ fromPubkey: payer.publicKey, toPubkey: mint, lamports }));
  }
  transaction.add(createUpdateFieldInstruction({
    programId,
    metadata: mint,
    updateAuthority: updateAuthorityPublicKey,
    field,
    value
  }));
  return await sendAndConfirmTransaction(connection, transaction, [payer, ...signers], confirmOptions);
}
async function tokenMetadataRemoveKey(connection, payer, mint, updateAuthority, key, idempotent, multiSigners = [], confirmOptions, programId = TOKEN_2022_PROGRAM_ID) {
  const [updateAuthorityPublicKey, signers] = getSigners(updateAuthority, multiSigners);
  const transaction = new Transaction().add(createRemoveKeyInstruction({
    programId,
    metadata: mint,
    updateAuthority: updateAuthorityPublicKey,
    key,
    idempotent
  }));
  return await sendAndConfirmTransaction(connection, transaction, [payer, ...signers], confirmOptions);
}
async function tokenMetadataUpdateAuthority(connection, payer, mint, updateAuthority, newAuthority, multiSigners = [], confirmOptions, programId = TOKEN_2022_PROGRAM_ID) {
  const [updateAuthorityPublicKey, signers] = getSigners(updateAuthority, multiSigners);
  const transaction = new Transaction().add(createUpdateAuthorityInstruction({
    programId,
    metadata: mint,
    oldAuthority: updateAuthorityPublicKey,
    newAuthority
  }));
  return await sendAndConfirmTransaction(connection, transaction, [payer, ...signers], confirmOptions);
}

// node_modules/@solana/spl-token/lib/esm/instructions/decode.js
var import_buffer_layout57 = __toESM(require_Layout(), 1);

// node_modules/@solana/spl-token/lib/esm/instructions/initializeAccount2.js
var import_buffer_layout55 = __toESM(require_Layout(), 1);
init_index_browser_esm();
var initializeAccount2InstructionData = (0, import_buffer_layout55.struct)([
  (0, import_buffer_layout55.u8)("instruction"),
  publicKey("owner")
]);
function createInitializeAccount2Instruction(account, mint, owner, programId = TOKEN_PROGRAM_ID) {
  const keys = [
    { pubkey: account, isSigner: false, isWritable: true },
    { pubkey: mint, isSigner: false, isWritable: false },
    { pubkey: SYSVAR_RENT_PUBKEY, isSigner: false, isWritable: false }
  ];
  const data = Buffer.alloc(initializeAccount2InstructionData.span);
  initializeAccount2InstructionData.encode({ instruction: TokenInstruction.InitializeAccount2, owner }, data);
  return new TransactionInstruction({ keys, programId, data });
}
function decodeInitializeAccount2Instruction(instruction, programId = TOKEN_PROGRAM_ID) {
  if (!instruction.programId.equals(programId))
    throw new TokenInvalidInstructionProgramError();
  if (instruction.data.length !== initializeAccount2InstructionData.span)
    throw new TokenInvalidInstructionDataError();
  const { keys: { account, mint, rent }, data } = decodeInitializeAccount2InstructionUnchecked(instruction);
  if (data.instruction !== TokenInstruction.InitializeAccount2)
    throw new TokenInvalidInstructionTypeError();
  if (!account || !mint || !rent)
    throw new TokenInvalidInstructionKeysError();
  return {
    programId,
    keys: {
      account,
      mint,
      rent
    },
    data
  };
}
function decodeInitializeAccount2InstructionUnchecked({ programId, keys: [account, mint, rent], data }) {
  return {
    programId,
    keys: {
      account,
      mint,
      rent
    },
    data: initializeAccount2InstructionData.decode(data)
  };
}

// node_modules/@solana/spl-token/lib/esm/instructions/initializeAccount3.js
var import_buffer_layout56 = __toESM(require_Layout(), 1);
init_index_browser_esm();
var initializeAccount3InstructionData = (0, import_buffer_layout56.struct)([
  (0, import_buffer_layout56.u8)("instruction"),
  publicKey("owner")
]);
function createInitializeAccount3Instruction(account, mint, owner, programId = TOKEN_PROGRAM_ID) {
  const keys = [
    { pubkey: account, isSigner: false, isWritable: true },
    { pubkey: mint, isSigner: false, isWritable: false }
  ];
  const data = Buffer.alloc(initializeAccount3InstructionData.span);
  initializeAccount3InstructionData.encode({ instruction: TokenInstruction.InitializeAccount3, owner }, data);
  return new TransactionInstruction({ keys, programId, data });
}
function decodeInitializeAccount3Instruction(instruction, programId = TOKEN_PROGRAM_ID) {
  if (!instruction.programId.equals(programId))
    throw new TokenInvalidInstructionProgramError();
  if (instruction.data.length !== initializeAccount3InstructionData.span)
    throw new TokenInvalidInstructionDataError();
  const { keys: { account, mint }, data } = decodeInitializeAccount3InstructionUnchecked(instruction);
  if (data.instruction !== TokenInstruction.InitializeAccount3)
    throw new TokenInvalidInstructionTypeError();
  if (!account || !mint)
    throw new TokenInvalidInstructionKeysError();
  return {
    programId,
    keys: {
      account,
      mint
    },
    data
  };
}
function decodeInitializeAccount3InstructionUnchecked({ programId, keys: [account, mint], data }) {
  return {
    programId,
    keys: {
      account,
      mint
    },
    data: initializeAccount3InstructionData.decode(data)
  };
}

// node_modules/@solana/spl-token/lib/esm/instructions/decode.js
function decodeInstruction(instruction, programId = TOKEN_PROGRAM_ID) {
  if (!instruction.data.length)
    throw new TokenInvalidInstructionDataError();
  const type = (0, import_buffer_layout57.u8)().decode(instruction.data);
  if (type === TokenInstruction.InitializeMint)
    return decodeInitializeMintInstruction(instruction, programId);
  if (type === TokenInstruction.InitializeAccount)
    return decodeInitializeAccountInstruction(instruction, programId);
  if (type === TokenInstruction.InitializeMultisig)
    return decodeInitializeMultisigInstruction(instruction, programId);
  if (type === TokenInstruction.Transfer)
    return decodeTransferInstruction(instruction, programId);
  if (type === TokenInstruction.Approve)
    return decodeApproveInstruction(instruction, programId);
  if (type === TokenInstruction.Revoke)
    return decodeRevokeInstruction(instruction, programId);
  if (type === TokenInstruction.SetAuthority)
    return decodeSetAuthorityInstruction(instruction, programId);
  if (type === TokenInstruction.MintTo)
    return decodeMintToInstruction(instruction, programId);
  if (type === TokenInstruction.Burn)
    return decodeBurnInstruction(instruction, programId);
  if (type === TokenInstruction.CloseAccount)
    return decodeCloseAccountInstruction(instruction, programId);
  if (type === TokenInstruction.FreezeAccount)
    return decodeFreezeAccountInstruction(instruction, programId);
  if (type === TokenInstruction.ThawAccount)
    return decodeThawAccountInstruction(instruction, programId);
  if (type === TokenInstruction.TransferChecked)
    return decodeTransferCheckedInstruction(instruction, programId);
  if (type === TokenInstruction.ApproveChecked)
    return decodeApproveCheckedInstruction(instruction, programId);
  if (type === TokenInstruction.MintToChecked)
    return decodeMintToCheckedInstruction(instruction, programId);
  if (type === TokenInstruction.BurnChecked)
    return decodeBurnCheckedInstruction(instruction, programId);
  if (type === TokenInstruction.InitializeAccount2)
    return decodeInitializeAccount2Instruction(instruction, programId);
  if (type === TokenInstruction.SyncNative)
    return decodeSyncNativeInstruction(instruction, programId);
  if (type === TokenInstruction.InitializeAccount3)
    return decodeInitializeAccount3Instruction(instruction, programId);
  if (type === TokenInstruction.InitializeMint2)
    return decodeInitializeMint2Instruction(instruction, programId);
  if (type === TokenInstruction.AmountToUiAmount)
    return decodeAmountToUiAmountInstruction(instruction, programId);
  if (type === TokenInstruction.UiAmountToAmount)
    return decodeUiAmountToAmountInstruction(instruction, programId);
  if (type === TokenInstruction.InitializeMultisig2)
    throw new TokenInvalidInstructionTypeError();
  throw new TokenInvalidInstructionTypeError();
}
function isInitializeMintInstruction(decoded) {
  return decoded.data.instruction === TokenInstruction.InitializeMint;
}
function isInitializeAccountInstruction(decoded) {
  return decoded.data.instruction === TokenInstruction.InitializeAccount;
}
function isInitializeMultisigInstruction(decoded) {
  return decoded.data.instruction === TokenInstruction.InitializeMultisig;
}
function isTransferInstruction(decoded) {
  return decoded.data.instruction === TokenInstruction.Transfer;
}
function isApproveInstruction(decoded) {
  return decoded.data.instruction === TokenInstruction.Approve;
}
function isRevokeInstruction(decoded) {
  return decoded.data.instruction === TokenInstruction.Revoke;
}
function isSetAuthorityInstruction(decoded) {
  return decoded.data.instruction === TokenInstruction.SetAuthority;
}
function isMintToInstruction(decoded) {
  return decoded.data.instruction === TokenInstruction.MintTo;
}
function isBurnInstruction(decoded) {
  return decoded.data.instruction === TokenInstruction.Burn;
}
function isCloseAccountInstruction(decoded) {
  return decoded.data.instruction === TokenInstruction.CloseAccount;
}
function isFreezeAccountInstruction(decoded) {
  return decoded.data.instruction === TokenInstruction.FreezeAccount;
}
function isThawAccountInstruction(decoded) {
  return decoded.data.instruction === TokenInstruction.ThawAccount;
}
function isTransferCheckedInstruction(decoded) {
  return decoded.data.instruction === TokenInstruction.TransferChecked;
}
function isApproveCheckedInstruction(decoded) {
  return decoded.data.instruction === TokenInstruction.ApproveChecked;
}
function isMintToCheckedInstruction(decoded) {
  return decoded.data.instruction === TokenInstruction.MintToChecked;
}
function isBurnCheckedInstruction(decoded) {
  return decoded.data.instruction === TokenInstruction.BurnChecked;
}
function isInitializeAccount2Instruction(decoded) {
  return decoded.data.instruction === TokenInstruction.InitializeAccount2;
}
function isSyncNativeInstruction(decoded) {
  return decoded.data.instruction === TokenInstruction.SyncNative;
}
function isInitializeAccount3Instruction(decoded) {
  return decoded.data.instruction === TokenInstruction.InitializeAccount3;
}
function isInitializeMint2Instruction(decoded) {
  return decoded.data.instruction === TokenInstruction.InitializeMint2;
}
function isAmountToUiAmountInstruction(decoded) {
  return decoded.data.instruction === TokenInstruction.AmountToUiAmount;
}
function isUiamountToAmountInstruction(decoded) {
  return decoded.data.instruction === TokenInstruction.UiAmountToAmount;
}

// node_modules/@solana/spl-token/lib/esm/instructions/initializeImmutableOwner.js
var import_buffer_layout58 = __toESM(require_Layout(), 1);
init_index_browser_esm();
var initializeImmutableOwnerInstructionData = (0, import_buffer_layout58.struct)([
  (0, import_buffer_layout58.u8)("instruction")
]);
function createInitializeImmutableOwnerInstruction(account, programId) {
  const keys = [{ pubkey: account, isSigner: false, isWritable: true }];
  const data = Buffer.alloc(initializeImmutableOwnerInstructionData.span);
  initializeImmutableOwnerInstructionData.encode({
    instruction: TokenInstruction.InitializeImmutableOwner
  }, data);
  return new TransactionInstruction({ keys, programId, data });
}
function decodeInitializeImmutableOwnerInstruction(instruction, programId) {
  if (!instruction.programId.equals(programId))
    throw new TokenInvalidInstructionProgramError();
  if (instruction.data.length !== initializeImmutableOwnerInstructionData.span)
    throw new TokenInvalidInstructionDataError();
  const { keys: { account }, data } = decodeInitializeImmutableOwnerInstructionUnchecked(instruction);
  if (data.instruction !== TokenInstruction.InitializeImmutableOwner)
    throw new TokenInvalidInstructionTypeError();
  if (!account)
    throw new TokenInvalidInstructionKeysError();
  return {
    programId,
    keys: {
      account
    },
    data
  };
}
function decodeInitializeImmutableOwnerInstructionUnchecked({ programId, keys: [account], data }) {
  const { instruction } = initializeImmutableOwnerInstructionData.decode(data);
  return {
    programId,
    keys: {
      account
    },
    data: {
      instruction
    }
  };
}

// node_modules/@solana/spl-token/lib/esm/instructions/initializeMintCloseAuthority.js
var import_buffer_layout59 = __toESM(require_Layout(), 1);
init_index_browser_esm();
var initializeMintCloseAuthorityInstructionData = (0, import_buffer_layout59.struct)([
  (0, import_buffer_layout59.u8)("instruction"),
  new COptionPublicKeyLayout("closeAuthority")
]);
function createInitializeMintCloseAuthorityInstruction(mint, closeAuthority, programId) {
  if (!programSupportsExtensions(programId)) {
    throw new TokenUnsupportedInstructionError();
  }
  const keys = [{ pubkey: mint, isSigner: false, isWritable: true }];
  const data = Buffer.alloc(34);
  initializeMintCloseAuthorityInstructionData.encode({
    instruction: TokenInstruction.InitializeMintCloseAuthority,
    closeAuthority
  }, data);
  return new TransactionInstruction({
    keys,
    programId,
    data: data.subarray(0, initializeMintCloseAuthorityInstructionData.getSpan(data))
  });
}
function decodeInitializeMintCloseAuthorityInstruction(instruction, programId) {
  if (!instruction.programId.equals(programId))
    throw new TokenInvalidInstructionProgramError();
  if (instruction.data.length !== initializeMintCloseAuthorityInstructionData.getSpan(instruction.data))
    throw new TokenInvalidInstructionDataError();
  const { keys: { mint }, data } = decodeInitializeMintCloseAuthorityInstructionUnchecked(instruction);
  if (data.instruction !== TokenInstruction.InitializeMintCloseAuthority)
    throw new TokenInvalidInstructionTypeError();
  if (!mint)
    throw new TokenInvalidInstructionKeysError();
  return {
    programId,
    keys: {
      mint
    },
    data
  };
}
function decodeInitializeMintCloseAuthorityInstructionUnchecked({ programId, keys: [mint], data }) {
  const { instruction, closeAuthority } = initializeMintCloseAuthorityInstructionData.decode(data);
  return {
    programId,
    keys: {
      mint
    },
    data: {
      instruction,
      closeAuthority
    }
  };
}

// node_modules/@solana/spl-token/lib/esm/instructions/reallocate.js
var import_buffer_layout60 = __toESM(require_Layout(), 1);
init_index_browser_esm();
function createReallocateInstruction(account, payer, extensionTypes, owner, multiSigners = [], programId = TOKEN_2022_PROGRAM_ID) {
  if (!programSupportsExtensions(programId)) {
    throw new TokenUnsupportedInstructionError();
  }
  const baseKeys = [
    { pubkey: account, isSigner: false, isWritable: true },
    { pubkey: payer, isSigner: true, isWritable: true },
    { pubkey: SystemProgram.programId, isSigner: false, isWritable: false }
  ];
  const keys = addSigners(baseKeys, owner, multiSigners);
  const reallocateInstructionData = (0, import_buffer_layout60.struct)([
    (0, import_buffer_layout60.u8)("instruction"),
    (0, import_buffer_layout60.seq)((0, import_buffer_layout60.u16)(), extensionTypes.length, "extensionTypes")
  ]);
  const data = Buffer.alloc(reallocateInstructionData.span);
  reallocateInstructionData.encode({ instruction: TokenInstruction.Reallocate, extensionTypes }, data);
  return new TransactionInstruction({ keys, programId, data });
}

// node_modules/@solana/spl-token/lib/esm/instructions/initializeNonTransferableMint.js
var import_buffer_layout61 = __toESM(require_Layout(), 1);
init_index_browser_esm();
var initializeNonTransferableMintInstructionData = (0, import_buffer_layout61.struct)([
  (0, import_buffer_layout61.u8)("instruction")
]);
function createInitializeNonTransferableMintInstruction(mint, programId) {
  if (!programSupportsExtensions(programId)) {
    throw new TokenUnsupportedInstructionError();
  }
  const keys = [{ pubkey: mint, isSigner: false, isWritable: true }];
  const data = Buffer.alloc(initializeNonTransferableMintInstructionData.span);
  initializeNonTransferableMintInstructionData.encode({
    instruction: TokenInstruction.InitializeNonTransferableMint
  }, data);
  return new TransactionInstruction({ keys, programId, data });
}

// node_modules/@solana/spl-token/lib/esm/instructions/initializePermanentDelegate.js
var import_buffer_layout62 = __toESM(require_Layout(), 1);
init_index_browser_esm();
init_index_browser_esm();
var initializePermanentDelegateInstructionData = (0, import_buffer_layout62.struct)([
  (0, import_buffer_layout62.u8)("instruction"),
  publicKey("delegate")
]);
function createInitializePermanentDelegateInstruction(mint, permanentDelegate, programId) {
  if (!programSupportsExtensions(programId)) {
    throw new TokenUnsupportedInstructionError();
  }
  const keys = [{ pubkey: mint, isSigner: false, isWritable: true }];
  const data = Buffer.alloc(initializePermanentDelegateInstructionData.span);
  initializePermanentDelegateInstructionData.encode({
    instruction: TokenInstruction.InitializePermanentDelegate,
    delegate: permanentDelegate || new PublicKey(0)
  }, data);
  return new TransactionInstruction({ keys, programId, data });
}
function decodeInitializePermanentDelegateInstruction(instruction, programId) {
  if (!instruction.programId.equals(programId))
    throw new TokenInvalidInstructionProgramError();
  if (instruction.data.length !== initializePermanentDelegateInstructionData.span)
    throw new TokenInvalidInstructionDataError();
  const { keys: { mint }, data } = decodeInitializePermanentDelegateInstructionUnchecked(instruction);
  if (data.instruction !== TokenInstruction.InitializePermanentDelegate)
    throw new TokenInvalidInstructionTypeError();
  if (!mint)
    throw new TokenInvalidInstructionKeysError();
  return {
    programId,
    keys: {
      mint
    },
    data
  };
}
function decodeInitializePermanentDelegateInstructionUnchecked({ programId, keys: [mint], data }) {
  const { instruction, delegate } = initializePermanentDelegateInstructionData.decode(data);
  return {
    programId,
    keys: {
      mint
    },
    data: {
      instruction,
      delegate
    }
  };
}
export {
  ACCOUNT_SIZE,
  ACCOUNT_TYPE_SIZE,
  ASSOCIATED_TOKEN_PROGRAM_ID,
  AccountLayout,
  AccountState,
  AccountType,
  AuthorityType,
  CPI_GUARD_SIZE,
  CpiGuardInstruction,
  CpiGuardLayout,
  DEFAULT_ACCOUNT_STATE_SIZE,
  DefaultAccountStateInstruction,
  DefaultAccountStateLayout,
  ExtensionType,
  ExtraAccountMetaAccountDataLayout,
  ExtraAccountMetaLayout,
  ExtraAccountMetaListLayout,
  GROUP_MEMBER_POINTER_SIZE,
  GROUP_POINTER_SIZE,
  GroupMemberPointerInstruction,
  GroupMemberPointerLayout,
  GroupPointerInstruction,
  GroupPointerLayout,
  IMMUTABLE_OWNER_SIZE,
  INTEREST_BEARING_MINT_CONFIG_STATE_SIZE,
  ImmutableOwnerLayout,
  InterestBearingMintConfigStateLayout,
  InterestBearingMintInstruction,
  LENGTH_SIZE,
  MAX_FEE_BASIS_POINTS,
  MEMO_TRANSFER_SIZE,
  METADATA_POINTER_SIZE,
  MINT_CLOSE_AUTHORITY_SIZE,
  MINT_SIZE,
  MULTISIG_SIZE,
  MemoTransferInstruction,
  MemoTransferLayout,
  MetadataPointerInstruction,
  MetadataPointerLayout,
  MintCloseAuthorityLayout,
  MintLayout,
  MultisigLayout,
  NATIVE_MINT,
  NATIVE_MINT_2022,
  NON_TRANSFERABLE_ACCOUNT_SIZE,
  NON_TRANSFERABLE_SIZE,
  NonTransferableLayout,
  ONE_IN_BASIS_POINTS,
  PAUSABLE_ACCOUNT_SIZE,
  PAUSABLE_CONFIG_SIZE,
  PERMANENT_DELEGATE_SIZE,
  PausableAccountLayout,
  PausableConfigLayout,
  PausableInstruction,
  PermanentDelegateLayout,
  SCALED_UI_AMOUNT_CONFIG_SIZE,
  ScaledUiAmountConfigLayout,
  ScaledUiAmountInstruction,
  TOKEN_2022_PROGRAM_ID,
  TOKEN_GROUP_MEMBER_SIZE,
  TOKEN_GROUP_SIZE,
  TOKEN_PROGRAM_ID,
  TRANSFER_FEE_AMOUNT_SIZE,
  TRANSFER_FEE_CONFIG_SIZE,
  TRANSFER_HOOK_ACCOUNT_SIZE,
  TRANSFER_HOOK_SIZE,
  TYPE_SIZE,
  TokenAccountNotFoundError,
  TokenError,
  TokenInstruction,
  TokenInvalidAccountDataError,
  TokenInvalidAccountError,
  TokenInvalidAccountOwnerError,
  TokenInvalidAccountSizeError,
  TokenInvalidInstructionDataError,
  TokenInvalidInstructionKeysError,
  TokenInvalidInstructionProgramError,
  TokenInvalidInstructionTypeError,
  TokenInvalidMintError,
  TokenInvalidOwnerError,
  TokenOwnerOffCurveError,
  TokenTransferHookAccountDataNotFound,
  TokenTransferHookAccountNotFound,
  TokenTransferHookInvalidPubkeyData,
  TokenTransferHookInvalidSeed,
  TokenTransferHookPubkeyDataTooSmall,
  TokenUnsupportedInstructionError,
  TransferFeeAmountLayout,
  TransferFeeConfigLayout,
  TransferFeeInstruction,
  TransferHookAccountLayout,
  TransferHookInstruction,
  TransferHookLayout,
  addExtraAccountMetasForExecute,
  amountToUiAmount,
  amountToUiAmountForMintWithoutSimulation,
  amountToUiAmountInstructionData,
  amountToUiAmountWithoutSimulation,
  approve,
  approveChecked,
  approveCheckedInstructionData,
  approveInstructionData,
  burn,
  burnChecked,
  burnCheckedInstructionData,
  burnInstructionData,
  calculateEpochFee,
  calculateFee,
  closeAccount,
  closeAccountInstructionData,
  cpiGuardInstructionData,
  createAccount,
  createAmountToUiAmountInstruction,
  createApproveCheckedInstruction,
  createApproveInstruction,
  createAssociatedTokenAccount,
  createAssociatedTokenAccountIdempotent,
  createAssociatedTokenAccountIdempotentInstruction,
  createAssociatedTokenAccountIdempotentInstructionWithDerivation,
  createAssociatedTokenAccountInstruction,
  createBurnCheckedInstruction,
  createBurnInstruction,
  createCloseAccountInstruction,
  createCreateNativeMintInstruction,
  createDisableCpiGuardInstruction,
  createDisableRequiredMemoTransfersInstruction,
  createEmitInstruction,
  createEnableCpiGuardInstruction,
  createEnableRequiredMemoTransfersInstruction,
  createExecuteInstruction,
  createFreezeAccountInstruction,
  createHarvestWithheldTokensToMintInstruction,
  createInitializeAccount2Instruction,
  createInitializeAccount3Instruction,
  createInitializeAccountInstruction,
  createInitializeDefaultAccountStateInstruction,
  createInitializeGroupInstruction,
  createInitializeGroupMemberPointerInstruction,
  createInitializeGroupPointerInstruction,
  createInitializeImmutableOwnerInstruction,
  createInitializeInstruction,
  createInitializeInterestBearingMintInstruction,
  createInitializeMemberInstruction,
  createInitializeMetadataPointerInstruction,
  createInitializeMint2Instruction,
  createInitializeMintCloseAuthorityInstruction,
  createInitializeMintInstruction,
  createInitializeMultisigInstruction,
  createInitializeNonTransferableMintInstruction,
  createInitializePausableConfigInstruction,
  createInitializePermanentDelegateInstruction,
  createInitializeScaledUiAmountConfigInstruction,
  createInitializeTransferFeeConfigInstruction,
  createInitializeTransferHookInstruction,
  createInterestBearingMint,
  createMint,
  createMintToCheckedInstruction,
  createMintToInstruction,
  createMultisig,
  createNativeMint,
  createNativeMintInstructionData,
  createPauseInstruction,
  createReallocateInstruction,
  createRecoverNestedInstruction,
  createRemoveKeyInstruction,
  createResumeInstruction,
  createRevokeInstruction,
  createSetAuthorityInstruction,
  createSetTransferFeeInstruction,
  createSyncNativeInstruction,
  createThawAccountInstruction,
  createTransferCheckedInstruction,
  createTransferCheckedWithFeeAndTransferHookInstruction,
  createTransferCheckedWithFeeInstruction,
  createTransferCheckedWithTransferHookInstruction,
  createTransferInstruction,
  createUiAmountToAmountInstruction,
  createUpdateAuthorityInstruction,
  createUpdateDefaultAccountStateInstruction,
  createUpdateFieldInstruction,
  createUpdateGroupAuthorityInstruction,
  createUpdateGroupMaxSizeInstruction,
  createUpdateGroupMemberPointerInstruction,
  createUpdateGroupPointerInstruction,
  createUpdateMetadataPointerInstruction,
  createUpdateMultiplierDataInstruction,
  createUpdateRateInterestBearingMintInstruction,
  createUpdateTransferHookInstruction,
  createWithdrawWithheldTokensFromAccountsInstruction,
  createWithdrawWithheldTokensFromMintInstruction,
  createWrappedNativeAccount,
  decodeAmountToUiAmountInstruction,
  decodeAmountToUiAmountInstructionUnchecked,
  decodeApproveCheckedInstruction,
  decodeApproveCheckedInstructionUnchecked,
  decodeApproveInstruction,
  decodeApproveInstructionUnchecked,
  decodeBurnCheckedInstruction,
  decodeBurnCheckedInstructionUnchecked,
  decodeBurnInstruction,
  decodeBurnInstructionUnchecked,
  decodeCloseAccountInstruction,
  decodeCloseAccountInstructionUnchecked,
  decodeFreezeAccountInstruction,
  decodeFreezeAccountInstructionUnchecked,
  decodeHarvestWithheldTokensToMintInstruction,
  decodeHarvestWithheldTokensToMintInstructionUnchecked,
  decodeInitializeAccount2Instruction,
  decodeInitializeAccount2InstructionUnchecked,
  decodeInitializeAccount3Instruction,
  decodeInitializeAccount3InstructionUnchecked,
  decodeInitializeAccountInstruction,
  decodeInitializeAccountInstructionUnchecked,
  decodeInitializeImmutableOwnerInstruction,
  decodeInitializeImmutableOwnerInstructionUnchecked,
  decodeInitializeMint2Instruction,
  decodeInitializeMint2InstructionUnchecked,
  decodeInitializeMintCloseAuthorityInstruction,
  decodeInitializeMintCloseAuthorityInstructionUnchecked,
  decodeInitializeMintInstruction,
  decodeInitializeMintInstructionUnchecked,
  decodeInitializeMultisigInstruction,
  decodeInitializeMultisigInstructionUnchecked,
  decodeInitializePermanentDelegateInstruction,
  decodeInitializePermanentDelegateInstructionUnchecked,
  decodeInitializeTransferFeeConfigInstruction,
  decodeInitializeTransferFeeConfigInstructionUnchecked,
  decodeInstruction,
  decodeMintToCheckedInstruction,
  decodeMintToCheckedInstructionUnchecked,
  decodeMintToInstruction,
  decodeMintToInstructionUnchecked,
  decodeRevokeInstruction,
  decodeRevokeInstructionUnchecked,
  decodeSetAuthorityInstruction,
  decodeSetAuthorityInstructionUnchecked,
  decodeSetTransferFeeInstruction,
  decodeSetTransferFeeInstructionUnchecked,
  decodeSyncNativeInstruction,
  decodeSyncNativeInstructionUnchecked,
  decodeThawAccountInstruction,
  decodeThawAccountInstructionUnchecked,
  decodeTransferCheckedInstruction,
  decodeTransferCheckedInstructionUnchecked,
  decodeTransferCheckedWithFeeInstruction,
  decodeTransferCheckedWithFeeInstructionUnchecked,
  decodeTransferInstruction,
  decodeTransferInstructionUnchecked,
  decodeUiAmountToAmountInstruction,
  decodeUiAmountToAmountInstructionUnchecked,
  decodeWithdrawWithheldTokensFromAccountsInstruction,
  decodeWithdrawWithheldTokensFromAccountsInstructionUnchecked,
  decodeWithdrawWithheldTokensFromMintInstruction,
  decodeWithdrawWithheldTokensFromMintInstructionUnchecked,
  defaultAccountStateInstructionData,
  disableCpiGuard,
  disableRequiredMemoTransfers,
  enableCpiGuard,
  enableRequiredMemoTransfers,
  freezeAccount,
  freezeAccountInstructionData,
  getAccount,
  getAccountLen,
  getAccountLenForMint,
  getAccountTypeOfMintType,
  getAssociatedTokenAddress,
  getAssociatedTokenAddressSync,
  getCpiGuard,
  getDefaultAccountState,
  getEpochFee,
  getExtensionData,
  getExtensionTypes,
  getExtraAccountMetaAddress,
  getExtraAccountMetas,
  getGroupMemberPointerState,
  getGroupPointerState,
  getImmutableOwner,
  getInterestBearingMintConfigState,
  getMemoTransfer,
  getMetadataPointerState,
  getMinimumBalanceForRentExemptAccount,
  getMinimumBalanceForRentExemptAccountWithExtensions,
  getMinimumBalanceForRentExemptMint,
  getMinimumBalanceForRentExemptMintWithExtensions,
  getMinimumBalanceForRentExemptMultisig,
  getMint,
  getMintCloseAuthority,
  getMintLen,
  getMultipleAccounts,
  getMultisig,
  getNewAccountLenForExtensionLen,
  getNonTransferable,
  getNonTransferableAccount,
  getOrCreateAssociatedTokenAccount,
  getPausableAccount,
  getPausableConfig,
  getPermanentDelegate,
  getScaledUiAmountConfig,
  getTokenGroupMemberState,
  getTokenGroupState,
  getTokenMetadata,
  getTransferFeeAmount,
  getTransferFeeConfig,
  getTransferHook,
  getTransferHookAccount,
  getTypeLen,
  harvestWithheldTokensToMint,
  harvestWithheldTokensToMintInstructionData,
  initializeAccount2InstructionData,
  initializeAccount3InstructionData,
  initializeAccountInstructionData,
  initializeDefaultAccountState,
  initializeGroupMemberPointerData,
  initializeGroupPointerData,
  initializeImmutableOwnerInstructionData,
  initializeMetadataPointerData,
  initializeMint2InstructionData,
  initializeMintCloseAuthorityInstructionData,
  initializeMintInstructionData,
  initializeMultisigInstructionData,
  initializeNonTransferableMintInstructionData,
  initializePausableConfigInstructionData,
  initializePermanentDelegateInstructionData,
  initializeScaledUiAmountConfigInstructionData,
  initializeTransferFeeConfigInstructionData,
  initializeTransferHook,
  initializeTransferHookInstructionData,
  interestBearingMintInitializeInstructionData,
  interestBearingMintUpdateRateInstructionData,
  isAccountExtension,
  isAmountToUiAmountInstruction,
  isApproveCheckedInstruction,
  isApproveInstruction,
  isBurnCheckedInstruction,
  isBurnInstruction,
  isCloseAccountInstruction,
  isFreezeAccountInstruction,
  isInitializeAccount2Instruction,
  isInitializeAccount3Instruction,
  isInitializeAccountInstruction,
  isInitializeMint2Instruction,
  isInitializeMintInstruction,
  isInitializeMultisigInstruction,
  isMintExtension,
  isMintToCheckedInstruction,
  isMintToInstruction,
  isRevokeInstruction,
  isSetAuthorityInstruction,
  isSyncNativeInstruction,
  isThawAccountInstruction,
  isTransferCheckedInstruction,
  isTransferInstruction,
  isUiamountToAmountInstruction,
  memoTransferInstructionData,
  mintTo,
  mintToChecked,
  mintToCheckedInstructionData,
  mintToInstructionData,
  pause,
  pauseInstructionData,
  programSupportsExtensions,
  recoverNested,
  resolveExtraAccountMeta,
  resume,
  resumeInstructionData,
  revoke,
  revokeInstructionData,
  setAuthority,
  setAuthorityInstructionData,
  setTransferFee,
  setTransferFeeInstructionData,
  syncNative,
  syncNativeInstructionData,
  thawAccount,
  thawAccountInstructionData,
  tokenGroupInitializeGroup,
  tokenGroupInitializeGroupWithRentTransfer,
  tokenGroupMemberInitialize,
  tokenGroupMemberInitializeWithRentTransfer,
  tokenGroupUpdateGroupAuthority,
  tokenGroupUpdateGroupMaxSize,
  tokenMetadataInitialize,
  tokenMetadataInitializeWithRentTransfer,
  tokenMetadataRemoveKey,
  tokenMetadataUpdateAuthority,
  tokenMetadataUpdateField,
  tokenMetadataUpdateFieldWithRentTransfer,
  transfer,
  transferChecked,
  transferCheckedInstructionData,
  transferCheckedWithFee,
  transferCheckedWithFeeAndTransferHook,
  transferCheckedWithFeeInstructionData,
  transferCheckedWithTransferHook,
  transferFeeLayout,
  transferInstructionData,
  uiAmountToAmount,
  uiAmountToAmountForMintWithoutSimulation,
  uiAmountToAmountWithoutSimulation,
  unpackAccount,
  unpackMint,
  unpackMultisig,
  unpackPubkeyData,
  unpackSeeds,
  updateDefaultAccountState,
  updateGroupMemberPointerData,
  updateGroupPointerData,
  updateMetadataPointerData,
  updateMultiplier,
  updateMultiplierData,
  updateRateInterestBearingMint,
  updateTokenMetadata,
  updateTransferHook,
  updateTransferHookInstructionData,
  withdrawWithheldTokensFromAccounts,
  withdrawWithheldTokensFromAccountsInstructionData,
  withdrawWithheldTokensFromMint,
  withdrawWithheldTokensFromMintInstructionData
};
//# sourceMappingURL=@solana_spl-token.js.map
