{"version": 3, "sources": ["../../@solana/wallet-adapter-backpack/src/adapter.ts"], "sourcesContent": ["import type { EventEmitter, SendTransactionOptions, WalletName } from '@solana/wallet-adapter-base';\nimport {\n    BaseMessageSignerWalletAdapter,\n    scopePollingDetectionStrategy,\n    WalletAccountError,\n    WalletConnectionError,\n    WalletDisconnectedError,\n    WalletDisconnectionError,\n    WalletNotConnectedError,\n    WalletNotReadyError,\n    WalletPublicKeyError,\n    WalletReadyState,\n    WalletSendTransactionError,\n    WalletSignMessageError,\n    WalletSignTransactionError,\n} from '@solana/wallet-adapter-base';\nimport type { Connection, SendOptions, Signer, Transaction, TransactionSignature } from '@solana/web3.js';\nimport { PublicKey } from '@solana/web3.js';\n\ninterface BackpackWalletEvents {\n    connect(...args: unknown[]): unknown;\n    disconnect(...args: unknown[]): unknown;\n}\n\ninterface BackpackWallet extends EventEmitter<BackpackWalletEvents> {\n    isBackpack?: boolean;\n    publicKey?: { toBytes(): Uint8Array };\n    isConnected: boolean;\n    signTransaction(transaction: Transaction, publicKey?: PublicKey | null): Promise<Transaction>;\n    signAllTransactions(transactions: Transaction[], publicKey?: PublicKey | null): Promise<Transaction[]>;\n    send(\n        transaction: Transaction,\n        signers?: Signer[],\n        options?: SendOptions,\n        connection?: Connection,\n        publicKey?: PublicKey | null\n    ): Promise<TransactionSignature>;\n    signMessage(message: Uint8Array, publicKey?: PublicKey | null): Promise<Uint8Array>;\n    connect(): Promise<void>;\n    disconnect(): Promise<void>;\n}\n\ninterface BackpackWindow extends Window {\n    backpack?: BackpackWallet;\n}\n\ndeclare const window: BackpackWindow;\n\nexport interface BackpackWalletAdapterConfig {}\n\nexport const BackpackWalletName = 'Backpack' as WalletName<'Backpack'>;\n\nexport class BackpackWalletAdapter extends BaseMessageSignerWalletAdapter {\n    name = BackpackWalletName;\n    url = 'https://backpack.app';\n    icon =\n        'data:image/png;base64,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';\n    readonly supportedTransactionVersions = null;\n\n    private _connecting: boolean;\n    private _wallet: BackpackWallet | null;\n    private _publicKey: PublicKey | null;\n    private _readyState: WalletReadyState =\n        typeof window === 'undefined' || typeof document === 'undefined'\n            ? WalletReadyState.Unsupported\n            : WalletReadyState.NotDetected;\n\n    constructor(config: BackpackWalletAdapterConfig = {}) {\n        super();\n        this._connecting = false;\n        this._wallet = null;\n        this._publicKey = null;\n\n        if (this._readyState !== WalletReadyState.Unsupported) {\n            scopePollingDetectionStrategy(() => {\n                if (window.backpack?.isBackpack) {\n                    this._readyState = WalletReadyState.Installed;\n                    this.emit('readyStateChange', this._readyState);\n                    return true;\n                }\n                return false;\n            });\n        }\n    }\n\n    get publicKey() {\n        return this._publicKey;\n    }\n\n    get connecting() {\n        return this._connecting;\n    }\n\n    get connected() {\n        return !!this._wallet?.isConnected;\n    }\n\n    get readyState() {\n        return this._readyState;\n    }\n\n    async connect(): Promise<void> {\n        try {\n            if (this.connected || this.connecting) return;\n            if (this._readyState !== WalletReadyState.Installed) throw new WalletNotReadyError();\n\n            this._connecting = true;\n\n            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n            const wallet = window.backpack!;\n\n            try {\n                await wallet.connect();\n            } catch (error: any) {\n                throw new WalletConnectionError(error?.message, error);\n            }\n\n            if (!wallet.publicKey) throw new WalletAccountError();\n\n            let publicKey: PublicKey;\n            try {\n                publicKey = new PublicKey(wallet.publicKey.toBytes());\n            } catch (error: any) {\n                throw new WalletPublicKeyError(error?.message, error);\n            }\n\n            wallet.on('disconnect', this._disconnected);\n\n            this._wallet = wallet;\n            this._publicKey = publicKey;\n\n            this.emit('connect', publicKey);\n        } catch (error: any) {\n            this.emit('error', error);\n            throw error;\n        } finally {\n            this._connecting = false;\n        }\n    }\n\n    async disconnect(): Promise<void> {\n        const wallet = this._wallet;\n        if (wallet) {\n            wallet.off('disconnect', this._disconnected);\n\n            this._wallet = null;\n            this._publicKey = null;\n\n            try {\n                await wallet.disconnect();\n            } catch (error: any) {\n                this.emit('error', new WalletDisconnectionError(error?.message, error));\n            }\n        }\n\n        this.emit('disconnect');\n    }\n\n    async sendTransaction(\n        transaction: Transaction,\n        connection: Connection,\n        options: SendTransactionOptions = {}\n    ): Promise<TransactionSignature> {\n        try {\n            const wallet = this._wallet;\n            if (!wallet) throw new WalletNotConnectedError();\n\n            const { signers, ...sendOptions } = options;\n\n            try {\n                return await wallet.send(transaction, signers, sendOptions, connection, this.publicKey);\n            } catch (error: any) {\n                throw new WalletSendTransactionError(error?.message, error);\n            }\n        } catch (error: any) {\n            this.emit('error', error);\n            throw error;\n        }\n    }\n\n    async signTransaction<T extends Transaction>(transaction: T): Promise<T> {\n        try {\n            const wallet = this._wallet;\n            if (!wallet) throw new WalletNotConnectedError();\n\n            try {\n                return (await wallet.signTransaction(transaction, this.publicKey)) as T;\n            } catch (error: any) {\n                throw new WalletSignTransactionError(error?.message, error);\n            }\n        } catch (error: any) {\n            this.emit('error', error);\n            throw error;\n        }\n    }\n\n    async signAllTransactions<T extends Transaction>(transactions: T[]): Promise<T[]> {\n        try {\n            const wallet = this._wallet;\n            if (!wallet) throw new WalletNotConnectedError();\n\n            try {\n                return (await wallet.signAllTransactions(transactions, this.publicKey)) as T[];\n            } catch (error: any) {\n                throw new WalletSignTransactionError(error?.message, error);\n            }\n        } catch (error: any) {\n            this.emit('error', error);\n            throw error;\n        }\n    }\n\n    async signMessage(message: Uint8Array): Promise<Uint8Array> {\n        try {\n            const wallet = this._wallet;\n            if (!wallet) throw new WalletNotConnectedError();\n\n            try {\n                return await wallet.signMessage(message, this.publicKey);\n            } catch (error: any) {\n                throw new WalletSignMessageError(error?.message, error);\n            }\n        } catch (error: any) {\n            this.emit('error', error);\n            throw error;\n        }\n    }\n\n    private _disconnected = () => {\n        const wallet = this._wallet;\n        if (wallet) {\n            wallet.off('disconnect', this._disconnected);\n\n            this._wallet = null;\n            this._publicKey = null;\n\n            this.emit('error', new WalletDisconnectedError());\n            this.emit('disconnect');\n        }\n    };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBA;AAiCO,IAAM,qBAAqB;AAE5B,IAAO,wBAAP,cAAqC,+BAA8B;EAerE,YAAY,SAAsC,CAAA,GAAE;AAChD,UAAK;AAfT,SAAA,OAAO;AACP,SAAA,MAAM;AACN,SAAA,OACI;AACK,SAAA,+BAA+B;AAKhC,SAAA,cACJ,OAAO,WAAW,eAAe,OAAO,aAAa,cAC/C,iBAAiB,cACjB,iBAAiB;AAmKnB,SAAA,gBAAgB,MAAK;AACzB,YAAM,SAAS,KAAK;AACpB,UAAI,QAAQ;AACR,eAAO,IAAI,cAAc,KAAK,aAAa;AAE3C,aAAK,UAAU;AACf,aAAK,aAAa;AAElB,aAAK,KAAK,SAAS,IAAI,wBAAuB,CAAE;AAChD,aAAK,KAAK,YAAY;;IAE9B;AA1KI,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,SAAK,aAAa;AAElB,QAAI,KAAK,gBAAgB,iBAAiB,aAAa;AACnD,oCAA8B,MAAK;AAzE/C;AA0EgB,aAAI,YAAO,aAAP,mBAAiB,YAAY;AAC7B,eAAK,cAAc,iBAAiB;AACpC,eAAK,KAAK,oBAAoB,KAAK,WAAW;AAC9C,iBAAO;;AAEX,eAAO;MACX,CAAC;;EAET;EAEA,IAAI,YAAS;AACT,WAAO,KAAK;EAChB;EAEA,IAAI,aAAU;AACV,WAAO,KAAK;EAChB;EAEA,IAAI,YAAS;AA5FjB;AA6FQ,WAAO,CAAC,GAAC,UAAK,YAAL,mBAAc;EAC3B;EAEA,IAAI,aAAU;AACV,WAAO,KAAK;EAChB;EAEA,MAAM,UAAO;AACT,QAAI;AACA,UAAI,KAAK,aAAa,KAAK;AAAY;AACvC,UAAI,KAAK,gBAAgB,iBAAiB;AAAW,cAAM,IAAI,oBAAmB;AAElF,WAAK,cAAc;AAGnB,YAAM,SAAS,OAAO;AAEtB,UAAI;AACA,cAAM,OAAO,QAAO;eACf,OAAY;AACjB,cAAM,IAAI,sBAAsB,+BAAO,SAAS,KAAK;;AAGzD,UAAI,CAAC,OAAO;AAAW,cAAM,IAAI,mBAAkB;AAEnD,UAAI;AACJ,UAAI;AACA,oBAAY,IAAI,UAAU,OAAO,UAAU,QAAO,CAAE;eAC/C,OAAY;AACjB,cAAM,IAAI,qBAAqB,+BAAO,SAAS,KAAK;;AAGxD,aAAO,GAAG,cAAc,KAAK,aAAa;AAE1C,WAAK,UAAU;AACf,WAAK,aAAa;AAElB,WAAK,KAAK,WAAW,SAAS;aACzB,OAAY;AACjB,WAAK,KAAK,SAAS,KAAK;AACxB,YAAM;;AAEN,WAAK,cAAc;;EAE3B;EAEA,MAAM,aAAU;AACZ,UAAM,SAAS,KAAK;AACpB,QAAI,QAAQ;AACR,aAAO,IAAI,cAAc,KAAK,aAAa;AAE3C,WAAK,UAAU;AACf,WAAK,aAAa;AAElB,UAAI;AACA,cAAM,OAAO,WAAU;eAClB,OAAY;AACjB,aAAK,KAAK,SAAS,IAAI,yBAAyB,+BAAO,SAAS,KAAK,CAAC;;;AAI9E,SAAK,KAAK,YAAY;EAC1B;EAEA,MAAM,gBACF,aACA,YACA,UAAkC,CAAA,GAAE;AAEpC,QAAI;AACA,YAAM,SAAS,KAAK;AACpB,UAAI,CAAC;AAAQ,cAAM,IAAI,wBAAuB;AAE9C,YAAM,EAAE,SAAS,GAAG,YAAW,IAAK;AAEpC,UAAI;AACA,eAAO,MAAM,OAAO,KAAK,aAAa,SAAS,aAAa,YAAY,KAAK,SAAS;eACjF,OAAY;AACjB,cAAM,IAAI,2BAA2B,+BAAO,SAAS,KAAK;;aAEzD,OAAY;AACjB,WAAK,KAAK,SAAS,KAAK;AACxB,YAAM;;EAEd;EAEA,MAAM,gBAAuC,aAAc;AACvD,QAAI;AACA,YAAM,SAAS,KAAK;AACpB,UAAI,CAAC;AAAQ,cAAM,IAAI,wBAAuB;AAE9C,UAAI;AACA,eAAQ,MAAM,OAAO,gBAAgB,aAAa,KAAK,SAAS;eAC3D,OAAY;AACjB,cAAM,IAAI,2BAA2B,+BAAO,SAAS,KAAK;;aAEzD,OAAY;AACjB,WAAK,KAAK,SAAS,KAAK;AACxB,YAAM;;EAEd;EAEA,MAAM,oBAA2C,cAAiB;AAC9D,QAAI;AACA,YAAM,SAAS,KAAK;AACpB,UAAI,CAAC;AAAQ,cAAM,IAAI,wBAAuB;AAE9C,UAAI;AACA,eAAQ,MAAM,OAAO,oBAAoB,cAAc,KAAK,SAAS;eAChE,OAAY;AACjB,cAAM,IAAI,2BAA2B,+BAAO,SAAS,KAAK;;aAEzD,OAAY;AACjB,WAAK,KAAK,SAAS,KAAK;AACxB,YAAM;;EAEd;EAEA,MAAM,YAAY,SAAmB;AACjC,QAAI;AACA,YAAM,SAAS,KAAK;AACpB,UAAI,CAAC;AAAQ,cAAM,IAAI,wBAAuB;AAE9C,UAAI;AACA,eAAO,MAAM,OAAO,YAAY,SAAS,KAAK,SAAS;eAClD,OAAY;AACjB,cAAM,IAAI,uBAAuB,+BAAO,SAAS,KAAK;;aAErD,OAAY;AACjB,WAAK,KAAK,SAAS,KAAK;AACxB,YAAM;;EAEd;;", "names": []}