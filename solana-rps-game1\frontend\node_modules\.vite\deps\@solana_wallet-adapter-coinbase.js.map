{"version": 3, "sources": ["../../@solana/wallet-adapter-coinbase/src/adapter.ts"], "sourcesContent": ["import type { EventEmitter, SendTransactionOptions, WalletName } from '@solana/wallet-adapter-base';\nimport {\n    BaseMessageSignerWalletAdapter,\n    scopePollingDetectionStrategy,\n    WalletAccountError,\n    WalletConnectionError,\n    WalletDisconnectedError,\n    WalletDisconnectionError,\n    WalletError,\n    WalletNotConnectedError,\n    WalletNotReadyError,\n    WalletPublicKeyError,\n    WalletReadyState,\n    WalletSendTransactionError,\n    WalletSignTransactionError,\n} from '@solana/wallet-adapter-base';\nimport type { Connection, SendOptions, Transaction, TransactionSignature } from '@solana/web3.js';\nimport { PublicKey } from '@solana/web3.js';\n\ninterface CoinbaseWalletEvents {\n    connect(...args: unknown[]): unknown;\n    disconnect(...args: unknown[]): unknown;\n}\n\ninterface CoinbaseWallet extends EventEmitter<CoinbaseWalletEvents> {\n    publicKey?: PublicKey;\n    signTransaction(transaction: Transaction): Promise<Transaction>;\n    signAllTransactions(transactions: Transaction[]): Promise<Transaction[]>;\n    signAndSendTransaction(\n        transaction: Transaction,\n        options?: SendOptions\n    ): Promise<{ signature: TransactionSignature }>;\n    signMessage(message: Uint8Array): Promise<{ signature: Uint8Array }>;\n    connect(): Promise<void>;\n    disconnect(): Promise<void>;\n}\n\ninterface CoinbaseWindow extends Window {\n    coinbaseSolana?: CoinbaseWallet;\n}\n\ndeclare const window: CoinbaseWindow;\n\nexport interface CoinbaseWalletAdapterConfig {}\n\nexport const CoinbaseWalletName = 'Coinbase Wallet' as WalletName<'Coinbase Wallet'>;\n\nexport class CoinbaseWalletAdapter extends BaseMessageSignerWalletAdapter {\n    name = CoinbaseWalletName;\n    url = 'https://chrome.google.com/webstore/detail/coinbase-wallet-extension/hnfanknocfeofbddgcijnmhnfnkdnaad';\n    icon =\n        'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAyNCIgaGVpZ2h0PSIxMDI0IiB2aWV3Qm94PSIwIDAgMTAyNCAxMDI0IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8Y2lyY2xlIGN4PSI1MTIiIGN5PSI1MTIiIHI9IjUxMiIgZmlsbD0iIzAwNTJGRiIvPgo8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTE1MiA1MTJDMTUyIDcxMC44MjMgMzEzLjE3NyA4NzIgNTEyIDg3MkM3MTAuODIzIDg3MiA4NzIgNzEwLjgyMyA4NzIgNTEyQzg3MiAzMTMuMTc3IDcxMC44MjMgMTUyIDUxMiAxNTJDMzEzLjE3NyAxNTIgMTUyIDMxMy4xNzcgMTUyIDUxMlpNNDIwIDM5NkM0MDYuNzQ1IDM5NiAzOTYgNDA2Ljc0NSAzOTYgNDIwVjYwNEMzOTYgNjE3LjI1NSA0MDYuNzQ1IDYyOCA0MjAgNjI4SDYwNEM2MTcuMjU1IDYyOCA2MjggNjE3LjI1NSA2MjggNjA0VjQyMEM2MjggNDA2Ljc0NSA2MTcuMjU1IDM5NiA2MDQgMzk2SDQyMFoiIGZpbGw9IndoaXRlIi8+Cjwvc3ZnPgo=';\n    readonly supportedTransactionVersions = null;\n\n    private _connecting: boolean;\n    private _wallet: CoinbaseWallet | null;\n    private _publicKey: PublicKey | null;\n    private _readyState: WalletReadyState =\n        typeof window === 'undefined' || typeof document === 'undefined'\n            ? WalletReadyState.Unsupported\n            : WalletReadyState.NotDetected;\n\n    constructor(config: CoinbaseWalletAdapterConfig = {}) {\n        super();\n        this._connecting = false;\n        this._wallet = null;\n        this._publicKey = null;\n\n        if (this._readyState !== WalletReadyState.Unsupported) {\n            scopePollingDetectionStrategy(() => {\n                if (window?.coinbaseSolana) {\n                    this._readyState = WalletReadyState.Installed;\n                    this.emit('readyStateChange', this._readyState);\n                    return true;\n                }\n                return false;\n            });\n        }\n    }\n\n    get publicKey() {\n        return this._publicKey;\n    }\n\n    get connecting() {\n        return this._connecting;\n    }\n\n    get readyState() {\n        return this._readyState;\n    }\n\n    async connect(): Promise<void> {\n        try {\n            if (this.connected || this.connecting) return;\n            if (this._readyState !== WalletReadyState.Installed) throw new WalletNotReadyError();\n\n            this._connecting = true;\n\n            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n            const wallet = window.coinbaseSolana!;\n\n            try {\n                await wallet.connect();\n            } catch (error: any) {\n                throw new WalletConnectionError(error?.message, error);\n            }\n\n            if (!wallet.publicKey) throw new WalletAccountError();\n\n            let publicKey: PublicKey;\n            try {\n                publicKey = new PublicKey(wallet.publicKey.toBytes());\n            } catch (error: any) {\n                throw new WalletPublicKeyError(error?.message, error);\n            }\n\n            wallet.on('disconnect', this._disconnected);\n\n            this._wallet = wallet;\n            this._publicKey = publicKey;\n\n            this.emit('connect', publicKey);\n        } catch (error: any) {\n            this.emit('error', error);\n            throw error;\n        } finally {\n            this._connecting = false;\n        }\n    }\n\n    async disconnect(): Promise<void> {\n        const wallet = this._wallet;\n        if (wallet) {\n            wallet.off('disconnect', this._disconnected);\n\n            this._wallet = null;\n            this._publicKey = null;\n\n            try {\n                await wallet.disconnect();\n            } catch (error: any) {\n                this.emit('error', new WalletDisconnectionError(error?.message, error));\n            }\n        }\n\n        this.emit('disconnect');\n    }\n\n    async sendTransaction(\n        transaction: Transaction,\n        connection: Connection,\n        options: SendTransactionOptions = {}\n    ): Promise<TransactionSignature> {\n        try {\n            const wallet = this._wallet;\n            if (!wallet) throw new WalletNotConnectedError();\n\n            try {\n                const { signers, ...sendOptions } = options;\n\n                transaction = await this.prepareTransaction(transaction, connection, sendOptions);\n\n                signers?.length && transaction.partialSign(...signers);\n\n                sendOptions.preflightCommitment = sendOptions.preflightCommitment || connection.commitment;\n\n                const { signature } = await wallet.signAndSendTransaction(transaction, sendOptions);\n                return signature;\n            } catch (error: any) {\n                if (error instanceof WalletError) throw error;\n                throw new WalletSendTransactionError(error?.message, error);\n            }\n        } catch (error: any) {\n            this.emit('error', error);\n            throw error;\n        }\n    }\n\n    async signTransaction<T extends Transaction>(transaction: T): Promise<T> {\n        try {\n            const wallet = this._wallet;\n            if (!wallet) throw new WalletNotConnectedError();\n\n            try {\n                return ((await wallet.signTransaction(transaction)) as T) || transaction;\n            } catch (error: any) {\n                throw new WalletSignTransactionError(error?.message, error);\n            }\n        } catch (error: any) {\n            this.emit('error', error);\n            throw error;\n        }\n    }\n\n    async signAllTransactions<T extends Transaction>(transactions: T[]): Promise<T[]> {\n        try {\n            const wallet = this._wallet;\n            if (!wallet) throw new WalletNotConnectedError();\n\n            try {\n                return ((await wallet.signAllTransactions(transactions)) as T[]) || transactions;\n            } catch (error: any) {\n                throw new WalletSignTransactionError(error?.message, error);\n            }\n        } catch (error: any) {\n            this.emit('error', error);\n            throw error;\n        }\n    }\n\n    async signMessage(message: Uint8Array): Promise<Uint8Array> {\n        try {\n            const wallet = this._wallet;\n            if (!wallet) throw new WalletNotConnectedError();\n\n            try {\n                const { signature } = await wallet.signMessage(message);\n                return signature;\n            } catch (error: any) {\n                throw new WalletSignTransactionError(error?.message, error);\n            }\n        } catch (error: any) {\n            this.emit('error', error);\n            throw error;\n        }\n    }\n\n    private _disconnected = () => {\n        const wallet = this._wallet;\n        if (wallet) {\n            wallet.off('disconnect', this._disconnected);\n\n            this._wallet = null;\n            this._publicKey = null;\n\n            this.emit('error', new WalletDisconnectedError());\n            this.emit('disconnect');\n        }\n    };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBA;AA4BO,IAAM,qBAAqB;AAE5B,IAAO,wBAAP,cAAqC,+BAA8B;EAerE,YAAY,SAAsC,CAAA,GAAE;AAChD,UAAK;AAfT,SAAA,OAAO;AACP,SAAA,MAAM;AACN,SAAA,OACI;AACK,SAAA,+BAA+B;AAKhC,SAAA,cACJ,OAAO,WAAW,eAAe,OAAO,aAAa,cAC/C,iBAAiB,cACjB,iBAAiB;AAwKnB,SAAA,gBAAgB,MAAK;AACzB,YAAM,SAAS,KAAK;AACpB,UAAI,QAAQ;AACR,eAAO,IAAI,cAAc,KAAK,aAAa;AAE3C,aAAK,UAAU;AACf,aAAK,aAAa;AAElB,aAAK,KAAK,SAAS,IAAI,wBAAuB,CAAE;AAChD,aAAK,KAAK,YAAY;;IAE9B;AA/KI,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,SAAK,aAAa;AAElB,QAAI,KAAK,gBAAgB,iBAAiB,aAAa;AACnD,oCAA8B,MAAK;AAC/B,YAAI,iCAAQ,gBAAgB;AACxB,eAAK,cAAc,iBAAiB;AACpC,eAAK,KAAK,oBAAoB,KAAK,WAAW;AAC9C,iBAAO;;AAEX,eAAO;MACX,CAAC;;EAET;EAEA,IAAI,YAAS;AACT,WAAO,KAAK;EAChB;EAEA,IAAI,aAAU;AACV,WAAO,KAAK;EAChB;EAEA,IAAI,aAAU;AACV,WAAO,KAAK;EAChB;EAEA,MAAM,UAAO;AACT,QAAI;AACA,UAAI,KAAK,aAAa,KAAK;AAAY;AACvC,UAAI,KAAK,gBAAgB,iBAAiB;AAAW,cAAM,IAAI,oBAAmB;AAElF,WAAK,cAAc;AAGnB,YAAM,SAAS,OAAO;AAEtB,UAAI;AACA,cAAM,OAAO,QAAO;eACf,OAAY;AACjB,cAAM,IAAI,sBAAsB,+BAAO,SAAS,KAAK;;AAGzD,UAAI,CAAC,OAAO;AAAW,cAAM,IAAI,mBAAkB;AAEnD,UAAI;AACJ,UAAI;AACA,oBAAY,IAAI,UAAU,OAAO,UAAU,QAAO,CAAE;eAC/C,OAAY;AACjB,cAAM,IAAI,qBAAqB,+BAAO,SAAS,KAAK;;AAGxD,aAAO,GAAG,cAAc,KAAK,aAAa;AAE1C,WAAK,UAAU;AACf,WAAK,aAAa;AAElB,WAAK,KAAK,WAAW,SAAS;aACzB,OAAY;AACjB,WAAK,KAAK,SAAS,KAAK;AACxB,YAAM;;AAEN,WAAK,cAAc;;EAE3B;EAEA,MAAM,aAAU;AACZ,UAAM,SAAS,KAAK;AACpB,QAAI,QAAQ;AACR,aAAO,IAAI,cAAc,KAAK,aAAa;AAE3C,WAAK,UAAU;AACf,WAAK,aAAa;AAElB,UAAI;AACA,cAAM,OAAO,WAAU;eAClB,OAAY;AACjB,aAAK,KAAK,SAAS,IAAI,yBAAyB,+BAAO,SAAS,KAAK,CAAC;;;AAI9E,SAAK,KAAK,YAAY;EAC1B;EAEA,MAAM,gBACF,aACA,YACA,UAAkC,CAAA,GAAE;AAEpC,QAAI;AACA,YAAM,SAAS,KAAK;AACpB,UAAI,CAAC;AAAQ,cAAM,IAAI,wBAAuB;AAE9C,UAAI;AACA,cAAM,EAAE,SAAS,GAAG,YAAW,IAAK;AAEpC,sBAAc,MAAM,KAAK,mBAAmB,aAAa,YAAY,WAAW;AAEhF,4CAAS,WAAU,YAAY,YAAY,GAAG,OAAO;AAErD,oBAAY,sBAAsB,YAAY,uBAAuB,WAAW;AAEhF,cAAM,EAAE,UAAS,IAAK,MAAM,OAAO,uBAAuB,aAAa,WAAW;AAClF,eAAO;eACF,OAAY;AACjB,YAAI,iBAAiB;AAAa,gBAAM;AACxC,cAAM,IAAI,2BAA2B,+BAAO,SAAS,KAAK;;aAEzD,OAAY;AACjB,WAAK,KAAK,SAAS,KAAK;AACxB,YAAM;;EAEd;EAEA,MAAM,gBAAuC,aAAc;AACvD,QAAI;AACA,YAAM,SAAS,KAAK;AACpB,UAAI,CAAC;AAAQ,cAAM,IAAI,wBAAuB;AAE9C,UAAI;AACA,eAAS,MAAM,OAAO,gBAAgB,WAAW,KAAY;eACxD,OAAY;AACjB,cAAM,IAAI,2BAA2B,+BAAO,SAAS,KAAK;;aAEzD,OAAY;AACjB,WAAK,KAAK,SAAS,KAAK;AACxB,YAAM;;EAEd;EAEA,MAAM,oBAA2C,cAAiB;AAC9D,QAAI;AACA,YAAM,SAAS,KAAK;AACpB,UAAI,CAAC;AAAQ,cAAM,IAAI,wBAAuB;AAE9C,UAAI;AACA,eAAS,MAAM,OAAO,oBAAoB,YAAY,KAAc;eAC/D,OAAY;AACjB,cAAM,IAAI,2BAA2B,+BAAO,SAAS,KAAK;;aAEzD,OAAY;AACjB,WAAK,KAAK,SAAS,KAAK;AACxB,YAAM;;EAEd;EAEA,MAAM,YAAY,SAAmB;AACjC,QAAI;AACA,YAAM,SAAS,KAAK;AACpB,UAAI,CAAC;AAAQ,cAAM,IAAI,wBAAuB;AAE9C,UAAI;AACA,cAAM,EAAE,UAAS,IAAK,MAAM,OAAO,YAAY,OAAO;AACtD,eAAO;eACF,OAAY;AACjB,cAAM,IAAI,2BAA2B,+BAAO,SAAS,KAAK;;aAEzD,OAAY;AACjB,WAAK,KAAK,SAAS,KAAK;AACxB,YAAM;;EAEd;;", "names": []}