import {
  StatusCodes,
  TransportStatusError
} from "./chunk-ZQ7UTWHO.js";
import {
  BaseSignerWalletAdapter,
  WalletConnectionError,
  WalletDisconnectedError,
  WalletDisconnectionError,
  WalletLoadError,
  WalletNotConnectedError,
  WalletNotReadyError,
  WalletPublicKeyError,
  WalletReadyState,
  WalletSignTransactionError,
  isVersionedTransaction
} from "./chunk-6SC6RHPY.js";
import "./chunk-42XXHGZT.js";
import {
  PublicKey,
  init_index_browser_esm
} from "./chunk-EMSKGLQ5.js";
import "./chunk-SUZE37AV.js";
import "./chunk-37HUACP4.js";
import "./chunk-E7YD6LZS.js";
import {
  require_buffer
} from "./chunk-LG344HM7.js";
import {
  __toESM
} from "./chunk-WXXH56N5.js";

// node_modules/@solana/wallet-adapter-ledger/lib/esm/polyfills/Buffer.js
var import_buffer = __toESM(require_buffer(), 1);
if (typeof window !== "undefined" && window.Buffer === void 0) {
  window.Buffer = import_buffer.Buffer;
}

// node_modules/@solana/wallet-adapter-ledger/lib/esm/util.js
init_index_browser_esm();
function getDerivationPath(account, change) {
  const length = account !== void 0 ? change === void 0 ? 3 : 4 : 2;
  const derivationPath = Buffer.alloc(1 + length * 4);
  let offset = derivationPath.writeUInt8(length, 0);
  offset = derivationPath.writeUInt32BE(harden(44), offset);
  offset = derivationPath.writeUInt32BE(harden(501), offset);
  if (account !== void 0) {
    offset = derivationPath.writeUInt32BE(harden(account), offset);
    if (change !== void 0) {
      derivationPath.writeUInt32BE(harden(change), offset);
    }
  }
  return derivationPath;
}
var BIP32_HARDENED_BIT = 1 << 31 >>> 0;
function harden(n) {
  return (n | BIP32_HARDENED_BIT) >>> 0;
}
var INS_GET_PUBKEY = 5;
var INS_SIGN_MESSAGE = 6;
var P1_NON_CONFIRM = 0;
var P1_CONFIRM = 1;
var P2_EXTEND = 1;
var P2_MORE = 2;
var MAX_PAYLOAD = 255;
var LEDGER_CLA = 224;
async function getPublicKey(transport, derivationPath) {
  const bytes = await send(transport, INS_GET_PUBKEY, P1_NON_CONFIRM, derivationPath);
  return new PublicKey(bytes);
}
async function signTransaction(transport, transaction, derivationPath) {
  const paths = Buffer.alloc(1);
  paths.writeUInt8(1, 0);
  const message = isVersionedTransaction(transaction) ? transaction.message.serialize() : transaction.serializeMessage();
  const data = Buffer.concat([paths, derivationPath, message]);
  return await send(transport, INS_SIGN_MESSAGE, P1_CONFIRM, data);
}
async function send(transport, instruction, p1, data) {
  let p2 = 0;
  let offset = 0;
  if (data.length > MAX_PAYLOAD) {
    while (data.length - offset > MAX_PAYLOAD) {
      const buffer2 = data.slice(offset, offset + MAX_PAYLOAD);
      const response2 = await transport.send(LEDGER_CLA, instruction, p1, p2 | P2_MORE, buffer2);
      if (response2.length !== 2)
        throw new TransportStatusError(StatusCodes.INCORRECT_DATA);
      p2 |= P2_EXTEND;
      offset += MAX_PAYLOAD;
    }
  }
  const buffer = data.slice(offset);
  const response = await transport.send(LEDGER_CLA, instruction, p1, p2, buffer);
  return response.slice(0, response.length - 2);
}

// node_modules/@solana/wallet-adapter-ledger/lib/esm/adapter.js
var LedgerWalletName = "Ledger";
var LedgerWalletAdapter = class extends BaseSignerWalletAdapter {
  constructor(config = {}) {
    super();
    this.name = LedgerWalletName;
    this.url = "https://ledger.com";
    this.icon = "data:image/svg+xml;base64,PHN2ZyB2aWV3Qm94PSIwIDAgMzUgMzUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0iI2ZmZiI+PHBhdGggZD0ibTIzLjU4OCAwaC0xNnYyMS41ODNoMjEuNnYtMTZhNS41ODUgNS41ODUgMCAwIDAgLTUuNi01LjU4M3oiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDUuNzM5KSIvPjxwYXRoIGQ9Im04LjM0MiAwaC0yLjc1N2E1LjU4NSA1LjU4NSAwIDAgMCAtNS41ODUgNS41ODV2Mi43NTdoOC4zNDJ6Ii8+PHBhdGggZD0ibTAgNy41OWg4LjM0MnY4LjM0MmgtOC4zNDJ6IiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwIDUuNzM5KSIvPjxwYXRoIGQ9Im0xNS4xOCAyMy40NTFoMi43NTdhNS41ODUgNS41ODUgMCAwIDAgNS41ODUtNS42di0yLjY3MWgtOC4zNDJ6IiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgxMS40NzggMTEuNDc4KSIvPjxwYXRoIGQ9Im03LjU5IDE1LjE4aDguMzQydjguMzQyaC04LjM0MnoiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDUuNzM5IDExLjQ3OCkiLz48cGF0aCBkPSJtMCAxNS4xOHYyLjc1N2E1LjU4NSA1LjU4NSAwIDAgMCA1LjU4NSA1LjU4NWgyLjc1N3YtOC4zNDJ6IiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwIDExLjQ3OCkiLz48L2c+PC9zdmc+";
    this.supportedTransactionVersions = /* @__PURE__ */ new Set(["legacy", 0]);
    this._readyState = typeof window === "undefined" || typeof document === "undefined" || typeof navigator === "undefined" || !navigator.hid ? WalletReadyState.Unsupported : WalletReadyState.Loadable;
    this._disconnected = () => {
      const transport = this._transport;
      if (transport) {
        transport.off("disconnect", this._disconnected);
        this._transport = null;
        this._publicKey = null;
        this.emit("error", new WalletDisconnectedError());
        this.emit("disconnect");
      }
    };
    this._derivationPath = config.derivationPath || getDerivationPath(0, 0);
    this._connecting = false;
    this._transport = null;
    this._publicKey = null;
  }
  get publicKey() {
    return this._publicKey;
  }
  get connecting() {
    return this._connecting;
  }
  get readyState() {
    return this._readyState;
  }
  async connect() {
    try {
      if (this.connected || this.connecting)
        return;
      if (this._readyState !== WalletReadyState.Loadable)
        throw new WalletNotReadyError();
      this._connecting = true;
      let TransportWebHIDClass;
      try {
        TransportWebHIDClass = (await import("./TransportWebHID-JNYYGLD5.js")).default;
      } catch (error) {
        throw new WalletLoadError(error == null ? void 0 : error.message, error);
      }
      let transport;
      try {
        transport = await TransportWebHIDClass.create();
      } catch (error) {
        throw new WalletConnectionError(error == null ? void 0 : error.message, error);
      }
      let publicKey;
      try {
        publicKey = await getPublicKey(transport, this._derivationPath);
      } catch (error) {
        throw new WalletPublicKeyError(error == null ? void 0 : error.message, error);
      }
      transport.on("disconnect", this._disconnected);
      this._transport = transport;
      this._publicKey = publicKey;
      this.emit("connect", publicKey);
    } catch (error) {
      this.emit("error", error);
      throw error;
    } finally {
      this._connecting = false;
    }
  }
  async disconnect() {
    const transport = this._transport;
    if (transport) {
      transport.off("disconnect", this._disconnected);
      this._transport = null;
      this._publicKey = null;
      try {
        await transport.close();
      } catch (error) {
        this.emit("error", new WalletDisconnectionError(error == null ? void 0 : error.message, error));
      }
    }
    this.emit("disconnect");
  }
  async signTransaction(transaction) {
    try {
      const transport = this._transport;
      const publicKey = this._publicKey;
      if (!transport || !publicKey)
        throw new WalletNotConnectedError();
      try {
        const signature = await signTransaction(transport, transaction, this._derivationPath);
        transaction.addSignature(publicKey, signature);
      } catch (error) {
        throw new WalletSignTransactionError(error == null ? void 0 : error.message, error);
      }
      return transaction;
    } catch (error) {
      this.emit("error", error);
      throw error;
    }
  }
};
export {
  LedgerWalletAdapter,
  LedgerWalletName,
  getDerivationPath
};
//# sourceMappingURL=@solana_wallet-adapter-ledger.js.map
