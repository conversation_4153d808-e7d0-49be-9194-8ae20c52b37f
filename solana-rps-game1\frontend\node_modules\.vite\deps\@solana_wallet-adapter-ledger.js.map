{"version": 3, "sources": ["../../@solana/wallet-adapter-ledger/src/polyfills/Buffer.ts", "../../@solana/wallet-adapter-ledger/src/util.ts", "../../@solana/wallet-adapter-ledger/src/adapter.ts"], "sourcesContent": ["import { Buffer } from 'buffer';\n\nif (typeof window !== 'undefined' && window.Buffer === undefined) {\n    (window as any).Buffer = Buffer;\n}\n\nexport {};\n", "import type { default as Transport } from '@ledgerhq/hw-transport';\nimport { StatusCodes, TransportStatusError } from '@ledgerhq/hw-transport';\nimport { isVersionedTransaction } from '@solana/wallet-adapter-base';\nimport type { Transaction, VersionedTransaction } from '@solana/web3.js';\nimport { PublicKey } from '@solana/web3.js';\nimport './polyfills/index.js';\n\nexport function getDerivationPath(account?: number, change?: number): Buffer {\n    const length = account !== undefined ? (change === undefined ? 3 : 4) : 2;\n    const derivationPath = Buffer.alloc(1 + length * 4);\n\n    let offset = derivationPath.writeUInt8(length, 0);\n    offset = derivationPath.writeUInt32BE(harden(44), offset); // Using BIP44\n    offset = derivationPath.writeUInt32BE(harden(501), offset); // Solana's BIP44 path\n\n    if (account !== undefined) {\n        offset = derivationPath.writeUInt32BE(harden(account), offset);\n        if (change !== undefined) {\n            derivationPath.writeUInt32BE(harden(change), offset);\n        }\n    }\n\n    return derivationPath;\n}\n\nconst BIP32_HARDENED_BIT = (1 << 31) >>> 0;\n\nfunction harden(n: number): number {\n    return (n | BIP32_HARDENED_BIT) >>> 0;\n}\n\nconst INS_GET_PUBKEY = 0x05;\nconst INS_SIGN_MESSAGE = 0x06;\n\nconst P1_NON_CONFIRM = 0x00;\nconst P1_CONFIRM = 0x01;\n\nconst P2_EXTEND = 0x01;\nconst P2_MORE = 0x02;\n\nconst MAX_PAYLOAD = 255;\n\nconst LEDGER_CLA = 0xe0;\n\n/** @internal */\nexport async function getPublicKey(transport: Transport, derivationPath: Buffer): Promise<PublicKey> {\n    const bytes = await send(transport, INS_GET_PUBKEY, P1_NON_CONFIRM, derivationPath);\n    return new PublicKey(bytes);\n}\n\n/** @internal */\nexport async function signTransaction(\n    transport: Transport,\n    transaction: Transaction | VersionedTransaction,\n    derivationPath: Buffer\n): Promise<Buffer> {\n    const paths = Buffer.alloc(1);\n    paths.writeUInt8(1, 0);\n\n    const message = isVersionedTransaction(transaction)\n        ? transaction.message.serialize()\n        : transaction.serializeMessage();\n    const data = Buffer.concat([paths, derivationPath, message]);\n\n    return await send(transport, INS_SIGN_MESSAGE, P1_CONFIRM, data);\n}\n\nasync function send(transport: Transport, instruction: number, p1: number, data: Buffer): Promise<Buffer> {\n    let p2 = 0;\n    let offset = 0;\n\n    if (data.length > MAX_PAYLOAD) {\n        while (data.length - offset > MAX_PAYLOAD) {\n            const buffer = data.slice(offset, offset + MAX_PAYLOAD);\n            const response = await transport.send(LEDGER_CLA, instruction, p1, p2 | P2_MORE, buffer);\n            // @ts-ignore -- TransportStatusError is a constructor Function, not a Class\n            if (response.length !== 2) throw new TransportStatusError(StatusCodes.INCORRECT_DATA);\n\n            p2 |= P2_EXTEND;\n            offset += MAX_PAYLOAD;\n        }\n    }\n\n    const buffer = data.slice(offset);\n    const response = await transport.send(LEDGER_CLA, instruction, p1, p2, buffer);\n\n    return response.slice(0, response.length - 2);\n}\n", "import type { default as Transport } from '@ledgerhq/hw-transport';\nimport type { default as TransportWebHID } from '@ledgerhq/hw-transport-webhid';\nimport type { WalletName } from '@solana/wallet-adapter-base';\nimport {\n    BaseSignerWalletAdapter,\n    WalletConnectionError,\n    WalletDisconnectedError,\n    WalletDisconnectionError,\n    WalletLoadError,\n    WalletNotConnectedError,\n    WalletNotReadyError,\n    WalletPublicKeyError,\n    WalletReadyState,\n    WalletSignTransactionError,\n} from '@solana/wallet-adapter-base';\nimport type { PublicKey, Transaction, TransactionVersion, VersionedTransaction } from '@solana/web3.js';\nimport './polyfills/index.js';\nimport { getDerivationPath, getPublicKey, signTransaction } from './util.js';\n\nexport interface LedgerWalletAdapterConfig {\n    derivationPath?: Buffer;\n}\n\nexport const LedgerWalletName = 'Ledger' as WalletName<'Ledger'>;\n\nexport class LedgerWalletAdapter extends BaseSignerWalletAdapter {\n    name = LedgerWalletName;\n    url = 'https://ledger.com';\n    icon =\n        'data:image/svg+xml;base64,PHN2ZyB2aWV3Qm94PSIwIDAgMzUgMzUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0iI2ZmZiI+PHBhdGggZD0ibTIzLjU4OCAwaC0xNnYyMS41ODNoMjEuNnYtMTZhNS41ODUgNS41ODUgMCAwIDAgLTUuNi01LjU4M3oiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDUuNzM5KSIvPjxwYXRoIGQ9Im04LjM0MiAwaC0yLjc1N2E1LjU4NSA1LjU4NSAwIDAgMCAtNS41ODUgNS41ODV2Mi43NTdoOC4zNDJ6Ii8+PHBhdGggZD0ibTAgNy41OWg4LjM0MnY4LjM0MmgtOC4zNDJ6IiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwIDUuNzM5KSIvPjxwYXRoIGQ9Im0xNS4xOCAyMy40NTFoMi43NTdhNS41ODUgNS41ODUgMCAwIDAgNS41ODUtNS42di0yLjY3MWgtOC4zNDJ6IiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgxMS40NzggMTEuNDc4KSIvPjxwYXRoIGQ9Im03LjU5IDE1LjE4aDguMzQydjguMzQyaC04LjM0MnoiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDUuNzM5IDExLjQ3OCkiLz48cGF0aCBkPSJtMCAxNS4xOHYyLjc1N2E1LjU4NSA1LjU4NSAwIDAgMCA1LjU4NSA1LjU4NWgyLjc1N3YtOC4zNDJ6IiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwIDExLjQ3OCkiLz48L2c+PC9zdmc+';\n    supportedTransactionVersions: ReadonlySet<TransactionVersion> = new Set(['legacy', 0]);\n\n    private _derivationPath: Buffer;\n    private _connecting: boolean;\n    private _transport: Transport | null;\n    private _publicKey: PublicKey | null;\n    private _readyState: WalletReadyState =\n        typeof window === 'undefined' ||\n        typeof document === 'undefined' ||\n        typeof navigator === 'undefined' ||\n        !navigator.hid\n            ? WalletReadyState.Unsupported\n            : WalletReadyState.Loadable;\n\n    constructor(config: LedgerWalletAdapterConfig = {}) {\n        super();\n        this._derivationPath = config.derivationPath || getDerivationPath(0, 0);\n        this._connecting = false;\n        this._transport = null;\n        this._publicKey = null;\n    }\n\n    get publicKey() {\n        return this._publicKey;\n    }\n\n    get connecting() {\n        return this._connecting;\n    }\n\n    get readyState() {\n        return this._readyState;\n    }\n\n    async connect(): Promise<void> {\n        try {\n            if (this.connected || this.connecting) return;\n            if (this._readyState !== WalletReadyState.Loadable) throw new WalletNotReadyError();\n\n            this._connecting = true;\n\n            let TransportWebHIDClass: typeof TransportWebHID;\n            try {\n                TransportWebHIDClass = (await import('@ledgerhq/hw-transport-webhid')).default;\n            } catch (error: any) {\n                throw new WalletLoadError(error?.message, error);\n            }\n\n            let transport: Transport;\n            try {\n                transport = await TransportWebHIDClass.create();\n            } catch (error: any) {\n                throw new WalletConnectionError(error?.message, error);\n            }\n\n            let publicKey: PublicKey;\n            try {\n                publicKey = await getPublicKey(transport, this._derivationPath);\n            } catch (error: any) {\n                throw new WalletPublicKeyError(error?.message, error);\n            }\n\n            transport.on('disconnect', this._disconnected);\n\n            this._transport = transport;\n            this._publicKey = publicKey;\n\n            this.emit('connect', publicKey);\n        } catch (error: any) {\n            this.emit('error', error);\n            throw error;\n        } finally {\n            this._connecting = false;\n        }\n    }\n\n    async disconnect(): Promise<void> {\n        const transport = this._transport;\n        if (transport) {\n            transport.off('disconnect', this._disconnected);\n\n            this._transport = null;\n            this._publicKey = null;\n\n            try {\n                await transport.close();\n            } catch (error: any) {\n                this.emit('error', new WalletDisconnectionError(error?.message, error));\n            }\n        }\n\n        this.emit('disconnect');\n    }\n\n    async signTransaction<T extends Transaction | VersionedTransaction>(transaction: T): Promise<T> {\n        try {\n            const transport = this._transport;\n            const publicKey = this._publicKey;\n            if (!transport || !publicKey) throw new WalletNotConnectedError();\n\n            try {\n                const signature = await signTransaction(transport, transaction, this._derivationPath);\n                transaction.addSignature(publicKey, signature);\n            } catch (error: any) {\n                throw new WalletSignTransactionError(error?.message, error);\n            }\n\n            return transaction;\n        } catch (error: any) {\n            this.emit('error', error);\n            throw error;\n        }\n    }\n\n    private _disconnected = () => {\n        const transport = this._transport;\n        if (transport) {\n            transport.off('disconnect', this._disconnected);\n\n            this._transport = null;\n            this._publicKey = null;\n\n            this.emit('error', new WalletDisconnectedError());\n            this.emit('disconnect');\n        }\n    };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,oBAAuB;AAEvB,IAAI,OAAO,WAAW,eAAe,OAAO,WAAW,QAAW;AAC7D,SAAe,SAAS;;;;ACC7B;AAGM,SAAU,kBAAkB,SAAkB,QAAe;AAC/D,QAAM,SAAS,YAAY,SAAa,WAAW,SAAY,IAAI,IAAK;AACxE,QAAM,iBAAiB,OAAO,MAAM,IAAI,SAAS,CAAC;AAElD,MAAI,SAAS,eAAe,WAAW,QAAQ,CAAC;AAChD,WAAS,eAAe,cAAc,OAAO,EAAE,GAAG,MAAM;AACxD,WAAS,eAAe,cAAc,OAAO,GAAG,GAAG,MAAM;AAEzD,MAAI,YAAY,QAAW;AACvB,aAAS,eAAe,cAAc,OAAO,OAAO,GAAG,MAAM;AAC7D,QAAI,WAAW,QAAW;AACtB,qBAAe,cAAc,OAAO,MAAM,GAAG,MAAM;;;AAI3D,SAAO;AACX;AAEA,IAAM,qBAAsB,KAAK,OAAQ;AAEzC,SAAS,OAAO,GAAS;AACrB,UAAQ,IAAI,wBAAwB;AACxC;AAEA,IAAM,iBAAiB;AACvB,IAAM,mBAAmB;AAEzB,IAAM,iBAAiB;AACvB,IAAM,aAAa;AAEnB,IAAM,YAAY;AAClB,IAAM,UAAU;AAEhB,IAAM,cAAc;AAEpB,IAAM,aAAa;AAGnB,eAAsB,aAAa,WAAsB,gBAAsB;AAC3E,QAAM,QAAQ,MAAM,KAAK,WAAW,gBAAgB,gBAAgB,cAAc;AAClF,SAAO,IAAI,UAAU,KAAK;AAC9B;AAGA,eAAsB,gBAClB,WACA,aACA,gBAAsB;AAEtB,QAAM,QAAQ,OAAO,MAAM,CAAC;AAC5B,QAAM,WAAW,GAAG,CAAC;AAErB,QAAM,UAAU,uBAAuB,WAAW,IAC5C,YAAY,QAAQ,UAAS,IAC7B,YAAY,iBAAgB;AAClC,QAAM,OAAO,OAAO,OAAO,CAAC,OAAO,gBAAgB,OAAO,CAAC;AAE3D,SAAO,MAAM,KAAK,WAAW,kBAAkB,YAAY,IAAI;AACnE;AAEA,eAAe,KAAK,WAAsB,aAAqB,IAAY,MAAY;AACnF,MAAI,KAAK;AACT,MAAI,SAAS;AAEb,MAAI,KAAK,SAAS,aAAa;AAC3B,WAAO,KAAK,SAAS,SAAS,aAAa;AACvC,YAAMA,UAAS,KAAK,MAAM,QAAQ,SAAS,WAAW;AACtD,YAAMC,YAAW,MAAM,UAAU,KAAK,YAAY,aAAa,IAAI,KAAK,SAASD,OAAM;AAEvF,UAAIC,UAAS,WAAW;AAAG,cAAM,IAAI,qBAAqB,YAAY,cAAc;AAEpF,YAAM;AACN,gBAAU;;;AAIlB,QAAM,SAAS,KAAK,MAAM,MAAM;AAChC,QAAM,WAAW,MAAM,UAAU,KAAK,YAAY,aAAa,IAAI,IAAI,MAAM;AAE7E,SAAO,SAAS,MAAM,GAAG,SAAS,SAAS,CAAC;AAChD;;;AChEO,IAAM,mBAAmB;AAE1B,IAAO,sBAAP,cAAmC,wBAAuB;EAmB5D,YAAY,SAAoC,CAAA,GAAE;AAC9C,UAAK;AAnBT,SAAA,OAAO;AACP,SAAA,MAAM;AACN,SAAA,OACI;AACJ,SAAA,+BAAgE,oBAAI,IAAI,CAAC,UAAU,CAAC,CAAC;AAM7E,SAAA,cACJ,OAAO,WAAW,eAClB,OAAO,aAAa,eACpB,OAAO,cAAc,eACrB,CAAC,UAAU,MACL,iBAAiB,cACjB,iBAAiB;AAsGnB,SAAA,gBAAgB,MAAK;AACzB,YAAM,YAAY,KAAK;AACvB,UAAI,WAAW;AACX,kBAAU,IAAI,cAAc,KAAK,aAAa;AAE9C,aAAK,aAAa;AAClB,aAAK,aAAa;AAElB,aAAK,KAAK,SAAS,IAAI,wBAAuB,CAAE;AAChD,aAAK,KAAK,YAAY;;IAE9B;AA7GI,SAAK,kBAAkB,OAAO,kBAAkB,kBAAkB,GAAG,CAAC;AACtE,SAAK,cAAc;AACnB,SAAK,aAAa;AAClB,SAAK,aAAa;EACtB;EAEA,IAAI,YAAS;AACT,WAAO,KAAK;EAChB;EAEA,IAAI,aAAU;AACV,WAAO,KAAK;EAChB;EAEA,IAAI,aAAU;AACV,WAAO,KAAK;EAChB;EAEA,MAAM,UAAO;AACT,QAAI;AACA,UAAI,KAAK,aAAa,KAAK;AAAY;AACvC,UAAI,KAAK,gBAAgB,iBAAiB;AAAU,cAAM,IAAI,oBAAmB;AAEjF,WAAK,cAAc;AAEnB,UAAI;AACJ,UAAI;AACA,gCAAwB,MAAM,OAAO,+BAA+B,GAAG;eAClE,OAAY;AACjB,cAAM,IAAI,gBAAgB,+BAAO,SAAS,KAAK;;AAGnD,UAAI;AACJ,UAAI;AACA,oBAAY,MAAM,qBAAqB,OAAM;eACxC,OAAY;AACjB,cAAM,IAAI,sBAAsB,+BAAO,SAAS,KAAK;;AAGzD,UAAI;AACJ,UAAI;AACA,oBAAY,MAAM,aAAa,WAAW,KAAK,eAAe;eACzD,OAAY;AACjB,cAAM,IAAI,qBAAqB,+BAAO,SAAS,KAAK;;AAGxD,gBAAU,GAAG,cAAc,KAAK,aAAa;AAE7C,WAAK,aAAa;AAClB,WAAK,aAAa;AAElB,WAAK,KAAK,WAAW,SAAS;aACzB,OAAY;AACjB,WAAK,KAAK,SAAS,KAAK;AACxB,YAAM;;AAEN,WAAK,cAAc;;EAE3B;EAEA,MAAM,aAAU;AACZ,UAAM,YAAY,KAAK;AACvB,QAAI,WAAW;AACX,gBAAU,IAAI,cAAc,KAAK,aAAa;AAE9C,WAAK,aAAa;AAClB,WAAK,aAAa;AAElB,UAAI;AACA,cAAM,UAAU,MAAK;eAChB,OAAY;AACjB,aAAK,KAAK,SAAS,IAAI,yBAAyB,+BAAO,SAAS,KAAK,CAAC;;;AAI9E,SAAK,KAAK,YAAY;EAC1B;EAEA,MAAM,gBAA8D,aAAc;AAC9E,QAAI;AACA,YAAM,YAAY,KAAK;AACvB,YAAM,YAAY,KAAK;AACvB,UAAI,CAAC,aAAa,CAAC;AAAW,cAAM,IAAI,wBAAuB;AAE/D,UAAI;AACA,cAAM,YAAY,MAAM,gBAAgB,WAAW,aAAa,KAAK,eAAe;AACpF,oBAAY,aAAa,WAAW,SAAS;eACxC,OAAY;AACjB,cAAM,IAAI,2BAA2B,+BAAO,SAAS,KAAK;;AAG9D,aAAO;aACF,OAAY;AACjB,WAAK,KAAK,SAAS,KAAK;AACxB,YAAM;;EAEd;;", "names": ["buffer", "response"]}