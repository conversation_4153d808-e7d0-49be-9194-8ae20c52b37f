{"version": 3, "sources": ["../../@solana/wallet-adapter-phantom/src/adapter.ts"], "sourcesContent": ["import type { EventEmitter, SendTransactionOptions, WalletName } from '@solana/wallet-adapter-base';\nimport {\n    BaseMessageSignerWalletAdapter,\n    isIosAndRedirectable,\n    isVersionedTransaction,\n    scopePollingDetectionStrategy,\n    WalletAccountError,\n    WalletConnectionError,\n    WalletDisconnectedError,\n    WalletDisconnectionError,\n    WalletError,\n    WalletNotConnectedError,\n    WalletNotReadyError,\n    WalletPublicKeyError,\n    WalletReadyState,\n    WalletSendTransactionError,\n    WalletSignMessageError,\n    WalletSignTransactionError,\n} from '@solana/wallet-adapter-base';\nimport type {\n    Connection,\n    SendOptions,\n    Transaction,\n    TransactionSignature,\n    TransactionVersion,\n    VersionedTransaction,\n} from '@solana/web3.js';\nimport { PublicKey } from '@solana/web3.js';\n\ninterface PhantomWalletEvents {\n    connect(...args: unknown[]): unknown;\n    disconnect(...args: unknown[]): unknown;\n    accountChanged(newPublicKey: PublicKey): unknown;\n}\n\ninterface PhantomWallet extends EventEmitter<PhantomWalletEvents> {\n    isPhantom?: boolean;\n    publicKey?: { toBytes(): Uint8Array };\n    isConnected: boolean;\n    signTransaction<T extends Transaction | VersionedTransaction>(transaction: T): Promise<T>;\n    signAllTransactions<T extends Transaction | VersionedTransaction>(transactions: T[]): Promise<T[]>;\n    signAndSendTransaction<T extends Transaction | VersionedTransaction>(\n        transaction: T,\n        options?: SendOptions\n    ): Promise<{ signature: TransactionSignature }>;\n    signMessage(message: Uint8Array): Promise<{ signature: Uint8Array }>;\n    connect(): Promise<void>;\n    disconnect(): Promise<void>;\n}\n\ninterface PhantomWindow extends Window {\n    phantom?: {\n        solana?: PhantomWallet;\n    };\n    solana?: PhantomWallet;\n}\n\ndeclare const window: PhantomWindow;\n\nexport interface PhantomWalletAdapterConfig {}\n\nexport const PhantomWalletName = 'Phantom' as WalletName<'Phantom'>;\n\nexport class PhantomWalletAdapter extends BaseMessageSignerWalletAdapter {\n    name = PhantomWalletName;\n    url = 'https://phantom.app';\n    icon =\n        'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDgiIGhlaWdodD0iMTA4IiB2aWV3Qm94PSIwIDAgMTA4IDEwOCIgZmlsbD0ibm9uZSI+CjxyZWN0IHdpZHRoPSIxMDgiIGhlaWdodD0iMTA4IiByeD0iMjYiIGZpbGw9IiNBQjlGRjIiLz4KPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik00Ni41MjY3IDY5LjkyMjlDNDIuMDA1NCA3Ni44NTA5IDM0LjQyOTIgODUuNjE4MiAyNC4zNDggODUuNjE4MkMxOS41ODI0IDg1LjYxODIgMTUgODMuNjU2MyAxNSA3NS4xMzQyQzE1IDUzLjQzMDUgNDQuNjMyNiAxOS44MzI3IDcyLjEyNjggMTkuODMyN0M4Ny43NjggMTkuODMyNyA5NCAzMC42ODQ2IDk0IDQzLjAwNzlDOTQgNTguODI1OCA4My43MzU1IDc2LjkxMjIgNzMuNTMyMSA3Ni45MTIyQzcwLjI5MzkgNzYuOTEyMiA2OC43MDUzIDc1LjEzNDIgNjguNzA1MyA3Mi4zMTRDNjguNzA1MyA3MS41NzgzIDY4LjgyNzUgNzAuNzgxMiA2OS4wNzE5IDY5LjkyMjlDNjUuNTg5MyA3NS44Njk5IDU4Ljg2ODUgODEuMzg3OCA1Mi41NzU0IDgxLjM4NzhDNDcuOTkzIDgxLjM4NzggNDUuNjcxMyA3OC41MDYzIDQ1LjY3MTMgNzQuNDU5OEM0NS42NzEzIDcyLjk4ODQgNDUuOTc2OCA3MS40NTU2IDQ2LjUyNjcgNjkuOTIyOVpNODMuNjc2MSA0Mi41Nzk0QzgzLjY3NjEgNDYuMTcwNCA4MS41NTc1IDQ3Ljk2NTggNzkuMTg3NSA0Ny45NjU4Qzc2Ljc4MTYgNDcuOTY1OCA3NC42OTg5IDQ2LjE3MDQgNzQuNjk4OSA0Mi41Nzk0Qzc0LjY5ODkgMzguOTg4NSA3Ni43ODE2IDM3LjE5MzEgNzkuMTg3NSAzNy4xOTMxQzgxLjU1NzUgMzcuMTkzMSA4My42NzYxIDM4Ljk4ODUgODMuNjc2MSA0Mi41Nzk0Wk03MC4yMTAzIDQyLjU3OTVDNzAuMjEwMyA0Ni4xNzA0IDY4LjA5MTYgNDcuOTY1OCA2NS43MjE2IDQ3Ljk2NThDNjMuMzE1NyA0Ny45NjU4IDYxLjIzMyA0Ni4xNzA0IDYxLjIzMyA0Mi41Nzk1QzYxLjIzMyAzOC45ODg1IDYzLjMxNTcgMzcuMTkzMSA2NS43MjE2IDM3LjE5MzFDNjguMDkxNiAzNy4xOTMxIDcwLjIxMDMgMzguOTg4NSA3MC4yMTAzIDQyLjU3OTVaIiBmaWxsPSIjRkZGREY4Ii8+Cjwvc3ZnPg==';\n    supportedTransactionVersions: ReadonlySet<TransactionVersion> = new Set(['legacy', 0]);\n\n    private _connecting: boolean;\n    private _wallet: PhantomWallet | null;\n    private _publicKey: PublicKey | null;\n    private _readyState: WalletReadyState =\n        typeof window === 'undefined' || typeof document === 'undefined'\n            ? WalletReadyState.Unsupported\n            : WalletReadyState.NotDetected;\n\n    constructor(config: PhantomWalletAdapterConfig = {}) {\n        super();\n        this._connecting = false;\n        this._wallet = null;\n        this._publicKey = null;\n\n        if (this._readyState !== WalletReadyState.Unsupported) {\n            if (isIosAndRedirectable()) {\n                // when in iOS (not webview), set Phantom as loadable instead of checking for install\n                this._readyState = WalletReadyState.Loadable;\n                this.emit('readyStateChange', this._readyState);\n            } else {\n                scopePollingDetectionStrategy(() => {\n                    if (window.phantom?.solana?.isPhantom || window.solana?.isPhantom) {\n                        this._readyState = WalletReadyState.Installed;\n                        this.emit('readyStateChange', this._readyState);\n                        return true;\n                    }\n                    return false;\n                });\n            }\n        }\n    }\n\n    get publicKey() {\n        return this._publicKey;\n    }\n\n    get connecting() {\n        return this._connecting;\n    }\n\n    get readyState() {\n        return this._readyState;\n    }\n\n    async autoConnect(): Promise<void> {\n        // Skip autoconnect in the Loadable state\n        // We can't redirect to a universal link without user input\n        if (this.readyState === WalletReadyState.Installed) {\n            await this.connect();\n        }\n    }\n\n    async connect(): Promise<void> {\n        try {\n            if (this.connected || this.connecting) return;\n\n            if (this.readyState === WalletReadyState.Loadable) {\n                // redirect to the Phantom /browse universal link\n                // this will open the current URL in the Phantom in-wallet browser\n                const url = encodeURIComponent(window.location.href);\n                const ref = encodeURIComponent(window.location.origin);\n                window.location.href = `https://phantom.app/ul/browse/${url}?ref=${ref}`;\n                return;\n            }\n\n            if (this.readyState !== WalletReadyState.Installed) throw new WalletNotReadyError();\n\n            this._connecting = true;\n\n            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n            const wallet = window.phantom?.solana || window.solana!;\n\n            if (!wallet.isConnected) {\n                try {\n                    await wallet.connect();\n                } catch (error: any) {\n                    throw new WalletConnectionError(error?.message, error);\n                }\n            }\n\n            if (!wallet.publicKey) throw new WalletAccountError();\n\n            let publicKey: PublicKey;\n            try {\n                publicKey = new PublicKey(wallet.publicKey.toBytes());\n            } catch (error: any) {\n                throw new WalletPublicKeyError(error?.message, error);\n            }\n\n            wallet.on('disconnect', this._disconnected);\n            wallet.on('accountChanged', this._accountChanged);\n\n            this._wallet = wallet;\n            this._publicKey = publicKey;\n\n            this.emit('connect', publicKey);\n        } catch (error: any) {\n            this.emit('error', error);\n            throw error;\n        } finally {\n            this._connecting = false;\n        }\n    }\n\n    async disconnect(): Promise<void> {\n        const wallet = this._wallet;\n        if (wallet) {\n            wallet.off('disconnect', this._disconnected);\n            wallet.off('accountChanged', this._accountChanged);\n\n            this._wallet = null;\n            this._publicKey = null;\n\n            try {\n                await wallet.disconnect();\n            } catch (error: any) {\n                this.emit('error', new WalletDisconnectionError(error?.message, error));\n            }\n        }\n\n        this.emit('disconnect');\n    }\n\n    async sendTransaction<T extends Transaction | VersionedTransaction>(\n        transaction: T,\n        connection: Connection,\n        options: SendTransactionOptions = {}\n    ): Promise<TransactionSignature> {\n        try {\n            const wallet = this._wallet;\n            if (!wallet) throw new WalletNotConnectedError();\n\n            try {\n                const { signers, ...sendOptions } = options;\n\n                if (isVersionedTransaction(transaction)) {\n                    signers?.length && transaction.sign(signers);\n                } else {\n                    transaction = (await this.prepareTransaction(transaction, connection, sendOptions)) as T;\n                    signers?.length && (transaction as Transaction).partialSign(...signers);\n                }\n\n                sendOptions.preflightCommitment = sendOptions.preflightCommitment || connection.commitment;\n\n                const { signature } = await wallet.signAndSendTransaction(transaction, sendOptions);\n                return signature;\n            } catch (error: any) {\n                if (error instanceof WalletError) throw error;\n                throw new WalletSendTransactionError(error?.message, error);\n            }\n        } catch (error: any) {\n            this.emit('error', error);\n            throw error;\n        }\n    }\n\n    async signTransaction<T extends Transaction | VersionedTransaction>(transaction: T): Promise<T> {\n        try {\n            const wallet = this._wallet;\n            if (!wallet) throw new WalletNotConnectedError();\n\n            try {\n                return (await wallet.signTransaction(transaction)) || transaction;\n            } catch (error: any) {\n                throw new WalletSignTransactionError(error?.message, error);\n            }\n        } catch (error: any) {\n            this.emit('error', error);\n            throw error;\n        }\n    }\n\n    async signAllTransactions<T extends Transaction | VersionedTransaction>(transactions: T[]): Promise<T[]> {\n        try {\n            const wallet = this._wallet;\n            if (!wallet) throw new WalletNotConnectedError();\n\n            try {\n                return (await wallet.signAllTransactions(transactions)) || transactions;\n            } catch (error: any) {\n                throw new WalletSignTransactionError(error?.message, error);\n            }\n        } catch (error: any) {\n            this.emit('error', error);\n            throw error;\n        }\n    }\n\n    async signMessage(message: Uint8Array): Promise<Uint8Array> {\n        try {\n            const wallet = this._wallet;\n            if (!wallet) throw new WalletNotConnectedError();\n\n            try {\n                const { signature } = await wallet.signMessage(message);\n                return signature;\n            } catch (error: any) {\n                throw new WalletSignMessageError(error?.message, error);\n            }\n        } catch (error: any) {\n            this.emit('error', error);\n            throw error;\n        }\n    }\n\n    private _disconnected = () => {\n        const wallet = this._wallet;\n        if (wallet) {\n            wallet.off('disconnect', this._disconnected);\n            wallet.off('accountChanged', this._accountChanged);\n\n            this._wallet = null;\n            this._publicKey = null;\n\n            this.emit('error', new WalletDisconnectedError());\n            this.emit('disconnect');\n        }\n    };\n\n    private _accountChanged = (newPublicKey: PublicKey) => {\n        const publicKey = this._publicKey;\n        if (!publicKey) return;\n\n        try {\n            newPublicKey = new PublicKey(newPublicKey.toBytes());\n        } catch (error: any) {\n            this.emit('error', new WalletPublicKeyError(error?.message, error));\n            return;\n        }\n\n        if (publicKey.equals(newPublicKey)) return;\n\n        this._publicKey = newPublicKey;\n        this.emit('connect', newPublicKey);\n    };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA;AAkCO,IAAM,oBAAoB;AAE3B,IAAO,uBAAP,cAAoC,+BAA8B;EAepE,YAAY,SAAqC,CAAA,GAAE;AAC/C,UAAK;AAfT,SAAA,OAAO;AACP,SAAA,MAAM;AACN,SAAA,OACI;AACJ,SAAA,+BAAgE,oBAAI,IAAI,CAAC,UAAU,CAAC,CAAC;AAK7E,SAAA,cACJ,OAAO,WAAW,eAAe,OAAO,aAAa,cAC/C,iBAAiB,cACjB,iBAAiB;AAuMnB,SAAA,gBAAgB,MAAK;AACzB,YAAM,SAAS,KAAK;AACpB,UAAI,QAAQ;AACR,eAAO,IAAI,cAAc,KAAK,aAAa;AAC3C,eAAO,IAAI,kBAAkB,KAAK,eAAe;AAEjD,aAAK,UAAU;AACf,aAAK,aAAa;AAElB,aAAK,KAAK,SAAS,IAAI,wBAAuB,CAAE;AAChD,aAAK,KAAK,YAAY;;IAE9B;AAEQ,SAAA,kBAAkB,CAAC,iBAA2B;AAClD,YAAM,YAAY,KAAK;AACvB,UAAI,CAAC;AAAW;AAEhB,UAAI;AACA,uBAAe,IAAI,UAAU,aAAa,QAAO,CAAE;eAC9C,OAAY;AACjB,aAAK,KAAK,SAAS,IAAI,qBAAqB,+BAAO,SAAS,KAAK,CAAC;AAClE;;AAGJ,UAAI,UAAU,OAAO,YAAY;AAAG;AAEpC,WAAK,aAAa;AAClB,WAAK,KAAK,WAAW,YAAY;IACrC;AAhOI,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,SAAK,aAAa;AAElB,QAAI,KAAK,gBAAgB,iBAAiB,aAAa;AACnD,UAAI,qBAAoB,GAAI;AAExB,aAAK,cAAc,iBAAiB;AACpC,aAAK,KAAK,oBAAoB,KAAK,WAAW;aAC3C;AACH,sCAA8B,MAAK;AAzFnD;AA0FoB,gBAAI,kBAAO,YAAP,mBAAgB,WAAhB,mBAAwB,gBAAa,YAAO,WAAP,mBAAe,YAAW;AAC/D,iBAAK,cAAc,iBAAiB;AACpC,iBAAK,KAAK,oBAAoB,KAAK,WAAW;AAC9C,mBAAO;;AAEX,iBAAO;QACX,CAAC;;;EAGb;EAEA,IAAI,YAAS;AACT,WAAO,KAAK;EAChB;EAEA,IAAI,aAAU;AACV,WAAO,KAAK;EAChB;EAEA,IAAI,aAAU;AACV,WAAO,KAAK;EAChB;EAEA,MAAM,cAAW;AAGb,QAAI,KAAK,eAAe,iBAAiB,WAAW;AAChD,YAAM,KAAK,QAAO;;EAE1B;EAEA,MAAM,UAAO;AAzHjB;AA0HQ,QAAI;AACA,UAAI,KAAK,aAAa,KAAK;AAAY;AAEvC,UAAI,KAAK,eAAe,iBAAiB,UAAU;AAG/C,cAAM,MAAM,mBAAmB,OAAO,SAAS,IAAI;AACnD,cAAM,MAAM,mBAAmB,OAAO,SAAS,MAAM;AACrD,eAAO,SAAS,OAAO,iCAAiC,GAAG,QAAQ,GAAG;AACtE;;AAGJ,UAAI,KAAK,eAAe,iBAAiB;AAAW,cAAM,IAAI,oBAAmB;AAEjF,WAAK,cAAc;AAGnB,YAAM,WAAS,YAAO,YAAP,mBAAgB,WAAU,OAAO;AAEhD,UAAI,CAAC,OAAO,aAAa;AACrB,YAAI;AACA,gBAAM,OAAO,QAAO;iBACf,OAAY;AACjB,gBAAM,IAAI,sBAAsB,+BAAO,SAAS,KAAK;;;AAI7D,UAAI,CAAC,OAAO;AAAW,cAAM,IAAI,mBAAkB;AAEnD,UAAI;AACJ,UAAI;AACA,oBAAY,IAAI,UAAU,OAAO,UAAU,QAAO,CAAE;eAC/C,OAAY;AACjB,cAAM,IAAI,qBAAqB,+BAAO,SAAS,KAAK;;AAGxD,aAAO,GAAG,cAAc,KAAK,aAAa;AAC1C,aAAO,GAAG,kBAAkB,KAAK,eAAe;AAEhD,WAAK,UAAU;AACf,WAAK,aAAa;AAElB,WAAK,KAAK,WAAW,SAAS;aACzB,OAAY;AACjB,WAAK,KAAK,SAAS,KAAK;AACxB,YAAM;;AAEN,WAAK,cAAc;;EAE3B;EAEA,MAAM,aAAU;AACZ,UAAM,SAAS,KAAK;AACpB,QAAI,QAAQ;AACR,aAAO,IAAI,cAAc,KAAK,aAAa;AAC3C,aAAO,IAAI,kBAAkB,KAAK,eAAe;AAEjD,WAAK,UAAU;AACf,WAAK,aAAa;AAElB,UAAI;AACA,cAAM,OAAO,WAAU;eAClB,OAAY;AACjB,aAAK,KAAK,SAAS,IAAI,yBAAyB,+BAAO,SAAS,KAAK,CAAC;;;AAI9E,SAAK,KAAK,YAAY;EAC1B;EAEA,MAAM,gBACF,aACA,YACA,UAAkC,CAAA,GAAE;AAEpC,QAAI;AACA,YAAM,SAAS,KAAK;AACpB,UAAI,CAAC;AAAQ,cAAM,IAAI,wBAAuB;AAE9C,UAAI;AACA,cAAM,EAAE,SAAS,GAAG,YAAW,IAAK;AAEpC,YAAI,uBAAuB,WAAW,GAAG;AACrC,8CAAS,WAAU,YAAY,KAAK,OAAO;eACxC;AACH,wBAAe,MAAM,KAAK,mBAAmB,aAAa,YAAY,WAAW;AACjF,8CAAS,WAAW,YAA4B,YAAY,GAAG,OAAO;;AAG1E,oBAAY,sBAAsB,YAAY,uBAAuB,WAAW;AAEhF,cAAM,EAAE,UAAS,IAAK,MAAM,OAAO,uBAAuB,aAAa,WAAW;AAClF,eAAO;eACF,OAAY;AACjB,YAAI,iBAAiB;AAAa,gBAAM;AACxC,cAAM,IAAI,2BAA2B,+BAAO,SAAS,KAAK;;aAEzD,OAAY;AACjB,WAAK,KAAK,SAAS,KAAK;AACxB,YAAM;;EAEd;EAEA,MAAM,gBAA8D,aAAc;AAC9E,QAAI;AACA,YAAM,SAAS,KAAK;AACpB,UAAI,CAAC;AAAQ,cAAM,IAAI,wBAAuB;AAE9C,UAAI;AACA,eAAQ,MAAM,OAAO,gBAAgB,WAAW,KAAM;eACjD,OAAY;AACjB,cAAM,IAAI,2BAA2B,+BAAO,SAAS,KAAK;;aAEzD,OAAY;AACjB,WAAK,KAAK,SAAS,KAAK;AACxB,YAAM;;EAEd;EAEA,MAAM,oBAAkE,cAAiB;AACrF,QAAI;AACA,YAAM,SAAS,KAAK;AACpB,UAAI,CAAC;AAAQ,cAAM,IAAI,wBAAuB;AAE9C,UAAI;AACA,eAAQ,MAAM,OAAO,oBAAoB,YAAY,KAAM;eACtD,OAAY;AACjB,cAAM,IAAI,2BAA2B,+BAAO,SAAS,KAAK;;aAEzD,OAAY;AACjB,WAAK,KAAK,SAAS,KAAK;AACxB,YAAM;;EAEd;EAEA,MAAM,YAAY,SAAmB;AACjC,QAAI;AACA,YAAM,SAAS,KAAK;AACpB,UAAI,CAAC;AAAQ,cAAM,IAAI,wBAAuB;AAE9C,UAAI;AACA,cAAM,EAAE,UAAS,IAAK,MAAM,OAAO,YAAY,OAAO;AACtD,eAAO;eACF,OAAY;AACjB,cAAM,IAAI,uBAAuB,+BAAO,SAAS,KAAK;;aAErD,OAAY;AACjB,WAAK,KAAK,SAAS,KAAK;AACxB,YAAM;;EAEd;;", "names": []}