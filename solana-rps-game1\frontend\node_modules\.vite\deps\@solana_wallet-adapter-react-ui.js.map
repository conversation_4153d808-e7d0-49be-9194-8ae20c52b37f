{"version": 3, "sources": ["../../@solana/wallet-adapter-base-ui/node_modules/eventemitter3/index.js", "../../@solana/wallet-adapter-react-ui/src/useWalletModal.tsx", "../../@solana/wallet-adapter-base-ui/node_modules/@solana/wallet-adapter-react/src/ConnectionProvider.tsx", "../../@solana/wallet-adapter-base-ui/node_modules/@solana/wallet-adapter-react/src/useConnection.ts", "../../@solana/wallet-adapter-base-ui/node_modules/eventemitter3/index.mjs", "../../@solana/wallet-adapter-base-ui/node_modules/@solana/wallet-adapter-base/src/adapter.ts", "../../@solana/wallet-adapter-base-ui/node_modules/@solana/wallet-adapter-base/src/types.ts", "../../@solana/wallet-adapter-base-ui/node_modules/@solana/wallet-adapter-react/src/useAnchorWallet.ts", "../../@solana/wallet-adapter-base-ui/node_modules/@solana/wallet-adapter-react/src/useWallet.ts", "../../@solana/wallet-adapter-base-ui/node_modules/@solana/wallet-adapter-react/src/useLocalStorage.ts", "../../@solana/wallet-adapter-base-ui/node_modules/@solana/wallet-adapter-react/src/WalletProvider.tsx", "../../@solana/wallet-adapter-base-ui/node_modules/@solana/wallet-adapter-react/src/getEnvironment.ts", "../../@solana/wallet-adapter-base-ui/node_modules/@solana/wallet-adapter-react/src/WalletProviderBase.tsx", "../../@solana/wallet-adapter-base-ui/src/useWalletConnectButton.ts", "../../@solana/wallet-adapter-base-ui/src/useWalletDisconnectButton.ts", "../../@solana/wallet-adapter-base-ui/src/useWalletMultiButton.ts", "../../@solana/wallet-adapter-react-ui/src/BaseWalletConnectButton.tsx", "../../@solana/wallet-adapter-react-ui/src/BaseWalletConnectionButton.tsx", "../../@solana/wallet-adapter-react-ui/src/Button.tsx", "../../@solana/wallet-adapter-react-ui/src/WalletIcon.tsx", "../../@solana/wallet-adapter-react-ui/src/BaseWalletDisconnectButton.tsx", "../../@solana/wallet-adapter-react-ui/src/BaseWalletMultiButton.tsx", "../../@solana/wallet-adapter-react-ui/src/WalletConnectButton.tsx", "../../@solana/wallet-adapter-react-ui/src/WalletModal.tsx", "../../@solana/wallet-adapter-react-ui/src/Collapse.tsx", "../../@solana/wallet-adapter-react-ui/src/WalletListItem.tsx", "../../@solana/wallet-adapter-react-ui/src/WalletSVG.tsx", "../../@solana/wallet-adapter-react-ui/src/WalletModalButton.tsx", "../../@solana/wallet-adapter-react-ui/src/WalletModalProvider.tsx", "../../@solana/wallet-adapter-react-ui/src/WalletDisconnectButton.tsx", "../../@solana/wallet-adapter-react-ui/src/WalletMultiButton.tsx"], "sourcesContent": ["'use strict';\n\nvar has = Object.prototype.hasOwnProperty\n  , prefix = '~';\n\n/**\n * Constructor to create a storage for our `EE` objects.\n * An `Events` instance is a plain object whose properties are event names.\n *\n * @constructor\n * @private\n */\nfunction Events() {}\n\n//\n// We try to not inherit from `Object.prototype`. In some engines creating an\n// instance in this way is faster than calling `Object.create(null)` directly.\n// If `Object.create(null)` is not supported we prefix the event names with a\n// character to make sure that the built-in object properties are not\n// overridden or used as an attack vector.\n//\nif (Object.create) {\n  Events.prototype = Object.create(null);\n\n  //\n  // This hack is needed because the `__proto__` property is still inherited in\n  // some old browsers like Android 4, iPhone 5.1, Opera 11 and Safari 5.\n  //\n  if (!new Events().__proto__) prefix = false;\n}\n\n/**\n * Representation of a single event listener.\n *\n * @param {Function} fn The listener function.\n * @param {*} context The context to invoke the listener with.\n * @param {Boolean} [once=false] Specify if the listener is a one-time listener.\n * @constructor\n * @private\n */\nfunction EE(fn, context, once) {\n  this.fn = fn;\n  this.context = context;\n  this.once = once || false;\n}\n\n/**\n * Add a listener for a given event.\n *\n * @param {EventEmitter} emitter Reference to the `EventEmitter` instance.\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} context The context to invoke the listener with.\n * @param {Boolean} once Specify if the listener is a one-time listener.\n * @returns {EventEmitter}\n * @private\n */\nfunction addListener(emitter, event, fn, context, once) {\n  if (typeof fn !== 'function') {\n    throw new TypeError('The listener must be a function');\n  }\n\n  var listener = new EE(fn, context || emitter, once)\n    , evt = prefix ? prefix + event : event;\n\n  if (!emitter._events[evt]) emitter._events[evt] = listener, emitter._eventsCount++;\n  else if (!emitter._events[evt].fn) emitter._events[evt].push(listener);\n  else emitter._events[evt] = [emitter._events[evt], listener];\n\n  return emitter;\n}\n\n/**\n * Clear event by name.\n *\n * @param {EventEmitter} emitter Reference to the `EventEmitter` instance.\n * @param {(String|Symbol)} evt The Event name.\n * @private\n */\nfunction clearEvent(emitter, evt) {\n  if (--emitter._eventsCount === 0) emitter._events = new Events();\n  else delete emitter._events[evt];\n}\n\n/**\n * Minimal `EventEmitter` interface that is molded against the Node.js\n * `EventEmitter` interface.\n *\n * @constructor\n * @public\n */\nfunction EventEmitter() {\n  this._events = new Events();\n  this._eventsCount = 0;\n}\n\n/**\n * Return an array listing the events for which the emitter has registered\n * listeners.\n *\n * @returns {Array}\n * @public\n */\nEventEmitter.prototype.eventNames = function eventNames() {\n  var names = []\n    , events\n    , name;\n\n  if (this._eventsCount === 0) return names;\n\n  for (name in (events = this._events)) {\n    if (has.call(events, name)) names.push(prefix ? name.slice(1) : name);\n  }\n\n  if (Object.getOwnPropertySymbols) {\n    return names.concat(Object.getOwnPropertySymbols(events));\n  }\n\n  return names;\n};\n\n/**\n * Return the listeners registered for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Array} The registered listeners.\n * @public\n */\nEventEmitter.prototype.listeners = function listeners(event) {\n  var evt = prefix ? prefix + event : event\n    , handlers = this._events[evt];\n\n  if (!handlers) return [];\n  if (handlers.fn) return [handlers.fn];\n\n  for (var i = 0, l = handlers.length, ee = new Array(l); i < l; i++) {\n    ee[i] = handlers[i].fn;\n  }\n\n  return ee;\n};\n\n/**\n * Return the number of listeners listening to a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Number} The number of listeners.\n * @public\n */\nEventEmitter.prototype.listenerCount = function listenerCount(event) {\n  var evt = prefix ? prefix + event : event\n    , listeners = this._events[evt];\n\n  if (!listeners) return 0;\n  if (listeners.fn) return 1;\n  return listeners.length;\n};\n\n/**\n * Calls each of the listeners registered for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Boolean} `true` if the event had listeners, else `false`.\n * @public\n */\nEventEmitter.prototype.emit = function emit(event, a1, a2, a3, a4, a5) {\n  var evt = prefix ? prefix + event : event;\n\n  if (!this._events[evt]) return false;\n\n  var listeners = this._events[evt]\n    , len = arguments.length\n    , args\n    , i;\n\n  if (listeners.fn) {\n    if (listeners.once) this.removeListener(event, listeners.fn, undefined, true);\n\n    switch (len) {\n      case 1: return listeners.fn.call(listeners.context), true;\n      case 2: return listeners.fn.call(listeners.context, a1), true;\n      case 3: return listeners.fn.call(listeners.context, a1, a2), true;\n      case 4: return listeners.fn.call(listeners.context, a1, a2, a3), true;\n      case 5: return listeners.fn.call(listeners.context, a1, a2, a3, a4), true;\n      case 6: return listeners.fn.call(listeners.context, a1, a2, a3, a4, a5), true;\n    }\n\n    for (i = 1, args = new Array(len -1); i < len; i++) {\n      args[i - 1] = arguments[i];\n    }\n\n    listeners.fn.apply(listeners.context, args);\n  } else {\n    var length = listeners.length\n      , j;\n\n    for (i = 0; i < length; i++) {\n      if (listeners[i].once) this.removeListener(event, listeners[i].fn, undefined, true);\n\n      switch (len) {\n        case 1: listeners[i].fn.call(listeners[i].context); break;\n        case 2: listeners[i].fn.call(listeners[i].context, a1); break;\n        case 3: listeners[i].fn.call(listeners[i].context, a1, a2); break;\n        case 4: listeners[i].fn.call(listeners[i].context, a1, a2, a3); break;\n        default:\n          if (!args) for (j = 1, args = new Array(len -1); j < len; j++) {\n            args[j - 1] = arguments[j];\n          }\n\n          listeners[i].fn.apply(listeners[i].context, args);\n      }\n    }\n  }\n\n  return true;\n};\n\n/**\n * Add a listener for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} [context=this] The context to invoke the listener with.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.on = function on(event, fn, context) {\n  return addListener(this, event, fn, context, false);\n};\n\n/**\n * Add a one-time listener for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} [context=this] The context to invoke the listener with.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.once = function once(event, fn, context) {\n  return addListener(this, event, fn, context, true);\n};\n\n/**\n * Remove the listeners of a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn Only remove the listeners that match this function.\n * @param {*} context Only remove the listeners that have this context.\n * @param {Boolean} once Only remove one-time listeners.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.removeListener = function removeListener(event, fn, context, once) {\n  var evt = prefix ? prefix + event : event;\n\n  if (!this._events[evt]) return this;\n  if (!fn) {\n    clearEvent(this, evt);\n    return this;\n  }\n\n  var listeners = this._events[evt];\n\n  if (listeners.fn) {\n    if (\n      listeners.fn === fn &&\n      (!once || listeners.once) &&\n      (!context || listeners.context === context)\n    ) {\n      clearEvent(this, evt);\n    }\n  } else {\n    for (var i = 0, events = [], length = listeners.length; i < length; i++) {\n      if (\n        listeners[i].fn !== fn ||\n        (once && !listeners[i].once) ||\n        (context && listeners[i].context !== context)\n      ) {\n        events.push(listeners[i]);\n      }\n    }\n\n    //\n    // Reset the array, or remove it completely if we have no more listeners.\n    //\n    if (events.length) this._events[evt] = events.length === 1 ? events[0] : events;\n    else clearEvent(this, evt);\n  }\n\n  return this;\n};\n\n/**\n * Remove all listeners, or those of the specified event.\n *\n * @param {(String|Symbol)} [event] The event name.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.removeAllListeners = function removeAllListeners(event) {\n  var evt;\n\n  if (event) {\n    evt = prefix ? prefix + event : event;\n    if (this._events[evt]) clearEvent(this, evt);\n  } else {\n    this._events = new Events();\n    this._eventsCount = 0;\n  }\n\n  return this;\n};\n\n//\n// Alias methods names because people roll like that.\n//\nEventEmitter.prototype.off = EventEmitter.prototype.removeListener;\nEventEmitter.prototype.addListener = EventEmitter.prototype.on;\n\n//\n// Expose the prefix.\n//\nEventEmitter.prefixed = prefix;\n\n//\n// Allow `EventEmitter` to be imported as module namespace.\n//\nEventEmitter.EventEmitter = EventEmitter;\n\n//\n// Expose the module.\n//\nif ('undefined' !== typeof module) {\n  module.exports = EventEmitter;\n}\n", "import { createContext, useContext } from 'react';\n\nexport interface WalletModalContextState {\n    visible: boolean;\n    setVisible: (open: boolean) => void;\n}\n\nconst DEFAULT_CONTEXT = {\n    setVisible(_open: boolean) {\n        console.error(constructMissingProviderErrorMessage('call', 'setVisible'));\n    },\n    visible: false,\n};\nObject.defineProperty(DEFAULT_CONTEXT, 'visible', {\n    get() {\n        console.error(constructMissingProviderErrorMessage('read', 'visible'));\n        return false;\n    },\n});\n\nfunction constructMissingProviderErrorMessage(action: string, valueName: string) {\n    return (\n        'You have tried to ' +\n        ` ${action} \"${valueName}\"` +\n        ' on a WalletModalContext without providing one.' +\n        ' Make sure to render a WalletModalProvider' +\n        ' as an ancestor of the component that uses ' +\n        'WalletModalContext'\n    );\n}\n\nexport const WalletModalContext = createContext<WalletModalContextState>(DEFAULT_CONTEXT as WalletModalContextState);\n\nexport function useWalletModal(): WalletModalContextState {\n    return useContext(WalletModalContext);\n}\n", "import { Connection, type ConnectionConfig } from '@solana/web3.js';\nimport React, { type FC, type ReactNode, useMemo } from 'react';\nimport { ConnectionContext } from './useConnection.js';\n\nexport interface ConnectionProviderProps {\n    children: ReactNode;\n    endpoint: string;\n    config?: ConnectionConfig;\n}\n\nexport const ConnectionProvider: FC<ConnectionProviderProps> = ({\n    children,\n    endpoint,\n    config = { commitment: 'confirmed' },\n}) => {\n    const connection = useMemo(() => new Connection(endpoint, config), [endpoint, config]);\n\n    return <ConnectionContext.Provider value={{ connection }}>{children}</ConnectionContext.Provider>;\n};\n", "import { type Connection } from '@solana/web3.js';\nimport { createContext, useContext } from 'react';\n\nexport interface ConnectionContextState {\n    connection: Connection;\n}\n\nexport const ConnectionContext = createContext<ConnectionContextState>({} as ConnectionContextState);\n\nexport function useConnection(): ConnectionContextState {\n    return useContext(ConnectionContext);\n}\n", "import EventEmitter from './index.js'\n\nexport { EventEmitter }\nexport default EventEmitter\n", "import type { Connection, PublicKey, SendOptions, Signer, Transaction, TransactionSignature } from '@solana/web3.js';\nimport EventEmitter from 'eventemitter3';\nimport { type WalletError, WalletNotConnectedError } from './errors.js';\nimport type { SupportedTransactionVersions, TransactionOrVersionedTransaction } from './transaction.js';\n\nexport { EventEmitter };\n\nexport interface WalletAdapterEvents {\n    connect(publicKey: PublicKey): void;\n    disconnect(): void;\n    error(error: WalletError): void;\n    readyStateChange(readyState: WalletReadyState): void;\n}\n\nexport interface SendTransactionOptions extends SendOptions {\n    signers?: Signer[];\n}\n\n// WalletName is a nominal type that wallet adapters should use, e.g. `'MyCryptoWallet' as WalletName<'MyCryptoWallet'>`\n// https://medium.com/@KevinBGreene/surviving-the-typescript-ecosystem-branding-and-type-tagging-6cf6e516523d\nexport type WalletName<T extends string = string> = T & { __brand__: 'WalletName' };\n\nexport interface WalletAdapterProps<Name extends string = string> {\n    name: WalletName<Name>;\n    url: string;\n    icon: string;\n    readyState: WalletReadyState;\n    publicKey: PublicKey | null;\n    connecting: boolean;\n    connected: boolean;\n    supportedTransactionVersions?: SupportedTransactionVersions;\n\n    autoConnect(): Promise<void>;\n    connect(): Promise<void>;\n    disconnect(): Promise<void>;\n    sendTransaction(\n        transaction: TransactionOrVersionedTransaction<this['supportedTransactionVersions']>,\n        connection: Connection,\n        options?: SendTransactionOptions\n    ): Promise<TransactionSignature>;\n}\n\nexport type WalletAdapter<Name extends string = string> = WalletAdapterProps<Name> & EventEmitter<WalletAdapterEvents>;\n\n/**\n * A wallet's readiness describes a series of states that the wallet can be in,\n * depending on what kind of wallet it is. An installable wallet (eg. a browser\n * extension like Phantom) might be `Installed` if we've found the Phantom API\n * in the global scope, or `NotDetected` otherwise. A loadable, zero-install\n * runtime (eg. Torus Wallet) might simply signal that it's `Loadable`. Use this\n * metadata to personalize the wallet list for each user (eg. to show their\n * installed wallets first).\n */\nexport enum WalletReadyState {\n    /**\n     * User-installable wallets can typically be detected by scanning for an API\n     * that they've injected into the global context. If such an API is present,\n     * we consider the wallet to have been installed.\n     */\n    Installed = 'Installed',\n    NotDetected = 'NotDetected',\n    /**\n     * Loadable wallets are always available to you. Since you can load them at\n     * any time, it's meaningless to say that they have been detected.\n     */\n    Loadable = 'Loadable',\n    /**\n     * If a wallet is not supported on a given platform (eg. server-rendering, or\n     * mobile) then it will stay in the `Unsupported` state.\n     */\n    Unsupported = 'Unsupported',\n}\n\nexport abstract class BaseWalletAdapter<Name extends string = string>\n    extends EventEmitter<WalletAdapterEvents>\n    implements WalletAdapter<Name>\n{\n    abstract name: WalletName<Name>;\n    abstract url: string;\n    abstract icon: string;\n    abstract readyState: WalletReadyState;\n    abstract publicKey: PublicKey | null;\n    abstract connecting: boolean;\n    abstract supportedTransactionVersions?: SupportedTransactionVersions;\n\n    get connected() {\n        return !!this.publicKey;\n    }\n\n    async autoConnect() {\n        await this.connect();\n    }\n\n    abstract connect(): Promise<void>;\n    abstract disconnect(): Promise<void>;\n\n    abstract sendTransaction(\n        transaction: TransactionOrVersionedTransaction<this['supportedTransactionVersions']>,\n        connection: Connection,\n        options?: SendTransactionOptions\n    ): Promise<TransactionSignature>;\n\n    protected async prepareTransaction(\n        transaction: Transaction,\n        connection: Connection,\n        options: SendOptions = {}\n    ): Promise<Transaction> {\n        const publicKey = this.publicKey;\n        if (!publicKey) throw new WalletNotConnectedError();\n\n        transaction.feePayer = transaction.feePayer || publicKey;\n        transaction.recentBlockhash =\n            transaction.recentBlockhash ||\n            (\n                await connection.getLatestBlockhash({\n                    commitment: options.preflightCommitment,\n                    minContextSlot: options.minContextSlot,\n                })\n            ).blockhash;\n\n        return transaction;\n    }\n}\n\nexport function scopePollingDetectionStrategy(detect: () => boolean): void {\n    // Early return when server-side rendering\n    if (typeof window === 'undefined' || typeof document === 'undefined') return;\n\n    const disposers: (() => void)[] = [];\n\n    function detectAndDispose() {\n        const detected = detect();\n        if (detected) {\n            for (const dispose of disposers) {\n                dispose();\n            }\n        }\n    }\n\n    // Strategy #1: Try detecting every second.\n    const interval =\n        // TODO: #334 Replace with idle callback strategy.\n        setInterval(detectAndDispose, 1000);\n    disposers.push(() => clearInterval(interval));\n\n    // Strategy #2: Detect as soon as the DOM becomes 'ready'/'interactive'.\n    if (\n        // Implies that `DOMContentLoaded` has not yet fired.\n        document.readyState === 'loading'\n    ) {\n        document.addEventListener('DOMContentLoaded', detectAndDispose, { once: true });\n        disposers.push(() => document.removeEventListener('DOMContentLoaded', detectAndDispose));\n    }\n\n    // Strategy #3: Detect after the `window` has fully loaded.\n    if (\n        // If the `complete` state has been reached, we're too late.\n        document.readyState !== 'complete'\n    ) {\n        window.addEventListener('load', detectAndDispose, { once: true });\n        disposers.push(() => window.removeEventListener('load', detectAndDispose));\n    }\n\n    // Strategy #4: Detect synchronously, now.\n    detectAndDispose();\n}\n\n/**\n * Users on iOS can be redirected into a wallet's in-app browser automatically,\n * if that wallet has a universal link configured to do so\n * But should not be redirected from within a webview, eg. if they're already\n * inside a wallet's browser\n * This function can be used to identify users who are on iOS and can be redirected\n *\n * @returns true if the user can be redirected\n */\nexport function isIosAndRedirectable() {\n    // SSR: return false\n    if (!navigator) return false;\n\n    const userAgent = navigator.userAgent.toLowerCase();\n\n    // if on iOS the user agent will contain either iPhone or iPad\n    // caveat: if requesting desktop site then this won't work\n    const isIos = userAgent.includes('iphone') || userAgent.includes('ipad');\n\n    // if in a webview then it will not include Safari\n    // note that other iOS browsers also include Safari\n    // so we will redirect only if Safari is also included\n    const isSafari = userAgent.includes('safari');\n\n    return isIos && isSafari;\n}\n", "import type { WalletAdapter } from './adapter.js';\nimport type { MessageSignerWalletAdapter, SignerWalletAdapter, SignInMessageSignerWalletAdapter } from './signer.js';\nimport type { StandardWalletAdapter } from './standard.js';\n\nexport type Adapter =\n    | WalletAdapter\n    | SignerWalletAdapter\n    | MessageSignerWalletAdapter\n    | SignInMessageSignerWalletAdapter\n    | StandardWalletAdapter;\n\nexport enum WalletAdapterNetwork {\n    Mainnet = 'mainnet-beta',\n    Testnet = 'testnet',\n    Devnet = 'devnet',\n}\n", "import { type PublicKey, type Transaction, type VersionedTransaction } from '@solana/web3.js';\nimport { useMemo } from 'react';\nimport { useWallet } from './useWallet.js';\n\nexport interface AnchorWallet {\n    publicKey: PublicKey;\n    signTransaction<T extends Transaction | VersionedTransaction>(transaction: T): Promise<T>;\n    signAllTransactions<T extends Transaction | VersionedTransaction>(transactions: T[]): Promise<T[]>;\n}\n\nexport function useAnchorWallet(): AnchorWallet | undefined {\n    const { publicKey, signTransaction, signAllTransactions } = useWallet();\n    return useMemo(\n        () =>\n            publicKey && signTransaction && signAllTransactions\n                ? { publicKey, signTransaction, signAllTransactions }\n                : undefined,\n        [publicKey, signTransaction, signAllTransactions]\n    );\n}\n", "import {\n    type Adapter,\n    type MessageSignerWalletAdapterProps,\n    type SignerWalletAdapterProps,\n    type SignInMessageSignerWalletAdapterProps,\n    type WalletAdapterProps,\n    type WalletName,\n    type WalletReadyState,\n} from '@solana/wallet-adapter-base';\nimport { type PublicKey } from '@solana/web3.js';\nimport { createContext, useContext } from 'react';\n\nexport interface Wallet {\n    adapter: Adapter;\n    readyState: WalletReadyState;\n}\n\nexport interface WalletContextState {\n    autoConnect: boolean;\n    wallets: Wallet[];\n    wallet: Wallet | null;\n    publicKey: PublicKey | null;\n    connecting: boolean;\n    connected: boolean;\n    disconnecting: boolean;\n\n    select(walletName: WalletName | null): void;\n    connect(): Promise<void>;\n    disconnect(): Promise<void>;\n\n    sendTransaction: WalletAdapterProps['sendTransaction'];\n    signTransaction: SignerWalletAdapterProps['signTransaction'] | undefined;\n    signAllTransactions: SignerWalletAdapterProps['signAllTransactions'] | undefined;\n    signMessage: MessageSignerWalletAdapterProps['signMessage'] | undefined;\n    signIn: SignInMessageSignerWalletAdapterProps['signIn'] | undefined;\n}\n\nconst EMPTY_ARRAY: ReadonlyArray<never> = [];\n\nconst DEFAULT_CONTEXT: Partial<WalletContextState> = {\n    autoConnect: false,\n    connecting: false,\n    connected: false,\n    disconnecting: false,\n    select() {\n        logMissingProviderError('call', 'select');\n    },\n    connect() {\n        return Promise.reject(logMissingProviderError('call', 'connect'));\n    },\n    disconnect() {\n        return Promise.reject(logMissingProviderError('call', 'disconnect'));\n    },\n    sendTransaction() {\n        return Promise.reject(logMissingProviderError('call', 'sendTransaction'));\n    },\n    signTransaction() {\n        return Promise.reject(logMissingProviderError('call', 'signTransaction'));\n    },\n    signAllTransactions() {\n        return Promise.reject(logMissingProviderError('call', 'signAllTransactions'));\n    },\n    signMessage() {\n        return Promise.reject(logMissingProviderError('call', 'signMessage'));\n    },\n    signIn() {\n        return Promise.reject(logMissingProviderError('call', 'signIn'));\n    },\n};\nObject.defineProperty(DEFAULT_CONTEXT, 'wallets', {\n    get() {\n        logMissingProviderError('read', 'wallets');\n        return EMPTY_ARRAY;\n    },\n});\nObject.defineProperty(DEFAULT_CONTEXT, 'wallet', {\n    get() {\n        logMissingProviderError('read', 'wallet');\n        return null;\n    },\n});\nObject.defineProperty(DEFAULT_CONTEXT, 'publicKey', {\n    get() {\n        logMissingProviderError('read', 'publicKey');\n        return null;\n    },\n});\n\nfunction logMissingProviderError(action: string, property: string) {\n    const error = new Error(\n        `You have tried to ${action} \"${property}\" on a WalletContext without providing one. ` +\n            'Make sure to render a WalletProvider as an ancestor of the component that uses WalletContext.'\n    );\n    console.error(error);\n    return error;\n}\n\nexport const WalletContext = createContext<WalletContextState>(DEFAULT_CONTEXT as WalletContextState);\n\nexport function useWallet(): WalletContextState {\n    return useContext(WalletContext);\n}\n", "import { type Dispatch, type SetStateAction, useEffect, useRef, useState } from 'react';\n\nexport function useLocalStorage<T>(key: string, defaultState: T): [T, Dispatch<SetStateAction<T>>] {\n    const state = useState<T>(() => {\n        try {\n            const value = localStorage.getItem(key);\n            if (value) return JSON.parse(value) as T;\n        } catch (error: any) {\n            if (typeof window !== 'undefined') {\n                console.error(error);\n            }\n        }\n\n        return defaultState;\n    });\n    const value = state[0];\n\n    const isFirstRenderRef = useRef(true);\n    useEffect(() => {\n        if (isFirstRenderRef.current) {\n            isFirstRenderRef.current = false;\n            return;\n        }\n        try {\n            if (value === null) {\n                localStorage.removeItem(key);\n            } else {\n                localStorage.setItem(key, JSON.stringify(value));\n            }\n        } catch (error: any) {\n            if (typeof window !== 'undefined') {\n                console.error(error);\n            }\n        }\n    }, [value, key]);\n\n    return state;\n}\n", "import {\n    createDefaultAddressSelector,\n    createDefaultAuthorizationResultCache,\n    createDefaultWalletNotFound<PERSON>andler,\n    SolanaMobileWalletAdapter,\n    SolanaMobileWalletAdapterWalletName,\n} from '@solana-mobile/wallet-adapter-mobile';\nimport { type Adapter, type WalletError, type WalletName } from '@solana/wallet-adapter-base';\nimport { useStandardWalletAdapters } from '@solana/wallet-standard-wallet-adapter-react';\nimport React, { type ReactNode, useCallback, useEffect, useMemo, useRef } from 'react';\nimport getEnvironment, { Environment } from './getEnvironment.js';\nimport getInferredClusterFromEndpoint from './getInferredClusterFromEndpoint.js';\nimport { useConnection } from './useConnection.js';\nimport { useLocalStorage } from './useLocalStorage.js';\nimport { WalletProviderBase } from './WalletProviderBase.js';\n\nexport interface WalletProviderProps {\n    children: ReactNode;\n    wallets: Adapter[];\n    autoConnect?: boolean | ((adapter: Adapter) => Promise<boolean>);\n    localStorageKey?: string;\n    onError?: (error: WalletError, adapter?: Adapter) => void;\n}\n\nlet _userAgent: string | null;\nfunction getUserAgent() {\n    if (_userAgent === undefined) {\n        _userAgent = globalThis.navigator?.userAgent ?? null;\n    }\n    return _userAgent;\n}\n\nfunction getIsMobile(adapters: Adapter[]) {\n    const userAgentString = getUserAgent();\n    return getEnvironment({ adapters, userAgentString }) === Environment.MOBILE_WEB;\n}\n\nfunction getUriForAppIdentity() {\n    const location = globalThis.location;\n    if (!location) return;\n    return `${location.protocol}//${location.host}`;\n}\n\nexport function WalletProvider({\n    children,\n    wallets: adapters,\n    autoConnect,\n    localStorageKey = 'walletName',\n    onError,\n}: WalletProviderProps) {\n    const { connection } = useConnection();\n    const adaptersWithStandardAdapters = useStandardWalletAdapters(adapters);\n    const mobileWalletAdapter = useMemo(() => {\n        if (!getIsMobile(adaptersWithStandardAdapters)) {\n            return null;\n        }\n        const existingMobileWalletAdapter = adaptersWithStandardAdapters.find(\n            (adapter) => adapter.name === SolanaMobileWalletAdapterWalletName\n        );\n        if (existingMobileWalletAdapter) {\n            return existingMobileWalletAdapter;\n        }\n        return new SolanaMobileWalletAdapter({\n            addressSelector: createDefaultAddressSelector(),\n            appIdentity: {\n                uri: getUriForAppIdentity(),\n            },\n            authorizationResultCache: createDefaultAuthorizationResultCache(),\n            cluster: getInferredClusterFromEndpoint(connection?.rpcEndpoint),\n            onWalletNotFound: createDefaultWalletNotFoundHandler(),\n        });\n    }, [adaptersWithStandardAdapters, connection?.rpcEndpoint]);\n    const adaptersWithMobileWalletAdapter = useMemo(() => {\n        if (mobileWalletAdapter == null || adaptersWithStandardAdapters.indexOf(mobileWalletAdapter) !== -1) {\n            return adaptersWithStandardAdapters;\n        }\n        return [mobileWalletAdapter, ...adaptersWithStandardAdapters];\n    }, [adaptersWithStandardAdapters, mobileWalletAdapter]);\n    const [walletName, setWalletName] = useLocalStorage<WalletName | null>(localStorageKey, null);\n    const adapter = useMemo(\n        () => adaptersWithMobileWalletAdapter.find((a) => a.name === walletName) ?? null,\n        [adaptersWithMobileWalletAdapter, walletName]\n    );\n    const changeWallet = useCallback(\n        (nextWalletName: WalletName<string> | null) => {\n            if (walletName === nextWalletName) return;\n            if (\n                adapter &&\n                // Selecting a wallet other than the mobile wallet adapter is not\n                // sufficient reason to call `disconnect` on the mobile wallet adapter.\n                // Calling `disconnect` on the mobile wallet adapter causes the entire\n                // authorization store to be wiped.\n                adapter.name !== SolanaMobileWalletAdapterWalletName\n            ) {\n                adapter.disconnect();\n            }\n            setWalletName(nextWalletName);\n        },\n        [adapter, setWalletName, walletName]\n    );\n    useEffect(() => {\n        if (!adapter) return;\n        function handleDisconnect() {\n            if (isUnloadingRef.current) return;\n            setWalletName(null);\n        }\n        adapter.on('disconnect', handleDisconnect);\n        return () => {\n            adapter.off('disconnect', handleDisconnect);\n        };\n    }, [adapter, adaptersWithStandardAdapters, setWalletName, walletName]);\n    const hasUserSelectedAWallet = useRef(false);\n    const handleAutoConnectRequest = useMemo(() => {\n        if (!autoConnect || !adapter) return;\n        return async () => {\n            // If autoConnect is true or returns true, use the default autoConnect behavior.\n            if (autoConnect === true || (await autoConnect(adapter))) {\n                if (hasUserSelectedAWallet.current) {\n                    await adapter.connect();\n                } else {\n                    await adapter.autoConnect();\n                }\n            }\n        };\n    }, [autoConnect, adapter]);\n    const isUnloadingRef = useRef(false);\n    useEffect(() => {\n        if (walletName === SolanaMobileWalletAdapterWalletName && getIsMobile(adaptersWithStandardAdapters)) {\n            isUnloadingRef.current = false;\n            return;\n        }\n        function handleBeforeUnload() {\n            isUnloadingRef.current = true;\n        }\n        /**\n         * Some wallets fire disconnection events when the window unloads. Since there's no way to\n         * distinguish between a disconnection event received because a user initiated it, and one\n         * that was received because they've closed the window, we have to track window unload\n         * events themselves. Downstream components use this information to decide whether to act\n         * upon or drop wallet events and errors.\n         */\n        window.addEventListener('beforeunload', handleBeforeUnload);\n        return () => {\n            window.removeEventListener('beforeunload', handleBeforeUnload);\n        };\n    }, [adaptersWithStandardAdapters, walletName]);\n    const handleConnectError = useCallback(() => {\n        if (adapter) {\n            // If any error happens while connecting, unset the adapter.\n            changeWallet(null);\n        }\n    }, [adapter, changeWallet]);\n    const selectWallet = useCallback(\n        (walletName: WalletName | null) => {\n            hasUserSelectedAWallet.current = true;\n            changeWallet(walletName);\n        },\n        [changeWallet]\n    );\n    return (\n        <WalletProviderBase\n            wallets={adaptersWithMobileWalletAdapter}\n            adapter={adapter}\n            isUnloadingRef={isUnloadingRef}\n            onAutoConnectRequest={handleAutoConnectRequest}\n            onConnectError={handleConnectError}\n            onError={onError}\n            onSelectWallet={selectWallet}\n        >\n            {children}\n        </WalletProviderBase>\n    );\n}\n", "import { SolanaMobileWalletAdapterWalletName } from '@solana-mobile/wallet-adapter-mobile';\nimport { type Adapter, WalletReadyState } from '@solana/wallet-adapter-base';\n\nexport enum Environment {\n    DESKTOP_WEB,\n    MOBILE_WEB,\n}\n\ntype Config = Readonly<{\n    adapters: Adapter[];\n    userAgentString: string | null;\n}>;\n\nfunction isWebView(userAgentString: string) {\n    return /(WebView|Version\\/.+(Chrome)\\/(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+)|; wv\\).+(Chrome)\\/(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+))/i.test(\n        userAgentString\n    );\n}\n\nexport default function getEnvironment({ adapters, userAgentString }: Config): Environment {\n    if (\n        adapters.some(\n            (adapter) =>\n                adapter.name !== SolanaMobileWalletAdapterWalletName &&\n                adapter.readyState === WalletReadyState.Installed\n        )\n    ) {\n        /**\n         * There are only two ways a browser extension adapter should be able to reach `Installed` status:\n         *\n         *     1. Its browser extension is installed.\n         *     2. The app is running on a mobile wallet's in-app browser.\n         *\n         * In either case, we consider the environment to be desktop-like.\n         */\n        return Environment.DESKTOP_WEB;\n    }\n    if (\n        userAgentString &&\n        // Step 1: Check whether we're on a platform that supports MWA at all.\n        /android/i.test(userAgentString) &&\n        // Step 2: Determine that we are *not* running in a WebView.\n        !isWebView(userAgentString)\n    ) {\n        return Environment.MOBILE_WEB;\n    } else {\n        return Environment.DESKTOP_WEB;\n    }\n}\n", "import {\n    type Adapter,\n    type MessageSignerWalletAdapterProps,\n    type SignerWalletAdapterProps,\n    type SignInMessageSignerWalletAdapterProps,\n    type WalletAdapterProps,\n    type WalletError,\n    type WalletName,\n    WalletNotConnectedError,\n    WalletNotReadyError,\n    WalletReadyState,\n} from '@solana/wallet-adapter-base';\nimport { type PublicKey } from '@solana/web3.js';\nimport React, { type ReactNode, useCallback, useEffect, useMemo, useRef, useState } from 'react';\nimport { WalletNotSelectedError } from './errors.js';\nimport { WalletContext } from './useWallet.js';\n\nexport interface WalletProviderBaseProps {\n    children: ReactNode;\n    wallets: Adapter[];\n    adapter: Adapter | null;\n    isUnloadingRef: React.RefObject<boolean>;\n    // NOTE: The presence/absence of this handler implies that auto-connect is enabled/disabled.\n    onAutoConnectRequest?: () => Promise<void>;\n    onConnectError: () => void;\n    onError?: (error: WalletError, adapter?: Adapter) => void;\n    onSelectWallet: (walletName: WalletName | null) => void;\n}\n\nexport function WalletProviderBase({\n    children,\n    wallets: adapters,\n    adapter,\n    isUnloadingRef,\n    onAutoConnectRequest,\n    onConnectError,\n    onError,\n    onSelectWallet,\n}: WalletProviderBaseProps) {\n    const isConnectingRef = useRef(false);\n    const [connecting, setConnecting] = useState(false);\n    const isDisconnectingRef = useRef(false);\n    const [disconnecting, setDisconnecting] = useState(false);\n    const [publicKey, setPublicKey] = useState(() => adapter?.publicKey ?? null);\n    const [connected, setConnected] = useState(() => adapter?.connected ?? false);\n\n    /**\n     * Store the error handlers as refs so that a change in the\n     * custom error handler does not recompute other dependencies.\n     */\n    const onErrorRef = useRef(onError);\n    useEffect(() => {\n        onErrorRef.current = onError;\n        return () => {\n            onErrorRef.current = undefined;\n        };\n    }, [onError]);\n    const handleErrorRef = useRef((error: WalletError, adapter?: Adapter) => {\n        if (!isUnloadingRef.current) {\n            if (onErrorRef.current) {\n                onErrorRef.current(error, adapter);\n            } else {\n                console.error(error, adapter);\n                if (error instanceof WalletNotReadyError && typeof window !== 'undefined' && adapter) {\n                    window.open(adapter.url, '_blank');\n                }\n            }\n        }\n        return error;\n    });\n\n    // Wrap adapters to conform to the `Wallet` interface\n    const [wallets, setWallets] = useState(() =>\n        adapters\n            .map((adapter) => ({\n                adapter,\n                readyState: adapter.readyState,\n            }))\n            .filter(({ readyState }) => readyState !== WalletReadyState.Unsupported)\n    );\n\n    // When the adapters change, start to listen for changes to their `readyState`\n    useEffect(() => {\n        // When the adapters change, wrap them to conform to the `Wallet` interface\n        setWallets((wallets) =>\n            adapters\n                .map((adapter, index) => {\n                    const wallet = wallets[index];\n                    // If the wallet hasn't changed, return the same instance\n                    return wallet && wallet.adapter === adapter && wallet.readyState === adapter.readyState\n                        ? wallet\n                        : {\n                              adapter: adapter,\n                              readyState: adapter.readyState,\n                          };\n                })\n                .filter(({ readyState }) => readyState !== WalletReadyState.Unsupported)\n        );\n        function handleReadyStateChange(this: Adapter, readyState: WalletReadyState) {\n            setWallets((prevWallets) => {\n                const index = prevWallets.findIndex(({ adapter }) => adapter === this);\n                if (index === -1) return prevWallets;\n\n                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n                const { adapter } = prevWallets[index]!;\n                return [\n                    ...prevWallets.slice(0, index),\n                    { adapter, readyState },\n                    ...prevWallets.slice(index + 1),\n                ].filter(({ readyState }) => readyState !== WalletReadyState.Unsupported);\n            });\n        }\n        adapters.forEach((adapter) => adapter.on('readyStateChange', handleReadyStateChange, adapter));\n        return () => {\n            adapters.forEach((adapter) => adapter.off('readyStateChange', handleReadyStateChange, adapter));\n        };\n    }, [adapter, adapters]);\n\n    const wallet = useMemo(() => wallets.find((wallet) => wallet.adapter === adapter) ?? null, [adapter, wallets]);\n\n    // Setup and teardown event listeners when the adapter changes\n    useEffect(() => {\n        if (!adapter) return;\n\n        const handleConnect = (publicKey: PublicKey) => {\n            setPublicKey(publicKey);\n            isConnectingRef.current = false;\n            setConnecting(false);\n            setConnected(true);\n            isDisconnectingRef.current = false;\n            setDisconnecting(false);\n        };\n\n        const handleDisconnect = () => {\n            if (isUnloadingRef.current) return;\n\n            setPublicKey(null);\n            isConnectingRef.current = false;\n            setConnecting(false);\n            setConnected(false);\n            isDisconnectingRef.current = false;\n            setDisconnecting(false);\n        };\n\n        const handleError = (error: WalletError) => {\n            handleErrorRef.current(error, adapter);\n        };\n\n        adapter.on('connect', handleConnect);\n        adapter.on('disconnect', handleDisconnect);\n        adapter.on('error', handleError);\n\n        return () => {\n            adapter.off('connect', handleConnect);\n            adapter.off('disconnect', handleDisconnect);\n            adapter.off('error', handleError);\n\n            handleDisconnect();\n        };\n    }, [adapter, isUnloadingRef]);\n\n    // When the adapter changes, clear the `autoConnect` tracking flag\n    const didAttemptAutoConnectRef = useRef(false);\n    useEffect(() => {\n        return () => {\n            didAttemptAutoConnectRef.current = false;\n        };\n    }, [adapter]);\n\n    // If auto-connect is enabled, request to connect when the adapter changes and is ready\n    useEffect(() => {\n        if (\n            didAttemptAutoConnectRef.current ||\n            isConnectingRef.current ||\n            connected ||\n            !onAutoConnectRequest ||\n            !(wallet?.readyState === WalletReadyState.Installed || wallet?.readyState === WalletReadyState.Loadable)\n        )\n            return;\n\n        isConnectingRef.current = true;\n        setConnecting(true);\n        didAttemptAutoConnectRef.current = true;\n        (async function () {\n            try {\n                await onAutoConnectRequest();\n            } catch {\n                onConnectError();\n                // Drop the error. It will be caught by `handleError` anyway.\n            } finally {\n                setConnecting(false);\n                isConnectingRef.current = false;\n            }\n        })();\n    }, [connected, onAutoConnectRequest, onConnectError, wallet]);\n\n    // Send a transaction using the provided connection\n    const sendTransaction: WalletAdapterProps['sendTransaction'] = useCallback(\n        async (transaction, connection, options) => {\n            if (!adapter) throw handleErrorRef.current(new WalletNotSelectedError());\n            if (!connected) throw handleErrorRef.current(new WalletNotConnectedError(), adapter);\n            return await adapter.sendTransaction(transaction, connection, options);\n        },\n        [adapter, connected]\n    );\n\n    // Sign a transaction if the wallet supports it\n    const signTransaction: SignerWalletAdapterProps['signTransaction'] | undefined = useMemo(\n        () =>\n            adapter && 'signTransaction' in adapter\n                ? async (transaction) => {\n                      if (!connected) throw handleErrorRef.current(new WalletNotConnectedError(), adapter);\n                      return await adapter.signTransaction(transaction);\n                  }\n                : undefined,\n        [adapter, connected]\n    );\n\n    // Sign multiple transactions if the wallet supports it\n    const signAllTransactions: SignerWalletAdapterProps['signAllTransactions'] | undefined = useMemo(\n        () =>\n            adapter && 'signAllTransactions' in adapter\n                ? async (transactions) => {\n                      if (!connected) throw handleErrorRef.current(new WalletNotConnectedError(), adapter);\n                      return await adapter.signAllTransactions(transactions);\n                  }\n                : undefined,\n        [adapter, connected]\n    );\n\n    // Sign an arbitrary message if the wallet supports it\n    const signMessage: MessageSignerWalletAdapterProps['signMessage'] | undefined = useMemo(\n        () =>\n            adapter && 'signMessage' in adapter\n                ? async (message) => {\n                      if (!connected) throw handleErrorRef.current(new WalletNotConnectedError(), adapter);\n                      return await adapter.signMessage(message);\n                  }\n                : undefined,\n        [adapter, connected]\n    );\n\n    // Sign in if the wallet supports it\n    const signIn: SignInMessageSignerWalletAdapterProps['signIn'] | undefined = useMemo(\n        () =>\n            adapter && 'signIn' in adapter\n                ? async (input) => {\n                      return await adapter.signIn(input);\n                  }\n                : undefined,\n        [adapter]\n    );\n\n    const handleConnect = useCallback(async () => {\n        if (isConnectingRef.current || isDisconnectingRef.current || wallet?.adapter.connected) return;\n        if (!wallet) throw handleErrorRef.current(new WalletNotSelectedError());\n        const { adapter, readyState } = wallet;\n        if (!(readyState === WalletReadyState.Installed || readyState === WalletReadyState.Loadable))\n            throw handleErrorRef.current(new WalletNotReadyError(), adapter);\n        isConnectingRef.current = true;\n        setConnecting(true);\n        try {\n            await adapter.connect();\n        } catch (e) {\n            onConnectError();\n            throw e;\n        } finally {\n            setConnecting(false);\n            isConnectingRef.current = false;\n        }\n    }, [onConnectError, wallet]);\n\n    const handleDisconnect = useCallback(async () => {\n        if (isDisconnectingRef.current) return;\n        if (!adapter) return;\n        isDisconnectingRef.current = true;\n        setDisconnecting(true);\n        try {\n            await adapter.disconnect();\n        } finally {\n            setDisconnecting(false);\n            isDisconnectingRef.current = false;\n        }\n    }, [adapter]);\n\n    return (\n        <WalletContext.Provider\n            value={{\n                autoConnect: !!onAutoConnectRequest,\n                wallets,\n                wallet,\n                publicKey,\n                connected,\n                connecting,\n                disconnecting,\n                select: onSelectWallet,\n                connect: handleConnect,\n                disconnect: handleDisconnect,\n                sendTransaction,\n                signTransaction,\n                signAllTransactions,\n                signMessage,\n                signIn,\n            }}\n        >\n            {children}\n        </WalletContext.Provider>\n    );\n}\n", "import { useWallet, type Wallet } from '@solana/wallet-adapter-react';\nimport { useCallback } from 'react';\n\ntype ButtonState = {\n    buttonDisabled: boolean;\n    buttonState: 'connecting' | 'connected' | 'has-wallet' | 'no-wallet';\n    onButtonClick?: () => void;\n    walletIcon?: Wallet['adapter']['icon'];\n    walletName?: Wallet['adapter']['name'];\n};\n\nexport function useWalletConnectButton(): ButtonState {\n    const { connect, connected, connecting, wallet } = useWallet();\n    let buttonState: ButtonState['buttonState'];\n    if (connecting) {\n        buttonState = 'connecting';\n    } else if (connected) {\n        buttonState = 'connected';\n    } else if (wallet) {\n        buttonState = 'has-wallet';\n    } else {\n        buttonState = 'no-wallet';\n    }\n    const handleConnectButtonClick = useCallback(() => {\n        connect().catch(() => {\n            // Silently catch because any errors are caught by the context `onError` handler\n        });\n    }, [connect]);\n    return {\n        buttonDisabled: buttonState !== 'has-wallet',\n        buttonState,\n        onButtonClick: buttonState === 'has-wallet' ? handleConnectButtonClick : undefined,\n        walletIcon: wallet?.adapter.icon,\n        walletName: wallet?.adapter.name,\n    };\n}\n", "import { useWallet, type Wallet } from '@solana/wallet-adapter-react';\nimport { useCallback } from 'react';\n\ntype ButtonState = {\n    buttonDisabled: boolean;\n    buttonState: 'disconnecting' | 'has-wallet' | 'no-wallet';\n    onButtonClick?: () => void;\n    walletIcon?: Wallet['adapter']['icon'];\n    walletName?: Wallet['adapter']['name'];\n};\n\nexport function useWalletDisconnectButton(): ButtonState {\n    const { disconnecting, disconnect, wallet } = useWallet();\n    let buttonState: ButtonState['buttonState'];\n    if (disconnecting) {\n        buttonState = 'disconnecting';\n    } else if (wallet) {\n        buttonState = 'has-wallet';\n    } else {\n        buttonState = 'no-wallet';\n    }\n    const handleDisconnectButtonClick = useCallback(() => {\n        disconnect().catch(() => {\n            // Silently catch because any errors are caught by the context `onError` handler\n        });\n    }, [disconnect]);\n    return {\n        buttonDisabled: buttonState !== 'has-wallet',\n        buttonState,\n        onButtonClick: buttonState === 'has-wallet' ? handleDisconnectButtonClick : undefined,\n        walletIcon: wallet?.adapter.icon,\n        walletName: wallet?.adapter.name,\n    };\n}\n", "import { useWallet, type Wallet } from '@solana/wallet-adapter-react';\nimport type { PublicKey } from '@solana/web3.js';\nimport { useCallback } from 'react';\n\ntype ButtonState = {\n    buttonState: 'connecting' | 'connected' | 'disconnecting' | 'has-wallet' | 'no-wallet';\n    onConnect?: () => void;\n    onDisconnect?: () => void;\n    onSelectWallet?: () => void;\n    publicKey?: PublicKey;\n    walletIcon?: Wallet['adapter']['icon'];\n    walletName?: Wallet['adapter']['name'];\n};\n\ntype Config = {\n    onSelectWallet: (config: {\n        onSelectWallet: (walletName: Wallet['adapter']['name']) => void;\n        wallets: Wallet[];\n    }) => void;\n};\n\nexport function useWalletMultiButton({ onSelectWallet }: Config): ButtonState {\n    const { connect, connected, connecting, disconnect, disconnecting, publicKey, select, wallet, wallets } =\n        useWallet();\n    let buttonState: ButtonState['buttonState'];\n    if (connecting) {\n        buttonState = 'connecting';\n    } else if (connected) {\n        buttonState = 'connected';\n    } else if (disconnecting) {\n        buttonState = 'disconnecting';\n    } else if (wallet) {\n        buttonState = 'has-wallet';\n    } else {\n        buttonState = 'no-wallet';\n    }\n    const handleConnect = useCallback(() => {\n        connect().catch(() => {\n            // Silently catch because any errors are caught by the context `onError` handler\n        });\n    }, [connect]);\n    const handleDisconnect = useCallback(() => {\n        disconnect().catch(() => {\n            // Silently catch because any errors are caught by the context `onError` handler\n        });\n    }, [disconnect]);\n    const handleSelectWallet = useCallback(() => {\n        onSelectWallet({ onSelectWallet: select, wallets });\n    }, [onSelectWallet, select, wallets]);\n    return {\n        buttonState,\n        onConnect: buttonState === 'has-wallet' ? handleConnect : undefined,\n        onDisconnect: buttonState !== 'disconnecting' && buttonState !== 'no-wallet' ? handleDisconnect : undefined,\n        onSelectWallet: handleSelectWallet,\n        publicKey: publicKey ?? undefined,\n        walletIcon: wallet?.adapter.icon,\n        walletName: wallet?.adapter.name,\n    };\n}\n", "import { useWalletConnectButton } from '@solana/wallet-adapter-base-ui';\nimport React from 'react';\nimport { BaseWalletConnectionButton } from './BaseWalletConnectionButton.js';\nimport type { ButtonProps } from './Button.js';\n\ntype Props = ButtonProps & {\n    labels: { [TButtonState in ReturnType<typeof useWalletConnectButton>['buttonState']]: string };\n};\n\nexport function BaseWalletConnectButton({ children, disabled, labels, onClick, ...props }: Props) {\n    const { buttonDisabled, buttonState, onButtonClick, walletIcon, walletName } = useWalletConnectButton();\n    return (\n        <BaseWalletConnectionButton\n            {...props}\n            disabled={disabled || buttonDisabled}\n            onClick={(e) => {\n                if (onClick) {\n                    onClick(e);\n                }\n                if (e.defaultPrevented) {\n                    return;\n                }\n                if (onButtonClick) {\n                    onButtonClick();\n                }\n            }}\n            walletIcon={walletIcon}\n            walletName={walletName}\n        >\n            {children ? children : labels[buttonState]}\n        </BaseWalletConnectionButton>\n    );\n}\n", "import type { WalletName } from '@solana/wallet-adapter-base';\nimport React from 'react';\nimport { Button } from './Button.js';\nimport { WalletIcon } from './WalletIcon.js';\n\ntype Props = React.ComponentProps<typeof Button> & {\n    walletIcon?: string;\n    walletName?: WalletName;\n};\n\nexport function BaseWalletConnectionButton({ walletIcon, walletName, ...props }: Props) {\n    return (\n        <Button\n            {...props}\n            className=\"wallet-adapter-button-trigger\"\n            startIcon={\n                walletIcon && walletName ? (\n                    <WalletIcon wallet={{ adapter: { icon: walletIcon, name: walletName } }} />\n                ) : undefined\n            }\n        />\n    );\n}\n", "import type { CSSProperties, FC, MouseEvent, PropsWithChildren, ReactElement } from 'react';\nimport React from 'react';\n\nexport type ButtonProps = PropsWithChildren<{\n    className?: string;\n    disabled?: boolean;\n    endIcon?: ReactElement;\n    onClick?: (e: MouseEvent<HTMLButtonElement>) => void;\n    startIcon?: ReactElement;\n    style?: CSSProperties;\n    tabIndex?: number;\n}>;\n\nexport const Button: FC<ButtonProps> = (props) => {\n    return (\n        <button\n            className={`wallet-adapter-button ${props.className || ''}`}\n            disabled={props.disabled}\n            style={props.style}\n            onClick={props.onClick}\n            tabIndex={props.tabIndex || 0}\n            type=\"button\"\n        >\n            {props.startIcon && <i className=\"wallet-adapter-button-start-icon\">{props.startIcon}</i>}\n            {props.children}\n            {props.endIcon && <i className=\"wallet-adapter-button-end-icon\">{props.endIcon}</i>}\n        </button>\n    );\n};\n", "import type { Wallet } from '@solana/wallet-adapter-react';\nimport type { DetailedHTMLProps, FC, ImgHTMLAttributes } from 'react';\nimport React from 'react';\n\nexport interface WalletIconProps extends DetailedHTMLProps<ImgHTMLAttributes<HTMLImageElement>, HTMLImageElement> {\n    wallet: { adapter: Pick<Wallet['adapter'], 'icon' | 'name'> } | null;\n}\n\nexport const WalletIcon: FC<WalletIconProps> = ({ wallet, ...props }) => {\n    return wallet && <img src={wallet.adapter.icon} alt={`${wallet.adapter.name} icon`} {...props} />;\n};\n", "import { useWalletDisconnectButton } from '@solana/wallet-adapter-base-ui';\nimport React from 'react';\nimport { BaseWalletConnectionButton } from './BaseWalletConnectionButton.js';\nimport type { ButtonProps } from './Button.js';\n\ntype Props = ButtonProps & {\n    labels: { [TButtonState in ReturnType<typeof useWalletDisconnectButton>['buttonState']]: string };\n};\n\nexport function BaseWalletDisconnectButton({ children, disabled, labels, onClick, ...props }: Props) {\n    const { buttonDisabled, buttonState, onButtonClick, walletIcon, walletName } = useWalletDisconnectButton();\n    return (\n        <BaseWalletConnectionButton\n            {...props}\n            disabled={disabled || buttonDisabled}\n            onClick={(e) => {\n                if (onClick) {\n                    onClick(e);\n                }\n                if (e.defaultPrevented) {\n                    return;\n                }\n                if (onButtonClick) {\n                    onButtonClick();\n                }\n            }}\n            walletIcon={walletIcon}\n            walletName={walletName}\n        >\n            {children ? children : labels[buttonState]}\n        </BaseWalletConnectionButton>\n    );\n}\n", "import { useWalletMultiButton } from '@solana/wallet-adapter-base-ui';\nimport React, { useEffect, useMemo, useRef, useState } from 'react';\nimport { BaseWalletConnectionButton } from './BaseWalletConnectionButton.js';\nimport type { ButtonProps } from './Button.js';\nimport { useWalletModal } from './useWalletModal.js';\n\ntype Props = ButtonProps & {\n    labels: Omit<\n        { [TButtonState in ReturnType<typeof useWalletMultiButton>['buttonState']]: string },\n        'connected' | 'disconnecting'\n    > & {\n        'copy-address': string;\n        copied: string;\n        'change-wallet': string;\n        disconnect: string;\n    };\n};\n\nexport function BaseWalletMultiButton({ children, labels, ...props }: Props) {\n    const { setVisible: setModalVisible } = useWalletModal();\n    const { buttonState, onConnect, onDisconnect, publicKey, walletIcon, walletName } = useWalletMultiButton({\n        onSelectWallet() {\n            setModalVisible(true);\n        },\n    });\n    const [copied, setCopied] = useState(false);\n    const [menuOpen, setMenuOpen] = useState(false);\n    const ref = useRef<HTMLUListElement>(null);\n    useEffect(() => {\n        const listener = (event: MouseEvent | TouchEvent) => {\n            const node = ref.current;\n\n            // Do nothing if clicking dropdown or its descendants\n            if (!node || node.contains(event.target as Node)) return;\n\n            setMenuOpen(false);\n        };\n\n        document.addEventListener('mousedown', listener);\n        document.addEventListener('touchstart', listener);\n\n        return () => {\n            document.removeEventListener('mousedown', listener);\n            document.removeEventListener('touchstart', listener);\n        };\n    }, []);\n    const content = useMemo(() => {\n        if (children) {\n            return children;\n        } else if (publicKey) {\n            const base58 = publicKey.toBase58();\n            return base58.slice(0, 4) + '..' + base58.slice(-4);\n        } else if (buttonState === 'connecting' || buttonState === 'has-wallet') {\n            return labels[buttonState];\n        } else {\n            return labels['no-wallet'];\n        }\n    }, [buttonState, children, labels, publicKey]);\n    return (\n        <div className=\"wallet-adapter-dropdown\">\n            <BaseWalletConnectionButton\n                {...props}\n                aria-expanded={menuOpen}\n                style={{ pointerEvents: menuOpen ? 'none' : 'auto', ...props.style }}\n                onClick={() => {\n                    switch (buttonState) {\n                        case 'no-wallet':\n                            setModalVisible(true);\n                            break;\n                        case 'has-wallet':\n                            if (onConnect) {\n                                onConnect();\n                            }\n                            break;\n                        case 'connected':\n                            setMenuOpen(true);\n                            break;\n                    }\n                }}\n                walletIcon={walletIcon}\n                walletName={walletName}\n            >\n                {content}\n            </BaseWalletConnectionButton>\n            <ul\n                aria-label=\"dropdown-list\"\n                className={`wallet-adapter-dropdown-list ${menuOpen && 'wallet-adapter-dropdown-list-active'}`}\n                ref={ref}\n                role=\"menu\"\n            >\n                {publicKey ? (\n                    <li\n                        className=\"wallet-adapter-dropdown-list-item\"\n                        onClick={async () => {\n                            await navigator.clipboard.writeText(publicKey.toBase58());\n                            setCopied(true);\n                            setTimeout(() => setCopied(false), 400);\n                        }}\n                        role=\"menuitem\"\n                    >\n                        {copied ? labels['copied'] : labels['copy-address']}\n                    </li>\n                ) : null}\n                <li\n                    className=\"wallet-adapter-dropdown-list-item\"\n                    onClick={() => {\n                        setModalVisible(true);\n                        setMenuOpen(false);\n                    }}\n                    role=\"menuitem\"\n                >\n                    {labels['change-wallet']}\n                </li>\n                {onDisconnect ? (\n                    <li\n                        className=\"wallet-adapter-dropdown-list-item\"\n                        onClick={() => {\n                            onDisconnect();\n                            setMenuOpen(false);\n                        }}\n                        role=\"menuitem\"\n                    >\n                        {labels['disconnect']}\n                    </li>\n                ) : null}\n            </ul>\n        </div>\n    );\n}\n", "import React from 'react';\nimport { BaseWalletConnectButton } from './BaseWalletConnectButton.js';\nimport type { ButtonProps } from './Button.js';\n\nconst LABELS = {\n    connecting: 'Connecting ...',\n    connected: 'Connected',\n    'has-wallet': 'Connect',\n    'no-wallet': 'Connect Wallet',\n} as const;\n\nexport function WalletConnectButton(props: ButtonProps) {\n    return <BaseWalletConnectButton {...props} labels={LABELS} />;\n}\n", "import type { WalletName } from '@solana/wallet-adapter-base';\nimport { WalletReadyState } from '@solana/wallet-adapter-base';\nimport type { Wallet } from '@solana/wallet-adapter-react';\nimport { useWallet } from '@solana/wallet-adapter-react';\nimport type { FC, MouseEvent } from 'react';\nimport React, { useCallback, useLayoutEffect, useMemo, useRef, useState } from 'react';\nimport { createPortal } from 'react-dom';\nimport { Collapse } from './Collapse.js';\nimport { useWalletModal } from './useWalletModal.js';\nimport { WalletListItem } from './WalletListItem.js';\nimport { WalletSVG } from './WalletSVG.js';\n\nexport interface WalletModalProps {\n    className?: string;\n    container?: string;\n}\n\nexport const WalletModal: FC<WalletModalProps> = ({ className = '', container = 'body' }) => {\n    const ref = useRef<HTMLDivElement>(null);\n    const { wallets, select } = useWallet();\n    const { setVisible } = useWalletModal();\n    const [expanded, setExpanded] = useState(false);\n    const [fadeIn, setFadeIn] = useState(false);\n    const [portal, setPortal] = useState<Element | null>(null);\n\n    const [listedWallets, collapsedWallets] = useMemo(() => {\n        const installed: Wallet[] = [];\n        const loadable: Wallet[] = [];\n        const notDetected: Wallet[] = [];\n\n        for (const wallet of wallets) {\n            if (wallet.readyState === WalletReadyState.NotDetected) {\n                notDetected.push(wallet);\n            } else if (wallet.readyState === WalletReadyState.Loadable) {\n                loadable.push(wallet);\n            } else if (wallet.readyState === WalletReadyState.Installed) {\n                installed.push(wallet);\n            }\n        }\n\n        let listed: Wallet[] = [];\n        let collapsed: Wallet[] = [];\n\n        if (installed.length) {\n            listed = installed;\n            collapsed = [...loadable, ...notDetected];\n        } else if (loadable.length) {\n            listed = loadable;\n            collapsed = notDetected;\n        } else {\n            collapsed = notDetected;\n        }\n\n        return [listed, collapsed];\n    }, [wallets]);\n\n    const hideModal = useCallback(() => {\n        setFadeIn(false);\n        setTimeout(() => setVisible(false), 150);\n    }, [setVisible]);\n\n    const handleClose = useCallback(\n        (event: MouseEvent) => {\n            event.preventDefault();\n            hideModal();\n        },\n        [hideModal]\n    );\n\n    const handleWalletClick = useCallback(\n        (event: MouseEvent, walletName: WalletName) => {\n            select(walletName);\n            handleClose(event);\n        },\n        [select, handleClose]\n    );\n\n    const handleCollapseClick = useCallback(() => setExpanded(!expanded), [expanded]);\n\n    const handleTabKey = useCallback(\n        (event: KeyboardEvent) => {\n            const node = ref.current;\n            if (!node) return;\n\n            // here we query all focusable elements\n            const focusableElements = node.querySelectorAll('button');\n            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n            const firstElement = focusableElements[0]!;\n            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n            const lastElement = focusableElements[focusableElements.length - 1]!;\n\n            if (event.shiftKey) {\n                // if going backward by pressing tab and firstElement is active, shift focus to last focusable element\n                if (document.activeElement === firstElement) {\n                    lastElement.focus();\n                    event.preventDefault();\n                }\n            } else {\n                // if going forward by pressing tab and lastElement is active, shift focus to first focusable element\n                if (document.activeElement === lastElement) {\n                    firstElement.focus();\n                    event.preventDefault();\n                }\n            }\n        },\n        [ref]\n    );\n\n    useLayoutEffect(() => {\n        const handleKeyDown = (event: KeyboardEvent) => {\n            if (event.key === 'Escape') {\n                hideModal();\n            } else if (event.key === 'Tab') {\n                handleTabKey(event);\n            }\n        };\n\n        // Get original overflow\n        const { overflow } = window.getComputedStyle(document.body);\n        // Hack to enable fade in animation after mount\n        setTimeout(() => setFadeIn(true), 0);\n        // Prevent scrolling on mount\n        document.body.style.overflow = 'hidden';\n        // Listen for keydown events\n        window.addEventListener('keydown', handleKeyDown, false);\n\n        return () => {\n            // Re-enable scrolling when component unmounts\n            document.body.style.overflow = overflow;\n            window.removeEventListener('keydown', handleKeyDown, false);\n        };\n    }, [hideModal, handleTabKey]);\n\n    useLayoutEffect(() => setPortal(document.querySelector(container)), [container]);\n\n    return (\n        portal &&\n        createPortal(\n            <div\n                aria-labelledby=\"wallet-adapter-modal-title\"\n                aria-modal=\"true\"\n                className={`wallet-adapter-modal ${fadeIn && 'wallet-adapter-modal-fade-in'} ${className}`}\n                ref={ref}\n                role=\"dialog\"\n            >\n                <div className=\"wallet-adapter-modal-container\">\n                    <div className=\"wallet-adapter-modal-wrapper\">\n                        <button onClick={handleClose} className=\"wallet-adapter-modal-button-close\">\n                            <svg width=\"14\" height=\"14\">\n                                <path d=\"M14 12.461 8.3 6.772l5.234-5.233L12.006 0 6.772 5.234 1.54 0 0 1.539l5.234 5.233L0 12.006l1.539 1.528L6.772 8.3l5.69 5.7L14 12.461z\" />\n                            </svg>\n                        </button>\n                        {listedWallets.length ? (\n                            <>\n                                <h1 className=\"wallet-adapter-modal-title\">Connect a wallet on Solana to continue</h1>\n                                <ul className=\"wallet-adapter-modal-list\">\n                                    {listedWallets.map((wallet) => (\n                                        <WalletListItem\n                                            key={wallet.adapter.name}\n                                            handleClick={(event) => handleWalletClick(event, wallet.adapter.name)}\n                                            wallet={wallet}\n                                        />\n                                    ))}\n                                    {collapsedWallets.length ? (\n                                        <Collapse expanded={expanded} id=\"wallet-adapter-modal-collapse\">\n                                            {collapsedWallets.map((wallet) => (\n                                                <WalletListItem\n                                                    key={wallet.adapter.name}\n                                                    handleClick={(event) =>\n                                                        handleWalletClick(event, wallet.adapter.name)\n                                                    }\n                                                    tabIndex={expanded ? 0 : -1}\n                                                    wallet={wallet}\n                                                />\n                                            ))}\n                                        </Collapse>\n                                    ) : null}\n                                </ul>\n                                {collapsedWallets.length ? (\n                                    <button\n                                        className=\"wallet-adapter-modal-list-more\"\n                                        onClick={handleCollapseClick}\n                                        tabIndex={0}\n                                    >\n                                        <span>{expanded ? 'Less ' : 'More '}options</span>\n                                        <svg\n                                            width=\"13\"\n                                            height=\"7\"\n                                            viewBox=\"0 0 13 7\"\n                                            xmlns=\"http://www.w3.org/2000/svg\"\n                                            className={`${\n                                                expanded ? 'wallet-adapter-modal-list-more-icon-rotate' : ''\n                                            }`}\n                                        >\n                                            <path d=\"M0.71418 1.626L5.83323 6.26188C5.91574 6.33657 6.0181 6.39652 6.13327 6.43762C6.24844 6.47872 6.37371 6.5 6.50048 6.5C6.62725 6.5 6.75252 6.47872 6.8677 6.43762C6.98287 6.39652 7.08523 6.33657 7.16774 6.26188L12.2868 1.626C12.7753 1.1835 12.3703 0.5 11.6195 0.5H1.37997C0.629216 0.5 0.224175 1.1835 0.71418 1.626Z\" />\n                                        </svg>\n                                    </button>\n                                ) : null}\n                            </>\n                        ) : (\n                            <>\n                                <h1 className=\"wallet-adapter-modal-title\">\n                                    You'll need a wallet on Solana to continue\n                                </h1>\n                                <div className=\"wallet-adapter-modal-middle\">\n                                    <WalletSVG />\n                                </div>\n                                {collapsedWallets.length ? (\n                                    <>\n                                        <button\n                                            className=\"wallet-adapter-modal-list-more\"\n                                            onClick={handleCollapseClick}\n                                            tabIndex={0}\n                                        >\n                                            <span>{expanded ? 'Hide ' : 'Already have a wallet? View '}options</span>\n                                            <svg\n                                                width=\"13\"\n                                                height=\"7\"\n                                                viewBox=\"0 0 13 7\"\n                                                xmlns=\"http://www.w3.org/2000/svg\"\n                                                className={`${\n                                                    expanded ? 'wallet-adapter-modal-list-more-icon-rotate' : ''\n                                                }`}\n                                            >\n                                                <path d=\"M0.71418 1.626L5.83323 6.26188C5.91574 6.33657 6.0181 6.39652 6.13327 6.43762C6.24844 6.47872 6.37371 6.5 6.50048 6.5C6.62725 6.5 6.75252 6.47872 6.8677 6.43762C6.98287 6.39652 7.08523 6.33657 7.16774 6.26188L12.2868 1.626C12.7753 1.1835 12.3703 0.5 11.6195 0.5H1.37997C0.629216 0.5 0.224175 1.1835 0.71418 1.626Z\" />\n                                            </svg>\n                                        </button>\n                                        <Collapse expanded={expanded} id=\"wallet-adapter-modal-collapse\">\n                                            <ul className=\"wallet-adapter-modal-list\">\n                                                {collapsedWallets.map((wallet) => (\n                                                    <WalletListItem\n                                                        key={wallet.adapter.name}\n                                                        handleClick={(event) =>\n                                                            handleWalletClick(event, wallet.adapter.name)\n                                                        }\n                                                        tabIndex={expanded ? 0 : -1}\n                                                        wallet={wallet}\n                                                    />\n                                                ))}\n                                            </ul>\n                                        </Collapse>\n                                    </>\n                                ) : null}\n                            </>\n                        )}\n                    </div>\n                </div>\n                <div className=\"wallet-adapter-modal-overlay\" onMouseDown={handleClose} />\n            </div>,\n            portal\n        )\n    );\n};\n", "import type { FC, PropsWithChildren } from 'react';\nimport React, { useLayoutEffect, useRef } from 'react';\n\nexport type CollapseProps = PropsWithChildren<{\n    expanded: boolean;\n    id: string;\n}>;\n\nexport const Collapse: FC<CollapseProps> = ({ id, children, expanded = false }) => {\n    const ref = useRef<HTMLDivElement>(null);\n    const instant = useRef(true);\n    const transition = 'height 250ms ease-out';\n\n    const openCollapse = () => {\n        const node = ref.current;\n        if (!node) return;\n\n        requestAnimationFrame(() => {\n            node.style.height = node.scrollHeight + 'px';\n        });\n    };\n\n    const closeCollapse = () => {\n        const node = ref.current;\n        if (!node) return;\n\n        requestAnimationFrame(() => {\n            node.style.height = node.offsetHeight + 'px';\n            node.style.overflow = 'hidden';\n            requestAnimationFrame(() => {\n                node.style.height = '0';\n            });\n        });\n    };\n\n    useLayoutEffect(() => {\n        if (expanded) {\n            openCollapse();\n        } else {\n            closeCollapse();\n        }\n    }, [expanded]);\n\n    useLayoutEffect(() => {\n        const node = ref.current;\n        if (!node) return;\n\n        function handleComplete() {\n            if (!node) return;\n\n            node.style.overflow = expanded ? 'initial' : 'hidden';\n            if (expanded) {\n                node.style.height = 'auto';\n            }\n        }\n\n        function handleTransitionEnd(event: TransitionEvent) {\n            if (node && event.target === node && event.propertyName === 'height') {\n                handleComplete();\n            }\n        }\n\n        if (instant.current) {\n            handleComplete();\n            instant.current = false;\n        }\n\n        node.addEventListener('transitionend', handleTransitionEnd);\n        return () => node.removeEventListener('transitionend', handleTransitionEnd);\n    }, [expanded]);\n\n    return (\n        <div\n            className=\"wallet-adapter-collapse\"\n            id={id}\n            ref={ref}\n            role=\"region\"\n            style={{ height: 0, transition: instant.current ? undefined : transition }}\n        >\n            {children}\n        </div>\n    );\n};\n", "import { WalletReadyState } from '@solana/wallet-adapter-base';\nimport type { Wallet } from '@solana/wallet-adapter-react';\nimport type { FC, MouseEventHandler } from 'react';\nimport React from 'react';\nimport { Button } from './Button.js';\nimport { WalletIcon } from './WalletIcon.js';\n\nexport interface WalletListItemProps {\n    handleClick: MouseEventHandler<HTMLButtonElement>;\n    tabIndex?: number;\n    wallet: Wallet;\n}\n\nexport const WalletListItem: FC<WalletListItemProps> = ({ handleClick, tabIndex, wallet }) => {\n    return (\n        <li>\n            <Button onClick={handleClick} startIcon={<WalletIcon wallet={wallet} />} tabIndex={tabIndex}>\n                {wallet.adapter.name}\n                {wallet.readyState === WalletReadyState.Installed && <span>Detected</span>}\n            </Button>\n        </li>\n    );\n};\n", "import type { FC } from 'react';\nimport React from 'react';\n\nexport const WalletSVG: FC = () => {\n    return (\n        <svg width=\"97\" height=\"96\" viewBox=\"0 0 97 96\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <circle cx=\"48.5\" cy=\"48\" r=\"48\" fill=\"url(#paint0_linear_880_5115)\" fillOpacity=\"0.1\" />\n            <circle\n                cx=\"48.5\"\n                cy=\"48\"\n                r=\"47\"\n                stroke=\"url(#paint1_linear_880_5115)\"\n                strokeOpacity=\"0.4\"\n                strokeWidth=\"2\"\n            />\n            <g clipPath=\"url(#clip0_880_5115)\">\n                <path\n                    d=\"M65.5769 28.1523H31.4231C27.6057 28.1523 24.5 31.258 24.5 35.0754V60.9215C24.5 64.7389 27.6057 67.8446 31.4231 67.8446H65.5769C69.3943 67.8446 72.5 64.7389 72.5 60.9215V35.0754C72.5 31.258 69.3943 28.1523 65.5769 28.1523ZM69.7308 52.1523H59.5769C57.2865 52.1523 55.4231 50.289 55.4231 47.9985C55.4231 45.708 57.2864 43.8446 59.5769 43.8446H69.7308V52.1523ZM69.7308 41.0754H59.5769C55.7595 41.0754 52.6539 44.1811 52.6539 47.9985C52.6539 51.8159 55.7595 54.9215 59.5769 54.9215H69.7308V60.9215C69.7308 63.2119 67.8674 65.0754 65.5769 65.0754H31.4231C29.1327 65.0754 27.2692 63.212 27.2692 60.9215V35.0754C27.2692 32.785 29.1326 30.9215 31.4231 30.9215H65.5769C67.8673 30.9215 69.7308 32.7849 69.7308 35.0754V41.0754Z\"\n                    fill=\"url(#paint2_linear_880_5115)\"\n                />\n                <path\n                    d=\"M61.4231 46.6172H59.577C58.8123 46.6172 58.1924 47.2371 58.1924 48.0018C58.1924 48.7665 58.8123 49.3863 59.577 49.3863H61.4231C62.1878 49.3863 62.8077 48.7664 62.8077 48.0018C62.8077 47.2371 62.1878 46.6172 61.4231 46.6172Z\"\n                    fill=\"url(#paint3_linear_880_5115)\"\n                />\n            </g>\n            <defs>\n                <linearGradient\n                    id=\"paint0_linear_880_5115\"\n                    x1=\"3.41664\"\n                    y1=\"98.0933\"\n                    x2=\"103.05\"\n                    y2=\"8.42498\"\n                    gradientUnits=\"userSpaceOnUse\"\n                >\n                    <stop stopColor=\"#9945FF\" />\n                    <stop offset=\"0.14\" stopColor=\"#8A53F4\" />\n                    <stop offset=\"0.42\" stopColor=\"#6377D6\" />\n                    <stop offset=\"0.79\" stopColor=\"#24B0A7\" />\n                    <stop offset=\"0.99\" stopColor=\"#00D18C\" />\n                    <stop offset=\"1\" stopColor=\"#00D18C\" />\n                </linearGradient>\n                <linearGradient\n                    id=\"paint1_linear_880_5115\"\n                    x1=\"3.41664\"\n                    y1=\"98.0933\"\n                    x2=\"103.05\"\n                    y2=\"8.42498\"\n                    gradientUnits=\"userSpaceOnUse\"\n                >\n                    <stop stopColor=\"#9945FF\" />\n                    <stop offset=\"0.14\" stopColor=\"#8A53F4\" />\n                    <stop offset=\"0.42\" stopColor=\"#6377D6\" />\n                    <stop offset=\"0.79\" stopColor=\"#24B0A7\" />\n                    <stop offset=\"0.99\" stopColor=\"#00D18C\" />\n                    <stop offset=\"1\" stopColor=\"#00D18C\" />\n                </linearGradient>\n                <linearGradient\n                    id=\"paint2_linear_880_5115\"\n                    x1=\"25.9583\"\n                    y1=\"68.7101\"\n                    x2=\"67.2337\"\n                    y2=\"23.7879\"\n                    gradientUnits=\"userSpaceOnUse\"\n                >\n                    <stop stopColor=\"#9945FF\" />\n                    <stop offset=\"0.14\" stopColor=\"#8A53F4\" />\n                    <stop offset=\"0.42\" stopColor=\"#6377D6\" />\n                    <stop offset=\"0.79\" stopColor=\"#24B0A7\" />\n                    <stop offset=\"0.99\" stopColor=\"#00D18C\" />\n                    <stop offset=\"1\" stopColor=\"#00D18C\" />\n                </linearGradient>\n                <linearGradient\n                    id=\"paint3_linear_880_5115\"\n                    x1=\"58.3326\"\n                    y1=\"49.4467\"\n                    x2=\"61.0002\"\n                    y2=\"45.4453\"\n                    gradientUnits=\"userSpaceOnUse\"\n                >\n                    <stop stopColor=\"#9945FF\" />\n                    <stop offset=\"0.14\" stopColor=\"#8A53F4\" />\n                    <stop offset=\"0.42\" stopColor=\"#6377D6\" />\n                    <stop offset=\"0.79\" stopColor=\"#24B0A7\" />\n                    <stop offset=\"0.99\" stopColor=\"#00D18C\" />\n                    <stop offset=\"1\" stopColor=\"#00D18C\" />\n                </linearGradient>\n                <clipPath id=\"clip0_880_5115\">\n                    <rect width=\"48\" height=\"48\" fill=\"white\" transform=\"translate(24.5 24)\" />\n                </clipPath>\n            </defs>\n        </svg>\n    );\n};\n", "import type { FC, MouseEvent } from 'react';\nimport React, { useCallback } from 'react';\nimport type { ButtonProps } from './Button.js';\nimport { Button as BaseWalletConnectionButton } from './Button.js';\nimport { useWalletModal } from './useWalletModal.js';\n\nexport const WalletModalButton: FC<ButtonProps> = ({ children = 'Select Wallet', onClick, ...props }) => {\n    const { visible, setVisible } = useWalletModal();\n\n    const handleClick = useCallback(\n        (event: MouseEvent<HTMLButtonElement>) => {\n            if (onClick) onClick(event);\n            if (!event.defaultPrevented) setVisible(!visible);\n        },\n        [onClick, setVisible, visible]\n    );\n\n    return (\n        <BaseWalletConnectionButton {...props} className=\"wallet-adapter-button-trigger\" onClick={handleClick}>\n            {children}\n        </BaseWalletConnectionButton>\n    );\n};\n", "import type { FC, ReactNode } from 'react';\nimport React, { useState } from 'react';\nimport { WalletModalContext } from './useWalletModal.js';\nimport type { WalletModalProps } from './WalletModal.js';\nimport { WalletModal } from './WalletModal.js';\n\nexport interface WalletModalProviderProps extends WalletModalProps {\n    children: ReactNode;\n}\n\nexport const WalletModalProvider: FC<WalletModalProviderProps> = ({ children, ...props }) => {\n    const [visible, setVisible] = useState(false);\n\n    return (\n        <WalletModalContext.Provider\n            value={{\n                visible,\n                setVisible,\n            }}\n        >\n            {children}\n            {visible && <WalletModal {...props} />}\n        </WalletModalContext.Provider>\n    );\n};\n", "import React from 'react';\nimport { BaseWalletDisconnectButton } from './BaseWalletDisconnectButton.js';\nimport type { ButtonProps } from './Button.js';\n\nconst LABELS = {\n    disconnecting: 'Disconnecting ...',\n    'has-wallet': 'Disconnect',\n    'no-wallet': 'Disconnect Wallet',\n} as const;\n\nexport function WalletDisconnectButton(props: ButtonProps) {\n    return <BaseWalletDisconnectButton {...props} labels={LABELS} />;\n}\n", "import React from 'react';\nimport { BaseWalletMultiButton } from './BaseWalletMultiButton.js';\nimport type { ButtonProps } from './Button.js';\n\nconst LABELS = {\n    'change-wallet': 'Change wallet',\n    connecting: 'Connecting ...',\n    'copy-address': 'Copy address',\n    copied: 'Copied',\n    disconnect: 'Disconnect',\n    'has-wallet': 'Connect',\n    'no-wallet': 'Select Wallet',\n} as const;\n\nexport function WalletMultiButton(props: ButtonProps) {\n    return <BaseWalletMultiButton {...props} labels={LABELS} />;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAEA,QAAI,MAAM,OAAO,UAAU;AAA3B,QACI,SAAS;AASb,aAAS,SAAS;AAAA,IAAC;AASnB,QAAI,OAAO,QAAQ;AACjB,aAAO,YAAY,uBAAO,OAAO,IAAI;AAMrC,UAAI,CAAC,IAAI,OAAO,EAAE;AAAW,iBAAS;AAAA,IACxC;AAWA,aAAS,GAAG,IAAI,SAAS,MAAM;AAC7B,WAAK,KAAK;AACV,WAAK,UAAU;AACf,WAAK,OAAO,QAAQ;AAAA,IACtB;AAaA,aAAS,YAAY,SAAS,OAAO,IAAI,SAAS,MAAM;AACtD,UAAI,OAAO,OAAO,YAAY;AAC5B,cAAM,IAAI,UAAU,iCAAiC;AAAA,MACvD;AAEA,UAAI,WAAW,IAAI,GAAG,IAAI,WAAW,SAAS,IAAI,GAC9C,MAAM,SAAS,SAAS,QAAQ;AAEpC,UAAI,CAAC,QAAQ,QAAQ,GAAG;AAAG,gBAAQ,QAAQ,GAAG,IAAI,UAAU,QAAQ;AAAA,eAC3D,CAAC,QAAQ,QAAQ,GAAG,EAAE;AAAI,gBAAQ,QAAQ,GAAG,EAAE,KAAK,QAAQ;AAAA;AAChE,gBAAQ,QAAQ,GAAG,IAAI,CAAC,QAAQ,QAAQ,GAAG,GAAG,QAAQ;AAE3D,aAAO;AAAA,IACT;AASA,aAAS,WAAW,SAAS,KAAK;AAChC,UAAI,EAAE,QAAQ,iBAAiB;AAAG,gBAAQ,UAAU,IAAI,OAAO;AAAA;AAC1D,eAAO,QAAQ,QAAQ,GAAG;AAAA,IACjC;AASA,aAASA,gBAAe;AACtB,WAAK,UAAU,IAAI,OAAO;AAC1B,WAAK,eAAe;AAAA,IACtB;AASA,IAAAA,cAAa,UAAU,aAAa,SAAS,aAAa;AACxD,UAAI,QAAQ,CAAC,GACT,QACA;AAEJ,UAAI,KAAK,iBAAiB;AAAG,eAAO;AAEpC,WAAK,QAAS,SAAS,KAAK,SAAU;AACpC,YAAI,IAAI,KAAK,QAAQ,IAAI;AAAG,gBAAM,KAAK,SAAS,KAAK,MAAM,CAAC,IAAI,IAAI;AAAA,MACtE;AAEA,UAAI,OAAO,uBAAuB;AAChC,eAAO,MAAM,OAAO,OAAO,sBAAsB,MAAM,CAAC;AAAA,MAC1D;AAEA,aAAO;AAAA,IACT;AASA,IAAAA,cAAa,UAAU,YAAY,SAAS,UAAU,OAAO;AAC3D,UAAI,MAAM,SAAS,SAAS,QAAQ,OAChC,WAAW,KAAK,QAAQ,GAAG;AAE/B,UAAI,CAAC;AAAU,eAAO,CAAC;AACvB,UAAI,SAAS;AAAI,eAAO,CAAC,SAAS,EAAE;AAEpC,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,KAAK;AAClE,WAAG,CAAC,IAAI,SAAS,CAAC,EAAE;AAAA,MACtB;AAEA,aAAO;AAAA,IACT;AASA,IAAAA,cAAa,UAAU,gBAAgB,SAAS,cAAc,OAAO;AACnE,UAAI,MAAM,SAAS,SAAS,QAAQ,OAChC,YAAY,KAAK,QAAQ,GAAG;AAEhC,UAAI,CAAC;AAAW,eAAO;AACvB,UAAI,UAAU;AAAI,eAAO;AACzB,aAAO,UAAU;AAAA,IACnB;AASA,IAAAA,cAAa,UAAU,OAAO,SAAS,KAAK,OAAO,IAAI,IAAI,IAAI,IAAI,IAAI;AACrE,UAAI,MAAM,SAAS,SAAS,QAAQ;AAEpC,UAAI,CAAC,KAAK,QAAQ,GAAG;AAAG,eAAO;AAE/B,UAAI,YAAY,KAAK,QAAQ,GAAG,GAC5B,MAAM,UAAU,QAChB,MACA;AAEJ,UAAI,UAAU,IAAI;AAChB,YAAI,UAAU;AAAM,eAAK,eAAe,OAAO,UAAU,IAAI,QAAW,IAAI;AAE5E,gBAAQ,KAAK;AAAA,UACX,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,OAAO,GAAG;AAAA,UACrD,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,EAAE,GAAG;AAAA,UACzD,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,EAAE,GAAG;AAAA,UAC7D,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,IAAI,EAAE,GAAG;AAAA,UACjE,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,IAAI,IAAI,EAAE,GAAG;AAAA,UACrE,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG;AAAA,QAC3E;AAEA,aAAK,IAAI,GAAG,OAAO,IAAI,MAAM,MAAK,CAAC,GAAG,IAAI,KAAK,KAAK;AAClD,eAAK,IAAI,CAAC,IAAI,UAAU,CAAC;AAAA,QAC3B;AAEA,kBAAU,GAAG,MAAM,UAAU,SAAS,IAAI;AAAA,MAC5C,OAAO;AACL,YAAI,SAAS,UAAU,QACnB;AAEJ,aAAK,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC3B,cAAI,UAAU,CAAC,EAAE;AAAM,iBAAK,eAAe,OAAO,UAAU,CAAC,EAAE,IAAI,QAAW,IAAI;AAElF,kBAAQ,KAAK;AAAA,YACX,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,OAAO;AAAG;AAAA,YACpD,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,SAAS,EAAE;AAAG;AAAA,YACxD,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,SAAS,IAAI,EAAE;AAAG;AAAA,YAC5D,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,SAAS,IAAI,IAAI,EAAE;AAAG;AAAA,YAChE;AACE,kBAAI,CAAC;AAAM,qBAAK,IAAI,GAAG,OAAO,IAAI,MAAM,MAAK,CAAC,GAAG,IAAI,KAAK,KAAK;AAC7D,uBAAK,IAAI,CAAC,IAAI,UAAU,CAAC;AAAA,gBAC3B;AAEA,wBAAU,CAAC,EAAE,GAAG,MAAM,UAAU,CAAC,EAAE,SAAS,IAAI;AAAA,UACpD;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAWA,IAAAA,cAAa,UAAU,KAAK,SAAS,GAAG,OAAO,IAAI,SAAS;AAC1D,aAAO,YAAY,MAAM,OAAO,IAAI,SAAS,KAAK;AAAA,IACpD;AAWA,IAAAA,cAAa,UAAU,OAAO,SAAS,KAAK,OAAO,IAAI,SAAS;AAC9D,aAAO,YAAY,MAAM,OAAO,IAAI,SAAS,IAAI;AAAA,IACnD;AAYA,IAAAA,cAAa,UAAU,iBAAiB,SAAS,eAAe,OAAO,IAAI,SAAS,MAAM;AACxF,UAAI,MAAM,SAAS,SAAS,QAAQ;AAEpC,UAAI,CAAC,KAAK,QAAQ,GAAG;AAAG,eAAO;AAC/B,UAAI,CAAC,IAAI;AACP,mBAAW,MAAM,GAAG;AACpB,eAAO;AAAA,MACT;AAEA,UAAI,YAAY,KAAK,QAAQ,GAAG;AAEhC,UAAI,UAAU,IAAI;AAChB,YACE,UAAU,OAAO,OAChB,CAAC,QAAQ,UAAU,UACnB,CAAC,WAAW,UAAU,YAAY,UACnC;AACA,qBAAW,MAAM,GAAG;AAAA,QACtB;AAAA,MACF,OAAO;AACL,iBAAS,IAAI,GAAG,SAAS,CAAC,GAAG,SAAS,UAAU,QAAQ,IAAI,QAAQ,KAAK;AACvE,cACE,UAAU,CAAC,EAAE,OAAO,MACnB,QAAQ,CAAC,UAAU,CAAC,EAAE,QACtB,WAAW,UAAU,CAAC,EAAE,YAAY,SACrC;AACA,mBAAO,KAAK,UAAU,CAAC,CAAC;AAAA,UAC1B;AAAA,QACF;AAKA,YAAI,OAAO;AAAQ,eAAK,QAAQ,GAAG,IAAI,OAAO,WAAW,IAAI,OAAO,CAAC,IAAI;AAAA;AACpE,qBAAW,MAAM,GAAG;AAAA,MAC3B;AAEA,aAAO;AAAA,IACT;AASA,IAAAA,cAAa,UAAU,qBAAqB,SAAS,mBAAmB,OAAO;AAC7E,UAAI;AAEJ,UAAI,OAAO;AACT,cAAM,SAAS,SAAS,QAAQ;AAChC,YAAI,KAAK,QAAQ,GAAG;AAAG,qBAAW,MAAM,GAAG;AAAA,MAC7C,OAAO;AACL,aAAK,UAAU,IAAI,OAAO;AAC1B,aAAK,eAAe;AAAA,MACtB;AAEA,aAAO;AAAA,IACT;AAKA,IAAAA,cAAa,UAAU,MAAMA,cAAa,UAAU;AACpD,IAAAA,cAAa,UAAU,cAAcA,cAAa,UAAU;AAK5D,IAAAA,cAAa,WAAW;AAKxB,IAAAA,cAAa,eAAeA;AAK5B,QAAI,gBAAgB,OAAO,QAAQ;AACjC,aAAO,UAAUA;AAAA,IACnB;AAAA;AAAA;;;AC/UA,mBAA0C;AAO1C,IAAM,kBAAkB;EACpB,WAAW,OAAc;AACrB,YAAQ,MAAM,qCAAqC,QAAQ,YAAY,CAAC;EAC5E;EACA,SAAS;;AAEb,OAAO,eAAe,iBAAiB,WAAW;EAC9C,MAAG;AACC,YAAQ,MAAM,qCAAqC,QAAQ,SAAS,CAAC;AACrE,WAAO;EACX;CACH;AAED,SAAS,qCAAqC,QAAgB,WAAiB;AAC3E,SACI,sBACI,MAAM,KAAK,SAAS;AAMhC;AAEO,IAAM,yBAAqB,4BAAuC,eAA0C;AAE7G,SAAU,iBAAc;AAC1B,aAAO,yBAAW,kBAAkB;AACxC;;;ACnCA;AACA,IAAAC,gBAAwD;;;ACAxD,IAAAC,gBAA0C;AAMnC,IAAM,wBAAoB,6BAAsC,CAAA,CAA4B;;;ACPnG,mBAAyB;;;ACqDzB,IAAYC;CAAZ,SAAYA,mBAAgB;AAMxB,EAAAA,kBAAA,WAAA,IAAA;AACA,EAAAA,kBAAA,aAAA,IAAA;AAKA,EAAAA,kBAAA,UAAA,IAAA;AAKA,EAAAA,kBAAA,aAAA,IAAA;AACJ,GAlBYA,sBAAAA,oBAAgB,CAAA,EAAA;;;AC1C5B,IAAY;CAAZ,SAAYC,uBAAoB;AAC5B,EAAAA,sBAAA,SAAA,IAAA;AACA,EAAAA,sBAAA,SAAA,IAAA;AACA,EAAAA,sBAAA,QAAA,IAAA;AACJ,GAJY,yBAAA,uBAAoB,CAAA,EAAA;;;ACVhC,IAAAC,gBAAwB;;;ACSxB,IAAAC,gBAA0C;AA2B1C,IAAM,cAAoC,CAAA;AAE1C,IAAMC,mBAA+C;EACjD,aAAa;EACb,YAAY;EACZ,WAAW;EACX,eAAe;EACf,SAAM;AACF,4BAAwB,QAAQ,QAAQ;EAC5C;EACA,UAAO;AACH,WAAO,QAAQ,OAAO,wBAAwB,QAAQ,SAAS,CAAC;EACpE;EACA,aAAU;AACN,WAAO,QAAQ,OAAO,wBAAwB,QAAQ,YAAY,CAAC;EACvE;EACA,kBAAe;AACX,WAAO,QAAQ,OAAO,wBAAwB,QAAQ,iBAAiB,CAAC;EAC5E;EACA,kBAAe;AACX,WAAO,QAAQ,OAAO,wBAAwB,QAAQ,iBAAiB,CAAC;EAC5E;EACA,sBAAmB;AACf,WAAO,QAAQ,OAAO,wBAAwB,QAAQ,qBAAqB,CAAC;EAChF;EACA,cAAW;AACP,WAAO,QAAQ,OAAO,wBAAwB,QAAQ,aAAa,CAAC;EACxE;EACA,SAAM;AACF,WAAO,QAAQ,OAAO,wBAAwB,QAAQ,QAAQ,CAAC;EACnE;;AAEJ,OAAO,eAAeA,kBAAiB,WAAW;EAC9C,MAAG;AACC,4BAAwB,QAAQ,SAAS;AACzC,WAAO;EACX;CACH;AACD,OAAO,eAAeA,kBAAiB,UAAU;EAC7C,MAAG;AACC,4BAAwB,QAAQ,QAAQ;AACxC,WAAO;EACX;CACH;AACD,OAAO,eAAeA,kBAAiB,aAAa;EAChD,MAAG;AACC,4BAAwB,QAAQ,WAAW;AAC3C,WAAO;EACX;CACH;AAED,SAAS,wBAAwB,QAAgB,UAAgB;AAC7D,QAAM,QAAQ,IAAI,MACd,qBAAqB,MAAM,KAAK,QAAQ,2IAC2D;AAEvG,UAAQ,MAAM,KAAK;AACnB,SAAO;AACX;AAEO,IAAM,oBAAgB,6BAAkCA,gBAAqC;AAE9F,SAAUC,aAAS;AACrB,aAAO,0BAAW,aAAa;AACnC;;;ACrGA,IAAAC,gBAAgF;;;ACAhF,IAAAC,gCAMO;AAGP,IAAAC,gBAA+E;;;ACT/E,mCAAoD;AAGpD,IAAY;CAAZ,SAAYC,cAAW;AACnB,EAAAA,aAAAA,aAAA,aAAA,IAAA,CAAA,IAAA;AACA,EAAAA,aAAAA,aAAA,YAAA,IAAA,CAAA,IAAA;AACJ,GAHY,gBAAA,cAAW,CAAA,EAAA;;;ACUvB,IAAAC,gBAAyF;;;ACZzF,IAAAC,gBAA4B;AAUtB,SAAU,yBAAsB;AAClC,QAAM,EAAE,SAAS,WAAW,YAAY,OAAM,IAAKC,WAAS;AAC5D,MAAI;AACJ,MAAI,YAAY;AACZ,kBAAc;EAClB,WAAW,WAAW;AAClB,kBAAc;EAClB,WAAW,QAAQ;AACf,kBAAc;EAClB,OAAO;AACH,kBAAc;EAClB;AACA,QAAM,+BAA2B,2BAAY,MAAK;AAC9C,YAAO,EAAG,MAAM,MAAK;IAErB,CAAC;EACL,GAAG,CAAC,OAAO,CAAC;AACZ,SAAO;IACH,gBAAgB,gBAAgB;IAChC;IACA,eAAe,gBAAgB,eAAe,2BAA2B;IACzE,YAAY,iCAAQ,QAAQ;IAC5B,YAAY,iCAAQ,QAAQ;;AAEpC;;;AClCA,IAAAC,iBAA4B;AAUtB,SAAU,4BAAyB;AACrC,QAAM,EAAE,eAAe,YAAY,OAAM,IAAKC,WAAS;AACvD,MAAI;AACJ,MAAI,eAAe;AACf,kBAAc;EAClB,WAAW,QAAQ;AACf,kBAAc;EAClB,OAAO;AACH,kBAAc;EAClB;AACA,QAAM,kCAA8B,4BAAY,MAAK;AACjD,eAAU,EAAG,MAAM,MAAK;IAExB,CAAC;EACL,GAAG,CAAC,UAAU,CAAC;AACf,SAAO;IACH,gBAAgB,gBAAgB;IAChC;IACA,eAAe,gBAAgB,eAAe,8BAA8B;IAC5E,YAAY,iCAAQ,QAAQ;IAC5B,YAAY,iCAAQ,QAAQ;;AAEpC;;;AC/BA,IAAAC,iBAA4B;AAmBtB,SAAU,qBAAqB,EAAE,eAAc,GAAU;AAC3D,QAAM,EAAE,SAAS,WAAW,YAAY,YAAY,eAAe,WAAW,QAAQ,QAAQ,QAAO,IACjGC,WAAS;AACb,MAAI;AACJ,MAAI,YAAY;AACZ,kBAAc;EAClB,WAAW,WAAW;AAClB,kBAAc;EAClB,WAAW,eAAe;AACtB,kBAAc;EAClB,WAAW,QAAQ;AACf,kBAAc;EAClB,OAAO;AACH,kBAAc;EAClB;AACA,QAAM,oBAAgB,4BAAY,MAAK;AACnC,YAAO,EAAG,MAAM,MAAK;IAErB,CAAC;EACL,GAAG,CAAC,OAAO,CAAC;AACZ,QAAM,uBAAmB,4BAAY,MAAK;AACtC,eAAU,EAAG,MAAM,MAAK;IAExB,CAAC;EACL,GAAG,CAAC,UAAU,CAAC;AACf,QAAM,yBAAqB,4BAAY,MAAK;AACxC,mBAAe,EAAE,gBAAgB,QAAQ,QAAO,CAAE;EACtD,GAAG,CAAC,gBAAgB,QAAQ,OAAO,CAAC;AACpC,SAAO;IACH;IACA,WAAW,gBAAgB,eAAe,gBAAgB;IAC1D,cAAc,gBAAgB,mBAAmB,gBAAgB,cAAc,mBAAmB;IAClG,gBAAgB;IAChB,WAAW,aAAa;IACxB,YAAY,iCAAQ,QAAQ;IAC5B,YAAY,iCAAQ,QAAQ;;AAEpC;;;ACzDA,IAAAC,iBAAkB;;;ACAlB,IAAAC,iBAAkB;;;ACAlB,IAAAC,iBAAkB;AAYX,IAAM,SAA0B,CAAC,UAAS;AAC7C,SACI,eAAAC,QAAA;IAAA;IAAA,EACI,WAAW,yBAAyB,MAAM,aAAa,EAAE,IACzD,UAAU,MAAM,UAChB,OAAO,MAAM,OACb,SAAS,MAAM,SACf,UAAU,MAAM,YAAY,GAC5B,MAAK,SAAQ;IAEZ,MAAM,aAAa,eAAAA,QAAA,cAAA,KAAA,EAAG,WAAU,mCAAkC,GAAE,MAAM,SAAS;IACnF,MAAM;IACN,MAAM,WAAW,eAAAA,QAAA,cAAA,KAAA,EAAG,WAAU,iCAAgC,GAAE,MAAM,OAAO;EAAK;AAG/F;;;AC1BA,IAAAC,iBAAkB;AAMX,IAAM,aAAkC,CAAC,EAAE,QAAQ,GAAG,MAAK,MAAM;AACpE,SAAO,UAAU,eAAAC,QAAA,cAAA,OAAA,EAAK,KAAK,OAAO,QAAQ,MAAM,KAAK,GAAG,OAAO,QAAQ,IAAI,SAAO,GAAM,MAAK,CAAA;AACjG;;;AFAM,SAAU,2BAA2B,EAAE,YAAY,YAAY,GAAG,MAAK,GAAS;AAClF,SACI,eAAAC,QAAA,cAAC,QAAM,EAAA,GACC,OACJ,WAAU,iCACV,WACI,cAAc,aACV,eAAAA,QAAA,cAAC,YAAU,EAAC,QAAQ,EAAE,SAAS,EAAE,MAAM,YAAY,MAAM,WAAU,EAAE,EAAE,CAAA,IACvE,OAAS,CAAA;AAI7B;;;ADbM,SAAU,wBAAwB,EAAE,UAAU,UAAU,QAAQ,SAAS,GAAG,MAAK,GAAS;AAC5F,QAAM,EAAE,gBAAgB,aAAa,eAAe,YAAY,WAAU,IAAK,uBAAsB;AACrG,SACI,eAAAC,QAAA,cAAC,4BAA0B,EAAA,GACnB,OACJ,UAAU,YAAY,gBACtB,SAAS,CAAC,MAAK;AACX,QAAI,SAAS;AACT,cAAQ,CAAC;;AAEb,QAAI,EAAE,kBAAkB;AACpB;;AAEJ,QAAI,eAAe;AACf,oBAAa;;EAErB,GACA,YACA,WAAsB,GAErB,WAAW,WAAW,OAAO,WAAW,CAAC;AAGtD;;;AI/BA,IAAAC,iBAAkB;AAQZ,SAAU,2BAA2B,EAAE,UAAU,UAAU,QAAQ,SAAS,GAAG,MAAK,GAAS;AAC/F,QAAM,EAAE,gBAAgB,aAAa,eAAe,YAAY,WAAU,IAAK,0BAAyB;AACxG,SACI,eAAAC,QAAA,cAAC,4BAA0B,EAAA,GACnB,OACJ,UAAU,YAAY,gBACtB,SAAS,CAAC,MAAK;AACX,QAAI,SAAS;AACT,cAAQ,CAAC;;AAEb,QAAI,EAAE,kBAAkB;AACpB;;AAEJ,QAAI,eAAe;AACf,oBAAa;;EAErB,GACA,YACA,WAAsB,GAErB,WAAW,WAAW,OAAO,WAAW,CAAC;AAGtD;;;AC/BA,IAAAC,iBAA4D;AAiBtD,SAAU,sBAAsB,EAAE,UAAU,QAAQ,GAAG,MAAK,GAAS;AACvE,QAAM,EAAE,YAAY,gBAAe,IAAK,eAAc;AACtD,QAAM,EAAE,aAAa,WAAW,cAAc,WAAW,YAAY,WAAU,IAAK,qBAAqB;IACrG,iBAAc;AACV,sBAAgB,IAAI;IACxB;GACH;AACD,QAAM,CAAC,QAAQ,SAAS,QAAI,yBAAS,KAAK;AAC1C,QAAM,CAAC,UAAU,WAAW,QAAI,yBAAS,KAAK;AAC9C,QAAM,UAAM,uBAAyB,IAAI;AACzC,gCAAU,MAAK;AACX,UAAM,WAAW,CAAC,UAAkC;AAChD,YAAM,OAAO,IAAI;AAGjB,UAAI,CAAC,QAAQ,KAAK,SAAS,MAAM,MAAc;AAAG;AAElD,kBAAY,KAAK;IACrB;AAEA,aAAS,iBAAiB,aAAa,QAAQ;AAC/C,aAAS,iBAAiB,cAAc,QAAQ;AAEhD,WAAO,MAAK;AACR,eAAS,oBAAoB,aAAa,QAAQ;AAClD,eAAS,oBAAoB,cAAc,QAAQ;IACvD;EACJ,GAAG,CAAA,CAAE;AACL,QAAM,cAAU,wBAAQ,MAAK;AACzB,QAAI,UAAU;AACV,aAAO;eACA,WAAW;AAClB,YAAM,SAAS,UAAU,SAAQ;AACjC,aAAO,OAAO,MAAM,GAAG,CAAC,IAAI,OAAO,OAAO,MAAM,EAAE;eAC3C,gBAAgB,gBAAgB,gBAAgB,cAAc;AACrE,aAAO,OAAO,WAAW;WACtB;AACH,aAAO,OAAO,WAAW;;EAEjC,GAAG,CAAC,aAAa,UAAU,QAAQ,SAAS,CAAC;AAC7C,SACI,eAAAC,QAAA;IAAA;IAAA,EAAK,WAAU,0BAAyB;IACpC,eAAAA,QAAA,cAAC,4BAA0B,EAAA,GACnB,OAAK,iBACM,UACf,OAAO,EAAE,eAAe,WAAW,SAAS,QAAQ,GAAG,MAAM,MAAK,GAClE,SAAS,MAAK;AACV,cAAQ,aAAa;QACjB,KAAK;AACD,0BAAgB,IAAI;AACpB;QACJ,KAAK;AACD,cAAI,WAAW;AACX,sBAAS;;AAEb;QACJ,KAAK;AACD,sBAAY,IAAI;AAChB;;IAEZ,GACA,YACA,WAAsB,GAErB,OAAO;IAEZ,eAAAA,QAAA;MAAA;MAAA,EAAA,cACe,iBACX,WAAW,gCAAgC,YAAY,qCAAqC,IAC5F,KACA,MAAK,OAAM;MAEV,YACG,eAAAA,QAAA,cAAA,MAAA,EACI,WAAU,qCACV,SAAS,YAAW;AAChB,cAAM,UAAU,UAAU,UAAU,UAAU,SAAQ,CAAE;AACxD,kBAAU,IAAI;AACd,mBAAW,MAAM,UAAU,KAAK,GAAG,GAAG;MAC1C,GACA,MAAK,WAAU,GAEd,SAAS,OAAO,QAAQ,IAAI,OAAO,cAAc,CAAC,IAEvD;MACJ,eAAAA,QAAA,cAAA,MAAA,EACI,WAAU,qCACV,SAAS,MAAK;AACV,wBAAgB,IAAI;AACpB,oBAAY,KAAK;MACrB,GACA,MAAK,WAAU,GAEd,OAAO,eAAe,CAAC;MAE3B,eACG,eAAAA,QAAA,cAAA,MAAA,EACI,WAAU,qCACV,SAAS,MAAK;AACV,qBAAY;AACZ,oBAAY,KAAK;MACrB,GACA,MAAK,WAAU,GAEd,OAAO,YAAY,CAAC,IAEzB;IAAI;EACP;AAGjB;;;AChIA,IAAAC,iBAAkB;AAIlB,IAAM,SAAS;EACX,YAAY;EACZ,WAAW;EACX,cAAc;EACd,aAAa;;AAGX,SAAU,oBAAoB,OAAkB;AAClD,SAAO,eAAAC,QAAA,cAAC,yBAAuB,EAAA,GAAK,OAAO,QAAQ,OAAM,CAAA;AAC7D;;;ACRA,IAAAC,iBAA+E;AAC/E,uBAA6B;;;ACL7B,IAAAC,iBAA+C;AAOxC,IAAM,WAA8B,CAAC,EAAE,IAAI,UAAU,WAAW,MAAK,MAAM;AAC9E,QAAM,UAAM,uBAAuB,IAAI;AACvC,QAAM,cAAU,uBAAO,IAAI;AAC3B,QAAM,aAAa;AAEnB,QAAM,eAAe,MAAK;AACtB,UAAM,OAAO,IAAI;AACjB,QAAI,CAAC;AAAM;AAEX,0BAAsB,MAAK;AACvB,WAAK,MAAM,SAAS,KAAK,eAAe;IAC5C,CAAC;EACL;AAEA,QAAM,gBAAgB,MAAK;AACvB,UAAM,OAAO,IAAI;AACjB,QAAI,CAAC;AAAM;AAEX,0BAAsB,MAAK;AACvB,WAAK,MAAM,SAAS,KAAK,eAAe;AACxC,WAAK,MAAM,WAAW;AACtB,4BAAsB,MAAK;AACvB,aAAK,MAAM,SAAS;MACxB,CAAC;IACL,CAAC;EACL;AAEA,sCAAgB,MAAK;AACjB,QAAI,UAAU;AACV,mBAAY;WACT;AACH,oBAAa;;EAErB,GAAG,CAAC,QAAQ,CAAC;AAEb,sCAAgB,MAAK;AACjB,UAAM,OAAO,IAAI;AACjB,QAAI,CAAC;AAAM;AAEX,aAAS,iBAAc;AACnB,UAAI,CAAC;AAAM;AAEX,WAAK,MAAM,WAAW,WAAW,YAAY;AAC7C,UAAI,UAAU;AACV,aAAK,MAAM,SAAS;;IAE5B;AAEA,aAAS,oBAAoB,OAAsB;AAC/C,UAAI,QAAQ,MAAM,WAAW,QAAQ,MAAM,iBAAiB,UAAU;AAClE,uBAAc;;IAEtB;AAEA,QAAI,QAAQ,SAAS;AACjB,qBAAc;AACd,cAAQ,UAAU;;AAGtB,SAAK,iBAAiB,iBAAiB,mBAAmB;AAC1D,WAAO,MAAM,KAAK,oBAAoB,iBAAiB,mBAAmB;EAC9E,GAAG,CAAC,QAAQ,CAAC;AAEb,SACI,eAAAC,QAAA,cAAA,OAAA,EACI,WAAU,2BACV,IACA,KACA,MAAK,UACL,OAAO,EAAE,QAAQ,GAAG,YAAY,QAAQ,UAAU,SAAY,WAAU,EAAE,GAEzE,QAAQ;AAGrB;;;AC/EA,IAAAC,iBAAkB;AAUX,IAAM,iBAA0C,CAAC,EAAE,aAAa,UAAU,OAAM,MAAM;AACzF,SACI,eAAAC,QAAA;IAAA;IAAA;IACI,eAAAA,QAAA;MAAC;MAAM,EAAC,SAAS,aAAa,WAAW,eAAAA,QAAA,cAAC,YAAU,EAAC,OAAc,CAAA,GAAM,SAAkB;MACtF,OAAO,QAAQ;MACf,OAAO,eAAe,iBAAiB,aAAa,eAAAA,QAAA,cAAA,QAAA,MAAA,UAAA;IAAqB;EACrE;AAGrB;;;ACrBA,IAAAC,iBAAkB;AAEX,IAAM,YAAgB,MAAK;AAC9B,SACI,eAAAC,QAAA;IAAA;IAAA,EAAK,OAAM,MAAK,QAAO,MAAK,SAAQ,aAAY,MAAK,QAAO,OAAM,6BAA4B;IAC1F,eAAAA,QAAA,cAAA,UAAA,EAAQ,IAAG,QAAO,IAAG,MAAK,GAAE,MAAK,MAAK,gCAA+B,aAAY,MAAK,CAAA;IACtF,eAAAA,QAAA,cAAA,UAAA,EACI,IAAG,QACH,IAAG,MACH,GAAE,MACF,QAAO,gCACP,eAAc,OACd,aAAY,IAAG,CAAA;IAEnB,eAAAA,QAAA;MAAA;MAAA,EAAG,UAAS,uBAAsB;MAC9B,eAAAA,QAAA,cAAA,QAAA,EACI,GAAE,+sBACF,MAAK,+BAA8B,CAAA;MAEvC,eAAAA,QAAA,cAAA,QAAA,EACI,GAAE,mOACF,MAAK,+BAA8B,CAAA;IACrC;IAEN,eAAAA,QAAA;MAAA;MAAA;MACI,eAAAA,QAAA;QAAA;QAAA,EACI,IAAG,0BACH,IAAG,WACH,IAAG,WACH,IAAG,UACH,IAAG,WACH,eAAc,iBAAgB;QAE9B,eAAAA,QAAA,cAAA,QAAA,EAAM,WAAU,UAAS,CAAA;QACzB,eAAAA,QAAA,cAAA,QAAA,EAAM,QAAO,QAAO,WAAU,UAAS,CAAA;QACvC,eAAAA,QAAA,cAAA,QAAA,EAAM,QAAO,QAAO,WAAU,UAAS,CAAA;QACvC,eAAAA,QAAA,cAAA,QAAA,EAAM,QAAO,QAAO,WAAU,UAAS,CAAA;QACvC,eAAAA,QAAA,cAAA,QAAA,EAAM,QAAO,QAAO,WAAU,UAAS,CAAA;QACvC,eAAAA,QAAA,cAAA,QAAA,EAAM,QAAO,KAAI,WAAU,UAAS,CAAA;MAAG;MAE3C,eAAAA,QAAA;QAAA;QAAA,EACI,IAAG,0BACH,IAAG,WACH,IAAG,WACH,IAAG,UACH,IAAG,WACH,eAAc,iBAAgB;QAE9B,eAAAA,QAAA,cAAA,QAAA,EAAM,WAAU,UAAS,CAAA;QACzB,eAAAA,QAAA,cAAA,QAAA,EAAM,QAAO,QAAO,WAAU,UAAS,CAAA;QACvC,eAAAA,QAAA,cAAA,QAAA,EAAM,QAAO,QAAO,WAAU,UAAS,CAAA;QACvC,eAAAA,QAAA,cAAA,QAAA,EAAM,QAAO,QAAO,WAAU,UAAS,CAAA;QACvC,eAAAA,QAAA,cAAA,QAAA,EAAM,QAAO,QAAO,WAAU,UAAS,CAAA;QACvC,eAAAA,QAAA,cAAA,QAAA,EAAM,QAAO,KAAI,WAAU,UAAS,CAAA;MAAG;MAE3C,eAAAA,QAAA;QAAA;QAAA,EACI,IAAG,0BACH,IAAG,WACH,IAAG,WACH,IAAG,WACH,IAAG,WACH,eAAc,iBAAgB;QAE9B,eAAAA,QAAA,cAAA,QAAA,EAAM,WAAU,UAAS,CAAA;QACzB,eAAAA,QAAA,cAAA,QAAA,EAAM,QAAO,QAAO,WAAU,UAAS,CAAA;QACvC,eAAAA,QAAA,cAAA,QAAA,EAAM,QAAO,QAAO,WAAU,UAAS,CAAA;QACvC,eAAAA,QAAA,cAAA,QAAA,EAAM,QAAO,QAAO,WAAU,UAAS,CAAA;QACvC,eAAAA,QAAA,cAAA,QAAA,EAAM,QAAO,QAAO,WAAU,UAAS,CAAA;QACvC,eAAAA,QAAA,cAAA,QAAA,EAAM,QAAO,KAAI,WAAU,UAAS,CAAA;MAAG;MAE3C,eAAAA,QAAA;QAAA;QAAA,EACI,IAAG,0BACH,IAAG,WACH,IAAG,WACH,IAAG,WACH,IAAG,WACH,eAAc,iBAAgB;QAE9B,eAAAA,QAAA,cAAA,QAAA,EAAM,WAAU,UAAS,CAAA;QACzB,eAAAA,QAAA,cAAA,QAAA,EAAM,QAAO,QAAO,WAAU,UAAS,CAAA;QACvC,eAAAA,QAAA,cAAA,QAAA,EAAM,QAAO,QAAO,WAAU,UAAS,CAAA;QACvC,eAAAA,QAAA,cAAA,QAAA,EAAM,QAAO,QAAO,WAAU,UAAS,CAAA;QACvC,eAAAA,QAAA,cAAA,QAAA,EAAM,QAAO,QAAO,WAAU,UAAS,CAAA;QACvC,eAAAA,QAAA,cAAA,QAAA,EAAM,QAAO,KAAI,WAAU,UAAS,CAAA;MAAG;MAE3C,eAAAA,QAAA;QAAA;QAAA,EAAU,IAAG,iBAAgB;QACzB,eAAAA,QAAA,cAAA,QAAA,EAAM,OAAM,MAAK,QAAO,MAAK,MAAK,SAAQ,WAAU,qBAAoB,CAAA;MAAG;IACpE;EACR;AAGnB;;;AH3EO,IAAM,cAAoC,CAAC,EAAE,YAAY,IAAI,YAAY,OAAM,MAAM;AACxF,QAAM,UAAM,uBAAuB,IAAI;AACvC,QAAM,EAAE,SAAS,OAAM,IAAK,UAAS;AACrC,QAAM,EAAE,WAAU,IAAK,eAAc;AACrC,QAAM,CAAC,UAAU,WAAW,QAAI,yBAAS,KAAK;AAC9C,QAAM,CAAC,QAAQ,SAAS,QAAI,yBAAS,KAAK;AAC1C,QAAM,CAAC,QAAQ,SAAS,QAAI,yBAAyB,IAAI;AAEzD,QAAM,CAAC,eAAe,gBAAgB,QAAI,wBAAQ,MAAK;AACnD,UAAM,YAAsB,CAAA;AAC5B,UAAM,WAAqB,CAAA;AAC3B,UAAM,cAAwB,CAAA;AAE9B,eAAW,UAAU,SAAS;AAC1B,UAAI,OAAO,eAAe,iBAAiB,aAAa;AACpD,oBAAY,KAAK,MAAM;iBAChB,OAAO,eAAe,iBAAiB,UAAU;AACxD,iBAAS,KAAK,MAAM;iBACb,OAAO,eAAe,iBAAiB,WAAW;AACzD,kBAAU,KAAK,MAAM;;;AAI7B,QAAI,SAAmB,CAAA;AACvB,QAAI,YAAsB,CAAA;AAE1B,QAAI,UAAU,QAAQ;AAClB,eAAS;AACT,kBAAY,CAAC,GAAG,UAAU,GAAG,WAAW;eACjC,SAAS,QAAQ;AACxB,eAAS;AACT,kBAAY;WACT;AACH,kBAAY;;AAGhB,WAAO,CAAC,QAAQ,SAAS;EAC7B,GAAG,CAAC,OAAO,CAAC;AAEZ,QAAM,gBAAY,4BAAY,MAAK;AAC/B,cAAU,KAAK;AACf,eAAW,MAAM,WAAW,KAAK,GAAG,GAAG;EAC3C,GAAG,CAAC,UAAU,CAAC;AAEf,QAAM,kBAAc,4BAChB,CAAC,UAAqB;AAClB,UAAM,eAAc;AACpB,cAAS;EACb,GACA,CAAC,SAAS,CAAC;AAGf,QAAM,wBAAoB,4BACtB,CAAC,OAAmB,eAA0B;AAC1C,WAAO,UAAU;AACjB,gBAAY,KAAK;EACrB,GACA,CAAC,QAAQ,WAAW,CAAC;AAGzB,QAAM,0BAAsB,4BAAY,MAAM,YAAY,CAAC,QAAQ,GAAG,CAAC,QAAQ,CAAC;AAEhF,QAAM,mBAAe,4BACjB,CAAC,UAAwB;AACrB,UAAM,OAAO,IAAI;AACjB,QAAI,CAAC;AAAM;AAGX,UAAM,oBAAoB,KAAK,iBAAiB,QAAQ;AAExD,UAAM,eAAe,kBAAkB,CAAC;AAExC,UAAM,cAAc,kBAAkB,kBAAkB,SAAS,CAAC;AAElE,QAAI,MAAM,UAAU;AAEhB,UAAI,SAAS,kBAAkB,cAAc;AACzC,oBAAY,MAAK;AACjB,cAAM,eAAc;;WAErB;AAEH,UAAI,SAAS,kBAAkB,aAAa;AACxC,qBAAa,MAAK;AAClB,cAAM,eAAc;;;EAGhC,GACA,CAAC,GAAG,CAAC;AAGT,sCAAgB,MAAK;AACjB,UAAM,gBAAgB,CAAC,UAAwB;AAC3C,UAAI,MAAM,QAAQ,UAAU;AACxB,kBAAS;iBACF,MAAM,QAAQ,OAAO;AAC5B,qBAAa,KAAK;;IAE1B;AAGA,UAAM,EAAE,SAAQ,IAAK,OAAO,iBAAiB,SAAS,IAAI;AAE1D,eAAW,MAAM,UAAU,IAAI,GAAG,CAAC;AAEnC,aAAS,KAAK,MAAM,WAAW;AAE/B,WAAO,iBAAiB,WAAW,eAAe,KAAK;AAEvD,WAAO,MAAK;AAER,eAAS,KAAK,MAAM,WAAW;AAC/B,aAAO,oBAAoB,WAAW,eAAe,KAAK;IAC9D;EACJ,GAAG,CAAC,WAAW,YAAY,CAAC;AAE5B,sCAAgB,MAAM,UAAU,SAAS,cAAc,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC;AAE/E,SACI,cACA,+BACI,eAAAC,QAAA;IAAA;IAAA,EAAA,mBACoB,8BAA4B,cACjC,QACX,WAAW,wBAAwB,UAAU,8BAA8B,IAAI,SAAS,IACxF,KACA,MAAK,SAAQ;IAEb,eAAAA,QAAA;MAAA;MAAA,EAAK,WAAU,iCAAgC;MAC3C,eAAAA,QAAA;QAAA;QAAA,EAAK,WAAU,+BAA8B;QACzC,eAAAA,QAAA;UAAA;UAAA,EAAQ,SAAS,aAAa,WAAU,oCAAmC;UACvE,eAAAA,QAAA;YAAA;YAAA,EAAK,OAAM,MAAK,QAAO,KAAI;YACvB,eAAAA,QAAA,cAAA,QAAA,EAAM,GAAE,sIAAqI,CAAA;UAAG;QAC9I;QAET,cAAc,SACX,eAAAA,QAAA;UAAA,eAAAA,QAAA;UAAA;UACI,eAAAA,QAAA,cAAA,MAAA,EAAI,WAAU,6BAA4B,GAAA,wCAAA;UAC1C,eAAAA,QAAA;YAAA;YAAA,EAAI,WAAU,4BAA2B;YACpC,cAAc,IAAI,CAAC,WAChB,eAAAA,QAAA,cAAC,gBAAc,EACX,KAAK,OAAO,QAAQ,MACpB,aAAa,CAAC,UAAU,kBAAkB,OAAO,OAAO,QAAQ,IAAI,GACpE,OAAc,CAAA,CAErB;YACA,iBAAiB,SACd,eAAAA,QAAA,cAAC,UAAQ,EAAC,UAAoB,IAAG,gCAA+B,GAC3D,iBAAiB,IAAI,CAAC,WACnB,eAAAA,QAAA,cAAC,gBAAc,EACX,KAAK,OAAO,QAAQ,MACpB,aAAa,CAAC,UACV,kBAAkB,OAAO,OAAO,QAAQ,IAAI,GAEhD,UAAU,WAAW,IAAI,IACzB,OAAc,CAAA,CAErB,CAAC,IAEN;UAAI;UAEX,iBAAiB,SACd,eAAAA,QAAA;YAAA;YAAA,EACI,WAAU,kCACV,SAAS,qBACT,UAAU,EAAC;YAEX,eAAAA,QAAA;cAAA;cAAA;cAAO,WAAW,UAAU;;;YAC5B,eAAAA,QAAA;cAAA;cAAA,EACI,OAAM,MACN,QAAO,KACP,SAAQ,YACR,OAAM,8BACN,WAAW,GACP,WAAW,+CAA+C,EAC9D,GAAE;cAEF,eAAAA,QAAA,cAAA,QAAA,EAAM,GAAE,4TAA2T,CAAA;YAAG;UACpU,IAEV;QAAI,IAGZ,eAAAA,QAAA;UAAA,eAAAA,QAAA;UAAA;UACI,eAAAA,QAAA,cAAA,MAAA,EAAI,WAAU,6BAA4B,GAAA,4CAAA;UAG1C,eAAAA,QAAA;YAAA;YAAA,EAAK,WAAU,8BAA6B;YACxC,eAAAA,QAAA,cAAC,WAAS,IAAA;UAAG;UAEhB,iBAAiB,SACd,eAAAA,QAAA;YAAA,eAAAA,QAAA;YAAA;YACI,eAAAA,QAAA;cAAA;cAAA,EACI,WAAU,kCACV,SAAS,qBACT,UAAU,EAAC;cAEX,eAAAA,QAAA;gBAAA;gBAAA;gBAAO,WAAW,UAAU;;;cAC5B,eAAAA,QAAA;gBAAA;gBAAA,EACI,OAAM,MACN,QAAO,KACP,SAAQ,YACR,OAAM,8BACN,WAAW,GACP,WAAW,+CAA+C,EAC9D,GAAE;gBAEF,eAAAA,QAAA,cAAA,QAAA,EAAM,GAAE,4TAA2T,CAAA;cAAG;YACpU;YAEV,eAAAA,QAAA;cAAC;cAAQ,EAAC,UAAoB,IAAG,gCAA+B;cAC5D,eAAAA,QAAA,cAAA,MAAA,EAAI,WAAU,4BAA2B,GACpC,iBAAiB,IAAI,CAAC,WACnB,eAAAA,QAAA,cAAC,gBAAc,EACX,KAAK,OAAO,QAAQ,MACpB,aAAa,CAAC,UACV,kBAAkB,OAAO,OAAO,QAAQ,IAAI,GAEhD,UAAU,WAAW,IAAI,IACzB,OAAc,CAAA,CAErB,CAAC;YACD;UACE,IAEf;QAAI;MAEf;IACC;IAEV,eAAAA,QAAA,cAAA,OAAA,EAAK,WAAU,gCAA+B,aAAa,YAAW,CAAA;EAAI,GAE9E,MAAM;AAGlB;;;AI3PA,IAAAC,iBAAmC;AAK5B,IAAM,oBAAqC,CAAC,EAAE,WAAW,iBAAiB,SAAS,GAAG,MAAK,MAAM;AACpG,QAAM,EAAE,SAAS,WAAU,IAAK,eAAc;AAE9C,QAAM,kBAAc,4BAChB,CAAC,UAAwC;AACrC,QAAI;AAAS,cAAQ,KAAK;AAC1B,QAAI,CAAC,MAAM;AAAkB,iBAAW,CAAC,OAAO;EACpD,GACA,CAAC,SAAS,YAAY,OAAO,CAAC;AAGlC,SACI,eAAAC,QAAA,cAAC,QAA0B,EAAA,GAAK,OAAO,WAAU,iCAAgC,SAAS,YAAW,GAChG,QAAQ;AAGrB;;;ACrBA,IAAAC,iBAAgC;AASzB,IAAM,sBAAoD,CAAC,EAAE,UAAU,GAAG,MAAK,MAAM;AACxF,QAAM,CAAC,SAAS,UAAU,QAAI,yBAAS,KAAK;AAE5C,SACI,eAAAC,QAAA;IAAC,mBAAmB;IAAQ,EACxB,OAAO;MACH;MACA;MACH;IAEA;IACA,WAAW,eAAAA,QAAA,cAAC,aAAW,EAAA,GAAK,MAAK,CAAA;EAAI;AAGlD;;;ACxBA,IAAAC,iBAAkB;AAIlB,IAAMC,UAAS;EACX,eAAe;EACf,cAAc;EACd,aAAa;;AAGX,SAAU,uBAAuB,OAAkB;AACrD,SAAO,eAAAC,QAAA,cAAC,4BAA0B,EAAA,GAAK,OAAO,QAAQD,QAAM,CAAA;AAChE;;;ACZA,IAAAE,iBAAkB;AAIlB,IAAMC,UAAS;EACX,iBAAiB;EACjB,YAAY;EACZ,gBAAgB;EAChB,QAAQ;EACR,YAAY;EACZ,cAAc;EACd,aAAa;;AAGX,SAAU,kBAAkB,OAAkB;AAChD,SAAO,eAAAC,QAAA,cAAC,uBAAqB,EAAA,GAAK,OAAO,QAAQD,QAAM,CAAA;AAC3D;", "names": ["EventEmitter", "import_react", "import_react", "WalletReadyState", "WalletAdapterNetwork", "import_react", "import_react", "DEFAULT_CONTEXT", "useWallet", "import_react", "import_wallet_adapter_mobile", "import_react", "Environment", "import_react", "import_react", "useWallet", "import_react", "useWallet", "import_react", "useWallet", "import_react", "import_react", "import_react", "React", "import_react", "React", "React", "React", "import_react", "React", "import_react", "React", "import_react", "React", "import_react", "import_react", "React", "import_react", "React", "import_react", "React", "React", "import_react", "React", "import_react", "React", "import_react", "LABELS", "React", "import_react", "LABELS", "React"]}