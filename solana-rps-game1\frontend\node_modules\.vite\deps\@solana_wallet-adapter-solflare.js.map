{"version": 3, "sources": ["../../@solana/wallet-adapter-solflare/src/adapter.ts", "../../@solana/wallet-adapter-solflare/src/metamask/icon.ts", "../../@solana/wallet-adapter-solflare/src/metamask/wallet.ts", "../../@solana/wallet-adapter-solflare/src/metamask/detect.ts"], "sourcesContent": ["import type { WalletAdapterNetwork, WalletName } from '@solana/wallet-adapter-base';\nimport {\n    BaseMessageSignerWalletAdapter,\n    WalletConfigError,\n    WalletConnectionError,\n    WalletDisconnectedError,\n    WalletDisconnectionError,\n    WalletError,\n    WalletLoadError,\n    WalletNotConnectedError,\n    WalletNotReadyError,\n    WalletPublicKeyError,\n    WalletReadyState,\n    WalletSendTransactionError,\n    WalletSignMessageError,\n    WalletSignTransactionError,\n    isIosAndRedirectable,\n    isVersionedTransaction,\n    scopePollingDetectionStrategy,\n    type SendTransactionOptions,\n} from '@solana/wallet-adapter-base';\nimport type { Transaction, TransactionVersion, VersionedTransaction } from '@solana/web3.js';\nimport { PublicKey, type Connection, type TransactionSignature } from '@solana/web3.js';\nimport type { default as Solflare } from '@solflare-wallet/sdk';\nimport { detectAndRegisterSolflareMetaMaskWallet } from './metamask/detect.js';\n\ninterface SolflareWindow extends Window {\n    solflare?: {\n        isSolflare?: boolean;\n    };\n    SolflareApp?: unknown;\n}\n\ndeclare const window: SolflareWindow;\n\nexport interface SolflareWalletAdapterConfig {\n    network?: WalletAdapterNetwork;\n}\n\nexport const SolflareWalletName = 'Solflare' as WalletName<'Solflare'>;\n\nexport class SolflareWalletAdapter extends BaseMessageSignerWalletAdapter {\n    name = SolflareWalletName;\n    url = 'https://solflare.com';\n    icon =\n        'data:image/svg+xml;base64,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';\n    supportedTransactionVersions: ReadonlySet<TransactionVersion> = new Set(['legacy', 0]);\n\n    private _connecting: boolean;\n    private _wallet: Solflare | null;\n    private _publicKey: PublicKey | null;\n    private _config: SolflareWalletAdapterConfig;\n    private _readyState: WalletReadyState =\n        typeof window === 'undefined' || typeof document === 'undefined'\n            ? WalletReadyState.Unsupported\n            : WalletReadyState.Loadable;\n\n    constructor(config: SolflareWalletAdapterConfig = {}) {\n        super();\n        this._connecting = false;\n        this._publicKey = null;\n        this._wallet = null;\n        this._config = config;\n\n        if (this._readyState !== WalletReadyState.Unsupported) {\n            scopePollingDetectionStrategy(() => {\n                if (window.solflare?.isSolflare || window.SolflareApp) {\n                    this._readyState = WalletReadyState.Installed;\n                    this.emit('readyStateChange', this._readyState);\n                    return true;\n                }\n                return false;\n            });\n            detectAndRegisterSolflareMetaMaskWallet();\n        }\n    }\n\n    get publicKey() {\n        return this._publicKey;\n    }\n\n    get connecting() {\n        return this._connecting;\n    }\n\n    get connected() {\n        return !!this._wallet?.connected;\n    }\n\n    get readyState() {\n        return this._readyState;\n    }\n\n    async autoConnect(): Promise<void> {\n        // Skip autoconnect in the Loadable state on iOS\n        // We can't redirect to a universal link without user input\n        if (!(this.readyState === WalletReadyState.Loadable && isIosAndRedirectable())) {\n            await this.connect();\n        }\n    }\n\n    async connect(): Promise<void> {\n        try {\n            if (this.connected || this.connecting) return;\n            if (this._readyState !== WalletReadyState.Loadable && this._readyState !== WalletReadyState.Installed)\n                throw new WalletNotReadyError();\n\n            // redirect to the Solflare /browse universal link\n            // this will open the current URL in the Solflare in-wallet browser\n            if (this.readyState === WalletReadyState.Loadable && isIosAndRedirectable()) {\n                const url = encodeURIComponent(window.location.href);\n                const ref = encodeURIComponent(window.location.origin);\n                window.location.href = `https://solflare.com/ul/v1/browse/${url}?ref=${ref}`;\n                return;\n            }\n\n            let SolflareClass: typeof Solflare;\n            try {\n                SolflareClass = (await import('@solflare-wallet/sdk')).default;\n            } catch (error: any) {\n                throw new WalletLoadError(error?.message, error);\n            }\n\n            let wallet: Solflare;\n            try {\n                wallet = new SolflareClass({ network: this._config.network });\n            } catch (error: any) {\n                throw new WalletConfigError(error?.message, error);\n            }\n\n            this._connecting = true;\n\n            if (!wallet.connected) {\n                try {\n                    await wallet.connect();\n                } catch (error: any) {\n                    throw new WalletConnectionError(error?.message, error);\n                }\n            }\n\n            if (!wallet.publicKey) throw new WalletConnectionError();\n\n            let publicKey: PublicKey;\n            try {\n                publicKey = new PublicKey(wallet.publicKey.toBytes());\n            } catch (error: any) {\n                throw new WalletPublicKeyError(error?.message, error);\n            }\n\n            wallet.on('disconnect', this._disconnected);\n            wallet.on('accountChanged', this._accountChanged);\n\n            this._wallet = wallet;\n            this._publicKey = publicKey;\n\n            this.emit('connect', publicKey);\n        } catch (error: any) {\n            this.emit('error', error);\n            throw error;\n        } finally {\n            this._connecting = false;\n        }\n    }\n\n    async disconnect(): Promise<void> {\n        const wallet = this._wallet;\n        if (wallet) {\n            wallet.off('disconnect', this._disconnected);\n            wallet.off('accountChanged', this._accountChanged);\n\n            this._wallet = null;\n            this._publicKey = null;\n\n            try {\n                await wallet.disconnect();\n            } catch (error: any) {\n                this.emit('error', new WalletDisconnectionError(error?.message, error));\n            }\n        }\n\n        this.emit('disconnect');\n    }\n\n    async sendTransaction<T extends Transaction | VersionedTransaction>(\n        transaction: T,\n        connection: Connection,\n        options: SendTransactionOptions = {}\n    ): Promise<TransactionSignature> {\n        try {\n            const wallet = this._wallet;\n            if (!wallet) throw new WalletNotConnectedError();\n\n            try {\n                const { signers, ...sendOptions } = options;\n\n                if (isVersionedTransaction(transaction)) {\n                    signers?.length && transaction.sign(signers);\n                } else {\n                    transaction = (await this.prepareTransaction(transaction, connection, sendOptions)) as T;\n                    signers?.length && (transaction as Transaction).partialSign(...signers);\n                }\n\n                sendOptions.preflightCommitment = sendOptions.preflightCommitment || connection.commitment;\n\n                return await wallet.signAndSendTransaction(transaction, sendOptions);\n            } catch (error: any) {\n                if (error instanceof WalletError) throw error;\n                throw new WalletSendTransactionError(error?.message, error);\n            }\n        } catch (error: any) {\n            this.emit('error', error);\n            throw error;\n        }\n    }\n\n    async signTransaction<T extends Transaction | VersionedTransaction>(transaction: T): Promise<T> {\n        try {\n            const wallet = this._wallet;\n            if (!wallet) throw new WalletNotConnectedError();\n\n            try {\n                return ((await wallet.signTransaction(transaction)) as T) || transaction;\n            } catch (error: any) {\n                throw new WalletSignTransactionError(error?.message, error);\n            }\n        } catch (error: any) {\n            this.emit('error', error);\n            throw error;\n        }\n    }\n\n    async signAllTransactions<T extends Transaction | VersionedTransaction>(transactions: T[]): Promise<T[]> {\n        try {\n            const wallet = this._wallet;\n            if (!wallet) throw new WalletNotConnectedError();\n\n            try {\n                return ((await wallet.signAllTransactions(transactions)) as T[]) || transactions;\n            } catch (error: any) {\n                throw new WalletSignTransactionError(error?.message, error);\n            }\n        } catch (error: any) {\n            this.emit('error', error);\n            throw error;\n        }\n    }\n\n    async signMessage(message: Uint8Array): Promise<Uint8Array> {\n        try {\n            const wallet = this._wallet;\n            if (!wallet) throw new WalletNotConnectedError();\n\n            try {\n                return await wallet.signMessage(message, 'utf8');\n            } catch (error: any) {\n                throw new WalletSignMessageError(error?.message, error);\n            }\n        } catch (error: any) {\n            this.emit('error', error);\n            throw error;\n        }\n    }\n\n    private _disconnected = () => {\n        const wallet = this._wallet;\n        if (wallet) {\n            wallet.off('disconnect', this._disconnected);\n\n            this._wallet = null;\n            this._publicKey = null;\n\n            this.emit('error', new WalletDisconnectedError());\n            this.emit('disconnect');\n        }\n    };\n\n    private _accountChanged = (newPublicKey?: PublicKey) => {\n        if (!newPublicKey) return;\n\n        const publicKey = this._publicKey;\n        if (!publicKey) return;\n\n        try {\n            newPublicKey = new PublicKey(newPublicKey.toBytes());\n        } catch (error: any) {\n            this.emit('error', new WalletPublicKeyError(error?.message, error));\n            return;\n        }\n\n        if (publicKey.equals(newPublicKey)) return;\n\n        this._publicKey = newPublicKey;\n        this.emit('connect', newPublicKey);\n    };\n}\n", "import type { WalletIcon } from '@wallet-standard/base';\n\n/** @internal */\nexport const icon: WalletIcon =\n    'data:image/svg+xml;base64,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' as const;\n", "import { WalletNotConnectedError } from '@solana/wallet-adapter-base';\nimport { SOLANA_DEVNET_CHAIN, SOLANA_MAINNET_CHAIN, SOLANA_TESTNET_CHAIN } from '@solana/wallet-standard-chains';\nimport {\n    SolanaSignAndSendTransaction,\n    type SolanaSignAndSendTransactionFeature,\n    type SolanaSignAndSendTransactionMethod,\n    SolanaSignMessage,\n    type SolanaSignMessageFeature,\n    type SolanaSignMessageMethod,\n    SolanaSignTransaction,\n    type SolanaSignTransactionFeature,\n    type SolanaSignTransactionMethod,\n} from '@solana/wallet-standard-features';\nimport type { default as SolflareMetaMask } from '@solflare-wallet/metamask-sdk';\nimport type { Wallet } from '@wallet-standard/base';\nimport {\n    StandardConnect,\n    type StandardConnectFeature,\n    type StandardConnectMethod,\n    StandardDisconnect,\n    type StandardDisconnectFeature,\n    type StandardDisconnectMethod,\n    StandardEvents,\n    type StandardEventsChangeProperties,\n    type StandardEventsFeature,\n    type StandardEventsListeners,\n    type StandardEventsNames,\n    type StandardEventsOnMethod,\n} from '@wallet-standard/features';\nimport { icon } from './icon.js';\n\nexport class SolflareMetaMaskWallet implements Wallet {\n    readonly #listeners: { [E in StandardEventsNames]?: StandardEventsListeners[E][] } = {};\n    readonly #version = '1.0.0' as const;\n    readonly #name = 'MetaMask' as const;\n    readonly #icon = icon;\n    #solflareMetaMask: SolflareMetaMask | null = null;\n\n    get version() {\n        return this.#version;\n    }\n\n    get name() {\n        return this.#name;\n    }\n\n    get icon() {\n        return this.#icon;\n    }\n\n    get chains() {\n        return [SOLANA_MAINNET_CHAIN, SOLANA_DEVNET_CHAIN, SOLANA_TESTNET_CHAIN] as const;\n    }\n\n    get features(): StandardConnectFeature &\n        StandardDisconnectFeature &\n        StandardEventsFeature &\n        SolanaSignAndSendTransactionFeature &\n        SolanaSignTransactionFeature &\n        SolanaSignMessageFeature {\n        return {\n            [StandardConnect]: {\n                version: '1.0.0',\n                connect: this.#connect,\n            },\n            [StandardDisconnect]: {\n                version: '1.0.0',\n                disconnect: this.#disconnect,\n            },\n            [StandardEvents]: {\n                version: '1.0.0',\n                on: this.#on,\n            },\n            [SolanaSignAndSendTransaction]: {\n                version: '1.0.0',\n                supportedTransactionVersions: ['legacy', 0],\n                signAndSendTransaction: this.#signAndSendTransaction,\n            },\n            [SolanaSignTransaction]: {\n                version: '1.0.0',\n                supportedTransactionVersions: ['legacy', 0],\n                signTransaction: this.#signTransaction,\n            },\n            [SolanaSignMessage]: {\n                version: '1.0.0',\n                signMessage: this.#signMessage,\n            },\n        };\n    }\n\n    get accounts() {\n        return this.#solflareMetaMask ? this.#solflareMetaMask.standardAccounts : [];\n    }\n\n    #on: StandardEventsOnMethod = (event, listener) => {\n        this.#listeners[event]?.push(listener) || (this.#listeners[event] = [listener]);\n        return (): void => this.#off(event, listener);\n    };\n\n    #emit<E extends StandardEventsNames>(event: E, ...args: Parameters<StandardEventsListeners[E]>): void {\n        // eslint-disable-next-line prefer-spread\n        this.#listeners[event]?.forEach((listener) => listener.apply(null, args));\n    }\n\n    #off<E extends StandardEventsNames>(event: E, listener: StandardEventsListeners[E]): void {\n        this.#listeners[event] = this.#listeners[event]?.filter((existingListener) => listener !== existingListener);\n    }\n\n    #connect: StandardConnectMethod = async () => {\n        if (!this.#solflareMetaMask) {\n            let SolflareMetaMaskClass: typeof SolflareMetaMask;\n            try {\n                SolflareMetaMaskClass = (await import('@solflare-wallet/metamask-sdk')).default;\n            } catch (error: any) {\n                throw new Error('Unable to load Solflare MetaMask SDK');\n            }\n            this.#solflareMetaMask = new SolflareMetaMaskClass();\n            this.#solflareMetaMask.on('standard_change', (properties: StandardEventsChangeProperties) =>\n                this.#emit('change', properties)\n            );\n        }\n\n        if (!this.accounts.length) {\n            await this.#solflareMetaMask.connect();\n        }\n\n        return { accounts: this.accounts };\n    };\n\n    #disconnect: StandardDisconnectMethod = async () => {\n        if (!this.#solflareMetaMask) return;\n        await this.#solflareMetaMask.disconnect();\n    };\n\n    #signAndSendTransaction: SolanaSignAndSendTransactionMethod = async (...inputs) => {\n        if (!this.#solflareMetaMask) throw new WalletNotConnectedError();\n        return await this.#solflareMetaMask.standardSignAndSendTransaction(...inputs);\n    };\n\n    #signTransaction: SolanaSignTransactionMethod = async (...inputs) => {\n        if (!this.#solflareMetaMask) throw new WalletNotConnectedError();\n        return await this.#solflareMetaMask.standardSignTransaction(...inputs);\n    };\n\n    #signMessage: SolanaSignMessageMethod = async (...inputs) => {\n        if (!this.#solflareMetaMask) throw new WalletNotConnectedError();\n        return await this.#solflareMetaMask.standardSignMessage(...inputs);\n    };\n}\n", "import { registerWallet } from '@wallet-standard/wallet';\nimport { SolflareMetaMaskWallet } from './wallet.js';\n\nlet registered = false;\n\nfunction register() {\n    if (registered) return;\n    registerWallet(new SolflareMetaMaskWallet());\n    registered = true;\n}\n\n/** @internal */\nexport async function detectAndRegisterSolflareMetaMaskWallet(): Promise<void> {\n    const id = 'solflare-detect-metamask';\n\n    function postMessage() {\n        window.postMessage(\n            {\n                target: 'metamask-contentscript',\n                data: {\n                    name: 'metamask-provider',\n                    data: {\n                        id,\n                        jsonrpc: '2.0',\n                        method: 'wallet_getSnaps',\n                    },\n                },\n            },\n            window.location.origin\n        );\n    }\n\n    function onMessage(event: MessageEvent) {\n        const message = event.data;\n        if (message?.target === 'metamask-inpage' && message.data?.name === 'metamask-provider') {\n            if (message.data.data?.id === id) {\n                window.removeEventListener('message', onMessage);\n\n                if (!message.data.data.error) {\n                    register();\n                }\n            } else {\n                postMessage();\n            }\n        }\n    }\n\n    window.addEventListener('message', onMessage);\n    window.setTimeout(() => window.removeEventListener('message', onMessage), 5000);\n\n    postMessage();\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsBA;;;ACnBO,IAAM,OACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC2BE,IAAO,yBAAP,MAA6B;EAAnC,cAAA;;AACI,sCAAA,IAAA,MAAqF,CAAA,CAAE;AACvF,oCAAA,IAAA,MAAoB,OAAgB;AACpC,iCAAA,IAAA,MAAiB,UAAmB;AACpC,iCAAA,IAAA,MAAiB,IAAI;AACrB,6CAAA,IAAA,MAA6C,IAAI;AA0DjD,+BAAA,IAAA,MAA8B,CAAC,OAAO,aAAY;;AAC9C,oCAAA,MAAI,mCAAA,GAAA,EAAY,KAAK,MAArB,mBAAwB,KAAK,eAAc,uBAAA,MAAI,mCAAA,GAAA,EAAY,KAAK,IAAI,CAAC,QAAQ;AAC7E,aAAO,MAAY,uBAAA,MAAI,mCAAA,KAAA,2BAAA,EAAK,KAAT,MAAU,OAAO,QAAQ;IAChD,CAAC;AAWD,oCAAA,IAAA,MAAkC,YAAW;AACzC,UAAI,CAAC,uBAAA,MAAI,0CAAA,GAAA,GAAoB;AACzB,YAAI;AACJ,YAAI;AACA,mCAAyB,MAAM,OAAO,mBAA+B,GAAG;iBACnE,OAAY;AACjB,gBAAM,IAAI,MAAM,sCAAsC;;AAE1D,+BAAA,MAAI,0CAAqB,IAAI,sBAAqB,GAAE,GAAA;AACpD,+BAAA,MAAI,0CAAA,GAAA,EAAmB,GAAG,mBAAmB,CAAC,eAC1C,uBAAA,MAAI,mCAAA,KAAA,4BAAA,EAAM,KAAV,MAAW,UAAU,UAAU,CAAC;;AAIxC,UAAI,CAAC,KAAK,SAAS,QAAQ;AACvB,cAAM,uBAAA,MAAI,0CAAA,GAAA,EAAmB,QAAO;;AAGxC,aAAO,EAAE,UAAU,KAAK,SAAQ;IACpC,CAAC;AAED,uCAAA,IAAA,MAAwC,YAAW;AAC/C,UAAI,CAAC,uBAAA,MAAI,0CAAA,GAAA;AAAoB;AAC7B,YAAM,uBAAA,MAAI,0CAAA,GAAA,EAAmB,WAAU;IAC3C,CAAC;AAED,mDAAA,IAAA,MAA8D,UAAU,WAAU;AAC9E,UAAI,CAAC,uBAAA,MAAI,0CAAA,GAAA;AAAoB,cAAM,IAAI,wBAAuB;AAC9D,aAAO,MAAM,uBAAA,MAAI,0CAAA,GAAA,EAAmB,+BAA+B,GAAG,MAAM;IAChF,CAAC;AAED,4CAAA,IAAA,MAAgD,UAAU,WAAU;AAChE,UAAI,CAAC,uBAAA,MAAI,0CAAA,GAAA;AAAoB,cAAM,IAAI,wBAAuB;AAC9D,aAAO,MAAM,uBAAA,MAAI,0CAAA,GAAA,EAAmB,wBAAwB,GAAG,MAAM;IACzE,CAAC;AAED,wCAAA,IAAA,MAAwC,UAAU,WAAU;AACxD,UAAI,CAAC,uBAAA,MAAI,0CAAA,GAAA;AAAoB,cAAM,IAAI,wBAAuB;AAC9D,aAAO,MAAM,uBAAA,MAAI,0CAAA,GAAA,EAAmB,oBAAoB,GAAG,MAAM;IACrE,CAAC;EACL;EA9GI,IAAI,UAAO;AACP,WAAO,uBAAA,MAAI,iCAAA,GAAA;EACf;EAEA,IAAI,OAAI;AACJ,WAAO,uBAAA,MAAI,8BAAA,GAAA;EACf;EAEA,IAAI,OAAI;AACJ,WAAO,uBAAA,MAAI,8BAAA,GAAA;EACf;EAEA,IAAI,SAAM;AACN,WAAO,CAAC,sBAAsB,qBAAqB,oBAAoB;EAC3E;EAEA,IAAI,WAAQ;AAMR,WAAO;MACH,CAAC,eAAe,GAAG;QACf,SAAS;QACT,SAAS,uBAAA,MAAI,iCAAA,GAAA;;MAEjB,CAAC,kBAAkB,GAAG;QAClB,SAAS;QACT,YAAY,uBAAA,MAAI,oCAAA,GAAA;;MAEpB,CAAC,cAAc,GAAG;QACd,SAAS;QACT,IAAI,uBAAA,MAAI,4BAAA,GAAA;;MAEZ,CAAC,4BAA4B,GAAG;QAC5B,SAAS;QACT,8BAA8B,CAAC,UAAU,CAAC;QAC1C,wBAAwB,uBAAA,MAAI,gDAAA,GAAA;;MAEhC,CAAC,qBAAqB,GAAG;QACrB,SAAS;QACT,8BAA8B,CAAC,UAAU,CAAC;QAC1C,iBAAiB,uBAAA,MAAI,yCAAA,GAAA;;MAEzB,CAAC,iBAAiB,GAAG;QACjB,SAAS;QACT,aAAa,uBAAA,MAAI,qCAAA,GAAA;;;EAG7B;EAEA,IAAI,WAAQ;AACR,WAAO,uBAAA,MAAI,0CAAA,GAAA,IAAqB,uBAAA,MAAI,0CAAA,GAAA,EAAmB,mBAAmB,CAAA;EAC9E;;k3BAOqC,UAAa,MAA4C;;AAE1F,+BAAA,MAAI,mCAAA,GAAA,EAAY,KAAK,MAArB,mBAAwB,QAAQ,CAAC,aAAa,SAAS,MAAM,MAAM,IAAI;AAC3E,GAAC,8BAAA,SAAAA,6BAEmC,OAAU,UAAoC;;AAC9E,yBAAA,MAAI,mCAAA,GAAA,EAAY,KAAK,KAAI,4BAAA,MAAI,mCAAA,GAAA,EAAY,KAAK,MAArB,mBAAwB,OAAO,CAAC,qBAAqB,aAAa;AAC/F;;;ACvGJ,IAAI,aAAa;AAEjB,SAAS,WAAQ;AACb,MAAI;AAAY;AAChB,iBAAe,IAAI,uBAAsB,CAAE;AAC3C,eAAa;AACjB;AAGA,eAAsB,0CAAuC;AACzD,QAAM,KAAK;AAEX,WAAS,cAAW;AAChB,WAAO,YACH;MACI,QAAQ;MACR,MAAM;QACF,MAAM;QACN,MAAM;UACF;UACA,SAAS;UACT,QAAQ;;;OAIpB,OAAO,SAAS,MAAM;EAE9B;AAEA,WAAS,UAAU,OAAmB;AAhC1C;AAiCQ,UAAM,UAAU,MAAM;AACtB,SAAI,mCAAS,YAAW,uBAAqB,aAAQ,SAAR,mBAAc,UAAS,qBAAqB;AACrF,YAAI,aAAQ,KAAK,SAAb,mBAAmB,QAAO,IAAI;AAC9B,eAAO,oBAAoB,WAAW,SAAS;AAE/C,YAAI,CAAC,QAAQ,KAAK,KAAK,OAAO;AAC1B,mBAAQ;;aAET;AACH,oBAAW;;;EAGvB;AAEA,SAAO,iBAAiB,WAAW,SAAS;AAC5C,SAAO,WAAW,MAAM,OAAO,oBAAoB,WAAW,SAAS,GAAG,GAAI;AAE9E,cAAW;AACf;;;AHZO,IAAM,qBAAqB;AAE5B,IAAO,wBAAP,cAAqC,+BAA8B;EAgBrE,YAAY,SAAsC,CAAA,GAAE;AAChD,UAAK;AAhBT,SAAA,OAAO;AACP,SAAA,MAAM;AACN,SAAA,OACI;AACJ,SAAA,+BAAgE,oBAAI,IAAI,CAAC,UAAU,CAAC,CAAC;AAM7E,SAAA,cACJ,OAAO,WAAW,eAAe,OAAO,aAAa,cAC/C,iBAAiB,cACjB,iBAAiB;AAgNnB,SAAA,gBAAgB,MAAK;AACzB,YAAM,SAAS,KAAK;AACpB,UAAI,QAAQ;AACR,eAAO,IAAI,cAAc,KAAK,aAAa;AAE3C,aAAK,UAAU;AACf,aAAK,aAAa;AAElB,aAAK,KAAK,SAAS,IAAI,wBAAuB,CAAE;AAChD,aAAK,KAAK,YAAY;;IAE9B;AAEQ,SAAA,kBAAkB,CAAC,iBAA4B;AACnD,UAAI,CAAC;AAAc;AAEnB,YAAM,YAAY,KAAK;AACvB,UAAI,CAAC;AAAW;AAEhB,UAAI;AACA,uBAAe,IAAI,UAAU,aAAa,QAAO,CAAE;eAC9C,OAAY;AACjB,aAAK,KAAK,SAAS,IAAI,qBAAqB,+BAAO,SAAS,KAAK,CAAC;AAClE;;AAGJ,UAAI,UAAU,OAAO,YAAY;AAAG;AAEpC,WAAK,aAAa;AAClB,WAAK,KAAK,WAAW,YAAY;IACrC;AA1OI,SAAK,cAAc;AACnB,SAAK,aAAa;AAClB,SAAK,UAAU;AACf,SAAK,UAAU;AAEf,QAAI,KAAK,gBAAgB,iBAAiB,aAAa;AACnD,oCAA8B,MAAK;AAhE/C;AAiEgB,cAAI,YAAO,aAAP,mBAAiB,eAAc,OAAO,aAAa;AACnD,eAAK,cAAc,iBAAiB;AACpC,eAAK,KAAK,oBAAoB,KAAK,WAAW;AAC9C,iBAAO;;AAEX,eAAO;MACX,CAAC;AACD,8CAAuC;;EAE/C;EAEA,IAAI,YAAS;AACT,WAAO,KAAK;EAChB;EAEA,IAAI,aAAU;AACV,WAAO,KAAK;EAChB;EAEA,IAAI,YAAS;AApFjB;AAqFQ,WAAO,CAAC,GAAC,UAAK,YAAL,mBAAc;EAC3B;EAEA,IAAI,aAAU;AACV,WAAO,KAAK;EAChB;EAEA,MAAM,cAAW;AAGb,QAAI,EAAE,KAAK,eAAe,iBAAiB,YAAY,qBAAoB,IAAK;AAC5E,YAAM,KAAK,QAAO;;EAE1B;EAEA,MAAM,UAAO;AACT,QAAI;AACA,UAAI,KAAK,aAAa,KAAK;AAAY;AACvC,UAAI,KAAK,gBAAgB,iBAAiB,YAAY,KAAK,gBAAgB,iBAAiB;AACxF,cAAM,IAAI,oBAAmB;AAIjC,UAAI,KAAK,eAAe,iBAAiB,YAAY,qBAAoB,GAAI;AACzE,cAAM,MAAM,mBAAmB,OAAO,SAAS,IAAI;AACnD,cAAM,MAAM,mBAAmB,OAAO,SAAS,MAAM;AACrD,eAAO,SAAS,OAAO,qCAAqC,GAAG,QAAQ,GAAG;AAC1E;;AAGJ,UAAI;AACJ,UAAI;AACA,yBAAiB,MAAM,OAAO,mBAAsB,GAAG;eAClD,OAAY;AACjB,cAAM,IAAI,gBAAgB,+BAAO,SAAS,KAAK;;AAGnD,UAAI;AACJ,UAAI;AACA,iBAAS,IAAI,cAAc,EAAE,SAAS,KAAK,QAAQ,QAAO,CAAE;eACvD,OAAY;AACjB,cAAM,IAAI,kBAAkB,+BAAO,SAAS,KAAK;;AAGrD,WAAK,cAAc;AAEnB,UAAI,CAAC,OAAO,WAAW;AACnB,YAAI;AACA,gBAAM,OAAO,QAAO;iBACf,OAAY;AACjB,gBAAM,IAAI,sBAAsB,+BAAO,SAAS,KAAK;;;AAI7D,UAAI,CAAC,OAAO;AAAW,cAAM,IAAI,sBAAqB;AAEtD,UAAI;AACJ,UAAI;AACA,oBAAY,IAAI,UAAU,OAAO,UAAU,QAAO,CAAE;eAC/C,OAAY;AACjB,cAAM,IAAI,qBAAqB,+BAAO,SAAS,KAAK;;AAGxD,aAAO,GAAG,cAAc,KAAK,aAAa;AAC1C,aAAO,GAAG,kBAAkB,KAAK,eAAe;AAEhD,WAAK,UAAU;AACf,WAAK,aAAa;AAElB,WAAK,KAAK,WAAW,SAAS;aACzB,OAAY;AACjB,WAAK,KAAK,SAAS,KAAK;AACxB,YAAM;;AAEN,WAAK,cAAc;;EAE3B;EAEA,MAAM,aAAU;AACZ,UAAM,SAAS,KAAK;AACpB,QAAI,QAAQ;AACR,aAAO,IAAI,cAAc,KAAK,aAAa;AAC3C,aAAO,IAAI,kBAAkB,KAAK,eAAe;AAEjD,WAAK,UAAU;AACf,WAAK,aAAa;AAElB,UAAI;AACA,cAAM,OAAO,WAAU;eAClB,OAAY;AACjB,aAAK,KAAK,SAAS,IAAI,yBAAyB,+BAAO,SAAS,KAAK,CAAC;;;AAI9E,SAAK,KAAK,YAAY;EAC1B;EAEA,MAAM,gBACF,aACA,YACA,UAAkC,CAAA,GAAE;AAEpC,QAAI;AACA,YAAM,SAAS,KAAK;AACpB,UAAI,CAAC;AAAQ,cAAM,IAAI,wBAAuB;AAE9C,UAAI;AACA,cAAM,EAAE,SAAS,GAAG,YAAW,IAAK;AAEpC,YAAI,uBAAuB,WAAW,GAAG;AACrC,8CAAS,WAAU,YAAY,KAAK,OAAO;eACxC;AACH,wBAAe,MAAM,KAAK,mBAAmB,aAAa,YAAY,WAAW;AACjF,8CAAS,WAAW,YAA4B,YAAY,GAAG,OAAO;;AAG1E,oBAAY,sBAAsB,YAAY,uBAAuB,WAAW;AAEhF,eAAO,MAAM,OAAO,uBAAuB,aAAa,WAAW;eAC9D,OAAY;AACjB,YAAI,iBAAiB;AAAa,gBAAM;AACxC,cAAM,IAAI,2BAA2B,+BAAO,SAAS,KAAK;;aAEzD,OAAY;AACjB,WAAK,KAAK,SAAS,KAAK;AACxB,YAAM;;EAEd;EAEA,MAAM,gBAA8D,aAAc;AAC9E,QAAI;AACA,YAAM,SAAS,KAAK;AACpB,UAAI,CAAC;AAAQ,cAAM,IAAI,wBAAuB;AAE9C,UAAI;AACA,eAAS,MAAM,OAAO,gBAAgB,WAAW,KAAY;eACxD,OAAY;AACjB,cAAM,IAAI,2BAA2B,+BAAO,SAAS,KAAK;;aAEzD,OAAY;AACjB,WAAK,KAAK,SAAS,KAAK;AACxB,YAAM;;EAEd;EAEA,MAAM,oBAAkE,cAAiB;AACrF,QAAI;AACA,YAAM,SAAS,KAAK;AACpB,UAAI,CAAC;AAAQ,cAAM,IAAI,wBAAuB;AAE9C,UAAI;AACA,eAAS,MAAM,OAAO,oBAAoB,YAAY,KAAc;eAC/D,OAAY;AACjB,cAAM,IAAI,2BAA2B,+BAAO,SAAS,KAAK;;aAEzD,OAAY;AACjB,WAAK,KAAK,SAAS,KAAK;AACxB,YAAM;;EAEd;EAEA,MAAM,YAAY,SAAmB;AACjC,QAAI;AACA,YAAM,SAAS,KAAK;AACpB,UAAI,CAAC;AAAQ,cAAM,IAAI,wBAAuB;AAE9C,UAAI;AACA,eAAO,MAAM,OAAO,YAAY,SAAS,MAAM;eAC1C,OAAY;AACjB,cAAM,IAAI,uBAAuB,+BAAO,SAAS,KAAK;;aAErD,OAAY;AACjB,WAAK,KAAK,SAAS,KAAK;AACxB,YAAM;;EAEd;;", "names": ["_SolflareMetaMaskWallet_off"]}