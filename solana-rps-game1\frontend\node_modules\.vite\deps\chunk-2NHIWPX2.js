import {
  require_bs58
} from "./chunk-MG7LX4OU.js";
import {
  ReadonlyWalletAccount,
  SOLANA_DEVNET_CHAIN,
  SOLANA_LOCALNET_CHAIN,
  SOLANA_MAINNET_CHAIN,
  SOLANA_TESTNET_CHAIN,
  arraysEqual,
  bytesEqual
} from "./chunk-VKDBSG5X.js";
import {
  BaseWalletAdapter,
  StandardConnect,
  StandardDisconnect,
  StandardEvents,
  WalletAccountError,
  WalletConfigError,
  WalletConnectionError,
  WalletDisconnectedError,
  WalletDisconnectionError,
  WalletError,
  WalletNotConnectedError,
  WalletNotReadyError,
  WalletPublicKeyError,
  WalletReadyState,
  WalletSendTransactionError,
  WalletSignInError,
  WalletSignMessageError,
  WalletSignTransactionError,
  isVersionedTransaction,
  isWalletAdapterCompatibleStandardWallet,
  require_eventemitter3
} from "./chunk-6SC6RHPY.js";
import {
  SolanaSignAndSendTransaction,
  SolanaSignIn,
  SolanaSignMessage,
  SolanaSignTransaction
} from "./chunk-42XXHGZT.js";
import {
  require_react
} from "./chunk-XLKA4T3M.js";
import {
  Connection,
  PublicKey,
  SIGNATURE_LENGTH_IN_BYTES,
  Transaction,
  VersionedMessage,
  VersionedTransaction,
  index_browser_esm_exports,
  init_ed25519,
  init_index_browser_esm
} from "./chunk-EMSKGLQ5.js";
import {
  __commonJS,
  __esm,
  __export,
  __toCommonJS,
  __toESM
} from "./chunk-WXXH56N5.js";

// node_modules/@solana/wallet-adapter-base/lib/cjs/errors.js
var require_errors = __commonJS({
  "node_modules/@solana/wallet-adapter-base/lib/cjs/errors.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.WalletWindowClosedError = exports.WalletWindowBlockedError = exports.WalletTimeoutError = exports.WalletSignInError = exports.WalletSignMessageError = exports.WalletSignTransactionError = exports.WalletSendTransactionError = exports.WalletNotConnectedError = exports.WalletKeypairError = exports.WalletPublicKeyError = exports.WalletAccountError = exports.WalletDisconnectionError = exports.WalletDisconnectedError = exports.WalletConnectionError = exports.WalletConfigError = exports.WalletLoadError = exports.WalletNotReadyError = exports.WalletError = void 0;
    var WalletError2 = class extends Error {
      // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
      constructor(message, error) {
        super(message);
        this.error = error;
      }
    };
    exports.WalletError = WalletError2;
    var WalletNotReadyError2 = class extends WalletError2 {
      constructor() {
        super(...arguments);
        this.name = "WalletNotReadyError";
      }
    };
    exports.WalletNotReadyError = WalletNotReadyError2;
    var WalletLoadError = class extends WalletError2 {
      constructor() {
        super(...arguments);
        this.name = "WalletLoadError";
      }
    };
    exports.WalletLoadError = WalletLoadError;
    var WalletConfigError2 = class extends WalletError2 {
      constructor() {
        super(...arguments);
        this.name = "WalletConfigError";
      }
    };
    exports.WalletConfigError = WalletConfigError2;
    var WalletConnectionError2 = class extends WalletError2 {
      constructor() {
        super(...arguments);
        this.name = "WalletConnectionError";
      }
    };
    exports.WalletConnectionError = WalletConnectionError2;
    var WalletDisconnectedError2 = class extends WalletError2 {
      constructor() {
        super(...arguments);
        this.name = "WalletDisconnectedError";
      }
    };
    exports.WalletDisconnectedError = WalletDisconnectedError2;
    var WalletDisconnectionError2 = class extends WalletError2 {
      constructor() {
        super(...arguments);
        this.name = "WalletDisconnectionError";
      }
    };
    exports.WalletDisconnectionError = WalletDisconnectionError2;
    var WalletAccountError2 = class extends WalletError2 {
      constructor() {
        super(...arguments);
        this.name = "WalletAccountError";
      }
    };
    exports.WalletAccountError = WalletAccountError2;
    var WalletPublicKeyError2 = class extends WalletError2 {
      constructor() {
        super(...arguments);
        this.name = "WalletPublicKeyError";
      }
    };
    exports.WalletPublicKeyError = WalletPublicKeyError2;
    var WalletKeypairError = class extends WalletError2 {
      constructor() {
        super(...arguments);
        this.name = "WalletKeypairError";
      }
    };
    exports.WalletKeypairError = WalletKeypairError;
    var WalletNotConnectedError2 = class extends WalletError2 {
      constructor() {
        super(...arguments);
        this.name = "WalletNotConnectedError";
      }
    };
    exports.WalletNotConnectedError = WalletNotConnectedError2;
    var WalletSendTransactionError2 = class extends WalletError2 {
      constructor() {
        super(...arguments);
        this.name = "WalletSendTransactionError";
      }
    };
    exports.WalletSendTransactionError = WalletSendTransactionError2;
    var WalletSignTransactionError2 = class extends WalletError2 {
      constructor() {
        super(...arguments);
        this.name = "WalletSignTransactionError";
      }
    };
    exports.WalletSignTransactionError = WalletSignTransactionError2;
    var WalletSignMessageError2 = class extends WalletError2 {
      constructor() {
        super(...arguments);
        this.name = "WalletSignMessageError";
      }
    };
    exports.WalletSignMessageError = WalletSignMessageError2;
    var WalletSignInError2 = class extends WalletError2 {
      constructor() {
        super(...arguments);
        this.name = "WalletSignInError";
      }
    };
    exports.WalletSignInError = WalletSignInError2;
    var WalletTimeoutError = class extends WalletError2 {
      constructor() {
        super(...arguments);
        this.name = "WalletTimeoutError";
      }
    };
    exports.WalletTimeoutError = WalletTimeoutError;
    var WalletWindowBlockedError = class extends WalletError2 {
      constructor() {
        super(...arguments);
        this.name = "WalletWindowBlockedError";
      }
    };
    exports.WalletWindowBlockedError = WalletWindowBlockedError;
    var WalletWindowClosedError = class extends WalletError2 {
      constructor() {
        super(...arguments);
        this.name = "WalletWindowClosedError";
      }
    };
    exports.WalletWindowClosedError = WalletWindowClosedError;
  }
});

// node_modules/@solana/wallet-adapter-base/lib/cjs/adapter.js
var require_adapter = __commonJS({
  "node_modules/@solana/wallet-adapter-base/lib/cjs/adapter.js"(exports) {
    "use strict";
    var __awaiter2 = exports && exports.__awaiter || function(thisArg, _arguments, P, generator) {
      function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
          resolve(value);
        });
      }
      return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
          try {
            step(generator.next(value));
          } catch (e) {
            reject(e);
          }
        }
        function rejected(value) {
          try {
            step(generator["throw"](value));
          } catch (e) {
            reject(e);
          }
        }
        function step(result) {
          result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
      });
    };
    var __importDefault = exports && exports.__importDefault || function(mod) {
      return mod && mod.__esModule ? mod : { "default": mod };
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.isIosAndRedirectable = exports.scopePollingDetectionStrategy = exports.BaseWalletAdapter = exports.WalletReadyState = exports.EventEmitter = void 0;
    var eventemitter3_1 = __importDefault(require_eventemitter3());
    exports.EventEmitter = eventemitter3_1.default;
    var errors_js_1 = require_errors();
    var WalletReadyState2;
    (function(WalletReadyState3) {
      WalletReadyState3["Installed"] = "Installed";
      WalletReadyState3["NotDetected"] = "NotDetected";
      WalletReadyState3["Loadable"] = "Loadable";
      WalletReadyState3["Unsupported"] = "Unsupported";
    })(WalletReadyState2 = exports.WalletReadyState || (exports.WalletReadyState = {}));
    var BaseWalletAdapter2 = class extends eventemitter3_1.default {
      get connected() {
        return !!this.publicKey;
      }
      autoConnect() {
        return __awaiter2(this, void 0, void 0, function* () {
          yield this.connect();
        });
      }
      prepareTransaction(transaction, connection, options = {}) {
        return __awaiter2(this, void 0, void 0, function* () {
          const publicKey = this.publicKey;
          if (!publicKey)
            throw new errors_js_1.WalletNotConnectedError();
          transaction.feePayer = transaction.feePayer || publicKey;
          transaction.recentBlockhash = transaction.recentBlockhash || (yield connection.getLatestBlockhash({
            commitment: options.preflightCommitment,
            minContextSlot: options.minContextSlot
          })).blockhash;
          return transaction;
        });
      }
    };
    exports.BaseWalletAdapter = BaseWalletAdapter2;
    function scopePollingDetectionStrategy(detect) {
      if (typeof window === "undefined" || typeof document === "undefined")
        return;
      const disposers = [];
      function detectAndDispose() {
        const detected = detect();
        if (detected) {
          for (const dispose of disposers) {
            dispose();
          }
        }
      }
      const interval = (
        // TODO: #334 Replace with idle callback strategy.
        setInterval(detectAndDispose, 1e3)
      );
      disposers.push(() => clearInterval(interval));
      if (
        // Implies that `DOMContentLoaded` has not yet fired.
        document.readyState === "loading"
      ) {
        document.addEventListener("DOMContentLoaded", detectAndDispose, { once: true });
        disposers.push(() => document.removeEventListener("DOMContentLoaded", detectAndDispose));
      }
      if (
        // If the `complete` state has been reached, we're too late.
        document.readyState !== "complete"
      ) {
        window.addEventListener("load", detectAndDispose, { once: true });
        disposers.push(() => window.removeEventListener("load", detectAndDispose));
      }
      detectAndDispose();
    }
    exports.scopePollingDetectionStrategy = scopePollingDetectionStrategy;
    function isIosAndRedirectable() {
      if (!navigator)
        return false;
      const userAgent = navigator.userAgent.toLowerCase();
      const isIos = userAgent.includes("iphone") || userAgent.includes("ipad");
      const isSafari = userAgent.includes("safari");
      return isIos && isSafari;
    }
    exports.isIosAndRedirectable = isIosAndRedirectable;
  }
});

// node_modules/@solana/wallet-adapter-base/lib/cjs/transaction.js
var require_transaction = __commonJS({
  "node_modules/@solana/wallet-adapter-base/lib/cjs/transaction.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.isVersionedTransaction = void 0;
    function isVersionedTransaction2(transaction) {
      return "version" in transaction;
    }
    exports.isVersionedTransaction = isVersionedTransaction2;
  }
});

// node_modules/@solana/wallet-adapter-base/lib/cjs/signer.js
var require_signer = __commonJS({
  "node_modules/@solana/wallet-adapter-base/lib/cjs/signer.js"(exports) {
    "use strict";
    var __awaiter2 = exports && exports.__awaiter || function(thisArg, _arguments, P, generator) {
      function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
          resolve(value);
        });
      }
      return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
          try {
            step(generator.next(value));
          } catch (e) {
            reject(e);
          }
        }
        function rejected(value) {
          try {
            step(generator["throw"](value));
          } catch (e) {
            reject(e);
          }
        }
        function step(result) {
          result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
      });
    };
    var __rest2 = exports && exports.__rest || function(s, e) {
      var t = {};
      for (var p in s)
        if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
          t[p] = s[p];
      if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
            t[p[i]] = s[p[i]];
        }
      return t;
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.BaseSignInMessageSignerWalletAdapter = exports.BaseMessageSignerWalletAdapter = exports.BaseSignerWalletAdapter = void 0;
    var adapter_js_1 = require_adapter();
    var errors_js_1 = require_errors();
    var transaction_js_1 = require_transaction();
    var BaseSignerWalletAdapter = class extends adapter_js_1.BaseWalletAdapter {
      sendTransaction(transaction, connection, options = {}) {
        return __awaiter2(this, void 0, void 0, function* () {
          let emit = true;
          try {
            if ((0, transaction_js_1.isVersionedTransaction)(transaction)) {
              if (!this.supportedTransactionVersions)
                throw new errors_js_1.WalletSendTransactionError(`Sending versioned transactions isn't supported by this wallet`);
              if (!this.supportedTransactionVersions.has(transaction.version))
                throw new errors_js_1.WalletSendTransactionError(`Sending transaction version ${transaction.version} isn't supported by this wallet`);
              try {
                transaction = yield this.signTransaction(transaction);
                const rawTransaction = transaction.serialize();
                return yield connection.sendRawTransaction(rawTransaction, options);
              } catch (error) {
                if (error instanceof errors_js_1.WalletSignTransactionError) {
                  emit = false;
                  throw error;
                }
                throw new errors_js_1.WalletSendTransactionError(error === null || error === void 0 ? void 0 : error.message, error);
              }
            } else {
              try {
                const { signers } = options, sendOptions = __rest2(options, ["signers"]);
                transaction = yield this.prepareTransaction(transaction, connection, sendOptions);
                (signers === null || signers === void 0 ? void 0 : signers.length) && transaction.partialSign(...signers);
                transaction = yield this.signTransaction(transaction);
                const rawTransaction = transaction.serialize();
                return yield connection.sendRawTransaction(rawTransaction, sendOptions);
              } catch (error) {
                if (error instanceof errors_js_1.WalletSignTransactionError) {
                  emit = false;
                  throw error;
                }
                throw new errors_js_1.WalletSendTransactionError(error === null || error === void 0 ? void 0 : error.message, error);
              }
            }
          } catch (error) {
            if (emit) {
              this.emit("error", error);
            }
            throw error;
          }
        });
      }
      signAllTransactions(transactions) {
        return __awaiter2(this, void 0, void 0, function* () {
          for (const transaction of transactions) {
            if ((0, transaction_js_1.isVersionedTransaction)(transaction)) {
              if (!this.supportedTransactionVersions)
                throw new errors_js_1.WalletSignTransactionError(`Signing versioned transactions isn't supported by this wallet`);
              if (!this.supportedTransactionVersions.has(transaction.version))
                throw new errors_js_1.WalletSignTransactionError(`Signing transaction version ${transaction.version} isn't supported by this wallet`);
            }
          }
          const signedTransactions = [];
          for (const transaction of transactions) {
            signedTransactions.push(yield this.signTransaction(transaction));
          }
          return signedTransactions;
        });
      }
    };
    exports.BaseSignerWalletAdapter = BaseSignerWalletAdapter;
    var BaseMessageSignerWalletAdapter = class extends BaseSignerWalletAdapter {
    };
    exports.BaseMessageSignerWalletAdapter = BaseMessageSignerWalletAdapter;
    var BaseSignInMessageSignerWalletAdapter = class extends BaseMessageSignerWalletAdapter {
    };
    exports.BaseSignInMessageSignerWalletAdapter = BaseSignInMessageSignerWalletAdapter;
  }
});

// node_modules/@solana/wallet-standard-features/lib/cjs/signAndSendTransaction.js
var require_signAndSendTransaction = __commonJS({
  "node_modules/@solana/wallet-standard-features/lib/cjs/signAndSendTransaction.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.SolanaSignAndSendTransaction = void 0;
    exports.SolanaSignAndSendTransaction = "solana:signAndSendTransaction";
  }
});

// node_modules/@solana/wallet-standard-features/lib/cjs/signIn.js
var require_signIn = __commonJS({
  "node_modules/@solana/wallet-standard-features/lib/cjs/signIn.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.SolanaSignIn = void 0;
    exports.SolanaSignIn = "solana:signIn";
  }
});

// node_modules/@solana/wallet-standard-features/lib/cjs/signMessage.js
var require_signMessage = __commonJS({
  "node_modules/@solana/wallet-standard-features/lib/cjs/signMessage.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.SolanaSignMessage = void 0;
    exports.SolanaSignMessage = "solana:signMessage";
  }
});

// node_modules/@solana/wallet-standard-features/lib/cjs/signTransaction.js
var require_signTransaction = __commonJS({
  "node_modules/@solana/wallet-standard-features/lib/cjs/signTransaction.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.SolanaSignTransaction = void 0;
    exports.SolanaSignTransaction = "solana:signTransaction";
  }
});

// node_modules/@solana/wallet-standard-features/lib/cjs/signAndSendAllTransactions.js
var require_signAndSendAllTransactions = __commonJS({
  "node_modules/@solana/wallet-standard-features/lib/cjs/signAndSendAllTransactions.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.SignAndSendAllTransactions = void 0;
    exports.SignAndSendAllTransactions = "solana:signAndSendAllTransactions";
  }
});

// node_modules/@solana/wallet-standard-features/lib/cjs/index.js
var require_cjs = __commonJS({
  "node_modules/@solana/wallet-standard-features/lib/cjs/index.js"(exports) {
    "use strict";
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0)
        k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0)
        k2 = k;
      o[k2] = m[k];
    });
    var __exportStar = exports && exports.__exportStar || function(m, exports2) {
      for (var p in m)
        if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports2, p))
          __createBinding(exports2, m, p);
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    __exportStar(require_signAndSendTransaction(), exports);
    __exportStar(require_signIn(), exports);
    __exportStar(require_signMessage(), exports);
    __exportStar(require_signTransaction(), exports);
    __exportStar(require_signAndSendAllTransactions(), exports);
  }
});

// node_modules/@wallet-standard/features/lib/cjs/connect.js
var require_connect = __commonJS({
  "node_modules/@wallet-standard/features/lib/cjs/connect.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.Connect = exports.StandardConnect = void 0;
    exports.StandardConnect = "standard:connect";
    exports.Connect = exports.StandardConnect;
  }
});

// node_modules/@wallet-standard/features/lib/cjs/disconnect.js
var require_disconnect = __commonJS({
  "node_modules/@wallet-standard/features/lib/cjs/disconnect.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.Disconnect = exports.StandardDisconnect = void 0;
    exports.StandardDisconnect = "standard:disconnect";
    exports.Disconnect = exports.StandardDisconnect;
  }
});

// node_modules/@wallet-standard/features/lib/cjs/events.js
var require_events = __commonJS({
  "node_modules/@wallet-standard/features/lib/cjs/events.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.Events = exports.StandardEvents = void 0;
    exports.StandardEvents = "standard:events";
    exports.Events = exports.StandardEvents;
  }
});

// node_modules/@wallet-standard/features/lib/cjs/index.js
var require_cjs2 = __commonJS({
  "node_modules/@wallet-standard/features/lib/cjs/index.js"(exports) {
    "use strict";
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0)
        k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0)
        k2 = k;
      o[k2] = m[k];
    });
    var __exportStar = exports && exports.__exportStar || function(m, exports2) {
      for (var p in m)
        if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports2, p))
          __createBinding(exports2, m, p);
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    __exportStar(require_connect(), exports);
    __exportStar(require_disconnect(), exports);
    __exportStar(require_events(), exports);
  }
});

// node_modules/@solana/wallet-adapter-base/lib/cjs/standard.js
var require_standard = __commonJS({
  "node_modules/@solana/wallet-adapter-base/lib/cjs/standard.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.isWalletAdapterCompatibleStandardWallet = void 0;
    var wallet_standard_features_1 = require_cjs();
    var features_1 = require_cjs2();
    function isWalletAdapterCompatibleStandardWallet2(wallet) {
      return features_1.StandardConnect in wallet.features && features_1.StandardEvents in wallet.features && (wallet_standard_features_1.SolanaSignAndSendTransaction in wallet.features || wallet_standard_features_1.SolanaSignTransaction in wallet.features);
    }
    exports.isWalletAdapterCompatibleStandardWallet = isWalletAdapterCompatibleStandardWallet2;
  }
});

// node_modules/@solana/wallet-adapter-base/lib/cjs/types.js
var require_types = __commonJS({
  "node_modules/@solana/wallet-adapter-base/lib/cjs/types.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.WalletAdapterNetwork = void 0;
    var WalletAdapterNetwork;
    (function(WalletAdapterNetwork2) {
      WalletAdapterNetwork2["Mainnet"] = "mainnet-beta";
      WalletAdapterNetwork2["Testnet"] = "testnet";
      WalletAdapterNetwork2["Devnet"] = "devnet";
    })(WalletAdapterNetwork = exports.WalletAdapterNetwork || (exports.WalletAdapterNetwork = {}));
  }
});

// node_modules/@solana/wallet-adapter-base/lib/cjs/index.js
var require_cjs3 = __commonJS({
  "node_modules/@solana/wallet-adapter-base/lib/cjs/index.js"(exports) {
    "use strict";
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0)
        k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0)
        k2 = k;
      o[k2] = m[k];
    });
    var __exportStar = exports && exports.__exportStar || function(m, exports2) {
      for (var p in m)
        if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports2, p))
          __createBinding(exports2, m, p);
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    __exportStar(require_adapter(), exports);
    __exportStar(require_errors(), exports);
    __exportStar(require_signer(), exports);
    __exportStar(require_standard(), exports);
    __exportStar(require_transaction(), exports);
    __exportStar(require_types(), exports);
  }
});

// node_modules/qrcode/lib/can-promise.js
var require_can_promise = __commonJS({
  "node_modules/qrcode/lib/can-promise.js"(exports, module) {
    module.exports = function() {
      return typeof Promise === "function" && Promise.prototype && Promise.prototype.then;
    };
  }
});

// node_modules/qrcode/lib/core/utils.js
var require_utils = __commonJS({
  "node_modules/qrcode/lib/core/utils.js"(exports) {
    var toSJISFunction;
    var CODEWORDS_COUNT = [
      0,
      // Not used
      26,
      44,
      70,
      100,
      134,
      172,
      196,
      242,
      292,
      346,
      404,
      466,
      532,
      581,
      655,
      733,
      815,
      901,
      991,
      1085,
      1156,
      1258,
      1364,
      1474,
      1588,
      1706,
      1828,
      1921,
      2051,
      2185,
      2323,
      2465,
      2611,
      2761,
      2876,
      3034,
      3196,
      3362,
      3532,
      3706
    ];
    exports.getSymbolSize = function getSymbolSize(version) {
      if (!version)
        throw new Error('"version" cannot be null or undefined');
      if (version < 1 || version > 40)
        throw new Error('"version" should be in range from 1 to 40');
      return version * 4 + 17;
    };
    exports.getSymbolTotalCodewords = function getSymbolTotalCodewords(version) {
      return CODEWORDS_COUNT[version];
    };
    exports.getBCHDigit = function(data) {
      let digit = 0;
      while (data !== 0) {
        digit++;
        data >>>= 1;
      }
      return digit;
    };
    exports.setToSJISFunction = function setToSJISFunction(f) {
      if (typeof f !== "function") {
        throw new Error('"toSJISFunc" is not a valid function.');
      }
      toSJISFunction = f;
    };
    exports.isKanjiModeEnabled = function() {
      return typeof toSJISFunction !== "undefined";
    };
    exports.toSJIS = function toSJIS(kanji) {
      return toSJISFunction(kanji);
    };
  }
});

// node_modules/qrcode/lib/core/error-correction-level.js
var require_error_correction_level = __commonJS({
  "node_modules/qrcode/lib/core/error-correction-level.js"(exports) {
    exports.L = { bit: 1 };
    exports.M = { bit: 0 };
    exports.Q = { bit: 3 };
    exports.H = { bit: 2 };
    function fromString(string) {
      if (typeof string !== "string") {
        throw new Error("Param is not a string");
      }
      const lcStr = string.toLowerCase();
      switch (lcStr) {
        case "l":
        case "low":
          return exports.L;
        case "m":
        case "medium":
          return exports.M;
        case "q":
        case "quartile":
          return exports.Q;
        case "h":
        case "high":
          return exports.H;
        default:
          throw new Error("Unknown EC Level: " + string);
      }
    }
    exports.isValid = function isValid(level) {
      return level && typeof level.bit !== "undefined" && level.bit >= 0 && level.bit < 4;
    };
    exports.from = function from(value, defaultValue) {
      if (exports.isValid(value)) {
        return value;
      }
      try {
        return fromString(value);
      } catch (e) {
        return defaultValue;
      }
    };
  }
});

// node_modules/qrcode/lib/core/bit-buffer.js
var require_bit_buffer = __commonJS({
  "node_modules/qrcode/lib/core/bit-buffer.js"(exports, module) {
    function BitBuffer() {
      this.buffer = [];
      this.length = 0;
    }
    BitBuffer.prototype = {
      get: function(index) {
        const bufIndex = Math.floor(index / 8);
        return (this.buffer[bufIndex] >>> 7 - index % 8 & 1) === 1;
      },
      put: function(num, length) {
        for (let i = 0; i < length; i++) {
          this.putBit((num >>> length - i - 1 & 1) === 1);
        }
      },
      getLengthInBits: function() {
        return this.length;
      },
      putBit: function(bit) {
        const bufIndex = Math.floor(this.length / 8);
        if (this.buffer.length <= bufIndex) {
          this.buffer.push(0);
        }
        if (bit) {
          this.buffer[bufIndex] |= 128 >>> this.length % 8;
        }
        this.length++;
      }
    };
    module.exports = BitBuffer;
  }
});

// node_modules/qrcode/lib/core/bit-matrix.js
var require_bit_matrix = __commonJS({
  "node_modules/qrcode/lib/core/bit-matrix.js"(exports, module) {
    function BitMatrix(size) {
      if (!size || size < 1) {
        throw new Error("BitMatrix size must be defined and greater than 0");
      }
      this.size = size;
      this.data = new Uint8Array(size * size);
      this.reservedBit = new Uint8Array(size * size);
    }
    BitMatrix.prototype.set = function(row, col, value, reserved) {
      const index = row * this.size + col;
      this.data[index] = value;
      if (reserved)
        this.reservedBit[index] = true;
    };
    BitMatrix.prototype.get = function(row, col) {
      return this.data[row * this.size + col];
    };
    BitMatrix.prototype.xor = function(row, col, value) {
      this.data[row * this.size + col] ^= value;
    };
    BitMatrix.prototype.isReserved = function(row, col) {
      return this.reservedBit[row * this.size + col];
    };
    module.exports = BitMatrix;
  }
});

// node_modules/qrcode/lib/core/alignment-pattern.js
var require_alignment_pattern = __commonJS({
  "node_modules/qrcode/lib/core/alignment-pattern.js"(exports) {
    var getSymbolSize = require_utils().getSymbolSize;
    exports.getRowColCoords = function getRowColCoords(version) {
      if (version === 1)
        return [];
      const posCount = Math.floor(version / 7) + 2;
      const size = getSymbolSize(version);
      const intervals = size === 145 ? 26 : Math.ceil((size - 13) / (2 * posCount - 2)) * 2;
      const positions = [size - 7];
      for (let i = 1; i < posCount - 1; i++) {
        positions[i] = positions[i - 1] - intervals;
      }
      positions.push(6);
      return positions.reverse();
    };
    exports.getPositions = function getPositions(version) {
      const coords = [];
      const pos = exports.getRowColCoords(version);
      const posLength = pos.length;
      for (let i = 0; i < posLength; i++) {
        for (let j = 0; j < posLength; j++) {
          if (i === 0 && j === 0 || // top-left
          i === 0 && j === posLength - 1 || // bottom-left
          i === posLength - 1 && j === 0) {
            continue;
          }
          coords.push([pos[i], pos[j]]);
        }
      }
      return coords;
    };
  }
});

// node_modules/qrcode/lib/core/finder-pattern.js
var require_finder_pattern = __commonJS({
  "node_modules/qrcode/lib/core/finder-pattern.js"(exports) {
    var getSymbolSize = require_utils().getSymbolSize;
    var FINDER_PATTERN_SIZE = 7;
    exports.getPositions = function getPositions(version) {
      const size = getSymbolSize(version);
      return [
        // top-left
        [0, 0],
        // top-right
        [size - FINDER_PATTERN_SIZE, 0],
        // bottom-left
        [0, size - FINDER_PATTERN_SIZE]
      ];
    };
  }
});

// node_modules/qrcode/lib/core/mask-pattern.js
var require_mask_pattern = __commonJS({
  "node_modules/qrcode/lib/core/mask-pattern.js"(exports) {
    exports.Patterns = {
      PATTERN000: 0,
      PATTERN001: 1,
      PATTERN010: 2,
      PATTERN011: 3,
      PATTERN100: 4,
      PATTERN101: 5,
      PATTERN110: 6,
      PATTERN111: 7
    };
    var PenaltyScores = {
      N1: 3,
      N2: 3,
      N3: 40,
      N4: 10
    };
    exports.isValid = function isValid(mask) {
      return mask != null && mask !== "" && !isNaN(mask) && mask >= 0 && mask <= 7;
    };
    exports.from = function from(value) {
      return exports.isValid(value) ? parseInt(value, 10) : void 0;
    };
    exports.getPenaltyN1 = function getPenaltyN1(data) {
      const size = data.size;
      let points = 0;
      let sameCountCol = 0;
      let sameCountRow = 0;
      let lastCol = null;
      let lastRow = null;
      for (let row = 0; row < size; row++) {
        sameCountCol = sameCountRow = 0;
        lastCol = lastRow = null;
        for (let col = 0; col < size; col++) {
          let module2 = data.get(row, col);
          if (module2 === lastCol) {
            sameCountCol++;
          } else {
            if (sameCountCol >= 5)
              points += PenaltyScores.N1 + (sameCountCol - 5);
            lastCol = module2;
            sameCountCol = 1;
          }
          module2 = data.get(col, row);
          if (module2 === lastRow) {
            sameCountRow++;
          } else {
            if (sameCountRow >= 5)
              points += PenaltyScores.N1 + (sameCountRow - 5);
            lastRow = module2;
            sameCountRow = 1;
          }
        }
        if (sameCountCol >= 5)
          points += PenaltyScores.N1 + (sameCountCol - 5);
        if (sameCountRow >= 5)
          points += PenaltyScores.N1 + (sameCountRow - 5);
      }
      return points;
    };
    exports.getPenaltyN2 = function getPenaltyN2(data) {
      const size = data.size;
      let points = 0;
      for (let row = 0; row < size - 1; row++) {
        for (let col = 0; col < size - 1; col++) {
          const last = data.get(row, col) + data.get(row, col + 1) + data.get(row + 1, col) + data.get(row + 1, col + 1);
          if (last === 4 || last === 0)
            points++;
        }
      }
      return points * PenaltyScores.N2;
    };
    exports.getPenaltyN3 = function getPenaltyN3(data) {
      const size = data.size;
      let points = 0;
      let bitsCol = 0;
      let bitsRow = 0;
      for (let row = 0; row < size; row++) {
        bitsCol = bitsRow = 0;
        for (let col = 0; col < size; col++) {
          bitsCol = bitsCol << 1 & 2047 | data.get(row, col);
          if (col >= 10 && (bitsCol === 1488 || bitsCol === 93))
            points++;
          bitsRow = bitsRow << 1 & 2047 | data.get(col, row);
          if (col >= 10 && (bitsRow === 1488 || bitsRow === 93))
            points++;
        }
      }
      return points * PenaltyScores.N3;
    };
    exports.getPenaltyN4 = function getPenaltyN4(data) {
      let darkCount = 0;
      const modulesCount = data.data.length;
      for (let i = 0; i < modulesCount; i++)
        darkCount += data.data[i];
      const k = Math.abs(Math.ceil(darkCount * 100 / modulesCount / 5) - 10);
      return k * PenaltyScores.N4;
    };
    function getMaskAt(maskPattern, i, j) {
      switch (maskPattern) {
        case exports.Patterns.PATTERN000:
          return (i + j) % 2 === 0;
        case exports.Patterns.PATTERN001:
          return i % 2 === 0;
        case exports.Patterns.PATTERN010:
          return j % 3 === 0;
        case exports.Patterns.PATTERN011:
          return (i + j) % 3 === 0;
        case exports.Patterns.PATTERN100:
          return (Math.floor(i / 2) + Math.floor(j / 3)) % 2 === 0;
        case exports.Patterns.PATTERN101:
          return i * j % 2 + i * j % 3 === 0;
        case exports.Patterns.PATTERN110:
          return (i * j % 2 + i * j % 3) % 2 === 0;
        case exports.Patterns.PATTERN111:
          return (i * j % 3 + (i + j) % 2) % 2 === 0;
        default:
          throw new Error("bad maskPattern:" + maskPattern);
      }
    }
    exports.applyMask = function applyMask(pattern, data) {
      const size = data.size;
      for (let col = 0; col < size; col++) {
        for (let row = 0; row < size; row++) {
          if (data.isReserved(row, col))
            continue;
          data.xor(row, col, getMaskAt(pattern, row, col));
        }
      }
    };
    exports.getBestMask = function getBestMask(data, setupFormatFunc) {
      const numPatterns = Object.keys(exports.Patterns).length;
      let bestPattern = 0;
      let lowerPenalty = Infinity;
      for (let p = 0; p < numPatterns; p++) {
        setupFormatFunc(p);
        exports.applyMask(p, data);
        const penalty = exports.getPenaltyN1(data) + exports.getPenaltyN2(data) + exports.getPenaltyN3(data) + exports.getPenaltyN4(data);
        exports.applyMask(p, data);
        if (penalty < lowerPenalty) {
          lowerPenalty = penalty;
          bestPattern = p;
        }
      }
      return bestPattern;
    };
  }
});

// node_modules/qrcode/lib/core/error-correction-code.js
var require_error_correction_code = __commonJS({
  "node_modules/qrcode/lib/core/error-correction-code.js"(exports) {
    var ECLevel = require_error_correction_level();
    var EC_BLOCKS_TABLE = [
      // L  M  Q  H
      1,
      1,
      1,
      1,
      1,
      1,
      1,
      1,
      1,
      1,
      2,
      2,
      1,
      2,
      2,
      4,
      1,
      2,
      4,
      4,
      2,
      4,
      4,
      4,
      2,
      4,
      6,
      5,
      2,
      4,
      6,
      6,
      2,
      5,
      8,
      8,
      4,
      5,
      8,
      8,
      4,
      5,
      8,
      11,
      4,
      8,
      10,
      11,
      4,
      9,
      12,
      16,
      4,
      9,
      16,
      16,
      6,
      10,
      12,
      18,
      6,
      10,
      17,
      16,
      6,
      11,
      16,
      19,
      6,
      13,
      18,
      21,
      7,
      14,
      21,
      25,
      8,
      16,
      20,
      25,
      8,
      17,
      23,
      25,
      9,
      17,
      23,
      34,
      9,
      18,
      25,
      30,
      10,
      20,
      27,
      32,
      12,
      21,
      29,
      35,
      12,
      23,
      34,
      37,
      12,
      25,
      34,
      40,
      13,
      26,
      35,
      42,
      14,
      28,
      38,
      45,
      15,
      29,
      40,
      48,
      16,
      31,
      43,
      51,
      17,
      33,
      45,
      54,
      18,
      35,
      48,
      57,
      19,
      37,
      51,
      60,
      19,
      38,
      53,
      63,
      20,
      40,
      56,
      66,
      21,
      43,
      59,
      70,
      22,
      45,
      62,
      74,
      24,
      47,
      65,
      77,
      25,
      49,
      68,
      81
    ];
    var EC_CODEWORDS_TABLE = [
      // L  M  Q  H
      7,
      10,
      13,
      17,
      10,
      16,
      22,
      28,
      15,
      26,
      36,
      44,
      20,
      36,
      52,
      64,
      26,
      48,
      72,
      88,
      36,
      64,
      96,
      112,
      40,
      72,
      108,
      130,
      48,
      88,
      132,
      156,
      60,
      110,
      160,
      192,
      72,
      130,
      192,
      224,
      80,
      150,
      224,
      264,
      96,
      176,
      260,
      308,
      104,
      198,
      288,
      352,
      120,
      216,
      320,
      384,
      132,
      240,
      360,
      432,
      144,
      280,
      408,
      480,
      168,
      308,
      448,
      532,
      180,
      338,
      504,
      588,
      196,
      364,
      546,
      650,
      224,
      416,
      600,
      700,
      224,
      442,
      644,
      750,
      252,
      476,
      690,
      816,
      270,
      504,
      750,
      900,
      300,
      560,
      810,
      960,
      312,
      588,
      870,
      1050,
      336,
      644,
      952,
      1110,
      360,
      700,
      1020,
      1200,
      390,
      728,
      1050,
      1260,
      420,
      784,
      1140,
      1350,
      450,
      812,
      1200,
      1440,
      480,
      868,
      1290,
      1530,
      510,
      924,
      1350,
      1620,
      540,
      980,
      1440,
      1710,
      570,
      1036,
      1530,
      1800,
      570,
      1064,
      1590,
      1890,
      600,
      1120,
      1680,
      1980,
      630,
      1204,
      1770,
      2100,
      660,
      1260,
      1860,
      2220,
      720,
      1316,
      1950,
      2310,
      750,
      1372,
      2040,
      2430
    ];
    exports.getBlocksCount = function getBlocksCount(version, errorCorrectionLevel) {
      switch (errorCorrectionLevel) {
        case ECLevel.L:
          return EC_BLOCKS_TABLE[(version - 1) * 4 + 0];
        case ECLevel.M:
          return EC_BLOCKS_TABLE[(version - 1) * 4 + 1];
        case ECLevel.Q:
          return EC_BLOCKS_TABLE[(version - 1) * 4 + 2];
        case ECLevel.H:
          return EC_BLOCKS_TABLE[(version - 1) * 4 + 3];
        default:
          return void 0;
      }
    };
    exports.getTotalCodewordsCount = function getTotalCodewordsCount(version, errorCorrectionLevel) {
      switch (errorCorrectionLevel) {
        case ECLevel.L:
          return EC_CODEWORDS_TABLE[(version - 1) * 4 + 0];
        case ECLevel.M:
          return EC_CODEWORDS_TABLE[(version - 1) * 4 + 1];
        case ECLevel.Q:
          return EC_CODEWORDS_TABLE[(version - 1) * 4 + 2];
        case ECLevel.H:
          return EC_CODEWORDS_TABLE[(version - 1) * 4 + 3];
        default:
          return void 0;
      }
    };
  }
});

// node_modules/qrcode/lib/core/galois-field.js
var require_galois_field = __commonJS({
  "node_modules/qrcode/lib/core/galois-field.js"(exports) {
    var EXP_TABLE = new Uint8Array(512);
    var LOG_TABLE = new Uint8Array(256);
    (function initTables() {
      let x = 1;
      for (let i = 0; i < 255; i++) {
        EXP_TABLE[i] = x;
        LOG_TABLE[x] = i;
        x <<= 1;
        if (x & 256) {
          x ^= 285;
        }
      }
      for (let i = 255; i < 512; i++) {
        EXP_TABLE[i] = EXP_TABLE[i - 255];
      }
    })();
    exports.log = function log(n) {
      if (n < 1)
        throw new Error("log(" + n + ")");
      return LOG_TABLE[n];
    };
    exports.exp = function exp(n) {
      return EXP_TABLE[n];
    };
    exports.mul = function mul(x, y) {
      if (x === 0 || y === 0)
        return 0;
      return EXP_TABLE[LOG_TABLE[x] + LOG_TABLE[y]];
    };
  }
});

// node_modules/qrcode/lib/core/polynomial.js
var require_polynomial = __commonJS({
  "node_modules/qrcode/lib/core/polynomial.js"(exports) {
    var GF = require_galois_field();
    exports.mul = function mul(p1, p2) {
      const coeff = new Uint8Array(p1.length + p2.length - 1);
      for (let i = 0; i < p1.length; i++) {
        for (let j = 0; j < p2.length; j++) {
          coeff[i + j] ^= GF.mul(p1[i], p2[j]);
        }
      }
      return coeff;
    };
    exports.mod = function mod(divident, divisor) {
      let result = new Uint8Array(divident);
      while (result.length - divisor.length >= 0) {
        const coeff = result[0];
        for (let i = 0; i < divisor.length; i++) {
          result[i] ^= GF.mul(divisor[i], coeff);
        }
        let offset = 0;
        while (offset < result.length && result[offset] === 0)
          offset++;
        result = result.slice(offset);
      }
      return result;
    };
    exports.generateECPolynomial = function generateECPolynomial(degree) {
      let poly = new Uint8Array([1]);
      for (let i = 0; i < degree; i++) {
        poly = exports.mul(poly, new Uint8Array([1, GF.exp(i)]));
      }
      return poly;
    };
  }
});

// node_modules/qrcode/lib/core/reed-solomon-encoder.js
var require_reed_solomon_encoder = __commonJS({
  "node_modules/qrcode/lib/core/reed-solomon-encoder.js"(exports, module) {
    var Polynomial = require_polynomial();
    function ReedSolomonEncoder(degree) {
      this.genPoly = void 0;
      this.degree = degree;
      if (this.degree)
        this.initialize(this.degree);
    }
    ReedSolomonEncoder.prototype.initialize = function initialize(degree) {
      this.degree = degree;
      this.genPoly = Polynomial.generateECPolynomial(this.degree);
    };
    ReedSolomonEncoder.prototype.encode = function encode(data) {
      if (!this.genPoly) {
        throw new Error("Encoder not initialized");
      }
      const paddedData = new Uint8Array(data.length + this.degree);
      paddedData.set(data);
      const remainder = Polynomial.mod(paddedData, this.genPoly);
      const start = this.degree - remainder.length;
      if (start > 0) {
        const buff = new Uint8Array(this.degree);
        buff.set(remainder, start);
        return buff;
      }
      return remainder;
    };
    module.exports = ReedSolomonEncoder;
  }
});

// node_modules/qrcode/lib/core/version-check.js
var require_version_check = __commonJS({
  "node_modules/qrcode/lib/core/version-check.js"(exports) {
    exports.isValid = function isValid(version) {
      return !isNaN(version) && version >= 1 && version <= 40;
    };
  }
});

// node_modules/qrcode/lib/core/regex.js
var require_regex = __commonJS({
  "node_modules/qrcode/lib/core/regex.js"(exports) {
    var numeric = "[0-9]+";
    var alphanumeric = "[A-Z $%*+\\-./:]+";
    var kanji = "(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";
    kanji = kanji.replace(/u/g, "\\u");
    var byte = "(?:(?![A-Z0-9 $%*+\\-./:]|" + kanji + ")(?:.|[\r\n]))+";
    exports.KANJI = new RegExp(kanji, "g");
    exports.BYTE_KANJI = new RegExp("[^A-Z0-9 $%*+\\-./:]+", "g");
    exports.BYTE = new RegExp(byte, "g");
    exports.NUMERIC = new RegExp(numeric, "g");
    exports.ALPHANUMERIC = new RegExp(alphanumeric, "g");
    var TEST_KANJI = new RegExp("^" + kanji + "$");
    var TEST_NUMERIC = new RegExp("^" + numeric + "$");
    var TEST_ALPHANUMERIC = new RegExp("^[A-Z0-9 $%*+\\-./:]+$");
    exports.testKanji = function testKanji(str) {
      return TEST_KANJI.test(str);
    };
    exports.testNumeric = function testNumeric(str) {
      return TEST_NUMERIC.test(str);
    };
    exports.testAlphanumeric = function testAlphanumeric(str) {
      return TEST_ALPHANUMERIC.test(str);
    };
  }
});

// node_modules/qrcode/lib/core/mode.js
var require_mode = __commonJS({
  "node_modules/qrcode/lib/core/mode.js"(exports) {
    var VersionCheck = require_version_check();
    var Regex = require_regex();
    exports.NUMERIC = {
      id: "Numeric",
      bit: 1 << 0,
      ccBits: [10, 12, 14]
    };
    exports.ALPHANUMERIC = {
      id: "Alphanumeric",
      bit: 1 << 1,
      ccBits: [9, 11, 13]
    };
    exports.BYTE = {
      id: "Byte",
      bit: 1 << 2,
      ccBits: [8, 16, 16]
    };
    exports.KANJI = {
      id: "Kanji",
      bit: 1 << 3,
      ccBits: [8, 10, 12]
    };
    exports.MIXED = {
      bit: -1
    };
    exports.getCharCountIndicator = function getCharCountIndicator(mode, version) {
      if (!mode.ccBits)
        throw new Error("Invalid mode: " + mode);
      if (!VersionCheck.isValid(version)) {
        throw new Error("Invalid version: " + version);
      }
      if (version >= 1 && version < 10)
        return mode.ccBits[0];
      else if (version < 27)
        return mode.ccBits[1];
      return mode.ccBits[2];
    };
    exports.getBestModeForData = function getBestModeForData(dataStr) {
      if (Regex.testNumeric(dataStr))
        return exports.NUMERIC;
      else if (Regex.testAlphanumeric(dataStr))
        return exports.ALPHANUMERIC;
      else if (Regex.testKanji(dataStr))
        return exports.KANJI;
      else
        return exports.BYTE;
    };
    exports.toString = function toString(mode) {
      if (mode && mode.id)
        return mode.id;
      throw new Error("Invalid mode");
    };
    exports.isValid = function isValid(mode) {
      return mode && mode.bit && mode.ccBits;
    };
    function fromString(string) {
      if (typeof string !== "string") {
        throw new Error("Param is not a string");
      }
      const lcStr = string.toLowerCase();
      switch (lcStr) {
        case "numeric":
          return exports.NUMERIC;
        case "alphanumeric":
          return exports.ALPHANUMERIC;
        case "kanji":
          return exports.KANJI;
        case "byte":
          return exports.BYTE;
        default:
          throw new Error("Unknown mode: " + string);
      }
    }
    exports.from = function from(value, defaultValue) {
      if (exports.isValid(value)) {
        return value;
      }
      try {
        return fromString(value);
      } catch (e) {
        return defaultValue;
      }
    };
  }
});

// node_modules/qrcode/lib/core/version.js
var require_version = __commonJS({
  "node_modules/qrcode/lib/core/version.js"(exports) {
    var Utils = require_utils();
    var ECCode = require_error_correction_code();
    var ECLevel = require_error_correction_level();
    var Mode = require_mode();
    var VersionCheck = require_version_check();
    var G18 = 1 << 12 | 1 << 11 | 1 << 10 | 1 << 9 | 1 << 8 | 1 << 5 | 1 << 2 | 1 << 0;
    var G18_BCH = Utils.getBCHDigit(G18);
    function getBestVersionForDataLength(mode, length, errorCorrectionLevel) {
      for (let currentVersion = 1; currentVersion <= 40; currentVersion++) {
        if (length <= exports.getCapacity(currentVersion, errorCorrectionLevel, mode)) {
          return currentVersion;
        }
      }
      return void 0;
    }
    function getReservedBitsCount(mode, version) {
      return Mode.getCharCountIndicator(mode, version) + 4;
    }
    function getTotalBitsFromDataArray(segments, version) {
      let totalBits = 0;
      segments.forEach(function(data) {
        const reservedBits = getReservedBitsCount(data.mode, version);
        totalBits += reservedBits + data.getBitsLength();
      });
      return totalBits;
    }
    function getBestVersionForMixedData(segments, errorCorrectionLevel) {
      for (let currentVersion = 1; currentVersion <= 40; currentVersion++) {
        const length = getTotalBitsFromDataArray(segments, currentVersion);
        if (length <= exports.getCapacity(currentVersion, errorCorrectionLevel, Mode.MIXED)) {
          return currentVersion;
        }
      }
      return void 0;
    }
    exports.from = function from(value, defaultValue) {
      if (VersionCheck.isValid(value)) {
        return parseInt(value, 10);
      }
      return defaultValue;
    };
    exports.getCapacity = function getCapacity(version, errorCorrectionLevel, mode) {
      if (!VersionCheck.isValid(version)) {
        throw new Error("Invalid QR Code version");
      }
      if (typeof mode === "undefined")
        mode = Mode.BYTE;
      const totalCodewords = Utils.getSymbolTotalCodewords(version);
      const ecTotalCodewords = ECCode.getTotalCodewordsCount(version, errorCorrectionLevel);
      const dataTotalCodewordsBits = (totalCodewords - ecTotalCodewords) * 8;
      if (mode === Mode.MIXED)
        return dataTotalCodewordsBits;
      const usableBits = dataTotalCodewordsBits - getReservedBitsCount(mode, version);
      switch (mode) {
        case Mode.NUMERIC:
          return Math.floor(usableBits / 10 * 3);
        case Mode.ALPHANUMERIC:
          return Math.floor(usableBits / 11 * 2);
        case Mode.KANJI:
          return Math.floor(usableBits / 13);
        case Mode.BYTE:
        default:
          return Math.floor(usableBits / 8);
      }
    };
    exports.getBestVersionForData = function getBestVersionForData(data, errorCorrectionLevel) {
      let seg;
      const ecl = ECLevel.from(errorCorrectionLevel, ECLevel.M);
      if (Array.isArray(data)) {
        if (data.length > 1) {
          return getBestVersionForMixedData(data, ecl);
        }
        if (data.length === 0) {
          return 1;
        }
        seg = data[0];
      } else {
        seg = data;
      }
      return getBestVersionForDataLength(seg.mode, seg.getLength(), ecl);
    };
    exports.getEncodedBits = function getEncodedBits(version) {
      if (!VersionCheck.isValid(version) || version < 7) {
        throw new Error("Invalid QR Code version");
      }
      let d = version << 12;
      while (Utils.getBCHDigit(d) - G18_BCH >= 0) {
        d ^= G18 << Utils.getBCHDigit(d) - G18_BCH;
      }
      return version << 12 | d;
    };
  }
});

// node_modules/qrcode/lib/core/format-info.js
var require_format_info = __commonJS({
  "node_modules/qrcode/lib/core/format-info.js"(exports) {
    var Utils = require_utils();
    var G15 = 1 << 10 | 1 << 8 | 1 << 5 | 1 << 4 | 1 << 2 | 1 << 1 | 1 << 0;
    var G15_MASK = 1 << 14 | 1 << 12 | 1 << 10 | 1 << 4 | 1 << 1;
    var G15_BCH = Utils.getBCHDigit(G15);
    exports.getEncodedBits = function getEncodedBits(errorCorrectionLevel, mask) {
      const data = errorCorrectionLevel.bit << 3 | mask;
      let d = data << 10;
      while (Utils.getBCHDigit(d) - G15_BCH >= 0) {
        d ^= G15 << Utils.getBCHDigit(d) - G15_BCH;
      }
      return (data << 10 | d) ^ G15_MASK;
    };
  }
});

// node_modules/qrcode/lib/core/numeric-data.js
var require_numeric_data = __commonJS({
  "node_modules/qrcode/lib/core/numeric-data.js"(exports, module) {
    var Mode = require_mode();
    function NumericData(data) {
      this.mode = Mode.NUMERIC;
      this.data = data.toString();
    }
    NumericData.getBitsLength = function getBitsLength(length) {
      return 10 * Math.floor(length / 3) + (length % 3 ? length % 3 * 3 + 1 : 0);
    };
    NumericData.prototype.getLength = function getLength() {
      return this.data.length;
    };
    NumericData.prototype.getBitsLength = function getBitsLength() {
      return NumericData.getBitsLength(this.data.length);
    };
    NumericData.prototype.write = function write(bitBuffer) {
      let i, group, value;
      for (i = 0; i + 3 <= this.data.length; i += 3) {
        group = this.data.substr(i, 3);
        value = parseInt(group, 10);
        bitBuffer.put(value, 10);
      }
      const remainingNum = this.data.length - i;
      if (remainingNum > 0) {
        group = this.data.substr(i);
        value = parseInt(group, 10);
        bitBuffer.put(value, remainingNum * 3 + 1);
      }
    };
    module.exports = NumericData;
  }
});

// node_modules/qrcode/lib/core/alphanumeric-data.js
var require_alphanumeric_data = __commonJS({
  "node_modules/qrcode/lib/core/alphanumeric-data.js"(exports, module) {
    var Mode = require_mode();
    var ALPHA_NUM_CHARS = [
      "0",
      "1",
      "2",
      "3",
      "4",
      "5",
      "6",
      "7",
      "8",
      "9",
      "A",
      "B",
      "C",
      "D",
      "E",
      "F",
      "G",
      "H",
      "I",
      "J",
      "K",
      "L",
      "M",
      "N",
      "O",
      "P",
      "Q",
      "R",
      "S",
      "T",
      "U",
      "V",
      "W",
      "X",
      "Y",
      "Z",
      " ",
      "$",
      "%",
      "*",
      "+",
      "-",
      ".",
      "/",
      ":"
    ];
    function AlphanumericData(data) {
      this.mode = Mode.ALPHANUMERIC;
      this.data = data;
    }
    AlphanumericData.getBitsLength = function getBitsLength(length) {
      return 11 * Math.floor(length / 2) + 6 * (length % 2);
    };
    AlphanumericData.prototype.getLength = function getLength() {
      return this.data.length;
    };
    AlphanumericData.prototype.getBitsLength = function getBitsLength() {
      return AlphanumericData.getBitsLength(this.data.length);
    };
    AlphanumericData.prototype.write = function write(bitBuffer) {
      let i;
      for (i = 0; i + 2 <= this.data.length; i += 2) {
        let value = ALPHA_NUM_CHARS.indexOf(this.data[i]) * 45;
        value += ALPHA_NUM_CHARS.indexOf(this.data[i + 1]);
        bitBuffer.put(value, 11);
      }
      if (this.data.length % 2) {
        bitBuffer.put(ALPHA_NUM_CHARS.indexOf(this.data[i]), 6);
      }
    };
    module.exports = AlphanumericData;
  }
});

// node_modules/qrcode/lib/core/byte-data.js
var require_byte_data = __commonJS({
  "node_modules/qrcode/lib/core/byte-data.js"(exports, module) {
    var Mode = require_mode();
    function ByteData(data) {
      this.mode = Mode.BYTE;
      if (typeof data === "string") {
        this.data = new TextEncoder().encode(data);
      } else {
        this.data = new Uint8Array(data);
      }
    }
    ByteData.getBitsLength = function getBitsLength(length) {
      return length * 8;
    };
    ByteData.prototype.getLength = function getLength() {
      return this.data.length;
    };
    ByteData.prototype.getBitsLength = function getBitsLength() {
      return ByteData.getBitsLength(this.data.length);
    };
    ByteData.prototype.write = function(bitBuffer) {
      for (let i = 0, l = this.data.length; i < l; i++) {
        bitBuffer.put(this.data[i], 8);
      }
    };
    module.exports = ByteData;
  }
});

// node_modules/qrcode/lib/core/kanji-data.js
var require_kanji_data = __commonJS({
  "node_modules/qrcode/lib/core/kanji-data.js"(exports, module) {
    var Mode = require_mode();
    var Utils = require_utils();
    function KanjiData(data) {
      this.mode = Mode.KANJI;
      this.data = data;
    }
    KanjiData.getBitsLength = function getBitsLength(length) {
      return length * 13;
    };
    KanjiData.prototype.getLength = function getLength() {
      return this.data.length;
    };
    KanjiData.prototype.getBitsLength = function getBitsLength() {
      return KanjiData.getBitsLength(this.data.length);
    };
    KanjiData.prototype.write = function(bitBuffer) {
      let i;
      for (i = 0; i < this.data.length; i++) {
        let value = Utils.toSJIS(this.data[i]);
        if (value >= 33088 && value <= 40956) {
          value -= 33088;
        } else if (value >= 57408 && value <= 60351) {
          value -= 49472;
        } else {
          throw new Error(
            "Invalid SJIS character: " + this.data[i] + "\nMake sure your charset is UTF-8"
          );
        }
        value = (value >>> 8 & 255) * 192 + (value & 255);
        bitBuffer.put(value, 13);
      }
    };
    module.exports = KanjiData;
  }
});

// node_modules/dijkstrajs/dijkstra.js
var require_dijkstra = __commonJS({
  "node_modules/dijkstrajs/dijkstra.js"(exports, module) {
    "use strict";
    var dijkstra = {
      single_source_shortest_paths: function(graph, s, d) {
        var predecessors = {};
        var costs = {};
        costs[s] = 0;
        var open = dijkstra.PriorityQueue.make();
        open.push(s, 0);
        var closest, u, v, cost_of_s_to_u, adjacent_nodes, cost_of_e, cost_of_s_to_u_plus_cost_of_e, cost_of_s_to_v, first_visit;
        while (!open.empty()) {
          closest = open.pop();
          u = closest.value;
          cost_of_s_to_u = closest.cost;
          adjacent_nodes = graph[u] || {};
          for (v in adjacent_nodes) {
            if (adjacent_nodes.hasOwnProperty(v)) {
              cost_of_e = adjacent_nodes[v];
              cost_of_s_to_u_plus_cost_of_e = cost_of_s_to_u + cost_of_e;
              cost_of_s_to_v = costs[v];
              first_visit = typeof costs[v] === "undefined";
              if (first_visit || cost_of_s_to_v > cost_of_s_to_u_plus_cost_of_e) {
                costs[v] = cost_of_s_to_u_plus_cost_of_e;
                open.push(v, cost_of_s_to_u_plus_cost_of_e);
                predecessors[v] = u;
              }
            }
          }
        }
        if (typeof d !== "undefined" && typeof costs[d] === "undefined") {
          var msg = ["Could not find a path from ", s, " to ", d, "."].join("");
          throw new Error(msg);
        }
        return predecessors;
      },
      extract_shortest_path_from_predecessor_list: function(predecessors, d) {
        var nodes = [];
        var u = d;
        var predecessor;
        while (u) {
          nodes.push(u);
          predecessor = predecessors[u];
          u = predecessors[u];
        }
        nodes.reverse();
        return nodes;
      },
      find_path: function(graph, s, d) {
        var predecessors = dijkstra.single_source_shortest_paths(graph, s, d);
        return dijkstra.extract_shortest_path_from_predecessor_list(
          predecessors,
          d
        );
      },
      /**
       * A very naive priority queue implementation.
       */
      PriorityQueue: {
        make: function(opts) {
          var T = dijkstra.PriorityQueue, t = {}, key;
          opts = opts || {};
          for (key in T) {
            if (T.hasOwnProperty(key)) {
              t[key] = T[key];
            }
          }
          t.queue = [];
          t.sorter = opts.sorter || T.default_sorter;
          return t;
        },
        default_sorter: function(a, b) {
          return a.cost - b.cost;
        },
        /**
         * Add a new item to the queue and ensure the highest priority element
         * is at the front of the queue.
         */
        push: function(value, cost) {
          var item = { value, cost };
          this.queue.push(item);
          this.queue.sort(this.sorter);
        },
        /**
         * Return the highest priority element in the queue.
         */
        pop: function() {
          return this.queue.shift();
        },
        empty: function() {
          return this.queue.length === 0;
        }
      }
    };
    if (typeof module !== "undefined") {
      module.exports = dijkstra;
    }
  }
});

// node_modules/qrcode/lib/core/segments.js
var require_segments = __commonJS({
  "node_modules/qrcode/lib/core/segments.js"(exports) {
    var Mode = require_mode();
    var NumericData = require_numeric_data();
    var AlphanumericData = require_alphanumeric_data();
    var ByteData = require_byte_data();
    var KanjiData = require_kanji_data();
    var Regex = require_regex();
    var Utils = require_utils();
    var dijkstra = require_dijkstra();
    function getStringByteLength(str) {
      return unescape(encodeURIComponent(str)).length;
    }
    function getSegments(regex, mode, str) {
      const segments = [];
      let result;
      while ((result = regex.exec(str)) !== null) {
        segments.push({
          data: result[0],
          index: result.index,
          mode,
          length: result[0].length
        });
      }
      return segments;
    }
    function getSegmentsFromString(dataStr) {
      const numSegs = getSegments(Regex.NUMERIC, Mode.NUMERIC, dataStr);
      const alphaNumSegs = getSegments(Regex.ALPHANUMERIC, Mode.ALPHANUMERIC, dataStr);
      let byteSegs;
      let kanjiSegs;
      if (Utils.isKanjiModeEnabled()) {
        byteSegs = getSegments(Regex.BYTE, Mode.BYTE, dataStr);
        kanjiSegs = getSegments(Regex.KANJI, Mode.KANJI, dataStr);
      } else {
        byteSegs = getSegments(Regex.BYTE_KANJI, Mode.BYTE, dataStr);
        kanjiSegs = [];
      }
      const segs = numSegs.concat(alphaNumSegs, byteSegs, kanjiSegs);
      return segs.sort(function(s1, s2) {
        return s1.index - s2.index;
      }).map(function(obj) {
        return {
          data: obj.data,
          mode: obj.mode,
          length: obj.length
        };
      });
    }
    function getSegmentBitsLength(length, mode) {
      switch (mode) {
        case Mode.NUMERIC:
          return NumericData.getBitsLength(length);
        case Mode.ALPHANUMERIC:
          return AlphanumericData.getBitsLength(length);
        case Mode.KANJI:
          return KanjiData.getBitsLength(length);
        case Mode.BYTE:
          return ByteData.getBitsLength(length);
      }
    }
    function mergeSegments(segs) {
      return segs.reduce(function(acc, curr) {
        const prevSeg = acc.length - 1 >= 0 ? acc[acc.length - 1] : null;
        if (prevSeg && prevSeg.mode === curr.mode) {
          acc[acc.length - 1].data += curr.data;
          return acc;
        }
        acc.push(curr);
        return acc;
      }, []);
    }
    function buildNodes(segs) {
      const nodes = [];
      for (let i = 0; i < segs.length; i++) {
        const seg = segs[i];
        switch (seg.mode) {
          case Mode.NUMERIC:
            nodes.push([
              seg,
              { data: seg.data, mode: Mode.ALPHANUMERIC, length: seg.length },
              { data: seg.data, mode: Mode.BYTE, length: seg.length }
            ]);
            break;
          case Mode.ALPHANUMERIC:
            nodes.push([
              seg,
              { data: seg.data, mode: Mode.BYTE, length: seg.length }
            ]);
            break;
          case Mode.KANJI:
            nodes.push([
              seg,
              { data: seg.data, mode: Mode.BYTE, length: getStringByteLength(seg.data) }
            ]);
            break;
          case Mode.BYTE:
            nodes.push([
              { data: seg.data, mode: Mode.BYTE, length: getStringByteLength(seg.data) }
            ]);
        }
      }
      return nodes;
    }
    function buildGraph(nodes, version) {
      const table = {};
      const graph = { start: {} };
      let prevNodeIds = ["start"];
      for (let i = 0; i < nodes.length; i++) {
        const nodeGroup = nodes[i];
        const currentNodeIds = [];
        for (let j = 0; j < nodeGroup.length; j++) {
          const node = nodeGroup[j];
          const key = "" + i + j;
          currentNodeIds.push(key);
          table[key] = { node, lastCount: 0 };
          graph[key] = {};
          for (let n = 0; n < prevNodeIds.length; n++) {
            const prevNodeId = prevNodeIds[n];
            if (table[prevNodeId] && table[prevNodeId].node.mode === node.mode) {
              graph[prevNodeId][key] = getSegmentBitsLength(table[prevNodeId].lastCount + node.length, node.mode) - getSegmentBitsLength(table[prevNodeId].lastCount, node.mode);
              table[prevNodeId].lastCount += node.length;
            } else {
              if (table[prevNodeId])
                table[prevNodeId].lastCount = node.length;
              graph[prevNodeId][key] = getSegmentBitsLength(node.length, node.mode) + 4 + Mode.getCharCountIndicator(node.mode, version);
            }
          }
        }
        prevNodeIds = currentNodeIds;
      }
      for (let n = 0; n < prevNodeIds.length; n++) {
        graph[prevNodeIds[n]].end = 0;
      }
      return { map: graph, table };
    }
    function buildSingleSegment(data, modesHint) {
      let mode;
      const bestMode = Mode.getBestModeForData(data);
      mode = Mode.from(modesHint, bestMode);
      if (mode !== Mode.BYTE && mode.bit < bestMode.bit) {
        throw new Error('"' + data + '" cannot be encoded with mode ' + Mode.toString(mode) + ".\n Suggested mode is: " + Mode.toString(bestMode));
      }
      if (mode === Mode.KANJI && !Utils.isKanjiModeEnabled()) {
        mode = Mode.BYTE;
      }
      switch (mode) {
        case Mode.NUMERIC:
          return new NumericData(data);
        case Mode.ALPHANUMERIC:
          return new AlphanumericData(data);
        case Mode.KANJI:
          return new KanjiData(data);
        case Mode.BYTE:
          return new ByteData(data);
      }
    }
    exports.fromArray = function fromArray(array) {
      return array.reduce(function(acc, seg) {
        if (typeof seg === "string") {
          acc.push(buildSingleSegment(seg, null));
        } else if (seg.data) {
          acc.push(buildSingleSegment(seg.data, seg.mode));
        }
        return acc;
      }, []);
    };
    exports.fromString = function fromString(data, version) {
      const segs = getSegmentsFromString(data, Utils.isKanjiModeEnabled());
      const nodes = buildNodes(segs);
      const graph = buildGraph(nodes, version);
      const path = dijkstra.find_path(graph.map, "start", "end");
      const optimizedSegs = [];
      for (let i = 1; i < path.length - 1; i++) {
        optimizedSegs.push(graph.table[path[i]].node);
      }
      return exports.fromArray(mergeSegments(optimizedSegs));
    };
    exports.rawSplit = function rawSplit(data) {
      return exports.fromArray(
        getSegmentsFromString(data, Utils.isKanjiModeEnabled())
      );
    };
  }
});

// node_modules/qrcode/lib/core/qrcode.js
var require_qrcode = __commonJS({
  "node_modules/qrcode/lib/core/qrcode.js"(exports) {
    var Utils = require_utils();
    var ECLevel = require_error_correction_level();
    var BitBuffer = require_bit_buffer();
    var BitMatrix = require_bit_matrix();
    var AlignmentPattern = require_alignment_pattern();
    var FinderPattern = require_finder_pattern();
    var MaskPattern = require_mask_pattern();
    var ECCode = require_error_correction_code();
    var ReedSolomonEncoder = require_reed_solomon_encoder();
    var Version = require_version();
    var FormatInfo = require_format_info();
    var Mode = require_mode();
    var Segments = require_segments();
    function setupFinderPattern(matrix, version) {
      const size = matrix.size;
      const pos = FinderPattern.getPositions(version);
      for (let i = 0; i < pos.length; i++) {
        const row = pos[i][0];
        const col = pos[i][1];
        for (let r = -1; r <= 7; r++) {
          if (row + r <= -1 || size <= row + r)
            continue;
          for (let c = -1; c <= 7; c++) {
            if (col + c <= -1 || size <= col + c)
              continue;
            if (r >= 0 && r <= 6 && (c === 0 || c === 6) || c >= 0 && c <= 6 && (r === 0 || r === 6) || r >= 2 && r <= 4 && c >= 2 && c <= 4) {
              matrix.set(row + r, col + c, true, true);
            } else {
              matrix.set(row + r, col + c, false, true);
            }
          }
        }
      }
    }
    function setupTimingPattern(matrix) {
      const size = matrix.size;
      for (let r = 8; r < size - 8; r++) {
        const value = r % 2 === 0;
        matrix.set(r, 6, value, true);
        matrix.set(6, r, value, true);
      }
    }
    function setupAlignmentPattern(matrix, version) {
      const pos = AlignmentPattern.getPositions(version);
      for (let i = 0; i < pos.length; i++) {
        const row = pos[i][0];
        const col = pos[i][1];
        for (let r = -2; r <= 2; r++) {
          for (let c = -2; c <= 2; c++) {
            if (r === -2 || r === 2 || c === -2 || c === 2 || r === 0 && c === 0) {
              matrix.set(row + r, col + c, true, true);
            } else {
              matrix.set(row + r, col + c, false, true);
            }
          }
        }
      }
    }
    function setupVersionInfo(matrix, version) {
      const size = matrix.size;
      const bits = Version.getEncodedBits(version);
      let row, col, mod;
      for (let i = 0; i < 18; i++) {
        row = Math.floor(i / 3);
        col = i % 3 + size - 8 - 3;
        mod = (bits >> i & 1) === 1;
        matrix.set(row, col, mod, true);
        matrix.set(col, row, mod, true);
      }
    }
    function setupFormatInfo(matrix, errorCorrectionLevel, maskPattern) {
      const size = matrix.size;
      const bits = FormatInfo.getEncodedBits(errorCorrectionLevel, maskPattern);
      let i, mod;
      for (i = 0; i < 15; i++) {
        mod = (bits >> i & 1) === 1;
        if (i < 6) {
          matrix.set(i, 8, mod, true);
        } else if (i < 8) {
          matrix.set(i + 1, 8, mod, true);
        } else {
          matrix.set(size - 15 + i, 8, mod, true);
        }
        if (i < 8) {
          matrix.set(8, size - i - 1, mod, true);
        } else if (i < 9) {
          matrix.set(8, 15 - i - 1 + 1, mod, true);
        } else {
          matrix.set(8, 15 - i - 1, mod, true);
        }
      }
      matrix.set(size - 8, 8, 1, true);
    }
    function setupData(matrix, data) {
      const size = matrix.size;
      let inc = -1;
      let row = size - 1;
      let bitIndex = 7;
      let byteIndex = 0;
      for (let col = size - 1; col > 0; col -= 2) {
        if (col === 6)
          col--;
        while (true) {
          for (let c = 0; c < 2; c++) {
            if (!matrix.isReserved(row, col - c)) {
              let dark = false;
              if (byteIndex < data.length) {
                dark = (data[byteIndex] >>> bitIndex & 1) === 1;
              }
              matrix.set(row, col - c, dark);
              bitIndex--;
              if (bitIndex === -1) {
                byteIndex++;
                bitIndex = 7;
              }
            }
          }
          row += inc;
          if (row < 0 || size <= row) {
            row -= inc;
            inc = -inc;
            break;
          }
        }
      }
    }
    function createData(version, errorCorrectionLevel, segments) {
      const buffer = new BitBuffer();
      segments.forEach(function(data) {
        buffer.put(data.mode.bit, 4);
        buffer.put(data.getLength(), Mode.getCharCountIndicator(data.mode, version));
        data.write(buffer);
      });
      const totalCodewords = Utils.getSymbolTotalCodewords(version);
      const ecTotalCodewords = ECCode.getTotalCodewordsCount(version, errorCorrectionLevel);
      const dataTotalCodewordsBits = (totalCodewords - ecTotalCodewords) * 8;
      if (buffer.getLengthInBits() + 4 <= dataTotalCodewordsBits) {
        buffer.put(0, 4);
      }
      while (buffer.getLengthInBits() % 8 !== 0) {
        buffer.putBit(0);
      }
      const remainingByte = (dataTotalCodewordsBits - buffer.getLengthInBits()) / 8;
      for (let i = 0; i < remainingByte; i++) {
        buffer.put(i % 2 ? 17 : 236, 8);
      }
      return createCodewords(buffer, version, errorCorrectionLevel);
    }
    function createCodewords(bitBuffer, version, errorCorrectionLevel) {
      const totalCodewords = Utils.getSymbolTotalCodewords(version);
      const ecTotalCodewords = ECCode.getTotalCodewordsCount(version, errorCorrectionLevel);
      const dataTotalCodewords = totalCodewords - ecTotalCodewords;
      const ecTotalBlocks = ECCode.getBlocksCount(version, errorCorrectionLevel);
      const blocksInGroup2 = totalCodewords % ecTotalBlocks;
      const blocksInGroup1 = ecTotalBlocks - blocksInGroup2;
      const totalCodewordsInGroup1 = Math.floor(totalCodewords / ecTotalBlocks);
      const dataCodewordsInGroup1 = Math.floor(dataTotalCodewords / ecTotalBlocks);
      const dataCodewordsInGroup2 = dataCodewordsInGroup1 + 1;
      const ecCount = totalCodewordsInGroup1 - dataCodewordsInGroup1;
      const rs = new ReedSolomonEncoder(ecCount);
      let offset = 0;
      const dcData = new Array(ecTotalBlocks);
      const ecData = new Array(ecTotalBlocks);
      let maxDataSize = 0;
      const buffer = new Uint8Array(bitBuffer.buffer);
      for (let b = 0; b < ecTotalBlocks; b++) {
        const dataSize = b < blocksInGroup1 ? dataCodewordsInGroup1 : dataCodewordsInGroup2;
        dcData[b] = buffer.slice(offset, offset + dataSize);
        ecData[b] = rs.encode(dcData[b]);
        offset += dataSize;
        maxDataSize = Math.max(maxDataSize, dataSize);
      }
      const data = new Uint8Array(totalCodewords);
      let index = 0;
      let i, r;
      for (i = 0; i < maxDataSize; i++) {
        for (r = 0; r < ecTotalBlocks; r++) {
          if (i < dcData[r].length) {
            data[index++] = dcData[r][i];
          }
        }
      }
      for (i = 0; i < ecCount; i++) {
        for (r = 0; r < ecTotalBlocks; r++) {
          data[index++] = ecData[r][i];
        }
      }
      return data;
    }
    function createSymbol(data, version, errorCorrectionLevel, maskPattern) {
      let segments;
      if (Array.isArray(data)) {
        segments = Segments.fromArray(data);
      } else if (typeof data === "string") {
        let estimatedVersion = version;
        if (!estimatedVersion) {
          const rawSegments = Segments.rawSplit(data);
          estimatedVersion = Version.getBestVersionForData(rawSegments, errorCorrectionLevel);
        }
        segments = Segments.fromString(data, estimatedVersion || 40);
      } else {
        throw new Error("Invalid data");
      }
      const bestVersion = Version.getBestVersionForData(segments, errorCorrectionLevel);
      if (!bestVersion) {
        throw new Error("The amount of data is too big to be stored in a QR Code");
      }
      if (!version) {
        version = bestVersion;
      } else if (version < bestVersion) {
        throw new Error(
          "\nThe chosen QR Code version cannot contain this amount of data.\nMinimum version required to store current data is: " + bestVersion + ".\n"
        );
      }
      const dataBits = createData(version, errorCorrectionLevel, segments);
      const moduleCount = Utils.getSymbolSize(version);
      const modules = new BitMatrix(moduleCount);
      setupFinderPattern(modules, version);
      setupTimingPattern(modules);
      setupAlignmentPattern(modules, version);
      setupFormatInfo(modules, errorCorrectionLevel, 0);
      if (version >= 7) {
        setupVersionInfo(modules, version);
      }
      setupData(modules, dataBits);
      if (isNaN(maskPattern)) {
        maskPattern = MaskPattern.getBestMask(
          modules,
          setupFormatInfo.bind(null, modules, errorCorrectionLevel)
        );
      }
      MaskPattern.applyMask(maskPattern, modules);
      setupFormatInfo(modules, errorCorrectionLevel, maskPattern);
      return {
        modules,
        version,
        errorCorrectionLevel,
        maskPattern,
        segments
      };
    }
    exports.create = function create(data, options) {
      if (typeof data === "undefined" || data === "") {
        throw new Error("No input text");
      }
      let errorCorrectionLevel = ECLevel.M;
      let version;
      let mask;
      if (typeof options !== "undefined") {
        errorCorrectionLevel = ECLevel.from(options.errorCorrectionLevel, ECLevel.M);
        version = Version.from(options.version);
        mask = MaskPattern.from(options.maskPattern);
        if (options.toSJISFunc) {
          Utils.setToSJISFunction(options.toSJISFunc);
        }
      }
      return createSymbol(data, version, errorCorrectionLevel, mask);
    };
  }
});

// node_modules/qrcode/lib/renderer/utils.js
var require_utils2 = __commonJS({
  "node_modules/qrcode/lib/renderer/utils.js"(exports) {
    function hex2rgba(hex) {
      if (typeof hex === "number") {
        hex = hex.toString();
      }
      if (typeof hex !== "string") {
        throw new Error("Color should be defined as hex string");
      }
      let hexCode = hex.slice().replace("#", "").split("");
      if (hexCode.length < 3 || hexCode.length === 5 || hexCode.length > 8) {
        throw new Error("Invalid hex color: " + hex);
      }
      if (hexCode.length === 3 || hexCode.length === 4) {
        hexCode = Array.prototype.concat.apply([], hexCode.map(function(c) {
          return [c, c];
        }));
      }
      if (hexCode.length === 6)
        hexCode.push("F", "F");
      const hexValue = parseInt(hexCode.join(""), 16);
      return {
        r: hexValue >> 24 & 255,
        g: hexValue >> 16 & 255,
        b: hexValue >> 8 & 255,
        a: hexValue & 255,
        hex: "#" + hexCode.slice(0, 6).join("")
      };
    }
    exports.getOptions = function getOptions(options) {
      if (!options)
        options = {};
      if (!options.color)
        options.color = {};
      const margin = typeof options.margin === "undefined" || options.margin === null || options.margin < 0 ? 4 : options.margin;
      const width = options.width && options.width >= 21 ? options.width : void 0;
      const scale = options.scale || 4;
      return {
        width,
        scale: width ? 4 : scale,
        margin,
        color: {
          dark: hex2rgba(options.color.dark || "#000000ff"),
          light: hex2rgba(options.color.light || "#ffffffff")
        },
        type: options.type,
        rendererOpts: options.rendererOpts || {}
      };
    };
    exports.getScale = function getScale(qrSize, opts) {
      return opts.width && opts.width >= qrSize + opts.margin * 2 ? opts.width / (qrSize + opts.margin * 2) : opts.scale;
    };
    exports.getImageWidth = function getImageWidth(qrSize, opts) {
      const scale = exports.getScale(qrSize, opts);
      return Math.floor((qrSize + opts.margin * 2) * scale);
    };
    exports.qrToImageData = function qrToImageData(imgData, qr, opts) {
      const size = qr.modules.size;
      const data = qr.modules.data;
      const scale = exports.getScale(size, opts);
      const symbolSize = Math.floor((size + opts.margin * 2) * scale);
      const scaledMargin = opts.margin * scale;
      const palette = [opts.color.light, opts.color.dark];
      for (let i = 0; i < symbolSize; i++) {
        for (let j = 0; j < symbolSize; j++) {
          let posDst = (i * symbolSize + j) * 4;
          let pxColor = opts.color.light;
          if (i >= scaledMargin && j >= scaledMargin && i < symbolSize - scaledMargin && j < symbolSize - scaledMargin) {
            const iSrc = Math.floor((i - scaledMargin) / scale);
            const jSrc = Math.floor((j - scaledMargin) / scale);
            pxColor = palette[data[iSrc * size + jSrc] ? 1 : 0];
          }
          imgData[posDst++] = pxColor.r;
          imgData[posDst++] = pxColor.g;
          imgData[posDst++] = pxColor.b;
          imgData[posDst] = pxColor.a;
        }
      }
    };
  }
});

// node_modules/qrcode/lib/renderer/canvas.js
var require_canvas = __commonJS({
  "node_modules/qrcode/lib/renderer/canvas.js"(exports) {
    var Utils = require_utils2();
    function clearCanvas(ctx, canvas, size) {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      if (!canvas.style)
        canvas.style = {};
      canvas.height = size;
      canvas.width = size;
      canvas.style.height = size + "px";
      canvas.style.width = size + "px";
    }
    function getCanvasElement() {
      try {
        return document.createElement("canvas");
      } catch (e) {
        throw new Error("You need to specify a canvas element");
      }
    }
    exports.render = function render(qrData, canvas, options) {
      let opts = options;
      let canvasEl = canvas;
      if (typeof opts === "undefined" && (!canvas || !canvas.getContext)) {
        opts = canvas;
        canvas = void 0;
      }
      if (!canvas) {
        canvasEl = getCanvasElement();
      }
      opts = Utils.getOptions(opts);
      const size = Utils.getImageWidth(qrData.modules.size, opts);
      const ctx = canvasEl.getContext("2d");
      const image = ctx.createImageData(size, size);
      Utils.qrToImageData(image.data, qrData, opts);
      clearCanvas(ctx, canvasEl, size);
      ctx.putImageData(image, 0, 0);
      return canvasEl;
    };
    exports.renderToDataURL = function renderToDataURL(qrData, canvas, options) {
      let opts = options;
      if (typeof opts === "undefined" && (!canvas || !canvas.getContext)) {
        opts = canvas;
        canvas = void 0;
      }
      if (!opts)
        opts = {};
      const canvasEl = exports.render(qrData, canvas, opts);
      const type = opts.type || "image/png";
      const rendererOpts = opts.rendererOpts || {};
      return canvasEl.toDataURL(type, rendererOpts.quality);
    };
  }
});

// node_modules/qrcode/lib/renderer/svg-tag.js
var require_svg_tag = __commonJS({
  "node_modules/qrcode/lib/renderer/svg-tag.js"(exports) {
    var Utils = require_utils2();
    function getColorAttrib(color, attrib) {
      const alpha = color.a / 255;
      const str = attrib + '="' + color.hex + '"';
      return alpha < 1 ? str + " " + attrib + '-opacity="' + alpha.toFixed(2).slice(1) + '"' : str;
    }
    function svgCmd(cmd, x, y) {
      let str = cmd + x;
      if (typeof y !== "undefined")
        str += " " + y;
      return str;
    }
    function qrToPath(data, size, margin) {
      let path = "";
      let moveBy = 0;
      let newRow = false;
      let lineLength = 0;
      for (let i = 0; i < data.length; i++) {
        const col = Math.floor(i % size);
        const row = Math.floor(i / size);
        if (!col && !newRow)
          newRow = true;
        if (data[i]) {
          lineLength++;
          if (!(i > 0 && col > 0 && data[i - 1])) {
            path += newRow ? svgCmd("M", col + margin, 0.5 + row + margin) : svgCmd("m", moveBy, 0);
            moveBy = 0;
            newRow = false;
          }
          if (!(col + 1 < size && data[i + 1])) {
            path += svgCmd("h", lineLength);
            lineLength = 0;
          }
        } else {
          moveBy++;
        }
      }
      return path;
    }
    exports.render = function render(qrData, options, cb) {
      const opts = Utils.getOptions(options);
      const size = qrData.modules.size;
      const data = qrData.modules.data;
      const qrcodesize = size + opts.margin * 2;
      const bg = !opts.color.light.a ? "" : "<path " + getColorAttrib(opts.color.light, "fill") + ' d="M0 0h' + qrcodesize + "v" + qrcodesize + 'H0z"/>';
      const path = "<path " + getColorAttrib(opts.color.dark, "stroke") + ' d="' + qrToPath(data, size, opts.margin) + '"/>';
      const viewBox = 'viewBox="0 0 ' + qrcodesize + " " + qrcodesize + '"';
      const width = !opts.width ? "" : 'width="' + opts.width + '" height="' + opts.width + '" ';
      const svgTag = '<svg xmlns="http://www.w3.org/2000/svg" ' + width + viewBox + ' shape-rendering="crispEdges">' + bg + path + "</svg>\n";
      if (typeof cb === "function") {
        cb(null, svgTag);
      }
      return svgTag;
    };
  }
});

// node_modules/qrcode/lib/browser.js
var require_browser = __commonJS({
  "node_modules/qrcode/lib/browser.js"(exports) {
    var canPromise = require_can_promise();
    var QRCode = require_qrcode();
    var CanvasRenderer = require_canvas();
    var SvgRenderer = require_svg_tag();
    function renderCanvas(renderFunc, canvas, text, opts, cb) {
      const args = [].slice.call(arguments, 1);
      const argsNum = args.length;
      const isLastArgCb = typeof args[argsNum - 1] === "function";
      if (!isLastArgCb && !canPromise()) {
        throw new Error("Callback required as last argument");
      }
      if (isLastArgCb) {
        if (argsNum < 2) {
          throw new Error("Too few arguments provided");
        }
        if (argsNum === 2) {
          cb = text;
          text = canvas;
          canvas = opts = void 0;
        } else if (argsNum === 3) {
          if (canvas.getContext && typeof cb === "undefined") {
            cb = opts;
            opts = void 0;
          } else {
            cb = opts;
            opts = text;
            text = canvas;
            canvas = void 0;
          }
        }
      } else {
        if (argsNum < 1) {
          throw new Error("Too few arguments provided");
        }
        if (argsNum === 1) {
          text = canvas;
          canvas = opts = void 0;
        } else if (argsNum === 2 && !canvas.getContext) {
          opts = text;
          text = canvas;
          canvas = void 0;
        }
        return new Promise(function(resolve, reject) {
          try {
            const data = QRCode.create(text, opts);
            resolve(renderFunc(data, canvas, opts));
          } catch (e) {
            reject(e);
          }
        });
      }
      try {
        const data = QRCode.create(text, opts);
        cb(null, renderFunc(data, canvas, opts));
      } catch (e) {
        cb(e);
      }
    }
    exports.create = QRCode.create;
    exports.toCanvas = renderCanvas.bind(null, CanvasRenderer.render);
    exports.toDataURL = renderCanvas.bind(null, CanvasRenderer.renderToDataURL);
    exports.toString = renderCanvas.bind(null, function(data, _, opts) {
      return SvgRenderer.render(data, opts);
    });
  }
});

// node_modules/@solana/wallet-standard-chains/lib/cjs/index.js
var require_cjs4 = __commonJS({
  "node_modules/@solana/wallet-standard-chains/lib/cjs/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.SOLANA_CHAINS = exports.SOLANA_LOCALNET_CHAIN = exports.SOLANA_TESTNET_CHAIN = exports.SOLANA_DEVNET_CHAIN = exports.SOLANA_MAINNET_CHAIN = void 0;
    exports.isSolanaChain = isSolanaChain2;
    exports.SOLANA_MAINNET_CHAIN = "solana:mainnet";
    exports.SOLANA_DEVNET_CHAIN = "solana:devnet";
    exports.SOLANA_TESTNET_CHAIN = "solana:testnet";
    exports.SOLANA_LOCALNET_CHAIN = "solana:localnet";
    exports.SOLANA_CHAINS = [
      exports.SOLANA_MAINNET_CHAIN,
      exports.SOLANA_DEVNET_CHAIN,
      exports.SOLANA_TESTNET_CHAIN,
      exports.SOLANA_LOCALNET_CHAIN
    ];
    function isSolanaChain2(chain) {
      return exports.SOLANA_CHAINS.includes(chain);
    }
  }
});

// node_modules/@solana/wallet-standard-util/lib/cjs/commitment.js
var require_commitment = __commonJS({
  "node_modules/@solana/wallet-standard-util/lib/cjs/commitment.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.getCommitment = getCommitment2;
    function getCommitment2(commitment) {
      switch (commitment) {
        case "processed":
        case "confirmed":
        case "finalized":
        case void 0:
          return commitment;
        case "recent":
          return "processed";
        case "single":
        case "singleGossip":
          return "confirmed";
        case "max":
        case "root":
          return "finalized";
        default:
          return void 0;
      }
    }
  }
});

// node_modules/@solana/wallet-standard-util/lib/cjs/endpoint.js
var require_endpoint = __commonJS({
  "node_modules/@solana/wallet-standard-util/lib/cjs/endpoint.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.LOCALNET_ENDPOINT = exports.TESTNET_ENDPOINT = exports.DEVNET_ENDPOINT = exports.MAINNET_ENDPOINT = void 0;
    exports.getChainForEndpoint = getChainForEndpoint2;
    exports.getEndpointForChain = getEndpointForChain2;
    var wallet_standard_chains_1 = require_cjs4();
    exports.MAINNET_ENDPOINT = "https://api.mainnet-beta.solana.com";
    exports.DEVNET_ENDPOINT = "https://api.devnet.solana.com";
    exports.TESTNET_ENDPOINT = "https://api.testnet.solana.com";
    exports.LOCALNET_ENDPOINT = "http://localhost:8899";
    function getChainForEndpoint2(endpoint) {
      if (endpoint.includes(exports.MAINNET_ENDPOINT))
        return wallet_standard_chains_1.SOLANA_MAINNET_CHAIN;
      if (/\bdevnet\b/i.test(endpoint))
        return wallet_standard_chains_1.SOLANA_DEVNET_CHAIN;
      if (/\btestnet\b/i.test(endpoint))
        return wallet_standard_chains_1.SOLANA_TESTNET_CHAIN;
      if (/\blocalhost\b/i.test(endpoint) || /\b127\.0\.0\.1\b/.test(endpoint))
        return wallet_standard_chains_1.SOLANA_LOCALNET_CHAIN;
      return wallet_standard_chains_1.SOLANA_MAINNET_CHAIN;
    }
    function getEndpointForChain2(chain, endpoint) {
      if (endpoint)
        return endpoint;
      if (chain === wallet_standard_chains_1.SOLANA_MAINNET_CHAIN)
        return exports.MAINNET_ENDPOINT;
      if (chain === wallet_standard_chains_1.SOLANA_DEVNET_CHAIN)
        return exports.DEVNET_ENDPOINT;
      if (chain === wallet_standard_chains_1.SOLANA_TESTNET_CHAIN)
        return exports.TESTNET_ENDPOINT;
      if (chain === wallet_standard_chains_1.SOLANA_LOCALNET_CHAIN)
        return exports.LOCALNET_ENDPOINT;
      return exports.MAINNET_ENDPOINT;
    }
  }
});

// node_modules/@noble/hashes/crypto.js
var require_crypto = __commonJS({
  "node_modules/@noble/hashes/crypto.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.crypto = void 0;
    exports.crypto = typeof globalThis === "object" && "crypto" in globalThis ? globalThis.crypto : void 0;
  }
});

// node_modules/@noble/hashes/utils.js
var require_utils3 = __commonJS({
  "node_modules/@noble/hashes/utils.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.wrapXOFConstructorWithOpts = exports.wrapConstructorWithOpts = exports.wrapConstructor = exports.Hash = exports.nextTick = exports.swap32IfBE = exports.byteSwapIfBE = exports.swap8IfBE = exports.isLE = void 0;
    exports.isBytes = isBytes;
    exports.anumber = anumber;
    exports.abytes = abytes;
    exports.ahash = ahash;
    exports.aexists = aexists;
    exports.aoutput = aoutput;
    exports.u8 = u8;
    exports.u32 = u32;
    exports.clean = clean;
    exports.createView = createView;
    exports.rotr = rotr;
    exports.rotl = rotl;
    exports.byteSwap = byteSwap;
    exports.byteSwap32 = byteSwap32;
    exports.bytesToHex = bytesToHex;
    exports.hexToBytes = hexToBytes;
    exports.asyncLoop = asyncLoop;
    exports.utf8ToBytes = utf8ToBytes;
    exports.bytesToUtf8 = bytesToUtf8;
    exports.toBytes = toBytes;
    exports.kdfInputToBytes = kdfInputToBytes;
    exports.concatBytes = concatBytes;
    exports.checkOpts = checkOpts;
    exports.createHasher = createHasher;
    exports.createOptHasher = createOptHasher;
    exports.createXOFer = createXOFer;
    exports.randomBytes = randomBytes;
    var crypto_1 = require_crypto();
    function isBytes(a) {
      return a instanceof Uint8Array || ArrayBuffer.isView(a) && a.constructor.name === "Uint8Array";
    }
    function anumber(n) {
      if (!Number.isSafeInteger(n) || n < 0)
        throw new Error("positive integer expected, got " + n);
    }
    function abytes(b, ...lengths) {
      if (!isBytes(b))
        throw new Error("Uint8Array expected");
      if (lengths.length > 0 && !lengths.includes(b.length))
        throw new Error("Uint8Array expected of length " + lengths + ", got length=" + b.length);
    }
    function ahash(h) {
      if (typeof h !== "function" || typeof h.create !== "function")
        throw new Error("Hash should be wrapped by utils.createHasher");
      anumber(h.outputLen);
      anumber(h.blockLen);
    }
    function aexists(instance, checkFinished = true) {
      if (instance.destroyed)
        throw new Error("Hash instance has been destroyed");
      if (checkFinished && instance.finished)
        throw new Error("Hash#digest() has already been called");
    }
    function aoutput(out, instance) {
      abytes(out);
      const min = instance.outputLen;
      if (out.length < min) {
        throw new Error("digestInto() expects output buffer of length at least " + min);
      }
    }
    function u8(arr) {
      return new Uint8Array(arr.buffer, arr.byteOffset, arr.byteLength);
    }
    function u32(arr) {
      return new Uint32Array(arr.buffer, arr.byteOffset, Math.floor(arr.byteLength / 4));
    }
    function clean(...arrays) {
      for (let i = 0; i < arrays.length; i++) {
        arrays[i].fill(0);
      }
    }
    function createView(arr) {
      return new DataView(arr.buffer, arr.byteOffset, arr.byteLength);
    }
    function rotr(word, shift) {
      return word << 32 - shift | word >>> shift;
    }
    function rotl(word, shift) {
      return word << shift | word >>> 32 - shift >>> 0;
    }
    exports.isLE = (() => new Uint8Array(new Uint32Array([287454020]).buffer)[0] === 68)();
    function byteSwap(word) {
      return word << 24 & 4278190080 | word << 8 & 16711680 | word >>> 8 & 65280 | word >>> 24 & 255;
    }
    exports.swap8IfBE = exports.isLE ? (n) => n : (n) => byteSwap(n);
    exports.byteSwapIfBE = exports.swap8IfBE;
    function byteSwap32(arr) {
      for (let i = 0; i < arr.length; i++) {
        arr[i] = byteSwap(arr[i]);
      }
      return arr;
    }
    exports.swap32IfBE = exports.isLE ? (u) => u : byteSwap32;
    var hasHexBuiltin = (() => (
      // @ts-ignore
      typeof Uint8Array.from([]).toHex === "function" && typeof Uint8Array.fromHex === "function"
    ))();
    var hexes = Array.from({ length: 256 }, (_, i) => i.toString(16).padStart(2, "0"));
    function bytesToHex(bytes) {
      abytes(bytes);
      if (hasHexBuiltin)
        return bytes.toHex();
      let hex = "";
      for (let i = 0; i < bytes.length; i++) {
        hex += hexes[bytes[i]];
      }
      return hex;
    }
    var asciis = { _0: 48, _9: 57, A: 65, F: 70, a: 97, f: 102 };
    function asciiToBase16(ch) {
      if (ch >= asciis._0 && ch <= asciis._9)
        return ch - asciis._0;
      if (ch >= asciis.A && ch <= asciis.F)
        return ch - (asciis.A - 10);
      if (ch >= asciis.a && ch <= asciis.f)
        return ch - (asciis.a - 10);
      return;
    }
    function hexToBytes(hex) {
      if (typeof hex !== "string")
        throw new Error("hex string expected, got " + typeof hex);
      if (hasHexBuiltin)
        return Uint8Array.fromHex(hex);
      const hl = hex.length;
      const al = hl / 2;
      if (hl % 2)
        throw new Error("hex string expected, got unpadded hex of length " + hl);
      const array = new Uint8Array(al);
      for (let ai = 0, hi = 0; ai < al; ai++, hi += 2) {
        const n1 = asciiToBase16(hex.charCodeAt(hi));
        const n2 = asciiToBase16(hex.charCodeAt(hi + 1));
        if (n1 === void 0 || n2 === void 0) {
          const char = hex[hi] + hex[hi + 1];
          throw new Error('hex string expected, got non-hex character "' + char + '" at index ' + hi);
        }
        array[ai] = n1 * 16 + n2;
      }
      return array;
    }
    var nextTick = async () => {
    };
    exports.nextTick = nextTick;
    async function asyncLoop(iters, tick, cb) {
      let ts = Date.now();
      for (let i = 0; i < iters; i++) {
        cb(i);
        const diff = Date.now() - ts;
        if (diff >= 0 && diff < tick)
          continue;
        await (0, exports.nextTick)();
        ts += diff;
      }
    }
    function utf8ToBytes(str) {
      if (typeof str !== "string")
        throw new Error("string expected");
      return new Uint8Array(new TextEncoder().encode(str));
    }
    function bytesToUtf8(bytes) {
      return new TextDecoder().decode(bytes);
    }
    function toBytes(data) {
      if (typeof data === "string")
        data = utf8ToBytes(data);
      abytes(data);
      return data;
    }
    function kdfInputToBytes(data) {
      if (typeof data === "string")
        data = utf8ToBytes(data);
      abytes(data);
      return data;
    }
    function concatBytes(...arrays) {
      let sum = 0;
      for (let i = 0; i < arrays.length; i++) {
        const a = arrays[i];
        abytes(a);
        sum += a.length;
      }
      const res = new Uint8Array(sum);
      for (let i = 0, pad = 0; i < arrays.length; i++) {
        const a = arrays[i];
        res.set(a, pad);
        pad += a.length;
      }
      return res;
    }
    function checkOpts(defaults, opts) {
      if (opts !== void 0 && {}.toString.call(opts) !== "[object Object]")
        throw new Error("options should be object or undefined");
      const merged = Object.assign(defaults, opts);
      return merged;
    }
    var Hash = class {
    };
    exports.Hash = Hash;
    function createHasher(hashCons) {
      const hashC = (msg) => hashCons().update(toBytes(msg)).digest();
      const tmp = hashCons();
      hashC.outputLen = tmp.outputLen;
      hashC.blockLen = tmp.blockLen;
      hashC.create = () => hashCons();
      return hashC;
    }
    function createOptHasher(hashCons) {
      const hashC = (msg, opts) => hashCons(opts).update(toBytes(msg)).digest();
      const tmp = hashCons({});
      hashC.outputLen = tmp.outputLen;
      hashC.blockLen = tmp.blockLen;
      hashC.create = (opts) => hashCons(opts);
      return hashC;
    }
    function createXOFer(hashCons) {
      const hashC = (msg, opts) => hashCons(opts).update(toBytes(msg)).digest();
      const tmp = hashCons({});
      hashC.outputLen = tmp.outputLen;
      hashC.blockLen = tmp.blockLen;
      hashC.create = (opts) => hashCons(opts);
      return hashC;
    }
    exports.wrapConstructor = createHasher;
    exports.wrapConstructorWithOpts = createOptHasher;
    exports.wrapXOFConstructorWithOpts = createXOFer;
    function randomBytes(bytesLength = 32) {
      if (crypto_1.crypto && typeof crypto_1.crypto.getRandomValues === "function") {
        return crypto_1.crypto.getRandomValues(new Uint8Array(bytesLength));
      }
      if (crypto_1.crypto && typeof crypto_1.crypto.randomBytes === "function") {
        return Uint8Array.from(crypto_1.crypto.randomBytes(bytesLength));
      }
      throw new Error("crypto.getRandomValues must be defined");
    }
  }
});

// node_modules/@noble/hashes/_md.js
var require_md = __commonJS({
  "node_modules/@noble/hashes/_md.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.SHA512_IV = exports.SHA384_IV = exports.SHA224_IV = exports.SHA256_IV = exports.HashMD = void 0;
    exports.setBigUint64 = setBigUint64;
    exports.Chi = Chi;
    exports.Maj = Maj;
    var utils_ts_1 = require_utils3();
    function setBigUint64(view, byteOffset, value, isLE) {
      if (typeof view.setBigUint64 === "function")
        return view.setBigUint64(byteOffset, value, isLE);
      const _32n = BigInt(32);
      const _u32_max = BigInt(4294967295);
      const wh = Number(value >> _32n & _u32_max);
      const wl = Number(value & _u32_max);
      const h = isLE ? 4 : 0;
      const l = isLE ? 0 : 4;
      view.setUint32(byteOffset + h, wh, isLE);
      view.setUint32(byteOffset + l, wl, isLE);
    }
    function Chi(a, b, c) {
      return a & b ^ ~a & c;
    }
    function Maj(a, b, c) {
      return a & b ^ a & c ^ b & c;
    }
    var HashMD = class extends utils_ts_1.Hash {
      constructor(blockLen, outputLen, padOffset, isLE) {
        super();
        this.finished = false;
        this.length = 0;
        this.pos = 0;
        this.destroyed = false;
        this.blockLen = blockLen;
        this.outputLen = outputLen;
        this.padOffset = padOffset;
        this.isLE = isLE;
        this.buffer = new Uint8Array(blockLen);
        this.view = (0, utils_ts_1.createView)(this.buffer);
      }
      update(data) {
        (0, utils_ts_1.aexists)(this);
        data = (0, utils_ts_1.toBytes)(data);
        (0, utils_ts_1.abytes)(data);
        const { view, buffer, blockLen } = this;
        const len = data.length;
        for (let pos = 0; pos < len; ) {
          const take = Math.min(blockLen - this.pos, len - pos);
          if (take === blockLen) {
            const dataView = (0, utils_ts_1.createView)(data);
            for (; blockLen <= len - pos; pos += blockLen)
              this.process(dataView, pos);
            continue;
          }
          buffer.set(data.subarray(pos, pos + take), this.pos);
          this.pos += take;
          pos += take;
          if (this.pos === blockLen) {
            this.process(view, 0);
            this.pos = 0;
          }
        }
        this.length += data.length;
        this.roundClean();
        return this;
      }
      digestInto(out) {
        (0, utils_ts_1.aexists)(this);
        (0, utils_ts_1.aoutput)(out, this);
        this.finished = true;
        const { buffer, view, blockLen, isLE } = this;
        let { pos } = this;
        buffer[pos++] = 128;
        (0, utils_ts_1.clean)(this.buffer.subarray(pos));
        if (this.padOffset > blockLen - pos) {
          this.process(view, 0);
          pos = 0;
        }
        for (let i = pos; i < blockLen; i++)
          buffer[i] = 0;
        setBigUint64(view, blockLen - 8, BigInt(this.length * 8), isLE);
        this.process(view, 0);
        const oview = (0, utils_ts_1.createView)(out);
        const len = this.outputLen;
        if (len % 4)
          throw new Error("_sha2: outputLen should be aligned to 32bit");
        const outLen = len / 4;
        const state = this.get();
        if (outLen > state.length)
          throw new Error("_sha2: outputLen bigger than state");
        for (let i = 0; i < outLen; i++)
          oview.setUint32(4 * i, state[i], isLE);
      }
      digest() {
        const { buffer, outputLen } = this;
        this.digestInto(buffer);
        const res = buffer.slice(0, outputLen);
        this.destroy();
        return res;
      }
      _cloneInto(to) {
        to || (to = new this.constructor());
        to.set(...this.get());
        const { blockLen, buffer, length, finished, destroyed, pos } = this;
        to.destroyed = destroyed;
        to.finished = finished;
        to.length = length;
        to.pos = pos;
        if (length % blockLen)
          to.buffer.set(buffer);
        return to;
      }
      clone() {
        return this._cloneInto();
      }
    };
    exports.HashMD = HashMD;
    exports.SHA256_IV = Uint32Array.from([
      1779033703,
      3144134277,
      1013904242,
      2773480762,
      1359893119,
      2600822924,
      528734635,
      1541459225
    ]);
    exports.SHA224_IV = Uint32Array.from([
      3238371032,
      914150663,
      812702999,
      4144912697,
      4290775857,
      1750603025,
      1694076839,
      3204075428
    ]);
    exports.SHA384_IV = Uint32Array.from([
      3418070365,
      3238371032,
      1654270250,
      914150663,
      2438529370,
      812702999,
      355462360,
      4144912697,
      1731405415,
      4290775857,
      2394180231,
      1750603025,
      3675008525,
      1694076839,
      1203062813,
      3204075428
    ]);
    exports.SHA512_IV = Uint32Array.from([
      1779033703,
      4089235720,
      3144134277,
      2227873595,
      1013904242,
      4271175723,
      2773480762,
      1595750129,
      1359893119,
      2917565137,
      2600822924,
      725511199,
      528734635,
      4215389547,
      1541459225,
      327033209
    ]);
  }
});

// node_modules/@noble/hashes/_u64.js
var require_u64 = __commonJS({
  "node_modules/@noble/hashes/_u64.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.toBig = exports.shrSL = exports.shrSH = exports.rotrSL = exports.rotrSH = exports.rotrBL = exports.rotrBH = exports.rotr32L = exports.rotr32H = exports.rotlSL = exports.rotlSH = exports.rotlBL = exports.rotlBH = exports.add5L = exports.add5H = exports.add4L = exports.add4H = exports.add3L = exports.add3H = void 0;
    exports.add = add;
    exports.fromBig = fromBig;
    exports.split = split;
    var U32_MASK64 = BigInt(2 ** 32 - 1);
    var _32n = BigInt(32);
    function fromBig(n, le = false) {
      if (le)
        return { h: Number(n & U32_MASK64), l: Number(n >> _32n & U32_MASK64) };
      return { h: Number(n >> _32n & U32_MASK64) | 0, l: Number(n & U32_MASK64) | 0 };
    }
    function split(lst, le = false) {
      const len = lst.length;
      let Ah = new Uint32Array(len);
      let Al = new Uint32Array(len);
      for (let i = 0; i < len; i++) {
        const { h, l } = fromBig(lst[i], le);
        [Ah[i], Al[i]] = [h, l];
      }
      return [Ah, Al];
    }
    var toBig = (h, l) => BigInt(h >>> 0) << _32n | BigInt(l >>> 0);
    exports.toBig = toBig;
    var shrSH = (h, _l, s) => h >>> s;
    exports.shrSH = shrSH;
    var shrSL = (h, l, s) => h << 32 - s | l >>> s;
    exports.shrSL = shrSL;
    var rotrSH = (h, l, s) => h >>> s | l << 32 - s;
    exports.rotrSH = rotrSH;
    var rotrSL = (h, l, s) => h << 32 - s | l >>> s;
    exports.rotrSL = rotrSL;
    var rotrBH = (h, l, s) => h << 64 - s | l >>> s - 32;
    exports.rotrBH = rotrBH;
    var rotrBL = (h, l, s) => h >>> s - 32 | l << 64 - s;
    exports.rotrBL = rotrBL;
    var rotr32H = (_h, l) => l;
    exports.rotr32H = rotr32H;
    var rotr32L = (h, _l) => h;
    exports.rotr32L = rotr32L;
    var rotlSH = (h, l, s) => h << s | l >>> 32 - s;
    exports.rotlSH = rotlSH;
    var rotlSL = (h, l, s) => l << s | h >>> 32 - s;
    exports.rotlSL = rotlSL;
    var rotlBH = (h, l, s) => l << s - 32 | h >>> 64 - s;
    exports.rotlBH = rotlBH;
    var rotlBL = (h, l, s) => h << s - 32 | l >>> 64 - s;
    exports.rotlBL = rotlBL;
    function add(Ah, Al, Bh, Bl) {
      const l = (Al >>> 0) + (Bl >>> 0);
      return { h: Ah + Bh + (l / 2 ** 32 | 0) | 0, l: l | 0 };
    }
    var add3L = (Al, Bl, Cl) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0);
    exports.add3L = add3L;
    var add3H = (low, Ah, Bh, Ch) => Ah + Bh + Ch + (low / 2 ** 32 | 0) | 0;
    exports.add3H = add3H;
    var add4L = (Al, Bl, Cl, Dl) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0);
    exports.add4L = add4L;
    var add4H = (low, Ah, Bh, Ch, Dh) => Ah + Bh + Ch + Dh + (low / 2 ** 32 | 0) | 0;
    exports.add4H = add4H;
    var add5L = (Al, Bl, Cl, Dl, El) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0) + (El >>> 0);
    exports.add5L = add5L;
    var add5H = (low, Ah, Bh, Ch, Dh, Eh) => Ah + Bh + Ch + Dh + Eh + (low / 2 ** 32 | 0) | 0;
    exports.add5H = add5H;
    var u64 = {
      fromBig,
      split,
      toBig,
      shrSH,
      shrSL,
      rotrSH,
      rotrSL,
      rotrBH,
      rotrBL,
      rotr32H,
      rotr32L,
      rotlSH,
      rotlSL,
      rotlBH,
      rotlBL,
      add,
      add3L,
      add3H,
      add4L,
      add4H,
      add5H,
      add5L
    };
    exports.default = u64;
  }
});

// node_modules/@noble/hashes/sha2.js
var require_sha2 = __commonJS({
  "node_modules/@noble/hashes/sha2.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.sha512_224 = exports.sha512_256 = exports.sha384 = exports.sha512 = exports.sha224 = exports.sha256 = exports.SHA512_256 = exports.SHA512_224 = exports.SHA384 = exports.SHA512 = exports.SHA224 = exports.SHA256 = void 0;
    var _md_ts_1 = require_md();
    var u64 = require_u64();
    var utils_ts_1 = require_utils3();
    var SHA256_K = Uint32Array.from([
      1116352408,
      1899447441,
      3049323471,
      3921009573,
      961987163,
      1508970993,
      2453635748,
      2870763221,
      3624381080,
      310598401,
      607225278,
      1426881987,
      1925078388,
      2162078206,
      2614888103,
      3248222580,
      3835390401,
      4022224774,
      264347078,
      604807628,
      770255983,
      1249150122,
      1555081692,
      1996064986,
      2554220882,
      2821834349,
      2952996808,
      3210313671,
      3336571891,
      3584528711,
      113926993,
      338241895,
      666307205,
      773529912,
      1294757372,
      1396182291,
      1695183700,
      1986661051,
      2177026350,
      2456956037,
      2730485921,
      2820302411,
      3259730800,
      3345764771,
      3516065817,
      3600352804,
      4094571909,
      275423344,
      430227734,
      506948616,
      659060556,
      883997877,
      958139571,
      1322822218,
      1537002063,
      1747873779,
      1955562222,
      2024104815,
      2227730452,
      2361852424,
      2428436474,
      2756734187,
      3204031479,
      3329325298
    ]);
    var SHA256_W = new Uint32Array(64);
    var SHA256 = class extends _md_ts_1.HashMD {
      constructor(outputLen = 32) {
        super(64, outputLen, 8, false);
        this.A = _md_ts_1.SHA256_IV[0] | 0;
        this.B = _md_ts_1.SHA256_IV[1] | 0;
        this.C = _md_ts_1.SHA256_IV[2] | 0;
        this.D = _md_ts_1.SHA256_IV[3] | 0;
        this.E = _md_ts_1.SHA256_IV[4] | 0;
        this.F = _md_ts_1.SHA256_IV[5] | 0;
        this.G = _md_ts_1.SHA256_IV[6] | 0;
        this.H = _md_ts_1.SHA256_IV[7] | 0;
      }
      get() {
        const { A, B, C, D, E, F, G, H } = this;
        return [A, B, C, D, E, F, G, H];
      }
      // prettier-ignore
      set(A, B, C, D, E, F, G, H) {
        this.A = A | 0;
        this.B = B | 0;
        this.C = C | 0;
        this.D = D | 0;
        this.E = E | 0;
        this.F = F | 0;
        this.G = G | 0;
        this.H = H | 0;
      }
      process(view, offset) {
        for (let i = 0; i < 16; i++, offset += 4)
          SHA256_W[i] = view.getUint32(offset, false);
        for (let i = 16; i < 64; i++) {
          const W15 = SHA256_W[i - 15];
          const W2 = SHA256_W[i - 2];
          const s0 = (0, utils_ts_1.rotr)(W15, 7) ^ (0, utils_ts_1.rotr)(W15, 18) ^ W15 >>> 3;
          const s1 = (0, utils_ts_1.rotr)(W2, 17) ^ (0, utils_ts_1.rotr)(W2, 19) ^ W2 >>> 10;
          SHA256_W[i] = s1 + SHA256_W[i - 7] + s0 + SHA256_W[i - 16] | 0;
        }
        let { A, B, C, D, E, F, G, H } = this;
        for (let i = 0; i < 64; i++) {
          const sigma1 = (0, utils_ts_1.rotr)(E, 6) ^ (0, utils_ts_1.rotr)(E, 11) ^ (0, utils_ts_1.rotr)(E, 25);
          const T1 = H + sigma1 + (0, _md_ts_1.Chi)(E, F, G) + SHA256_K[i] + SHA256_W[i] | 0;
          const sigma0 = (0, utils_ts_1.rotr)(A, 2) ^ (0, utils_ts_1.rotr)(A, 13) ^ (0, utils_ts_1.rotr)(A, 22);
          const T2 = sigma0 + (0, _md_ts_1.Maj)(A, B, C) | 0;
          H = G;
          G = F;
          F = E;
          E = D + T1 | 0;
          D = C;
          C = B;
          B = A;
          A = T1 + T2 | 0;
        }
        A = A + this.A | 0;
        B = B + this.B | 0;
        C = C + this.C | 0;
        D = D + this.D | 0;
        E = E + this.E | 0;
        F = F + this.F | 0;
        G = G + this.G | 0;
        H = H + this.H | 0;
        this.set(A, B, C, D, E, F, G, H);
      }
      roundClean() {
        (0, utils_ts_1.clean)(SHA256_W);
      }
      destroy() {
        this.set(0, 0, 0, 0, 0, 0, 0, 0);
        (0, utils_ts_1.clean)(this.buffer);
      }
    };
    exports.SHA256 = SHA256;
    var SHA224 = class extends SHA256 {
      constructor() {
        super(28);
        this.A = _md_ts_1.SHA224_IV[0] | 0;
        this.B = _md_ts_1.SHA224_IV[1] | 0;
        this.C = _md_ts_1.SHA224_IV[2] | 0;
        this.D = _md_ts_1.SHA224_IV[3] | 0;
        this.E = _md_ts_1.SHA224_IV[4] | 0;
        this.F = _md_ts_1.SHA224_IV[5] | 0;
        this.G = _md_ts_1.SHA224_IV[6] | 0;
        this.H = _md_ts_1.SHA224_IV[7] | 0;
      }
    };
    exports.SHA224 = SHA224;
    var K512 = (() => u64.split([
      "0x428a2f98d728ae22",
      "0x7137449123ef65cd",
      "0xb5c0fbcfec4d3b2f",
      "0xe9b5dba58189dbbc",
      "0x3956c25bf348b538",
      "0x59f111f1b605d019",
      "0x923f82a4af194f9b",
      "0xab1c5ed5da6d8118",
      "0xd807aa98a3030242",
      "0x12835b0145706fbe",
      "0x243185be4ee4b28c",
      "0x550c7dc3d5ffb4e2",
      "0x72be5d74f27b896f",
      "0x80deb1fe3b1696b1",
      "0x9bdc06a725c71235",
      "0xc19bf174cf692694",
      "0xe49b69c19ef14ad2",
      "0xefbe4786384f25e3",
      "0x0fc19dc68b8cd5b5",
      "0x240ca1cc77ac9c65",
      "0x2de92c6f592b0275",
      "0x4a7484aa6ea6e483",
      "0x5cb0a9dcbd41fbd4",
      "0x76f988da831153b5",
      "0x983e5152ee66dfab",
      "0xa831c66d2db43210",
      "0xb00327c898fb213f",
      "0xbf597fc7beef0ee4",
      "0xc6e00bf33da88fc2",
      "0xd5a79147930aa725",
      "0x06ca6351e003826f",
      "0x142929670a0e6e70",
      "0x27b70a8546d22ffc",
      "0x2e1b21385c26c926",
      "0x4d2c6dfc5ac42aed",
      "0x53380d139d95b3df",
      "0x650a73548baf63de",
      "0x766a0abb3c77b2a8",
      "0x81c2c92e47edaee6",
      "0x92722c851482353b",
      "0xa2bfe8a14cf10364",
      "0xa81a664bbc423001",
      "0xc24b8b70d0f89791",
      "0xc76c51a30654be30",
      "0xd192e819d6ef5218",
      "0xd69906245565a910",
      "0xf40e35855771202a",
      "0x106aa07032bbd1b8",
      "0x19a4c116b8d2d0c8",
      "0x1e376c085141ab53",
      "0x2748774cdf8eeb99",
      "0x34b0bcb5e19b48a8",
      "0x391c0cb3c5c95a63",
      "0x4ed8aa4ae3418acb",
      "0x5b9cca4f7763e373",
      "0x682e6ff3d6b2b8a3",
      "0x748f82ee5defb2fc",
      "0x78a5636f43172f60",
      "0x84c87814a1f0ab72",
      "0x8cc702081a6439ec",
      "0x90befffa23631e28",
      "0xa4506cebde82bde9",
      "0xbef9a3f7b2c67915",
      "0xc67178f2e372532b",
      "0xca273eceea26619c",
      "0xd186b8c721c0c207",
      "0xeada7dd6cde0eb1e",
      "0xf57d4f7fee6ed178",
      "0x06f067aa72176fba",
      "0x0a637dc5a2c898a6",
      "0x113f9804bef90dae",
      "0x1b710b35131c471b",
      "0x28db77f523047d84",
      "0x32caab7b40c72493",
      "0x3c9ebe0a15c9bebc",
      "0x431d67c49c100d4c",
      "0x4cc5d4becb3e42b6",
      "0x597f299cfc657e2a",
      "0x5fcb6fab3ad6faec",
      "0x6c44198c4a475817"
    ].map((n) => BigInt(n))))();
    var SHA512_Kh = (() => K512[0])();
    var SHA512_Kl = (() => K512[1])();
    var SHA512_W_H = new Uint32Array(80);
    var SHA512_W_L = new Uint32Array(80);
    var SHA512 = class extends _md_ts_1.HashMD {
      constructor(outputLen = 64) {
        super(128, outputLen, 16, false);
        this.Ah = _md_ts_1.SHA512_IV[0] | 0;
        this.Al = _md_ts_1.SHA512_IV[1] | 0;
        this.Bh = _md_ts_1.SHA512_IV[2] | 0;
        this.Bl = _md_ts_1.SHA512_IV[3] | 0;
        this.Ch = _md_ts_1.SHA512_IV[4] | 0;
        this.Cl = _md_ts_1.SHA512_IV[5] | 0;
        this.Dh = _md_ts_1.SHA512_IV[6] | 0;
        this.Dl = _md_ts_1.SHA512_IV[7] | 0;
        this.Eh = _md_ts_1.SHA512_IV[8] | 0;
        this.El = _md_ts_1.SHA512_IV[9] | 0;
        this.Fh = _md_ts_1.SHA512_IV[10] | 0;
        this.Fl = _md_ts_1.SHA512_IV[11] | 0;
        this.Gh = _md_ts_1.SHA512_IV[12] | 0;
        this.Gl = _md_ts_1.SHA512_IV[13] | 0;
        this.Hh = _md_ts_1.SHA512_IV[14] | 0;
        this.Hl = _md_ts_1.SHA512_IV[15] | 0;
      }
      // prettier-ignore
      get() {
        const { Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl } = this;
        return [Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl];
      }
      // prettier-ignore
      set(Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl) {
        this.Ah = Ah | 0;
        this.Al = Al | 0;
        this.Bh = Bh | 0;
        this.Bl = Bl | 0;
        this.Ch = Ch | 0;
        this.Cl = Cl | 0;
        this.Dh = Dh | 0;
        this.Dl = Dl | 0;
        this.Eh = Eh | 0;
        this.El = El | 0;
        this.Fh = Fh | 0;
        this.Fl = Fl | 0;
        this.Gh = Gh | 0;
        this.Gl = Gl | 0;
        this.Hh = Hh | 0;
        this.Hl = Hl | 0;
      }
      process(view, offset) {
        for (let i = 0; i < 16; i++, offset += 4) {
          SHA512_W_H[i] = view.getUint32(offset);
          SHA512_W_L[i] = view.getUint32(offset += 4);
        }
        for (let i = 16; i < 80; i++) {
          const W15h = SHA512_W_H[i - 15] | 0;
          const W15l = SHA512_W_L[i - 15] | 0;
          const s0h = u64.rotrSH(W15h, W15l, 1) ^ u64.rotrSH(W15h, W15l, 8) ^ u64.shrSH(W15h, W15l, 7);
          const s0l = u64.rotrSL(W15h, W15l, 1) ^ u64.rotrSL(W15h, W15l, 8) ^ u64.shrSL(W15h, W15l, 7);
          const W2h = SHA512_W_H[i - 2] | 0;
          const W2l = SHA512_W_L[i - 2] | 0;
          const s1h = u64.rotrSH(W2h, W2l, 19) ^ u64.rotrBH(W2h, W2l, 61) ^ u64.shrSH(W2h, W2l, 6);
          const s1l = u64.rotrSL(W2h, W2l, 19) ^ u64.rotrBL(W2h, W2l, 61) ^ u64.shrSL(W2h, W2l, 6);
          const SUMl = u64.add4L(s0l, s1l, SHA512_W_L[i - 7], SHA512_W_L[i - 16]);
          const SUMh = u64.add4H(SUMl, s0h, s1h, SHA512_W_H[i - 7], SHA512_W_H[i - 16]);
          SHA512_W_H[i] = SUMh | 0;
          SHA512_W_L[i] = SUMl | 0;
        }
        let { Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl } = this;
        for (let i = 0; i < 80; i++) {
          const sigma1h = u64.rotrSH(Eh, El, 14) ^ u64.rotrSH(Eh, El, 18) ^ u64.rotrBH(Eh, El, 41);
          const sigma1l = u64.rotrSL(Eh, El, 14) ^ u64.rotrSL(Eh, El, 18) ^ u64.rotrBL(Eh, El, 41);
          const CHIh = Eh & Fh ^ ~Eh & Gh;
          const CHIl = El & Fl ^ ~El & Gl;
          const T1ll = u64.add5L(Hl, sigma1l, CHIl, SHA512_Kl[i], SHA512_W_L[i]);
          const T1h = u64.add5H(T1ll, Hh, sigma1h, CHIh, SHA512_Kh[i], SHA512_W_H[i]);
          const T1l = T1ll | 0;
          const sigma0h = u64.rotrSH(Ah, Al, 28) ^ u64.rotrBH(Ah, Al, 34) ^ u64.rotrBH(Ah, Al, 39);
          const sigma0l = u64.rotrSL(Ah, Al, 28) ^ u64.rotrBL(Ah, Al, 34) ^ u64.rotrBL(Ah, Al, 39);
          const MAJh = Ah & Bh ^ Ah & Ch ^ Bh & Ch;
          const MAJl = Al & Bl ^ Al & Cl ^ Bl & Cl;
          Hh = Gh | 0;
          Hl = Gl | 0;
          Gh = Fh | 0;
          Gl = Fl | 0;
          Fh = Eh | 0;
          Fl = El | 0;
          ({ h: Eh, l: El } = u64.add(Dh | 0, Dl | 0, T1h | 0, T1l | 0));
          Dh = Ch | 0;
          Dl = Cl | 0;
          Ch = Bh | 0;
          Cl = Bl | 0;
          Bh = Ah | 0;
          Bl = Al | 0;
          const All = u64.add3L(T1l, sigma0l, MAJl);
          Ah = u64.add3H(All, T1h, sigma0h, MAJh);
          Al = All | 0;
        }
        ({ h: Ah, l: Al } = u64.add(this.Ah | 0, this.Al | 0, Ah | 0, Al | 0));
        ({ h: Bh, l: Bl } = u64.add(this.Bh | 0, this.Bl | 0, Bh | 0, Bl | 0));
        ({ h: Ch, l: Cl } = u64.add(this.Ch | 0, this.Cl | 0, Ch | 0, Cl | 0));
        ({ h: Dh, l: Dl } = u64.add(this.Dh | 0, this.Dl | 0, Dh | 0, Dl | 0));
        ({ h: Eh, l: El } = u64.add(this.Eh | 0, this.El | 0, Eh | 0, El | 0));
        ({ h: Fh, l: Fl } = u64.add(this.Fh | 0, this.Fl | 0, Fh | 0, Fl | 0));
        ({ h: Gh, l: Gl } = u64.add(this.Gh | 0, this.Gl | 0, Gh | 0, Gl | 0));
        ({ h: Hh, l: Hl } = u64.add(this.Hh | 0, this.Hl | 0, Hh | 0, Hl | 0));
        this.set(Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl);
      }
      roundClean() {
        (0, utils_ts_1.clean)(SHA512_W_H, SHA512_W_L);
      }
      destroy() {
        (0, utils_ts_1.clean)(this.buffer);
        this.set(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
      }
    };
    exports.SHA512 = SHA512;
    var SHA384 = class extends SHA512 {
      constructor() {
        super(48);
        this.Ah = _md_ts_1.SHA384_IV[0] | 0;
        this.Al = _md_ts_1.SHA384_IV[1] | 0;
        this.Bh = _md_ts_1.SHA384_IV[2] | 0;
        this.Bl = _md_ts_1.SHA384_IV[3] | 0;
        this.Ch = _md_ts_1.SHA384_IV[4] | 0;
        this.Cl = _md_ts_1.SHA384_IV[5] | 0;
        this.Dh = _md_ts_1.SHA384_IV[6] | 0;
        this.Dl = _md_ts_1.SHA384_IV[7] | 0;
        this.Eh = _md_ts_1.SHA384_IV[8] | 0;
        this.El = _md_ts_1.SHA384_IV[9] | 0;
        this.Fh = _md_ts_1.SHA384_IV[10] | 0;
        this.Fl = _md_ts_1.SHA384_IV[11] | 0;
        this.Gh = _md_ts_1.SHA384_IV[12] | 0;
        this.Gl = _md_ts_1.SHA384_IV[13] | 0;
        this.Hh = _md_ts_1.SHA384_IV[14] | 0;
        this.Hl = _md_ts_1.SHA384_IV[15] | 0;
      }
    };
    exports.SHA384 = SHA384;
    var T224_IV = Uint32Array.from([
      2352822216,
      424955298,
      1944164710,
      2312950998,
      502970286,
      855612546,
      1738396948,
      1479516111,
      258812777,
      2077511080,
      2011393907,
      79989058,
      1067287976,
      1780299464,
      286451373,
      2446758561
    ]);
    var T256_IV = Uint32Array.from([
      573645204,
      4230739756,
      2673172387,
      3360449730,
      596883563,
      1867755857,
      2520282905,
      1497426621,
      2519219938,
      2827943907,
      3193839141,
      1401305490,
      721525244,
      746961066,
      246885852,
      2177182882
    ]);
    var SHA512_224 = class extends SHA512 {
      constructor() {
        super(28);
        this.Ah = T224_IV[0] | 0;
        this.Al = T224_IV[1] | 0;
        this.Bh = T224_IV[2] | 0;
        this.Bl = T224_IV[3] | 0;
        this.Ch = T224_IV[4] | 0;
        this.Cl = T224_IV[5] | 0;
        this.Dh = T224_IV[6] | 0;
        this.Dl = T224_IV[7] | 0;
        this.Eh = T224_IV[8] | 0;
        this.El = T224_IV[9] | 0;
        this.Fh = T224_IV[10] | 0;
        this.Fl = T224_IV[11] | 0;
        this.Gh = T224_IV[12] | 0;
        this.Gl = T224_IV[13] | 0;
        this.Hh = T224_IV[14] | 0;
        this.Hl = T224_IV[15] | 0;
      }
    };
    exports.SHA512_224 = SHA512_224;
    var SHA512_256 = class extends SHA512 {
      constructor() {
        super(32);
        this.Ah = T256_IV[0] | 0;
        this.Al = T256_IV[1] | 0;
        this.Bh = T256_IV[2] | 0;
        this.Bl = T256_IV[3] | 0;
        this.Ch = T256_IV[4] | 0;
        this.Cl = T256_IV[5] | 0;
        this.Dh = T256_IV[6] | 0;
        this.Dl = T256_IV[7] | 0;
        this.Eh = T256_IV[8] | 0;
        this.El = T256_IV[9] | 0;
        this.Fh = T256_IV[10] | 0;
        this.Fl = T256_IV[11] | 0;
        this.Gh = T256_IV[12] | 0;
        this.Gl = T256_IV[13] | 0;
        this.Hh = T256_IV[14] | 0;
        this.Hl = T256_IV[15] | 0;
      }
    };
    exports.SHA512_256 = SHA512_256;
    exports.sha256 = (0, utils_ts_1.createHasher)(() => new SHA256());
    exports.sha224 = (0, utils_ts_1.createHasher)(() => new SHA224());
    exports.sha512 = (0, utils_ts_1.createHasher)(() => new SHA512());
    exports.sha384 = (0, utils_ts_1.createHasher)(() => new SHA384());
    exports.sha512_256 = (0, utils_ts_1.createHasher)(() => new SHA512_256());
    exports.sha512_224 = (0, utils_ts_1.createHasher)(() => new SHA512_224());
  }
});

// node_modules/@noble/curves/utils.js
var require_utils4 = __commonJS({
  "node_modules/@noble/curves/utils.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.notImplemented = exports.bitMask = exports.utf8ToBytes = exports.randomBytes = exports.isBytes = exports.hexToBytes = exports.concatBytes = exports.bytesToUtf8 = exports.bytesToHex = exports.anumber = exports.abytes = void 0;
    exports.abool = abool;
    exports.numberToHexUnpadded = numberToHexUnpadded;
    exports.hexToNumber = hexToNumber;
    exports.bytesToNumberBE = bytesToNumberBE;
    exports.bytesToNumberLE = bytesToNumberLE;
    exports.numberToBytesBE = numberToBytesBE;
    exports.numberToBytesLE = numberToBytesLE;
    exports.numberToVarBytesBE = numberToVarBytesBE;
    exports.ensureBytes = ensureBytes;
    exports.equalBytes = equalBytes;
    exports.inRange = inRange;
    exports.aInRange = aInRange;
    exports.bitLen = bitLen;
    exports.bitGet = bitGet;
    exports.bitSet = bitSet;
    exports.createHmacDrbg = createHmacDrbg;
    exports.validateObject = validateObject;
    exports.isHash = isHash;
    exports._validateObject = _validateObject;
    exports.memoized = memoized;
    var utils_js_1 = require_utils3();
    var utils_js_2 = require_utils3();
    Object.defineProperty(exports, "abytes", { enumerable: true, get: function() {
      return utils_js_2.abytes;
    } });
    Object.defineProperty(exports, "anumber", { enumerable: true, get: function() {
      return utils_js_2.anumber;
    } });
    Object.defineProperty(exports, "bytesToHex", { enumerable: true, get: function() {
      return utils_js_2.bytesToHex;
    } });
    Object.defineProperty(exports, "bytesToUtf8", { enumerable: true, get: function() {
      return utils_js_2.bytesToUtf8;
    } });
    Object.defineProperty(exports, "concatBytes", { enumerable: true, get: function() {
      return utils_js_2.concatBytes;
    } });
    Object.defineProperty(exports, "hexToBytes", { enumerable: true, get: function() {
      return utils_js_2.hexToBytes;
    } });
    Object.defineProperty(exports, "isBytes", { enumerable: true, get: function() {
      return utils_js_2.isBytes;
    } });
    Object.defineProperty(exports, "randomBytes", { enumerable: true, get: function() {
      return utils_js_2.randomBytes;
    } });
    Object.defineProperty(exports, "utf8ToBytes", { enumerable: true, get: function() {
      return utils_js_2.utf8ToBytes;
    } });
    var _0n = BigInt(0);
    var _1n = BigInt(1);
    function abool(title, value) {
      if (typeof value !== "boolean")
        throw new Error(title + " boolean expected, got " + value);
    }
    function numberToHexUnpadded(num) {
      const hex = num.toString(16);
      return hex.length & 1 ? "0" + hex : hex;
    }
    function hexToNumber(hex) {
      if (typeof hex !== "string")
        throw new Error("hex string expected, got " + typeof hex);
      return hex === "" ? _0n : BigInt("0x" + hex);
    }
    function bytesToNumberBE(bytes) {
      return hexToNumber((0, utils_js_1.bytesToHex)(bytes));
    }
    function bytesToNumberLE(bytes) {
      (0, utils_js_1.abytes)(bytes);
      return hexToNumber((0, utils_js_1.bytesToHex)(Uint8Array.from(bytes).reverse()));
    }
    function numberToBytesBE(n, len) {
      return (0, utils_js_1.hexToBytes)(n.toString(16).padStart(len * 2, "0"));
    }
    function numberToBytesLE(n, len) {
      return numberToBytesBE(n, len).reverse();
    }
    function numberToVarBytesBE(n) {
      return (0, utils_js_1.hexToBytes)(numberToHexUnpadded(n));
    }
    function ensureBytes(title, hex, expectedLength) {
      let res;
      if (typeof hex === "string") {
        try {
          res = (0, utils_js_1.hexToBytes)(hex);
        } catch (e) {
          throw new Error(title + " must be hex string or Uint8Array, cause: " + e);
        }
      } else if ((0, utils_js_1.isBytes)(hex)) {
        res = Uint8Array.from(hex);
      } else {
        throw new Error(title + " must be hex string or Uint8Array");
      }
      const len = res.length;
      if (typeof expectedLength === "number" && len !== expectedLength)
        throw new Error(title + " of length " + expectedLength + " expected, got " + len);
      return res;
    }
    function equalBytes(a, b) {
      if (a.length !== b.length)
        return false;
      let diff = 0;
      for (let i = 0; i < a.length; i++)
        diff |= a[i] ^ b[i];
      return diff === 0;
    }
    var isPosBig = (n) => typeof n === "bigint" && _0n <= n;
    function inRange(n, min, max) {
      return isPosBig(n) && isPosBig(min) && isPosBig(max) && min <= n && n < max;
    }
    function aInRange(title, n, min, max) {
      if (!inRange(n, min, max))
        throw new Error("expected valid " + title + ": " + min + " <= n < " + max + ", got " + n);
    }
    function bitLen(n) {
      let len;
      for (len = 0; n > _0n; n >>= _1n, len += 1)
        ;
      return len;
    }
    function bitGet(n, pos) {
      return n >> BigInt(pos) & _1n;
    }
    function bitSet(n, pos, value) {
      return n | (value ? _1n : _0n) << BigInt(pos);
    }
    var bitMask = (n) => (_1n << BigInt(n)) - _1n;
    exports.bitMask = bitMask;
    function createHmacDrbg(hashLen, qByteLen, hmacFn) {
      if (typeof hashLen !== "number" || hashLen < 2)
        throw new Error("hashLen must be a number");
      if (typeof qByteLen !== "number" || qByteLen < 2)
        throw new Error("qByteLen must be a number");
      if (typeof hmacFn !== "function")
        throw new Error("hmacFn must be a function");
      const u8n = (len) => new Uint8Array(len);
      const u8of = (byte) => Uint8Array.of(byte);
      let v = u8n(hashLen);
      let k = u8n(hashLen);
      let i = 0;
      const reset = () => {
        v.fill(1);
        k.fill(0);
        i = 0;
      };
      const h = (...b) => hmacFn(k, v, ...b);
      const reseed = (seed = u8n(0)) => {
        k = h(u8of(0), seed);
        v = h();
        if (seed.length === 0)
          return;
        k = h(u8of(1), seed);
        v = h();
      };
      const gen = () => {
        if (i++ >= 1e3)
          throw new Error("drbg: tried 1000 values");
        let len = 0;
        const out = [];
        while (len < qByteLen) {
          v = h();
          const sl = v.slice();
          out.push(sl);
          len += v.length;
        }
        return (0, utils_js_1.concatBytes)(...out);
      };
      const genUntil = (seed, pred) => {
        reset();
        reseed(seed);
        let res = void 0;
        while (!(res = pred(gen())))
          reseed();
        reset();
        return res;
      };
      return genUntil;
    }
    var validatorFns = {
      bigint: (val) => typeof val === "bigint",
      function: (val) => typeof val === "function",
      boolean: (val) => typeof val === "boolean",
      string: (val) => typeof val === "string",
      stringOrUint8Array: (val) => typeof val === "string" || (0, utils_js_1.isBytes)(val),
      isSafeInteger: (val) => Number.isSafeInteger(val),
      array: (val) => Array.isArray(val),
      field: (val, object) => object.Fp.isValid(val),
      hash: (val) => typeof val === "function" && Number.isSafeInteger(val.outputLen)
    };
    function validateObject(object, validators, optValidators = {}) {
      const checkField = (fieldName, type, isOptional) => {
        const checkVal = validatorFns[type];
        if (typeof checkVal !== "function")
          throw new Error("invalid validator function");
        const val = object[fieldName];
        if (isOptional && val === void 0)
          return;
        if (!checkVal(val, object)) {
          throw new Error("param " + String(fieldName) + " is invalid. Expected " + type + ", got " + val);
        }
      };
      for (const [fieldName, type] of Object.entries(validators))
        checkField(fieldName, type, false);
      for (const [fieldName, type] of Object.entries(optValidators))
        checkField(fieldName, type, true);
      return object;
    }
    function isHash(val) {
      return typeof val === "function" && Number.isSafeInteger(val.outputLen);
    }
    function _validateObject(object, fields, optFields = {}) {
      if (!object || typeof object !== "object")
        throw new Error("expected valid options object");
      function checkField(fieldName, expectedType, isOpt) {
        const val = object[fieldName];
        if (isOpt && val === void 0)
          return;
        const current = typeof val;
        if (current !== expectedType || val === null)
          throw new Error(`param "${fieldName}" is invalid: expected ${expectedType}, got ${current}`);
      }
      Object.entries(fields).forEach(([k, v]) => checkField(k, v, false));
      Object.entries(optFields).forEach(([k, v]) => checkField(k, v, true));
    }
    var notImplemented = () => {
      throw new Error("not implemented");
    };
    exports.notImplemented = notImplemented;
    function memoized(fn) {
      const map = /* @__PURE__ */ new WeakMap();
      return (arg, ...args) => {
        const val = map.get(arg);
        if (val !== void 0)
          return val;
        const computed = fn(arg, ...args);
        map.set(arg, computed);
        return computed;
      };
    }
  }
});

// node_modules/@noble/curves/abstract/modular.js
var require_modular = __commonJS({
  "node_modules/@noble/curves/abstract/modular.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.isNegativeLE = void 0;
    exports.mod = mod;
    exports.pow = pow;
    exports.pow2 = pow2;
    exports.invert = invert;
    exports.tonelliShanks = tonelliShanks;
    exports.FpSqrt = FpSqrt;
    exports.validateField = validateField;
    exports.FpPow = FpPow;
    exports.FpInvertBatch = FpInvertBatch;
    exports.FpDiv = FpDiv;
    exports.FpLegendre = FpLegendre;
    exports.FpIsSquare = FpIsSquare;
    exports.nLength = nLength;
    exports.Field = Field;
    exports.FpSqrtOdd = FpSqrtOdd;
    exports.FpSqrtEven = FpSqrtEven;
    exports.hashToPrivateScalar = hashToPrivateScalar;
    exports.getFieldBytesLength = getFieldBytesLength;
    exports.getMinHashLength = getMinHashLength;
    exports.mapHashToField = mapHashToField;
    var utils_ts_1 = require_utils4();
    var _0n = BigInt(0);
    var _1n = BigInt(1);
    var _2n = BigInt(2);
    var _3n = BigInt(3);
    var _4n = BigInt(4);
    var _5n = BigInt(5);
    var _8n = BigInt(8);
    function mod(a, b) {
      const result = a % b;
      return result >= _0n ? result : b + result;
    }
    function pow(num, power, modulo) {
      return FpPow(Field(modulo), num, power);
    }
    function pow2(x, power, modulo) {
      let res = x;
      while (power-- > _0n) {
        res *= res;
        res %= modulo;
      }
      return res;
    }
    function invert(number, modulo) {
      if (number === _0n)
        throw new Error("invert: expected non-zero number");
      if (modulo <= _0n)
        throw new Error("invert: expected positive modulus, got " + modulo);
      let a = mod(number, modulo);
      let b = modulo;
      let x = _0n, y = _1n, u = _1n, v = _0n;
      while (a !== _0n) {
        const q = b / a;
        const r = b % a;
        const m = x - u * q;
        const n = y - v * q;
        b = a, a = r, x = u, y = v, u = m, v = n;
      }
      const gcd = b;
      if (gcd !== _1n)
        throw new Error("invert: does not exist");
      return mod(x, modulo);
    }
    function sqrt3mod4(Fp, n) {
      const p1div4 = (Fp.ORDER + _1n) / _4n;
      const root = Fp.pow(n, p1div4);
      if (!Fp.eql(Fp.sqr(root), n))
        throw new Error("Cannot find square root");
      return root;
    }
    function sqrt5mod8(Fp, n) {
      const p5div8 = (Fp.ORDER - _5n) / _8n;
      const n2 = Fp.mul(n, _2n);
      const v = Fp.pow(n2, p5div8);
      const nv = Fp.mul(n, v);
      const i = Fp.mul(Fp.mul(nv, _2n), v);
      const root = Fp.mul(nv, Fp.sub(i, Fp.ONE));
      if (!Fp.eql(Fp.sqr(root), n))
        throw new Error("Cannot find square root");
      return root;
    }
    function tonelliShanks(P) {
      if (P < BigInt(3))
        throw new Error("sqrt is not defined for small field");
      let Q = P - _1n;
      let S = 0;
      while (Q % _2n === _0n) {
        Q /= _2n;
        S++;
      }
      let Z = _2n;
      const _Fp = Field(P);
      while (FpLegendre(_Fp, Z) === 1) {
        if (Z++ > 1e3)
          throw new Error("Cannot find square root: probably non-prime P");
      }
      if (S === 1)
        return sqrt3mod4;
      let cc = _Fp.pow(Z, Q);
      const Q1div2 = (Q + _1n) / _2n;
      return function tonelliSlow(Fp, n) {
        if (Fp.is0(n))
          return n;
        if (FpLegendre(Fp, n) !== 1)
          throw new Error("Cannot find square root");
        let M = S;
        let c = Fp.mul(Fp.ONE, cc);
        let t = Fp.pow(n, Q);
        let R = Fp.pow(n, Q1div2);
        while (!Fp.eql(t, Fp.ONE)) {
          if (Fp.is0(t))
            return Fp.ZERO;
          let i = 1;
          let t_tmp = Fp.sqr(t);
          while (!Fp.eql(t_tmp, Fp.ONE)) {
            i++;
            t_tmp = Fp.sqr(t_tmp);
            if (i === M)
              throw new Error("Cannot find square root");
          }
          const exponent = _1n << BigInt(M - i - 1);
          const b = Fp.pow(c, exponent);
          M = i;
          c = Fp.sqr(b);
          t = Fp.mul(t, c);
          R = Fp.mul(R, b);
        }
        return R;
      };
    }
    function FpSqrt(P) {
      if (P % _4n === _3n)
        return sqrt3mod4;
      if (P % _8n === _5n)
        return sqrt5mod8;
      return tonelliShanks(P);
    }
    var isNegativeLE = (num, modulo) => (mod(num, modulo) & _1n) === _1n;
    exports.isNegativeLE = isNegativeLE;
    var FIELD_FIELDS = [
      "create",
      "isValid",
      "is0",
      "neg",
      "inv",
      "sqrt",
      "sqr",
      "eql",
      "add",
      "sub",
      "mul",
      "pow",
      "div",
      "addN",
      "subN",
      "mulN",
      "sqrN"
    ];
    function validateField(field) {
      const initial = {
        ORDER: "bigint",
        MASK: "bigint",
        BYTES: "number",
        BITS: "number"
      };
      const opts = FIELD_FIELDS.reduce((map, val) => {
        map[val] = "function";
        return map;
      }, initial);
      (0, utils_ts_1._validateObject)(field, opts);
      return field;
    }
    function FpPow(Fp, num, power) {
      if (power < _0n)
        throw new Error("invalid exponent, negatives unsupported");
      if (power === _0n)
        return Fp.ONE;
      if (power === _1n)
        return num;
      let p = Fp.ONE;
      let d = num;
      while (power > _0n) {
        if (power & _1n)
          p = Fp.mul(p, d);
        d = Fp.sqr(d);
        power >>= _1n;
      }
      return p;
    }
    function FpInvertBatch(Fp, nums, passZero = false) {
      const inverted = new Array(nums.length).fill(passZero ? Fp.ZERO : void 0);
      const multipliedAcc = nums.reduce((acc, num, i) => {
        if (Fp.is0(num))
          return acc;
        inverted[i] = acc;
        return Fp.mul(acc, num);
      }, Fp.ONE);
      const invertedAcc = Fp.inv(multipliedAcc);
      nums.reduceRight((acc, num, i) => {
        if (Fp.is0(num))
          return acc;
        inverted[i] = Fp.mul(acc, inverted[i]);
        return Fp.mul(acc, num);
      }, invertedAcc);
      return inverted;
    }
    function FpDiv(Fp, lhs, rhs) {
      return Fp.mul(lhs, typeof rhs === "bigint" ? invert(rhs, Fp.ORDER) : Fp.inv(rhs));
    }
    function FpLegendre(Fp, n) {
      const p1mod2 = (Fp.ORDER - _1n) / _2n;
      const powered = Fp.pow(n, p1mod2);
      const yes = Fp.eql(powered, Fp.ONE);
      const zero = Fp.eql(powered, Fp.ZERO);
      const no = Fp.eql(powered, Fp.neg(Fp.ONE));
      if (!yes && !zero && !no)
        throw new Error("invalid Legendre symbol result");
      return yes ? 1 : zero ? 0 : -1;
    }
    function FpIsSquare(Fp, n) {
      const l = FpLegendre(Fp, n);
      return l === 1;
    }
    function nLength(n, nBitLength) {
      if (nBitLength !== void 0)
        (0, utils_ts_1.anumber)(nBitLength);
      const _nBitLength = nBitLength !== void 0 ? nBitLength : n.toString(2).length;
      const nByteLength = Math.ceil(_nBitLength / 8);
      return { nBitLength: _nBitLength, nByteLength };
    }
    function Field(ORDER, bitLenOrOpts, isLE = false, opts = {}) {
      if (ORDER <= _0n)
        throw new Error("invalid field: expected ORDER > 0, got " + ORDER);
      let _nbitLength = void 0;
      let _sqrt = void 0;
      if (typeof bitLenOrOpts === "object" && bitLenOrOpts != null) {
        if (opts.sqrt || isLE)
          throw new Error("cannot specify opts in two arguments");
        const _opts = bitLenOrOpts;
        if (_opts.BITS)
          _nbitLength = _opts.BITS;
        if (_opts.sqrt)
          _sqrt = _opts.sqrt;
        if (typeof _opts.isLE === "boolean")
          isLE = _opts.isLE;
      } else {
        if (typeof bitLenOrOpts === "number")
          _nbitLength = bitLenOrOpts;
        if (opts.sqrt)
          _sqrt = opts.sqrt;
      }
      const { nBitLength: BITS, nByteLength: BYTES } = nLength(ORDER, _nbitLength);
      if (BYTES > 2048)
        throw new Error("invalid field: expected ORDER of <= 2048 bytes");
      let sqrtP;
      const f = Object.freeze({
        ORDER,
        isLE,
        BITS,
        BYTES,
        MASK: (0, utils_ts_1.bitMask)(BITS),
        ZERO: _0n,
        ONE: _1n,
        create: (num) => mod(num, ORDER),
        isValid: (num) => {
          if (typeof num !== "bigint")
            throw new Error("invalid field element: expected bigint, got " + typeof num);
          return _0n <= num && num < ORDER;
        },
        is0: (num) => num === _0n,
        // is valid and invertible
        isValidNot0: (num) => !f.is0(num) && f.isValid(num),
        isOdd: (num) => (num & _1n) === _1n,
        neg: (num) => mod(-num, ORDER),
        eql: (lhs, rhs) => lhs === rhs,
        sqr: (num) => mod(num * num, ORDER),
        add: (lhs, rhs) => mod(lhs + rhs, ORDER),
        sub: (lhs, rhs) => mod(lhs - rhs, ORDER),
        mul: (lhs, rhs) => mod(lhs * rhs, ORDER),
        pow: (num, power) => FpPow(f, num, power),
        div: (lhs, rhs) => mod(lhs * invert(rhs, ORDER), ORDER),
        // Same as above, but doesn't normalize
        sqrN: (num) => num * num,
        addN: (lhs, rhs) => lhs + rhs,
        subN: (lhs, rhs) => lhs - rhs,
        mulN: (lhs, rhs) => lhs * rhs,
        inv: (num) => invert(num, ORDER),
        sqrt: _sqrt || ((n) => {
          if (!sqrtP)
            sqrtP = FpSqrt(ORDER);
          return sqrtP(f, n);
        }),
        toBytes: (num) => isLE ? (0, utils_ts_1.numberToBytesLE)(num, BYTES) : (0, utils_ts_1.numberToBytesBE)(num, BYTES),
        fromBytes: (bytes) => {
          if (bytes.length !== BYTES)
            throw new Error("Field.fromBytes: expected " + BYTES + " bytes, got " + bytes.length);
          return isLE ? (0, utils_ts_1.bytesToNumberLE)(bytes) : (0, utils_ts_1.bytesToNumberBE)(bytes);
        },
        // TODO: we don't need it here, move out to separate fn
        invertBatch: (lst) => FpInvertBatch(f, lst),
        // We can't move this out because Fp6, Fp12 implement it
        // and it's unclear what to return in there.
        cmov: (a, b, c) => c ? b : a
      });
      return Object.freeze(f);
    }
    function FpSqrtOdd(Fp, elm) {
      if (!Fp.isOdd)
        throw new Error("Field doesn't have isOdd");
      const root = Fp.sqrt(elm);
      return Fp.isOdd(root) ? root : Fp.neg(root);
    }
    function FpSqrtEven(Fp, elm) {
      if (!Fp.isOdd)
        throw new Error("Field doesn't have isOdd");
      const root = Fp.sqrt(elm);
      return Fp.isOdd(root) ? Fp.neg(root) : root;
    }
    function hashToPrivateScalar(hash, groupOrder, isLE = false) {
      hash = (0, utils_ts_1.ensureBytes)("privateHash", hash);
      const hashLen = hash.length;
      const minLen = nLength(groupOrder).nByteLength + 8;
      if (minLen < 24 || hashLen < minLen || hashLen > 1024)
        throw new Error("hashToPrivateScalar: expected " + minLen + "-1024 bytes of input, got " + hashLen);
      const num = isLE ? (0, utils_ts_1.bytesToNumberLE)(hash) : (0, utils_ts_1.bytesToNumberBE)(hash);
      return mod(num, groupOrder - _1n) + _1n;
    }
    function getFieldBytesLength(fieldOrder) {
      if (typeof fieldOrder !== "bigint")
        throw new Error("field order must be bigint");
      const bitLength = fieldOrder.toString(2).length;
      return Math.ceil(bitLength / 8);
    }
    function getMinHashLength(fieldOrder) {
      const length = getFieldBytesLength(fieldOrder);
      return length + Math.ceil(length / 2);
    }
    function mapHashToField(key, fieldOrder, isLE = false) {
      const len = key.length;
      const fieldLen = getFieldBytesLength(fieldOrder);
      const minLen = getMinHashLength(fieldOrder);
      if (len < 16 || len < minLen || len > 1024)
        throw new Error("expected " + minLen + "-1024 bytes of input, got " + len);
      const num = isLE ? (0, utils_ts_1.bytesToNumberLE)(key) : (0, utils_ts_1.bytesToNumberBE)(key);
      const reduced = mod(num, fieldOrder - _1n) + _1n;
      return isLE ? (0, utils_ts_1.numberToBytesLE)(reduced, fieldLen) : (0, utils_ts_1.numberToBytesBE)(reduced, fieldLen);
    }
  }
});

// node_modules/@noble/curves/abstract/curve.js
var require_curve = __commonJS({
  "node_modules/@noble/curves/abstract/curve.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.negateCt = negateCt;
    exports.normalizeZ = normalizeZ;
    exports.wNAF = wNAF;
    exports.mulEndoUnsafe = mulEndoUnsafe;
    exports.pippenger = pippenger;
    exports.precomputeMSMUnsafe = precomputeMSMUnsafe;
    exports.validateBasic = validateBasic;
    exports._createCurveFields = _createCurveFields;
    var utils_ts_1 = require_utils4();
    var modular_ts_1 = require_modular();
    var _0n = BigInt(0);
    var _1n = BigInt(1);
    function negateCt(condition, item) {
      const neg = item.negate();
      return condition ? neg : item;
    }
    function normalizeZ(c, property, points) {
      const getz = property === "pz" ? (p) => p.pz : (p) => p.ez;
      const toInv = (0, modular_ts_1.FpInvertBatch)(c.Fp, points.map(getz));
      const affined = points.map((p, i) => p.toAffine(toInv[i]));
      return affined.map(c.fromAffine);
    }
    function validateW(W, bits) {
      if (!Number.isSafeInteger(W) || W <= 0 || W > bits)
        throw new Error("invalid window size, expected [1.." + bits + "], got W=" + W);
    }
    function calcWOpts(W, scalarBits) {
      validateW(W, scalarBits);
      const windows = Math.ceil(scalarBits / W) + 1;
      const windowSize = 2 ** (W - 1);
      const maxNumber = 2 ** W;
      const mask = (0, utils_ts_1.bitMask)(W);
      const shiftBy = BigInt(W);
      return { windows, windowSize, mask, maxNumber, shiftBy };
    }
    function calcOffsets(n, window2, wOpts) {
      const { windowSize, mask, maxNumber, shiftBy } = wOpts;
      let wbits = Number(n & mask);
      let nextN = n >> shiftBy;
      if (wbits > windowSize) {
        wbits -= maxNumber;
        nextN += _1n;
      }
      const offsetStart = window2 * windowSize;
      const offset = offsetStart + Math.abs(wbits) - 1;
      const isZero = wbits === 0;
      const isNeg = wbits < 0;
      const isNegF = window2 % 2 !== 0;
      const offsetF = offsetStart;
      return { nextN, offset, isZero, isNeg, isNegF, offsetF };
    }
    function validateMSMPoints(points, c) {
      if (!Array.isArray(points))
        throw new Error("array expected");
      points.forEach((p, i) => {
        if (!(p instanceof c))
          throw new Error("invalid point at index " + i);
      });
    }
    function validateMSMScalars(scalars, field) {
      if (!Array.isArray(scalars))
        throw new Error("array of scalars expected");
      scalars.forEach((s, i) => {
        if (!field.isValid(s))
          throw new Error("invalid scalar at index " + i);
      });
    }
    var pointPrecomputes = /* @__PURE__ */ new WeakMap();
    var pointWindowSizes = /* @__PURE__ */ new WeakMap();
    function getW(P) {
      return pointWindowSizes.get(P) || 1;
    }
    function assert0(n) {
      if (n !== _0n)
        throw new Error("invalid wNAF");
    }
    function wNAF(c, bits) {
      return {
        constTimeNegate: negateCt,
        hasPrecomputes(elm) {
          return getW(elm) !== 1;
        },
        // non-const time multiplication ladder
        unsafeLadder(elm, n, p = c.ZERO) {
          let d = elm;
          while (n > _0n) {
            if (n & _1n)
              p = p.add(d);
            d = d.double();
            n >>= _1n;
          }
          return p;
        },
        /**
         * Creates a wNAF precomputation window. Used for caching.
         * Default window size is set by `utils.precompute()` and is equal to 8.
         * Number of precomputed points depends on the curve size:
         * 2^(𝑊−1) * (Math.ceil(𝑛 / 𝑊) + 1), where:
         * - 𝑊 is the window size
         * - 𝑛 is the bitlength of the curve order.
         * For a 256-bit curve and window size 8, the number of precomputed points is 128 * 33 = 4224.
         * @param elm Point instance
         * @param W window size
         * @returns precomputed point tables flattened to a single array
         */
        precomputeWindow(elm, W) {
          const { windows, windowSize } = calcWOpts(W, bits);
          const points = [];
          let p = elm;
          let base = p;
          for (let window2 = 0; window2 < windows; window2++) {
            base = p;
            points.push(base);
            for (let i = 1; i < windowSize; i++) {
              base = base.add(p);
              points.push(base);
            }
            p = base.double();
          }
          return points;
        },
        /**
         * Implements ec multiplication using precomputed tables and w-ary non-adjacent form.
         * @param W window size
         * @param precomputes precomputed tables
         * @param n scalar (we don't check here, but should be less than curve order)
         * @returns real and fake (for const-time) points
         */
        wNAF(W, precomputes, n) {
          let p = c.ZERO;
          let f = c.BASE;
          const wo = calcWOpts(W, bits);
          for (let window2 = 0; window2 < wo.windows; window2++) {
            const { nextN, offset, isZero, isNeg, isNegF, offsetF } = calcOffsets(n, window2, wo);
            n = nextN;
            if (isZero) {
              f = f.add(negateCt(isNegF, precomputes[offsetF]));
            } else {
              p = p.add(negateCt(isNeg, precomputes[offset]));
            }
          }
          assert0(n);
          return { p, f };
        },
        /**
         * Implements ec unsafe (non const-time) multiplication using precomputed tables and w-ary non-adjacent form.
         * @param W window size
         * @param precomputes precomputed tables
         * @param n scalar (we don't check here, but should be less than curve order)
         * @param acc accumulator point to add result of multiplication
         * @returns point
         */
        wNAFUnsafe(W, precomputes, n, acc = c.ZERO) {
          const wo = calcWOpts(W, bits);
          for (let window2 = 0; window2 < wo.windows; window2++) {
            if (n === _0n)
              break;
            const { nextN, offset, isZero, isNeg } = calcOffsets(n, window2, wo);
            n = nextN;
            if (isZero) {
              continue;
            } else {
              const item = precomputes[offset];
              acc = acc.add(isNeg ? item.negate() : item);
            }
          }
          assert0(n);
          return acc;
        },
        getPrecomputes(W, P, transform) {
          let comp = pointPrecomputes.get(P);
          if (!comp) {
            comp = this.precomputeWindow(P, W);
            if (W !== 1) {
              if (typeof transform === "function")
                comp = transform(comp);
              pointPrecomputes.set(P, comp);
            }
          }
          return comp;
        },
        wNAFCached(P, n, transform) {
          const W = getW(P);
          return this.wNAF(W, this.getPrecomputes(W, P, transform), n);
        },
        wNAFCachedUnsafe(P, n, transform, prev) {
          const W = getW(P);
          if (W === 1)
            return this.unsafeLadder(P, n, prev);
          return this.wNAFUnsafe(W, this.getPrecomputes(W, P, transform), n, prev);
        },
        // We calculate precomputes for elliptic curve point multiplication
        // using windowed method. This specifies window size and
        // stores precomputed values. Usually only base point would be precomputed.
        setWindowSize(P, W) {
          validateW(W, bits);
          pointWindowSizes.set(P, W);
          pointPrecomputes.delete(P);
        }
      };
    }
    function mulEndoUnsafe(c, point, k1, k2) {
      let acc = point;
      let p1 = c.ZERO;
      let p2 = c.ZERO;
      while (k1 > _0n || k2 > _0n) {
        if (k1 & _1n)
          p1 = p1.add(acc);
        if (k2 & _1n)
          p2 = p2.add(acc);
        acc = acc.double();
        k1 >>= _1n;
        k2 >>= _1n;
      }
      return { p1, p2 };
    }
    function pippenger(c, fieldN, points, scalars) {
      validateMSMPoints(points, c);
      validateMSMScalars(scalars, fieldN);
      const plength = points.length;
      const slength = scalars.length;
      if (plength !== slength)
        throw new Error("arrays of points and scalars must have equal length");
      const zero = c.ZERO;
      const wbits = (0, utils_ts_1.bitLen)(BigInt(plength));
      let windowSize = 1;
      if (wbits > 12)
        windowSize = wbits - 3;
      else if (wbits > 4)
        windowSize = wbits - 2;
      else if (wbits > 0)
        windowSize = 2;
      const MASK = (0, utils_ts_1.bitMask)(windowSize);
      const buckets = new Array(Number(MASK) + 1).fill(zero);
      const lastBits = Math.floor((fieldN.BITS - 1) / windowSize) * windowSize;
      let sum = zero;
      for (let i = lastBits; i >= 0; i -= windowSize) {
        buckets.fill(zero);
        for (let j = 0; j < slength; j++) {
          const scalar = scalars[j];
          const wbits2 = Number(scalar >> BigInt(i) & MASK);
          buckets[wbits2] = buckets[wbits2].add(points[j]);
        }
        let resI = zero;
        for (let j = buckets.length - 1, sumI = zero; j > 0; j--) {
          sumI = sumI.add(buckets[j]);
          resI = resI.add(sumI);
        }
        sum = sum.add(resI);
        if (i !== 0)
          for (let j = 0; j < windowSize; j++)
            sum = sum.double();
      }
      return sum;
    }
    function precomputeMSMUnsafe(c, fieldN, points, windowSize) {
      validateW(windowSize, fieldN.BITS);
      validateMSMPoints(points, c);
      const zero = c.ZERO;
      const tableSize = 2 ** windowSize - 1;
      const chunks = Math.ceil(fieldN.BITS / windowSize);
      const MASK = (0, utils_ts_1.bitMask)(windowSize);
      const tables = points.map((p) => {
        const res = [];
        for (let i = 0, acc = p; i < tableSize; i++) {
          res.push(acc);
          acc = acc.add(p);
        }
        return res;
      });
      return (scalars) => {
        validateMSMScalars(scalars, fieldN);
        if (scalars.length > points.length)
          throw new Error("array of scalars must be smaller than array of points");
        let res = zero;
        for (let i = 0; i < chunks; i++) {
          if (res !== zero)
            for (let j = 0; j < windowSize; j++)
              res = res.double();
          const shiftBy = BigInt(chunks * windowSize - (i + 1) * windowSize);
          for (let j = 0; j < scalars.length; j++) {
            const n = scalars[j];
            const curr = Number(n >> shiftBy & MASK);
            if (!curr)
              continue;
            res = res.add(tables[j][curr - 1]);
          }
        }
        return res;
      };
    }
    function validateBasic(curve) {
      (0, modular_ts_1.validateField)(curve.Fp);
      (0, utils_ts_1.validateObject)(curve, {
        n: "bigint",
        h: "bigint",
        Gx: "field",
        Gy: "field"
      }, {
        nBitLength: "isSafeInteger",
        nByteLength: "isSafeInteger"
      });
      return Object.freeze({
        ...(0, modular_ts_1.nLength)(curve.n, curve.nBitLength),
        ...curve,
        ...{ p: curve.Fp.ORDER }
      });
    }
    function createField(order, field) {
      if (field) {
        if (field.ORDER !== order)
          throw new Error("Field.ORDER must match order: Fp == p, Fn == n");
        (0, modular_ts_1.validateField)(field);
        return field;
      } else {
        return (0, modular_ts_1.Field)(order);
      }
    }
    function _createCurveFields(type, CURVE, curveOpts = {}) {
      if (!CURVE || typeof CURVE !== "object")
        throw new Error(`expected valid ${type} CURVE object`);
      for (const p of ["p", "n", "h"]) {
        const val = CURVE[p];
        if (!(typeof val === "bigint" && val > _0n))
          throw new Error(`CURVE.${p} must be positive bigint`);
      }
      const Fp = createField(CURVE.p, curveOpts.Fp);
      const Fn = createField(CURVE.n, curveOpts.Fn);
      const _b = type === "weierstrass" ? "b" : "d";
      const params = ["Gx", "Gy", "a", _b];
      for (const p of params) {
        if (!Fp.isValid(CURVE[p]))
          throw new Error(`CURVE.${p} must be valid field element of CURVE.Fp`);
      }
      return { Fp, Fn };
    }
  }
});

// node_modules/@noble/curves/abstract/edwards.js
var require_edwards = __commonJS({
  "node_modules/@noble/curves/abstract/edwards.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.edwards = edwards;
    exports.eddsa = eddsa;
    exports.twistedEdwards = twistedEdwards;
    var utils_ts_1 = require_utils4();
    var curve_ts_1 = require_curve();
    var modular_ts_1 = require_modular();
    var _0n = BigInt(0);
    var _1n = BigInt(1);
    var _2n = BigInt(2);
    var _8n = BigInt(8);
    var VERIFY_DEFAULT = { zip215: true };
    function isEdValidXY(Fp, CURVE, x, y) {
      const x2 = Fp.sqr(x);
      const y2 = Fp.sqr(y);
      const left = Fp.add(Fp.mul(CURVE.a, x2), y2);
      const right = Fp.add(Fp.ONE, Fp.mul(CURVE.d, Fp.mul(x2, y2)));
      return Fp.eql(left, right);
    }
    function edwards(CURVE, curveOpts = {}) {
      const { Fp, Fn } = (0, curve_ts_1._createCurveFields)("edwards", CURVE, curveOpts);
      const { h: cofactor, n: CURVE_ORDER } = CURVE;
      (0, utils_ts_1._validateObject)(curveOpts, {}, { uvRatio: "function" });
      const MASK = _2n << BigInt(Fn.BYTES * 8) - _1n;
      const modP = (n) => Fp.create(n);
      const uvRatio = curveOpts.uvRatio || ((u, v) => {
        try {
          return { isValid: true, value: Fp.sqrt(Fp.div(u, v)) };
        } catch (e) {
          return { isValid: false, value: _0n };
        }
      });
      if (!isEdValidXY(Fp, CURVE, CURVE.Gx, CURVE.Gy))
        throw new Error("bad curve params: generator point");
      function acoord(title, n, banZero = false) {
        const min = banZero ? _1n : _0n;
        (0, utils_ts_1.aInRange)("coordinate " + title, n, min, MASK);
        return n;
      }
      function aextpoint(other) {
        if (!(other instanceof Point))
          throw new Error("ExtendedPoint expected");
      }
      const toAffineMemo = (0, utils_ts_1.memoized)((p, iz) => {
        const { ex: x, ey: y, ez: z } = p;
        const is0 = p.is0();
        if (iz == null)
          iz = is0 ? _8n : Fp.inv(z);
        const ax = modP(x * iz);
        const ay = modP(y * iz);
        const zz = modP(z * iz);
        if (is0)
          return { x: _0n, y: _1n };
        if (zz !== _1n)
          throw new Error("invZ was invalid");
        return { x: ax, y: ay };
      });
      const assertValidMemo = (0, utils_ts_1.memoized)((p) => {
        const { a, d } = CURVE;
        if (p.is0())
          throw new Error("bad point: ZERO");
        const { ex: X, ey: Y, ez: Z, et: T } = p;
        const X2 = modP(X * X);
        const Y2 = modP(Y * Y);
        const Z2 = modP(Z * Z);
        const Z4 = modP(Z2 * Z2);
        const aX2 = modP(X2 * a);
        const left = modP(Z2 * modP(aX2 + Y2));
        const right = modP(Z4 + modP(d * modP(X2 * Y2)));
        if (left !== right)
          throw new Error("bad point: equation left != right (1)");
        const XY = modP(X * Y);
        const ZT = modP(Z * T);
        if (XY !== ZT)
          throw new Error("bad point: equation left != right (2)");
        return true;
      });
      class Point {
        constructor(ex, ey, ez, et) {
          this.ex = acoord("x", ex);
          this.ey = acoord("y", ey);
          this.ez = acoord("z", ez, true);
          this.et = acoord("t", et);
          Object.freeze(this);
        }
        get x() {
          return this.toAffine().x;
        }
        get y() {
          return this.toAffine().y;
        }
        static fromAffine(p) {
          if (p instanceof Point)
            throw new Error("extended point not allowed");
          const { x, y } = p || {};
          acoord("x", x);
          acoord("y", y);
          return new Point(x, y, _1n, modP(x * y));
        }
        static normalizeZ(points) {
          return (0, curve_ts_1.normalizeZ)(Point, "ez", points);
        }
        // Multiscalar Multiplication
        static msm(points, scalars) {
          return (0, curve_ts_1.pippenger)(Point, Fn, points, scalars);
        }
        // "Private method", don't use it directly
        _setWindowSize(windowSize) {
          this.precompute(windowSize);
        }
        precompute(windowSize = 8, isLazy = true) {
          wnaf.setWindowSize(this, windowSize);
          if (!isLazy)
            this.multiply(_2n);
          return this;
        }
        // Not required for fromHex(), which always creates valid points.
        // Could be useful for fromAffine().
        assertValidity() {
          assertValidMemo(this);
        }
        // Compare one point to another.
        equals(other) {
          aextpoint(other);
          const { ex: X1, ey: Y1, ez: Z1 } = this;
          const { ex: X2, ey: Y2, ez: Z2 } = other;
          const X1Z2 = modP(X1 * Z2);
          const X2Z1 = modP(X2 * Z1);
          const Y1Z2 = modP(Y1 * Z2);
          const Y2Z1 = modP(Y2 * Z1);
          return X1Z2 === X2Z1 && Y1Z2 === Y2Z1;
        }
        is0() {
          return this.equals(Point.ZERO);
        }
        negate() {
          return new Point(modP(-this.ex), this.ey, this.ez, modP(-this.et));
        }
        // Fast algo for doubling Extended Point.
        // https://hyperelliptic.org/EFD/g1p/auto-twisted-extended.html#doubling-dbl-2008-hwcd
        // Cost: 4M + 4S + 1*a + 6add + 1*2.
        double() {
          const { a } = CURVE;
          const { ex: X1, ey: Y1, ez: Z1 } = this;
          const A = modP(X1 * X1);
          const B = modP(Y1 * Y1);
          const C = modP(_2n * modP(Z1 * Z1));
          const D = modP(a * A);
          const x1y1 = X1 + Y1;
          const E = modP(modP(x1y1 * x1y1) - A - B);
          const G = D + B;
          const F = G - C;
          const H = D - B;
          const X3 = modP(E * F);
          const Y3 = modP(G * H);
          const T3 = modP(E * H);
          const Z3 = modP(F * G);
          return new Point(X3, Y3, Z3, T3);
        }
        // Fast algo for adding 2 Extended Points.
        // https://hyperelliptic.org/EFD/g1p/auto-twisted-extended.html#addition-add-2008-hwcd
        // Cost: 9M + 1*a + 1*d + 7add.
        add(other) {
          aextpoint(other);
          const { a, d } = CURVE;
          const { ex: X1, ey: Y1, ez: Z1, et: T1 } = this;
          const { ex: X2, ey: Y2, ez: Z2, et: T2 } = other;
          const A = modP(X1 * X2);
          const B = modP(Y1 * Y2);
          const C = modP(T1 * d * T2);
          const D = modP(Z1 * Z2);
          const E = modP((X1 + Y1) * (X2 + Y2) - A - B);
          const F = D - C;
          const G = D + C;
          const H = modP(B - a * A);
          const X3 = modP(E * F);
          const Y3 = modP(G * H);
          const T3 = modP(E * H);
          const Z3 = modP(F * G);
          return new Point(X3, Y3, Z3, T3);
        }
        subtract(other) {
          return this.add(other.negate());
        }
        // Constant-time multiplication.
        multiply(scalar) {
          const n = scalar;
          (0, utils_ts_1.aInRange)("scalar", n, _1n, CURVE_ORDER);
          const { p, f } = wnaf.wNAFCached(this, n, Point.normalizeZ);
          return Point.normalizeZ([p, f])[0];
        }
        // Non-constant-time multiplication. Uses double-and-add algorithm.
        // It's faster, but should only be used when you don't care about
        // an exposed private key e.g. sig verification.
        // Does NOT allow scalars higher than CURVE.n.
        // Accepts optional accumulator to merge with multiply (important for sparse scalars)
        multiplyUnsafe(scalar, acc = Point.ZERO) {
          const n = scalar;
          (0, utils_ts_1.aInRange)("scalar", n, _0n, CURVE_ORDER);
          if (n === _0n)
            return Point.ZERO;
          if (this.is0() || n === _1n)
            return this;
          return wnaf.wNAFCachedUnsafe(this, n, Point.normalizeZ, acc);
        }
        // Checks if point is of small order.
        // If you add something to small order point, you will have "dirty"
        // point with torsion component.
        // Multiplies point by cofactor and checks if the result is 0.
        isSmallOrder() {
          return this.multiplyUnsafe(cofactor).is0();
        }
        // Multiplies point by curve order and checks if the result is 0.
        // Returns `false` is the point is dirty.
        isTorsionFree() {
          return wnaf.wNAFCachedUnsafe(this, CURVE_ORDER).is0();
        }
        // Converts Extended point to default (x, y) coordinates.
        // Can accept precomputed Z^-1 - for example, from invertBatch.
        toAffine(invertedZ) {
          return toAffineMemo(this, invertedZ);
        }
        clearCofactor() {
          if (cofactor === _1n)
            return this;
          return this.multiplyUnsafe(cofactor);
        }
        static fromBytes(bytes, zip215 = false) {
          (0, utils_ts_1.abytes)(bytes);
          return this.fromHex(bytes, zip215);
        }
        // Converts hash string or Uint8Array to Point.
        // Uses algo from RFC8032 5.1.3.
        static fromHex(hex, zip215 = false) {
          const { d, a } = CURVE;
          const len = Fp.BYTES;
          hex = (0, utils_ts_1.ensureBytes)("pointHex", hex, len);
          (0, utils_ts_1.abool)("zip215", zip215);
          const normed = hex.slice();
          const lastByte = hex[len - 1];
          normed[len - 1] = lastByte & ~128;
          const y = (0, utils_ts_1.bytesToNumberLE)(normed);
          const max = zip215 ? MASK : Fp.ORDER;
          (0, utils_ts_1.aInRange)("pointHex.y", y, _0n, max);
          const y2 = modP(y * y);
          const u = modP(y2 - _1n);
          const v = modP(d * y2 - a);
          let { isValid, value: x } = uvRatio(u, v);
          if (!isValid)
            throw new Error("Point.fromHex: invalid y coordinate");
          const isXOdd = (x & _1n) === _1n;
          const isLastByteOdd = (lastByte & 128) !== 0;
          if (!zip215 && x === _0n && isLastByteOdd)
            throw new Error("Point.fromHex: x=0 and x_0=1");
          if (isLastByteOdd !== isXOdd)
            x = modP(-x);
          return Point.fromAffine({ x, y });
        }
        static fromPrivateScalar(scalar) {
          return Point.BASE.multiply(scalar);
        }
        toBytes() {
          const { x, y } = this.toAffine();
          const bytes = (0, utils_ts_1.numberToBytesLE)(y, Fp.BYTES);
          bytes[bytes.length - 1] |= x & _1n ? 128 : 0;
          return bytes;
        }
        /** @deprecated use `toBytes` */
        toRawBytes() {
          return this.toBytes();
        }
        toHex() {
          return (0, utils_ts_1.bytesToHex)(this.toBytes());
        }
        toString() {
          return `<Point ${this.is0() ? "ZERO" : this.toHex()}>`;
        }
      }
      Point.BASE = new Point(CURVE.Gx, CURVE.Gy, _1n, modP(CURVE.Gx * CURVE.Gy));
      Point.ZERO = new Point(_0n, _1n, _1n, _0n);
      Point.Fp = Fp;
      Point.Fn = Fn;
      const wnaf = (0, curve_ts_1.wNAF)(Point, Fn.BYTES * 8);
      return Point;
    }
    function eddsa(Point, eddsaOpts) {
      (0, utils_ts_1._validateObject)(eddsaOpts, {
        hash: "function"
      }, {
        adjustScalarBytes: "function",
        randomBytes: "function",
        domain: "function",
        prehash: "function",
        mapToCurve: "function"
      });
      const { prehash, hash: cHash } = eddsaOpts;
      const { BASE: G, Fp, Fn } = Point;
      const CURVE_ORDER = Fn.ORDER;
      const randomBytes_ = eddsaOpts.randomBytes || utils_ts_1.randomBytes;
      const adjustScalarBytes = eddsaOpts.adjustScalarBytes || ((bytes) => bytes);
      const domain = eddsaOpts.domain || ((data, ctx, phflag) => {
        (0, utils_ts_1.abool)("phflag", phflag);
        if (ctx.length || phflag)
          throw new Error("Contexts/pre-hash are not supported");
        return data;
      });
      function modN(a) {
        return Fn.create(a);
      }
      function modN_LE(hash) {
        return modN((0, utils_ts_1.bytesToNumberLE)(hash));
      }
      function getPrivateScalar(key) {
        const len = Fp.BYTES;
        key = (0, utils_ts_1.ensureBytes)("private key", key, len);
        const hashed = (0, utils_ts_1.ensureBytes)("hashed private key", cHash(key), 2 * len);
        const head = adjustScalarBytes(hashed.slice(0, len));
        const prefix = hashed.slice(len, 2 * len);
        const scalar = modN_LE(head);
        return { head, prefix, scalar };
      }
      function getExtendedPublicKey(key) {
        const { head, prefix, scalar } = getPrivateScalar(key);
        const point = G.multiply(scalar);
        const pointBytes = point.toBytes();
        return { head, prefix, scalar, point, pointBytes };
      }
      function getPublicKey(privKey) {
        return getExtendedPublicKey(privKey).pointBytes;
      }
      function hashDomainToScalar(context = Uint8Array.of(), ...msgs) {
        const msg = (0, utils_ts_1.concatBytes)(...msgs);
        return modN_LE(cHash(domain(msg, (0, utils_ts_1.ensureBytes)("context", context), !!prehash)));
      }
      function sign(msg, privKey, options = {}) {
        msg = (0, utils_ts_1.ensureBytes)("message", msg);
        if (prehash)
          msg = prehash(msg);
        const { prefix, scalar, pointBytes } = getExtendedPublicKey(privKey);
        const r = hashDomainToScalar(options.context, prefix, msg);
        const R = G.multiply(r).toBytes();
        const k = hashDomainToScalar(options.context, R, pointBytes, msg);
        const s = modN(r + k * scalar);
        (0, utils_ts_1.aInRange)("signature.s", s, _0n, CURVE_ORDER);
        const L = Fp.BYTES;
        const res = (0, utils_ts_1.concatBytes)(R, (0, utils_ts_1.numberToBytesLE)(s, L));
        return (0, utils_ts_1.ensureBytes)("result", res, L * 2);
      }
      const verifyOpts = VERIFY_DEFAULT;
      function verify(sig, msg, publicKey, options = verifyOpts) {
        const { context, zip215 } = options;
        const len = Fp.BYTES;
        sig = (0, utils_ts_1.ensureBytes)("signature", sig, 2 * len);
        msg = (0, utils_ts_1.ensureBytes)("message", msg);
        publicKey = (0, utils_ts_1.ensureBytes)("publicKey", publicKey, len);
        if (zip215 !== void 0)
          (0, utils_ts_1.abool)("zip215", zip215);
        if (prehash)
          msg = prehash(msg);
        const s = (0, utils_ts_1.bytesToNumberLE)(sig.slice(len, 2 * len));
        let A, R, SB;
        try {
          A = Point.fromHex(publicKey, zip215);
          R = Point.fromHex(sig.slice(0, len), zip215);
          SB = G.multiplyUnsafe(s);
        } catch (error) {
          return false;
        }
        if (!zip215 && A.isSmallOrder())
          return false;
        const k = hashDomainToScalar(context, R.toBytes(), A.toBytes(), msg);
        const RkA = R.add(A.multiplyUnsafe(k));
        return RkA.subtract(SB).clearCofactor().is0();
      }
      G.precompute(8);
      const utils = {
        getExtendedPublicKey,
        /** ed25519 priv keys are uniform 32b. No need to check for modulo bias, like in secp256k1. */
        randomPrivateKey: () => randomBytes_(Fp.BYTES),
        /**
         * We're doing scalar multiplication (used in getPublicKey etc) with precomputed BASE_POINT
         * values. This slows down first getPublicKey() by milliseconds (see Speed section),
         * but allows to speed-up subsequent getPublicKey() calls up to 20x.
         * @param windowSize 2, 4, 8, 16
         */
        precompute(windowSize = 8, point = Point.BASE) {
          return point.precompute(windowSize, false);
        }
      };
      return { getPublicKey, sign, verify, utils, Point };
    }
    function _eddsa_legacy_opts_to_new(c) {
      const CURVE = {
        a: c.a,
        d: c.d,
        p: c.Fp.ORDER,
        n: c.n,
        h: c.h,
        Gx: c.Gx,
        Gy: c.Gy
      };
      const Fp = c.Fp;
      const Fn = (0, modular_ts_1.Field)(CURVE.n, c.nBitLength, true);
      const curveOpts = { Fp, Fn, uvRatio: c.uvRatio };
      const eddsaOpts = {
        hash: c.hash,
        randomBytes: c.randomBytes,
        adjustScalarBytes: c.adjustScalarBytes,
        domain: c.domain,
        prehash: c.prehash,
        mapToCurve: c.mapToCurve
      };
      return { CURVE, curveOpts, eddsaOpts };
    }
    function _eddsa_new_output_to_legacy(c, eddsa2) {
      const legacy = Object.assign({}, eddsa2, { ExtendedPoint: eddsa2.Point, CURVE: c });
      return legacy;
    }
    function twistedEdwards(c) {
      const { CURVE, curveOpts, eddsaOpts } = _eddsa_legacy_opts_to_new(c);
      const Point = edwards(CURVE, curveOpts);
      const EDDSA = eddsa(Point, eddsaOpts);
      return _eddsa_new_output_to_legacy(c, EDDSA);
    }
  }
});

// node_modules/@noble/curves/abstract/hash-to-curve.js
var require_hash_to_curve = __commonJS({
  "node_modules/@noble/curves/abstract/hash-to-curve.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.expand_message_xmd = expand_message_xmd;
    exports.expand_message_xof = expand_message_xof;
    exports.hash_to_field = hash_to_field;
    exports.isogenyMap = isogenyMap;
    exports.createHasher = createHasher;
    var utils_ts_1 = require_utils4();
    var modular_ts_1 = require_modular();
    var os2ip = utils_ts_1.bytesToNumberBE;
    function i2osp(value, length) {
      anum(value);
      anum(length);
      if (value < 0 || value >= 1 << 8 * length)
        throw new Error("invalid I2OSP input: " + value);
      const res = Array.from({ length }).fill(0);
      for (let i = length - 1; i >= 0; i--) {
        res[i] = value & 255;
        value >>>= 8;
      }
      return new Uint8Array(res);
    }
    function strxor(a, b) {
      const arr = new Uint8Array(a.length);
      for (let i = 0; i < a.length; i++) {
        arr[i] = a[i] ^ b[i];
      }
      return arr;
    }
    function anum(item) {
      if (!Number.isSafeInteger(item))
        throw new Error("number expected");
    }
    function expand_message_xmd(msg, DST, lenInBytes, H) {
      (0, utils_ts_1.abytes)(msg);
      (0, utils_ts_1.abytes)(DST);
      anum(lenInBytes);
      if (DST.length > 255)
        DST = H((0, utils_ts_1.concatBytes)((0, utils_ts_1.utf8ToBytes)("H2C-OVERSIZE-DST-"), DST));
      const { outputLen: b_in_bytes, blockLen: r_in_bytes } = H;
      const ell = Math.ceil(lenInBytes / b_in_bytes);
      if (lenInBytes > 65535 || ell > 255)
        throw new Error("expand_message_xmd: invalid lenInBytes");
      const DST_prime = (0, utils_ts_1.concatBytes)(DST, i2osp(DST.length, 1));
      const Z_pad = i2osp(0, r_in_bytes);
      const l_i_b_str = i2osp(lenInBytes, 2);
      const b = new Array(ell);
      const b_0 = H((0, utils_ts_1.concatBytes)(Z_pad, msg, l_i_b_str, i2osp(0, 1), DST_prime));
      b[0] = H((0, utils_ts_1.concatBytes)(b_0, i2osp(1, 1), DST_prime));
      for (let i = 1; i <= ell; i++) {
        const args = [strxor(b_0, b[i - 1]), i2osp(i + 1, 1), DST_prime];
        b[i] = H((0, utils_ts_1.concatBytes)(...args));
      }
      const pseudo_random_bytes = (0, utils_ts_1.concatBytes)(...b);
      return pseudo_random_bytes.slice(0, lenInBytes);
    }
    function expand_message_xof(msg, DST, lenInBytes, k, H) {
      (0, utils_ts_1.abytes)(msg);
      (0, utils_ts_1.abytes)(DST);
      anum(lenInBytes);
      if (DST.length > 255) {
        const dkLen = Math.ceil(2 * k / 8);
        DST = H.create({ dkLen }).update((0, utils_ts_1.utf8ToBytes)("H2C-OVERSIZE-DST-")).update(DST).digest();
      }
      if (lenInBytes > 65535 || DST.length > 255)
        throw new Error("expand_message_xof: invalid lenInBytes");
      return H.create({ dkLen: lenInBytes }).update(msg).update(i2osp(lenInBytes, 2)).update(DST).update(i2osp(DST.length, 1)).digest();
    }
    function hash_to_field(msg, count, options) {
      (0, utils_ts_1._validateObject)(options, {
        p: "bigint",
        m: "number",
        k: "number",
        hash: "function"
      });
      const { p, k, m, hash, expand, DST: _DST } = options;
      if (!(0, utils_ts_1.isBytes)(_DST) && typeof _DST !== "string")
        throw new Error("DST must be string or uint8array");
      if (!(0, utils_ts_1.isHash)(options.hash))
        throw new Error("expected valid hash");
      (0, utils_ts_1.abytes)(msg);
      anum(count);
      const DST = typeof _DST === "string" ? (0, utils_ts_1.utf8ToBytes)(_DST) : _DST;
      const log2p = p.toString(2).length;
      const L = Math.ceil((log2p + k) / 8);
      const len_in_bytes = count * m * L;
      let prb;
      if (expand === "xmd") {
        prb = expand_message_xmd(msg, DST, len_in_bytes, hash);
      } else if (expand === "xof") {
        prb = expand_message_xof(msg, DST, len_in_bytes, k, hash);
      } else if (expand === "_internal_pass") {
        prb = msg;
      } else {
        throw new Error('expand must be "xmd" or "xof"');
      }
      const u = new Array(count);
      for (let i = 0; i < count; i++) {
        const e = new Array(m);
        for (let j = 0; j < m; j++) {
          const elm_offset = L * (j + i * m);
          const tv = prb.subarray(elm_offset, elm_offset + L);
          e[j] = (0, modular_ts_1.mod)(os2ip(tv), p);
        }
        u[i] = e;
      }
      return u;
    }
    function isogenyMap(field, map) {
      const coeff = map.map((i) => Array.from(i).reverse());
      return (x, y) => {
        const [xn, xd, yn, yd] = coeff.map((val) => val.reduce((acc, i) => field.add(field.mul(acc, x), i)));
        const [xd_inv, yd_inv] = (0, modular_ts_1.FpInvertBatch)(field, [xd, yd], true);
        x = field.mul(xn, xd_inv);
        y = field.mul(y, field.mul(yn, yd_inv));
        return { x, y };
      };
    }
    function createHasher(Point, mapToCurve, defaults) {
      if (typeof mapToCurve !== "function")
        throw new Error("mapToCurve() must be defined");
      function map(num) {
        return Point.fromAffine(mapToCurve(num));
      }
      function clear(initial) {
        const P = initial.clearCofactor();
        if (P.equals(Point.ZERO))
          return Point.ZERO;
        P.assertValidity();
        return P;
      }
      return {
        defaults,
        hashToCurve(msg, options) {
          const dst = defaults.DST ? defaults.DST : {};
          const opts = Object.assign({}, defaults, dst, options);
          const u = hash_to_field(msg, 2, opts);
          const u0 = map(u[0]);
          const u1 = map(u[1]);
          return clear(u0.add(u1));
        },
        encodeToCurve(msg, options) {
          const dst = defaults.encodeDST ? defaults.encodeDST : {};
          const opts = Object.assign({}, defaults, dst, options);
          const u = hash_to_field(msg, 1, opts);
          return clear(map(u[0]));
        },
        /** See {@link H2CHasher} */
        mapToCurve(scalars) {
          if (!Array.isArray(scalars))
            throw new Error("expected array of bigints");
          for (const i of scalars)
            if (typeof i !== "bigint")
              throw new Error("expected array of bigints");
          return clear(map(scalars));
        }
      };
    }
  }
});

// node_modules/@noble/curves/abstract/montgomery.js
var require_montgomery = __commonJS({
  "node_modules/@noble/curves/abstract/montgomery.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.montgomery = montgomery;
    var utils_ts_1 = require_utils4();
    var modular_ts_1 = require_modular();
    var _0n = BigInt(0);
    var _1n = BigInt(1);
    var _2n = BigInt(2);
    function validateOpts(curve) {
      (0, utils_ts_1._validateObject)(curve, {
        adjustScalarBytes: "function",
        powPminus2: "function"
      });
      return Object.freeze({ ...curve });
    }
    function montgomery(curveDef) {
      const CURVE = validateOpts(curveDef);
      const { P, type, adjustScalarBytes, powPminus2, randomBytes: rand } = CURVE;
      const is25519 = type === "x25519";
      if (!is25519 && type !== "x448")
        throw new Error("invalid type");
      const randomBytes_ = rand || utils_ts_1.randomBytes;
      const montgomeryBits = is25519 ? 255 : 448;
      const fieldLen = is25519 ? 32 : 56;
      const Gu = is25519 ? BigInt(9) : BigInt(5);
      const a24 = is25519 ? BigInt(121665) : BigInt(39081);
      const minScalar = is25519 ? _2n ** BigInt(254) : _2n ** BigInt(447);
      const maxAdded = is25519 ? BigInt(8) * _2n ** BigInt(251) - _1n : BigInt(4) * _2n ** BigInt(445) - _1n;
      const maxScalar = minScalar + maxAdded + _1n;
      const modP = (n) => (0, modular_ts_1.mod)(n, P);
      const GuBytes = encodeU(Gu);
      function encodeU(u) {
        return (0, utils_ts_1.numberToBytesLE)(modP(u), fieldLen);
      }
      function decodeU(u) {
        const _u = (0, utils_ts_1.ensureBytes)("u coordinate", u, fieldLen);
        if (is25519)
          _u[31] &= 127;
        return modP((0, utils_ts_1.bytesToNumberLE)(_u));
      }
      function decodeScalar(scalar) {
        return (0, utils_ts_1.bytesToNumberLE)(adjustScalarBytes((0, utils_ts_1.ensureBytes)("scalar", scalar, fieldLen)));
      }
      function scalarMult(scalar, u) {
        const pu = montgomeryLadder(decodeU(u), decodeScalar(scalar));
        if (pu === _0n)
          throw new Error("invalid private or public key received");
        return encodeU(pu);
      }
      function scalarMultBase(scalar) {
        return scalarMult(scalar, GuBytes);
      }
      function cswap(swap, x_2, x_3) {
        const dummy = modP(swap * (x_2 - x_3));
        x_2 = modP(x_2 - dummy);
        x_3 = modP(x_3 + dummy);
        return { x_2, x_3 };
      }
      function montgomeryLadder(u, scalar) {
        (0, utils_ts_1.aInRange)("u", u, _0n, P);
        (0, utils_ts_1.aInRange)("scalar", scalar, minScalar, maxScalar);
        const k = scalar;
        const x_1 = u;
        let x_2 = _1n;
        let z_2 = _0n;
        let x_3 = u;
        let z_3 = _1n;
        let swap = _0n;
        for (let t = BigInt(montgomeryBits - 1); t >= _0n; t--) {
          const k_t = k >> t & _1n;
          swap ^= k_t;
          ({ x_2, x_3 } = cswap(swap, x_2, x_3));
          ({ x_2: z_2, x_3: z_3 } = cswap(swap, z_2, z_3));
          swap = k_t;
          const A = x_2 + z_2;
          const AA = modP(A * A);
          const B = x_2 - z_2;
          const BB = modP(B * B);
          const E = AA - BB;
          const C = x_3 + z_3;
          const D = x_3 - z_3;
          const DA = modP(D * A);
          const CB = modP(C * B);
          const dacb = DA + CB;
          const da_cb = DA - CB;
          x_3 = modP(dacb * dacb);
          z_3 = modP(x_1 * modP(da_cb * da_cb));
          x_2 = modP(AA * BB);
          z_2 = modP(E * (AA + modP(a24 * E)));
        }
        ({ x_2, x_3 } = cswap(swap, x_2, x_3));
        ({ x_2: z_2, x_3: z_3 } = cswap(swap, z_2, z_3));
        const z2 = powPminus2(z_2);
        return modP(x_2 * z2);
      }
      return {
        scalarMult,
        scalarMultBase,
        getSharedSecret: (privateKey, publicKey) => scalarMult(privateKey, publicKey),
        getPublicKey: (privateKey) => scalarMultBase(privateKey),
        utils: { randomPrivateKey: () => randomBytes_(fieldLen) },
        GuBytes: GuBytes.slice()
      };
    }
  }
});

// node_modules/@noble/curves/ed25519.js
var require_ed25519 = __commonJS({
  "node_modules/@noble/curves/ed25519.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.hash_to_ristretto255 = exports.hashToRistretto255 = exports.RistrettoPoint = exports.encodeToCurve = exports.hashToCurve = exports.ed25519_hasher = exports.edwardsToMontgomery = exports.x25519 = exports.ed25519ph = exports.ed25519ctx = exports.ed25519 = exports.ED25519_TORSION_SUBGROUP = void 0;
    exports.edwardsToMontgomeryPub = edwardsToMontgomeryPub;
    exports.edwardsToMontgomeryPriv = edwardsToMontgomeryPriv;
    var sha2_js_1 = require_sha2();
    var utils_js_1 = require_utils3();
    var curve_ts_1 = require_curve();
    var edwards_ts_1 = require_edwards();
    var hash_to_curve_ts_1 = require_hash_to_curve();
    var modular_ts_1 = require_modular();
    var montgomery_ts_1 = require_montgomery();
    var utils_ts_1 = require_utils4();
    var _0n = BigInt(0);
    var _1n = BigInt(1);
    var _2n = BigInt(2);
    var _3n = BigInt(3);
    var _5n = BigInt(5);
    var _8n = BigInt(8);
    var ed25519_CURVE = {
      p: BigInt("0x7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffed"),
      n: BigInt("0x1000000000000000000000000000000014def9dea2f79cd65812631a5cf5d3ed"),
      h: _8n,
      a: BigInt("0x7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffec"),
      d: BigInt("0x52036cee2b6ffe738cc740797779e89800700a4d4141d8ab75eb4dca135978a3"),
      Gx: BigInt("0x216936d3cd6e53fec0a4e231fdd6dc5c692cc7609525a7b2c9562d608f25d51a"),
      Gy: BigInt("0x6666666666666666666666666666666666666666666666666666666666666658")
    };
    function ed25519_pow_2_252_3(x) {
      const _10n = BigInt(10), _20n = BigInt(20), _40n = BigInt(40), _80n = BigInt(80);
      const P = ed25519_CURVE.p;
      const x2 = x * x % P;
      const b2 = x2 * x % P;
      const b4 = (0, modular_ts_1.pow2)(b2, _2n, P) * b2 % P;
      const b5 = (0, modular_ts_1.pow2)(b4, _1n, P) * x % P;
      const b10 = (0, modular_ts_1.pow2)(b5, _5n, P) * b5 % P;
      const b20 = (0, modular_ts_1.pow2)(b10, _10n, P) * b10 % P;
      const b40 = (0, modular_ts_1.pow2)(b20, _20n, P) * b20 % P;
      const b80 = (0, modular_ts_1.pow2)(b40, _40n, P) * b40 % P;
      const b160 = (0, modular_ts_1.pow2)(b80, _80n, P) * b80 % P;
      const b240 = (0, modular_ts_1.pow2)(b160, _80n, P) * b80 % P;
      const b250 = (0, modular_ts_1.pow2)(b240, _10n, P) * b10 % P;
      const pow_p_5_8 = (0, modular_ts_1.pow2)(b250, _2n, P) * x % P;
      return { pow_p_5_8, b2 };
    }
    function adjustScalarBytes(bytes) {
      bytes[0] &= 248;
      bytes[31] &= 127;
      bytes[31] |= 64;
      return bytes;
    }
    var ED25519_SQRT_M1 = BigInt("19681161376707505956807079304988542015446066515923890162744021073123829784752");
    function uvRatio(u, v) {
      const P = ed25519_CURVE.p;
      const v3 = (0, modular_ts_1.mod)(v * v * v, P);
      const v7 = (0, modular_ts_1.mod)(v3 * v3 * v, P);
      const pow = ed25519_pow_2_252_3(u * v7).pow_p_5_8;
      let x = (0, modular_ts_1.mod)(u * v3 * pow, P);
      const vx2 = (0, modular_ts_1.mod)(v * x * x, P);
      const root1 = x;
      const root2 = (0, modular_ts_1.mod)(x * ED25519_SQRT_M1, P);
      const useRoot1 = vx2 === u;
      const useRoot2 = vx2 === (0, modular_ts_1.mod)(-u, P);
      const noRoot = vx2 === (0, modular_ts_1.mod)(-u * ED25519_SQRT_M1, P);
      if (useRoot1)
        x = root1;
      if (useRoot2 || noRoot)
        x = root2;
      if ((0, modular_ts_1.isNegativeLE)(x, P))
        x = (0, modular_ts_1.mod)(-x, P);
      return { isValid: useRoot1 || useRoot2, value: x };
    }
    exports.ED25519_TORSION_SUBGROUP = [
      "0100000000000000000000000000000000000000000000000000000000000000",
      "c7176a703d4dd84fba3c0b760d10670f2a2053fa2c39ccc64ec7fd7792ac037a",
      "0000000000000000000000000000000000000000000000000000000000000080",
      "26e8958fc2b227b045c3f489f2ef98f0d5dfac05d3c63339b13802886d53fc05",
      "ecffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff7f",
      "26e8958fc2b227b045c3f489f2ef98f0d5dfac05d3c63339b13802886d53fc85",
      "0000000000000000000000000000000000000000000000000000000000000000",
      "c7176a703d4dd84fba3c0b760d10670f2a2053fa2c39ccc64ec7fd7792ac03fa"
    ];
    var Fp = (() => (0, modular_ts_1.Field)(ed25519_CURVE.p, void 0, true))();
    var ed25519Defaults = (() => ({
      ...ed25519_CURVE,
      Fp,
      hash: sha2_js_1.sha512,
      adjustScalarBytes,
      // dom2
      // Ratio of u to v. Allows us to combine inversion and square root. Uses algo from RFC8032 5.1.3.
      // Constant-time, u/√v
      uvRatio
    }))();
    exports.ed25519 = (() => (0, edwards_ts_1.twistedEdwards)(ed25519Defaults))();
    function ed25519_domain(data, ctx, phflag) {
      if (ctx.length > 255)
        throw new Error("Context is too big");
      return (0, utils_js_1.concatBytes)((0, utils_js_1.utf8ToBytes)("SigEd25519 no Ed25519 collisions"), new Uint8Array([phflag ? 1 : 0, ctx.length]), ctx, data);
    }
    exports.ed25519ctx = (() => (0, edwards_ts_1.twistedEdwards)({
      ...ed25519Defaults,
      domain: ed25519_domain
    }))();
    exports.ed25519ph = (() => (0, edwards_ts_1.twistedEdwards)(Object.assign({}, ed25519Defaults, {
      domain: ed25519_domain,
      prehash: sha2_js_1.sha512
    })))();
    exports.x25519 = (() => {
      const P = ed25519_CURVE.p;
      return (0, montgomery_ts_1.montgomery)({
        P,
        type: "x25519",
        powPminus2: (x) => {
          const { pow_p_5_8, b2 } = ed25519_pow_2_252_3(x);
          return (0, modular_ts_1.mod)((0, modular_ts_1.pow2)(pow_p_5_8, _3n, P) * b2, P);
        },
        adjustScalarBytes
      });
    })();
    function edwardsToMontgomeryPub(edwardsPub) {
      const bpub = (0, utils_ts_1.ensureBytes)("pub", edwardsPub);
      const { y } = exports.ed25519.Point.fromHex(bpub);
      const _1n2 = BigInt(1);
      return Fp.toBytes(Fp.create((_1n2 + y) * Fp.inv(_1n2 - y)));
    }
    exports.edwardsToMontgomery = edwardsToMontgomeryPub;
    function edwardsToMontgomeryPriv(edwardsPriv) {
      const hashed = ed25519Defaults.hash(edwardsPriv.subarray(0, 32));
      return ed25519Defaults.adjustScalarBytes(hashed).subarray(0, 32);
    }
    var ELL2_C1 = (() => (Fp.ORDER + _3n) / _8n)();
    var ELL2_C2 = (() => Fp.pow(_2n, ELL2_C1))();
    var ELL2_C3 = (() => Fp.sqrt(Fp.neg(Fp.ONE)))();
    function map_to_curve_elligator2_curve25519(u) {
      const ELL2_C4 = (Fp.ORDER - _5n) / _8n;
      const ELL2_J = BigInt(486662);
      let tv1 = Fp.sqr(u);
      tv1 = Fp.mul(tv1, _2n);
      let xd = Fp.add(tv1, Fp.ONE);
      let x1n = Fp.neg(ELL2_J);
      let tv2 = Fp.sqr(xd);
      let gxd = Fp.mul(tv2, xd);
      let gx1 = Fp.mul(tv1, ELL2_J);
      gx1 = Fp.mul(gx1, x1n);
      gx1 = Fp.add(gx1, tv2);
      gx1 = Fp.mul(gx1, x1n);
      let tv3 = Fp.sqr(gxd);
      tv2 = Fp.sqr(tv3);
      tv3 = Fp.mul(tv3, gxd);
      tv3 = Fp.mul(tv3, gx1);
      tv2 = Fp.mul(tv2, tv3);
      let y11 = Fp.pow(tv2, ELL2_C4);
      y11 = Fp.mul(y11, tv3);
      let y12 = Fp.mul(y11, ELL2_C3);
      tv2 = Fp.sqr(y11);
      tv2 = Fp.mul(tv2, gxd);
      let e1 = Fp.eql(tv2, gx1);
      let y1 = Fp.cmov(y12, y11, e1);
      let x2n = Fp.mul(x1n, tv1);
      let y21 = Fp.mul(y11, u);
      y21 = Fp.mul(y21, ELL2_C2);
      let y22 = Fp.mul(y21, ELL2_C3);
      let gx2 = Fp.mul(gx1, tv1);
      tv2 = Fp.sqr(y21);
      tv2 = Fp.mul(tv2, gxd);
      let e2 = Fp.eql(tv2, gx2);
      let y2 = Fp.cmov(y22, y21, e2);
      tv2 = Fp.sqr(y1);
      tv2 = Fp.mul(tv2, gxd);
      let e3 = Fp.eql(tv2, gx1);
      let xn = Fp.cmov(x2n, x1n, e3);
      let y = Fp.cmov(y2, y1, e3);
      let e4 = Fp.isOdd(y);
      y = Fp.cmov(y, Fp.neg(y), e3 !== e4);
      return { xMn: xn, xMd: xd, yMn: y, yMd: _1n };
    }
    var ELL2_C1_EDWARDS = (() => (0, modular_ts_1.FpSqrtEven)(Fp, Fp.neg(BigInt(486664))))();
    function map_to_curve_elligator2_edwards25519(u) {
      const { xMn, xMd, yMn, yMd } = map_to_curve_elligator2_curve25519(u);
      let xn = Fp.mul(xMn, yMd);
      xn = Fp.mul(xn, ELL2_C1_EDWARDS);
      let xd = Fp.mul(xMd, yMn);
      let yn = Fp.sub(xMn, xMd);
      let yd = Fp.add(xMn, xMd);
      let tv1 = Fp.mul(xd, yd);
      let e = Fp.eql(tv1, Fp.ZERO);
      xn = Fp.cmov(xn, Fp.ZERO, e);
      xd = Fp.cmov(xd, Fp.ONE, e);
      yn = Fp.cmov(yn, Fp.ONE, e);
      yd = Fp.cmov(yd, Fp.ONE, e);
      const [xd_inv, yd_inv] = (0, modular_ts_1.FpInvertBatch)(Fp, [xd, yd], true);
      return { x: Fp.mul(xn, xd_inv), y: Fp.mul(yn, yd_inv) };
    }
    exports.ed25519_hasher = (() => (0, hash_to_curve_ts_1.createHasher)(exports.ed25519.Point, (scalars) => map_to_curve_elligator2_edwards25519(scalars[0]), {
      DST: "edwards25519_XMD:SHA-512_ELL2_RO_",
      encodeDST: "edwards25519_XMD:SHA-512_ELL2_NU_",
      p: Fp.ORDER,
      m: 1,
      k: 128,
      expand: "xmd",
      hash: sha2_js_1.sha512
    }))();
    exports.hashToCurve = (() => exports.ed25519_hasher.hashToCurve)();
    exports.encodeToCurve = (() => exports.ed25519_hasher.encodeToCurve)();
    function aristp(other) {
      if (!(other instanceof RistPoint))
        throw new Error("RistrettoPoint expected");
    }
    var SQRT_M1 = ED25519_SQRT_M1;
    var SQRT_AD_MINUS_ONE = BigInt("25063068953384623474111414158702152701244531502492656460079210482610430750235");
    var INVSQRT_A_MINUS_D = BigInt("54469307008909316920995813868745141605393597292927456921205312896311721017578");
    var ONE_MINUS_D_SQ = BigInt("1159843021668779879193775521855586647937357759715417654439879720876111806838");
    var D_MINUS_ONE_SQ = BigInt("40440834346308536858101042469323190826248399146238708352240133220865137265952");
    var invertSqrt = (number) => uvRatio(_1n, number);
    var MAX_255B = BigInt("0x7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff");
    var bytes255ToNumberLE = (bytes) => exports.ed25519.CURVE.Fp.create((0, utils_ts_1.bytesToNumberLE)(bytes) & MAX_255B);
    function calcElligatorRistrettoMap(r0) {
      const { d } = exports.ed25519.CURVE;
      const P = exports.ed25519.CURVE.Fp.ORDER;
      const mod = exports.ed25519.CURVE.Fp.create;
      const r = mod(SQRT_M1 * r0 * r0);
      const Ns = mod((r + _1n) * ONE_MINUS_D_SQ);
      let c = BigInt(-1);
      const D = mod((c - d * r) * mod(r + d));
      let { isValid: Ns_D_is_sq, value: s } = uvRatio(Ns, D);
      let s_ = mod(s * r0);
      if (!(0, modular_ts_1.isNegativeLE)(s_, P))
        s_ = mod(-s_);
      if (!Ns_D_is_sq)
        s = s_;
      if (!Ns_D_is_sq)
        c = r;
      const Nt = mod(c * (r - _1n) * D_MINUS_ONE_SQ - D);
      const s2 = s * s;
      const W0 = mod((s + s) * D);
      const W1 = mod(Nt * SQRT_AD_MINUS_ONE);
      const W2 = mod(_1n - s2);
      const W3 = mod(_1n + s2);
      return new exports.ed25519.Point(mod(W0 * W3), mod(W2 * W1), mod(W1 * W3), mod(W0 * W2));
    }
    var RistPoint = class _RistPoint {
      // Private property to discourage combining ExtendedPoint + RistrettoPoint
      // Always use Ristretto encoding/decoding instead.
      constructor(ep) {
        this.ep = ep;
      }
      static fromAffine(ap) {
        return new _RistPoint(exports.ed25519.Point.fromAffine(ap));
      }
      /**
       * Takes uniform output of 64-byte hash function like sha512 and converts it to `RistrettoPoint`.
       * The hash-to-group operation applies Elligator twice and adds the results.
       * **Note:** this is one-way map, there is no conversion from point to hash.
       * Described in [RFC9380](https://www.rfc-editor.org/rfc/rfc9380#appendix-B) and on
       * the [website](https://ristretto.group/formulas/elligator.html).
       * @param hex 64-byte output of a hash function
       */
      static hashToCurve(hex) {
        hex = (0, utils_ts_1.ensureBytes)("ristrettoHash", hex, 64);
        const r1 = bytes255ToNumberLE(hex.slice(0, 32));
        const R1 = calcElligatorRistrettoMap(r1);
        const r2 = bytes255ToNumberLE(hex.slice(32, 64));
        const R2 = calcElligatorRistrettoMap(r2);
        return new _RistPoint(R1.add(R2));
      }
      static fromBytes(bytes) {
        (0, utils_js_1.abytes)(bytes);
        return this.fromHex(bytes);
      }
      /**
       * Converts ristretto-encoded string to ristretto point.
       * Described in [RFC9496](https://www.rfc-editor.org/rfc/rfc9496#name-decode).
       * @param hex Ristretto-encoded 32 bytes. Not every 32-byte string is valid ristretto encoding
       */
      static fromHex(hex) {
        hex = (0, utils_ts_1.ensureBytes)("ristrettoHex", hex, 32);
        const { a, d } = exports.ed25519.CURVE;
        const P = Fp.ORDER;
        const mod = Fp.create;
        const emsg = "RistrettoPoint.fromHex: the hex is not valid encoding of RistrettoPoint";
        const s = bytes255ToNumberLE(hex);
        if (!(0, utils_ts_1.equalBytes)((0, utils_ts_1.numberToBytesLE)(s, 32), hex) || (0, modular_ts_1.isNegativeLE)(s, P))
          throw new Error(emsg);
        const s2 = mod(s * s);
        const u1 = mod(_1n + a * s2);
        const u2 = mod(_1n - a * s2);
        const u1_2 = mod(u1 * u1);
        const u2_2 = mod(u2 * u2);
        const v = mod(a * d * u1_2 - u2_2);
        const { isValid, value: I } = invertSqrt(mod(v * u2_2));
        const Dx = mod(I * u2);
        const Dy = mod(I * Dx * v);
        let x = mod((s + s) * Dx);
        if ((0, modular_ts_1.isNegativeLE)(x, P))
          x = mod(-x);
        const y = mod(u1 * Dy);
        const t = mod(x * y);
        if (!isValid || (0, modular_ts_1.isNegativeLE)(t, P) || y === _0n)
          throw new Error(emsg);
        return new _RistPoint(new exports.ed25519.Point(x, y, _1n, t));
      }
      static msm(points, scalars) {
        const Fn = (0, modular_ts_1.Field)(exports.ed25519.CURVE.n, exports.ed25519.CURVE.nBitLength);
        return (0, curve_ts_1.pippenger)(_RistPoint, Fn, points, scalars);
      }
      /**
       * Encodes ristretto point to Uint8Array.
       * Described in [RFC9496](https://www.rfc-editor.org/rfc/rfc9496#name-encode).
       */
      toBytes() {
        let { ex: x, ey: y, ez: z, et: t } = this.ep;
        const P = Fp.ORDER;
        const mod = Fp.create;
        const u1 = mod(mod(z + y) * mod(z - y));
        const u2 = mod(x * y);
        const u2sq = mod(u2 * u2);
        const { value: invsqrt } = invertSqrt(mod(u1 * u2sq));
        const D1 = mod(invsqrt * u1);
        const D2 = mod(invsqrt * u2);
        const zInv = mod(D1 * D2 * t);
        let D;
        if ((0, modular_ts_1.isNegativeLE)(t * zInv, P)) {
          let _x = mod(y * SQRT_M1);
          let _y = mod(x * SQRT_M1);
          x = _x;
          y = _y;
          D = mod(D1 * INVSQRT_A_MINUS_D);
        } else {
          D = D2;
        }
        if ((0, modular_ts_1.isNegativeLE)(x * zInv, P))
          y = mod(-y);
        let s = mod((z - y) * D);
        if ((0, modular_ts_1.isNegativeLE)(s, P))
          s = mod(-s);
        return (0, utils_ts_1.numberToBytesLE)(s, 32);
      }
      /** @deprecated use `toBytes` */
      toRawBytes() {
        return this.toBytes();
      }
      toHex() {
        return (0, utils_ts_1.bytesToHex)(this.toBytes());
      }
      toString() {
        return this.toHex();
      }
      /**
       * Compares two Ristretto points.
       * Described in [RFC9496](https://www.rfc-editor.org/rfc/rfc9496#name-equals).
       */
      equals(other) {
        aristp(other);
        const { ex: X1, ey: Y1 } = this.ep;
        const { ex: X2, ey: Y2 } = other.ep;
        const mod = Fp.create;
        const one = mod(X1 * Y2) === mod(Y1 * X2);
        const two = mod(Y1 * Y2) === mod(X1 * X2);
        return one || two;
      }
      add(other) {
        aristp(other);
        return new _RistPoint(this.ep.add(other.ep));
      }
      subtract(other) {
        aristp(other);
        return new _RistPoint(this.ep.subtract(other.ep));
      }
      multiply(scalar) {
        return new _RistPoint(this.ep.multiply(scalar));
      }
      multiplyUnsafe(scalar) {
        return new _RistPoint(this.ep.multiplyUnsafe(scalar));
      }
      double() {
        return new _RistPoint(this.ep.double());
      }
      negate() {
        return new _RistPoint(this.ep.negate());
      }
    };
    exports.RistrettoPoint = (() => {
      if (!RistPoint.BASE)
        RistPoint.BASE = new RistPoint(exports.ed25519.Point.BASE);
      if (!RistPoint.ZERO)
        RistPoint.ZERO = new RistPoint(exports.ed25519.Point.ZERO);
      return RistPoint;
    })();
    var hashToRistretto255 = (msg, options) => {
      const d = options.DST;
      const DST = typeof d === "string" ? (0, utils_js_1.utf8ToBytes)(d) : d;
      const uniform_bytes = (0, hash_to_curve_ts_1.expand_message_xmd)(msg, DST, 64, sha2_js_1.sha512);
      const P = RistPoint.hashToCurve(uniform_bytes);
      return P;
    };
    exports.hashToRistretto255 = hashToRistretto255;
    exports.hash_to_ristretto255 = exports.hashToRistretto255;
  }
});

// node_modules/@solana/wallet-standard-util/lib/cjs/util.js
var require_util = __commonJS({
  "node_modules/@solana/wallet-standard-util/lib/cjs/util.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.arraysEqual = arraysEqual3;
    exports.bytesEqual = bytesEqual3;
    function arraysEqual3(a, b) {
      if (a === b)
        return true;
      const length = a.length;
      if (length !== b.length)
        return false;
      for (let i = 0; i < length; i++) {
        if (a[i] !== b[i])
          return false;
      }
      return true;
    }
    function bytesEqual3(a, b) {
      return arraysEqual3(a, b);
    }
  }
});

// node_modules/@solana/wallet-standard-util/lib/cjs/signMessage.js
var require_signMessage2 = __commonJS({
  "node_modules/@solana/wallet-standard-util/lib/cjs/signMessage.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.verifyMessageSignature = verifyMessageSignature2;
    exports.verifySignMessage = verifySignMessage;
    var ed25519_1 = require_ed25519();
    var util_js_1 = require_util();
    function verifyMessageSignature2({ message, signedMessage, signature, publicKey }) {
      return (0, util_js_1.bytesEqual)(message, signedMessage) && ed25519_1.ed25519.verify(signature, signedMessage, publicKey);
    }
    function verifySignMessage(input, output) {
      const { message, account: { publicKey } } = input;
      const { signedMessage, signature } = output;
      return verifyMessageSignature2({ message, signedMessage, signature, publicKey });
    }
  }
});

// node_modules/@solana/wallet-standard-util/lib/cjs/signIn.js
var require_signIn2 = __commonJS({
  "node_modules/@solana/wallet-standard-util/lib/cjs/signIn.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.verifySignIn = verifySignIn;
    exports.deriveSignInMessage = deriveSignInMessage;
    exports.deriveSignInMessageText = deriveSignInMessageText;
    exports.parseSignInMessage = parseSignInMessage;
    exports.parseSignInMessageText = parseSignInMessageText;
    exports.createSignInMessage = createSignInMessage;
    exports.createSignInMessageText = createSignInMessageText;
    var signMessage_js_1 = require_signMessage2();
    var util_js_1 = require_util();
    function verifySignIn(input, output) {
      const { signedMessage, signature, account: { publicKey } } = output;
      const message = deriveSignInMessage(input, output);
      return !!message && (0, signMessage_js_1.verifyMessageSignature)({ message, signedMessage, signature, publicKey });
    }
    function deriveSignInMessage(input, output) {
      const text = deriveSignInMessageText(input, output);
      if (!text)
        return null;
      return new TextEncoder().encode(text);
    }
    function deriveSignInMessageText(input, output) {
      const parsed = parseSignInMessage(output.signedMessage);
      if (!parsed)
        return null;
      if (input.domain && input.domain !== parsed.domain)
        return null;
      if (input.address && input.address !== parsed.address)
        return null;
      if (input.statement !== parsed.statement)
        return null;
      if (input.uri !== parsed.uri)
        return null;
      if (input.version !== parsed.version)
        return null;
      if (input.chainId !== parsed.chainId)
        return null;
      if (input.nonce !== parsed.nonce)
        return null;
      if (input.issuedAt !== parsed.issuedAt)
        return null;
      if (input.expirationTime !== parsed.expirationTime)
        return null;
      if (input.notBefore !== parsed.notBefore)
        return null;
      if (input.requestId !== parsed.requestId)
        return null;
      if (input.resources) {
        if (!parsed.resources)
          return null;
        if (!(0, util_js_1.arraysEqual)(input.resources, parsed.resources))
          return null;
      } else if (parsed.resources)
        return null;
      return createSignInMessageText(parsed);
    }
    function parseSignInMessage(message) {
      const text = new TextDecoder().decode(message);
      return parseSignInMessageText(text);
    }
    var DOMAIN2 = "(?<domain>[^\\n]+?) wants you to sign in with your Solana account:\\n";
    var ADDRESS2 = "(?<address>[^\\n]+)(?:\\n|$)";
    var STATEMENT2 = "(?:\\n(?<statement>[\\S\\s]*?)(?:\\n|$))??";
    var URI2 = "(?:\\nURI: (?<uri>[^\\n]+))?";
    var VERSION2 = "(?:\\nVersion: (?<version>[^\\n]+))?";
    var CHAIN_ID2 = "(?:\\nChain ID: (?<chainId>[^\\n]+))?";
    var NONCE2 = "(?:\\nNonce: (?<nonce>[^\\n]+))?";
    var ISSUED_AT2 = "(?:\\nIssued At: (?<issuedAt>[^\\n]+))?";
    var EXPIRATION_TIME2 = "(?:\\nExpiration Time: (?<expirationTime>[^\\n]+))?";
    var NOT_BEFORE2 = "(?:\\nNot Before: (?<notBefore>[^\\n]+))?";
    var REQUEST_ID2 = "(?:\\nRequest ID: (?<requestId>[^\\n]+))?";
    var RESOURCES2 = "(?:\\nResources:(?<resources>(?:\\n- [^\\n]+)*))?";
    var FIELDS2 = `${URI2}${VERSION2}${CHAIN_ID2}${NONCE2}${ISSUED_AT2}${EXPIRATION_TIME2}${NOT_BEFORE2}${REQUEST_ID2}${RESOURCES2}`;
    var MESSAGE2 = new RegExp(`^${DOMAIN2}${ADDRESS2}${STATEMENT2}${FIELDS2}\\n*$`);
    function parseSignInMessageText(text) {
      var _a;
      const match = MESSAGE2.exec(text);
      if (!match)
        return null;
      const groups = match.groups;
      if (!groups)
        return null;
      return {
        domain: groups.domain,
        address: groups.address,
        statement: groups.statement,
        uri: groups.uri,
        version: groups.version,
        nonce: groups.nonce,
        chainId: groups.chainId,
        issuedAt: groups.issuedAt,
        expirationTime: groups.expirationTime,
        notBefore: groups.notBefore,
        requestId: groups.requestId,
        resources: (_a = groups.resources) === null || _a === void 0 ? void 0 : _a.split("\n- ").slice(1)
      };
    }
    function createSignInMessage(input) {
      const text = createSignInMessageText(input);
      return new TextEncoder().encode(text);
    }
    function createSignInMessageText(input) {
      let message = `${input.domain} wants you to sign in with your Solana account:
`;
      message += `${input.address}`;
      if (input.statement) {
        message += `

${input.statement}`;
      }
      const fields = [];
      if (input.uri) {
        fields.push(`URI: ${input.uri}`);
      }
      if (input.version) {
        fields.push(`Version: ${input.version}`);
      }
      if (input.chainId) {
        fields.push(`Chain ID: ${input.chainId}`);
      }
      if (input.nonce) {
        fields.push(`Nonce: ${input.nonce}`);
      }
      if (input.issuedAt) {
        fields.push(`Issued At: ${input.issuedAt}`);
      }
      if (input.expirationTime) {
        fields.push(`Expiration Time: ${input.expirationTime}`);
      }
      if (input.notBefore) {
        fields.push(`Not Before: ${input.notBefore}`);
      }
      if (input.requestId) {
        fields.push(`Request ID: ${input.requestId}`);
      }
      if (input.resources) {
        fields.push(`Resources:`);
        for (const resource of input.resources) {
          fields.push(`- ${resource}`);
        }
      }
      if (fields.length) {
        message += `

${fields.join("\n")}`;
      }
      return message;
    }
  }
});

// node_modules/@solana/wallet-standard-util/lib/cjs/index.js
var require_cjs5 = __commonJS({
  "node_modules/@solana/wallet-standard-util/lib/cjs/index.js"(exports) {
    "use strict";
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0)
        k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0)
        k2 = k;
      o[k2] = m[k];
    });
    var __exportStar = exports && exports.__exportStar || function(m, exports2) {
      for (var p in m)
        if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports2, p))
          __createBinding(exports2, m, p);
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    __exportStar(require_commitment(), exports);
    __exportStar(require_endpoint(), exports);
    __exportStar(require_signIn2(), exports);
    __exportStar(require_signMessage2(), exports);
  }
});

// node_modules/@solana-mobile/mobile-wallet-adapter-protocol/lib/cjs/index.browser.js
var require_index_browser = __commonJS({
  "node_modules/@solana-mobile/mobile-wallet-adapter-protocol/lib/cjs/index.browser.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    var walletStandardUtil = require_cjs5();
    var SolanaMobileWalletAdapterErrorCode = {
      ERROR_ASSOCIATION_PORT_OUT_OF_RANGE: "ERROR_ASSOCIATION_PORT_OUT_OF_RANGE",
      ERROR_REFLECTOR_ID_OUT_OF_RANGE: "ERROR_REFLECTOR_ID_OUT_OF_RANGE",
      ERROR_FORBIDDEN_WALLET_BASE_URL: "ERROR_FORBIDDEN_WALLET_BASE_URL",
      ERROR_SECURE_CONTEXT_REQUIRED: "ERROR_SECURE_CONTEXT_REQUIRED",
      ERROR_SESSION_CLOSED: "ERROR_SESSION_CLOSED",
      ERROR_SESSION_TIMEOUT: "ERROR_SESSION_TIMEOUT",
      ERROR_WALLET_NOT_FOUND: "ERROR_WALLET_NOT_FOUND",
      ERROR_INVALID_PROTOCOL_VERSION: "ERROR_INVALID_PROTOCOL_VERSION",
      ERROR_BROWSER_NOT_SUPPORTED: "ERROR_BROWSER_NOT_SUPPORTED"
    };
    var SolanaMobileWalletAdapterError = class extends Error {
      constructor(...args) {
        const [code, message, data] = args;
        super(message);
        this.code = code;
        this.data = data;
        this.name = "SolanaMobileWalletAdapterError";
      }
    };
    var SolanaMobileWalletAdapterProtocolErrorCode = {
      // Keep these in sync with `mobilewalletadapter/common/ProtocolContract.java`.
      ERROR_AUTHORIZATION_FAILED: -1,
      ERROR_INVALID_PAYLOADS: -2,
      ERROR_NOT_SIGNED: -3,
      ERROR_NOT_SUBMITTED: -4,
      ERROR_TOO_MANY_PAYLOADS: -5,
      ERROR_ATTEST_ORIGIN_ANDROID: -100
    };
    var SolanaMobileWalletAdapterProtocolError = class extends Error {
      constructor(...args) {
        const [jsonRpcMessageId, code, message, data] = args;
        super(message);
        this.code = code;
        this.data = data;
        this.jsonRpcMessageId = jsonRpcMessageId;
        this.name = "SolanaMobileWalletAdapterProtocolError";
      }
    };
    function __awaiter2(thisArg, _arguments, P, generator) {
      function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
          resolve(value);
        });
      }
      return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
          try {
            step(generator.next(value));
          } catch (e) {
            reject(e);
          }
        }
        function rejected(value) {
          try {
            step(generator["throw"](value));
          } catch (e) {
            reject(e);
          }
        }
        function step(result) {
          result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
      });
    }
    function encode(input) {
      return window.btoa(input);
    }
    function fromUint8Array2(byteArray, urlsafe) {
      const base64 = window.btoa(String.fromCharCode.call(null, ...byteArray));
      if (urlsafe) {
        return base64.replace(/\+/g, "-").replace(/\//g, "_").replace(/=+$/, "");
      } else
        return base64;
    }
    function toUint8Array2(base64EncodedByteArray) {
      return new Uint8Array(window.atob(base64EncodedByteArray).split("").map((c) => c.charCodeAt(0)));
    }
    function createHelloReq(ecdhPublicKey, associationKeypairPrivateKey) {
      return __awaiter2(this, void 0, void 0, function* () {
        const publicKeyBuffer = yield crypto.subtle.exportKey("raw", ecdhPublicKey);
        const signatureBuffer = yield crypto.subtle.sign({ hash: "SHA-256", name: "ECDSA" }, associationKeypairPrivateKey, publicKeyBuffer);
        const response = new Uint8Array(publicKeyBuffer.byteLength + signatureBuffer.byteLength);
        response.set(new Uint8Array(publicKeyBuffer), 0);
        response.set(new Uint8Array(signatureBuffer), publicKeyBuffer.byteLength);
        return response;
      });
    }
    function createSIWSMessage(payload) {
      return walletStandardUtil.createSignInMessageText(payload);
    }
    function createSIWSMessageBase64(payload) {
      return encode(createSIWSMessage(payload));
    }
    var SolanaSignTransactions = "solana:signTransactions";
    var SolanaCloneAuthorization = "solana:cloneAuthorization";
    var SolanaSignInWithSolana = "solana:signInWithSolana";
    function createMobileWalletProxy(protocolVersion, protocolRequestHandler) {
      return new Proxy({}, {
        get(target, p) {
          if (p === "then") {
            return null;
          }
          if (target[p] == null) {
            target[p] = function(inputParams) {
              return __awaiter2(this, void 0, void 0, function* () {
                const { method, params } = handleMobileWalletRequest(p, inputParams, protocolVersion);
                const result = yield protocolRequestHandler(method, params);
                if (method === "authorize" && params.sign_in_payload && !result.sign_in_result) {
                  result["sign_in_result"] = yield signInFallback(params.sign_in_payload, result, protocolRequestHandler);
                }
                return handleMobileWalletResponse(p, result, protocolVersion);
              });
            };
          }
          return target[p];
        },
        defineProperty() {
          return false;
        },
        deleteProperty() {
          return false;
        }
      });
    }
    function handleMobileWalletRequest(methodName, methodParams, protocolVersion) {
      let params = methodParams;
      let method = methodName.toString().replace(/[A-Z]/g, (letter) => `_${letter.toLowerCase()}`).toLowerCase();
      switch (methodName) {
        case "authorize": {
          let { chain } = params;
          if (protocolVersion === "legacy") {
            switch (chain) {
              case "solana:testnet": {
                chain = "testnet";
                break;
              }
              case "solana:devnet": {
                chain = "devnet";
                break;
              }
              case "solana:mainnet": {
                chain = "mainnet-beta";
                break;
              }
              default: {
                chain = params.cluster;
              }
            }
            params.cluster = chain;
          } else {
            switch (chain) {
              case "testnet":
              case "devnet": {
                chain = `solana:${chain}`;
                break;
              }
              case "mainnet-beta": {
                chain = "solana:mainnet";
                break;
              }
            }
            params.chain = chain;
          }
        }
        case "reauthorize": {
          const { auth_token, identity } = params;
          if (auth_token) {
            switch (protocolVersion) {
              case "legacy": {
                method = "reauthorize";
                params = { auth_token, identity };
                break;
              }
              default: {
                method = "authorize";
                break;
              }
            }
          }
          break;
        }
      }
      return { method, params };
    }
    function handleMobileWalletResponse(method, response, protocolVersion) {
      switch (method) {
        case "getCapabilities": {
          const capabilities = response;
          switch (protocolVersion) {
            case "legacy": {
              const features = [SolanaSignTransactions];
              if (capabilities.supports_clone_authorization === true) {
                features.push(SolanaCloneAuthorization);
              }
              return Object.assign(Object.assign({}, capabilities), { features });
            }
            case "v1": {
              return Object.assign(Object.assign({}, capabilities), { supports_sign_and_send_transactions: true, supports_clone_authorization: capabilities.features.includes(SolanaCloneAuthorization) });
            }
          }
        }
      }
      return response;
    }
    function signInFallback(signInPayload, authorizationResult, protocolRequestHandler) {
      var _a;
      return __awaiter2(this, void 0, void 0, function* () {
        const domain = (_a = signInPayload.domain) !== null && _a !== void 0 ? _a : window.location.host;
        const address = authorizationResult.accounts[0].address;
        const siwsMessage = createSIWSMessageBase64(Object.assign(Object.assign({}, signInPayload), { domain, address }));
        const signMessageResult = yield protocolRequestHandler("sign_messages", {
          addresses: [address],
          payloads: [siwsMessage]
        });
        const signInResult = {
          address,
          signed_message: siwsMessage,
          signature: signMessageResult.signed_payloads[0].slice(siwsMessage.length)
        };
        return signInResult;
      });
    }
    var SEQUENCE_NUMBER_BYTES = 4;
    function createSequenceNumberVector(sequenceNumber) {
      if (sequenceNumber >= **********) {
        throw new Error("Outbound sequence number overflow. The maximum sequence number is 32-bytes.");
      }
      const byteArray = new ArrayBuffer(SEQUENCE_NUMBER_BYTES);
      const view = new DataView(byteArray);
      view.setUint32(
        0,
        sequenceNumber,
        /* littleEndian */
        false
      );
      return new Uint8Array(byteArray);
    }
    var INITIALIZATION_VECTOR_BYTES = 12;
    var ENCODED_PUBLIC_KEY_LENGTH_BYTES = 65;
    function encryptMessage(plaintext, sequenceNumber, sharedSecret) {
      return __awaiter2(this, void 0, void 0, function* () {
        const sequenceNumberVector = createSequenceNumberVector(sequenceNumber);
        const initializationVector = new Uint8Array(INITIALIZATION_VECTOR_BYTES);
        crypto.getRandomValues(initializationVector);
        const ciphertext = yield crypto.subtle.encrypt(getAlgorithmParams(sequenceNumberVector, initializationVector), sharedSecret, new TextEncoder().encode(plaintext));
        const response = new Uint8Array(sequenceNumberVector.byteLength + initializationVector.byteLength + ciphertext.byteLength);
        response.set(new Uint8Array(sequenceNumberVector), 0);
        response.set(new Uint8Array(initializationVector), sequenceNumberVector.byteLength);
        response.set(new Uint8Array(ciphertext), sequenceNumberVector.byteLength + initializationVector.byteLength);
        return response;
      });
    }
    function decryptMessage(message, sharedSecret) {
      return __awaiter2(this, void 0, void 0, function* () {
        const sequenceNumberVector = message.slice(0, SEQUENCE_NUMBER_BYTES);
        const initializationVector = message.slice(SEQUENCE_NUMBER_BYTES, SEQUENCE_NUMBER_BYTES + INITIALIZATION_VECTOR_BYTES);
        const ciphertext = message.slice(SEQUENCE_NUMBER_BYTES + INITIALIZATION_VECTOR_BYTES);
        const plaintextBuffer = yield crypto.subtle.decrypt(getAlgorithmParams(sequenceNumberVector, initializationVector), sharedSecret, ciphertext);
        const plaintext = getUtf8Decoder().decode(plaintextBuffer);
        return plaintext;
      });
    }
    function getAlgorithmParams(sequenceNumber, initializationVector) {
      return {
        additionalData: sequenceNumber,
        iv: initializationVector,
        name: "AES-GCM",
        tagLength: 128
        // 16 byte tag => 128 bits
      };
    }
    var _utf8Decoder;
    function getUtf8Decoder() {
      if (_utf8Decoder === void 0) {
        _utf8Decoder = new TextDecoder("utf-8");
      }
      return _utf8Decoder;
    }
    function generateAssociationKeypair() {
      return __awaiter2(this, void 0, void 0, function* () {
        return yield crypto.subtle.generateKey(
          {
            name: "ECDSA",
            namedCurve: "P-256"
          },
          false,
          ["sign"]
          /* keyUsages */
        );
      });
    }
    function generateECDHKeypair() {
      return __awaiter2(this, void 0, void 0, function* () {
        return yield crypto.subtle.generateKey(
          {
            name: "ECDH",
            namedCurve: "P-256"
          },
          false,
          ["deriveKey", "deriveBits"]
          /* keyUsages */
        );
      });
    }
    function arrayBufferToBase64String(buffer) {
      let binary = "";
      const bytes = new Uint8Array(buffer);
      const len = bytes.byteLength;
      for (let ii = 0; ii < len; ii++) {
        binary += String.fromCharCode(bytes[ii]);
      }
      return window.btoa(binary);
    }
    function getRandomAssociationPort() {
      return assertAssociationPort(49152 + Math.floor(Math.random() * (65535 - 49152 + 1)));
    }
    function assertAssociationPort(port) {
      if (port < 49152 || port > 65535) {
        throw new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_ASSOCIATION_PORT_OUT_OF_RANGE, `Association port number must be between 49152 and 65535. ${port} given.`, { port });
      }
      return port;
    }
    function getStringWithURLUnsafeCharactersReplaced(unsafeBase64EncodedString) {
      return unsafeBase64EncodedString.replace(/[/+=]/g, (m) => ({
        "/": "_",
        "+": "-",
        "=": "."
      })[m]);
    }
    var INTENT_NAME = "solana-wallet";
    function getPathParts(pathString) {
      return pathString.replace(/(^\/+|\/+$)/g, "").split("/");
    }
    function getIntentURL(methodPathname, intentUrlBase) {
      let baseUrl = null;
      if (intentUrlBase) {
        try {
          baseUrl = new URL(intentUrlBase);
        } catch (_a) {
        }
        if ((baseUrl === null || baseUrl === void 0 ? void 0 : baseUrl.protocol) !== "https:") {
          throw new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_FORBIDDEN_WALLET_BASE_URL, "Base URLs supplied by wallets must be valid `https` URLs");
        }
      }
      baseUrl || (baseUrl = new URL(`${INTENT_NAME}:/`));
      const pathname = methodPathname.startsWith("/") ? (
        // Method is an absolute path. Replace it wholesale.
        methodPathname
      ) : (
        // Method is a relative path. Merge it with the existing one.
        [...getPathParts(baseUrl.pathname), ...getPathParts(methodPathname)].join("/")
      );
      return new URL(pathname, baseUrl);
    }
    function getAssociateAndroidIntentURL(associationPublicKey, putativePort, associationURLBase, protocolVersions = ["v1"]) {
      return __awaiter2(this, void 0, void 0, function* () {
        const associationPort = assertAssociationPort(putativePort);
        const exportedKey = yield crypto.subtle.exportKey("raw", associationPublicKey);
        const encodedKey = arrayBufferToBase64String(exportedKey);
        const url = getIntentURL("v1/associate/local", associationURLBase);
        url.searchParams.set("association", getStringWithURLUnsafeCharactersReplaced(encodedKey));
        url.searchParams.set("port", `${associationPort}`);
        protocolVersions.forEach((version) => {
          url.searchParams.set("v", version);
        });
        return url;
      });
    }
    function getRemoteAssociateAndroidIntentURL(associationPublicKey, hostAuthority, reflectorId, associationURLBase, protocolVersions = ["v1"]) {
      return __awaiter2(this, void 0, void 0, function* () {
        const exportedKey = yield crypto.subtle.exportKey("raw", associationPublicKey);
        const encodedKey = arrayBufferToBase64String(exportedKey);
        const url = getIntentURL("v1/associate/remote", associationURLBase);
        url.searchParams.set("association", getStringWithURLUnsafeCharactersReplaced(encodedKey));
        url.searchParams.set("reflector", `${hostAuthority}`);
        url.searchParams.set("id", `${fromUint8Array2(reflectorId, true)}`);
        protocolVersions.forEach((version) => {
          url.searchParams.set("v", version);
        });
        return url;
      });
    }
    function encryptJsonRpcMessage(jsonRpcMessage, sharedSecret) {
      return __awaiter2(this, void 0, void 0, function* () {
        const plaintext = JSON.stringify(jsonRpcMessage);
        const sequenceNumber = jsonRpcMessage.id;
        return encryptMessage(plaintext, sequenceNumber, sharedSecret);
      });
    }
    function decryptJsonRpcMessage(message, sharedSecret) {
      return __awaiter2(this, void 0, void 0, function* () {
        const plaintext = yield decryptMessage(message, sharedSecret);
        const jsonRpcMessage = JSON.parse(plaintext);
        if (Object.hasOwnProperty.call(jsonRpcMessage, "error")) {
          throw new SolanaMobileWalletAdapterProtocolError(jsonRpcMessage.id, jsonRpcMessage.error.code, jsonRpcMessage.error.message);
        }
        return jsonRpcMessage;
      });
    }
    function parseHelloRsp(payloadBuffer, associationPublicKey, ecdhPrivateKey) {
      return __awaiter2(this, void 0, void 0, function* () {
        const [associationPublicKeyBuffer, walletPublicKey] = yield Promise.all([
          crypto.subtle.exportKey("raw", associationPublicKey),
          crypto.subtle.importKey(
            "raw",
            payloadBuffer.slice(0, ENCODED_PUBLIC_KEY_LENGTH_BYTES),
            { name: "ECDH", namedCurve: "P-256" },
            false,
            []
            /* keyUsages */
          )
        ]);
        const sharedSecret = yield crypto.subtle.deriveBits({ name: "ECDH", public: walletPublicKey }, ecdhPrivateKey, 256);
        const ecdhSecretKey = yield crypto.subtle.importKey(
          "raw",
          sharedSecret,
          "HKDF",
          false,
          ["deriveKey"]
          /* keyUsages */
        );
        const aesKeyMaterialVal = yield crypto.subtle.deriveKey({
          name: "HKDF",
          hash: "SHA-256",
          salt: new Uint8Array(associationPublicKeyBuffer),
          info: new Uint8Array()
        }, ecdhSecretKey, { name: "AES-GCM", length: 128 }, false, ["encrypt", "decrypt"]);
        return aesKeyMaterialVal;
      });
    }
    function parseSessionProps(message, sharedSecret) {
      return __awaiter2(this, void 0, void 0, function* () {
        const plaintext = yield decryptMessage(message, sharedSecret);
        const jsonProperties = JSON.parse(plaintext);
        let protocolVersion = "legacy";
        if (Object.hasOwnProperty.call(jsonProperties, "v")) {
          switch (jsonProperties.v) {
            case 1:
            case "1":
            case "v1":
              protocolVersion = "v1";
              break;
            case "legacy":
              protocolVersion = "legacy";
              break;
            default:
              throw new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_INVALID_PROTOCOL_VERSION, `Unknown/unsupported protocol version: ${jsonProperties.v}`);
          }
        }
        return {
          protocol_version: protocolVersion
        };
      });
    }
    var Browser = {
      Firefox: 0,
      Other: 1
    };
    function assertUnreachable(x) {
      return x;
    }
    function getBrowser() {
      return navigator.userAgent.indexOf("Firefox/") !== -1 ? Browser.Firefox : Browser.Other;
    }
    function getDetectionPromise() {
      return new Promise((resolve, reject) => {
        function cleanup() {
          clearTimeout(timeoutId);
          window.removeEventListener("blur", handleBlur);
        }
        function handleBlur() {
          cleanup();
          resolve();
        }
        window.addEventListener("blur", handleBlur);
        const timeoutId = setTimeout(() => {
          cleanup();
          reject();
        }, 3e3);
      });
    }
    var _frame = null;
    function launchUrlThroughHiddenFrame(url) {
      if (_frame == null) {
        _frame = document.createElement("iframe");
        _frame.style.display = "none";
        document.body.appendChild(_frame);
      }
      _frame.contentWindow.location.href = url.toString();
    }
    function launchAssociation(associationUrl) {
      return __awaiter2(this, void 0, void 0, function* () {
        if (associationUrl.protocol === "https:") {
          window.location.assign(associationUrl);
        } else {
          try {
            const browser = getBrowser();
            switch (browser) {
              case Browser.Firefox:
                launchUrlThroughHiddenFrame(associationUrl);
                break;
              case Browser.Other: {
                const detectionPromise = getDetectionPromise();
                window.location.assign(associationUrl);
                yield detectionPromise;
                break;
              }
              default:
                assertUnreachable(browser);
            }
          } catch (e) {
            throw new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_WALLET_NOT_FOUND, "Found no installed wallet that supports the mobile wallet protocol.");
          }
        }
      });
    }
    function startSession(associationPublicKey, associationURLBase) {
      return __awaiter2(this, void 0, void 0, function* () {
        const randomAssociationPort = getRandomAssociationPort();
        const associationUrl = yield getAssociateAndroidIntentURL(associationPublicKey, randomAssociationPort, associationURLBase);
        yield launchAssociation(associationUrl);
        return randomAssociationPort;
      });
    }
    var WEBSOCKET_CONNECTION_CONFIG = {
      /**
       * 300 milliseconds is a generally accepted threshold for what someone
       * would consider an acceptable response time for a user interface
       * after having performed a low-attention tapping task. We set the initial
       * interval at which we wait for the wallet to set up the websocket at
       * half this, as per the Nyquist frequency, with a progressive backoff
       * sequence from there. The total wait time is 30s, which allows for the
       * user to be presented with a disambiguation dialog, select a wallet, and
       * for the wallet app to subsequently start.
       */
      retryDelayScheduleMs: [150, 150, 200, 500, 500, 750, 750, 1e3],
      timeoutMs: 3e4
    };
    var WEBSOCKET_PROTOCOL_BINARY = "com.solana.mobilewalletadapter.v1";
    var WEBSOCKET_PROTOCOL_BASE64 = "com.solana.mobilewalletadapter.v1.base64";
    function assertSecureContext() {
      if (typeof window === "undefined" || window.isSecureContext !== true) {
        throw new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_SECURE_CONTEXT_REQUIRED, "The mobile wallet adapter protocol must be used in a secure context (`https`).");
      }
    }
    function assertSecureEndpointSpecificURI(walletUriBase) {
      let url;
      try {
        url = new URL(walletUriBase);
      } catch (_a) {
        throw new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_FORBIDDEN_WALLET_BASE_URL, "Invalid base URL supplied by wallet");
      }
      if (url.protocol !== "https:") {
        throw new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_FORBIDDEN_WALLET_BASE_URL, "Base URLs supplied by wallets must be valid `https` URLs");
      }
    }
    function getSequenceNumberFromByteArray(byteArray) {
      const view = new DataView(byteArray);
      return view.getUint32(
        0,
        /* littleEndian */
        false
      );
    }
    function decodeVarLong(byteArray) {
      var bytes = new Uint8Array(byteArray), l = byteArray.byteLength, limit = 10, value = 0, offset = 0, b;
      do {
        if (offset >= l || offset > limit)
          throw new RangeError("Failed to decode varint");
        b = bytes[offset++];
        value |= (b & 127) << 7 * offset;
      } while (b >= 128);
      return { value, offset };
    }
    function getReflectorIdFromByteArray(byteArray) {
      let { value: length, offset } = decodeVarLong(byteArray);
      return new Uint8Array(byteArray.slice(offset, offset + length));
    }
    function transact2(callback, config) {
      return __awaiter2(this, void 0, void 0, function* () {
        assertSecureContext();
        const associationKeypair = yield generateAssociationKeypair();
        const sessionPort = yield startSession(associationKeypair.publicKey, config === null || config === void 0 ? void 0 : config.baseUri);
        const websocketURL = `ws://localhost:${sessionPort}/solana-wallet`;
        let connectionStartTime;
        const getNextRetryDelayMs = (() => {
          const schedule = [...WEBSOCKET_CONNECTION_CONFIG.retryDelayScheduleMs];
          return () => schedule.length > 1 ? schedule.shift() : schedule[0];
        })();
        let nextJsonRpcMessageId = 1;
        let lastKnownInboundSequenceNumber = 0;
        let state = { __type: "disconnected" };
        return new Promise((resolve, reject) => {
          let socket;
          const jsonRpcResponsePromises = {};
          const handleOpen = () => __awaiter2(this, void 0, void 0, function* () {
            if (state.__type !== "connecting") {
              console.warn(`Expected adapter state to be \`connecting\` at the moment the websocket opens. Got \`${state.__type}\`.`);
              return;
            }
            socket.removeEventListener("open", handleOpen);
            const { associationKeypair: associationKeypair2 } = state;
            const ecdhKeypair = yield generateECDHKeypair();
            socket.send(yield createHelloReq(ecdhKeypair.publicKey, associationKeypair2.privateKey));
            state = {
              __type: "hello_req_sent",
              associationPublicKey: associationKeypair2.publicKey,
              ecdhPrivateKey: ecdhKeypair.privateKey
            };
          });
          const handleClose = (evt) => {
            if (evt.wasClean) {
              state = { __type: "disconnected" };
            } else {
              reject(new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_SESSION_CLOSED, `The wallet session dropped unexpectedly (${evt.code}: ${evt.reason}).`, { closeEvent: evt }));
            }
            disposeSocket();
          };
          const handleError = (_evt) => __awaiter2(this, void 0, void 0, function* () {
            disposeSocket();
            if (Date.now() - connectionStartTime >= WEBSOCKET_CONNECTION_CONFIG.timeoutMs) {
              reject(new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_SESSION_TIMEOUT, `Failed to connect to the wallet websocket at ${websocketURL}.`));
            } else {
              yield new Promise((resolve2) => {
                const retryDelayMs = getNextRetryDelayMs();
                retryWaitTimeoutId = window.setTimeout(resolve2, retryDelayMs);
              });
              attemptSocketConnection();
            }
          });
          const handleMessage = (evt) => __awaiter2(this, void 0, void 0, function* () {
            const responseBuffer = yield evt.data.arrayBuffer();
            switch (state.__type) {
              case "connecting":
                if (responseBuffer.byteLength !== 0) {
                  throw new Error("Encountered unexpected message while connecting");
                }
                const ecdhKeypair = yield generateECDHKeypair();
                socket.send(yield createHelloReq(ecdhKeypair.publicKey, associationKeypair.privateKey));
                state = {
                  __type: "hello_req_sent",
                  associationPublicKey: associationKeypair.publicKey,
                  ecdhPrivateKey: ecdhKeypair.privateKey
                };
                break;
              case "connected":
                try {
                  const sequenceNumberVector = responseBuffer.slice(0, SEQUENCE_NUMBER_BYTES);
                  const sequenceNumber = getSequenceNumberFromByteArray(sequenceNumberVector);
                  if (sequenceNumber !== lastKnownInboundSequenceNumber + 1) {
                    throw new Error("Encrypted message has invalid sequence number");
                  }
                  lastKnownInboundSequenceNumber = sequenceNumber;
                  const jsonRpcMessage = yield decryptJsonRpcMessage(responseBuffer, state.sharedSecret);
                  const responsePromise = jsonRpcResponsePromises[jsonRpcMessage.id];
                  delete jsonRpcResponsePromises[jsonRpcMessage.id];
                  responsePromise.resolve(jsonRpcMessage.result);
                } catch (e) {
                  if (e instanceof SolanaMobileWalletAdapterProtocolError) {
                    const responsePromise = jsonRpcResponsePromises[e.jsonRpcMessageId];
                    delete jsonRpcResponsePromises[e.jsonRpcMessageId];
                    responsePromise.reject(e);
                  } else {
                    throw e;
                  }
                }
                break;
              case "hello_req_sent": {
                if (responseBuffer.byteLength === 0) {
                  const ecdhKeypair2 = yield generateECDHKeypair();
                  socket.send(yield createHelloReq(ecdhKeypair2.publicKey, associationKeypair.privateKey));
                  state = {
                    __type: "hello_req_sent",
                    associationPublicKey: associationKeypair.publicKey,
                    ecdhPrivateKey: ecdhKeypair2.privateKey
                  };
                  break;
                }
                const sharedSecret = yield parseHelloRsp(responseBuffer, state.associationPublicKey, state.ecdhPrivateKey);
                const sessionPropertiesBuffer = responseBuffer.slice(ENCODED_PUBLIC_KEY_LENGTH_BYTES);
                const sessionProperties = sessionPropertiesBuffer.byteLength !== 0 ? yield (() => __awaiter2(this, void 0, void 0, function* () {
                  const sequenceNumberVector = sessionPropertiesBuffer.slice(0, SEQUENCE_NUMBER_BYTES);
                  const sequenceNumber = getSequenceNumberFromByteArray(sequenceNumberVector);
                  if (sequenceNumber !== lastKnownInboundSequenceNumber + 1) {
                    throw new Error("Encrypted message has invalid sequence number");
                  }
                  lastKnownInboundSequenceNumber = sequenceNumber;
                  return parseSessionProps(sessionPropertiesBuffer, sharedSecret);
                }))() : { protocol_version: "legacy" };
                state = { __type: "connected", sharedSecret, sessionProperties };
                const wallet = createMobileWalletProxy(sessionProperties.protocol_version, (method, params) => __awaiter2(this, void 0, void 0, function* () {
                  const id = nextJsonRpcMessageId++;
                  socket.send(yield encryptJsonRpcMessage({
                    id,
                    jsonrpc: "2.0",
                    method,
                    params: params !== null && params !== void 0 ? params : {}
                  }, sharedSecret));
                  return new Promise((resolve2, reject2) => {
                    jsonRpcResponsePromises[id] = {
                      resolve(result) {
                        switch (method) {
                          case "authorize":
                          case "reauthorize": {
                            const { wallet_uri_base } = result;
                            if (wallet_uri_base != null) {
                              try {
                                assertSecureEndpointSpecificURI(wallet_uri_base);
                              } catch (e) {
                                reject2(e);
                                return;
                              }
                            }
                            break;
                          }
                        }
                        resolve2(result);
                      },
                      reject: reject2
                    };
                  });
                }));
                try {
                  resolve(yield callback(wallet));
                } catch (e) {
                  reject(e);
                } finally {
                  disposeSocket();
                  socket.close();
                }
                break;
              }
            }
          });
          let disposeSocket;
          let retryWaitTimeoutId;
          const attemptSocketConnection = () => {
            if (disposeSocket) {
              disposeSocket();
            }
            state = { __type: "connecting", associationKeypair };
            if (connectionStartTime === void 0) {
              connectionStartTime = Date.now();
            }
            socket = new WebSocket(websocketURL, [WEBSOCKET_PROTOCOL_BINARY]);
            socket.addEventListener("open", handleOpen);
            socket.addEventListener("close", handleClose);
            socket.addEventListener("error", handleError);
            socket.addEventListener("message", handleMessage);
            disposeSocket = () => {
              window.clearTimeout(retryWaitTimeoutId);
              socket.removeEventListener("open", handleOpen);
              socket.removeEventListener("close", handleClose);
              socket.removeEventListener("error", handleError);
              socket.removeEventListener("message", handleMessage);
            };
          };
          attemptSocketConnection();
        });
      });
    }
    function startRemoteScenario2(config) {
      return __awaiter2(this, void 0, void 0, function* () {
        assertSecureContext();
        const associationKeypair = yield generateAssociationKeypair();
        const websocketURL = `wss://${config === null || config === void 0 ? void 0 : config.remoteHostAuthority}/reflect`;
        let connectionStartTime;
        const getNextRetryDelayMs = (() => {
          const schedule = [...WEBSOCKET_CONNECTION_CONFIG.retryDelayScheduleMs];
          return () => schedule.length > 1 ? schedule.shift() : schedule[0];
        })();
        let nextJsonRpcMessageId = 1;
        let lastKnownInboundSequenceNumber = 0;
        let encoding;
        let state = { __type: "disconnected" };
        let socket;
        let disposeSocket;
        let decodeBytes = (evt) => __awaiter2(this, void 0, void 0, function* () {
          if (encoding == "base64") {
            const message = yield evt.data;
            return toUint8Array2(message).buffer;
          } else {
            return yield evt.data.arrayBuffer();
          }
        });
        const associationUrl = yield new Promise((resolve, reject) => {
          const handleOpen = () => __awaiter2(this, void 0, void 0, function* () {
            if (state.__type !== "connecting") {
              console.warn(`Expected adapter state to be \`connecting\` at the moment the websocket opens. Got \`${state.__type}\`.`);
              return;
            }
            if (socket.protocol.includes(WEBSOCKET_PROTOCOL_BASE64)) {
              encoding = "base64";
            } else {
              encoding = "binary";
            }
            socket.removeEventListener("open", handleOpen);
          });
          const handleClose2 = (evt) => {
            if (evt.wasClean) {
              state = { __type: "disconnected" };
            } else {
              reject(new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_SESSION_CLOSED, `The wallet session dropped unexpectedly (${evt.code}: ${evt.reason}).`, { closeEvent: evt }));
            }
            disposeSocket();
          };
          const handleError = (_evt) => __awaiter2(this, void 0, void 0, function* () {
            disposeSocket();
            if (Date.now() - connectionStartTime >= WEBSOCKET_CONNECTION_CONFIG.timeoutMs) {
              reject(new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_SESSION_TIMEOUT, `Failed to connect to the wallet websocket at ${websocketURL}.`));
            } else {
              yield new Promise((resolve2) => {
                const retryDelayMs = getNextRetryDelayMs();
                retryWaitTimeoutId = window.setTimeout(resolve2, retryDelayMs);
              });
              attemptSocketConnection();
            }
          });
          const handleReflectorIdMessage = (evt) => __awaiter2(this, void 0, void 0, function* () {
            const responseBuffer = yield decodeBytes(evt);
            if (state.__type === "connecting") {
              if (responseBuffer.byteLength == 0) {
                throw new Error("Encountered unexpected message while connecting");
              }
              const reflectorId = getReflectorIdFromByteArray(responseBuffer);
              state = {
                __type: "reflector_id_received",
                reflectorId
              };
              const associationUrl2 = yield getRemoteAssociateAndroidIntentURL(associationKeypair.publicKey, config.remoteHostAuthority, reflectorId, config === null || config === void 0 ? void 0 : config.baseUri);
              socket.removeEventListener("message", handleReflectorIdMessage);
              resolve(associationUrl2);
            }
          });
          let retryWaitTimeoutId;
          const attemptSocketConnection = () => {
            if (disposeSocket) {
              disposeSocket();
            }
            state = { __type: "connecting", associationKeypair };
            if (connectionStartTime === void 0) {
              connectionStartTime = Date.now();
            }
            socket = new WebSocket(websocketURL, [WEBSOCKET_PROTOCOL_BINARY, WEBSOCKET_PROTOCOL_BASE64]);
            socket.addEventListener("open", handleOpen);
            socket.addEventListener("close", handleClose2);
            socket.addEventListener("error", handleError);
            socket.addEventListener("message", handleReflectorIdMessage);
            disposeSocket = () => {
              window.clearTimeout(retryWaitTimeoutId);
              socket.removeEventListener("open", handleOpen);
              socket.removeEventListener("close", handleClose2);
              socket.removeEventListener("error", handleError);
              socket.removeEventListener("message", handleReflectorIdMessage);
            };
          };
          attemptSocketConnection();
        });
        let sessionEstablished = false;
        let handleClose;
        return { associationUrl, close: () => {
          socket.close();
          handleClose();
        }, wallet: new Promise((resolve, reject) => {
          const jsonRpcResponsePromises = {};
          const handleMessage = (evt) => __awaiter2(this, void 0, void 0, function* () {
            const responseBuffer = yield decodeBytes(evt);
            switch (state.__type) {
              case "reflector_id_received":
                if (responseBuffer.byteLength !== 0) {
                  throw new Error("Encountered unexpected message while awaiting reflection");
                }
                const ecdhKeypair = yield generateECDHKeypair();
                const binaryMsg = yield createHelloReq(ecdhKeypair.publicKey, associationKeypair.privateKey);
                if (encoding == "base64") {
                  socket.send(fromUint8Array2(binaryMsg));
                } else {
                  socket.send(binaryMsg);
                }
                state = {
                  __type: "hello_req_sent",
                  associationPublicKey: associationKeypair.publicKey,
                  ecdhPrivateKey: ecdhKeypair.privateKey
                };
                break;
              case "connected":
                try {
                  const sequenceNumberVector = responseBuffer.slice(0, SEQUENCE_NUMBER_BYTES);
                  const sequenceNumber = getSequenceNumberFromByteArray(sequenceNumberVector);
                  if (sequenceNumber !== lastKnownInboundSequenceNumber + 1) {
                    throw new Error("Encrypted message has invalid sequence number");
                  }
                  lastKnownInboundSequenceNumber = sequenceNumber;
                  const jsonRpcMessage = yield decryptJsonRpcMessage(responseBuffer, state.sharedSecret);
                  const responsePromise = jsonRpcResponsePromises[jsonRpcMessage.id];
                  delete jsonRpcResponsePromises[jsonRpcMessage.id];
                  responsePromise.resolve(jsonRpcMessage.result);
                } catch (e) {
                  if (e instanceof SolanaMobileWalletAdapterProtocolError) {
                    const responsePromise = jsonRpcResponsePromises[e.jsonRpcMessageId];
                    delete jsonRpcResponsePromises[e.jsonRpcMessageId];
                    responsePromise.reject(e);
                  } else {
                    throw e;
                  }
                }
                break;
              case "hello_req_sent": {
                const sharedSecret = yield parseHelloRsp(responseBuffer, state.associationPublicKey, state.ecdhPrivateKey);
                const sessionPropertiesBuffer = responseBuffer.slice(ENCODED_PUBLIC_KEY_LENGTH_BYTES);
                const sessionProperties = sessionPropertiesBuffer.byteLength !== 0 ? yield (() => __awaiter2(this, void 0, void 0, function* () {
                  const sequenceNumberVector = sessionPropertiesBuffer.slice(0, SEQUENCE_NUMBER_BYTES);
                  const sequenceNumber = getSequenceNumberFromByteArray(sequenceNumberVector);
                  if (sequenceNumber !== lastKnownInboundSequenceNumber + 1) {
                    throw new Error("Encrypted message has invalid sequence number");
                  }
                  lastKnownInboundSequenceNumber = sequenceNumber;
                  return parseSessionProps(sessionPropertiesBuffer, sharedSecret);
                }))() : { protocol_version: "legacy" };
                state = { __type: "connected", sharedSecret, sessionProperties };
                const wallet = createMobileWalletProxy(sessionProperties.protocol_version, (method, params) => __awaiter2(this, void 0, void 0, function* () {
                  const id = nextJsonRpcMessageId++;
                  const binaryMsg2 = yield encryptJsonRpcMessage({
                    id,
                    jsonrpc: "2.0",
                    method,
                    params: params !== null && params !== void 0 ? params : {}
                  }, sharedSecret);
                  if (encoding == "base64") {
                    socket.send(fromUint8Array2(binaryMsg2));
                  } else {
                    socket.send(binaryMsg2);
                  }
                  return new Promise((resolve2, reject2) => {
                    jsonRpcResponsePromises[id] = {
                      resolve(result) {
                        switch (method) {
                          case "authorize":
                          case "reauthorize": {
                            const { wallet_uri_base } = result;
                            if (wallet_uri_base != null) {
                              try {
                                assertSecureEndpointSpecificURI(wallet_uri_base);
                              } catch (e) {
                                reject2(e);
                                return;
                              }
                            }
                            break;
                          }
                        }
                        resolve2(result);
                      },
                      reject: reject2
                    };
                  });
                }));
                sessionEstablished = true;
                try {
                  resolve(wallet);
                } catch (e) {
                  reject(e);
                }
                break;
              }
            }
          });
          socket.addEventListener("message", handleMessage);
          handleClose = () => {
            socket.removeEventListener("message", handleMessage);
            disposeSocket();
            if (!sessionEstablished) {
              reject(new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_SESSION_CLOSED, `The wallet session was closed before connection.`, { closeEvent: new CloseEvent("socket was closed before connection") }));
            }
          };
        }) };
      });
    }
    exports.SolanaCloneAuthorization = SolanaCloneAuthorization;
    exports.SolanaMobileWalletAdapterError = SolanaMobileWalletAdapterError;
    exports.SolanaMobileWalletAdapterErrorCode = SolanaMobileWalletAdapterErrorCode;
    exports.SolanaMobileWalletAdapterProtocolError = SolanaMobileWalletAdapterProtocolError;
    exports.SolanaMobileWalletAdapterProtocolErrorCode = SolanaMobileWalletAdapterProtocolErrorCode;
    exports.SolanaSignInWithSolana = SolanaSignInWithSolana;
    exports.SolanaSignTransactions = SolanaSignTransactions;
    exports.startRemoteScenario = startRemoteScenario2;
    exports.transact = transact2;
  }
});

// node_modules/@solana-mobile/mobile-wallet-adapter-protocol-web3js/lib/esm/index.browser.js
var index_browser_exports = {};
__export(index_browser_exports, {
  startRemoteScenario: () => startRemoteScenario,
  transact: () => transact
});
function __rest(s, e) {
  var t = {};
  for (var p in s)
    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
      t[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function")
    for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
      if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
        t[p[i]] = s[p[i]];
    }
  return t;
}
function __awaiter(thisArg, _arguments, P, generator) {
  function adopt(value) {
    return value instanceof P ? value : new P(function(resolve) {
      resolve(value);
    });
  }
  return new (P || (P = Promise))(function(resolve, reject) {
    function fulfilled(value) {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    }
    function rejected(value) {
      try {
        step(generator["throw"](value));
      } catch (e) {
        reject(e);
      }
    }
    function step(result) {
      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
    }
    step((generator = generator.apply(thisArg, _arguments || [])).next());
  });
}
function fromUint8Array(byteArray) {
  return window.btoa(String.fromCharCode.call(null, ...byteArray));
}
function toUint8Array(base64EncodedByteArray) {
  return new Uint8Array(window.atob(base64EncodedByteArray).split("").map((c) => c.charCodeAt(0)));
}
function getPayloadFromTransaction(transaction) {
  const serializedTransaction = "version" in transaction ? transaction.serialize() : transaction.serialize({
    requireAllSignatures: false,
    verifySignatures: false
  });
  const payload = fromUint8Array(serializedTransaction);
  return payload;
}
function getTransactionFromWireMessage(byteArray) {
  const numSignatures = byteArray[0];
  const messageOffset = numSignatures * SIGNATURE_LENGTH_IN_BYTES + 1;
  const version = VersionedMessage.deserializeMessageVersion(byteArray.slice(messageOffset, byteArray.length));
  if (version === "legacy") {
    return Transaction.from(byteArray);
  } else {
    return VersionedTransaction.deserialize(byteArray);
  }
}
function transact(callback, config) {
  return __awaiter(this, void 0, void 0, function* () {
    const augmentedCallback = (wallet) => {
      return callback(augmentWalletAPI(wallet));
    };
    return yield (0, import_mobile_wallet_adapter_protocol.transact)(augmentedCallback, config);
  });
}
function startRemoteScenario(config) {
  return __awaiter(this, void 0, void 0, function* () {
    const { wallet, close, associationUrl } = yield (0, import_mobile_wallet_adapter_protocol.startRemoteScenario)(config);
    const augmentedPromise = wallet.then((wallet2) => {
      return augmentWalletAPI(wallet2);
    });
    return { wallet: augmentedPromise, close, associationUrl };
  });
}
function augmentWalletAPI(wallet) {
  return new Proxy({}, {
    get(target, p) {
      if (target[p] == null) {
        switch (p) {
          case "signAndSendTransactions":
            target[p] = function(_a) {
              var { minContextSlot, commitment, skipPreflight, maxRetries, waitForCommitmentToSendNextTransaction, transactions } = _a, rest = __rest(_a, ["minContextSlot", "commitment", "skipPreflight", "maxRetries", "waitForCommitmentToSendNextTransaction", "transactions"]);
              return __awaiter(this, void 0, void 0, function* () {
                const payloads = transactions.map(getPayloadFromTransaction);
                const options = {
                  min_context_slot: minContextSlot,
                  commitment,
                  skip_preflight: skipPreflight,
                  max_retries: maxRetries,
                  wait_for_commitment_to_send_next_transaction: waitForCommitmentToSendNextTransaction
                };
                const { signatures: base64EncodedSignatures } = yield wallet.signAndSendTransactions(Object.assign(Object.assign(Object.assign({}, rest), Object.values(options).some((element) => element != null) ? { options } : null), { payloads }));
                const signatures = base64EncodedSignatures.map(toUint8Array).map(import_bs58.default.encode);
                return signatures;
              });
            };
            break;
          case "signMessages":
            target[p] = function(_a) {
              var { payloads } = _a, rest = __rest(_a, ["payloads"]);
              return __awaiter(this, void 0, void 0, function* () {
                const base64EncodedPayloads = payloads.map(fromUint8Array);
                const { signed_payloads: base64EncodedSignedMessages } = yield wallet.signMessages(Object.assign(Object.assign({}, rest), { payloads: base64EncodedPayloads }));
                const signedMessages = base64EncodedSignedMessages.map(toUint8Array);
                return signedMessages;
              });
            };
            break;
          case "signTransactions":
            target[p] = function(_a) {
              var { transactions } = _a, rest = __rest(_a, ["transactions"]);
              return __awaiter(this, void 0, void 0, function* () {
                const payloads = transactions.map(getPayloadFromTransaction);
                const { signed_payloads: base64EncodedCompiledTransactions } = yield wallet.signTransactions(Object.assign(Object.assign({}, rest), { payloads }));
                const compiledTransactions = base64EncodedCompiledTransactions.map(toUint8Array);
                const signedTransactions = compiledTransactions.map(getTransactionFromWireMessage);
                return signedTransactions;
              });
            };
            break;
          default: {
            target[p] = wallet[p];
            break;
          }
        }
      }
      return target[p];
    },
    defineProperty() {
      return false;
    },
    deleteProperty() {
      return false;
    }
  });
}
var import_mobile_wallet_adapter_protocol, import_bs58;
var init_index_browser = __esm({
  "node_modules/@solana-mobile/mobile-wallet-adapter-protocol-web3js/lib/esm/index.browser.js"() {
    init_index_browser_esm();
    import_mobile_wallet_adapter_protocol = __toESM(require_index_browser());
    import_bs58 = __toESM(require_bs58());
  }
});

// node_modules/@solana-mobile/wallet-standard-mobile/lib/cjs/index.browser.js
var require_index_browser2 = __commonJS({
  "node_modules/@solana-mobile/wallet-standard-mobile/lib/cjs/index.browser.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    var walletStandardFeatures = require_cjs();
    var web3_js = (init_index_browser_esm(), __toCommonJS(index_browser_esm_exports));
    var QRCode = require_browser();
    var features = require_cjs2();
    var walletStandardChains = require_cjs4();
    var mobileWalletAdapterProtocolWeb3js = (init_index_browser(), __toCommonJS(index_browser_exports));
    var base58 = require_bs58();
    function _interopDefaultLegacy(e) {
      return e && typeof e === "object" && "default" in e ? e : { "default": e };
    }
    var QRCode__default = _interopDefaultLegacy(QRCode);
    var base58__default = _interopDefaultLegacy(base58);
    function __awaiter2(thisArg, _arguments, P, generator) {
      function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
          resolve(value);
        });
      }
      return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
          try {
            step(generator.next(value));
          } catch (e) {
            reject(e);
          }
        }
        function rejected(value) {
          try {
            step(generator["throw"](value));
          } catch (e) {
            reject(e);
          }
        }
        function step(result) {
          result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
      });
    }
    function __classPrivateFieldGet$1(receiver, state, kind, f) {
      if (kind === "a" && !f)
        throw new TypeError("Private accessor was defined without a getter");
      if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver))
        throw new TypeError("Cannot read private member from an object whose class did not declare it");
      return kind === "m" ? f : kind === "a" ? f.call(receiver) : f ? f.value : state.get(receiver);
    }
    function __classPrivateFieldSet$1(receiver, state, value, kind, f) {
      if (kind === "m")
        throw new TypeError("Private method is not writable");
      if (kind === "a" && !f)
        throw new TypeError("Private accessor was defined without a setter");
      if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver))
        throw new TypeError("Cannot write private member to an object whose class did not declare it");
      return kind === "a" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value), value;
    }
    var _EmbeddedModal_instances;
    var _EmbeddedModal_root;
    var _EmbeddedModal_eventListeners;
    var _EmbeddedModal_listenersAttached;
    var _EmbeddedModal_injectHTML;
    var _EmbeddedModal_attachEventListeners;
    var _EmbeddedModal_removeEventListeners;
    var _EmbeddedModal_handleKeyDown;
    var modalHtml = `
<div class="mobile-wallet-adapter-embedded-modal-container" role="dialog" aria-modal="true" aria-labelledby="modal-title">
    <div data-modal-close style="position: absolute; width: 100%; height: 100%;"></div>
	<div class="mobile-wallet-adapter-embedded-modal-card">
		<div>
			<button data-modal-close class="mobile-wallet-adapter-embedded-modal-close">
				<svg width="14" height="14">
					<path d="M 6.7125,8.3036995 1.9082,13.108199 c -0.2113,0.2112 -0.4765,0.3168 -0.7957,0.3168 -0.3192,0 -0.5844,-0.1056 -0.7958,-0.3168 C 0.1056,12.896899 0,12.631699 0,12.312499 c 0,-0.3192 0.1056,-0.5844 0.3167,-0.7958 L 5.1212,6.7124995 0.3167,1.9082 C 0.1056,1.6969 0,1.4317 0,1.1125 0,0.7933 0.1056,0.5281 0.3167,0.3167 0.5281,0.1056 0.7933,0 1.1125,0 1.4317,0 1.6969,0.1056 1.9082,0.3167 L 6.7125,5.1212 11.5167,0.3167 C 11.7281,0.1056 11.9933,0 12.3125,0 c 0.3192,0 0.5844,0.1056 0.7957,0.3167 0.2112,0.2114 0.3168,0.4766 0.3168,0.7958 0,0.3192 -0.1056,0.5844 -0.3168,0.7957 L 8.3037001,6.7124995 13.1082,11.516699 c 0.2112,0.2114 0.3168,0.4766 0.3168,0.7958 0,0.3192 -0.1056,0.5844 -0.3168,0.7957 -0.2113,0.2112 -0.4765,0.3168 -0.7957,0.3168 -0.3192,0 -0.5844,-0.1056 -0.7958,-0.3168 z" />
				</svg>
			</button>
		</div>
		<div class="mobile-wallet-adapter-embedded-modal-content"></div>
	</div>
</div>
`;
    var css$2 = `
.mobile-wallet-adapter-embedded-modal-container {
    display: flex; /* Use flexbox to center content */
    justify-content: center; /* Center horizontally */
    align-items: center; /* Center vertically */
    position: fixed; /* Stay in place */
    z-index: 1; /* Sit on top */
    left: 0;
    top: 0;
    width: 100%; /* Full width */
    height: 100%; /* Full height */
    background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
    overflow-y: auto; /* enable scrolling */
}

.mobile-wallet-adapter-embedded-modal-card {
    display: flex;
    flex-direction: column;
    margin: auto 20px;
    max-width: 780px;
    padding: 20px;
    border-radius: 24px;
    background: #ffffff;
    font-family: "Inter Tight", "PT Sans", Calibri, sans-serif;
    transform: translateY(-200%);
    animation: slide-in 0.5s forwards;
}

@keyframes slide-in {
    100% { transform: translateY(0%); }
}

.mobile-wallet-adapter-embedded-modal-close {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    cursor: pointer;
    background: #e4e9e9;
    border: none;
    border-radius: 50%;
}

.mobile-wallet-adapter-embedded-modal-close:focus-visible {
    outline-color: red;
}

.mobile-wallet-adapter-embedded-modal-close svg {
    fill: #546266;
    transition: fill 200ms ease 0s;
}

.mobile-wallet-adapter-embedded-modal-close:hover svg {
    fill: #fff;
}
`;
    var fonts = `
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter+Tight:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet">
`;
    var EmbeddedModal = class {
      constructor() {
        _EmbeddedModal_instances.add(this);
        _EmbeddedModal_root.set(this, null);
        _EmbeddedModal_eventListeners.set(this, {});
        _EmbeddedModal_listenersAttached.set(this, false);
        this.dom = null;
        this.open = () => {
          console.debug("Modal open");
          __classPrivateFieldGet$1(this, _EmbeddedModal_instances, "m", _EmbeddedModal_attachEventListeners).call(this);
          if (__classPrivateFieldGet$1(this, _EmbeddedModal_root, "f")) {
            __classPrivateFieldGet$1(this, _EmbeddedModal_root, "f").style.display = "flex";
          }
        };
        this.close = (event = void 0) => {
          var _a;
          console.debug("Modal close");
          __classPrivateFieldGet$1(this, _EmbeddedModal_instances, "m", _EmbeddedModal_removeEventListeners).call(this);
          if (__classPrivateFieldGet$1(this, _EmbeddedModal_root, "f")) {
            __classPrivateFieldGet$1(this, _EmbeddedModal_root, "f").style.display = "none";
          }
          (_a = __classPrivateFieldGet$1(this, _EmbeddedModal_eventListeners, "f")["close"]) === null || _a === void 0 ? void 0 : _a.forEach((listener) => listener(event));
        };
        _EmbeddedModal_handleKeyDown.set(this, (event) => {
          if (event.key === "Escape")
            this.close(event);
        });
        this.init = this.init.bind(this);
        __classPrivateFieldSet$1(this, _EmbeddedModal_root, document.getElementById("mobile-wallet-adapter-embedded-root-ui"), "f");
      }
      init() {
        return __awaiter2(this, void 0, void 0, function* () {
          console.log("Injecting modal");
          __classPrivateFieldGet$1(this, _EmbeddedModal_instances, "m", _EmbeddedModal_injectHTML).call(this);
        });
      }
      addEventListener(event, listener) {
        var _a;
        ((_a = __classPrivateFieldGet$1(this, _EmbeddedModal_eventListeners, "f")[event]) === null || _a === void 0 ? void 0 : _a.push(listener)) || (__classPrivateFieldGet$1(this, _EmbeddedModal_eventListeners, "f")[event] = [listener]);
        return () => this.removeEventListener(event, listener);
      }
      removeEventListener(event, listener) {
        var _a;
        __classPrivateFieldGet$1(this, _EmbeddedModal_eventListeners, "f")[event] = (_a = __classPrivateFieldGet$1(this, _EmbeddedModal_eventListeners, "f")[event]) === null || _a === void 0 ? void 0 : _a.filter((existingListener) => listener !== existingListener);
      }
    };
    _EmbeddedModal_root = /* @__PURE__ */ new WeakMap(), _EmbeddedModal_eventListeners = /* @__PURE__ */ new WeakMap(), _EmbeddedModal_listenersAttached = /* @__PURE__ */ new WeakMap(), _EmbeddedModal_handleKeyDown = /* @__PURE__ */ new WeakMap(), _EmbeddedModal_instances = /* @__PURE__ */ new WeakSet(), _EmbeddedModal_injectHTML = function _EmbeddedModal_injectHTML2() {
      if (document.getElementById("mobile-wallet-adapter-embedded-root-ui")) {
        if (!__classPrivateFieldGet$1(this, _EmbeddedModal_root, "f"))
          __classPrivateFieldSet$1(this, _EmbeddedModal_root, document.getElementById("mobile-wallet-adapter-embedded-root-ui"), "f");
        return;
      }
      __classPrivateFieldSet$1(this, _EmbeddedModal_root, document.createElement("div"), "f");
      __classPrivateFieldGet$1(this, _EmbeddedModal_root, "f").id = "mobile-wallet-adapter-embedded-root-ui";
      __classPrivateFieldGet$1(this, _EmbeddedModal_root, "f").innerHTML = modalHtml;
      __classPrivateFieldGet$1(this, _EmbeddedModal_root, "f").style.display = "none";
      const content = __classPrivateFieldGet$1(this, _EmbeddedModal_root, "f").querySelector(".mobile-wallet-adapter-embedded-modal-content");
      if (content)
        content.innerHTML = this.contentHtml;
      const styles = document.createElement("style");
      styles.id = "mobile-wallet-adapter-embedded-modal-styles";
      styles.textContent = css$2 + this.contentStyles;
      const host = document.createElement("div");
      host.innerHTML = fonts;
      this.dom = host.attachShadow({ mode: "closed" });
      this.dom.appendChild(styles);
      this.dom.appendChild(__classPrivateFieldGet$1(this, _EmbeddedModal_root, "f"));
      document.body.appendChild(host);
    }, _EmbeddedModal_attachEventListeners = function _EmbeddedModal_attachEventListeners2() {
      if (!__classPrivateFieldGet$1(this, _EmbeddedModal_root, "f") || __classPrivateFieldGet$1(this, _EmbeddedModal_listenersAttached, "f"))
        return;
      const closers = [...__classPrivateFieldGet$1(this, _EmbeddedModal_root, "f").querySelectorAll("[data-modal-close]")];
      closers.forEach((closer) => closer === null || closer === void 0 ? void 0 : closer.addEventListener("click", this.close));
      window.addEventListener("load", this.close);
      document.addEventListener("keydown", __classPrivateFieldGet$1(this, _EmbeddedModal_handleKeyDown, "f"));
      __classPrivateFieldSet$1(this, _EmbeddedModal_listenersAttached, true, "f");
    }, _EmbeddedModal_removeEventListeners = function _EmbeddedModal_removeEventListeners2() {
      if (!__classPrivateFieldGet$1(this, _EmbeddedModal_listenersAttached, "f"))
        return;
      window.removeEventListener("load", this.close);
      document.removeEventListener("keydown", __classPrivateFieldGet$1(this, _EmbeddedModal_handleKeyDown, "f"));
      if (!__classPrivateFieldGet$1(this, _EmbeddedModal_root, "f"))
        return;
      const closers = [...__classPrivateFieldGet$1(this, _EmbeddedModal_root, "f").querySelectorAll("[data-modal-close]")];
      closers.forEach((closer) => closer === null || closer === void 0 ? void 0 : closer.removeEventListener("click", this.close));
      __classPrivateFieldSet$1(this, _EmbeddedModal_listenersAttached, false, "f");
    };
    var RemoteConnectionModal = class extends EmbeddedModal {
      constructor() {
        super(...arguments);
        this.contentStyles = css$1;
        this.contentHtml = QRCodeHtml;
      }
      initWithQR(qrCode) {
        const _super = Object.create(null, {
          init: { get: () => super.init }
        });
        return __awaiter2(this, void 0, void 0, function* () {
          _super.init.call(this);
          this.populateQRCode(qrCode);
        });
      }
      populateQRCode(qrUrl) {
        var _a;
        return __awaiter2(this, void 0, void 0, function* () {
          const qrcodeContainer = (_a = this.dom) === null || _a === void 0 ? void 0 : _a.getElementById("mobile-wallet-adapter-embedded-modal-qr-code-container");
          if (qrcodeContainer) {
            const qrCodeElement = yield QRCode__default["default"].toCanvas(qrUrl, { width: 200, margin: 0 });
            if (qrcodeContainer.firstElementChild !== null) {
              qrcodeContainer.replaceChild(qrCodeElement, qrcodeContainer.firstElementChild);
            } else
              qrcodeContainer.appendChild(qrCodeElement);
          } else {
            console.error("QRCode Container not found");
          }
        });
      }
    };
    var QRCodeHtml = `
<div class="mobile-wallet-adapter-embedded-modal-qr-content">
    <div>
        <svg class="mobile-wallet-adapter-embedded-modal-icon" width="100%" height="100%">
            <circle r="52" cx="53" cy="53" fill="#99b3be" stroke="#000000" stroke-width="2"/>
            <path d="m 53,82.7305 c -3.3116,0 -6.1361,-1.169 -8.4735,-3.507 -2.338,-2.338 -3.507,-5.1625 -3.507,-8.4735 0,-3.3116 1.169,-6.1364 3.507,-8.4744 2.3374,-2.338 5.1619,-3.507 8.4735,-3.507 3.3116,0 6.1361,1.169 8.4735,3.507 2.338,2.338 3.507,5.1628 3.507,8.4744 0,3.311 -1.169,6.1355 -3.507,8.4735 -2.3374,2.338 -5.1619,3.507 -8.4735,3.507 z m 0.007,-5.25 c 1.8532,0 3.437,-0.6598 4.7512,-1.9793 1.3149,-1.3195 1.9723,-2.9058 1.9723,-4.7591 0,-1.8526 -0.6598,-3.4364 -1.9793,-4.7512 -1.3195,-1.3149 -2.9055,-1.9723 -4.7582,-1.9723 -1.8533,0 -3.437,0.6598 -4.7513,1.9793 -1.3148,1.3195 -1.9722,2.9058 -1.9722,4.7591 0,1.8527 0.6597,3.4364 1.9792,4.7512 1.3195,1.3149 2.9056,1.9723 4.7583,1.9723 z m -28,-33.5729 -3.85,-3.6347 c 4.1195,-4.025 8.8792,-7.1984 14.2791,-9.52 5.4005,-2.3223 11.2551,-3.4834 17.5639,-3.4834 6.3087,0 12.1634,1.1611 17.5639,3.4834 5.3999,2.3216 10.1596,5.495 14.2791,9.52 l -3.85,3.6347 C 77.2999,40.358 73.0684,37.5726 68.2985,35.5514 63.5292,33.5301 58.4296,32.5195 53,32.5195 c -5.4297,0 -10.5292,1.0106 -15.2985,3.0319 -4.7699,2.0212 -9.0014,4.8066 -12.6945,8.3562 z m 44.625,10.8771 c -2.2709,-2.1046 -4.7962,-3.7167 -7.5758,-4.8361 -2.7795,-1.12 -5.7983,-1.68 -9.0562,-1.68 -3.2579,0 -6.2621,0.56 -9.0125,1.68 -2.7504,1.1194 -5.2903,2.7315 -7.6195,4.8361 L 32.5189,51.15 c 2.8355,-2.6028 5.9777,-4.6086 9.4263,-6.0174 3.4481,-1.4087 7.133,-2.1131 11.0548,-2.1131 3.9217,0 7.5979,0.7044 11.0285,2.1131 3.43,1.4088 6.5631,3.4146 9.3992,6.0174 z"/>
        </svg>
        <div class="mobile-wallet-adapter-embedded-modal-title">Remote Mobile Wallet Adapter</div>
    </div>
    <div>
        <div>
            <h4 class="mobile-wallet-adapter-embedded-modal-qr-label">
                Open your wallet and scan this code
            </h4>
        </div>
        <div id="mobile-wallet-adapter-embedded-modal-qr-code-container" class="mobile-wallet-adapter-embedded-modal-qr-code-container"></div>
    </div>
</div>
<div class="mobile-wallet-adapter-embedded-modal-divider"><hr></div>
<div class="mobile-wallet-adapter-embedded-modal-footer">
    <div class="mobile-wallet-adapter-embedded-modal-subtitle">
        Follow the instructions on your device. When you're finished, this screen will update.
    </div>
    <div class="mobile-wallet-adapter-embedded-modal-progress-badge">
        <div>
            <div class="spinner">
                <div class="leftWrapper">
                    <div class="left">
                        <div class="circle"></div>
                    </div>
                </div>
                <div class="rightWrapper">
                    <div class="right">
                        <div class="circle"></div>
                    </div>
                </div>
            </div>
        </div>
        <div>Waiting for scan</div>
    </div>
</div>
`;
    var css$1 = `
.mobile-wallet-adapter-embedded-modal-qr-content {
    display: flex; 
    margin-top: 10px;
    padding: 10px;
}

.mobile-wallet-adapter-embedded-modal-qr-content > div:first-child {
    display: flex;
    flex-direction: column;
    flex: 2;
    margin-top: auto;
    margin-right: 30px;
}

.mobile-wallet-adapter-embedded-modal-qr-content > div:nth-child(2) {
    display: flex;
    flex-direction: column;
    flex: 1;
    margin-left: auto;
}

.mobile-wallet-adapter-embedded-modal-footer {
    display: flex;
    padding: 10px;
}

.mobile-wallet-adapter-embedded-modal-icon {}

.mobile-wallet-adapter-embedded-modal-title {
    color: #000000;
    font-size: 2.5em;
    font-weight: 600;
}

.mobile-wallet-adapter-embedded-modal-qr-label {
    text-align: right;
    color: #000000;
}

.mobile-wallet-adapter-embedded-modal-qr-code-container {
    margin-left: auto;
}

.mobile-wallet-adapter-embedded-modal-divider {
    margin-top: 20px;
    padding-left: 10px;
    padding-right: 10px;
}

.mobile-wallet-adapter-embedded-modal-divider hr {
    border-top: 1px solid #D9DEDE;
}

.mobile-wallet-adapter-embedded-modal-subtitle {
    margin: auto;
    margin-right: 60px;
    padding: 20px;
    color: #6E8286;
}

.mobile-wallet-adapter-embedded-modal-progress-badge {
    display: flex;
    background: #F7F8F8;
    height: 56px;
    min-width: 200px;
    margin: auto;
    padding-left: 20px;
    padding-right: 20px;
    border-radius: 18px;
    color: #A8B6B8;
    align-items: center;
}

.mobile-wallet-adapter-embedded-modal-progress-badge > div:first-child {
    margin-left: auto;
    margin-right: 20px;
}

.mobile-wallet-adapter-embedded-modal-progress-badge > div:nth-child(2) {
    margin-right: auto;
}

/* Smaller screens */
@media all and (max-width: 600px) {
    .mobile-wallet-adapter-embedded-modal-card {
        text-align: center;
    }
    .mobile-wallet-adapter-embedded-modal-qr-content {
        flex-direction: column;
    }
    .mobile-wallet-adapter-embedded-modal-qr-content > div:first-child {
        margin: auto;
    }
    .mobile-wallet-adapter-embedded-modal-qr-content > div:nth-child(2) {
        margin: auto;
        flex: 2 auto;
    }
    .mobile-wallet-adapter-embedded-modal-footer {
        flex-direction: column;
    }
    .mobile-wallet-adapter-embedded-modal-icon {
        display: none;
    }
    .mobile-wallet-adapter-embedded-modal-title {
        font-size: 1.5em;
    }
    .mobile-wallet-adapter-embedded-modal-subtitle {
        margin-right: unset;
    }
    .mobile-wallet-adapter-embedded-modal-qr-label {
        text-align: center;
    }
    .mobile-wallet-adapter-embedded-modal-qr-code-container {
        margin: auto;
    }
}

/* Spinner */
@keyframes spinLeft {
    0% {
        transform: rotate(20deg);
    }
    50% {
        transform: rotate(160deg);
    }
    100% {
        transform: rotate(20deg);
    }
}
@keyframes spinRight {
    0% {
        transform: rotate(160deg);
    }
    50% {
        transform: rotate(20deg);
    }
    100% {
        transform: rotate(160deg);
    }
}
@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(2520deg);
    }
}

.spinner {
    position: relative;
    width: 1.5em;
    height: 1.5em;
    margin: auto;
    animation: spin 10s linear infinite;
}
.spinner::before {
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
}
.right, .rightWrapper, .left, .leftWrapper {
    position: absolute;
    top: 0;
    overflow: hidden;
    width: .75em;
    height: 1.5em;
}
.left, .leftWrapper {
    left: 0;
}
.right {
    left: -12px;
}
.rightWrapper {
    right: 0;
}
.circle {
    border: .125em solid #A8B6B8;
    width: 1.25em; /* 1.5em - 2*0.125em border */
    height: 1.25em; /* 1.5em - 2*0.125em border */
    border-radius: 0.75em; /* 0.5*1.5em spinner size 8 */
}
.left {
    transform-origin: 100% 50%;
    animation: spinLeft 2.5s cubic-bezier(.2,0,.8,1) infinite;
}
.right {
    transform-origin: 100% 50%;
    animation: spinRight 2.5s cubic-bezier(.2,0,.8,1) infinite;
}
`;
    var icon = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik03IDIuNUgxN0MxNy44Mjg0IDIuNSAxOC41IDMuMTcxNTcgMTguNSA0VjIwQzE4LjUgMjAuODI4NCAxNy44Mjg0IDIxLjUgMTcgMjEuNUg3QzYuMTcxNTcgMjEuNSA1LjUgMjAuODI4NCA1LjUgMjBWNEM1LjUgMy4xNzE1NyA2LjE3MTU3IDIuNSA3IDIuNVpNMyA0QzMgMS43OTA4NiA0Ljc5MDg2IDAgNyAwSDE3QzE5LjIwOTEgMCAyMSAxLjc5MDg2IDIxIDRWMjBDMjEgMjIuMjA5MSAxOS4yMDkxIDI0IDE3IDI0SDdDNC43OTA4NiAyNCAzIDIyLjIwOTEgMyAyMFY0Wk0xMSA0LjYxNTM4QzEwLjQ0NzcgNC42MTUzOCAxMCA1LjA2MzEgMTAgNS42MTUzOFY2LjM4NDYyQzEwIDYuOTM2OSAxMC40NDc3IDcuMzg0NjIgMTEgNy4zODQ2MkgxM0MxMy41NTIzIDcuMzg0NjIgMTQgNi45MzY5IDE0IDYuMzg0NjJWNS42MTUzOEMxNCA1LjA2MzEgMTMuNTUyMyA0LjYxNTM4IDEzIDQuNjE1MzhIMTFaIiBmaWxsPSIjRENCOEZGIi8+Cjwvc3ZnPgo=";
    function isVersionedTransaction2(transaction) {
      return "version" in transaction;
    }
    function fromUint8Array2(byteArray) {
      return window.btoa(String.fromCharCode.call(null, ...byteArray));
    }
    function toUint8Array2(base64EncodedByteArray) {
      return new Uint8Array(window.atob(base64EncodedByteArray).split("").map((c) => c.charCodeAt(0)));
    }
    var _LocalSolanaMobileWalletAdapterWallet_instances;
    var _LocalSolanaMobileWalletAdapterWallet_listeners;
    var _LocalSolanaMobileWalletAdapterWallet_version;
    var _LocalSolanaMobileWalletAdapterWallet_name;
    var _LocalSolanaMobileWalletAdapterWallet_url;
    var _LocalSolanaMobileWalletAdapterWallet_icon;
    var _LocalSolanaMobileWalletAdapterWallet_appIdentity;
    var _LocalSolanaMobileWalletAdapterWallet_authorization;
    var _LocalSolanaMobileWalletAdapterWallet_authorizationCache;
    var _LocalSolanaMobileWalletAdapterWallet_connecting;
    var _LocalSolanaMobileWalletAdapterWallet_connectionGeneration;
    var _LocalSolanaMobileWalletAdapterWallet_chains;
    var _LocalSolanaMobileWalletAdapterWallet_chainSelector;
    var _LocalSolanaMobileWalletAdapterWallet_optionalFeatures;
    var _LocalSolanaMobileWalletAdapterWallet_onWalletNotFound;
    var _LocalSolanaMobileWalletAdapterWallet_on;
    var _LocalSolanaMobileWalletAdapterWallet_emit;
    var _LocalSolanaMobileWalletAdapterWallet_off;
    var _LocalSolanaMobileWalletAdapterWallet_connect;
    var _LocalSolanaMobileWalletAdapterWallet_performAuthorization;
    var _LocalSolanaMobileWalletAdapterWallet_handleAuthorizationResult;
    var _LocalSolanaMobileWalletAdapterWallet_handleWalletCapabilitiesResult;
    var _LocalSolanaMobileWalletAdapterWallet_performReauthorization;
    var _LocalSolanaMobileWalletAdapterWallet_disconnect;
    var _LocalSolanaMobileWalletAdapterWallet_transact;
    var _LocalSolanaMobileWalletAdapterWallet_assertIsAuthorized;
    var _LocalSolanaMobileWalletAdapterWallet_accountsToWalletStandardAccounts;
    var _LocalSolanaMobileWalletAdapterWallet_performSignTransactions;
    var _LocalSolanaMobileWalletAdapterWallet_performSignAndSendTransaction;
    var _LocalSolanaMobileWalletAdapterWallet_signAndSendTransaction;
    var _LocalSolanaMobileWalletAdapterWallet_signTransaction;
    var _LocalSolanaMobileWalletAdapterWallet_signMessage;
    var _LocalSolanaMobileWalletAdapterWallet_signIn;
    var _LocalSolanaMobileWalletAdapterWallet_performSignIn;
    var _RemoteSolanaMobileWalletAdapterWallet_instances;
    var _RemoteSolanaMobileWalletAdapterWallet_listeners;
    var _RemoteSolanaMobileWalletAdapterWallet_version;
    var _RemoteSolanaMobileWalletAdapterWallet_name;
    var _RemoteSolanaMobileWalletAdapterWallet_url;
    var _RemoteSolanaMobileWalletAdapterWallet_icon;
    var _RemoteSolanaMobileWalletAdapterWallet_appIdentity;
    var _RemoteSolanaMobileWalletAdapterWallet_authorization;
    var _RemoteSolanaMobileWalletAdapterWallet_authorizationCache;
    var _RemoteSolanaMobileWalletAdapterWallet_connecting;
    var _RemoteSolanaMobileWalletAdapterWallet_connectionGeneration;
    var _RemoteSolanaMobileWalletAdapterWallet_chains;
    var _RemoteSolanaMobileWalletAdapterWallet_chainSelector;
    var _RemoteSolanaMobileWalletAdapterWallet_optionalFeatures;
    var _RemoteSolanaMobileWalletAdapterWallet_onWalletNotFound;
    var _RemoteSolanaMobileWalletAdapterWallet_hostAuthority;
    var _RemoteSolanaMobileWalletAdapterWallet_session;
    var _RemoteSolanaMobileWalletAdapterWallet_on;
    var _RemoteSolanaMobileWalletAdapterWallet_emit;
    var _RemoteSolanaMobileWalletAdapterWallet_off;
    var _RemoteSolanaMobileWalletAdapterWallet_connect;
    var _RemoteSolanaMobileWalletAdapterWallet_performAuthorization;
    var _RemoteSolanaMobileWalletAdapterWallet_handleAuthorizationResult;
    var _RemoteSolanaMobileWalletAdapterWallet_handleWalletCapabilitiesResult;
    var _RemoteSolanaMobileWalletAdapterWallet_performReauthorization;
    var _RemoteSolanaMobileWalletAdapterWallet_disconnect;
    var _RemoteSolanaMobileWalletAdapterWallet_transact;
    var _RemoteSolanaMobileWalletAdapterWallet_assertIsAuthorized;
    var _RemoteSolanaMobileWalletAdapterWallet_accountsToWalletStandardAccounts;
    var _RemoteSolanaMobileWalletAdapterWallet_performSignTransactions;
    var _RemoteSolanaMobileWalletAdapterWallet_performSignAndSendTransaction;
    var _RemoteSolanaMobileWalletAdapterWallet_signAndSendTransaction;
    var _RemoteSolanaMobileWalletAdapterWallet_signTransaction;
    var _RemoteSolanaMobileWalletAdapterWallet_signMessage;
    var _RemoteSolanaMobileWalletAdapterWallet_signIn;
    var _RemoteSolanaMobileWalletAdapterWallet_performSignIn;
    var SolanaMobileWalletAdapterWalletName3 = "Mobile Wallet Adapter";
    var SIGNATURE_LENGTH_IN_BYTES2 = 64;
    var DEFAULT_FEATURES = [walletStandardFeatures.SolanaSignAndSendTransaction, walletStandardFeatures.SolanaSignTransaction, walletStandardFeatures.SolanaSignMessage, walletStandardFeatures.SolanaSignIn];
    var LocalSolanaMobileWalletAdapterWallet = class {
      constructor(config) {
        _LocalSolanaMobileWalletAdapterWallet_instances.add(this);
        _LocalSolanaMobileWalletAdapterWallet_listeners.set(this, {});
        _LocalSolanaMobileWalletAdapterWallet_version.set(this, "1.0.0");
        _LocalSolanaMobileWalletAdapterWallet_name.set(this, SolanaMobileWalletAdapterWalletName3);
        _LocalSolanaMobileWalletAdapterWallet_url.set(this, "https://solanamobile.com/wallets");
        _LocalSolanaMobileWalletAdapterWallet_icon.set(this, icon);
        _LocalSolanaMobileWalletAdapterWallet_appIdentity.set(this, void 0);
        _LocalSolanaMobileWalletAdapterWallet_authorization.set(this, void 0);
        _LocalSolanaMobileWalletAdapterWallet_authorizationCache.set(this, void 0);
        _LocalSolanaMobileWalletAdapterWallet_connecting.set(this, false);
        _LocalSolanaMobileWalletAdapterWallet_connectionGeneration.set(this, 0);
        _LocalSolanaMobileWalletAdapterWallet_chains.set(this, []);
        _LocalSolanaMobileWalletAdapterWallet_chainSelector.set(this, void 0);
        _LocalSolanaMobileWalletAdapterWallet_optionalFeatures.set(this, void 0);
        _LocalSolanaMobileWalletAdapterWallet_onWalletNotFound.set(this, void 0);
        _LocalSolanaMobileWalletAdapterWallet_on.set(this, (event, listener) => {
          var _a;
          ((_a = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_listeners, "f")[event]) === null || _a === void 0 ? void 0 : _a.push(listener)) || (__classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_listeners, "f")[event] = [listener]);
          return () => __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_instances, "m", _LocalSolanaMobileWalletAdapterWallet_off).call(this, event, listener);
        });
        _LocalSolanaMobileWalletAdapterWallet_connect.set(this, ({ silent } = {}) => __awaiter2(this, void 0, void 0, function* () {
          if (__classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_connecting, "f") || this.connected) {
            return { accounts: this.accounts };
          }
          __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_connecting, true, "f");
          try {
            if (silent) {
              const cachedAuthorization = yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorizationCache, "f").get();
              if (cachedAuthorization) {
                yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_handleAuthorizationResult, "f").call(this, cachedAuthorization);
              } else {
                return { accounts: this.accounts };
              }
            } else {
              yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_performAuthorization, "f").call(this);
            }
          } catch (e) {
            throw new Error(e instanceof Error && e.message || "Unknown error");
          } finally {
            __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_connecting, false, "f");
          }
          return { accounts: this.accounts };
        }));
        _LocalSolanaMobileWalletAdapterWallet_performAuthorization.set(this, (signInPayload) => __awaiter2(this, void 0, void 0, function* () {
          try {
            const cachedAuthorizationResult = yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorizationCache, "f").get();
            if (cachedAuthorizationResult) {
              __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_handleAuthorizationResult, "f").call(this, cachedAuthorizationResult);
              return cachedAuthorizationResult;
            }
            const selectedChain = yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_chainSelector, "f").select(__classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_chains, "f"));
            return yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_transact, "f").call(this, (wallet) => __awaiter2(this, void 0, void 0, function* () {
              const [capabilities, mwaAuthorizationResult] = yield Promise.all([
                wallet.getCapabilities(),
                wallet.authorize({
                  chain: selectedChain,
                  identity: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_appIdentity, "f"),
                  sign_in_payload: signInPayload
                })
              ]);
              const accounts = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_accountsToWalletStandardAccounts, "f").call(this, mwaAuthorizationResult.accounts);
              const authorization = Object.assign(Object.assign({}, mwaAuthorizationResult), { accounts, chain: selectedChain });
              Promise.all([
                __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_handleWalletCapabilitiesResult, "f").call(this, capabilities),
                __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorizationCache, "f").set(authorization),
                __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_handleAuthorizationResult, "f").call(this, authorization)
              ]);
              return authorization;
            }));
          } catch (e) {
            throw new Error(e instanceof Error && e.message || "Unknown error");
          }
        }));
        _LocalSolanaMobileWalletAdapterWallet_handleAuthorizationResult.set(this, (authorization) => __awaiter2(this, void 0, void 0, function* () {
          var _a;
          const didPublicKeysChange = (
            // Case 1: We started from having no authorization.
            __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, "f") == null || // Case 2: The number of authorized accounts changed.
            ((_a = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, "f")) === null || _a === void 0 ? void 0 : _a.accounts.length) !== authorization.accounts.length || // Case 3: The new list of addresses isn't exactly the same as the old list, in the same order.
            __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, "f").accounts.some((account, ii) => account.address !== authorization.accounts[ii].address)
          );
          __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, authorization, "f");
          if (didPublicKeysChange) {
            __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_instances, "m", _LocalSolanaMobileWalletAdapterWallet_emit).call(this, "change", { accounts: this.accounts });
          }
        }));
        _LocalSolanaMobileWalletAdapterWallet_handleWalletCapabilitiesResult.set(this, (capabilities) => __awaiter2(this, void 0, void 0, function* () {
          const supportsSignTransaction = capabilities.features.includes("solana:signTransactions");
          const supportsSignAndSendTransaction = capabilities.supports_sign_and_send_transactions;
          const didCapabilitiesChange = walletStandardFeatures.SolanaSignAndSendTransaction in this.features !== supportsSignAndSendTransaction || walletStandardFeatures.SolanaSignTransaction in this.features !== supportsSignTransaction;
          __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_optionalFeatures, Object.assign(Object.assign({}, (supportsSignAndSendTransaction || !supportsSignAndSendTransaction && !supportsSignTransaction) && {
            [walletStandardFeatures.SolanaSignAndSendTransaction]: {
              version: "1.0.0",
              supportedTransactionVersions: ["legacy", 0],
              signAndSendTransaction: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_signAndSendTransaction, "f")
            }
          }), supportsSignTransaction && {
            [walletStandardFeatures.SolanaSignTransaction]: {
              version: "1.0.0",
              supportedTransactionVersions: ["legacy", 0],
              signTransaction: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_signTransaction, "f")
            }
          }), "f");
          if (didCapabilitiesChange) {
            __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_instances, "m", _LocalSolanaMobileWalletAdapterWallet_emit).call(this, "change", { features: this.features });
          }
        }));
        _LocalSolanaMobileWalletAdapterWallet_performReauthorization.set(this, (wallet, authToken, chain) => __awaiter2(this, void 0, void 0, function* () {
          try {
            const mwaAuthorizationResult = yield wallet.authorize({
              auth_token: authToken,
              identity: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_appIdentity, "f"),
              chain
            });
            const accounts = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_accountsToWalletStandardAccounts, "f").call(this, mwaAuthorizationResult.accounts);
            const authorization = Object.assign(Object.assign({}, mwaAuthorizationResult), { accounts, chain });
            Promise.all([
              __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorizationCache, "f").set(authorization),
              __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_handleAuthorizationResult, "f").call(this, authorization)
            ]);
          } catch (e) {
            __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_disconnect, "f").call(this);
            throw new Error(e instanceof Error && e.message || "Unknown error");
          }
        }));
        _LocalSolanaMobileWalletAdapterWallet_disconnect.set(this, () => __awaiter2(this, void 0, void 0, function* () {
          var _b;
          __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorizationCache, "f").clear();
          __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_connecting, false, "f");
          __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_connectionGeneration, (_b = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_connectionGeneration, "f"), _b++, _b), "f");
          __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, void 0, "f");
          __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_instances, "m", _LocalSolanaMobileWalletAdapterWallet_emit).call(this, "change", { accounts: this.accounts });
        }));
        _LocalSolanaMobileWalletAdapterWallet_transact.set(this, (callback) => __awaiter2(this, void 0, void 0, function* () {
          var _c;
          const walletUriBase = (_c = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, "f")) === null || _c === void 0 ? void 0 : _c.wallet_uri_base;
          const config2 = walletUriBase ? { baseUri: walletUriBase } : void 0;
          const currentConnectionGeneration = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_connectionGeneration, "f");
          try {
            return yield mobileWalletAdapterProtocolWeb3js.transact(callback, config2);
          } catch (e) {
            if (__classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_connectionGeneration, "f") !== currentConnectionGeneration) {
              yield new Promise(() => {
              });
            }
            if (e instanceof Error && e.name === "SolanaMobileWalletAdapterError" && e.code === "ERROR_WALLET_NOT_FOUND") {
              yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_onWalletNotFound, "f").call(this, this);
            }
            throw e;
          }
        }));
        _LocalSolanaMobileWalletAdapterWallet_assertIsAuthorized.set(this, () => {
          if (!__classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, "f"))
            throw new Error("Wallet not connected");
          return { authToken: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, "f").auth_token, chain: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, "f").chain };
        });
        _LocalSolanaMobileWalletAdapterWallet_accountsToWalletStandardAccounts.set(this, (accounts) => {
          return accounts.map((account) => {
            var _a, _b;
            const publicKey = toUint8Array2(account.address);
            return {
              address: base58__default["default"].encode(publicKey),
              publicKey,
              label: account.label,
              icon: account.icon,
              chains: (_a = account.chains) !== null && _a !== void 0 ? _a : __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_chains, "f"),
              // TODO: get supported features from getCapabilities API 
              features: (_b = account.features) !== null && _b !== void 0 ? _b : DEFAULT_FEATURES
            };
          });
        });
        _LocalSolanaMobileWalletAdapterWallet_performSignTransactions.set(this, (transactions) => __awaiter2(this, void 0, void 0, function* () {
          const { authToken, chain } = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_assertIsAuthorized, "f").call(this);
          try {
            return yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_transact, "f").call(this, (wallet) => __awaiter2(this, void 0, void 0, function* () {
              yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_performReauthorization, "f").call(this, wallet, authToken, chain);
              const signedTransactions = yield wallet.signTransactions({
                transactions
              });
              return signedTransactions;
            }));
          } catch (e) {
            throw new Error(e instanceof Error && e.message || "Unknown error");
          }
        }));
        _LocalSolanaMobileWalletAdapterWallet_performSignAndSendTransaction.set(this, (transaction, options) => __awaiter2(this, void 0, void 0, function* () {
          const { authToken, chain } = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_assertIsAuthorized, "f").call(this);
          try {
            return yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_transact, "f").call(this, (wallet) => __awaiter2(this, void 0, void 0, function* () {
              const [capabilities, _1] = yield Promise.all([
                wallet.getCapabilities(),
                __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_performReauthorization, "f").call(this, wallet, authToken, chain)
              ]);
              if (capabilities.supports_sign_and_send_transactions) {
                const signatures = yield wallet.signAndSendTransactions(Object.assign(Object.assign({}, options), { transactions: [transaction] }));
                return signatures[0];
              } else {
                throw new Error("connected wallet does not support signAndSendTransaction");
              }
            }));
          } catch (e) {
            throw new Error(e instanceof Error && e.message || "Unknown error");
          }
        }));
        _LocalSolanaMobileWalletAdapterWallet_signAndSendTransaction.set(this, (...inputs) => __awaiter2(this, void 0, void 0, function* () {
          const outputs = [];
          for (const input of inputs) {
            const transaction = web3_js.VersionedTransaction.deserialize(input.transaction);
            const signature = yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_performSignAndSendTransaction, "f").call(this, transaction, input.options);
            outputs.push({ signature: base58__default["default"].decode(signature) });
          }
          return outputs;
        }));
        _LocalSolanaMobileWalletAdapterWallet_signTransaction.set(this, (...inputs) => __awaiter2(this, void 0, void 0, function* () {
          const transactions = inputs.map(({ transaction }) => web3_js.VersionedTransaction.deserialize(transaction));
          const signedTransactions = yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_performSignTransactions, "f").call(this, transactions);
          return signedTransactions.map((signedTransaction) => {
            const serializedTransaction = isVersionedTransaction2(signedTransaction) ? signedTransaction.serialize() : new Uint8Array(signedTransaction.serialize({
              requireAllSignatures: false,
              verifySignatures: false
            }));
            return { signedTransaction: serializedTransaction };
          });
        }));
        _LocalSolanaMobileWalletAdapterWallet_signMessage.set(this, (...inputs) => __awaiter2(this, void 0, void 0, function* () {
          const { authToken, chain } = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_assertIsAuthorized, "f").call(this);
          const addresses = inputs.map(({ account }) => fromUint8Array2(account.publicKey));
          const messages = inputs.map(({ message }) => message);
          try {
            return yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_transact, "f").call(this, (wallet) => __awaiter2(this, void 0, void 0, function* () {
              yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_performReauthorization, "f").call(this, wallet, authToken, chain);
              const signedMessages = yield wallet.signMessages({
                addresses,
                payloads: messages
              });
              return signedMessages.map((signedMessage) => {
                return { signedMessage, signature: signedMessage.slice(-SIGNATURE_LENGTH_IN_BYTES2) };
              });
            }));
          } catch (e) {
            throw new Error(e instanceof Error && e.message || "Unknown error");
          }
        }));
        _LocalSolanaMobileWalletAdapterWallet_signIn.set(this, (...inputs) => __awaiter2(this, void 0, void 0, function* () {
          const outputs = [];
          if (inputs.length > 1) {
            for (const input of inputs) {
              outputs.push(yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_performSignIn, "f").call(this, input));
            }
          } else {
            return [yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_performSignIn, "f").call(this, inputs[0])];
          }
          return outputs;
        }));
        _LocalSolanaMobileWalletAdapterWallet_performSignIn.set(this, (input) => __awaiter2(this, void 0, void 0, function* () {
          var _d, _e;
          __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_connecting, true, "f");
          try {
            const authorizationResult = yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_performAuthorization, "f").call(this, Object.assign(Object.assign({}, input), { domain: (_d = input === null || input === void 0 ? void 0 : input.domain) !== null && _d !== void 0 ? _d : window.location.host }));
            if (!authorizationResult.sign_in_result) {
              throw new Error("Sign in failed, no sign in result returned by wallet");
            }
            const signedInAddress = authorizationResult.sign_in_result.address;
            const signedInAccount = Object.assign(Object.assign({}, (_e = authorizationResult.accounts.find((acc) => acc.address == signedInAddress)) !== null && _e !== void 0 ? _e : {
              address: signedInAddress
            }), { publicKey: toUint8Array2(signedInAddress) });
            return {
              account: signedInAccount,
              signedMessage: toUint8Array2(authorizationResult.sign_in_result.signed_message),
              signature: toUint8Array2(authorizationResult.sign_in_result.signature)
            };
          } catch (e) {
            throw new Error(e instanceof Error && e.message || "Unknown error");
          } finally {
            __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_connecting, false, "f");
          }
        }));
        __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorizationCache, config.authorizationCache, "f");
        __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_appIdentity, config.appIdentity, "f");
        __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_chains, config.chains, "f");
        __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_chainSelector, config.chainSelector, "f");
        __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_onWalletNotFound, config.onWalletNotFound, "f");
        __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_optionalFeatures, {
          // We are forced to provide either SolanaSignAndSendTransaction or SolanaSignTransaction
          // because the wallet-adapter compatible wallet-standard wallet requires at least one of them.
          // MWA 2.0+ wallets must implement signAndSend and pre 2.0 wallets have always provided it so 
          // this is a safe assumption. We later update the features after we get the wallets capabilities. 
          [walletStandardFeatures.SolanaSignAndSendTransaction]: {
            version: "1.0.0",
            supportedTransactionVersions: ["legacy", 0],
            signAndSendTransaction: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_signAndSendTransaction, "f")
          }
        }, "f");
      }
      get version() {
        return __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_version, "f");
      }
      get name() {
        return __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_name, "f");
      }
      get url() {
        return __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_url, "f");
      }
      get icon() {
        return __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_icon, "f");
      }
      get chains() {
        return __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_chains, "f");
      }
      get features() {
        return Object.assign({ [features.StandardConnect]: {
          version: "1.0.0",
          connect: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_connect, "f")
        }, [features.StandardDisconnect]: {
          version: "1.0.0",
          disconnect: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_disconnect, "f")
        }, [features.StandardEvents]: {
          version: "1.0.0",
          on: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_on, "f")
        }, [walletStandardFeatures.SolanaSignMessage]: {
          version: "1.0.0",
          signMessage: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_signMessage, "f")
        }, [walletStandardFeatures.SolanaSignIn]: {
          version: "1.0.0",
          signIn: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_signIn, "f")
        } }, __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_optionalFeatures, "f"));
      }
      get accounts() {
        var _a, _b;
        return (_b = (_a = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, "f")) === null || _a === void 0 ? void 0 : _a.accounts) !== null && _b !== void 0 ? _b : [];
      }
      get connected() {
        return !!__classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, "f");
      }
      get isAuthorized() {
        return !!__classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, "f");
      }
      get currentAuthorization() {
        return __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, "f");
      }
      get cachedAuthorizationResult() {
        return __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorizationCache, "f").get();
      }
    };
    _LocalSolanaMobileWalletAdapterWallet_listeners = /* @__PURE__ */ new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_version = /* @__PURE__ */ new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_name = /* @__PURE__ */ new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_url = /* @__PURE__ */ new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_icon = /* @__PURE__ */ new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_appIdentity = /* @__PURE__ */ new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_authorization = /* @__PURE__ */ new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_authorizationCache = /* @__PURE__ */ new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_connecting = /* @__PURE__ */ new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_connectionGeneration = /* @__PURE__ */ new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_chains = /* @__PURE__ */ new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_chainSelector = /* @__PURE__ */ new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_optionalFeatures = /* @__PURE__ */ new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_onWalletNotFound = /* @__PURE__ */ new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_on = /* @__PURE__ */ new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_connect = /* @__PURE__ */ new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_performAuthorization = /* @__PURE__ */ new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_handleAuthorizationResult = /* @__PURE__ */ new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_handleWalletCapabilitiesResult = /* @__PURE__ */ new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_performReauthorization = /* @__PURE__ */ new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_disconnect = /* @__PURE__ */ new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_transact = /* @__PURE__ */ new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_assertIsAuthorized = /* @__PURE__ */ new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_accountsToWalletStandardAccounts = /* @__PURE__ */ new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_performSignTransactions = /* @__PURE__ */ new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_performSignAndSendTransaction = /* @__PURE__ */ new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_signAndSendTransaction = /* @__PURE__ */ new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_signTransaction = /* @__PURE__ */ new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_signMessage = /* @__PURE__ */ new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_signIn = /* @__PURE__ */ new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_performSignIn = /* @__PURE__ */ new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_instances = /* @__PURE__ */ new WeakSet(), _LocalSolanaMobileWalletAdapterWallet_emit = function _LocalSolanaMobileWalletAdapterWallet_emit2(event, ...args) {
      var _a;
      (_a = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_listeners, "f")[event]) === null || _a === void 0 ? void 0 : _a.forEach((listener) => listener.apply(null, args));
    }, _LocalSolanaMobileWalletAdapterWallet_off = function _LocalSolanaMobileWalletAdapterWallet_off2(event, listener) {
      var _a;
      __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_listeners, "f")[event] = (_a = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_listeners, "f")[event]) === null || _a === void 0 ? void 0 : _a.filter((existingListener) => listener !== existingListener);
    };
    var RemoteSolanaMobileWalletAdapterWallet = class {
      constructor(config) {
        _RemoteSolanaMobileWalletAdapterWallet_instances.add(this);
        _RemoteSolanaMobileWalletAdapterWallet_listeners.set(this, {});
        _RemoteSolanaMobileWalletAdapterWallet_version.set(this, "1.0.0");
        _RemoteSolanaMobileWalletAdapterWallet_name.set(this, SolanaMobileWalletAdapterWalletName3);
        _RemoteSolanaMobileWalletAdapterWallet_url.set(this, "https://solanamobile.com/wallets");
        _RemoteSolanaMobileWalletAdapterWallet_icon.set(this, icon);
        _RemoteSolanaMobileWalletAdapterWallet_appIdentity.set(this, void 0);
        _RemoteSolanaMobileWalletAdapterWallet_authorization.set(this, void 0);
        _RemoteSolanaMobileWalletAdapterWallet_authorizationCache.set(this, void 0);
        _RemoteSolanaMobileWalletAdapterWallet_connecting.set(this, false);
        _RemoteSolanaMobileWalletAdapterWallet_connectionGeneration.set(this, 0);
        _RemoteSolanaMobileWalletAdapterWallet_chains.set(this, []);
        _RemoteSolanaMobileWalletAdapterWallet_chainSelector.set(this, void 0);
        _RemoteSolanaMobileWalletAdapterWallet_optionalFeatures.set(this, void 0);
        _RemoteSolanaMobileWalletAdapterWallet_onWalletNotFound.set(this, void 0);
        _RemoteSolanaMobileWalletAdapterWallet_hostAuthority.set(this, void 0);
        _RemoteSolanaMobileWalletAdapterWallet_session.set(this, void 0);
        _RemoteSolanaMobileWalletAdapterWallet_on.set(this, (event, listener) => {
          var _a;
          ((_a = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_listeners, "f")[event]) === null || _a === void 0 ? void 0 : _a.push(listener)) || (__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_listeners, "f")[event] = [listener]);
          return () => __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_instances, "m", _RemoteSolanaMobileWalletAdapterWallet_off).call(this, event, listener);
        });
        _RemoteSolanaMobileWalletAdapterWallet_connect.set(this, ({ silent } = {}) => __awaiter2(this, void 0, void 0, function* () {
          if (__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_connecting, "f") || this.connected) {
            return { accounts: this.accounts };
          }
          __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_connecting, true, "f");
          try {
            yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_performAuthorization, "f").call(this);
          } catch (e) {
            throw new Error(e instanceof Error && e.message || "Unknown error");
          } finally {
            __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_connecting, false, "f");
          }
          return { accounts: this.accounts };
        }));
        _RemoteSolanaMobileWalletAdapterWallet_performAuthorization.set(this, (signInPayload) => __awaiter2(this, void 0, void 0, function* () {
          try {
            const cachedAuthorizationResult = yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorizationCache, "f").get();
            if (cachedAuthorizationResult) {
              __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_handleAuthorizationResult, "f").call(this, cachedAuthorizationResult);
              return cachedAuthorizationResult;
            }
            if (__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_session, "f"))
              __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_session, void 0, "f");
            const selectedChain = yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_chainSelector, "f").select(__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_chains, "f"));
            return yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_transact, "f").call(this, (wallet) => __awaiter2(this, void 0, void 0, function* () {
              const [capabilities, mwaAuthorizationResult] = yield Promise.all([
                wallet.getCapabilities(),
                wallet.authorize({
                  chain: selectedChain,
                  identity: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_appIdentity, "f"),
                  sign_in_payload: signInPayload
                })
              ]);
              const accounts = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_accountsToWalletStandardAccounts, "f").call(this, mwaAuthorizationResult.accounts);
              const authorizationResult = Object.assign(Object.assign({}, mwaAuthorizationResult), { accounts, chain: selectedChain });
              Promise.all([
                __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_handleWalletCapabilitiesResult, "f").call(this, capabilities),
                __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorizationCache, "f").set(authorizationResult),
                __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_handleAuthorizationResult, "f").call(this, authorizationResult)
              ]);
              return authorizationResult;
            }));
          } catch (e) {
            throw new Error(e instanceof Error && e.message || "Unknown error");
          }
        }));
        _RemoteSolanaMobileWalletAdapterWallet_handleAuthorizationResult.set(this, (authorization) => __awaiter2(this, void 0, void 0, function* () {
          var _a;
          const didPublicKeysChange = (
            // Case 1: We started from having no authorization.
            __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, "f") == null || // Case 2: The number of authorized accounts changed.
            ((_a = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, "f")) === null || _a === void 0 ? void 0 : _a.accounts.length) !== authorization.accounts.length || // Case 3: The new list of addresses isn't exactly the same as the old list, in the same order.
            __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, "f").accounts.some((account, ii) => account.address !== authorization.accounts[ii].address)
          );
          __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, authorization, "f");
          if (didPublicKeysChange) {
            __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_instances, "m", _RemoteSolanaMobileWalletAdapterWallet_emit).call(this, "change", { accounts: this.accounts });
          }
        }));
        _RemoteSolanaMobileWalletAdapterWallet_handleWalletCapabilitiesResult.set(this, (capabilities) => __awaiter2(this, void 0, void 0, function* () {
          const supportsSignTransaction = capabilities.features.includes("solana:signTransactions");
          const supportsSignAndSendTransaction = capabilities.supports_sign_and_send_transactions || capabilities.features.includes("solana:signAndSendTransaction");
          const didCapabilitiesChange = walletStandardFeatures.SolanaSignAndSendTransaction in this.features !== supportsSignAndSendTransaction || walletStandardFeatures.SolanaSignTransaction in this.features !== supportsSignTransaction;
          __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_optionalFeatures, Object.assign(Object.assign({}, supportsSignAndSendTransaction && {
            [walletStandardFeatures.SolanaSignAndSendTransaction]: {
              version: "1.0.0",
              supportedTransactionVersions: capabilities.supported_transaction_versions,
              signAndSendTransaction: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_signAndSendTransaction, "f")
            }
          }), supportsSignTransaction && {
            [walletStandardFeatures.SolanaSignTransaction]: {
              version: "1.0.0",
              supportedTransactionVersions: capabilities.supported_transaction_versions,
              signTransaction: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_signTransaction, "f")
            }
          }), "f");
          if (didCapabilitiesChange) {
            __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_instances, "m", _RemoteSolanaMobileWalletAdapterWallet_emit).call(this, "change", { features: this.features });
          }
        }));
        _RemoteSolanaMobileWalletAdapterWallet_performReauthorization.set(this, (wallet, authToken, chain) => __awaiter2(this, void 0, void 0, function* () {
          try {
            const mwaAuthorizationResult = yield wallet.authorize({
              auth_token: authToken,
              identity: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_appIdentity, "f")
            });
            const accounts = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_accountsToWalletStandardAccounts, "f").call(this, mwaAuthorizationResult.accounts);
            const authorization = Object.assign(Object.assign({}, mwaAuthorizationResult), { accounts, chain });
            Promise.all([
              __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorizationCache, "f").set(authorization),
              __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_handleAuthorizationResult, "f").call(this, authorization)
            ]);
          } catch (e) {
            __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_disconnect, "f").call(this);
            throw new Error(e instanceof Error && e.message || "Unknown error");
          }
        }));
        _RemoteSolanaMobileWalletAdapterWallet_disconnect.set(this, () => __awaiter2(this, void 0, void 0, function* () {
          var _b;
          var _c;
          (_b = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_session, "f")) === null || _b === void 0 ? void 0 : _b.close();
          __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorizationCache, "f").clear();
          __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_connecting, false, "f");
          __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_connectionGeneration, (_c = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_connectionGeneration, "f"), _c++, _c), "f");
          __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, void 0, "f");
          __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_session, void 0, "f");
          __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_instances, "m", _RemoteSolanaMobileWalletAdapterWallet_emit).call(this, "change", { accounts: this.accounts });
        }));
        _RemoteSolanaMobileWalletAdapterWallet_transact.set(this, (callback) => __awaiter2(this, void 0, void 0, function* () {
          var _d;
          const walletUriBase = (_d = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, "f")) === null || _d === void 0 ? void 0 : _d.wallet_uri_base;
          const baseConfig = walletUriBase ? { baseUri: walletUriBase } : void 0;
          const remoteConfig = Object.assign(Object.assign({}, baseConfig), { remoteHostAuthority: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_hostAuthority, "f") });
          const currentConnectionGeneration = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_connectionGeneration, "f");
          const modal = new RemoteConnectionModal();
          if (__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_session, "f")) {
            return callback(__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_session, "f").wallet);
          }
          try {
            const { associationUrl, close, wallet } = yield mobileWalletAdapterProtocolWeb3js.startRemoteScenario(remoteConfig);
            const removeCloseListener = modal.addEventListener("close", (event) => {
              if (event)
                close();
            });
            modal.initWithQR(associationUrl.toString());
            modal.open();
            __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_session, { close, wallet: yield wallet }, "f");
            removeCloseListener();
            modal.close();
            return yield callback(__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_session, "f").wallet);
          } catch (e) {
            modal.close();
            if (__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_connectionGeneration, "f") !== currentConnectionGeneration) {
              yield new Promise(() => {
              });
            }
            if (e instanceof Error && e.name === "SolanaMobileWalletAdapterError" && e.code === "ERROR_WALLET_NOT_FOUND") {
              yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_onWalletNotFound, "f").call(this, this);
            }
            throw e;
          }
        }));
        _RemoteSolanaMobileWalletAdapterWallet_assertIsAuthorized.set(this, () => {
          if (!__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, "f"))
            throw new Error("Wallet not connected");
          return { authToken: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, "f").auth_token, chain: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, "f").chain };
        });
        _RemoteSolanaMobileWalletAdapterWallet_accountsToWalletStandardAccounts.set(this, (accounts) => {
          return accounts.map((account) => {
            var _a, _b;
            const publicKey = toUint8Array2(account.address);
            return {
              address: base58__default["default"].encode(publicKey),
              publicKey,
              label: account.label,
              icon: account.icon,
              chains: (_a = account.chains) !== null && _a !== void 0 ? _a : __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_chains, "f"),
              // TODO: get supported features from getCapabilities API 
              features: (_b = account.features) !== null && _b !== void 0 ? _b : DEFAULT_FEATURES
            };
          });
        });
        _RemoteSolanaMobileWalletAdapterWallet_performSignTransactions.set(this, (transactions) => __awaiter2(this, void 0, void 0, function* () {
          const { authToken, chain } = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_assertIsAuthorized, "f").call(this);
          try {
            return yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_transact, "f").call(this, (wallet) => __awaiter2(this, void 0, void 0, function* () {
              yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_performReauthorization, "f").call(this, wallet, authToken, chain);
              const signedTransactions = yield wallet.signTransactions({
                transactions
              });
              return signedTransactions;
            }));
          } catch (e) {
            throw new Error(e instanceof Error && e.message || "Unknown error");
          }
        }));
        _RemoteSolanaMobileWalletAdapterWallet_performSignAndSendTransaction.set(this, (transaction, options) => __awaiter2(this, void 0, void 0, function* () {
          const { authToken, chain } = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_assertIsAuthorized, "f").call(this);
          try {
            return yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_transact, "f").call(this, (wallet) => __awaiter2(this, void 0, void 0, function* () {
              const [capabilities, _1] = yield Promise.all([
                wallet.getCapabilities(),
                __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_performReauthorization, "f").call(this, wallet, authToken, chain)
              ]);
              if (capabilities.supports_sign_and_send_transactions) {
                const signatures = yield wallet.signAndSendTransactions(Object.assign(Object.assign({}, options), { transactions: [transaction] }));
                return signatures[0];
              } else {
                throw new Error("connected wallet does not support signAndSendTransaction");
              }
            }));
          } catch (e) {
            throw new Error(e instanceof Error && e.message || "Unknown error");
          }
        }));
        _RemoteSolanaMobileWalletAdapterWallet_signAndSendTransaction.set(this, (...inputs) => __awaiter2(this, void 0, void 0, function* () {
          const outputs = [];
          for (const input of inputs) {
            const transaction = web3_js.VersionedTransaction.deserialize(input.transaction);
            const signature = yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_performSignAndSendTransaction, "f").call(this, transaction, input.options);
            outputs.push({ signature: base58__default["default"].decode(signature) });
          }
          return outputs;
        }));
        _RemoteSolanaMobileWalletAdapterWallet_signTransaction.set(this, (...inputs) => __awaiter2(this, void 0, void 0, function* () {
          const transactions = inputs.map(({ transaction }) => web3_js.VersionedTransaction.deserialize(transaction));
          const signedTransactions = yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_performSignTransactions, "f").call(this, transactions);
          return signedTransactions.map((signedTransaction) => {
            const serializedTransaction = isVersionedTransaction2(signedTransaction) ? signedTransaction.serialize() : new Uint8Array(signedTransaction.serialize({
              requireAllSignatures: false,
              verifySignatures: false
            }));
            return { signedTransaction: serializedTransaction };
          });
        }));
        _RemoteSolanaMobileWalletAdapterWallet_signMessage.set(this, (...inputs) => __awaiter2(this, void 0, void 0, function* () {
          const { authToken, chain } = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_assertIsAuthorized, "f").call(this);
          const addresses = inputs.map(({ account }) => fromUint8Array2(account.publicKey));
          const messages = inputs.map(({ message }) => message);
          try {
            return yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_transact, "f").call(this, (wallet) => __awaiter2(this, void 0, void 0, function* () {
              yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_performReauthorization, "f").call(this, wallet, authToken, chain);
              const signedMessages = yield wallet.signMessages({
                addresses,
                payloads: messages
              });
              return signedMessages.map((signedMessage) => {
                return { signedMessage, signature: signedMessage.slice(-SIGNATURE_LENGTH_IN_BYTES2) };
              });
            }));
          } catch (e) {
            throw new Error(e instanceof Error && e.message || "Unknown error");
          }
        }));
        _RemoteSolanaMobileWalletAdapterWallet_signIn.set(this, (...inputs) => __awaiter2(this, void 0, void 0, function* () {
          const outputs = [];
          if (inputs.length > 1) {
            for (const input of inputs) {
              outputs.push(yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_performSignIn, "f").call(this, input));
            }
          } else {
            return [yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_performSignIn, "f").call(this, inputs[0])];
          }
          return outputs;
        }));
        _RemoteSolanaMobileWalletAdapterWallet_performSignIn.set(this, (input) => __awaiter2(this, void 0, void 0, function* () {
          var _e, _f;
          __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_connecting, true, "f");
          try {
            const authorizationResult = yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_performAuthorization, "f").call(this, Object.assign(Object.assign({}, input), { domain: (_e = input === null || input === void 0 ? void 0 : input.domain) !== null && _e !== void 0 ? _e : window.location.host }));
            if (!authorizationResult.sign_in_result) {
              throw new Error("Sign in failed, no sign in result returned by wallet");
            }
            const signedInAddress = authorizationResult.sign_in_result.address;
            const signedInAccount = Object.assign(Object.assign({}, (_f = authorizationResult.accounts.find((acc) => acc.address == signedInAddress)) !== null && _f !== void 0 ? _f : {
              address: signedInAddress
            }), { publicKey: toUint8Array2(signedInAddress) });
            return {
              account: signedInAccount,
              signedMessage: toUint8Array2(authorizationResult.sign_in_result.signed_message),
              signature: toUint8Array2(authorizationResult.sign_in_result.signature)
            };
          } catch (e) {
            throw new Error(e instanceof Error && e.message || "Unknown error");
          } finally {
            __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_connecting, false, "f");
          }
        }));
        __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorizationCache, config.authorizationCache, "f");
        __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_appIdentity, config.appIdentity, "f");
        __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_chains, config.chains, "f");
        __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_chainSelector, config.chainSelector, "f");
        __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_hostAuthority, config.remoteHostAuthority, "f");
        __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_onWalletNotFound, config.onWalletNotFound, "f");
        __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_optionalFeatures, {
          // We are forced to provide either SolanaSignAndSendTransaction or SolanaSignTransaction
          // because the wallet-adapter compatible wallet-standard wallet requires at least one of them.
          // MWA 2.0+ wallets must implement signAndSend and pre 2.0 wallets have always provided it so 
          // this is a safe assumption. We later update the features after we get the wallets capabilities. 
          [walletStandardFeatures.SolanaSignAndSendTransaction]: {
            version: "1.0.0",
            supportedTransactionVersions: ["legacy", 0],
            signAndSendTransaction: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_signAndSendTransaction, "f")
          }
        }, "f");
      }
      get version() {
        return __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_version, "f");
      }
      get name() {
        return __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_name, "f");
      }
      get url() {
        return __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_url, "f");
      }
      get icon() {
        return __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_icon, "f");
      }
      get chains() {
        return __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_chains, "f");
      }
      get features() {
        return Object.assign({ [features.StandardConnect]: {
          version: "1.0.0",
          connect: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_connect, "f")
        }, [features.StandardDisconnect]: {
          version: "1.0.0",
          disconnect: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_disconnect, "f")
        }, [features.StandardEvents]: {
          version: "1.0.0",
          on: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_on, "f")
        }, [walletStandardFeatures.SolanaSignMessage]: {
          version: "1.0.0",
          signMessage: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_signMessage, "f")
        }, [walletStandardFeatures.SolanaSignIn]: {
          version: "1.0.0",
          signIn: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_signIn, "f")
        } }, __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_optionalFeatures, "f"));
      }
      get accounts() {
        var _a, _b;
        return (_b = (_a = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, "f")) === null || _a === void 0 ? void 0 : _a.accounts) !== null && _b !== void 0 ? _b : [];
      }
      get connected() {
        return !!__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_session, "f") && !!__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, "f");
      }
      get isAuthorized() {
        return !!__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, "f");
      }
      get currentAuthorization() {
        return __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, "f");
      }
      get cachedAuthorizationResult() {
        return __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorizationCache, "f").get();
      }
    };
    _RemoteSolanaMobileWalletAdapterWallet_listeners = /* @__PURE__ */ new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_version = /* @__PURE__ */ new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_name = /* @__PURE__ */ new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_url = /* @__PURE__ */ new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_icon = /* @__PURE__ */ new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_appIdentity = /* @__PURE__ */ new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_authorization = /* @__PURE__ */ new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_authorizationCache = /* @__PURE__ */ new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_connecting = /* @__PURE__ */ new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_connectionGeneration = /* @__PURE__ */ new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_chains = /* @__PURE__ */ new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_chainSelector = /* @__PURE__ */ new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_optionalFeatures = /* @__PURE__ */ new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_onWalletNotFound = /* @__PURE__ */ new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_hostAuthority = /* @__PURE__ */ new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_session = /* @__PURE__ */ new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_on = /* @__PURE__ */ new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_connect = /* @__PURE__ */ new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_performAuthorization = /* @__PURE__ */ new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_handleAuthorizationResult = /* @__PURE__ */ new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_handleWalletCapabilitiesResult = /* @__PURE__ */ new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_performReauthorization = /* @__PURE__ */ new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_disconnect = /* @__PURE__ */ new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_transact = /* @__PURE__ */ new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_assertIsAuthorized = /* @__PURE__ */ new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_accountsToWalletStandardAccounts = /* @__PURE__ */ new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_performSignTransactions = /* @__PURE__ */ new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_performSignAndSendTransaction = /* @__PURE__ */ new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_signAndSendTransaction = /* @__PURE__ */ new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_signTransaction = /* @__PURE__ */ new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_signMessage = /* @__PURE__ */ new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_signIn = /* @__PURE__ */ new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_performSignIn = /* @__PURE__ */ new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_instances = /* @__PURE__ */ new WeakSet(), _RemoteSolanaMobileWalletAdapterWallet_emit = function _RemoteSolanaMobileWalletAdapterWallet_emit2(event, ...args) {
      var _a;
      (_a = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_listeners, "f")[event]) === null || _a === void 0 ? void 0 : _a.forEach((listener) => listener.apply(null, args));
    }, _RemoteSolanaMobileWalletAdapterWallet_off = function _RemoteSolanaMobileWalletAdapterWallet_off2(event, listener) {
      var _a;
      __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_listeners, "f")[event] = (_a = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_listeners, "f")[event]) === null || _a === void 0 ? void 0 : _a.filter((existingListener) => listener !== existingListener);
    };
    var __classPrivateFieldSet4 = function(receiver, state, value, kind, f) {
      if (kind === "m")
        throw new TypeError("Private method is not writable");
      if (kind === "a" && !f)
        throw new TypeError("Private accessor was defined without a setter");
      if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver))
        throw new TypeError("Cannot write private member to an object whose class did not declare it");
      return kind === "a" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value), value;
    };
    var __classPrivateFieldGet4 = function(receiver, state, kind, f) {
      if (kind === "a" && !f)
        throw new TypeError("Private accessor was defined without a getter");
      if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver))
        throw new TypeError("Cannot read private member from an object whose class did not declare it");
      return kind === "m" ? f : kind === "a" ? f.call(receiver) : f ? f.value : state.get(receiver);
    };
    var _RegisterWalletEvent_detail;
    function registerWallet(wallet) {
      const callback = ({ register: register2 }) => register2(wallet);
      try {
        window.dispatchEvent(new RegisterWalletEvent(callback));
      } catch (error) {
        console.error("wallet-standard:register-wallet event could not be dispatched\n", error);
      }
      try {
        window.addEventListener("wallet-standard:app-ready", ({ detail: api }) => callback(api));
      } catch (error) {
        console.error("wallet-standard:app-ready event listener could not be added\n", error);
      }
    }
    var RegisterWalletEvent = class extends Event {
      constructor(callback) {
        super("wallet-standard:register-wallet", {
          bubbles: false,
          cancelable: false,
          composed: false
        });
        _RegisterWalletEvent_detail.set(this, void 0);
        __classPrivateFieldSet4(this, _RegisterWalletEvent_detail, callback, "f");
      }
      get detail() {
        return __classPrivateFieldGet4(this, _RegisterWalletEvent_detail, "f");
      }
      get type() {
        return "wallet-standard:register-wallet";
      }
      /** @deprecated */
      preventDefault() {
        throw new Error("preventDefault cannot be called");
      }
      /** @deprecated */
      stopImmediatePropagation() {
        throw new Error("stopImmediatePropagation cannot be called");
      }
      /** @deprecated */
      stopPropagation() {
        throw new Error("stopPropagation cannot be called");
      }
    };
    _RegisterWalletEvent_detail = /* @__PURE__ */ new WeakMap();
    function getIsLocalAssociationSupported() {
      return typeof window !== "undefined" && window.isSecureContext && typeof document !== "undefined" && /android/i.test(navigator.userAgent);
    }
    function getIsRemoteAssociationSupported() {
      return typeof window !== "undefined" && window.isSecureContext && typeof document !== "undefined" && !/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }
    function registerMwa(config) {
      if (getIsLocalAssociationSupported()) {
        registerWallet(new LocalSolanaMobileWalletAdapterWallet(config));
      } else if (getIsRemoteAssociationSupported() && config.remoteHostAuthority !== void 0) {
        registerWallet(new RemoteSolanaMobileWalletAdapterWallet(Object.assign(Object.assign({}, config), { remoteHostAuthority: config.remoteHostAuthority })));
      } else
        ;
    }
    var WALLET_NOT_FOUND_ERROR_MESSAGE = "To use mobile wallet adapter, you must have a compatible mobile wallet application installed on your device.";
    var BROWSER_NOT_SUPPORTED_ERROR_MESSAGE = "This browser appears to be incompatible with mobile wallet adapter. Open this page in a compatible mobile browser app and try again.";
    var ErrorModal = class extends EmbeddedModal {
      constructor() {
        super(...arguments);
        this.contentStyles = css;
        this.contentHtml = ErrorDialogHtml;
      }
      initWithError(error) {
        super.init();
        this.populateError(error);
      }
      populateError(error) {
        var _a, _b;
        const errorMessageElement = (_a = this.dom) === null || _a === void 0 ? void 0 : _a.getElementById("mobile-wallet-adapter-error-message");
        const actionBtn = (_b = this.dom) === null || _b === void 0 ? void 0 : _b.getElementById("mobile-wallet-adapter-error-action");
        if (errorMessageElement) {
          if (error.name === "SolanaMobileWalletAdapterError") {
            switch (error.code) {
              case "ERROR_WALLET_NOT_FOUND":
                errorMessageElement.innerHTML = WALLET_NOT_FOUND_ERROR_MESSAGE;
                if (actionBtn)
                  actionBtn.addEventListener("click", () => {
                    window.location.href = "https://solanamobile.com/wallets";
                  });
                return;
              case "ERROR_BROWSER_NOT_SUPPORTED":
                errorMessageElement.innerHTML = BROWSER_NOT_SUPPORTED_ERROR_MESSAGE;
                if (actionBtn)
                  actionBtn.style.display = "none";
                return;
            }
          }
          errorMessageElement.innerHTML = `An unexpected error occurred: ${error.message}`;
        } else {
          console.log("Failed to locate error dialog element");
        }
      }
    };
    var ErrorDialogHtml = `
<svg class="mobile-wallet-adapter-embedded-modal-error-icon" xmlns="http://www.w3.org/2000/svg" height="50px" viewBox="0 -960 960 960" width="50px" fill="#000000"><path d="M 280,-80 Q 197,-80 138.5,-138.5 80,-197 80,-280 80,-363 138.5,-421.5 197,-480 280,-480 q 83,0 141.5,58.5 58.5,58.5 58.5,141.5 0,83 -58.5,141.5 Q 363,-80 280,-80 Z M 824,-120 568,-376 Q 556,-389 542.5,-402.5 529,-416 516,-428 q 38,-24 61,-64 23,-40 23,-88 0,-75 -52.5,-127.5 Q 495,-760 420,-760 345,-760 292.5,-707.5 240,-655 240,-580 q 0,6 0.5,11.5 0.5,5.5 1.5,11.5 -18,2 -39.5,8 -21.5,6 -38.5,14 -2,-11 -3,-22 -1,-11 -1,-23 0,-109 75.5,-184.5 Q 311,-840 420,-840 q 109,0 184.5,75.5 75.5,75.5 75.5,184.5 0,43 -13.5,81.5 Q 653,-460 629,-428 l 251,252 z m -615,-61 71,-71 70,71 29,-28 -71,-71 71,-71 -28,-28 -71,71 -71,-71 -28,28 71,71 -71,71 z"/></svg>
<div class="mobile-wallet-adapter-embedded-modal-title">We can't find a wallet.</div>
<div id="mobile-wallet-adapter-error-message" class="mobile-wallet-adapter-embedded-modal-subtitle"></div>
<div>
    <button data-error-action id="mobile-wallet-adapter-error-action" class="mobile-wallet-adapter-embedded-modal-error-action">
        Find a wallet
    </button>
</div>
`;
    var css = `
.mobile-wallet-adapter-embedded-modal-content {
    text-align: center;
}

.mobile-wallet-adapter-embedded-modal-error-icon {
    margin-top: 24px;
}

.mobile-wallet-adapter-embedded-modal-title {
    margin: 18px 100px auto 100px;
    color: #000000;
    font-size: 2.75em;
    font-weight: 600;
}

.mobile-wallet-adapter-embedded-modal-subtitle {
    margin: 30px 60px 40px 60px;
    color: #000000;
    font-size: 1.25em;
    font-weight: 400;
}

.mobile-wallet-adapter-embedded-modal-error-action {
    display: block;
    width: 100%;
    height: 56px;
    /*margin-top: 40px;*/
    font-size: 1.25em;
    /*line-height: 24px;*/
    /*letter-spacing: -1%;*/
    background: #000000;
    color: #FFFFFF;
    border-radius: 18px;
}

/* Smaller screens */
@media all and (max-width: 600px) {
    .mobile-wallet-adapter-embedded-modal-title {
        font-size: 1.5em;
        margin-right: 12px;
        margin-left: 12px;
    }
    .mobile-wallet-adapter-embedded-modal-subtitle {
        margin-right: 12px;
        margin-left: 12px;
    }
}
`;
    function defaultErrorModalWalletNotFoundHandler() {
      return __awaiter2(this, void 0, void 0, function* () {
        if (typeof window !== "undefined") {
          const userAgent = window.navigator.userAgent.toLowerCase();
          const errorDialog = new ErrorModal();
          if (userAgent.includes("wv")) {
            errorDialog.initWithError({
              name: "SolanaMobileWalletAdapterError",
              code: "ERROR_BROWSER_NOT_SUPPORTED",
              message: ""
            });
          } else {
            errorDialog.initWithError({
              name: "SolanaMobileWalletAdapterError",
              code: "ERROR_WALLET_NOT_FOUND",
              message: ""
            });
          }
          errorDialog.open();
        }
      });
    }
    function createDefaultWalletNotFoundHandler2() {
      return () => __awaiter2(this, void 0, void 0, function* () {
        defaultErrorModalWalletNotFoundHandler();
      });
    }
    var CACHE_KEY = "SolanaMobileWalletAdapterDefaultAuthorizationCache";
    function createDefaultAuthorizationCache() {
      let storage;
      try {
        storage = window.localStorage;
      } catch (_a) {
      }
      return {
        clear() {
          return __awaiter2(this, void 0, void 0, function* () {
            if (!storage) {
              return;
            }
            try {
              storage.removeItem(CACHE_KEY);
            } catch (_a) {
            }
          });
        },
        get() {
          return __awaiter2(this, void 0, void 0, function* () {
            if (!storage) {
              return;
            }
            try {
              const parsed = JSON.parse(storage.getItem(CACHE_KEY));
              if (parsed && parsed.accounts) {
                const parsedAccounts = parsed.accounts.map((account) => {
                  return Object.assign(Object.assign({}, account), { publicKey: "publicKey" in account ? new Uint8Array(Object.values(account.publicKey)) : new web3_js.PublicKey(account.address).toBytes() });
                });
                return Object.assign(Object.assign({}, parsed), { accounts: parsedAccounts });
              } else
                return parsed || void 0;
            } catch (_a) {
            }
          });
        },
        set(authorizationResult) {
          return __awaiter2(this, void 0, void 0, function* () {
            if (!storage) {
              return;
            }
            try {
              storage.setItem(CACHE_KEY, JSON.stringify(authorizationResult));
            } catch (_a) {
            }
          });
        }
      };
    }
    function createDefaultChainSelector() {
      return {
        select(chains) {
          return __awaiter2(this, void 0, void 0, function* () {
            if (chains.length === 1) {
              return chains[0];
            } else if (chains.includes(walletStandardChains.SOLANA_MAINNET_CHAIN)) {
              return walletStandardChains.SOLANA_MAINNET_CHAIN;
            } else
              return chains[0];
          });
        }
      };
    }
    exports.LocalSolanaMobileWalletAdapterWallet = LocalSolanaMobileWalletAdapterWallet;
    exports.RemoteSolanaMobileWalletAdapterWallet = RemoteSolanaMobileWalletAdapterWallet;
    exports.SolanaMobileWalletAdapterWalletName = SolanaMobileWalletAdapterWalletName3;
    exports.createDefaultAuthorizationCache = createDefaultAuthorizationCache;
    exports.createDefaultChainSelector = createDefaultChainSelector;
    exports.createDefaultWalletNotFoundHandler = createDefaultWalletNotFoundHandler2;
    exports.defaultErrorModalWalletNotFoundHandler = defaultErrorModalWalletNotFoundHandler;
    exports.registerMwa = registerMwa;
  }
});

// node_modules/@solana-mobile/wallet-adapter-mobile/lib/cjs/index.browser.js
var require_index_browser3 = __commonJS({
  "node_modules/@solana-mobile/wallet-adapter-mobile/lib/cjs/index.browser.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    var walletAdapterBase = require_cjs3();
    var web3_js = (init_index_browser_esm(), __toCommonJS(index_browser_esm_exports));
    var walletStandardFeatures = require_cjs();
    var walletStandardMobile = require_index_browser2();
    function __awaiter2(thisArg, _arguments, P, generator) {
      function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
          resolve(value);
        });
      }
      return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
          try {
            step(generator.next(value));
          } catch (e) {
            reject(e);
          }
        }
        function rejected(value) {
          try {
            step(generator["throw"](value));
          } catch (e) {
            reject(e);
          }
        }
        function step(result) {
          result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
      });
    }
    function __classPrivateFieldGet4(receiver, state, kind, f) {
      if (kind === "a" && !f)
        throw new TypeError("Private accessor was defined without a getter");
      if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver))
        throw new TypeError("Cannot read private member from an object whose class did not declare it");
      return kind === "m" ? f : kind === "a" ? f.call(receiver) : f ? f.value : state.get(receiver);
    }
    function __classPrivateFieldSet4(receiver, state, value, kind, f) {
      if (kind === "m")
        throw new TypeError("Private method is not writable");
      if (kind === "a" && !f)
        throw new TypeError("Private accessor was defined without a setter");
      if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver))
        throw new TypeError("Cannot write private member to an object whose class did not declare it");
      return kind === "a" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value), value;
    }
    var StandardConnect2 = "standard:connect";
    var StandardDisconnect2 = "standard:disconnect";
    var StandardEvents2 = "standard:events";
    function fromUint8Array2(byteArray) {
      return window.btoa(String.fromCharCode.call(null, ...byteArray));
    }
    function getIsSupported() {
      return typeof window !== "undefined" && window.isSecureContext && typeof document !== "undefined" && /android/i.test(navigator.userAgent);
    }
    var _BaseSolanaMobileWalletAdapter_instances;
    var _BaseSolanaMobileWalletAdapter_wallet;
    var _BaseSolanaMobileWalletAdapter_connecting;
    var _BaseSolanaMobileWalletAdapter_readyState;
    var _BaseSolanaMobileWalletAdapter_accountSelector;
    var _BaseSolanaMobileWalletAdapter_selectedAccount;
    var _BaseSolanaMobileWalletAdapter_publicKey;
    var _BaseSolanaMobileWalletAdapter_handleChangeEvent;
    var _BaseSolanaMobileWalletAdapter_connect;
    var _BaseSolanaMobileWalletAdapter_declareWalletAsInstalled;
    var _BaseSolanaMobileWalletAdapter_assertIsAuthorized;
    var _BaseSolanaMobileWalletAdapter_performSignTransactions;
    var _BaseSolanaMobileWalletAdapter_runWithGuard;
    var SolanaMobileWalletAdapterWalletName3 = "Mobile Wallet Adapter";
    var SIGNATURE_LENGTH_IN_BYTES2 = 64;
    function isVersionedTransaction2(transaction) {
      return "version" in transaction;
    }
    function chainOrClusterToChainId(chain) {
      switch (chain) {
        case "mainnet-beta":
          return "solana:mainnet";
        case "testnet":
          return "solana:testnet";
        case "devnet":
          return "solana:devnet";
        default:
          return chain;
      }
    }
    var BaseSolanaMobileWalletAdapter = class extends walletAdapterBase.BaseSignInMessageSignerWalletAdapter {
      constructor(wallet, config) {
        super();
        _BaseSolanaMobileWalletAdapter_instances.add(this);
        this.supportedTransactionVersions = /* @__PURE__ */ new Set(
          // FIXME(#244): We can't actually know what versions are supported until we know which wallet we're talking to.
          ["legacy", 0]
        );
        _BaseSolanaMobileWalletAdapter_wallet.set(this, void 0);
        _BaseSolanaMobileWalletAdapter_connecting.set(this, false);
        _BaseSolanaMobileWalletAdapter_readyState.set(this, getIsSupported() ? walletAdapterBase.WalletReadyState.Loadable : walletAdapterBase.WalletReadyState.Unsupported);
        _BaseSolanaMobileWalletAdapter_accountSelector.set(this, void 0);
        _BaseSolanaMobileWalletAdapter_selectedAccount.set(this, void 0);
        _BaseSolanaMobileWalletAdapter_publicKey.set(this, void 0);
        _BaseSolanaMobileWalletAdapter_handleChangeEvent.set(this, (properties) => __awaiter2(this, void 0, void 0, function* () {
          if (properties.accounts && properties.accounts.length > 0) {
            __classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_instances, "m", _BaseSolanaMobileWalletAdapter_declareWalletAsInstalled).call(this);
            const nextSelectedAccount = yield __classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_accountSelector, "f").call(this, properties.accounts);
            if (nextSelectedAccount !== __classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_selectedAccount, "f")) {
              __classPrivateFieldSet4(this, _BaseSolanaMobileWalletAdapter_selectedAccount, nextSelectedAccount, "f");
              __classPrivateFieldSet4(this, _BaseSolanaMobileWalletAdapter_publicKey, void 0, "f");
              this.emit(
                "connect",
                // Having just set `this.#selectedAccount`, `this.publicKey` is definitely non-null
                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
                this.publicKey
              );
            }
          }
        }));
        __classPrivateFieldSet4(this, _BaseSolanaMobileWalletAdapter_accountSelector, (accounts) => __awaiter2(this, void 0, void 0, function* () {
          var _a;
          const selectedBase64EncodedAddress = yield config.addressSelector.select(accounts.map(({ publicKey }) => fromUint8Array2(publicKey)));
          return (_a = accounts.find(({ publicKey }) => fromUint8Array2(publicKey) === selectedBase64EncodedAddress)) !== null && _a !== void 0 ? _a : accounts[0];
        }), "f");
        __classPrivateFieldSet4(this, _BaseSolanaMobileWalletAdapter_wallet, wallet, "f");
        __classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_wallet, "f").features[StandardEvents2].on("change", __classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_handleChangeEvent, "f"));
        this.name = __classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_wallet, "f").name;
        this.icon = __classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_wallet, "f").icon;
        this.url = __classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_wallet, "f").url;
      }
      get publicKey() {
        var _a;
        if (!__classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_publicKey, "f") && __classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_selectedAccount, "f")) {
          try {
            __classPrivateFieldSet4(this, _BaseSolanaMobileWalletAdapter_publicKey, new web3_js.PublicKey(__classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_selectedAccount, "f").publicKey), "f");
          } catch (e) {
            throw new walletAdapterBase.WalletPublicKeyError(e instanceof Error && (e === null || e === void 0 ? void 0 : e.message) || "Unknown error", e);
          }
        }
        return (_a = __classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_publicKey, "f")) !== null && _a !== void 0 ? _a : null;
      }
      get connected() {
        return __classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_wallet, "f").connected;
      }
      get connecting() {
        return __classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_connecting, "f");
      }
      get readyState() {
        return __classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_readyState, "f");
      }
      /** @deprecated Use `autoConnect()` instead. */
      autoConnect_DO_NOT_USE_OR_YOU_WILL_BE_FIRED() {
        return __awaiter2(this, void 0, void 0, function* () {
          return yield this.autoConnect();
        });
      }
      autoConnect() {
        return __awaiter2(this, void 0, void 0, function* () {
          __classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_instances, "m", _BaseSolanaMobileWalletAdapter_connect).call(this, true);
        });
      }
      connect() {
        return __awaiter2(this, void 0, void 0, function* () {
          __classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_instances, "m", _BaseSolanaMobileWalletAdapter_connect).call(this);
        });
      }
      /** @deprecated Use `connect()` or `autoConnect()` instead. */
      performAuthorization(signInPayload) {
        return __awaiter2(this, void 0, void 0, function* () {
          try {
            const cachedAuthorizationResult = yield __classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_wallet, "f").cachedAuthorizationResult;
            if (cachedAuthorizationResult) {
              yield __classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_wallet, "f").features[StandardConnect2].connect({ silent: true });
              return cachedAuthorizationResult;
            }
            if (signInPayload) {
              yield __classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_wallet, "f").features[walletStandardFeatures.SolanaSignIn].signIn(signInPayload);
            } else
              yield __classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_wallet, "f").features[StandardConnect2].connect();
            const authorizationResult = yield yield __classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_wallet, "f").cachedAuthorizationResult;
            return authorizationResult;
          } catch (e) {
            throw new walletAdapterBase.WalletConnectionError(e instanceof Error && e.message || "Unknown error", e);
          }
        });
      }
      disconnect() {
        return __awaiter2(this, void 0, void 0, function* () {
          return yield __classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_instances, "m", _BaseSolanaMobileWalletAdapter_runWithGuard).call(this, () => __awaiter2(this, void 0, void 0, function* () {
            __classPrivateFieldSet4(this, _BaseSolanaMobileWalletAdapter_connecting, false, "f");
            __classPrivateFieldSet4(this, _BaseSolanaMobileWalletAdapter_publicKey, void 0, "f");
            __classPrivateFieldSet4(this, _BaseSolanaMobileWalletAdapter_selectedAccount, void 0, "f");
            yield __classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_wallet, "f").features[StandardDisconnect2].disconnect();
            this.emit("disconnect");
          }));
        });
      }
      signIn(input) {
        return __awaiter2(this, void 0, void 0, function* () {
          return __classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_instances, "m", _BaseSolanaMobileWalletAdapter_runWithGuard).call(this, () => __awaiter2(this, void 0, void 0, function* () {
            var _a;
            if (__classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_readyState, "f") !== walletAdapterBase.WalletReadyState.Installed && __classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_readyState, "f") !== walletAdapterBase.WalletReadyState.Loadable) {
              throw new walletAdapterBase.WalletNotReadyError();
            }
            __classPrivateFieldSet4(this, _BaseSolanaMobileWalletAdapter_connecting, true, "f");
            try {
              const outputs = yield __classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_wallet, "f").features[walletStandardFeatures.SolanaSignIn].signIn(Object.assign(Object.assign({}, input), { domain: (_a = input === null || input === void 0 ? void 0 : input.domain) !== null && _a !== void 0 ? _a : window.location.host }));
              if (outputs.length > 0) {
                return outputs[0];
              } else {
                throw new Error("Sign in failed, no sign in result returned by wallet");
              }
            } catch (e) {
              throw new walletAdapterBase.WalletConnectionError(e instanceof Error && e.message || "Unknown error", e);
            } finally {
              __classPrivateFieldSet4(this, _BaseSolanaMobileWalletAdapter_connecting, false, "f");
            }
          }));
        });
      }
      signMessage(message) {
        return __awaiter2(this, void 0, void 0, function* () {
          return yield __classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_instances, "m", _BaseSolanaMobileWalletAdapter_runWithGuard).call(this, () => __awaiter2(this, void 0, void 0, function* () {
            const account = __classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_instances, "m", _BaseSolanaMobileWalletAdapter_assertIsAuthorized).call(this);
            try {
              const outputs = yield __classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_wallet, "f").features[walletStandardFeatures.SolanaSignMessage].signMessage({
                account,
                message
              });
              return outputs[0].signature;
            } catch (error) {
              throw new walletAdapterBase.WalletSignMessageError(error === null || error === void 0 ? void 0 : error.message, error);
            }
          }));
        });
      }
      sendTransaction(transaction, connection, options) {
        return __awaiter2(this, void 0, void 0, function* () {
          return yield __classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_instances, "m", _BaseSolanaMobileWalletAdapter_runWithGuard).call(this, () => __awaiter2(this, void 0, void 0, function* () {
            const account = __classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_instances, "m", _BaseSolanaMobileWalletAdapter_assertIsAuthorized).call(this);
            try {
              let getTargetCommitment = function() {
                let targetCommitment;
                switch (connection.commitment) {
                  case "confirmed":
                  case "finalized":
                  case "processed":
                    targetCommitment = connection.commitment;
                    break;
                  default:
                    targetCommitment = "finalized";
                }
                let targetPreflightCommitment;
                switch (options === null || options === void 0 ? void 0 : options.preflightCommitment) {
                  case "confirmed":
                  case "finalized":
                  case "processed":
                    targetPreflightCommitment = options.preflightCommitment;
                    break;
                  case void 0:
                    targetPreflightCommitment = targetCommitment;
                    break;
                  default:
                    targetPreflightCommitment = "finalized";
                }
                const preflightCommitmentScore = targetPreflightCommitment === "finalized" ? 2 : targetPreflightCommitment === "confirmed" ? 1 : 0;
                const targetCommitmentScore = targetCommitment === "finalized" ? 2 : targetCommitment === "confirmed" ? 1 : 0;
                return preflightCommitmentScore < targetCommitmentScore ? targetPreflightCommitment : targetCommitment;
              };
              if (walletStandardFeatures.SolanaSignAndSendTransaction in __classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_wallet, "f").features) {
                const chain = chainOrClusterToChainId(__classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_wallet, "f").currentAuthorization.chain);
                const [signature] = (yield __classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_wallet, "f").features[walletStandardFeatures.SolanaSignAndSendTransaction].signAndSendTransaction({
                  account,
                  transaction: transaction.serialize(),
                  chain,
                  options: options ? {
                    skipPreflight: options.skipPreflight,
                    maxRetries: options.maxRetries
                  } : void 0
                })).map((output) => {
                  return fromUint8Array2(output.signature);
                });
                return signature;
              } else {
                const [signedTransaction] = yield __classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_instances, "m", _BaseSolanaMobileWalletAdapter_performSignTransactions).call(this, [transaction]);
                if (isVersionedTransaction2(signedTransaction)) {
                  return yield connection.sendTransaction(signedTransaction);
                } else {
                  const serializedTransaction = signedTransaction.serialize();
                  return yield connection.sendRawTransaction(serializedTransaction, Object.assign(Object.assign({}, options), { preflightCommitment: getTargetCommitment() }));
                }
              }
            } catch (error) {
              throw new walletAdapterBase.WalletSendTransactionError(error === null || error === void 0 ? void 0 : error.message, error);
            }
          }));
        });
      }
      signTransaction(transaction) {
        return __awaiter2(this, void 0, void 0, function* () {
          return yield __classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_instances, "m", _BaseSolanaMobileWalletAdapter_runWithGuard).call(this, () => __awaiter2(this, void 0, void 0, function* () {
            const [signedTransaction] = yield __classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_instances, "m", _BaseSolanaMobileWalletAdapter_performSignTransactions).call(this, [transaction]);
            return signedTransaction;
          }));
        });
      }
      signAllTransactions(transactions) {
        return __awaiter2(this, void 0, void 0, function* () {
          return yield __classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_instances, "m", _BaseSolanaMobileWalletAdapter_runWithGuard).call(this, () => __awaiter2(this, void 0, void 0, function* () {
            const signedTransactions = yield __classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_instances, "m", _BaseSolanaMobileWalletAdapter_performSignTransactions).call(this, transactions);
            return signedTransactions;
          }));
        });
      }
    };
    _BaseSolanaMobileWalletAdapter_wallet = /* @__PURE__ */ new WeakMap(), _BaseSolanaMobileWalletAdapter_connecting = /* @__PURE__ */ new WeakMap(), _BaseSolanaMobileWalletAdapter_readyState = /* @__PURE__ */ new WeakMap(), _BaseSolanaMobileWalletAdapter_accountSelector = /* @__PURE__ */ new WeakMap(), _BaseSolanaMobileWalletAdapter_selectedAccount = /* @__PURE__ */ new WeakMap(), _BaseSolanaMobileWalletAdapter_publicKey = /* @__PURE__ */ new WeakMap(), _BaseSolanaMobileWalletAdapter_handleChangeEvent = /* @__PURE__ */ new WeakMap(), _BaseSolanaMobileWalletAdapter_instances = /* @__PURE__ */ new WeakSet(), _BaseSolanaMobileWalletAdapter_connect = function _BaseSolanaMobileWalletAdapter_connect2(autoConnect = false) {
      return __awaiter2(this, void 0, void 0, function* () {
        if (this.connecting || this.connected) {
          return;
        }
        return yield __classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_instances, "m", _BaseSolanaMobileWalletAdapter_runWithGuard).call(this, () => __awaiter2(this, void 0, void 0, function* () {
          if (__classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_readyState, "f") !== walletAdapterBase.WalletReadyState.Installed && __classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_readyState, "f") !== walletAdapterBase.WalletReadyState.Loadable) {
            throw new walletAdapterBase.WalletNotReadyError();
          }
          __classPrivateFieldSet4(this, _BaseSolanaMobileWalletAdapter_connecting, true, "f");
          try {
            yield __classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_wallet, "f").features[StandardConnect2].connect({ silent: autoConnect });
          } catch (e) {
            throw new walletAdapterBase.WalletConnectionError(e instanceof Error && e.message || "Unknown error", e);
          } finally {
            __classPrivateFieldSet4(this, _BaseSolanaMobileWalletAdapter_connecting, false, "f");
          }
        }));
      });
    }, _BaseSolanaMobileWalletAdapter_declareWalletAsInstalled = function _BaseSolanaMobileWalletAdapter_declareWalletAsInstalled2() {
      if (__classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_readyState, "f") !== walletAdapterBase.WalletReadyState.Installed) {
        this.emit("readyStateChange", __classPrivateFieldSet4(this, _BaseSolanaMobileWalletAdapter_readyState, walletAdapterBase.WalletReadyState.Installed, "f"));
      }
    }, _BaseSolanaMobileWalletAdapter_assertIsAuthorized = function _BaseSolanaMobileWalletAdapter_assertIsAuthorized2() {
      if (!__classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_wallet, "f").isAuthorized || !__classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_selectedAccount, "f"))
        throw new walletAdapterBase.WalletNotConnectedError();
      return __classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_selectedAccount, "f");
    }, _BaseSolanaMobileWalletAdapter_performSignTransactions = function _BaseSolanaMobileWalletAdapter_performSignTransactions2(transactions) {
      return __awaiter2(this, void 0, void 0, function* () {
        const account = __classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_instances, "m", _BaseSolanaMobileWalletAdapter_assertIsAuthorized).call(this);
        try {
          if (walletStandardFeatures.SolanaSignTransaction in __classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_wallet, "f").features) {
            return __classPrivateFieldGet4(this, _BaseSolanaMobileWalletAdapter_wallet, "f").features[walletStandardFeatures.SolanaSignTransaction].signTransaction(...transactions.map((value) => {
              return { account, transaction: value.serialize() };
            })).then((outputs) => {
              return outputs.map((output) => {
                const byteArray = output.signedTransaction;
                const numSignatures = byteArray[0];
                const messageOffset = numSignatures * SIGNATURE_LENGTH_IN_BYTES2 + 1;
                const version = web3_js.VersionedMessage.deserializeMessageVersion(byteArray.slice(messageOffset, byteArray.length));
                if (version === "legacy") {
                  return web3_js.Transaction.from(byteArray);
                } else {
                  return web3_js.VersionedTransaction.deserialize(byteArray);
                }
              });
            });
          } else {
            throw new Error("Connected wallet does not support signing transactions");
          }
        } catch (error) {
          throw new walletAdapterBase.WalletSignTransactionError(error === null || error === void 0 ? void 0 : error.message, error);
        }
      });
    }, _BaseSolanaMobileWalletAdapter_runWithGuard = function _BaseSolanaMobileWalletAdapter_runWithGuard2(callback) {
      return __awaiter2(this, void 0, void 0, function* () {
        try {
          return yield callback();
        } catch (e) {
          this.emit("error", e);
          throw e;
        }
      });
    };
    var LocalSolanaMobileWalletAdapter = class extends BaseSolanaMobileWalletAdapter {
      constructor(config) {
        var _a;
        const chain = chainOrClusterToChainId((_a = config.chain) !== null && _a !== void 0 ? _a : config.cluster);
        super(new walletStandardMobile.LocalSolanaMobileWalletAdapterWallet({
          appIdentity: config.appIdentity,
          authorizationCache: {
            set: config.authorizationResultCache.set,
            get: () => __awaiter2(this, void 0, void 0, function* () {
              const authorizationResult = yield config.authorizationResultCache.get();
              if (authorizationResult && "chain" in authorizationResult) {
                return authorizationResult;
              } else if (authorizationResult) {
                return Object.assign(Object.assign({}, authorizationResult), { chain });
              } else
                return void 0;
            }),
            clear: config.authorizationResultCache.clear
          },
          chains: [chain],
          chainSelector: walletStandardMobile.createDefaultChainSelector(),
          onWalletNotFound: () => __awaiter2(this, void 0, void 0, function* () {
            config.onWalletNotFound(this);
          })
        }), {
          addressSelector: config.addressSelector,
          chain
        });
      }
    };
    var RemoteSolanaMobileWalletAdapter = class extends BaseSolanaMobileWalletAdapter {
      constructor(config) {
        const chain = chainOrClusterToChainId(config.chain);
        super(new walletStandardMobile.RemoteSolanaMobileWalletAdapterWallet({
          appIdentity: config.appIdentity,
          authorizationCache: {
            set: config.authorizationResultCache.set,
            get: () => __awaiter2(this, void 0, void 0, function* () {
              const authorizationResult = yield config.authorizationResultCache.get();
              if (authorizationResult && "chain" in authorizationResult) {
                return authorizationResult;
              } else if (authorizationResult) {
                return Object.assign(Object.assign({}, authorizationResult), { chain });
              } else
                return void 0;
            }),
            clear: config.authorizationResultCache.clear
          },
          chains: [chain],
          chainSelector: walletStandardMobile.createDefaultChainSelector(),
          remoteHostAuthority: config.remoteHostAuthority,
          onWalletNotFound: () => __awaiter2(this, void 0, void 0, function* () {
            config.onWalletNotFound(this);
          })
        }), {
          addressSelector: config.addressSelector,
          chain
        });
      }
    };
    var SolanaMobileWalletAdapter2 = class extends LocalSolanaMobileWalletAdapter {
    };
    function createDefaultAddressSelector2() {
      return {
        select(addresses) {
          return __awaiter2(this, void 0, void 0, function* () {
            return addresses[0];
          });
        }
      };
    }
    function createDefaultAuthorizationResultCache2() {
      return walletStandardMobile.createDefaultAuthorizationCache();
    }
    function defaultWalletNotFoundHandler(mobileWalletAdapter) {
      return __awaiter2(this, void 0, void 0, function* () {
        return walletStandardMobile.defaultErrorModalWalletNotFoundHandler();
      });
    }
    function createDefaultWalletNotFoundHandler2() {
      return defaultWalletNotFoundHandler;
    }
    exports.LocalSolanaMobileWalletAdapter = LocalSolanaMobileWalletAdapter;
    exports.RemoteSolanaMobileWalletAdapter = RemoteSolanaMobileWalletAdapter;
    exports.SolanaMobileWalletAdapter = SolanaMobileWalletAdapter2;
    exports.SolanaMobileWalletAdapterWalletName = SolanaMobileWalletAdapterWalletName3;
    exports.createDefaultAddressSelector = createDefaultAddressSelector2;
    exports.createDefaultAuthorizationResultCache = createDefaultAuthorizationResultCache2;
    exports.createDefaultWalletNotFoundHandler = createDefaultWalletNotFoundHandler2;
  }
});

// node_modules/@solana/wallet-adapter-react/lib/esm/ConnectionProvider.js
init_index_browser_esm();
var import_react2 = __toESM(require_react(), 1);

// node_modules/@solana/wallet-adapter-react/lib/esm/useConnection.js
var import_react = __toESM(require_react(), 1);
var ConnectionContext = (0, import_react.createContext)({});
function useConnection() {
  return (0, import_react.useContext)(ConnectionContext);
}

// node_modules/@solana/wallet-adapter-react/lib/esm/ConnectionProvider.js
var ConnectionProvider = ({ children, endpoint, config = { commitment: "confirmed" } }) => {
  const connection = (0, import_react2.useMemo)(() => new Connection(endpoint, config), [endpoint, config]);
  return import_react2.default.createElement(ConnectionContext.Provider, { value: { connection } }, children);
};

// node_modules/@solana/wallet-adapter-react/lib/esm/errors.js
var WalletNotSelectedError = class extends WalletError {
  constructor() {
    super(...arguments);
    this.name = "WalletNotSelectedError";
  }
};

// node_modules/@solana/wallet-adapter-react/lib/esm/useAnchorWallet.js
var import_react4 = __toESM(require_react(), 1);

// node_modules/@solana/wallet-adapter-react/lib/esm/useWallet.js
var import_react3 = __toESM(require_react(), 1);
var EMPTY_ARRAY = [];
var DEFAULT_CONTEXT = {
  autoConnect: false,
  connecting: false,
  connected: false,
  disconnecting: false,
  select() {
    logMissingProviderError("call", "select");
  },
  connect() {
    return Promise.reject(logMissingProviderError("call", "connect"));
  },
  disconnect() {
    return Promise.reject(logMissingProviderError("call", "disconnect"));
  },
  sendTransaction() {
    return Promise.reject(logMissingProviderError("call", "sendTransaction"));
  },
  signTransaction() {
    return Promise.reject(logMissingProviderError("call", "signTransaction"));
  },
  signAllTransactions() {
    return Promise.reject(logMissingProviderError("call", "signAllTransactions"));
  },
  signMessage() {
    return Promise.reject(logMissingProviderError("call", "signMessage"));
  },
  signIn() {
    return Promise.reject(logMissingProviderError("call", "signIn"));
  }
};
Object.defineProperty(DEFAULT_CONTEXT, "wallets", {
  get() {
    logMissingProviderError("read", "wallets");
    return EMPTY_ARRAY;
  }
});
Object.defineProperty(DEFAULT_CONTEXT, "wallet", {
  get() {
    logMissingProviderError("read", "wallet");
    return null;
  }
});
Object.defineProperty(DEFAULT_CONTEXT, "publicKey", {
  get() {
    logMissingProviderError("read", "publicKey");
    return null;
  }
});
function logMissingProviderError(action, property) {
  const error = new Error(`You have tried to ${action} "${property}" on a WalletContext without providing one. Make sure to render a WalletProvider as an ancestor of the component that uses WalletContext.`);
  console.error(error);
  return error;
}
var WalletContext = (0, import_react3.createContext)(DEFAULT_CONTEXT);
function useWallet() {
  return (0, import_react3.useContext)(WalletContext);
}

// node_modules/@solana/wallet-adapter-react/lib/esm/useAnchorWallet.js
function useAnchorWallet() {
  const { publicKey, signTransaction, signAllTransactions } = useWallet();
  return (0, import_react4.useMemo)(() => publicKey && signTransaction && signAllTransactions ? { publicKey, signTransaction, signAllTransactions } : void 0, [publicKey, signTransaction, signAllTransactions]);
}

// node_modules/@solana/wallet-adapter-react/lib/esm/useLocalStorage.js
var import_react5 = __toESM(require_react(), 1);
function useLocalStorage(key, defaultState) {
  const state = (0, import_react5.useState)(() => {
    try {
      const value2 = localStorage.getItem(key);
      if (value2)
        return JSON.parse(value2);
    } catch (error) {
      if (typeof window !== "undefined") {
        console.error(error);
      }
    }
    return defaultState;
  });
  const value = state[0];
  const isFirstRenderRef = (0, import_react5.useRef)(true);
  (0, import_react5.useEffect)(() => {
    if (isFirstRenderRef.current) {
      isFirstRenderRef.current = false;
      return;
    }
    try {
      if (value === null) {
        localStorage.removeItem(key);
      } else {
        localStorage.setItem(key, JSON.stringify(value));
      }
    } catch (error) {
      if (typeof window !== "undefined") {
        console.error(error);
      }
    }
  }, [value, key]);
  return state;
}

// node_modules/@solana/wallet-adapter-react/lib/esm/WalletProvider.js
var import_wallet_adapter_mobile2 = __toESM(require_index_browser3(), 1);

// node_modules/@solana/wallet-standard-util/lib/esm/commitment.js
function getCommitment(commitment) {
  switch (commitment) {
    case "processed":
    case "confirmed":
    case "finalized":
    case void 0:
      return commitment;
    case "recent":
      return "processed";
    case "single":
    case "singleGossip":
      return "confirmed";
    case "max":
    case "root":
      return "finalized";
    default:
      return void 0;
  }
}

// node_modules/@solana/wallet-standard-util/lib/esm/endpoint.js
var MAINNET_ENDPOINT = "https://api.mainnet-beta.solana.com";
function getChainForEndpoint(endpoint) {
  if (endpoint.includes(MAINNET_ENDPOINT))
    return SOLANA_MAINNET_CHAIN;
  if (/\bdevnet\b/i.test(endpoint))
    return SOLANA_DEVNET_CHAIN;
  if (/\btestnet\b/i.test(endpoint))
    return SOLANA_TESTNET_CHAIN;
  if (/\blocalhost\b/i.test(endpoint) || /\b127\.0\.0\.1\b/.test(endpoint))
    return SOLANA_LOCALNET_CHAIN;
  return SOLANA_MAINNET_CHAIN;
}

// node_modules/@solana/wallet-standard-util/lib/esm/signMessage.js
init_ed25519();

// node_modules/@solana/wallet-standard-util/lib/esm/signIn.js
var DOMAIN = "(?<domain>[^\\n]+?) wants you to sign in with your Solana account:\\n";
var ADDRESS = "(?<address>[^\\n]+)(?:\\n|$)";
var STATEMENT = "(?:\\n(?<statement>[\\S\\s]*?)(?:\\n|$))??";
var URI = "(?:\\nURI: (?<uri>[^\\n]+))?";
var VERSION = "(?:\\nVersion: (?<version>[^\\n]+))?";
var CHAIN_ID = "(?:\\nChain ID: (?<chainId>[^\\n]+))?";
var NONCE = "(?:\\nNonce: (?<nonce>[^\\n]+))?";
var ISSUED_AT = "(?:\\nIssued At: (?<issuedAt>[^\\n]+))?";
var EXPIRATION_TIME = "(?:\\nExpiration Time: (?<expirationTime>[^\\n]+))?";
var NOT_BEFORE = "(?:\\nNot Before: (?<notBefore>[^\\n]+))?";
var REQUEST_ID = "(?:\\nRequest ID: (?<requestId>[^\\n]+))?";
var RESOURCES = "(?:\\nResources:(?<resources>(?:\\n- [^\\n]+)*))?";
var FIELDS = `${URI}${VERSION}${CHAIN_ID}${NONCE}${ISSUED_AT}${EXPIRATION_TIME}${NOT_BEFORE}${REQUEST_ID}${RESOURCES}`;
var MESSAGE = new RegExp(`^${DOMAIN}${ADDRESS}${STATEMENT}${FIELDS}\\n*$`);

// node_modules/@solana/wallet-standard-wallet-adapter-base/lib/esm/adapter.js
init_index_browser_esm();
var import_bs582 = __toESM(require_bs58(), 1);
var __classPrivateFieldGet = function(receiver, state, kind, f) {
  if (kind === "a" && !f)
    throw new TypeError("Private accessor was defined without a getter");
  if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver))
    throw new TypeError("Cannot read private member from an object whose class did not declare it");
  return kind === "m" ? f : kind === "a" ? f.call(receiver) : f ? f.value : state.get(receiver);
};
var __classPrivateFieldSet = function(receiver, state, value, kind, f) {
  if (kind === "m")
    throw new TypeError("Private method is not writable");
  if (kind === "a" && !f)
    throw new TypeError("Private accessor was defined without a setter");
  if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver))
    throw new TypeError("Cannot write private member to an object whose class did not declare it");
  return kind === "a" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value), value;
};
var _StandardWalletAdapter_instances;
var _StandardWalletAdapter_account;
var _StandardWalletAdapter_publicKey;
var _StandardWalletAdapter_connecting;
var _StandardWalletAdapter_disconnecting;
var _StandardWalletAdapter_off;
var _StandardWalletAdapter_supportedTransactionVersions;
var _StandardWalletAdapter_wallet;
var _StandardWalletAdapter_readyState;
var _StandardWalletAdapter_connect;
var _StandardWalletAdapter_connected;
var _StandardWalletAdapter_disconnected;
var _StandardWalletAdapter_reset;
var _StandardWalletAdapter_changed;
var _StandardWalletAdapter_signTransaction;
var _StandardWalletAdapter_signAllTransactions;
var _StandardWalletAdapter_signMessage;
var _StandardWalletAdapter_signIn;
var StandardWalletAdapter = class extends BaseWalletAdapter {
  get name() {
    return __classPrivateFieldGet(this, _StandardWalletAdapter_wallet, "f").name;
  }
  get url() {
    return "https://github.com/solana-labs/wallet-standard";
  }
  get icon() {
    return __classPrivateFieldGet(this, _StandardWalletAdapter_wallet, "f").icon;
  }
  get readyState() {
    return __classPrivateFieldGet(this, _StandardWalletAdapter_readyState, "f");
  }
  get publicKey() {
    return __classPrivateFieldGet(this, _StandardWalletAdapter_publicKey, "f");
  }
  get connecting() {
    return __classPrivateFieldGet(this, _StandardWalletAdapter_connecting, "f");
  }
  get supportedTransactionVersions() {
    return __classPrivateFieldGet(this, _StandardWalletAdapter_supportedTransactionVersions, "f");
  }
  get wallet() {
    return __classPrivateFieldGet(this, _StandardWalletAdapter_wallet, "f");
  }
  get standard() {
    return true;
  }
  constructor({ wallet }) {
    super();
    _StandardWalletAdapter_instances.add(this);
    _StandardWalletAdapter_account.set(this, void 0);
    _StandardWalletAdapter_publicKey.set(this, void 0);
    _StandardWalletAdapter_connecting.set(this, void 0);
    _StandardWalletAdapter_disconnecting.set(this, void 0);
    _StandardWalletAdapter_off.set(this, void 0);
    _StandardWalletAdapter_supportedTransactionVersions.set(this, void 0);
    _StandardWalletAdapter_wallet.set(this, void 0);
    _StandardWalletAdapter_readyState.set(this, typeof window === "undefined" || typeof document === "undefined" ? WalletReadyState.Unsupported : WalletReadyState.Installed);
    _StandardWalletAdapter_changed.set(this, (properties) => {
      if ("accounts" in properties) {
        const account = __classPrivateFieldGet(this, _StandardWalletAdapter_wallet, "f").accounts[0];
        if (__classPrivateFieldGet(this, _StandardWalletAdapter_account, "f") && !__classPrivateFieldGet(this, _StandardWalletAdapter_disconnecting, "f") && account !== __classPrivateFieldGet(this, _StandardWalletAdapter_account, "f")) {
          if (account) {
            __classPrivateFieldGet(this, _StandardWalletAdapter_instances, "m", _StandardWalletAdapter_connected).call(this, account);
          } else {
            this.emit("error", new WalletDisconnectedError());
            __classPrivateFieldGet(this, _StandardWalletAdapter_instances, "m", _StandardWalletAdapter_disconnected).call(this);
          }
        }
      }
      if ("features" in properties) {
        __classPrivateFieldGet(this, _StandardWalletAdapter_instances, "m", _StandardWalletAdapter_reset).call(this);
      }
    });
    __classPrivateFieldSet(this, _StandardWalletAdapter_wallet, wallet, "f");
    __classPrivateFieldSet(this, _StandardWalletAdapter_account, null, "f");
    __classPrivateFieldSet(this, _StandardWalletAdapter_publicKey, null, "f");
    __classPrivateFieldSet(this, _StandardWalletAdapter_connecting, false, "f");
    __classPrivateFieldSet(this, _StandardWalletAdapter_disconnecting, false, "f");
    __classPrivateFieldSet(this, _StandardWalletAdapter_off, __classPrivateFieldGet(this, _StandardWalletAdapter_wallet, "f").features[StandardEvents].on("change", __classPrivateFieldGet(this, _StandardWalletAdapter_changed, "f")), "f");
    __classPrivateFieldGet(this, _StandardWalletAdapter_instances, "m", _StandardWalletAdapter_reset).call(this);
  }
  destroy() {
    __classPrivateFieldSet(this, _StandardWalletAdapter_account, null, "f");
    __classPrivateFieldSet(this, _StandardWalletAdapter_publicKey, null, "f");
    __classPrivateFieldSet(this, _StandardWalletAdapter_connecting, false, "f");
    __classPrivateFieldSet(this, _StandardWalletAdapter_disconnecting, false, "f");
    const off = __classPrivateFieldGet(this, _StandardWalletAdapter_off, "f");
    if (off) {
      __classPrivateFieldSet(this, _StandardWalletAdapter_off, null, "f");
      off();
    }
  }
  async autoConnect() {
    return __classPrivateFieldGet(this, _StandardWalletAdapter_instances, "m", _StandardWalletAdapter_connect).call(this, { silent: true });
  }
  async connect() {
    return __classPrivateFieldGet(this, _StandardWalletAdapter_instances, "m", _StandardWalletAdapter_connect).call(this);
  }
  async disconnect() {
    if (StandardDisconnect in __classPrivateFieldGet(this, _StandardWalletAdapter_wallet, "f").features) {
      try {
        __classPrivateFieldSet(this, _StandardWalletAdapter_disconnecting, true, "f");
        await __classPrivateFieldGet(this, _StandardWalletAdapter_wallet, "f").features[StandardDisconnect].disconnect();
      } catch (error) {
        this.emit("error", new WalletDisconnectionError(error == null ? void 0 : error.message, error));
      } finally {
        __classPrivateFieldSet(this, _StandardWalletAdapter_disconnecting, false, "f");
      }
    }
    __classPrivateFieldGet(this, _StandardWalletAdapter_instances, "m", _StandardWalletAdapter_disconnected).call(this);
  }
  async sendTransaction(transaction, connection, options = {}) {
    try {
      const account = __classPrivateFieldGet(this, _StandardWalletAdapter_account, "f");
      if (!account)
        throw new WalletNotConnectedError();
      let feature;
      if (SolanaSignAndSendTransaction in __classPrivateFieldGet(this, _StandardWalletAdapter_wallet, "f").features) {
        if (account.features.includes(SolanaSignAndSendTransaction)) {
          feature = SolanaSignAndSendTransaction;
        } else if (SolanaSignTransaction in __classPrivateFieldGet(this, _StandardWalletAdapter_wallet, "f").features && account.features.includes(SolanaSignTransaction)) {
          feature = SolanaSignTransaction;
        } else {
          throw new WalletAccountError();
        }
      } else if (SolanaSignTransaction in __classPrivateFieldGet(this, _StandardWalletAdapter_wallet, "f").features) {
        if (!account.features.includes(SolanaSignTransaction))
          throw new WalletAccountError();
        feature = SolanaSignTransaction;
      } else {
        throw new WalletConfigError();
      }
      const chain = getChainForEndpoint(connection.rpcEndpoint);
      if (!account.chains.includes(chain))
        throw new WalletSendTransactionError();
      try {
        const { signers, ...sendOptions } = options;
        let serializedTransaction;
        if (isVersionedTransaction(transaction)) {
          (signers == null ? void 0 : signers.length) && transaction.sign(signers);
          serializedTransaction = transaction.serialize();
        } else {
          transaction = await this.prepareTransaction(transaction, connection, sendOptions);
          (signers == null ? void 0 : signers.length) && transaction.partialSign(...signers);
          serializedTransaction = new Uint8Array(transaction.serialize({
            requireAllSignatures: false,
            verifySignatures: false
          }));
        }
        if (feature === SolanaSignAndSendTransaction) {
          const [output] = await __classPrivateFieldGet(this, _StandardWalletAdapter_wallet, "f").features[SolanaSignAndSendTransaction].signAndSendTransaction({
            account,
            chain,
            transaction: serializedTransaction,
            options: {
              preflightCommitment: getCommitment(sendOptions.preflightCommitment || connection.commitment),
              skipPreflight: sendOptions.skipPreflight,
              maxRetries: sendOptions.maxRetries,
              minContextSlot: sendOptions.minContextSlot
            }
          });
          return import_bs582.default.encode(output.signature);
        } else {
          const [output] = await __classPrivateFieldGet(this, _StandardWalletAdapter_wallet, "f").features[SolanaSignTransaction].signTransaction({
            account,
            chain,
            transaction: serializedTransaction,
            options: {
              preflightCommitment: getCommitment(sendOptions.preflightCommitment || connection.commitment),
              minContextSlot: sendOptions.minContextSlot
            }
          });
          return await connection.sendRawTransaction(output.signedTransaction, {
            ...sendOptions,
            preflightCommitment: getCommitment(sendOptions.preflightCommitment || connection.commitment)
          });
        }
      } catch (error) {
        if (error instanceof WalletError)
          throw error;
        throw new WalletSendTransactionError(error == null ? void 0 : error.message, error);
      }
    } catch (error) {
      this.emit("error", error);
      throw error;
    }
  }
};
_StandardWalletAdapter_account = /* @__PURE__ */ new WeakMap(), _StandardWalletAdapter_publicKey = /* @__PURE__ */ new WeakMap(), _StandardWalletAdapter_connecting = /* @__PURE__ */ new WeakMap(), _StandardWalletAdapter_disconnecting = /* @__PURE__ */ new WeakMap(), _StandardWalletAdapter_off = /* @__PURE__ */ new WeakMap(), _StandardWalletAdapter_supportedTransactionVersions = /* @__PURE__ */ new WeakMap(), _StandardWalletAdapter_wallet = /* @__PURE__ */ new WeakMap(), _StandardWalletAdapter_readyState = /* @__PURE__ */ new WeakMap(), _StandardWalletAdapter_changed = /* @__PURE__ */ new WeakMap(), _StandardWalletAdapter_instances = /* @__PURE__ */ new WeakSet(), _StandardWalletAdapter_connect = async function _StandardWalletAdapter_connect2(input) {
  try {
    if (this.connected || this.connecting)
      return;
    if (__classPrivateFieldGet(this, _StandardWalletAdapter_readyState, "f") !== WalletReadyState.Installed)
      throw new WalletNotReadyError();
    __classPrivateFieldSet(this, _StandardWalletAdapter_connecting, true, "f");
    if (!__classPrivateFieldGet(this, _StandardWalletAdapter_wallet, "f").accounts.length) {
      try {
        await __classPrivateFieldGet(this, _StandardWalletAdapter_wallet, "f").features[StandardConnect].connect(input);
      } catch (error) {
        throw new WalletConnectionError(error == null ? void 0 : error.message, error);
      }
    }
    const account = __classPrivateFieldGet(this, _StandardWalletAdapter_wallet, "f").accounts[0];
    if (!account)
      throw new WalletAccountError();
    __classPrivateFieldGet(this, _StandardWalletAdapter_instances, "m", _StandardWalletAdapter_connected).call(this, account);
  } catch (error) {
    this.emit("error", error);
    throw error;
  } finally {
    __classPrivateFieldSet(this, _StandardWalletAdapter_connecting, false, "f");
  }
}, _StandardWalletAdapter_connected = function _StandardWalletAdapter_connected2(account) {
  let publicKey;
  try {
    publicKey = new PublicKey(account.address);
  } catch (error) {
    throw new WalletPublicKeyError(error == null ? void 0 : error.message, error);
  }
  __classPrivateFieldSet(this, _StandardWalletAdapter_account, account, "f");
  __classPrivateFieldSet(this, _StandardWalletAdapter_publicKey, publicKey, "f");
  __classPrivateFieldGet(this, _StandardWalletAdapter_instances, "m", _StandardWalletAdapter_reset).call(this);
  this.emit("connect", publicKey);
}, _StandardWalletAdapter_disconnected = function _StandardWalletAdapter_disconnected2() {
  __classPrivateFieldSet(this, _StandardWalletAdapter_account, null, "f");
  __classPrivateFieldSet(this, _StandardWalletAdapter_publicKey, null, "f");
  __classPrivateFieldGet(this, _StandardWalletAdapter_instances, "m", _StandardWalletAdapter_reset).call(this);
  this.emit("disconnect");
}, _StandardWalletAdapter_reset = function _StandardWalletAdapter_reset2() {
  var _a, _b;
  const supportedTransactionVersions = SolanaSignAndSendTransaction in __classPrivateFieldGet(this, _StandardWalletAdapter_wallet, "f").features ? __classPrivateFieldGet(this, _StandardWalletAdapter_wallet, "f").features[SolanaSignAndSendTransaction].supportedTransactionVersions : __classPrivateFieldGet(this, _StandardWalletAdapter_wallet, "f").features[SolanaSignTransaction].supportedTransactionVersions;
  __classPrivateFieldSet(this, _StandardWalletAdapter_supportedTransactionVersions, arraysEqual(supportedTransactionVersions, ["legacy"]) ? null : new Set(supportedTransactionVersions), "f");
  if (SolanaSignTransaction in __classPrivateFieldGet(this, _StandardWalletAdapter_wallet, "f").features && ((_a = __classPrivateFieldGet(this, _StandardWalletAdapter_account, "f")) == null ? void 0 : _a.features.includes(SolanaSignTransaction))) {
    this.signTransaction = __classPrivateFieldGet(this, _StandardWalletAdapter_instances, "m", _StandardWalletAdapter_signTransaction);
    this.signAllTransactions = __classPrivateFieldGet(this, _StandardWalletAdapter_instances, "m", _StandardWalletAdapter_signAllTransactions);
  } else {
    delete this.signTransaction;
    delete this.signAllTransactions;
  }
  if (SolanaSignMessage in __classPrivateFieldGet(this, _StandardWalletAdapter_wallet, "f").features && ((_b = __classPrivateFieldGet(this, _StandardWalletAdapter_account, "f")) == null ? void 0 : _b.features.includes(SolanaSignMessage))) {
    this.signMessage = __classPrivateFieldGet(this, _StandardWalletAdapter_instances, "m", _StandardWalletAdapter_signMessage);
  } else {
    delete this.signMessage;
  }
  if (SolanaSignIn in __classPrivateFieldGet(this, _StandardWalletAdapter_wallet, "f").features) {
    this.signIn = __classPrivateFieldGet(this, _StandardWalletAdapter_instances, "m", _StandardWalletAdapter_signIn);
  } else {
    delete this.signIn;
  }
}, _StandardWalletAdapter_signTransaction = async function _StandardWalletAdapter_signTransaction2(transaction) {
  try {
    const account = __classPrivateFieldGet(this, _StandardWalletAdapter_account, "f");
    if (!account)
      throw new WalletNotConnectedError();
    if (!(SolanaSignTransaction in __classPrivateFieldGet(this, _StandardWalletAdapter_wallet, "f").features))
      throw new WalletConfigError();
    if (!account.features.includes(SolanaSignTransaction))
      throw new WalletAccountError();
    try {
      const signedTransactions = await __classPrivateFieldGet(this, _StandardWalletAdapter_wallet, "f").features[SolanaSignTransaction].signTransaction({
        account,
        transaction: isVersionedTransaction(transaction) ? transaction.serialize() : new Uint8Array(transaction.serialize({
          requireAllSignatures: false,
          verifySignatures: false
        }))
      });
      const serializedTransaction = signedTransactions[0].signedTransaction;
      return isVersionedTransaction(transaction) ? VersionedTransaction.deserialize(serializedTransaction) : Transaction.from(serializedTransaction);
    } catch (error) {
      if (error instanceof WalletError)
        throw error;
      throw new WalletSignTransactionError(error == null ? void 0 : error.message, error);
    }
  } catch (error) {
    this.emit("error", error);
    throw error;
  }
}, _StandardWalletAdapter_signAllTransactions = async function _StandardWalletAdapter_signAllTransactions2(transactions) {
  try {
    const account = __classPrivateFieldGet(this, _StandardWalletAdapter_account, "f");
    if (!account)
      throw new WalletNotConnectedError();
    if (!(SolanaSignTransaction in __classPrivateFieldGet(this, _StandardWalletAdapter_wallet, "f").features))
      throw new WalletConfigError();
    if (!account.features.includes(SolanaSignTransaction))
      throw new WalletAccountError();
    try {
      const signedTransactions = await __classPrivateFieldGet(this, _StandardWalletAdapter_wallet, "f").features[SolanaSignTransaction].signTransaction(...transactions.map((transaction) => ({
        account,
        transaction: isVersionedTransaction(transaction) ? transaction.serialize() : new Uint8Array(transaction.serialize({
          requireAllSignatures: false,
          verifySignatures: false
        }))
      })));
      return transactions.map((transaction, index) => {
        const signedTransaction = signedTransactions[index].signedTransaction;
        return isVersionedTransaction(transaction) ? VersionedTransaction.deserialize(signedTransaction) : Transaction.from(signedTransaction);
      });
    } catch (error) {
      throw new WalletSignTransactionError(error == null ? void 0 : error.message, error);
    }
  } catch (error) {
    this.emit("error", error);
    throw error;
  }
}, _StandardWalletAdapter_signMessage = async function _StandardWalletAdapter_signMessage2(message) {
  try {
    const account = __classPrivateFieldGet(this, _StandardWalletAdapter_account, "f");
    if (!account)
      throw new WalletNotConnectedError();
    if (!(SolanaSignMessage in __classPrivateFieldGet(this, _StandardWalletAdapter_wallet, "f").features))
      throw new WalletConfigError();
    if (!account.features.includes(SolanaSignMessage))
      throw new WalletAccountError();
    try {
      const signedMessages = await __classPrivateFieldGet(this, _StandardWalletAdapter_wallet, "f").features[SolanaSignMessage].signMessage({
        account,
        message
      });
      return signedMessages[0].signature;
    } catch (error) {
      throw new WalletSignMessageError(error == null ? void 0 : error.message, error);
    }
  } catch (error) {
    this.emit("error", error);
    throw error;
  }
}, _StandardWalletAdapter_signIn = async function _StandardWalletAdapter_signIn2(input = {}) {
  try {
    if (!(SolanaSignIn in __classPrivateFieldGet(this, _StandardWalletAdapter_wallet, "f").features))
      throw new WalletConfigError();
    let output;
    try {
      [output] = await __classPrivateFieldGet(this, _StandardWalletAdapter_wallet, "f").features[SolanaSignIn].signIn(input);
    } catch (error) {
      throw new WalletSignInError(error == null ? void 0 : error.message, error);
    }
    if (!output)
      throw new WalletSignInError();
    __classPrivateFieldGet(this, _StandardWalletAdapter_instances, "m", _StandardWalletAdapter_connected).call(this, output.account);
    return output;
  } catch (error) {
    this.emit("error", error);
    throw error;
  }
};

// node_modules/@solana/wallet-standard-wallet-adapter-base/lib/esm/types.js
var isWalletAdapterCompatibleWallet = isWalletAdapterCompatibleStandardWallet;

// node_modules/@solana/wallet-standard-wallet-adapter-base/lib/esm/wallet.js
init_index_browser_esm();

// node_modules/@wallet-standard/app/lib/esm/wallets.js
var __classPrivateFieldGet2 = function(receiver, state, kind, f) {
  if (kind === "a" && !f)
    throw new TypeError("Private accessor was defined without a getter");
  if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver))
    throw new TypeError("Cannot read private member from an object whose class did not declare it");
  return kind === "m" ? f : kind === "a" ? f.call(receiver) : f ? f.value : state.get(receiver);
};
var __classPrivateFieldSet2 = function(receiver, state, value, kind, f) {
  if (kind === "m")
    throw new TypeError("Private method is not writable");
  if (kind === "a" && !f)
    throw new TypeError("Private accessor was defined without a setter");
  if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver))
    throw new TypeError("Cannot write private member to an object whose class did not declare it");
  return kind === "a" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value), value;
};
var _AppReadyEvent_detail;
var wallets = void 0;
var registeredWalletsSet = /* @__PURE__ */ new Set();
function addRegisteredWallet(wallet) {
  cachedWalletsArray = void 0;
  registeredWalletsSet.add(wallet);
}
function removeRegisteredWallet(wallet) {
  cachedWalletsArray = void 0;
  registeredWalletsSet.delete(wallet);
}
var listeners = {};
function getWallets() {
  if (wallets)
    return wallets;
  wallets = Object.freeze({ register, get, on });
  if (typeof window === "undefined")
    return wallets;
  const api = Object.freeze({ register });
  try {
    window.addEventListener("wallet-standard:register-wallet", ({ detail: callback }) => callback(api));
  } catch (error) {
    console.error("wallet-standard:register-wallet event listener could not be added\n", error);
  }
  try {
    window.dispatchEvent(new AppReadyEvent(api));
  } catch (error) {
    console.error("wallet-standard:app-ready event could not be dispatched\n", error);
  }
  return wallets;
}
function register(...wallets2) {
  var _a;
  wallets2 = wallets2.filter((wallet) => !registeredWalletsSet.has(wallet));
  if (!wallets2.length)
    return () => {
    };
  wallets2.forEach((wallet) => addRegisteredWallet(wallet));
  (_a = listeners["register"]) == null ? void 0 : _a.forEach((listener) => guard(() => listener(...wallets2)));
  return function unregister() {
    var _a2;
    wallets2.forEach((wallet) => removeRegisteredWallet(wallet));
    (_a2 = listeners["unregister"]) == null ? void 0 : _a2.forEach((listener) => guard(() => listener(...wallets2)));
  };
}
var cachedWalletsArray;
function get() {
  if (!cachedWalletsArray) {
    cachedWalletsArray = [...registeredWalletsSet];
  }
  return cachedWalletsArray;
}
function on(event, listener) {
  var _a;
  ((_a = listeners[event]) == null ? void 0 : _a.push(listener)) || (listeners[event] = [listener]);
  return function off() {
    var _a2;
    listeners[event] = (_a2 = listeners[event]) == null ? void 0 : _a2.filter((existingListener) => listener !== existingListener);
  };
}
function guard(callback) {
  try {
    callback();
  } catch (error) {
    console.error(error);
  }
}
var AppReadyEvent = class extends Event {
  get detail() {
    return __classPrivateFieldGet2(this, _AppReadyEvent_detail, "f");
  }
  get type() {
    return "wallet-standard:app-ready";
  }
  constructor(api) {
    super("wallet-standard:app-ready", {
      bubbles: false,
      cancelable: false,
      composed: false
    });
    _AppReadyEvent_detail.set(this, void 0);
    __classPrivateFieldSet2(this, _AppReadyEvent_detail, api, "f");
  }
  /** @deprecated */
  preventDefault() {
    throw new Error("preventDefault cannot be called");
  }
  /** @deprecated */
  stopImmediatePropagation() {
    throw new Error("stopImmediatePropagation cannot be called");
  }
  /** @deprecated */
  stopPropagation() {
    throw new Error("stopPropagation cannot be called");
  }
};
_AppReadyEvent_detail = /* @__PURE__ */ new WeakMap();
function DEPRECATED_getWallets() {
  if (wallets)
    return wallets;
  wallets = getWallets();
  if (typeof window === "undefined")
    return wallets;
  const callbacks = window.navigator.wallets || [];
  if (!Array.isArray(callbacks)) {
    console.error("window.navigator.wallets is not an array");
    return wallets;
  }
  const { register: register2 } = wallets;
  const push = (...callbacks2) => callbacks2.forEach((callback) => guard(() => callback({ register: register2 })));
  try {
    Object.defineProperty(window.navigator, "wallets", {
      value: Object.freeze({ push })
    });
  } catch (error) {
    console.error("window.navigator.wallets could not be set");
    return wallets;
  }
  push(...callbacks);
  return wallets;
}

// node_modules/@solana/wallet-standard-wallet-adapter-base/lib/esm/wallet.js
var import_bs583 = __toESM(require_bs58(), 1);
var __classPrivateFieldSet3 = function(receiver, state, value, kind, f) {
  if (kind === "m")
    throw new TypeError("Private method is not writable");
  if (kind === "a" && !f)
    throw new TypeError("Private accessor was defined without a setter");
  if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver))
    throw new TypeError("Cannot write private member to an object whose class did not declare it");
  return kind === "a" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value), value;
};
var __classPrivateFieldGet3 = function(receiver, state, kind, f) {
  if (kind === "a" && !f)
    throw new TypeError("Private accessor was defined without a getter");
  if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver))
    throw new TypeError("Cannot read private member from an object whose class did not declare it");
  return kind === "m" ? f : kind === "a" ? f.call(receiver) : f ? f.value : state.get(receiver);
};
var _SolanaWalletAdapterWalletAccount_adapter;
var _SolanaWalletAdapterWallet_instances;
var _SolanaWalletAdapterWallet_listeners;
var _SolanaWalletAdapterWallet_adapter;
var _SolanaWalletAdapterWallet_supportedTransactionVersions;
var _SolanaWalletAdapterWallet_chain;
var _SolanaWalletAdapterWallet_endpoint;
var _SolanaWalletAdapterWallet_account;
var _SolanaWalletAdapterWallet_connected;
var _SolanaWalletAdapterWallet_disconnected;
var _SolanaWalletAdapterWallet_connect;
var _SolanaWalletAdapterWallet_disconnect;
var _SolanaWalletAdapterWallet_on;
var _SolanaWalletAdapterWallet_emit;
var _SolanaWalletAdapterWallet_off;
var _SolanaWalletAdapterWallet_deserializeTransaction;
var _SolanaWalletAdapterWallet_signAndSendTransaction;
var _SolanaWalletAdapterWallet_signTransaction;
var _SolanaWalletAdapterWallet_signMessage;
var _SolanaWalletAdapterWallet_signIn;
var SolanaWalletAdapterWalletAccount = class _SolanaWalletAdapterWalletAccount extends ReadonlyWalletAccount {
  constructor({ adapter, address, publicKey, chains }) {
    const features = [SolanaSignAndSendTransaction];
    if ("signTransaction" in adapter) {
      features.push(SolanaSignTransaction);
    }
    if ("signMessage" in adapter) {
      features.push(SolanaSignMessage);
    }
    if ("signIn" in adapter) {
      features.push(SolanaSignIn);
    }
    super({ address, publicKey, chains, features });
    _SolanaWalletAdapterWalletAccount_adapter.set(this, void 0);
    if (new.target === _SolanaWalletAdapterWalletAccount) {
      Object.freeze(this);
    }
    __classPrivateFieldSet3(this, _SolanaWalletAdapterWalletAccount_adapter, adapter, "f");
  }
};
_SolanaWalletAdapterWalletAccount_adapter = /* @__PURE__ */ new WeakMap();
_SolanaWalletAdapterWallet_listeners = /* @__PURE__ */ new WeakMap(), _SolanaWalletAdapterWallet_adapter = /* @__PURE__ */ new WeakMap(), _SolanaWalletAdapterWallet_supportedTransactionVersions = /* @__PURE__ */ new WeakMap(), _SolanaWalletAdapterWallet_chain = /* @__PURE__ */ new WeakMap(), _SolanaWalletAdapterWallet_endpoint = /* @__PURE__ */ new WeakMap(), _SolanaWalletAdapterWallet_account = /* @__PURE__ */ new WeakMap(), _SolanaWalletAdapterWallet_connect = /* @__PURE__ */ new WeakMap(), _SolanaWalletAdapterWallet_disconnect = /* @__PURE__ */ new WeakMap(), _SolanaWalletAdapterWallet_on = /* @__PURE__ */ new WeakMap(), _SolanaWalletAdapterWallet_signAndSendTransaction = /* @__PURE__ */ new WeakMap(), _SolanaWalletAdapterWallet_signTransaction = /* @__PURE__ */ new WeakMap(), _SolanaWalletAdapterWallet_signMessage = /* @__PURE__ */ new WeakMap(), _SolanaWalletAdapterWallet_signIn = /* @__PURE__ */ new WeakMap(), _SolanaWalletAdapterWallet_instances = /* @__PURE__ */ new WeakSet(), _SolanaWalletAdapterWallet_connected = function _SolanaWalletAdapterWallet_connected2() {
  var _a;
  const publicKey = (_a = __classPrivateFieldGet3(this, _SolanaWalletAdapterWallet_adapter, "f").publicKey) == null ? void 0 : _a.toBytes();
  if (publicKey) {
    const address = __classPrivateFieldGet3(this, _SolanaWalletAdapterWallet_adapter, "f").publicKey.toBase58();
    const account = __classPrivateFieldGet3(this, _SolanaWalletAdapterWallet_account, "f");
    if (!account || account.address !== address || account.chains.includes(__classPrivateFieldGet3(this, _SolanaWalletAdapterWallet_chain, "f")) || !bytesEqual(account.publicKey, publicKey)) {
      __classPrivateFieldSet3(this, _SolanaWalletAdapterWallet_account, new SolanaWalletAdapterWalletAccount({
        adapter: __classPrivateFieldGet3(this, _SolanaWalletAdapterWallet_adapter, "f"),
        address,
        publicKey,
        chains: [__classPrivateFieldGet3(this, _SolanaWalletAdapterWallet_chain, "f")]
      }), "f");
      __classPrivateFieldGet3(this, _SolanaWalletAdapterWallet_instances, "m", _SolanaWalletAdapterWallet_emit).call(this, "change", { accounts: this.accounts });
    }
  }
}, _SolanaWalletAdapterWallet_disconnected = function _SolanaWalletAdapterWallet_disconnected2() {
  if (__classPrivateFieldGet3(this, _SolanaWalletAdapterWallet_account, "f")) {
    __classPrivateFieldSet3(this, _SolanaWalletAdapterWallet_account, void 0, "f");
    __classPrivateFieldGet3(this, _SolanaWalletAdapterWallet_instances, "m", _SolanaWalletAdapterWallet_emit).call(this, "change", { accounts: this.accounts });
  }
}, _SolanaWalletAdapterWallet_emit = function _SolanaWalletAdapterWallet_emit2(event, ...args) {
  var _a;
  (_a = __classPrivateFieldGet3(this, _SolanaWalletAdapterWallet_listeners, "f")[event]) == null ? void 0 : _a.forEach((listener) => listener.apply(null, args));
}, _SolanaWalletAdapterWallet_off = function _SolanaWalletAdapterWallet_off2(event, listener) {
  var _a;
  __classPrivateFieldGet3(this, _SolanaWalletAdapterWallet_listeners, "f")[event] = (_a = __classPrivateFieldGet3(this, _SolanaWalletAdapterWallet_listeners, "f")[event]) == null ? void 0 : _a.filter((existingListener) => listener !== existingListener);
}, _SolanaWalletAdapterWallet_deserializeTransaction = function _SolanaWalletAdapterWallet_deserializeTransaction2(serializedTransaction) {
  const transaction = VersionedTransaction.deserialize(serializedTransaction);
  if (!__classPrivateFieldGet3(this, _SolanaWalletAdapterWallet_supportedTransactionVersions, "f").includes(transaction.version))
    throw new Error("unsupported transaction version");
  if (transaction.version === "legacy" && arraysEqual(__classPrivateFieldGet3(this, _SolanaWalletAdapterWallet_supportedTransactionVersions, "f"), ["legacy"]))
    return Transaction.from(serializedTransaction);
  return transaction;
};

// node_modules/@solana/wallet-standard-wallet-adapter-react/lib/esm/useStandardWalletAdapters.js
var import_react6 = __toESM(require_react(), 1);
function useStandardWalletAdapters(adapters) {
  const warnings = useConstant(() => /* @__PURE__ */ new Set());
  const { get: get2, on: on2 } = useConstant(() => DEPRECATED_getWallets());
  const [standardAdapters, setStandardAdapters] = (0, import_react6.useState)(() => wrapWalletsWithAdapters(get2()));
  (0, import_react6.useEffect)(() => {
    const listeners2 = [
      on2("register", (...wallets2) => setStandardAdapters((standardAdapters2) => [...standardAdapters2, ...wrapWalletsWithAdapters(wallets2)])),
      on2("unregister", (...wallets2) => setStandardAdapters((standardAdapters2) => standardAdapters2.filter((standardAdapter) => wallets2.some((wallet) => wallet === standardAdapter.wallet))))
    ];
    return () => listeners2.forEach((off) => off());
  }, [on2]);
  const prevStandardAdapters = usePrevious(standardAdapters);
  (0, import_react6.useEffect)(() => {
    if (!prevStandardAdapters)
      return;
    const currentAdapters = new Set(standardAdapters);
    const removedAdapters = new Set(prevStandardAdapters.filter((previousAdapter) => !currentAdapters.has(previousAdapter)));
    removedAdapters.forEach((adapter) => adapter.destroy());
  }, [prevStandardAdapters, standardAdapters]);
  (0, import_react6.useEffect)(() => () => standardAdapters.forEach((adapter) => adapter.destroy()), []);
  return (0, import_react6.useMemo)(() => [
    ...standardAdapters,
    ...adapters.filter(({ name }) => {
      if (standardAdapters.some((standardAdapter) => standardAdapter.name === name)) {
        if (!warnings.has(name)) {
          warnings.add(name);
          console.warn(`${name} was registered as a Standard Wallet. The Wallet Adapter for ${name} can be removed from your app.`);
        }
        return false;
      }
      return true;
    })
  ], [standardAdapters, adapters, warnings]);
}
function useConstant(fn) {
  const ref = (0, import_react6.useRef)(void 0);
  if (ref.current === void 0) {
    ref.current = { value: fn() };
  }
  return ref.current.value;
}
function usePrevious(state) {
  const ref = (0, import_react6.useRef)(void 0);
  (0, import_react6.useEffect)(() => {
    ref.current = state;
  });
  return ref.current;
}
function wrapWalletsWithAdapters(wallets2) {
  return wallets2.filter(isWalletAdapterCompatibleWallet).map((wallet) => new StandardWalletAdapter({ wallet }));
}

// node_modules/@solana/wallet-adapter-react/lib/esm/WalletProvider.js
var import_react8 = __toESM(require_react(), 1);

// node_modules/@solana/wallet-adapter-react/lib/esm/getEnvironment.js
var import_wallet_adapter_mobile = __toESM(require_index_browser3(), 1);
var Environment;
(function(Environment2) {
  Environment2[Environment2["DESKTOP_WEB"] = 0] = "DESKTOP_WEB";
  Environment2[Environment2["MOBILE_WEB"] = 1] = "MOBILE_WEB";
})(Environment || (Environment = {}));
function isWebView(userAgentString) {
  return /(WebView|Version\/.+(Chrome)\/(\d+)\.(\d+)\.(\d+)\.(\d+)|; wv\).+(Chrome)\/(\d+)\.(\d+)\.(\d+)\.(\d+))/i.test(userAgentString);
}
function getEnvironment({ adapters, userAgentString }) {
  if (adapters.some((adapter) => adapter.name !== import_wallet_adapter_mobile.SolanaMobileWalletAdapterWalletName && adapter.readyState === WalletReadyState.Installed)) {
    return Environment.DESKTOP_WEB;
  }
  if (userAgentString && // Step 1: Check whether we're on a platform that supports MWA at all.
  /android/i.test(userAgentString) && // Step 2: Determine that we are *not* running in a WebView.
  !isWebView(userAgentString)) {
    return Environment.MOBILE_WEB;
  } else {
    return Environment.DESKTOP_WEB;
  }
}

// node_modules/@solana/wallet-adapter-react/lib/esm/getInferredClusterFromEndpoint.js
function getInferredClusterFromEndpoint(endpoint) {
  if (!endpoint) {
    return "mainnet-beta";
  }
  if (/devnet/i.test(endpoint)) {
    return "devnet";
  } else if (/testnet/i.test(endpoint)) {
    return "testnet";
  } else {
    return "mainnet-beta";
  }
}

// node_modules/@solana/wallet-adapter-react/lib/esm/WalletProviderBase.js
var import_react7 = __toESM(require_react(), 1);
function WalletProviderBase({ children, wallets: adapters, adapter, isUnloadingRef, onAutoConnectRequest, onConnectError, onError, onSelectWallet }) {
  const isConnectingRef = (0, import_react7.useRef)(false);
  const [connecting, setConnecting] = (0, import_react7.useState)(false);
  const isDisconnectingRef = (0, import_react7.useRef)(false);
  const [disconnecting, setDisconnecting] = (0, import_react7.useState)(false);
  const [publicKey, setPublicKey] = (0, import_react7.useState)(() => (adapter == null ? void 0 : adapter.publicKey) ?? null);
  const [connected, setConnected] = (0, import_react7.useState)(() => (adapter == null ? void 0 : adapter.connected) ?? false);
  const onErrorRef = (0, import_react7.useRef)(onError);
  (0, import_react7.useEffect)(() => {
    onErrorRef.current = onError;
    return () => {
      onErrorRef.current = void 0;
    };
  }, [onError]);
  const handleErrorRef = (0, import_react7.useRef)((error, adapter2) => {
    if (!isUnloadingRef.current) {
      if (onErrorRef.current) {
        onErrorRef.current(error, adapter2);
      } else {
        console.error(error, adapter2);
        if (error instanceof WalletNotReadyError && typeof window !== "undefined" && adapter2) {
          window.open(adapter2.url, "_blank");
        }
      }
    }
    return error;
  });
  const [wallets2, setWallets] = (0, import_react7.useState)(() => adapters.map((adapter2) => ({
    adapter: adapter2,
    readyState: adapter2.readyState
  })).filter(({ readyState }) => readyState !== WalletReadyState.Unsupported));
  (0, import_react7.useEffect)(() => {
    setWallets((wallets3) => adapters.map((adapter2, index) => {
      const wallet2 = wallets3[index];
      return wallet2 && wallet2.adapter === adapter2 && wallet2.readyState === adapter2.readyState ? wallet2 : {
        adapter: adapter2,
        readyState: adapter2.readyState
      };
    }).filter(({ readyState }) => readyState !== WalletReadyState.Unsupported));
    function handleReadyStateChange(readyState) {
      setWallets((prevWallets) => {
        const index = prevWallets.findIndex(({ adapter: adapter3 }) => adapter3 === this);
        if (index === -1)
          return prevWallets;
        const { adapter: adapter2 } = prevWallets[index];
        return [
          ...prevWallets.slice(0, index),
          { adapter: adapter2, readyState },
          ...prevWallets.slice(index + 1)
        ].filter(({ readyState: readyState2 }) => readyState2 !== WalletReadyState.Unsupported);
      });
    }
    adapters.forEach((adapter2) => adapter2.on("readyStateChange", handleReadyStateChange, adapter2));
    return () => {
      adapters.forEach((adapter2) => adapter2.off("readyStateChange", handleReadyStateChange, adapter2));
    };
  }, [adapter, adapters]);
  const wallet = (0, import_react7.useMemo)(() => wallets2.find((wallet2) => wallet2.adapter === adapter) ?? null, [adapter, wallets2]);
  (0, import_react7.useEffect)(() => {
    if (!adapter)
      return;
    const handleConnect2 = (publicKey2) => {
      setPublicKey(publicKey2);
      isConnectingRef.current = false;
      setConnecting(false);
      setConnected(true);
      isDisconnectingRef.current = false;
      setDisconnecting(false);
    };
    const handleDisconnect2 = () => {
      if (isUnloadingRef.current)
        return;
      setPublicKey(null);
      isConnectingRef.current = false;
      setConnecting(false);
      setConnected(false);
      isDisconnectingRef.current = false;
      setDisconnecting(false);
    };
    const handleError = (error) => {
      handleErrorRef.current(error, adapter);
    };
    adapter.on("connect", handleConnect2);
    adapter.on("disconnect", handleDisconnect2);
    adapter.on("error", handleError);
    return () => {
      adapter.off("connect", handleConnect2);
      adapter.off("disconnect", handleDisconnect2);
      adapter.off("error", handleError);
      handleDisconnect2();
    };
  }, [adapter, isUnloadingRef]);
  const didAttemptAutoConnectRef = (0, import_react7.useRef)(false);
  (0, import_react7.useEffect)(() => {
    return () => {
      didAttemptAutoConnectRef.current = false;
    };
  }, [adapter]);
  (0, import_react7.useEffect)(() => {
    if (didAttemptAutoConnectRef.current || isConnectingRef.current || connected || !onAutoConnectRequest || !((wallet == null ? void 0 : wallet.readyState) === WalletReadyState.Installed || (wallet == null ? void 0 : wallet.readyState) === WalletReadyState.Loadable))
      return;
    isConnectingRef.current = true;
    setConnecting(true);
    didAttemptAutoConnectRef.current = true;
    (async function() {
      try {
        await onAutoConnectRequest();
      } catch {
        onConnectError();
      } finally {
        setConnecting(false);
        isConnectingRef.current = false;
      }
    })();
  }, [connected, onAutoConnectRequest, onConnectError, wallet]);
  const sendTransaction = (0, import_react7.useCallback)(async (transaction, connection, options) => {
    if (!adapter)
      throw handleErrorRef.current(new WalletNotSelectedError());
    if (!connected)
      throw handleErrorRef.current(new WalletNotConnectedError(), adapter);
    return await adapter.sendTransaction(transaction, connection, options);
  }, [adapter, connected]);
  const signTransaction = (0, import_react7.useMemo)(() => adapter && "signTransaction" in adapter ? async (transaction) => {
    if (!connected)
      throw handleErrorRef.current(new WalletNotConnectedError(), adapter);
    return await adapter.signTransaction(transaction);
  } : void 0, [adapter, connected]);
  const signAllTransactions = (0, import_react7.useMemo)(() => adapter && "signAllTransactions" in adapter ? async (transactions) => {
    if (!connected)
      throw handleErrorRef.current(new WalletNotConnectedError(), adapter);
    return await adapter.signAllTransactions(transactions);
  } : void 0, [adapter, connected]);
  const signMessage = (0, import_react7.useMemo)(() => adapter && "signMessage" in adapter ? async (message) => {
    if (!connected)
      throw handleErrorRef.current(new WalletNotConnectedError(), adapter);
    return await adapter.signMessage(message);
  } : void 0, [adapter, connected]);
  const signIn = (0, import_react7.useMemo)(() => adapter && "signIn" in adapter ? async (input) => {
    return await adapter.signIn(input);
  } : void 0, [adapter]);
  const handleConnect = (0, import_react7.useCallback)(async () => {
    if (isConnectingRef.current || isDisconnectingRef.current || (wallet == null ? void 0 : wallet.adapter.connected))
      return;
    if (!wallet)
      throw handleErrorRef.current(new WalletNotSelectedError());
    const { adapter: adapter2, readyState } = wallet;
    if (!(readyState === WalletReadyState.Installed || readyState === WalletReadyState.Loadable))
      throw handleErrorRef.current(new WalletNotReadyError(), adapter2);
    isConnectingRef.current = true;
    setConnecting(true);
    try {
      await adapter2.connect();
    } catch (e) {
      onConnectError();
      throw e;
    } finally {
      setConnecting(false);
      isConnectingRef.current = false;
    }
  }, [onConnectError, wallet]);
  const handleDisconnect = (0, import_react7.useCallback)(async () => {
    if (isDisconnectingRef.current)
      return;
    if (!adapter)
      return;
    isDisconnectingRef.current = true;
    setDisconnecting(true);
    try {
      await adapter.disconnect();
    } finally {
      setDisconnecting(false);
      isDisconnectingRef.current = false;
    }
  }, [adapter]);
  return import_react7.default.createElement(WalletContext.Provider, { value: {
    autoConnect: !!onAutoConnectRequest,
    wallets: wallets2,
    wallet,
    publicKey,
    connected,
    connecting,
    disconnecting,
    select: onSelectWallet,
    connect: handleConnect,
    disconnect: handleDisconnect,
    sendTransaction,
    signTransaction,
    signAllTransactions,
    signMessage,
    signIn
  } }, children);
}

// node_modules/@solana/wallet-adapter-react/lib/esm/WalletProvider.js
var _userAgent;
function getUserAgent() {
  var _a;
  if (_userAgent === void 0) {
    _userAgent = ((_a = globalThis.navigator) == null ? void 0 : _a.userAgent) ?? null;
  }
  return _userAgent;
}
function getIsMobile(adapters) {
  const userAgentString = getUserAgent();
  return getEnvironment({ adapters, userAgentString }) === Environment.MOBILE_WEB;
}
function getUriForAppIdentity() {
  const location = globalThis.location;
  if (!location)
    return;
  return `${location.protocol}//${location.host}`;
}
function WalletProvider({ children, wallets: adapters, autoConnect, localStorageKey = "walletName", onError }) {
  const { connection } = useConnection();
  const adaptersWithStandardAdapters = useStandardWalletAdapters(adapters);
  const mobileWalletAdapter = (0, import_react8.useMemo)(() => {
    if (!getIsMobile(adaptersWithStandardAdapters)) {
      return null;
    }
    const existingMobileWalletAdapter = adaptersWithStandardAdapters.find((adapter2) => adapter2.name === import_wallet_adapter_mobile2.SolanaMobileWalletAdapterWalletName);
    if (existingMobileWalletAdapter) {
      return existingMobileWalletAdapter;
    }
    return new import_wallet_adapter_mobile2.SolanaMobileWalletAdapter({
      addressSelector: (0, import_wallet_adapter_mobile2.createDefaultAddressSelector)(),
      appIdentity: {
        uri: getUriForAppIdentity()
      },
      authorizationResultCache: (0, import_wallet_adapter_mobile2.createDefaultAuthorizationResultCache)(),
      cluster: getInferredClusterFromEndpoint(connection == null ? void 0 : connection.rpcEndpoint),
      onWalletNotFound: (0, import_wallet_adapter_mobile2.createDefaultWalletNotFoundHandler)()
    });
  }, [adaptersWithStandardAdapters, connection == null ? void 0 : connection.rpcEndpoint]);
  const adaptersWithMobileWalletAdapter = (0, import_react8.useMemo)(() => {
    if (mobileWalletAdapter == null || adaptersWithStandardAdapters.indexOf(mobileWalletAdapter) !== -1) {
      return adaptersWithStandardAdapters;
    }
    return [mobileWalletAdapter, ...adaptersWithStandardAdapters];
  }, [adaptersWithStandardAdapters, mobileWalletAdapter]);
  const [walletName, setWalletName] = useLocalStorage(localStorageKey, getIsMobile(adaptersWithStandardAdapters) ? import_wallet_adapter_mobile2.SolanaMobileWalletAdapterWalletName : null);
  const adapter = (0, import_react8.useMemo)(() => adaptersWithMobileWalletAdapter.find((a) => a.name === walletName) ?? null, [adaptersWithMobileWalletAdapter, walletName]);
  const changeWallet = (0, import_react8.useCallback)((nextWalletName) => {
    if (walletName === nextWalletName)
      return;
    if (adapter && // Selecting a wallet other than the mobile wallet adapter is not
    // sufficient reason to call `disconnect` on the mobile wallet adapter.
    // Calling `disconnect` on the mobile wallet adapter causes the entire
    // authorization store to be wiped.
    adapter.name !== import_wallet_adapter_mobile2.SolanaMobileWalletAdapterWalletName) {
      adapter.disconnect();
    }
    setWalletName(nextWalletName);
  }, [adapter, setWalletName, walletName]);
  (0, import_react8.useEffect)(() => {
    if (!adapter)
      return;
    function handleDisconnect() {
      if (isUnloadingRef.current)
        return;
      if (walletName === import_wallet_adapter_mobile2.SolanaMobileWalletAdapterWalletName && getIsMobile(adaptersWithStandardAdapters))
        return;
      setWalletName(null);
    }
    adapter.on("disconnect", handleDisconnect);
    return () => {
      adapter.off("disconnect", handleDisconnect);
    };
  }, [adapter, adaptersWithStandardAdapters, setWalletName, walletName]);
  const hasUserSelectedAWallet = (0, import_react8.useRef)(false);
  const handleAutoConnectRequest = (0, import_react8.useMemo)(() => {
    if (!autoConnect || !adapter)
      return;
    return async () => {
      if (autoConnect === true || await autoConnect(adapter)) {
        if (hasUserSelectedAWallet.current) {
          await adapter.connect();
        } else {
          await adapter.autoConnect();
        }
      }
    };
  }, [autoConnect, adapter]);
  const isUnloadingRef = (0, import_react8.useRef)(false);
  (0, import_react8.useEffect)(() => {
    if (walletName === import_wallet_adapter_mobile2.SolanaMobileWalletAdapterWalletName && getIsMobile(adaptersWithStandardAdapters)) {
      isUnloadingRef.current = false;
      return;
    }
    function handleBeforeUnload() {
      isUnloadingRef.current = true;
    }
    window.addEventListener("beforeunload", handleBeforeUnload);
    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, [adaptersWithStandardAdapters, walletName]);
  const handleConnectError = (0, import_react8.useCallback)(() => {
    if (adapter && adapter.name !== import_wallet_adapter_mobile2.SolanaMobileWalletAdapterWalletName) {
      changeWallet(null);
    }
  }, [adapter, changeWallet]);
  const selectWallet = (0, import_react8.useCallback)((walletName2) => {
    hasUserSelectedAWallet.current = true;
    changeWallet(walletName2);
  }, [changeWallet]);
  return import_react8.default.createElement(WalletProviderBase, { wallets: adaptersWithMobileWalletAdapter, adapter, isUnloadingRef, onAutoConnectRequest: handleAutoConnectRequest, onConnectError: handleConnectError, onError, onSelectWallet: selectWallet }, children);
}

export {
  ConnectionContext,
  useConnection,
  ConnectionProvider,
  WalletNotSelectedError,
  WalletContext,
  useWallet,
  useAnchorWallet,
  useLocalStorage,
  require_index_browser3 as require_index_browser,
  WalletProvider
};
/*! Bundled license information:

@noble/hashes/utils.js:
  (*! noble-hashes - MIT License (c) 2022 Paul Miller (paulmillr.com) *)

@noble/curves/utils.js:
  (*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) *)

@noble/curves/abstract/modular.js:
  (*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) *)

@noble/curves/abstract/curve.js:
  (*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) *)

@noble/curves/abstract/edwards.js:
  (*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) *)

@noble/curves/abstract/montgomery.js:
  (*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) *)

@noble/curves/ed25519.js:
  (*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) *)
*/
//# sourceMappingURL=chunk-2NHIWPX2.js.map
