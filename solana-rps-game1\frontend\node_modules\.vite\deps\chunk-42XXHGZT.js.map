{"version": 3, "sources": ["../../@solana/wallet-standard-features/src/signAndSendTransaction.ts", "../../@solana/wallet-standard-features/src/signMessage.ts", "../../@solana/wallet-standard-features/src/signTransaction.ts", "../../@solana/wallet-standard-features/src/signIn.ts"], "sourcesContent": ["import type { IdentifierString } from '@wallet-standard/base';\nimport type {\n    SolanaSignTransactionInput,\n    SolanaSignTransactionOptions,\n    SolanaTransactionCommitment,\n    SolanaTransactionVersion,\n} from './signTransaction.js';\n\n/** Name of the feature. */\nexport const SolanaSignAndSendTransaction = 'solana:signAndSendTransaction';\n\n/** TODO: docs */\nexport type SolanaSignAndSendTransactionFeature = {\n    /** Name of the feature. */\n    readonly [SolanaSignAndSendTransaction]: {\n        /** Version of the feature API. */\n        readonly version: SolanaSignAndSendTransactionVersion;\n\n        /** TODO: docs */\n        readonly supportedTransactionVersions: readonly SolanaTransactionVersion[];\n\n        /**\n         * Sign transactions using the account's secret key and send them to the chain.\n         *\n         * @param inputs Inputs for signing and sending transactions.\n         *\n         * @return Outputs of signing and sending transactions.\n         */\n        readonly signAndSendTransaction: SolanaSignAndSendTransactionMethod;\n    };\n};\n\n/** Version of the feature. */\nexport type SolanaSignAndSendTransactionVersion = '1.0.0';\n\n/** TODO: docs */\nexport type SolanaSignAndSendTransactionMethod = (\n    ...inputs: readonly SolanaSignAndSendTransactionInput[]\n) => Promise<readonly SolanaSignAndSendTransactionOutput[]>;\n\n/** Input for signing and sending a transaction. */\nexport interface SolanaSignAndSendTransactionInput extends SolanaSignTransactionInput {\n    /** Chain to use. */\n    readonly chain: IdentifierString;\n\n    /** TODO: docs */\n    readonly options?: SolanaSignAndSendTransactionOptions;\n}\n\n/** Output of signing and sending a transaction. */\nexport interface SolanaSignAndSendTransactionOutput {\n    /** Transaction signature, as raw bytes. */\n    readonly signature: Uint8Array;\n}\n\n/** Options for signing and sending a transaction. */\nexport type SolanaSignAndSendTransactionOptions = SolanaSignTransactionOptions & {\n    /** Desired commitment level. If provided, confirm the transaction after sending. */\n    readonly commitment?: SolanaTransactionCommitment;\n\n    /** Disable transaction verification at the RPC. */\n    readonly skipPreflight?: boolean;\n\n    /** Maximum number of times for the RPC node to retry sending the transaction to the leader. */\n    readonly maxRetries?: number;\n};\n", "import type { WalletAccount } from '@wallet-standard/base';\n\n/** Name of the feature. */\nexport const SolanaSignMessage = 'solana:signMessage';\n\n/** TODO: docs */\nexport type SolanaSignMessageFeature = {\n    /** Name of the feature. */\n    readonly [SolanaSignMessage]: {\n        /** Version of the feature API. */\n        readonly version: SolanaSignMessageVersion;\n\n        /** Sign messages (arbitrary bytes) using the account's secret key. */\n        readonly signMessage: SolanaSignMessageMethod;\n    };\n};\n\n/** Version of the feature. */\nexport type SolanaSignMessageVersion = '1.1.0' | '1.0.0';\n\n/** TODO: docs */\nexport type SolanaSignMessageMethod = (\n    ...inputs: readonly SolanaSignMessageInput[]\n) => Promise<readonly SolanaSignMessageOutput[]>;\n\n/** Input for signing a message. */\nexport interface SolanaSignMessageInput {\n    /** Account to use. */\n    readonly account: WalletAccount;\n\n    /** Message to sign, as raw bytes. */\n    readonly message: Uint8Array;\n}\n\n/** Output of signing a message. */\nexport interface SolanaSignMessageOutput {\n    /**\n     * Message bytes that were signed.\n     * The wallet may prefix or otherwise modify the message before signing it.\n     */\n    readonly signedMessage: Uint8Array;\n\n    /**\n     * Message signature produced.\n     * If the signature type is provided, the signature must be Ed25519.\n     */\n    readonly signature: Uint8Array;\n\n    /**\n     * Optional type of the message signature produced.\n     * If not provided, the signature must be Ed25519.\n     */\n    readonly signatureType?: 'ed25519';\n}\n", "import type { IdentifierString, WalletAccount } from '@wallet-standard/base';\n\n/** Name of the feature. */\nexport const SolanaSignTransaction = 'solana:signTransaction';\n\n/** TODO: docs */\nexport type SolanaSignTransactionFeature = {\n    /** Name of the feature. */\n    readonly [SolanaSignTransaction]: {\n        /** Version of the feature API. */\n        readonly version: SolanaSignTransactionVersion;\n\n        /** TODO: docs */\n        readonly supportedTransactionVersions: readonly SolanaTransactionVersion[];\n\n        /**\n         * Sign transactions using the account's secret key.\n         *\n         * @param inputs Inputs for signing transactions.\n         *\n         * @return Outputs of signing transactions.\n         */\n        readonly signTransaction: SolanaSignTransactionMethod;\n    };\n};\n\n/** Version of the feature. */\nexport type SolanaSignTransactionVersion = '1.0.0';\n\n/** TODO: docs */\nexport type SolanaTransactionVersion = 'legacy' | 0;\n\n/** TODO: docs */\nexport type SolanaSignTransactionMethod = (\n    ...inputs: readonly SolanaSignTransactionInput[]\n) => Promise<readonly SolanaSignTransactionOutput[]>;\n\n/** Input for signing a transaction. */\nexport interface SolanaSignTransactionInput {\n    /** Account to use. */\n    readonly account: WalletAccount;\n\n    /** Serialized transaction, as raw bytes. */\n    readonly transaction: Uint8Array;\n\n    /** Chain to use. */\n    readonly chain?: IdentifierString;\n\n    /** TODO: docs */\n    readonly options?: SolanaSignTransactionOptions;\n}\n\n/** Output of signing a transaction. */\nexport interface SolanaSignTransactionOutput {\n    /**\n     * Signed, serialized transaction, as raw bytes.\n     * Returning a transaction rather than signatures allows multisig wallets, program wallets, and other wallets that\n     * use meta-transactions to return a modified, signed transaction.\n     */\n    readonly signedTransaction: Uint8Array;\n}\n\n/** Options for signing a transaction. */\nexport type SolanaSignTransactionOptions = {\n    /** Preflight commitment level. */\n    readonly preflightCommitment?: SolanaTransactionCommitment;\n\n    /** The minimum slot that the request can be evaluated at. */\n    readonly minContextSlot?: number;\n};\n\n/** Commitment level for transactions. */\nexport type SolanaTransactionCommitment = 'processed' | 'confirmed' | 'finalized';\n", "import type { WalletAccount } from '@wallet-standard/base';\n\n/** Name of the feature. */\nexport const SolanaSignIn = 'solana:signIn';\n\n/** TODO: docs */\nexport type SolanaSignInFeature = {\n    /** Name of the feature. */\n    readonly [SolanaSignIn]: {\n        /** Version of the feature API. */\n        readonly version: SolanaSignInVersion;\n\n        /** Sign In With Solana (based on https://eips.ethereum.org/EIPS/eip-4361 and https://github.com/ChainAgnostic/CAIPs/blob/master/CAIPs/caip-122.md). */\n        readonly signIn: SolanaSignInMethod;\n    };\n};\n\n/** Version of the feature. */\nexport type SolanaSignInVersion = '1.0.0';\n\n/** TODO: docs */\nexport type SolanaSignInMethod = (...inputs: readonly SolanaSignInInput[]) => Promise<readonly SolanaSignInOutput[]>;\n\n/** Input for signing in. */\nexport interface SolanaSignInInput {\n    /**\n     * Optional EIP-4361 Domain.\n     * If not provided, the wallet must determine the Domain to include in the message.\n     */\n    readonly domain?: string;\n\n    /**\n     * Optional EIP-4361 Address.\n     * If not provided, the wallet must determine the Address to include in the message.\n     */\n    readonly address?: string;\n\n    /**\n     * Optional EIP-4361 Statement.\n     * If not provided, the wallet must not include Statement in the message.\n     */\n    readonly statement?: string;\n\n    /**\n     * Optional EIP-4361 URI.\n     * If not provided, the wallet must not include URI in the message.\n     */\n    readonly uri?: string;\n\n    /**\n     * Optional EIP-4361 Version.\n     * If not provided, the wallet must not include Version in the message.\n     */\n    readonly version?: string;\n\n    /**\n     * Optional EIP-4361 Chain ID.\n     * If not provided, the wallet must not include Chain ID in the message.\n     */\n    readonly chainId?: string;\n\n    /**\n     * Optional EIP-4361 Nonce.\n     * If not provided, the wallet must not include Nonce in the message.\n     */\n    readonly nonce?: string;\n\n    /**\n     * Optional EIP-4361 Issued At.\n     * If not provided, the wallet must not include Issued At in the message.\n     */\n    readonly issuedAt?: string;\n\n    /**\n     * Optional EIP-4361 Expiration Time.\n     * If not provided, the wallet must not include Expiration Time in the message.\n     */\n    readonly expirationTime?: string;\n\n    /**\n     * Optional EIP-4361 Not Before.\n     * If not provided, the wallet must not include Not Before in the message.\n     */\n    readonly notBefore?: string;\n\n    /**\n     * Optional EIP-4361 Request ID.\n     * If not provided, the wallet must not include Request ID in the message.\n     */\n    readonly requestId?: string;\n\n    /**\n     * Optional EIP-4361 Resources.\n     * If not provided, the wallet must not include Resources in the message.\n     */\n    readonly resources?: readonly string[];\n}\n\n/** Output of signing in. */\nexport interface SolanaSignInOutput {\n    /**\n     * Account that was signed in.\n     * The address of the account may be different from the provided input Address.\n     */\n    readonly account: WalletAccount;\n\n    /**\n     * Message bytes that were signed.\n     * The wallet may prefix or otherwise modify the message before signing it.\n     */\n    readonly signedMessage: Uint8Array;\n\n    /**\n     * Message signature produced.\n     * If the signature type is provided, the signature must be Ed25519.\n     */\n    readonly signature: Uint8Array;\n\n    /**\n     * Optional type of the message signature produced.\n     * If not provided, the signature must be Ed25519.\n     */\n    readonly signatureType?: 'ed25519';\n}\n"], "mappings": ";AASO,IAAM,+BAA+B;;;ACNrC,IAAM,oBAAoB;;;ACA1B,IAAM,wBAAwB;;;ACA9B,IAAM,eAAe;", "names": []}