{"version": 3, "sources": ["../../eventemitter3/index.js", "../../@solana/wallet-adapter-base/src/adapter.ts", "../../@solana/wallet-adapter-base/src/errors.ts", "../../@solana/wallet-adapter-base/src/transaction.ts", "../../@solana/wallet-adapter-base/src/signer.ts", "../../@wallet-standard/features/src/connect.ts", "../../@wallet-standard/features/src/disconnect.ts", "../../@wallet-standard/features/src/events.ts", "../../@solana/wallet-adapter-base/src/standard.ts", "../../@solana/wallet-adapter-base/src/types.ts"], "sourcesContent": ["'use strict';\n\nvar has = Object.prototype.hasOwnProperty\n  , prefix = '~';\n\n/**\n * Constructor to create a storage for our `EE` objects.\n * An `Events` instance is a plain object whose properties are event names.\n *\n * @constructor\n * @private\n */\nfunction Events() {}\n\n//\n// We try to not inherit from `Object.prototype`. In some engines creating an\n// instance in this way is faster than calling `Object.create(null)` directly.\n// If `Object.create(null)` is not supported we prefix the event names with a\n// character to make sure that the built-in object properties are not\n// overridden or used as an attack vector.\n//\nif (Object.create) {\n  Events.prototype = Object.create(null);\n\n  //\n  // This hack is needed because the `__proto__` property is still inherited in\n  // some old browsers like Android 4, iPhone 5.1, Opera 11 and Safari 5.\n  //\n  if (!new Events().__proto__) prefix = false;\n}\n\n/**\n * Representation of a single event listener.\n *\n * @param {Function} fn The listener function.\n * @param {*} context The context to invoke the listener with.\n * @param {Boolean} [once=false] Specify if the listener is a one-time listener.\n * @constructor\n * @private\n */\nfunction EE(fn, context, once) {\n  this.fn = fn;\n  this.context = context;\n  this.once = once || false;\n}\n\n/**\n * Add a listener for a given event.\n *\n * @param {EventEmitter} emitter Reference to the `EventEmitter` instance.\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} context The context to invoke the listener with.\n * @param {Boolean} once Specify if the listener is a one-time listener.\n * @returns {EventEmitter}\n * @private\n */\nfunction addListener(emitter, event, fn, context, once) {\n  if (typeof fn !== 'function') {\n    throw new TypeError('The listener must be a function');\n  }\n\n  var listener = new EE(fn, context || emitter, once)\n    , evt = prefix ? prefix + event : event;\n\n  if (!emitter._events[evt]) emitter._events[evt] = listener, emitter._eventsCount++;\n  else if (!emitter._events[evt].fn) emitter._events[evt].push(listener);\n  else emitter._events[evt] = [emitter._events[evt], listener];\n\n  return emitter;\n}\n\n/**\n * Clear event by name.\n *\n * @param {EventEmitter} emitter Reference to the `EventEmitter` instance.\n * @param {(String|Symbol)} evt The Event name.\n * @private\n */\nfunction clearEvent(emitter, evt) {\n  if (--emitter._eventsCount === 0) emitter._events = new Events();\n  else delete emitter._events[evt];\n}\n\n/**\n * Minimal `EventEmitter` interface that is molded against the Node.js\n * `EventEmitter` interface.\n *\n * @constructor\n * @public\n */\nfunction EventEmitter() {\n  this._events = new Events();\n  this._eventsCount = 0;\n}\n\n/**\n * Return an array listing the events for which the emitter has registered\n * listeners.\n *\n * @returns {Array}\n * @public\n */\nEventEmitter.prototype.eventNames = function eventNames() {\n  var names = []\n    , events\n    , name;\n\n  if (this._eventsCount === 0) return names;\n\n  for (name in (events = this._events)) {\n    if (has.call(events, name)) names.push(prefix ? name.slice(1) : name);\n  }\n\n  if (Object.getOwnPropertySymbols) {\n    return names.concat(Object.getOwnPropertySymbols(events));\n  }\n\n  return names;\n};\n\n/**\n * Return the listeners registered for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Array} The registered listeners.\n * @public\n */\nEventEmitter.prototype.listeners = function listeners(event) {\n  var evt = prefix ? prefix + event : event\n    , handlers = this._events[evt];\n\n  if (!handlers) return [];\n  if (handlers.fn) return [handlers.fn];\n\n  for (var i = 0, l = handlers.length, ee = new Array(l); i < l; i++) {\n    ee[i] = handlers[i].fn;\n  }\n\n  return ee;\n};\n\n/**\n * Return the number of listeners listening to a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Number} The number of listeners.\n * @public\n */\nEventEmitter.prototype.listenerCount = function listenerCount(event) {\n  var evt = prefix ? prefix + event : event\n    , listeners = this._events[evt];\n\n  if (!listeners) return 0;\n  if (listeners.fn) return 1;\n  return listeners.length;\n};\n\n/**\n * Calls each of the listeners registered for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Boolean} `true` if the event had listeners, else `false`.\n * @public\n */\nEventEmitter.prototype.emit = function emit(event, a1, a2, a3, a4, a5) {\n  var evt = prefix ? prefix + event : event;\n\n  if (!this._events[evt]) return false;\n\n  var listeners = this._events[evt]\n    , len = arguments.length\n    , args\n    , i;\n\n  if (listeners.fn) {\n    if (listeners.once) this.removeListener(event, listeners.fn, undefined, true);\n\n    switch (len) {\n      case 1: return listeners.fn.call(listeners.context), true;\n      case 2: return listeners.fn.call(listeners.context, a1), true;\n      case 3: return listeners.fn.call(listeners.context, a1, a2), true;\n      case 4: return listeners.fn.call(listeners.context, a1, a2, a3), true;\n      case 5: return listeners.fn.call(listeners.context, a1, a2, a3, a4), true;\n      case 6: return listeners.fn.call(listeners.context, a1, a2, a3, a4, a5), true;\n    }\n\n    for (i = 1, args = new Array(len -1); i < len; i++) {\n      args[i - 1] = arguments[i];\n    }\n\n    listeners.fn.apply(listeners.context, args);\n  } else {\n    var length = listeners.length\n      , j;\n\n    for (i = 0; i < length; i++) {\n      if (listeners[i].once) this.removeListener(event, listeners[i].fn, undefined, true);\n\n      switch (len) {\n        case 1: listeners[i].fn.call(listeners[i].context); break;\n        case 2: listeners[i].fn.call(listeners[i].context, a1); break;\n        case 3: listeners[i].fn.call(listeners[i].context, a1, a2); break;\n        case 4: listeners[i].fn.call(listeners[i].context, a1, a2, a3); break;\n        default:\n          if (!args) for (j = 1, args = new Array(len -1); j < len; j++) {\n            args[j - 1] = arguments[j];\n          }\n\n          listeners[i].fn.apply(listeners[i].context, args);\n      }\n    }\n  }\n\n  return true;\n};\n\n/**\n * Add a listener for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} [context=this] The context to invoke the listener with.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.on = function on(event, fn, context) {\n  return addListener(this, event, fn, context, false);\n};\n\n/**\n * Add a one-time listener for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} [context=this] The context to invoke the listener with.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.once = function once(event, fn, context) {\n  return addListener(this, event, fn, context, true);\n};\n\n/**\n * Remove the listeners of a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn Only remove the listeners that match this function.\n * @param {*} context Only remove the listeners that have this context.\n * @param {Boolean} once Only remove one-time listeners.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.removeListener = function removeListener(event, fn, context, once) {\n  var evt = prefix ? prefix + event : event;\n\n  if (!this._events[evt]) return this;\n  if (!fn) {\n    clearEvent(this, evt);\n    return this;\n  }\n\n  var listeners = this._events[evt];\n\n  if (listeners.fn) {\n    if (\n      listeners.fn === fn &&\n      (!once || listeners.once) &&\n      (!context || listeners.context === context)\n    ) {\n      clearEvent(this, evt);\n    }\n  } else {\n    for (var i = 0, events = [], length = listeners.length; i < length; i++) {\n      if (\n        listeners[i].fn !== fn ||\n        (once && !listeners[i].once) ||\n        (context && listeners[i].context !== context)\n      ) {\n        events.push(listeners[i]);\n      }\n    }\n\n    //\n    // Reset the array, or remove it completely if we have no more listeners.\n    //\n    if (events.length) this._events[evt] = events.length === 1 ? events[0] : events;\n    else clearEvent(this, evt);\n  }\n\n  return this;\n};\n\n/**\n * Remove all listeners, or those of the specified event.\n *\n * @param {(String|Symbol)} [event] The event name.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.removeAllListeners = function removeAllListeners(event) {\n  var evt;\n\n  if (event) {\n    evt = prefix ? prefix + event : event;\n    if (this._events[evt]) clearEvent(this, evt);\n  } else {\n    this._events = new Events();\n    this._eventsCount = 0;\n  }\n\n  return this;\n};\n\n//\n// Alias methods names because people roll like that.\n//\nEventEmitter.prototype.off = EventEmitter.prototype.removeListener;\nEventEmitter.prototype.addListener = EventEmitter.prototype.on;\n\n//\n// Expose the prefix.\n//\nEventEmitter.prefixed = prefix;\n\n//\n// Allow `EventEmitter` to be imported as module namespace.\n//\nEventEmitter.EventEmitter = EventEmitter;\n\n//\n// Expose the module.\n//\nif ('undefined' !== typeof module) {\n  module.exports = EventEmitter;\n}\n", "import type { Connection, PublicKey, SendOptions, Signer, Transaction, TransactionSignature } from '@solana/web3.js';\nimport EventEmitter from 'eventemitter3';\nimport { type WalletError, WalletNotConnectedError } from './errors.js';\nimport type { SupportedTransactionVersions, TransactionOrVersionedTransaction } from './transaction.js';\n\nexport { EventEmitter };\n\nexport interface WalletAdapterEvents {\n    connect(publicKey: PublicKey): void;\n    disconnect(): void;\n    error(error: WalletError): void;\n    readyStateChange(readyState: WalletReadyState): void;\n}\n\nexport interface SendTransactionOptions extends SendOptions {\n    signers?: Signer[];\n}\n\n// WalletName is a nominal type that wallet adapters should use, e.g. `'MyCryptoWallet' as WalletName<'MyCryptoWallet'>`\n// https://medium.com/@KevinBGreene/surviving-the-typescript-ecosystem-branding-and-type-tagging-6cf6e516523d\nexport type WalletName<T extends string = string> = T & { __brand__: 'WalletName' };\n\nexport interface WalletAdapterProps<Name extends string = string> {\n    name: WalletName<Name>;\n    url: string;\n    icon: string;\n    readyState: WalletReadyState;\n    publicKey: PublicKey | null;\n    connecting: boolean;\n    connected: boolean;\n    supportedTransactionVersions?: SupportedTransactionVersions;\n\n    autoConnect(): Promise<void>;\n    connect(): Promise<void>;\n    disconnect(): Promise<void>;\n    sendTransaction(\n        transaction: TransactionOrVersionedTransaction<this['supportedTransactionVersions']>,\n        connection: Connection,\n        options?: SendTransactionOptions\n    ): Promise<TransactionSignature>;\n}\n\nexport type WalletAdapter<Name extends string = string> = WalletAdapterProps<Name> & EventEmitter<WalletAdapterEvents>;\n\n/**\n * A wallet's readiness describes a series of states that the wallet can be in,\n * depending on what kind of wallet it is. An installable wallet (eg. a browser\n * extension like Phantom) might be `Installed` if we've found the Phantom API\n * in the global scope, or `NotDetected` otherwise. A loadable, zero-install\n * runtime (eg. Torus Wallet) might simply signal that it's `Loadable`. Use this\n * metadata to personalize the wallet list for each user (eg. to show their\n * installed wallets first).\n */\nexport enum WalletReadyState {\n    /**\n     * User-installable wallets can typically be detected by scanning for an API\n     * that they've injected into the global context. If such an API is present,\n     * we consider the wallet to have been installed.\n     */\n    Installed = 'Installed',\n    NotDetected = 'NotDetected',\n    /**\n     * Loadable wallets are always available to you. Since you can load them at\n     * any time, it's meaningless to say that they have been detected.\n     */\n    Loadable = 'Loadable',\n    /**\n     * If a wallet is not supported on a given platform (eg. server-rendering, or\n     * mobile) then it will stay in the `Unsupported` state.\n     */\n    Unsupported = 'Unsupported',\n}\n\nexport abstract class BaseWalletAdapter<Name extends string = string>\n    extends EventEmitter<WalletAdapterEvents>\n    implements WalletAdapter<Name>\n{\n    abstract name: WalletName<Name>;\n    abstract url: string;\n    abstract icon: string;\n    abstract readyState: WalletReadyState;\n    abstract publicKey: PublicKey | null;\n    abstract connecting: boolean;\n    abstract supportedTransactionVersions?: SupportedTransactionVersions;\n\n    get connected() {\n        return !!this.publicKey;\n    }\n\n    async autoConnect() {\n        await this.connect();\n    }\n\n    abstract connect(): Promise<void>;\n    abstract disconnect(): Promise<void>;\n\n    abstract sendTransaction(\n        transaction: TransactionOrVersionedTransaction<this['supportedTransactionVersions']>,\n        connection: Connection,\n        options?: SendTransactionOptions\n    ): Promise<TransactionSignature>;\n\n    protected async prepareTransaction(\n        transaction: Transaction,\n        connection: Connection,\n        options: SendOptions = {}\n    ): Promise<Transaction> {\n        const publicKey = this.publicKey;\n        if (!publicKey) throw new WalletNotConnectedError();\n\n        transaction.feePayer = transaction.feePayer || publicKey;\n        transaction.recentBlockhash =\n            transaction.recentBlockhash ||\n            (\n                await connection.getLatestBlockhash({\n                    commitment: options.preflightCommitment,\n                    minContextSlot: options.minContextSlot,\n                })\n            ).blockhash;\n\n        return transaction;\n    }\n}\n\nexport function scopePollingDetectionStrategy(detect: () => boolean): void {\n    // Early return when server-side rendering\n    if (typeof window === 'undefined' || typeof document === 'undefined') return;\n\n    const disposers: (() => void)[] = [];\n\n    function detectAndDispose() {\n        const detected = detect();\n        if (detected) {\n            for (const dispose of disposers) {\n                dispose();\n            }\n        }\n    }\n\n    // Strategy #1: Try detecting every second.\n    const interval =\n        // TODO: #334 Replace with idle callback strategy.\n        setInterval(detectAndDispose, 1000);\n    disposers.push(() => clearInterval(interval));\n\n    // Strategy #2: Detect as soon as the DOM becomes 'ready'/'interactive'.\n    if (\n        // Implies that `DOMContentLoaded` has not yet fired.\n        document.readyState === 'loading'\n    ) {\n        document.addEventListener('DOMContentLoaded', detectAndDispose, { once: true });\n        disposers.push(() => document.removeEventListener('DOMContentLoaded', detectAndDispose));\n    }\n\n    // Strategy #3: Detect after the `window` has fully loaded.\n    if (\n        // If the `complete` state has been reached, we're too late.\n        document.readyState !== 'complete'\n    ) {\n        window.addEventListener('load', detectAndDispose, { once: true });\n        disposers.push(() => window.removeEventListener('load', detectAndDispose));\n    }\n\n    // Strategy #4: Detect synchronously, now.\n    detectAndDispose();\n}\n\n/**\n * Users on iOS can be redirected into a wallet's in-app browser automatically,\n * if that wallet has a universal link configured to do so\n * But should not be redirected from within a webview, eg. if they're already\n * inside a wallet's browser\n * This function can be used to identify users who are on iOS and can be redirected\n *\n * @returns true if the user can be redirected\n */\nexport function isIosAndRedirectable() {\n    // SSR: return false\n    if (!navigator) return false;\n\n    const userAgent = navigator.userAgent.toLowerCase();\n\n    // if on iOS the user agent will contain either iPhone or iPad\n    // caveat: if requesting desktop site then this won't work\n    const isIos = userAgent.includes('iphone') || userAgent.includes('ipad');\n\n    // if in a webview then it will not include Safari\n    // note that other iOS browsers also include Safari\n    // so we will redirect only if Safari is also included\n    const isSafari = userAgent.includes('safari');\n\n    return isIos && isSafari;\n}\n", "export class WalletError extends Error {\n    error: any;\n\n    // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\n    constructor(message?: string, error?: any) {\n        super(message);\n        this.error = error;\n    }\n}\n\nexport class WalletNotReadyError extends WalletError {\n    name = 'WalletNotReadyError';\n}\n\nexport class WalletLoadError extends WalletError {\n    name = 'WalletLoadError';\n}\n\nexport class WalletConfigError extends WalletError {\n    name = 'WalletConfigError';\n}\n\nexport class WalletConnectionError extends WalletError {\n    name = 'WalletConnectionError';\n}\n\nexport class WalletDisconnectedError extends WalletError {\n    name = 'WalletDisconnectedError';\n}\n\nexport class WalletDisconnectionError extends WalletError {\n    name = 'WalletDisconnectionError';\n}\n\nexport class WalletAccountError extends WalletError {\n    name = 'WalletAccountError';\n}\n\nexport class WalletPublicKeyError extends WalletError {\n    name = 'WalletPublicKeyError';\n}\n\nexport class WalletKeypairError extends WalletError {\n    name = 'WalletKeypairError';\n}\n\nexport class WalletNotConnectedError extends WalletError {\n    name = 'WalletNotConnectedError';\n}\n\nexport class WalletSendTransactionError extends WalletError {\n    name = 'WalletSendTransactionError';\n}\n\nexport class WalletSignTransactionError extends WalletError {\n    name = 'WalletSignTransactionError';\n}\n\nexport class WalletSignMessageError extends WalletError {\n    name = 'WalletSignMessageError';\n}\n\nexport class WalletSignInError extends WalletError {\n    name = 'WalletSignInError';\n}\n\nexport class WalletTimeoutError extends WalletError {\n    name = 'WalletTimeoutError';\n}\n\nexport class WalletWindowBlockedError extends WalletError {\n    name = 'WalletWindowBlockedError';\n}\n\nexport class WalletWindowClosedError extends WalletError {\n    name = 'WalletWindowClosedError';\n}\n", "import type { Transaction, TransactionVersion, VersionedTransaction } from '@solana/web3.js';\n\nexport type SupportedTransactionVersions = ReadonlySet<TransactionVersion> | null | undefined;\n\nexport type TransactionOrVersionedTransaction<S extends SupportedTransactionVersions> = S extends null | undefined\n    ? Transaction\n    : Transaction | VersionedTransaction;\n\nexport function isVersionedTransaction(\n    transaction: Transaction | VersionedTransaction\n): transaction is VersionedTransaction {\n    return 'version' in transaction;\n}\n", "import type { SolanaSignInInput, SolanaSignInOutput } from '@solana/wallet-standard-features';\nimport type { Connection, TransactionSignature } from '@solana/web3.js';\nimport {\n    BaseWalletAdapter,\n    type SendTransactionOptions,\n    type WalletAdapter,\n    type WalletAdapterProps,\n} from './adapter.js';\nimport { WalletSendTransactionError, WalletSignTransactionError } from './errors.js';\nimport { isVersionedTransaction, type TransactionOrVersionedTransaction } from './transaction.js';\n\nexport interface SignerWalletAdapterProps<Name extends string = string> extends WalletAdapterProps<Name> {\n    signTransaction<T extends TransactionOrVersionedTransaction<this['supportedTransactionVersions']>>(\n        transaction: T\n    ): Promise<T>;\n    signAllTransactions<T extends TransactionOrVersionedTransaction<this['supportedTransactionVersions']>>(\n        transactions: T[]\n    ): Promise<T[]>;\n}\n\nexport type SignerWalletAdapter<Name extends string = string> = WalletAdapter<Name> & SignerWalletAdapterProps<Name>;\n\nexport abstract class BaseSignerWalletAdapter<Name extends string = string>\n    extends BaseWalletAdapter<Name>\n    implements SignerWalletAdapter<Name>\n{\n    async sendTransaction(\n        transaction: TransactionOrVersionedTransaction<this['supportedTransactionVersions']>,\n        connection: Connection,\n        options: SendTransactionOptions = {}\n    ): Promise<TransactionSignature> {\n        let emit = true;\n        try {\n            if (isVersionedTransaction(transaction)) {\n                if (!this.supportedTransactionVersions)\n                    throw new WalletSendTransactionError(\n                        `Sending versioned transactions isn't supported by this wallet`\n                    );\n\n                if (!this.supportedTransactionVersions.has(transaction.version))\n                    throw new WalletSendTransactionError(\n                        `Sending transaction version ${transaction.version} isn't supported by this wallet`\n                    );\n\n                try {\n                    transaction = await this.signTransaction(transaction);\n\n                    const rawTransaction = transaction.serialize();\n\n                    return await connection.sendRawTransaction(rawTransaction, options);\n                } catch (error: any) {\n                    // If the error was thrown by `signTransaction`, rethrow it and don't emit a duplicate event\n                    if (error instanceof WalletSignTransactionError) {\n                        emit = false;\n                        throw error;\n                    }\n                    throw new WalletSendTransactionError(error?.message, error);\n                }\n            } else {\n                try {\n                    const { signers, ...sendOptions } = options;\n\n                    transaction = await this.prepareTransaction(transaction, connection, sendOptions);\n\n                    signers?.length && transaction.partialSign(...signers);\n\n                    transaction = await this.signTransaction(transaction);\n\n                    const rawTransaction = transaction.serialize();\n\n                    return await connection.sendRawTransaction(rawTransaction, sendOptions);\n                } catch (error: any) {\n                    // If the error was thrown by `signTransaction`, rethrow it and don't emit a duplicate event\n                    if (error instanceof WalletSignTransactionError) {\n                        emit = false;\n                        throw error;\n                    }\n                    throw new WalletSendTransactionError(error?.message, error);\n                }\n            }\n        } catch (error: any) {\n            if (emit) {\n                this.emit('error', error);\n            }\n            throw error;\n        }\n    }\n\n    abstract signTransaction<T extends TransactionOrVersionedTransaction<this['supportedTransactionVersions']>>(\n        transaction: T\n    ): Promise<T>;\n\n    async signAllTransactions<T extends TransactionOrVersionedTransaction<this['supportedTransactionVersions']>>(\n        transactions: T[]\n    ): Promise<T[]> {\n        for (const transaction of transactions) {\n            if (isVersionedTransaction(transaction)) {\n                if (!this.supportedTransactionVersions)\n                    throw new WalletSignTransactionError(\n                        `Signing versioned transactions isn't supported by this wallet`\n                    );\n\n                if (!this.supportedTransactionVersions.has(transaction.version))\n                    throw new WalletSignTransactionError(\n                        `Signing transaction version ${transaction.version} isn't supported by this wallet`\n                    );\n            }\n        }\n\n        const signedTransactions: T[] = [];\n        for (const transaction of transactions) {\n            signedTransactions.push(await this.signTransaction(transaction));\n        }\n        return signedTransactions;\n    }\n}\n\nexport interface MessageSignerWalletAdapterProps<Name extends string = string> extends WalletAdapterProps<Name> {\n    signMessage(message: Uint8Array): Promise<Uint8Array>;\n}\n\nexport type MessageSignerWalletAdapter<Name extends string = string> = WalletAdapter<Name> &\n    MessageSignerWalletAdapterProps<Name>;\n\nexport abstract class BaseMessageSignerWalletAdapter<Name extends string = string>\n    extends BaseSignerWalletAdapter<Name>\n    implements MessageSignerWalletAdapter<Name>\n{\n    abstract signMessage(message: Uint8Array): Promise<Uint8Array>;\n}\n\nexport interface SignInMessageSignerWalletAdapterProps<Name extends string = string> extends WalletAdapterProps<Name> {\n    signIn(input?: SolanaSignInInput): Promise<SolanaSignInOutput>;\n}\n\nexport type SignInMessageSignerWalletAdapter<Name extends string = string> = WalletAdapter<Name> &\n    SignInMessageSignerWalletAdapterProps<Name>;\n\nexport abstract class BaseSignInMessageSignerWalletAdapter<Name extends string = string>\n    extends BaseMessageSignerWalletAdapter<Name>\n    implements SignInMessageSignerWalletAdapter<Name>\n{\n    abstract signIn(input?: SolanaSignInInput): Promise<SolanaSignInOutput>;\n}\n", "import type { WalletAccount } from '@wallet-standard/base';\n\n/** Name of the feature. */\nexport const StandardConnect = 'standard:connect';\n/**\n * @deprecated Use {@link StandardConnect} instead.\n *\n * @group Deprecated\n */\nexport const Connect = StandardConnect;\n\n/**\n * `standard:connect` is a {@link \"@wallet-standard/base\".Wallet.features | feature} that may be implemented by a\n * {@link \"@wallet-standard/base\".Wallet} to allow the app to obtain authorization to use\n * {@link \"@wallet-standard/base\".Wallet.accounts}.\n *\n * @group Connect\n */\nexport type StandardConnectFeature = {\n    /** Name of the feature. */\n    readonly [StandardConnect]: {\n        /** Version of the feature implemented by the Wallet. */\n        readonly version: StandardConnectVersion;\n        /** Method to call to use the feature. */\n        readonly connect: StandardConnectMethod;\n    };\n};\n/**\n * @deprecated Use {@link StandardConnectFeature} instead.\n *\n * @group Deprecated\n */\nexport type ConnectFeature = StandardConnectFeature;\n\n/**\n * Version of the {@link StandardConnectFeature} implemented by a {@link \"@wallet-standard/base\".Wallet}.\n *\n * @group Connect\n */\nexport type StandardConnectVersion = '1.0.0';\n/**\n * @deprecated Use {@link StandardConnectVersion} instead.\n *\n * @group Deprecated\n */\nexport type ConnectVersion = StandardConnectVersion;\n\n/**\n * Method to call to use the {@link StandardConnectFeature}.\n *\n * @group Connect\n */\nexport type StandardConnectMethod = (input?: StandardConnectInput) => Promise<StandardConnectOutput>;\n/**\n * @deprecated Use {@link StandardConnectMethod} instead.\n *\n * @group Deprecated\n */\nexport type ConnectMethod = StandardConnectMethod;\n\n/**\n * Input for the {@link StandardConnectMethod}.\n *\n * @group Connect\n */\nexport interface StandardConnectInput {\n    /**\n     * By default, using the {@link StandardConnectFeature} should prompt the user to request authorization to accounts.\n     * Set the `silent` flag to `true` to request accounts that have already been authorized without prompting.\n     *\n     * This flag may or may not be used by the Wallet and the app should not depend on it being used.\n     * If this flag is used by the Wallet, the Wallet should not prompt the user, and should return only the accounts\n     * that the app is authorized to use.\n     */\n    readonly silent?: boolean;\n}\n/**\n * @deprecated Use {@link StandardConnectInput} instead.\n *\n * @group Deprecated\n */\nexport type ConnectInput = StandardConnectInput;\n\n/**\n * Output of the {@link StandardConnectMethod}.\n *\n * @group Connect\n */\nexport interface StandardConnectOutput {\n    /** List of accounts in the {@link \"@wallet-standard/base\".Wallet} that the app has been authorized to use. */\n    readonly accounts: readonly WalletAccount[];\n}\n/**\n * @deprecated Use {@link StandardConnectOutput} instead.\n *\n * @group Deprecated\n */\nexport type ConnectOutput = StandardConnectOutput;\n", "/** Name of the feature. */\nexport const StandardDisconnect = 'standard:disconnect';\n/**\n * @deprecated Use {@link StandardDisconnect} instead.\n *\n * @group Deprecated\n */\nexport const Disconnect = StandardDisconnect;\n\n/**\n * `standard:disconnect` is a {@link \"@wallet-standard/base\".Wallet.features | feature} that may be implemented by a\n * {@link \"@wallet-standard/base\".Wallet} to allow the app to perform any cleanup work.\n *\n * This feature may or may not be used by the app and the Wallet should not depend on it being used.\n * If this feature is used by the app, the Wallet should perform any cleanup work, but should not revoke authorization\n * to use accounts previously granted through the {@link ConnectFeature}.\n *\n * @group Disconnect\n */\nexport type StandardDisconnectFeature = {\n    /** Name of the feature. */\n    readonly [StandardDisconnect]: {\n        /** Version of the feature implemented by the Wallet. */\n        readonly version: StandardDisconnectVersion;\n        /** Method to call to use the feature. */\n        readonly disconnect: StandardDisconnectMethod;\n    };\n};\n/**\n * @deprecated Use {@link StandardDisconnectFeature} instead.\n *\n * @group Deprecated\n */\nexport type DisconnectFeature = StandardDisconnectFeature;\n\n/**\n * Version of the {@link StandardDisconnectFeature} implemented by a Wallet.\n *\n * @group Disconnect\n */\nexport type StandardDisconnectVersion = '1.0.0';\n/**\n * @deprecated Use {@link StandardDisconnectVersion} instead.\n *\n * @group Deprecated\n */\nexport type DisconnectVersion = StandardDisconnectVersion;\n\n/**\n * Method to call to use the {@link StandardDisconnectFeature}.\n *\n * @group Disconnect\n */\nexport type StandardDisconnectMethod = () => Promise<void>;\n/**\n * @deprecated Use {@link StandardDisconnectMethod} instead.\n *\n * @group Deprecated\n */\nexport type DisconnectMethod = StandardDisconnectMethod;\n", "import type { Wallet } from '@wallet-standard/base';\n\n/** Name of the feature. */\nexport const StandardEvents = 'standard:events';\n/**\n * @deprecated Use {@link StandardEvents} instead.\n *\n * @group Deprecated\n */\nexport const Events = StandardEvents;\n\n/**\n * `standard:events` is a {@link \"@wallet-standard/base\".Wallet.features | feature} that may be implemented by a\n * {@link \"@wallet-standard/base\".Wallet} to allow the app to add an event listener and subscribe to events emitted by\n * the Wallet when properties of the Wallet {@link StandardEventsListeners.change}.\n *\n * @group Events\n */\nexport type StandardEventsFeature = {\n    /** Name of the feature. */\n    readonly [StandardEvents]: {\n        /** Version of the feature implemented by the {@link \"@wallet-standard/base\".Wallet}. */\n        readonly version: StandardEventsVersion;\n        /** Method to call to use the feature. */\n        readonly on: StandardEventsOnMethod;\n    };\n};\n/**\n * @deprecated Use {@link StandardEventsFeature} instead.\n *\n * @group Deprecated\n */\nexport type EventsFeature = StandardEventsFeature;\n\n/**\n * Version of the {@link StandardEventsFeature} implemented by a {@link \"@wallet-standard/base\".Wallet}.\n *\n * @group Events\n */\nexport type StandardEventsVersion = '1.0.0';\n/**\n * @deprecated Use {@link StandardEventsVersion} instead.\n *\n * @group Deprecated\n */\nexport type EventsVersion = StandardEventsVersion;\n\n/**\n * Method to call to use the {@link StandardEventsFeature}.\n *\n * @param event    Event type to listen for. {@link StandardEventsListeners.change | `change`} is the only event type.\n * @param listener Function that will be called when an event of the type is emitted.\n *\n * @return\n * `off` function which may be called to remove the event listener and unsubscribe from events.\n *\n * As with all event listeners, be careful to avoid memory leaks.\n *\n * @group Events\n */\nexport type StandardEventsOnMethod = <E extends StandardEventsNames>(\n    event: E,\n    listener: StandardEventsListeners[E]\n) => () => void;\n/**\n * @deprecated Use {@link StandardEventsOnMethod} instead.\n *\n * @group Deprecated\n */\nexport type EventsOnMethod = StandardEventsOnMethod;\n\n/**\n * Types of event listeners of the {@link StandardEventsFeature}.\n *\n * @group Events\n */\nexport interface StandardEventsListeners {\n    /**\n     * Listener that will be called when {@link StandardEventsChangeProperties | properties} of the\n     * {@link \"@wallet-standard/base\".Wallet} have changed.\n     *\n     * @param properties Properties that changed with their **new** values.\n     */\n    change(properties: StandardEventsChangeProperties): void;\n}\n/**\n * @deprecated Use {@link StandardEventsListeners} instead.\n *\n * @group Deprecated\n */\nexport type EventsListeners = StandardEventsListeners;\n\n/**\n * Names of {@link StandardEventsListeners} that can be listened for.\n *\n * @group Events\n */\nexport type StandardEventsNames = keyof StandardEventsListeners;\n/**\n * @deprecated Use {@link StandardEventsNames} instead.\n *\n * @group Deprecated\n */\nexport type EventsNames = StandardEventsNames;\n\n/**\n * Properties of a {@link \"@wallet-standard/base\".Wallet} that {@link StandardEventsListeners.change | changed} with their\n * **new** values.\n *\n * @group Events\n */\nexport interface StandardEventsChangeProperties {\n    /**\n     * {@link \"@wallet-standard/base\".Wallet.chains | Chains} supported by the Wallet.\n     *\n     * The Wallet should only define this field if the value of the property has changed.\n     *\n     * The value must be the **new** value of the property.\n     */\n    readonly chains?: Wallet['chains'];\n    /**\n     * {@link \"@wallet-standard/base\".Wallet.features | Features} supported by the Wallet.\n     *\n     * The Wallet should only define this field if the value of the property has changed.\n     *\n     * The value must be the **new** value of the property.\n     */\n    readonly features?: Wallet['features'];\n    /**\n     * {@link \"@wallet-standard/base\".Wallet.accounts | Accounts} that the app is authorized to use.\n     *\n     * The Wallet should only define this field if the value of the property has changed.\n     *\n     * The value must be the **new** value of the property.\n     */\n    readonly accounts?: Wallet['accounts'];\n}\n/**\n * @deprecated Use {@link StandardEventsChangeProperties} instead.\n *\n * @group Deprecated\n */\nexport type EventsChangeProperties = StandardEventsChangeProperties;\n", "import {\n    SolanaSignAndSendTransaction,\n    type SolanaSignAndSendTransactionFeature,\n    type SolanaSignInFeature,\n    type SolanaSignMessageFeature,\n    SolanaSignTransaction,\n    type SolanaSignTransactionFeature,\n} from '@solana/wallet-standard-features';\nimport type { Wallet as StandardWallet, WalletWithFeatures as StandardWalletWithFeatures } from '@wallet-standard/base';\nimport {\n    StandardConnect,\n    type StandardConnectFeature,\n    type StandardDisconnectFeature,\n    StandardEvents,\n    type StandardEventsFeature,\n} from '@wallet-standard/features';\nimport type { WalletAdapter, WalletAdapterProps } from './adapter.js';\n\nexport type WalletAdapterCompatibleStandardWallet = StandardWalletWithFeatures<\n    StandardConnectFeature &\n        StandardEventsFeature &\n        (SolanaSignAndSendTransactionFeature | SolanaSignTransactionFeature) &\n        (StandardDisconnectFeature | SolanaSignMessageFeature | SolanaSignInFeature | object)\n>;\n\nexport interface StandardWalletAdapterProps<Name extends string = string> extends WalletAdapterProps<Name> {\n    wallet: WalletAdapterCompatibleStandardWallet;\n    standard: true;\n}\n\nexport type StandardWalletAdapter<Name extends string = string> = WalletAdapter<Name> &\n    StandardWalletAdapterProps<Name>;\n\nexport function isWalletAdapterCompatibleStandardWallet(\n    wallet: StandardWallet\n): wallet is WalletAdapterCompatibleStandardWallet {\n    return (\n        StandardConnect in wallet.features &&\n        StandardEvents in wallet.features &&\n        (SolanaSignAndSendTransaction in wallet.features || SolanaSignTransaction in wallet.features)\n    );\n}\n", "import type { WalletAdapter } from './adapter.js';\nimport type { MessageSignerWalletAdapter, SignerWalletAdapter, SignInMessageSignerWalletAdapter } from './signer.js';\nimport type { StandardWalletAdapter } from './standard.js';\n\nexport type Adapter =\n    | WalletAdapter\n    | SignerWalletAdapter\n    | MessageSignerWalletAdapter\n    | SignInMessageSignerWalletAdapter\n    | StandardWalletAdapter;\n\nexport enum WalletAdapterNetwork {\n    Mainnet = 'mainnet-beta',\n    Testnet = 'testnet',\n    Devnet = 'devnet',\n}\n"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAEA,QAAI,MAAM,OAAO,UAAU;AAA3B,QACI,SAAS;AASb,aAAS,SAAS;AAAA,IAAC;AASnB,QAAI,OAAO,QAAQ;AACjB,aAAO,YAAY,uBAAO,OAAO,IAAI;AAMrC,UAAI,CAAC,IAAI,OAAO,EAAE;AAAW,iBAAS;AAAA,IACxC;AAWA,aAAS,GAAG,IAAI,SAAS,MAAM;AAC7B,WAAK,KAAK;AACV,WAAK,UAAU;AACf,WAAK,OAAO,QAAQ;AAAA,IACtB;AAaA,aAAS,YAAY,SAAS,OAAO,IAAI,SAAS,MAAM;AACtD,UAAI,OAAO,OAAO,YAAY;AAC5B,cAAM,IAAI,UAAU,iCAAiC;AAAA,MACvD;AAEA,UAAI,WAAW,IAAI,GAAG,IAAI,WAAW,SAAS,IAAI,GAC9C,MAAM,SAAS,SAAS,QAAQ;AAEpC,UAAI,CAAC,QAAQ,QAAQ,GAAG;AAAG,gBAAQ,QAAQ,GAAG,IAAI,UAAU,QAAQ;AAAA,eAC3D,CAAC,QAAQ,QAAQ,GAAG,EAAE;AAAI,gBAAQ,QAAQ,GAAG,EAAE,KAAK,QAAQ;AAAA;AAChE,gBAAQ,QAAQ,GAAG,IAAI,CAAC,QAAQ,QAAQ,GAAG,GAAG,QAAQ;AAE3D,aAAO;AAAA,IACT;AASA,aAAS,WAAW,SAAS,KAAK;AAChC,UAAI,EAAE,QAAQ,iBAAiB;AAAG,gBAAQ,UAAU,IAAI,OAAO;AAAA;AAC1D,eAAO,QAAQ,QAAQ,GAAG;AAAA,IACjC;AASA,aAASA,gBAAe;AACtB,WAAK,UAAU,IAAI,OAAO;AAC1B,WAAK,eAAe;AAAA,IACtB;AASA,IAAAA,cAAa,UAAU,aAAa,SAAS,aAAa;AACxD,UAAI,QAAQ,CAAC,GACT,QACA;AAEJ,UAAI,KAAK,iBAAiB;AAAG,eAAO;AAEpC,WAAK,QAAS,SAAS,KAAK,SAAU;AACpC,YAAI,IAAI,KAAK,QAAQ,IAAI;AAAG,gBAAM,KAAK,SAAS,KAAK,MAAM,CAAC,IAAI,IAAI;AAAA,MACtE;AAEA,UAAI,OAAO,uBAAuB;AAChC,eAAO,MAAM,OAAO,OAAO,sBAAsB,MAAM,CAAC;AAAA,MAC1D;AAEA,aAAO;AAAA,IACT;AASA,IAAAA,cAAa,UAAU,YAAY,SAAS,UAAU,OAAO;AAC3D,UAAI,MAAM,SAAS,SAAS,QAAQ,OAChC,WAAW,KAAK,QAAQ,GAAG;AAE/B,UAAI,CAAC;AAAU,eAAO,CAAC;AACvB,UAAI,SAAS;AAAI,eAAO,CAAC,SAAS,EAAE;AAEpC,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,KAAK;AAClE,WAAG,CAAC,IAAI,SAAS,CAAC,EAAE;AAAA,MACtB;AAEA,aAAO;AAAA,IACT;AASA,IAAAA,cAAa,UAAU,gBAAgB,SAAS,cAAc,OAAO;AACnE,UAAI,MAAM,SAAS,SAAS,QAAQ,OAChC,YAAY,KAAK,QAAQ,GAAG;AAEhC,UAAI,CAAC;AAAW,eAAO;AACvB,UAAI,UAAU;AAAI,eAAO;AACzB,aAAO,UAAU;AAAA,IACnB;AASA,IAAAA,cAAa,UAAU,OAAO,SAAS,KAAK,OAAO,IAAI,IAAI,IAAI,IAAI,IAAI;AACrE,UAAI,MAAM,SAAS,SAAS,QAAQ;AAEpC,UAAI,CAAC,KAAK,QAAQ,GAAG;AAAG,eAAO;AAE/B,UAAI,YAAY,KAAK,QAAQ,GAAG,GAC5B,MAAM,UAAU,QAChB,MACA;AAEJ,UAAI,UAAU,IAAI;AAChB,YAAI,UAAU;AAAM,eAAK,eAAe,OAAO,UAAU,IAAI,QAAW,IAAI;AAE5E,gBAAQ,KAAK;AAAA,UACX,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,OAAO,GAAG;AAAA,UACrD,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,EAAE,GAAG;AAAA,UACzD,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,EAAE,GAAG;AAAA,UAC7D,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,IAAI,EAAE,GAAG;AAAA,UACjE,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,IAAI,IAAI,EAAE,GAAG;AAAA,UACrE,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG;AAAA,QAC3E;AAEA,aAAK,IAAI,GAAG,OAAO,IAAI,MAAM,MAAK,CAAC,GAAG,IAAI,KAAK,KAAK;AAClD,eAAK,IAAI,CAAC,IAAI,UAAU,CAAC;AAAA,QAC3B;AAEA,kBAAU,GAAG,MAAM,UAAU,SAAS,IAAI;AAAA,MAC5C,OAAO;AACL,YAAI,SAAS,UAAU,QACnB;AAEJ,aAAK,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC3B,cAAI,UAAU,CAAC,EAAE;AAAM,iBAAK,eAAe,OAAO,UAAU,CAAC,EAAE,IAAI,QAAW,IAAI;AAElF,kBAAQ,KAAK;AAAA,YACX,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,OAAO;AAAG;AAAA,YACpD,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,SAAS,EAAE;AAAG;AAAA,YACxD,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,SAAS,IAAI,EAAE;AAAG;AAAA,YAC5D,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,SAAS,IAAI,IAAI,EAAE;AAAG;AAAA,YAChE;AACE,kBAAI,CAAC;AAAM,qBAAK,IAAI,GAAG,OAAO,IAAI,MAAM,MAAK,CAAC,GAAG,IAAI,KAAK,KAAK;AAC7D,uBAAK,IAAI,CAAC,IAAI,UAAU,CAAC;AAAA,gBAC3B;AAEA,wBAAU,CAAC,EAAE,GAAG,MAAM,UAAU,CAAC,EAAE,SAAS,IAAI;AAAA,UACpD;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAWA,IAAAA,cAAa,UAAU,KAAK,SAAS,GAAG,OAAO,IAAI,SAAS;AAC1D,aAAO,YAAY,MAAM,OAAO,IAAI,SAAS,KAAK;AAAA,IACpD;AAWA,IAAAA,cAAa,UAAU,OAAO,SAAS,KAAK,OAAO,IAAI,SAAS;AAC9D,aAAO,YAAY,MAAM,OAAO,IAAI,SAAS,IAAI;AAAA,IACnD;AAYA,IAAAA,cAAa,UAAU,iBAAiB,SAAS,eAAe,OAAO,IAAI,SAAS,MAAM;AACxF,UAAI,MAAM,SAAS,SAAS,QAAQ;AAEpC,UAAI,CAAC,KAAK,QAAQ,GAAG;AAAG,eAAO;AAC/B,UAAI,CAAC,IAAI;AACP,mBAAW,MAAM,GAAG;AACpB,eAAO;AAAA,MACT;AAEA,UAAI,YAAY,KAAK,QAAQ,GAAG;AAEhC,UAAI,UAAU,IAAI;AAChB,YACE,UAAU,OAAO,OAChB,CAAC,QAAQ,UAAU,UACnB,CAAC,WAAW,UAAU,YAAY,UACnC;AACA,qBAAW,MAAM,GAAG;AAAA,QACtB;AAAA,MACF,OAAO;AACL,iBAAS,IAAI,GAAG,SAAS,CAAC,GAAG,SAAS,UAAU,QAAQ,IAAI,QAAQ,KAAK;AACvE,cACE,UAAU,CAAC,EAAE,OAAO,MACnB,QAAQ,CAAC,UAAU,CAAC,EAAE,QACtB,WAAW,UAAU,CAAC,EAAE,YAAY,SACrC;AACA,mBAAO,KAAK,UAAU,CAAC,CAAC;AAAA,UAC1B;AAAA,QACF;AAKA,YAAI,OAAO;AAAQ,eAAK,QAAQ,GAAG,IAAI,OAAO,WAAW,IAAI,OAAO,CAAC,IAAI;AAAA;AACpE,qBAAW,MAAM,GAAG;AAAA,MAC3B;AAEA,aAAO;AAAA,IACT;AASA,IAAAA,cAAa,UAAU,qBAAqB,SAAS,mBAAmB,OAAO;AAC7E,UAAI;AAEJ,UAAI,OAAO;AACT,cAAM,SAAS,SAAS,QAAQ;AAChC,YAAI,KAAK,QAAQ,GAAG;AAAG,qBAAW,MAAM,GAAG;AAAA,MAC7C,OAAO;AACL,aAAK,UAAU,IAAI,OAAO;AAC1B,aAAK,eAAe;AAAA,MACtB;AAEA,aAAO;AAAA,IACT;AAKA,IAAAA,cAAa,UAAU,MAAMA,cAAa,UAAU;AACpD,IAAAA,cAAa,UAAU,cAAcA,cAAa,UAAU;AAK5D,IAAAA,cAAa,WAAW;AAKxB,IAAAA,cAAa,eAAeA;AAK5B,QAAI,gBAAgB,OAAO,QAAQ;AACjC,aAAO,UAAUA;AAAA,IACnB;AAAA;AAAA;;;AC9UA,2BAAyB;;;ACDnB,IAAO,cAAP,cAA2B,MAAK;;EAIlC,YAAY,SAAkB,OAAW;AACrC,UAAM,OAAO;AACb,SAAK,QAAQ;EACjB;;AAGE,IAAO,sBAAP,cAAmC,YAAW;EAApD,cAAA;;AACI,SAAA,OAAO;EACX;;AAEM,IAAO,kBAAP,cAA+B,YAAW;EAAhD,cAAA;;AACI,SAAA,OAAO;EACX;;AAEM,IAAO,oBAAP,cAAiC,YAAW;EAAlD,cAAA;;AACI,SAAA,OAAO;EACX;;AAEM,IAAO,wBAAP,cAAqC,YAAW;EAAtD,cAAA;;AACI,SAAA,OAAO;EACX;;AAEM,IAAO,0BAAP,cAAuC,YAAW;EAAxD,cAAA;;AACI,SAAA,OAAO;EACX;;AAEM,IAAO,2BAAP,cAAwC,YAAW;EAAzD,cAAA;;AACI,SAAA,OAAO;EACX;;AAEM,IAAO,qBAAP,cAAkC,YAAW;EAAnD,cAAA;;AACI,SAAA,OAAO;EACX;;AAEM,IAAO,uBAAP,cAAoC,YAAW;EAArD,cAAA;;AACI,SAAA,OAAO;EACX;;AAEM,IAAO,qBAAP,cAAkC,YAAW;EAAnD,cAAA;;AACI,SAAA,OAAO;EACX;;AAEM,IAAO,0BAAP,cAAuC,YAAW;EAAxD,cAAA;;AACI,SAAA,OAAO;EACX;;AAEM,IAAO,6BAAP,cAA0C,YAAW;EAA3D,cAAA;;AACI,SAAA,OAAO;EACX;;AAEM,IAAO,6BAAP,cAA0C,YAAW;EAA3D,cAAA;;AACI,SAAA,OAAO;EACX;;AAEM,IAAO,yBAAP,cAAsC,YAAW;EAAvD,cAAA;;AACI,SAAA,OAAO;EACX;;AAEM,IAAO,oBAAP,cAAiC,YAAW;EAAlD,cAAA;;AACI,SAAA,OAAO;EACX;;AAEM,IAAO,qBAAP,cAAkC,YAAW;EAAnD,cAAA;;AACI,SAAA,OAAO;EACX;;AAEM,IAAO,2BAAP,cAAwC,YAAW;EAAzD,cAAA;;AACI,SAAA,OAAO;EACX;;AAEM,IAAO,0BAAP,cAAuC,YAAW;EAAxD,cAAA;;AACI,SAAA,OAAO;EACX;;;;ADvBA,IAAY;CAAZ,SAAYC,mBAAgB;AAMxB,EAAAA,kBAAA,WAAA,IAAA;AACA,EAAAA,kBAAA,aAAA,IAAA;AAKA,EAAAA,kBAAA,UAAA,IAAA;AAKA,EAAAA,kBAAA,aAAA,IAAA;AACJ,GAlBY,qBAAA,mBAAgB,CAAA,EAAA;AAoBtB,IAAgB,oBAAhB,cACM,qBAAAC,QAAiC;EAWzC,IAAI,YAAS;AACT,WAAO,CAAC,CAAC,KAAK;EAClB;EAEA,MAAM,cAAW;AACb,UAAM,KAAK,QAAO;EACtB;EAWU,MAAM,mBACZ,aACA,YACA,UAAuB,CAAA,GAAE;AAEzB,UAAM,YAAY,KAAK;AACvB,QAAI,CAAC;AAAW,YAAM,IAAI,wBAAuB;AAEjD,gBAAY,WAAW,YAAY,YAAY;AAC/C,gBAAY,kBACR,YAAY,oBAER,MAAM,WAAW,mBAAmB;MAChC,YAAY,QAAQ;MACpB,gBAAgB,QAAQ;KAC3B,GACH;AAEN,WAAO;EACX;;AAGE,SAAU,8BAA8B,QAAqB;AAE/D,MAAI,OAAO,WAAW,eAAe,OAAO,aAAa;AAAa;AAEtE,QAAM,YAA4B,CAAA;AAElC,WAAS,mBAAgB;AACrB,UAAM,WAAW,OAAM;AACvB,QAAI,UAAU;AACV,iBAAW,WAAW,WAAW;AAC7B,gBAAO;;;EAGnB;AAGA,QAAM;;IAEF,YAAY,kBAAkB,GAAI;;AACtC,YAAU,KAAK,MAAM,cAAc,QAAQ,CAAC;AAG5C;;IAEI,SAAS,eAAe;IAC1B;AACE,aAAS,iBAAiB,oBAAoB,kBAAkB,EAAE,MAAM,KAAI,CAAE;AAC9E,cAAU,KAAK,MAAM,SAAS,oBAAoB,oBAAoB,gBAAgB,CAAC;;AAI3F;;IAEI,SAAS,eAAe;IAC1B;AACE,WAAO,iBAAiB,QAAQ,kBAAkB,EAAE,MAAM,KAAI,CAAE;AAChE,cAAU,KAAK,MAAM,OAAO,oBAAoB,QAAQ,gBAAgB,CAAC;;AAI7E,mBAAgB;AACpB;AAWM,SAAU,uBAAoB;AAEhC,MAAI,CAAC;AAAW,WAAO;AAEvB,QAAM,YAAY,UAAU,UAAU,YAAW;AAIjD,QAAM,QAAQ,UAAU,SAAS,QAAQ,KAAK,UAAU,SAAS,MAAM;AAKvE,QAAM,WAAW,UAAU,SAAS,QAAQ;AAE5C,SAAO,SAAS;AACpB;;;AExLM,SAAU,uBACZ,aAA+C;AAE/C,SAAO,aAAa;AACxB;;;ACUM,IAAgB,0BAAhB,cACM,kBAAuB;EAG/B,MAAM,gBACF,aACA,YACA,UAAkC,CAAA,GAAE;AAEpC,QAAI,OAAO;AACX,QAAI;AACA,UAAI,uBAAuB,WAAW,GAAG;AACrC,YAAI,CAAC,KAAK;AACN,gBAAM,IAAI,2BACN,+DAA+D;AAGvE,YAAI,CAAC,KAAK,6BAA6B,IAAI,YAAY,OAAO;AAC1D,gBAAM,IAAI,2BACN,+BAA+B,YAAY,OAAO,iCAAiC;AAG3F,YAAI;AACA,wBAAc,MAAM,KAAK,gBAAgB,WAAW;AAEpD,gBAAM,iBAAiB,YAAY,UAAS;AAE5C,iBAAO,MAAM,WAAW,mBAAmB,gBAAgB,OAAO;iBAC7D,OAAY;AAEjB,cAAI,iBAAiB,4BAA4B;AAC7C,mBAAO;AACP,kBAAM;;AAEV,gBAAM,IAAI,2BAA2B,+BAAO,SAAS,KAAK;;aAE3D;AACH,YAAI;AACA,gBAAM,EAAE,SAAS,GAAG,YAAW,IAAK;AAEpC,wBAAc,MAAM,KAAK,mBAAmB,aAAa,YAAY,WAAW;AAEhF,8CAAS,WAAU,YAAY,YAAY,GAAG,OAAO;AAErD,wBAAc,MAAM,KAAK,gBAAgB,WAAW;AAEpD,gBAAM,iBAAiB,YAAY,UAAS;AAE5C,iBAAO,MAAM,WAAW,mBAAmB,gBAAgB,WAAW;iBACjE,OAAY;AAEjB,cAAI,iBAAiB,4BAA4B;AAC7C,mBAAO;AACP,kBAAM;;AAEV,gBAAM,IAAI,2BAA2B,+BAAO,SAAS,KAAK;;;aAG7D,OAAY;AACjB,UAAI,MAAM;AACN,aAAK,KAAK,SAAS,KAAK;;AAE5B,YAAM;;EAEd;EAMA,MAAM,oBACF,cAAiB;AAEjB,eAAW,eAAe,cAAc;AACpC,UAAI,uBAAuB,WAAW,GAAG;AACrC,YAAI,CAAC,KAAK;AACN,gBAAM,IAAI,2BACN,+DAA+D;AAGvE,YAAI,CAAC,KAAK,6BAA6B,IAAI,YAAY,OAAO;AAC1D,gBAAM,IAAI,2BACN,+BAA+B,YAAY,OAAO,iCAAiC;;;AAKnG,UAAM,qBAA0B,CAAA;AAChC,eAAW,eAAe,cAAc;AACpC,yBAAmB,KAAK,MAAM,KAAK,gBAAgB,WAAW,CAAC;;AAEnE,WAAO;EACX;;AAUE,IAAgB,iCAAhB,cACM,wBAA6B;;AAanC,IAAgB,uCAAhB,cACM,+BAAoC;;;;ACxIzC,IAAM,kBAAkB;;;ACFxB,IAAM,qBAAqB;;;ACE3B,IAAM,iBAAiB;;;AC8BxB,SAAU,wCACZ,QAAsB;AAEtB,SACI,mBAAmB,OAAO,YAC1B,kBAAkB,OAAO,aACxB,gCAAgC,OAAO,YAAY,yBAAyB,OAAO;AAE5F;;;AC9BA,IAAY;CAAZ,SAAYC,uBAAoB;AAC5B,EAAAA,sBAAA,SAAA,IAAA;AACA,EAAAA,sBAAA,SAAA,IAAA;AACA,EAAAA,sBAAA,QAAA,IAAA;AACJ,GAJY,yBAAA,uBAAoB,CAAA,EAAA;", "names": ["EventEmitter", "WalletReadyState", "EventEmitter", "WalletAdapterNetwork"]}