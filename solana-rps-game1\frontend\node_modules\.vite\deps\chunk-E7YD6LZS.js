import {
  require_buffer
} from "./chunk-LG344HM7.js";
import {
  __commonJS,
  __esm,
  __publicField,
  __toESM
} from "./chunk-WXXH56N5.js";

// node_modules/rpc-websockets/node_modules/eventemitter3/index.js
var require_eventemitter3 = __commonJS({
  "node_modules/rpc-websockets/node_modules/eventemitter3/index.js"(exports, module) {
    "use strict";
    var has = Object.prototype.hasOwnProperty;
    var prefix = "~";
    function Events() {
    }
    if (Object.create) {
      Events.prototype = /* @__PURE__ */ Object.create(null);
      if (!new Events().__proto__)
        prefix = false;
    }
    function EE(fn, context, once) {
      this.fn = fn;
      this.context = context;
      this.once = once || false;
    }
    function addListener(emitter, event, fn, context, once) {
      if (typeof fn !== "function") {
        throw new TypeError("The listener must be a function");
      }
      var listener = new EE(fn, context || emitter, once), evt = prefix ? prefix + event : event;
      if (!emitter._events[evt])
        emitter._events[evt] = listener, emitter._eventsCount++;
      else if (!emitter._events[evt].fn)
        emitter._events[evt].push(listener);
      else
        emitter._events[evt] = [emitter._events[evt], listener];
      return emitter;
    }
    function clearEvent(emitter, evt) {
      if (--emitter._eventsCount === 0)
        emitter._events = new Events();
      else
        delete emitter._events[evt];
    }
    function EventEmitter2() {
      this._events = new Events();
      this._eventsCount = 0;
    }
    EventEmitter2.prototype.eventNames = function eventNames() {
      var names = [], events, name;
      if (this._eventsCount === 0)
        return names;
      for (name in events = this._events) {
        if (has.call(events, name))
          names.push(prefix ? name.slice(1) : name);
      }
      if (Object.getOwnPropertySymbols) {
        return names.concat(Object.getOwnPropertySymbols(events));
      }
      return names;
    };
    EventEmitter2.prototype.listeners = function listeners(event) {
      var evt = prefix ? prefix + event : event, handlers = this._events[evt];
      if (!handlers)
        return [];
      if (handlers.fn)
        return [handlers.fn];
      for (var i = 0, l = handlers.length, ee = new Array(l); i < l; i++) {
        ee[i] = handlers[i].fn;
      }
      return ee;
    };
    EventEmitter2.prototype.listenerCount = function listenerCount(event) {
      var evt = prefix ? prefix + event : event, listeners = this._events[evt];
      if (!listeners)
        return 0;
      if (listeners.fn)
        return 1;
      return listeners.length;
    };
    EventEmitter2.prototype.emit = function emit(event, a1, a2, a3, a4, a5) {
      var evt = prefix ? prefix + event : event;
      if (!this._events[evt])
        return false;
      var listeners = this._events[evt], len = arguments.length, args, i;
      if (listeners.fn) {
        if (listeners.once)
          this.removeListener(event, listeners.fn, void 0, true);
        switch (len) {
          case 1:
            return listeners.fn.call(listeners.context), true;
          case 2:
            return listeners.fn.call(listeners.context, a1), true;
          case 3:
            return listeners.fn.call(listeners.context, a1, a2), true;
          case 4:
            return listeners.fn.call(listeners.context, a1, a2, a3), true;
          case 5:
            return listeners.fn.call(listeners.context, a1, a2, a3, a4), true;
          case 6:
            return listeners.fn.call(listeners.context, a1, a2, a3, a4, a5), true;
        }
        for (i = 1, args = new Array(len - 1); i < len; i++) {
          args[i - 1] = arguments[i];
        }
        listeners.fn.apply(listeners.context, args);
      } else {
        var length = listeners.length, j;
        for (i = 0; i < length; i++) {
          if (listeners[i].once)
            this.removeListener(event, listeners[i].fn, void 0, true);
          switch (len) {
            case 1:
              listeners[i].fn.call(listeners[i].context);
              break;
            case 2:
              listeners[i].fn.call(listeners[i].context, a1);
              break;
            case 3:
              listeners[i].fn.call(listeners[i].context, a1, a2);
              break;
            case 4:
              listeners[i].fn.call(listeners[i].context, a1, a2, a3);
              break;
            default:
              if (!args)
                for (j = 1, args = new Array(len - 1); j < len; j++) {
                  args[j - 1] = arguments[j];
                }
              listeners[i].fn.apply(listeners[i].context, args);
          }
        }
      }
      return true;
    };
    EventEmitter2.prototype.on = function on(event, fn, context) {
      return addListener(this, event, fn, context, false);
    };
    EventEmitter2.prototype.once = function once(event, fn, context) {
      return addListener(this, event, fn, context, true);
    };
    EventEmitter2.prototype.removeListener = function removeListener(event, fn, context, once) {
      var evt = prefix ? prefix + event : event;
      if (!this._events[evt])
        return this;
      if (!fn) {
        clearEvent(this, evt);
        return this;
      }
      var listeners = this._events[evt];
      if (listeners.fn) {
        if (listeners.fn === fn && (!once || listeners.once) && (!context || listeners.context === context)) {
          clearEvent(this, evt);
        }
      } else {
        for (var i = 0, events = [], length = listeners.length; i < length; i++) {
          if (listeners[i].fn !== fn || once && !listeners[i].once || context && listeners[i].context !== context) {
            events.push(listeners[i]);
          }
        }
        if (events.length)
          this._events[evt] = events.length === 1 ? events[0] : events;
        else
          clearEvent(this, evt);
      }
      return this;
    };
    EventEmitter2.prototype.removeAllListeners = function removeAllListeners(event) {
      var evt;
      if (event) {
        evt = prefix ? prefix + event : event;
        if (this._events[evt])
          clearEvent(this, evt);
      } else {
        this._events = new Events();
        this._eventsCount = 0;
      }
      return this;
    };
    EventEmitter2.prototype.off = EventEmitter2.prototype.removeListener;
    EventEmitter2.prototype.addListener = EventEmitter2.prototype.on;
    EventEmitter2.prefixed = prefix;
    EventEmitter2.EventEmitter = EventEmitter2;
    if ("undefined" !== typeof module) {
      module.exports = EventEmitter2;
    }
  }
});

// node_modules/rpc-websockets/node_modules/eventemitter3/index.mjs
var import_index;
var init_eventemitter3 = __esm({
  "node_modules/rpc-websockets/node_modules/eventemitter3/index.mjs"() {
    import_index = __toESM(require_eventemitter3(), 1);
  }
});

// node_modules/rpc-websockets/dist/index.browser.mjs
function WebSocket(address, options) {
  return new WebSocketBrowserImpl(address, options);
}
var import_buffer, WebSocketBrowserImpl, DefaultDataPack, CommonClient, Client;
var init_index_browser = __esm({
  "node_modules/rpc-websockets/dist/index.browser.mjs"() {
    import_buffer = __toESM(require_buffer(), 1);
    init_eventemitter3();
    WebSocketBrowserImpl = class extends import_index.default {
      /** Instantiate a WebSocket class
      * @constructor
      * @param {String} address - url to a websocket server
      * @param {(Object)} options - websocket options
      * @param {(String|Array)} protocols - a list of protocols
      * @return {WebSocketBrowserImpl} - returns a WebSocket instance
      */
      constructor(address, options, protocols) {
        super();
        __publicField(this, "socket");
        this.socket = new window.WebSocket(address, protocols);
        this.socket.onopen = () => this.emit("open");
        this.socket.onmessage = (event) => this.emit("message", event.data);
        this.socket.onerror = (error) => this.emit("error", error);
        this.socket.onclose = (event) => {
          this.emit("close", event.code, event.reason);
        };
      }
      /**
      * Sends data through a websocket connection
      * @method
      * @param {(String|Object)} data - data to be sent via websocket
      * @param {Object} optionsOrCallback - ws options
      * @param {Function} callback - a callback called once the data is sent
      * @return {Undefined}
      */
      send(data, optionsOrCallback, callback) {
        const cb = callback || optionsOrCallback;
        try {
          this.socket.send(data);
          cb();
        } catch (error) {
          cb(error);
        }
      }
      /**
      * Closes an underlying socket
      * @method
      * @param {Number} code - status code explaining why the connection is being closed
      * @param {String} reason - a description why the connection is closing
      * @return {Undefined}
      * @throws {Error}
      */
      close(code, reason) {
        this.socket.close(code, reason);
      }
      addEventListener(type, listener, options) {
        this.socket.addEventListener(type, listener, options);
      }
    };
    DefaultDataPack = class {
      encode(value) {
        return JSON.stringify(value);
      }
      decode(value) {
        return JSON.parse(value);
      }
    };
    CommonClient = class extends import_index.default {
      /**
      * Instantiate a Client class.
      * @constructor
      * @param {webSocketFactory} webSocketFactory - factory method for WebSocket
      * @param {String} address - url to a websocket server
      * @param {Object} options - ws options object with reconnect parameters
      * @param {Function} generate_request_id - custom generation request Id
      * @param {DataPack} dataPack - data pack contains encoder and decoder
      * @return {CommonClient}
      */
      constructor(webSocketFactory, address = "ws://localhost:8080", {
        autoconnect = true,
        reconnect = true,
        reconnect_interval = 1e3,
        max_reconnects = 5,
        ...rest_options
      } = {}, generate_request_id, dataPack) {
        super();
        __publicField(this, "address");
        __publicField(this, "rpc_id");
        __publicField(this, "queue");
        __publicField(this, "options");
        __publicField(this, "autoconnect");
        __publicField(this, "ready");
        __publicField(this, "reconnect");
        __publicField(this, "reconnect_timer_id");
        __publicField(this, "reconnect_interval");
        __publicField(this, "max_reconnects");
        __publicField(this, "rest_options");
        __publicField(this, "current_reconnects");
        __publicField(this, "generate_request_id");
        __publicField(this, "socket");
        __publicField(this, "webSocketFactory");
        __publicField(this, "dataPack");
        this.webSocketFactory = webSocketFactory;
        this.queue = {};
        this.rpc_id = 0;
        this.address = address;
        this.autoconnect = autoconnect;
        this.ready = false;
        this.reconnect = reconnect;
        this.reconnect_timer_id = void 0;
        this.reconnect_interval = reconnect_interval;
        this.max_reconnects = max_reconnects;
        this.rest_options = rest_options;
        this.current_reconnects = 0;
        this.generate_request_id = generate_request_id || (() => typeof this.rpc_id === "number" ? ++this.rpc_id : Number(this.rpc_id) + 1);
        if (!dataPack)
          this.dataPack = new DefaultDataPack();
        else
          this.dataPack = dataPack;
        if (this.autoconnect)
          this._connect(this.address, {
            autoconnect: this.autoconnect,
            reconnect: this.reconnect,
            reconnect_interval: this.reconnect_interval,
            max_reconnects: this.max_reconnects,
            ...this.rest_options
          });
      }
      /**
      * Connects to a defined server if not connected already.
      * @method
      * @return {Undefined}
      */
      connect() {
        if (this.socket)
          return;
        this._connect(this.address, {
          autoconnect: this.autoconnect,
          reconnect: this.reconnect,
          reconnect_interval: this.reconnect_interval,
          max_reconnects: this.max_reconnects,
          ...this.rest_options
        });
      }
      /**
      * Calls a registered RPC method on server.
      * @method
      * @param {String} method - RPC method name
      * @param {Object|Array} params - optional method parameters
      * @param {Number} timeout - RPC reply timeout value
      * @param {Object} ws_opts - options passed to ws
      * @return {Promise}
      */
      call(method, params, timeout, ws_opts) {
        if (!ws_opts && "object" === typeof timeout) {
          ws_opts = timeout;
          timeout = null;
        }
        return new Promise((resolve, reject) => {
          if (!this.ready)
            return reject(new Error("socket not ready"));
          const rpc_id = this.generate_request_id(method, params);
          const message = {
            jsonrpc: "2.0",
            method,
            params: params || void 0,
            id: rpc_id
          };
          this.socket.send(this.dataPack.encode(message), ws_opts, (error) => {
            if (error)
              return reject(error);
            this.queue[rpc_id] = { promise: [resolve, reject] };
            if (timeout) {
              this.queue[rpc_id].timeout = setTimeout(() => {
                delete this.queue[rpc_id];
                reject(new Error("reply timeout"));
              }, timeout);
            }
          });
        });
      }
      /**
      * Logins with the other side of the connection.
      * @method
      * @param {Object} params - Login credentials object
      * @return {Promise}
      */
      async login(params) {
        const resp = await this.call("rpc.login", params);
        if (!resp)
          throw new Error("authentication failed");
        return resp;
      }
      /**
      * Fetches a list of client's methods registered on server.
      * @method
      * @return {Array}
      */
      async listMethods() {
        return await this.call("__listMethods");
      }
      /**
      * Sends a JSON-RPC 2.0 notification to server.
      * @method
      * @param {String} method - RPC method name
      * @param {Object} params - optional method parameters
      * @return {Promise}
      */
      notify(method, params) {
        return new Promise((resolve, reject) => {
          if (!this.ready)
            return reject(new Error("socket not ready"));
          const message = {
            jsonrpc: "2.0",
            method,
            params
          };
          this.socket.send(this.dataPack.encode(message), (error) => {
            if (error)
              return reject(error);
            resolve();
          });
        });
      }
      /**
      * Subscribes for a defined event.
      * @method
      * @param {String|Array} event - event name
      * @return {Undefined}
      * @throws {Error}
      */
      async subscribe(event) {
        if (typeof event === "string")
          event = [event];
        const result = await this.call("rpc.on", event);
        if (typeof event === "string" && result[event] !== "ok")
          throw new Error(
            "Failed subscribing to an event '" + event + "' with: " + result[event]
          );
        return result;
      }
      /**
      * Unsubscribes from a defined event.
      * @method
      * @param {String|Array} event - event name
      * @return {Undefined}
      * @throws {Error}
      */
      async unsubscribe(event) {
        if (typeof event === "string")
          event = [event];
        const result = await this.call("rpc.off", event);
        if (typeof event === "string" && result[event] !== "ok")
          throw new Error("Failed unsubscribing from an event with: " + result);
        return result;
      }
      /**
      * Closes a WebSocket connection gracefully.
      * @method
      * @param {Number} code - socket close code
      * @param {String} data - optional data to be sent before closing
      * @return {Undefined}
      */
      close(code, data) {
        this.socket.close(code || 1e3, data);
      }
      /**
      * Enable / disable automatic reconnection.
      * @method
      * @param {Boolean} reconnect - enable / disable reconnection
      * @return {Undefined}
      */
      setAutoReconnect(reconnect) {
        this.reconnect = reconnect;
      }
      /**
      * Set the interval between reconnection attempts.
      * @method
      * @param {Number} interval - reconnection interval in milliseconds
      * @return {Undefined}
      */
      setReconnectInterval(interval) {
        this.reconnect_interval = interval;
      }
      /**
      * Set the maximum number of reconnection attempts.
      * @method
      * @param {Number} max_reconnects - maximum reconnection attempts
      * @return {Undefined}
      */
      setMaxReconnects(max_reconnects) {
        this.max_reconnects = max_reconnects;
      }
      /**
      * Connection/Message handler.
      * @method
      * @private
      * @param {String} address - WebSocket API address
      * @param {Object} options - ws options object
      * @return {Undefined}
      */
      _connect(address, options) {
        clearTimeout(this.reconnect_timer_id);
        this.socket = this.webSocketFactory(address, options);
        this.socket.addEventListener("open", () => {
          this.ready = true;
          this.emit("open");
          this.current_reconnects = 0;
        });
        this.socket.addEventListener("message", ({ data: message }) => {
          if (message instanceof ArrayBuffer)
            message = import_buffer.Buffer.from(message).toString();
          try {
            message = this.dataPack.decode(message);
          } catch (error) {
            return;
          }
          if (message.notification && this.listeners(message.notification).length) {
            if (!Object.keys(message.params).length)
              return this.emit(message.notification);
            const args = [message.notification];
            if (message.params.constructor === Object)
              args.push(message.params);
            else
              for (let i = 0; i < message.params.length; i++)
                args.push(message.params[i]);
            return Promise.resolve().then(() => {
              this.emit.apply(this, args);
            });
          }
          if (!this.queue[message.id]) {
            if (message.method) {
              return Promise.resolve().then(() => {
                this.emit(message.method, message == null ? void 0 : message.params);
              });
            }
            return;
          }
          if ("error" in message === "result" in message)
            this.queue[message.id].promise[1](
              new Error(
                'Server response malformed. Response must include either "result" or "error", but not both.'
              )
            );
          if (this.queue[message.id].timeout)
            clearTimeout(this.queue[message.id].timeout);
          if (message.error)
            this.queue[message.id].promise[1](message.error);
          else
            this.queue[message.id].promise[0](message.result);
          delete this.queue[message.id];
        });
        this.socket.addEventListener("error", (error) => this.emit("error", error));
        this.socket.addEventListener("close", ({ code, reason }) => {
          if (this.ready)
            setTimeout(() => this.emit("close", code, reason), 0);
          this.ready = false;
          this.socket = void 0;
          if (code === 1e3)
            return;
          this.current_reconnects++;
          if (this.reconnect && (this.max_reconnects > this.current_reconnects || this.max_reconnects === 0))
            this.reconnect_timer_id = setTimeout(
              () => this._connect(address, options),
              this.reconnect_interval
            );
        });
      }
    };
    Client = class extends CommonClient {
      constructor(address = "ws://localhost:8080", {
        autoconnect = true,
        reconnect = true,
        reconnect_interval = 1e3,
        max_reconnects = 5
      } = {}, generate_request_id) {
        super(
          WebSocket,
          address,
          {
            autoconnect,
            reconnect,
            reconnect_interval,
            max_reconnects
          },
          generate_request_id
        );
      }
    };
  }
});

export {
  WebSocket,
  DefaultDataPack,
  CommonClient,
  Client,
  init_index_browser
};
//# sourceMappingURL=chunk-E7YD6LZS.js.map
