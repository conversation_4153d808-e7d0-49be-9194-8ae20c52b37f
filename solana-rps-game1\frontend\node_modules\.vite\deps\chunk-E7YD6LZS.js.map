{"version": 3, "sources": ["../../rpc-websockets/node_modules/eventemitter3/index.js", "../../rpc-websockets/node_modules/eventemitter3/index.mjs", "../../rpc-websockets/node_modules/esbuild-plugin-polyfill-node/polyfills/buffer.js", "../../rpc-websockets/src/lib/client/websocket.browser.ts", "../../rpc-websockets/src/lib/client.ts", "../../rpc-websockets/src/lib/utils.ts", "../../rpc-websockets/src/index.browser.ts"], "sourcesContent": ["'use strict';\n\nvar has = Object.prototype.hasOwnProperty\n  , prefix = '~';\n\n/**\n * Constructor to create a storage for our `EE` objects.\n * An `Events` instance is a plain object whose properties are event names.\n *\n * @constructor\n * @private\n */\nfunction Events() {}\n\n//\n// We try to not inherit from `Object.prototype`. In some engines creating an\n// instance in this way is faster than calling `Object.create(null)` directly.\n// If `Object.create(null)` is not supported we prefix the event names with a\n// character to make sure that the built-in object properties are not\n// overridden or used as an attack vector.\n//\nif (Object.create) {\n  Events.prototype = Object.create(null);\n\n  //\n  // This hack is needed because the `__proto__` property is still inherited in\n  // some old browsers like Android 4, iPhone 5.1, Opera 11 and Safari 5.\n  //\n  if (!new Events().__proto__) prefix = false;\n}\n\n/**\n * Representation of a single event listener.\n *\n * @param {Function} fn The listener function.\n * @param {*} context The context to invoke the listener with.\n * @param {Boolean} [once=false] Specify if the listener is a one-time listener.\n * @constructor\n * @private\n */\nfunction EE(fn, context, once) {\n  this.fn = fn;\n  this.context = context;\n  this.once = once || false;\n}\n\n/**\n * Add a listener for a given event.\n *\n * @param {EventEmitter} emitter Reference to the `EventEmitter` instance.\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} context The context to invoke the listener with.\n * @param {Boolean} once Specify if the listener is a one-time listener.\n * @returns {EventEmitter}\n * @private\n */\nfunction addListener(emitter, event, fn, context, once) {\n  if (typeof fn !== 'function') {\n    throw new TypeError('The listener must be a function');\n  }\n\n  var listener = new EE(fn, context || emitter, once)\n    , evt = prefix ? prefix + event : event;\n\n  if (!emitter._events[evt]) emitter._events[evt] = listener, emitter._eventsCount++;\n  else if (!emitter._events[evt].fn) emitter._events[evt].push(listener);\n  else emitter._events[evt] = [emitter._events[evt], listener];\n\n  return emitter;\n}\n\n/**\n * Clear event by name.\n *\n * @param {EventEmitter} emitter Reference to the `EventEmitter` instance.\n * @param {(String|Symbol)} evt The Event name.\n * @private\n */\nfunction clearEvent(emitter, evt) {\n  if (--emitter._eventsCount === 0) emitter._events = new Events();\n  else delete emitter._events[evt];\n}\n\n/**\n * Minimal `EventEmitter` interface that is molded against the Node.js\n * `EventEmitter` interface.\n *\n * @constructor\n * @public\n */\nfunction EventEmitter() {\n  this._events = new Events();\n  this._eventsCount = 0;\n}\n\n/**\n * Return an array listing the events for which the emitter has registered\n * listeners.\n *\n * @returns {Array}\n * @public\n */\nEventEmitter.prototype.eventNames = function eventNames() {\n  var names = []\n    , events\n    , name;\n\n  if (this._eventsCount === 0) return names;\n\n  for (name in (events = this._events)) {\n    if (has.call(events, name)) names.push(prefix ? name.slice(1) : name);\n  }\n\n  if (Object.getOwnPropertySymbols) {\n    return names.concat(Object.getOwnPropertySymbols(events));\n  }\n\n  return names;\n};\n\n/**\n * Return the listeners registered for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Array} The registered listeners.\n * @public\n */\nEventEmitter.prototype.listeners = function listeners(event) {\n  var evt = prefix ? prefix + event : event\n    , handlers = this._events[evt];\n\n  if (!handlers) return [];\n  if (handlers.fn) return [handlers.fn];\n\n  for (var i = 0, l = handlers.length, ee = new Array(l); i < l; i++) {\n    ee[i] = handlers[i].fn;\n  }\n\n  return ee;\n};\n\n/**\n * Return the number of listeners listening to a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Number} The number of listeners.\n * @public\n */\nEventEmitter.prototype.listenerCount = function listenerCount(event) {\n  var evt = prefix ? prefix + event : event\n    , listeners = this._events[evt];\n\n  if (!listeners) return 0;\n  if (listeners.fn) return 1;\n  return listeners.length;\n};\n\n/**\n * Calls each of the listeners registered for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Boolean} `true` if the event had listeners, else `false`.\n * @public\n */\nEventEmitter.prototype.emit = function emit(event, a1, a2, a3, a4, a5) {\n  var evt = prefix ? prefix + event : event;\n\n  if (!this._events[evt]) return false;\n\n  var listeners = this._events[evt]\n    , len = arguments.length\n    , args\n    , i;\n\n  if (listeners.fn) {\n    if (listeners.once) this.removeListener(event, listeners.fn, undefined, true);\n\n    switch (len) {\n      case 1: return listeners.fn.call(listeners.context), true;\n      case 2: return listeners.fn.call(listeners.context, a1), true;\n      case 3: return listeners.fn.call(listeners.context, a1, a2), true;\n      case 4: return listeners.fn.call(listeners.context, a1, a2, a3), true;\n      case 5: return listeners.fn.call(listeners.context, a1, a2, a3, a4), true;\n      case 6: return listeners.fn.call(listeners.context, a1, a2, a3, a4, a5), true;\n    }\n\n    for (i = 1, args = new Array(len -1); i < len; i++) {\n      args[i - 1] = arguments[i];\n    }\n\n    listeners.fn.apply(listeners.context, args);\n  } else {\n    var length = listeners.length\n      , j;\n\n    for (i = 0; i < length; i++) {\n      if (listeners[i].once) this.removeListener(event, listeners[i].fn, undefined, true);\n\n      switch (len) {\n        case 1: listeners[i].fn.call(listeners[i].context); break;\n        case 2: listeners[i].fn.call(listeners[i].context, a1); break;\n        case 3: listeners[i].fn.call(listeners[i].context, a1, a2); break;\n        case 4: listeners[i].fn.call(listeners[i].context, a1, a2, a3); break;\n        default:\n          if (!args) for (j = 1, args = new Array(len -1); j < len; j++) {\n            args[j - 1] = arguments[j];\n          }\n\n          listeners[i].fn.apply(listeners[i].context, args);\n      }\n    }\n  }\n\n  return true;\n};\n\n/**\n * Add a listener for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} [context=this] The context to invoke the listener with.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.on = function on(event, fn, context) {\n  return addListener(this, event, fn, context, false);\n};\n\n/**\n * Add a one-time listener for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} [context=this] The context to invoke the listener with.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.once = function once(event, fn, context) {\n  return addListener(this, event, fn, context, true);\n};\n\n/**\n * Remove the listeners of a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn Only remove the listeners that match this function.\n * @param {*} context Only remove the listeners that have this context.\n * @param {Boolean} once Only remove one-time listeners.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.removeListener = function removeListener(event, fn, context, once) {\n  var evt = prefix ? prefix + event : event;\n\n  if (!this._events[evt]) return this;\n  if (!fn) {\n    clearEvent(this, evt);\n    return this;\n  }\n\n  var listeners = this._events[evt];\n\n  if (listeners.fn) {\n    if (\n      listeners.fn === fn &&\n      (!once || listeners.once) &&\n      (!context || listeners.context === context)\n    ) {\n      clearEvent(this, evt);\n    }\n  } else {\n    for (var i = 0, events = [], length = listeners.length; i < length; i++) {\n      if (\n        listeners[i].fn !== fn ||\n        (once && !listeners[i].once) ||\n        (context && listeners[i].context !== context)\n      ) {\n        events.push(listeners[i]);\n      }\n    }\n\n    //\n    // Reset the array, or remove it completely if we have no more listeners.\n    //\n    if (events.length) this._events[evt] = events.length === 1 ? events[0] : events;\n    else clearEvent(this, evt);\n  }\n\n  return this;\n};\n\n/**\n * Remove all listeners, or those of the specified event.\n *\n * @param {(String|Symbol)} [event] The event name.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.removeAllListeners = function removeAllListeners(event) {\n  var evt;\n\n  if (event) {\n    evt = prefix ? prefix + event : event;\n    if (this._events[evt]) clearEvent(this, evt);\n  } else {\n    this._events = new Events();\n    this._eventsCount = 0;\n  }\n\n  return this;\n};\n\n//\n// Alias methods names because people roll like that.\n//\nEventEmitter.prototype.off = EventEmitter.prototype.removeListener;\nEventEmitter.prototype.addListener = EventEmitter.prototype.on;\n\n//\n// Expose the prefix.\n//\nEventEmitter.prefixed = prefix;\n\n//\n// Allow `EventEmitter` to be imported as module namespace.\n//\nEventEmitter.EventEmitter = EventEmitter;\n\n//\n// Expose the module.\n//\nif ('undefined' !== typeof module) {\n  module.exports = EventEmitter;\n}\n", "import EventEmitter from './index.js'\n\nexport { EventEmitter }\nexport default EventEmitter\n", "export { Buffer } from \"buffer\";\n", "/**\n * WebSocket implements a browser-side WebSocket specification.\n * @module Client\n */\n\n\"use strict\"\n\nimport { EventEmitter } from \"eventemitter3\"\n\nimport {\n    BrowserWebSocketType,\n    NodeWebSocketType,\n    IWSClientAdditionalOptions,\n} from \"./client.types.js\"\n\nclass WebSocket<PERSON>rowserImpl extends EventEmitter\n{\n    socket: BrowserWebSocketType\n\n    /** Instantiate a WebSocket class\n   * @constructor\n   * @param {String} address - url to a websocket server\n   * @param {(Object)} options - websocket options\n   * @param {(String|Array)} protocols - a list of protocols\n   * @return {WebSocketBrowserImpl} - returns a WebSocket instance\n   */\n    constructor(address: string, options: {}, protocols?: string | string[])\n    {\n        super()\n\n        this.socket = new window.WebSocket(address, protocols)\n\n        this.socket.onopen = () => this.emit(\"open\")\n        this.socket.onmessage = (event) => this.emit(\"message\", event.data)\n        this.socket.onerror = (error) => this.emit(\"error\", error)\n        this.socket.onclose = (event) =>\n        {\n            this.emit(\"close\", event.code, event.reason)\n        }\n    }\n\n    /**\n   * Sends data through a websocket connection\n   * @method\n   * @param {(String|Object)} data - data to be sent via websocket\n   * @param {Object} optionsOrCallback - ws options\n   * @param {Function} callback - a callback called once the data is sent\n   * @return {Undefined}\n   */\n    send(\n        data: Parameters<BrowserWebSocketType[\"send\"]>[0],\n        optionsOrCallback: (\n      error?: Error\n    ) => void | Parameters<NodeWebSocketType[\"send\"]>[1],\n        callback?: () => void\n    )\n    {\n        const cb = callback || optionsOrCallback\n\n        try\n        {\n            this.socket.send(data)\n            cb()\n        }\n        catch (error)\n        {\n            cb(error)\n        }\n    }\n\n    /**\n   * Closes an underlying socket\n   * @method\n   * @param {Number} code - status code explaining why the connection is being closed\n   * @param {String} reason - a description why the connection is closing\n   * @return {Undefined}\n   * @throws {Error}\n   */\n    close(code?: number, reason?: string)\n    {\n        this.socket.close(code, reason)\n    }\n\n    addEventListener<K extends keyof WebSocketEventMap>(\n        type: K,\n        listener: (ev: WebSocketEventMap[K]) => any,\n        options?: boolean | AddEventListenerOptions\n    ): void\n    {\n        this.socket.addEventListener(type, listener, options)\n    }\n}\n\n/**\n * factory method for common WebSocket instance\n * @method\n * @param {String} address - url to a websocket server\n * @param {(Object)} options - websocket options\n * @return {Undefined}\n */\nexport function WebSocket(\n    address: string,\n    options: IWSClientAdditionalOptions\n)\n{\n    return new WebSocketBrowserImpl(address, options)\n}\n", "/**\n * \"Client\" wraps \"ws\" or a browser-implemented \"WebSocket\" library\n * according to the environment providing JSON RPC 2.0 support on top.\n * @module Client\n */\n\n\"use strict\"\n\nimport NodeWebSocket from \"ws\"\nimport { EventEmitter } from \"eventemitter3\"\nimport {\n    ICommonWebSocket,\n    IWSClientAdditionalOptions,\n    NodeWebSocketType,\n    ICommonWebSocketFactory,\n} from \"./client/client.types.js\"\n\nimport { <PERSON>Pack, DefaultDataPack } from \"./utils.js\"\n\ninterface IQueueElement {\n  promise: [\n    Parameters<ConstructorParameters<typeof Promise>[0]>[0],\n    Parameters<ConstructorParameters<typeof Promise>[0]>[1]\n  ];\n  timeout?: ReturnType<typeof setTimeout>;\n}\n\nexport interface IQueue {\n  [x: number | string]: IQueueElement;\n}\n\nexport interface IWSRequestParams {\n  [x: string]: any;\n  [x: number]: any;\n}\n\nexport class CommonClient extends EventEmitter\n{\n    private address: string\n    private rpc_id: number | string\n    private queue: IQueue\n    private options: IWSClientAdditionalOptions & NodeWebSocket.ClientOptions\n    private autoconnect: boolean\n    private ready: boolean\n    private reconnect: boolean\n    private reconnect_timer_id: NodeJS.Timeout\n    private reconnect_interval: number\n    private max_reconnects: number\n    private rest_options: IWSClientAdditionalOptions &\n    NodeWebSocket.ClientOptions\n    private current_reconnects: number\n    private generate_request_id: (\n    method: string,\n    params: object | Array<any>\n  ) => number | string\n    private socket: ICommonWebSocket\n    private webSocketFactory: ICommonWebSocketFactory\n    private dataPack: DataPack<object, string>\n\n    /**\n   * Instantiate a Client class.\n   * @constructor\n   * @param {webSocketFactory} webSocketFactory - factory method for WebSocket\n   * @param {String} address - url to a websocket server\n   * @param {Object} options - ws options object with reconnect parameters\n   * @param {Function} generate_request_id - custom generation request Id\n   * @param {DataPack} dataPack - data pack contains encoder and decoder\n   * @return {CommonClient}\n   */\n    constructor(\n        webSocketFactory: ICommonWebSocketFactory,\n        address = \"ws://localhost:8080\",\n        {\n            autoconnect = true,\n            reconnect = true,\n            reconnect_interval = 1000,\n            max_reconnects = 5,\n            ...rest_options\n        } = {},\n        generate_request_id?: (\n      method: string,\n      params: object | Array<any>\n    ) => number | string,\n        dataPack?: DataPack<object, string>\n    )\n    {\n        super()\n\n        this.webSocketFactory = webSocketFactory\n\n        this.queue = {}\n        this.rpc_id = 0\n\n        this.address = address\n        this.autoconnect = autoconnect\n        this.ready = false\n        this.reconnect = reconnect\n        this.reconnect_timer_id = undefined\n        this.reconnect_interval = reconnect_interval\n        this.max_reconnects = max_reconnects\n        this.rest_options = rest_options\n        this.current_reconnects = 0\n        this.generate_request_id = generate_request_id || (() => typeof this.rpc_id === \"number\"\n            ? ++this.rpc_id\n            : Number(this.rpc_id) + 1)\n\n        if (!dataPack) this.dataPack = new DefaultDataPack()\n        else this.dataPack = dataPack\n\n        if (this.autoconnect)\n            this._connect(this.address, {\n                autoconnect: this.autoconnect,\n                reconnect: this.reconnect,\n                reconnect_interval: this.reconnect_interval,\n                max_reconnects: this.max_reconnects,\n                ...this.rest_options,\n            })\n    }\n\n    /**\n   * Connects to a defined server if not connected already.\n   * @method\n   * @return {Undefined}\n   */\n    connect()\n    {\n        if (this.socket) return\n\n        this._connect(this.address, {\n            autoconnect: this.autoconnect,\n            reconnect: this.reconnect,\n            reconnect_interval: this.reconnect_interval,\n            max_reconnects: this.max_reconnects,\n            ...this.rest_options,\n        })\n    }\n\n    /**\n   * Calls a registered RPC method on server.\n   * @method\n   * @param {String} method - RPC method name\n   * @param {Object|Array} params - optional method parameters\n   * @param {Number} timeout - RPC reply timeout value\n   * @param {Object} ws_opts - options passed to ws\n   * @return {Promise}\n   */\n    call(\n        method: string,\n        params?: IWSRequestParams,\n        timeout?: number,\n        ws_opts?: Parameters<NodeWebSocketType[\"send\"]>[1]\n    )\n    {\n        if (!ws_opts && \"object\" === typeof timeout)\n        {\n            ws_opts = timeout\n            timeout = null\n        }\n\n        return new Promise((resolve, reject) =>\n        {\n            if (!this.ready) return reject(new Error(\"socket not ready\"))\n\n            const rpc_id = this.generate_request_id(method, params)\n\n            const message = {\n                jsonrpc: \"2.0\",\n                method: method,\n                params: params || undefined,\n                id: rpc_id,\n            }\n\n            this.socket.send(this.dataPack.encode(message), ws_opts, (error) =>\n            {\n                if (error) return reject(error)\n\n                this.queue[rpc_id] = { promise: [resolve, reject] }\n\n                if (timeout)\n                {\n                    this.queue[rpc_id].timeout = setTimeout(() =>\n                    {\n                        delete this.queue[rpc_id]\n                        reject(new Error(\"reply timeout\"))\n                    }, timeout)\n                }\n            })\n        })\n    }\n\n    /**\n   * Logins with the other side of the connection.\n   * @method\n   * @param {Object} params - Login credentials object\n   * @return {Promise}\n   */\n    async login(params: IWSRequestParams)\n    {\n        const resp = await this.call(\"rpc.login\", params)\n\n        if (!resp) throw new Error(\"authentication failed\")\n\n        return resp\n    }\n\n    /**\n   * Fetches a list of client's methods registered on server.\n   * @method\n   * @return {Array}\n   */\n    async listMethods()\n    {\n        return await this.call(\"__listMethods\")\n    }\n\n    /**\n   * Sends a JSON-RPC 2.0 notification to server.\n   * @method\n   * @param {String} method - RPC method name\n   * @param {Object} params - optional method parameters\n   * @return {Promise}\n   */\n    notify(method: string, params?: IWSRequestParams)\n    {\n        return new Promise<void>((resolve, reject) =>\n        {\n            if (!this.ready) return reject(new Error(\"socket not ready\"))\n\n            const message = {\n                jsonrpc: \"2.0\",\n                method: method,\n                params,\n            }\n\n            this.socket.send(this.dataPack.encode(message), (error) =>\n            {\n                if (error) return reject(error)\n\n                resolve()\n            })\n        })\n    }\n\n    /**\n   * Subscribes for a defined event.\n   * @method\n   * @param {String|Array} event - event name\n   * @return {Undefined}\n   * @throws {Error}\n   */\n    async subscribe(event: string | Array<string>)\n    {\n        if (typeof event === \"string\") event = [event]\n\n        const result = await this.call(\"rpc.on\", event)\n\n        if (typeof event === \"string\" && result[event] !== \"ok\")\n            throw new Error(\n                \"Failed subscribing to an event '\" + event + \"' with: \" + result[event]\n            )\n\n        return result\n    }\n\n    /**\n   * Unsubscribes from a defined event.\n   * @method\n   * @param {String|Array} event - event name\n   * @return {Undefined}\n   * @throws {Error}\n   */\n    async unsubscribe(event: string | Array<string>)\n    {\n        if (typeof event === \"string\") event = [event]\n\n        const result = await this.call(\"rpc.off\", event)\n\n        if (typeof event === \"string\" && result[event] !== \"ok\")\n            throw new Error(\"Failed unsubscribing from an event with: \" + result)\n\n        return result\n    }\n\n    /**\n   * Closes a WebSocket connection gracefully.\n   * @method\n   * @param {Number} code - socket close code\n   * @param {String} data - optional data to be sent before closing\n   * @return {Undefined}\n   */\n    close(code?: number, data?: string)\n    {\n        this.socket.close(code || 1000, data)\n    }\n\n    /**\n   * Enable / disable automatic reconnection.\n   * @method\n   * @param {Boolean} reconnect - enable / disable reconnection\n   * @return {Undefined}\n   */\n    setAutoReconnect(reconnect: boolean)\n    {\n        this.reconnect = reconnect\n    }\n\n    /**\n   * Set the interval between reconnection attempts.\n   * @method\n   * @param {Number} interval - reconnection interval in milliseconds\n   * @return {Undefined}\n   */\n    setReconnectInterval(interval: number)\n    {\n        this.reconnect_interval = interval\n    }\n\n    /**\n   * Set the maximum number of reconnection attempts.\n   * @method\n   * @param {Number} max_reconnects - maximum reconnection attempts\n   * @return {Undefined}\n   */\n    setMaxReconnects(max_reconnects: number)\n    {\n        this.max_reconnects = max_reconnects\n    }\n\n    /**\n   * Connection/Message handler.\n   * @method\n   * @private\n   * @param {String} address - WebSocket API address\n   * @param {Object} options - ws options object\n   * @return {Undefined}\n   */\n    private _connect(\n        address: string,\n        options: IWSClientAdditionalOptions & NodeWebSocket.ClientOptions\n    )\n    {\n        clearTimeout(this.reconnect_timer_id)\n        this.socket = this.webSocketFactory(address, options)\n\n        this.socket.addEventListener(\"open\", () =>\n        {\n            this.ready = true\n            this.emit(\"open\")\n            this.current_reconnects = 0\n        })\n\n        this.socket.addEventListener(\"message\", ({ data: message }) =>\n        {\n            if (message instanceof ArrayBuffer)\n                message = Buffer.from(message).toString()\n\n            try\n            {\n                message = this.dataPack.decode(message)\n            }\n            catch (error)\n            {\n                return\n            }\n\n            // check if any listeners are attached and forward event\n            if (message.notification && this.listeners(message.notification).length)\n            {\n                if (!Object.keys(message.params).length)\n                    return this.emit(message.notification)\n\n                const args = [message.notification]\n\n                if (message.params.constructor === Object) args.push(message.params)\n                // using for-loop instead of unshift/spread because performance is better\n                else\n                    for (let i = 0; i < message.params.length; i++)\n                        args.push(message.params[i])\n\n                // run as microtask so that pending queue messages are resolved first\n                // eslint-disable-next-line prefer-spread\n                return Promise.resolve().then(() =>\n                {\n                    // eslint-disable-next-line prefer-spread\n                    this.emit.apply(this, args)\n                })\n            }\n\n            if (!this.queue[message.id])\n            {\n                // general JSON RPC 2.0 events\n                if (message.method)\n                {\n                    // run as microtask so that pending queue messages are resolved first\n                    return Promise.resolve().then(() =>\n                    {\n                        this.emit(message.method, message?.params)\n                    })\n                }\n\n                return\n            }\n\n            // reject early since server's response is invalid\n            if (\"error\" in message === \"result\" in message)\n                this.queue[message.id].promise[1](\n                    new Error(\n                        \"Server response malformed. Response must include either \\\"result\\\"\" +\n              \" or \\\"error\\\", but not both.\"\n                    )\n                )\n\n            if (this.queue[message.id].timeout)\n                clearTimeout(this.queue[message.id].timeout)\n\n            if (message.error) this.queue[message.id].promise[1](message.error)\n            else this.queue[message.id].promise[0](message.result)\n\n            delete this.queue[message.id]\n        })\n\n        this.socket.addEventListener(\"error\", (error) => this.emit(\"error\", error))\n\n        this.socket.addEventListener(\"close\", ({ code, reason }) =>\n        {\n            if (this.ready)\n            // Delay close event until internal state is updated\n                setTimeout(() => this.emit(\"close\", code, reason), 0)\n\n            this.ready = false\n            this.socket = undefined\n\n            if (code === 1000) return\n\n            this.current_reconnects++\n\n            if (\n                this.reconnect &&\n        (this.max_reconnects > this.current_reconnects ||\n          this.max_reconnects === 0)\n            )\n                this.reconnect_timer_id = setTimeout(\n                    () => this._connect(address, options),\n                    this.reconnect_interval\n                )\n        })\n    }\n}\n", "\"use strict\"\n\nexport interface DataPack<\n  T,\n  R extends string | ArrayBufferLike | Blob | ArrayBufferView\n> {\n  encode(value: T): R;\n  decode(value: R): T;\n}\n\nexport class DefaultDataPack implements DataPack<Object, string>\n{\n    encode(value: Object): string\n    {\n        return JSON.stringify(value)\n    }\n\n    decode(value: string): Object\n    {\n        return JSON.parse(value)\n    }\n}\n", "\"use strict\"\n\nimport { WebSocket } from \"./lib/client/websocket.browser.js\"\nimport { CommonClient } from \"./lib/client.js\"\nimport { IWSClientAdditionalOptions } from \"./lib/client/client.types.js\"\n\nexport class Client extends CommonClient\n{\n    constructor(\n        address = \"ws://localhost:8080\",\n        {\n            autoconnect = true,\n            reconnect = true,\n            reconnect_interval = 1000,\n            max_reconnects = 5,\n        }: IWSClientAdditionalOptions = {},\n        generate_request_id?: (\n      method: string,\n      params: object | Array<any>\n    ) => number | string\n    )\n    {\n        super(\n            WebSocket,\n            address,\n            {\n                autoconnect,\n                reconnect,\n                reconnect_interval,\n                max_reconnects,\n            },\n            generate_request_id\n        )\n    }\n}\n\nexport * from \"./lib/client.js\"\nexport * from \"./lib/client/websocket.browser.js\"\nexport * from \"./lib/client/client.types.js\"\nexport * from \"./lib/utils.js\"\n"], "mappings": ";;;;;;;;;;;AAAA;AAAA;AAAA;AAEA,QAAI,MAAM,OAAO,UAAU;AAA3B,QACI,SAAS;AASb,aAAS,SAAS;AAAA,IAAC;AASnB,QAAI,OAAO,QAAQ;AACjB,aAAO,YAAY,uBAAO,OAAO,IAAI;AAMrC,UAAI,CAAC,IAAI,OAAO,EAAE;AAAW,iBAAS;AAAA,IACxC;AAWA,aAAS,GAAG,IAAI,SAAS,MAAM;AAC7B,WAAK,KAAK;AACV,WAAK,UAAU;AACf,WAAK,OAAO,QAAQ;AAAA,IACtB;AAaA,aAAS,YAAY,SAAS,OAAO,IAAI,SAAS,MAAM;AACtD,UAAI,OAAO,OAAO,YAAY;AAC5B,cAAM,IAAI,UAAU,iCAAiC;AAAA,MACvD;AAEA,UAAI,WAAW,IAAI,GAAG,IAAI,WAAW,SAAS,IAAI,GAC9C,MAAM,SAAS,SAAS,QAAQ;AAEpC,UAAI,CAAC,QAAQ,QAAQ,GAAG;AAAG,gBAAQ,QAAQ,GAAG,IAAI,UAAU,QAAQ;AAAA,eAC3D,CAAC,QAAQ,QAAQ,GAAG,EAAE;AAAI,gBAAQ,QAAQ,GAAG,EAAE,KAAK,QAAQ;AAAA;AAChE,gBAAQ,QAAQ,GAAG,IAAI,CAAC,QAAQ,QAAQ,GAAG,GAAG,QAAQ;AAE3D,aAAO;AAAA,IACT;AASA,aAAS,WAAW,SAAS,KAAK;AAChC,UAAI,EAAE,QAAQ,iBAAiB;AAAG,gBAAQ,UAAU,IAAI,OAAO;AAAA;AAC1D,eAAO,QAAQ,QAAQ,GAAG;AAAA,IACjC;AASA,aAASA,gBAAe;AACtB,WAAK,UAAU,IAAI,OAAO;AAC1B,WAAK,eAAe;AAAA,IACtB;AASA,IAAAA,cAAa,UAAU,aAAa,SAAS,aAAa;AACxD,UAAI,QAAQ,CAAC,GACT,QACA;AAEJ,UAAI,KAAK,iBAAiB;AAAG,eAAO;AAEpC,WAAK,QAAS,SAAS,KAAK,SAAU;AACpC,YAAI,IAAI,KAAK,QAAQ,IAAI;AAAG,gBAAM,KAAK,SAAS,KAAK,MAAM,CAAC,IAAI,IAAI;AAAA,MACtE;AAEA,UAAI,OAAO,uBAAuB;AAChC,eAAO,MAAM,OAAO,OAAO,sBAAsB,MAAM,CAAC;AAAA,MAC1D;AAEA,aAAO;AAAA,IACT;AASA,IAAAA,cAAa,UAAU,YAAY,SAAS,UAAU,OAAO;AAC3D,UAAI,MAAM,SAAS,SAAS,QAAQ,OAChC,WAAW,KAAK,QAAQ,GAAG;AAE/B,UAAI,CAAC;AAAU,eAAO,CAAC;AACvB,UAAI,SAAS;AAAI,eAAO,CAAC,SAAS,EAAE;AAEpC,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,KAAK;AAClE,WAAG,CAAC,IAAI,SAAS,CAAC,EAAE;AAAA,MACtB;AAEA,aAAO;AAAA,IACT;AASA,IAAAA,cAAa,UAAU,gBAAgB,SAAS,cAAc,OAAO;AACnE,UAAI,MAAM,SAAS,SAAS,QAAQ,OAChC,YAAY,KAAK,QAAQ,GAAG;AAEhC,UAAI,CAAC;AAAW,eAAO;AACvB,UAAI,UAAU;AAAI,eAAO;AACzB,aAAO,UAAU;AAAA,IACnB;AASA,IAAAA,cAAa,UAAU,OAAO,SAAS,KAAK,OAAO,IAAI,IAAI,IAAI,IAAI,IAAI;AACrE,UAAI,MAAM,SAAS,SAAS,QAAQ;AAEpC,UAAI,CAAC,KAAK,QAAQ,GAAG;AAAG,eAAO;AAE/B,UAAI,YAAY,KAAK,QAAQ,GAAG,GAC5B,MAAM,UAAU,QAChB,MACA;AAEJ,UAAI,UAAU,IAAI;AAChB,YAAI,UAAU;AAAM,eAAK,eAAe,OAAO,UAAU,IAAI,QAAW,IAAI;AAE5E,gBAAQ,KAAK;AAAA,UACX,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,OAAO,GAAG;AAAA,UACrD,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,EAAE,GAAG;AAAA,UACzD,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,EAAE,GAAG;AAAA,UAC7D,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,IAAI,EAAE,GAAG;AAAA,UACjE,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,IAAI,IAAI,EAAE,GAAG;AAAA,UACrE,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG;AAAA,QAC3E;AAEA,aAAK,IAAI,GAAG,OAAO,IAAI,MAAM,MAAK,CAAC,GAAG,IAAI,KAAK,KAAK;AAClD,eAAK,IAAI,CAAC,IAAI,UAAU,CAAC;AAAA,QAC3B;AAEA,kBAAU,GAAG,MAAM,UAAU,SAAS,IAAI;AAAA,MAC5C,OAAO;AACL,YAAI,SAAS,UAAU,QACnB;AAEJ,aAAK,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC3B,cAAI,UAAU,CAAC,EAAE;AAAM,iBAAK,eAAe,OAAO,UAAU,CAAC,EAAE,IAAI,QAAW,IAAI;AAElF,kBAAQ,KAAK;AAAA,YACX,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,OAAO;AAAG;AAAA,YACpD,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,SAAS,EAAE;AAAG;AAAA,YACxD,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,SAAS,IAAI,EAAE;AAAG;AAAA,YAC5D,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,SAAS,IAAI,IAAI,EAAE;AAAG;AAAA,YAChE;AACE,kBAAI,CAAC;AAAM,qBAAK,IAAI,GAAG,OAAO,IAAI,MAAM,MAAK,CAAC,GAAG,IAAI,KAAK,KAAK;AAC7D,uBAAK,IAAI,CAAC,IAAI,UAAU,CAAC;AAAA,gBAC3B;AAEA,wBAAU,CAAC,EAAE,GAAG,MAAM,UAAU,CAAC,EAAE,SAAS,IAAI;AAAA,UACpD;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAWA,IAAAA,cAAa,UAAU,KAAK,SAAS,GAAG,OAAO,IAAI,SAAS;AAC1D,aAAO,YAAY,MAAM,OAAO,IAAI,SAAS,KAAK;AAAA,IACpD;AAWA,IAAAA,cAAa,UAAU,OAAO,SAAS,KAAK,OAAO,IAAI,SAAS;AAC9D,aAAO,YAAY,MAAM,OAAO,IAAI,SAAS,IAAI;AAAA,IACnD;AAYA,IAAAA,cAAa,UAAU,iBAAiB,SAAS,eAAe,OAAO,IAAI,SAAS,MAAM;AACxF,UAAI,MAAM,SAAS,SAAS,QAAQ;AAEpC,UAAI,CAAC,KAAK,QAAQ,GAAG;AAAG,eAAO;AAC/B,UAAI,CAAC,IAAI;AACP,mBAAW,MAAM,GAAG;AACpB,eAAO;AAAA,MACT;AAEA,UAAI,YAAY,KAAK,QAAQ,GAAG;AAEhC,UAAI,UAAU,IAAI;AAChB,YACE,UAAU,OAAO,OAChB,CAAC,QAAQ,UAAU,UACnB,CAAC,WAAW,UAAU,YAAY,UACnC;AACA,qBAAW,MAAM,GAAG;AAAA,QACtB;AAAA,MACF,OAAO;AACL,iBAAS,IAAI,GAAG,SAAS,CAAC,GAAG,SAAS,UAAU,QAAQ,IAAI,QAAQ,KAAK;AACvE,cACE,UAAU,CAAC,EAAE,OAAO,MACnB,QAAQ,CAAC,UAAU,CAAC,EAAE,QACtB,WAAW,UAAU,CAAC,EAAE,YAAY,SACrC;AACA,mBAAO,KAAK,UAAU,CAAC,CAAC;AAAA,UAC1B;AAAA,QACF;AAKA,YAAI,OAAO;AAAQ,eAAK,QAAQ,GAAG,IAAI,OAAO,WAAW,IAAI,OAAO,CAAC,IAAI;AAAA;AACpE,qBAAW,MAAM,GAAG;AAAA,MAC3B;AAEA,aAAO;AAAA,IACT;AASA,IAAAA,cAAa,UAAU,qBAAqB,SAAS,mBAAmB,OAAO;AAC7E,UAAI;AAEJ,UAAI,OAAO;AACT,cAAM,SAAS,SAAS,QAAQ;AAChC,YAAI,KAAK,QAAQ,GAAG;AAAG,qBAAW,MAAM,GAAG;AAAA,MAC7C,OAAO;AACL,aAAK,UAAU,IAAI,OAAO;AAC1B,aAAK,eAAe;AAAA,MACtB;AAEA,aAAO;AAAA,IACT;AAKA,IAAAA,cAAa,UAAU,MAAMA,cAAa,UAAU;AACpD,IAAAA,cAAa,UAAU,cAAcA,cAAa,UAAU;AAK5D,IAAAA,cAAa,WAAW;AAKxB,IAAAA,cAAa,eAAeA;AAK5B,QAAI,gBAAgB,OAAO,QAAQ;AACjC,aAAO,UAAUA;AAAA,IACnB;AAAA;AAAA;;;AC/UA;AAAA;AAAA;AAAA,mBAAyB;AAAA;AAAA;;;AE2FzB,SAAA,UAAA,SAAA,SAAA;AASO,SAAS,IAAA,qBAEZ,SAEJ,OAAA;AACI;mBAlGJ,uCEaI,cD0aA;;;;AF9bJ;ACOA,IAAA,uBAAS,cAAoB,aAAAC,QAAA;;;;;;;;MAUzB,YAAA,SAAA,SAAA,WAAA;AASA,cAAA;AAXJ;AAaQ,aAAA,SAAM,IAAA,OAAA,UAAA,SAAA,SAAA;AAEN,aAAK,OAAA,SAAa,MAAO,KAAA,KAAU,MAAA;AAEnC,aAAK,OAAO,YAAS,CAAA,UAAW,KAAK,KAAM,WAAA,MAAA,IAAA;AAC3C,aAAK,OAAO,UAAA,CAAA,UAAa,KAAU,KAAK,SAAK,KAAW;AACxD,aAAK,OAAO,UAAU,CAAC,UAAU;AACjC,eAAK,KAAO,SAAA,MAAW,MACvB,MAAA,MAAA;QACI;MAA2C;;;;;;;;;MAEnD,KAAA,MAAA,mBAAA,UAAA;AAUA,cACI,KACA,YAAA;AAMA,YAAA;AAEA,eACA,OAAA,KAAA,IAAA;AACI,aAAA;QACA,SAAG,OAAA;AACP,aAAA,KACO;QAEH;MAAQ;;;;;;;;;MAEhB,MAAA,MAAA,QAAA;AAUA,aAAM,OAAe,MACrB,MAAA,MAAA;MACI;MACJ,iBAAA,MAAA,UAAA,SAAA;AAEA,aAAA,OAAA,iBAEI,MACA,UAEJ,OAAA;MACI;IAAoD;;MChF5D,OAAS,OAAA;;;MCCF,OAAM,OAAA;AAET,eAAO,KACP,MAAA,KAAA;MACI;IAA2B;AAKJ,IAC3B,eAAA,cAAA,aAAAA,QAAA;;;;;;;;;;;MDqCQ,YAAA,kBAAA,UAAA,uBAAA;QAAA,cAAA;QAAA,YAAA;QAAA,qBAAA;QAYR,iBACI;QAGI,GAAA;MAAc,IACd,CAAA,GAAA,qBAAY,UAAA;AACZ,cAAA;ACtDZ;;;ADeO;AAEK;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AA0BA,aAAA,mBAAiB;AACjB,aAAG,QAAA,CAAA;AACP,aAAK,SACL;AAOA,aAAA,UAAM;AAEN,aAAK,cAAA;AAEL,aAAK,QAAQ;AACb,aAAK,YAAS;AAEd,aAAK,qBAAU;AACf,aAAK,qBAAc;AACnB,aAAK,iBAAQ;AACb,aAAK,eAAY;AACjB,aAAK,qBAAqB;AAC1B,aAAK,sBAAqB,wBAAA,MAAA,OAAA,KAAA,WAAA,WAAA,EAAA,KAAA,SAAA,OAAA,KAAA,MAAA,IAAA;AAC1B,YAAA,CAAK;AAAA,eAAA,WAAiB,IAAA,gBAAA;;AACjB,eAAA,WAAe;AACpB,YAAA,KAAK;AACL,eAAK,SAAA,KAAA,SAAsB;YAIvB,aAAW,KAAK;YACf,WAAK,KAAA;YAEN,oBAAK,KAAA;YACL,gBAAc,KAAK;YACf,GAAA,KAAA;UAAkB,CAAA;MACF;;;;;;MAK5B,UAAA;AAAA,YAAA,KAAA;AAAA;AAAA,aAAA,SAAA,KAAA,SAAA;UAAA,aAAA,KAAA;UAOA,WACA,KAAA;UACI,oBAAiB,KAAA;UAEjB,gBAAc,KAAK;UACf,GAAA,KAAA;QAAkB,CAAA;MACF;;;;;;;;;;MAKxB,KAAA,QAAA,QAAA,SAAA,SAAA;AAAA,YAAA,CAAA,WAAA,aAAA,OAAA,SAAA;AAAA,oBAAA;AAAA,oBAAA;QAWA;AAOI,eAAK,IAAA,QAAW,CAAA,SAAa,WAAO;AAEhC,cAAA,CAAA,KAAU;AAAA,mBAAA,OAAA,IAAA,MAAA,kBAAA,CAAA;AACV,gBAAA,SAAU,KAAA,oBAAA,QAAA,MAAA;AACd,gBAAA,UAAA;YAEA,SAAW;YAEP;YAEA,QAAM,UAAc;YAEpB,IAAM;UAAU;AACH,eACT,OAAA,KAAA,KAAA,SAAA,OAAA,OAAA,GAAA,SAAA,CAAA,UAAA;AACA,gBAAA;AAAQ,qBAAU,OAAA,KAAA;AAClB,iBAAI,MAAA,MAAA,IAAA,EAAA,SAAA,CAAA,SAAA,MAAA,EAAA;AACR,gBAAA,SAAA;AAEA,mBAAK,MAAO,MAAK,EAAK,UAAS,WAAO,MAAU;AAExC,uBAAO,KAAA,MAAO,MAAO;AAEzB,uBAAW,IAAA,MAAU,eAAY,CAAA;cAEjC,GAAI,OAAA;YAEA;UAEI,CAAA;QACA,CAAA;MAAiC;;;;;;;MAKrD,MAAA,MAAA,QAAA;AAAA,cAAA,OAAA,MAAA,KAAA,KAAA,aAAA,MAAA;AAAA,YAAA,CAAA;AAAA,gBAAA,IAAA,MAAA,uBAAA;AAAA,eAAA;MAQA;;;;;;MAOA,MAAA,cAAA;AAAA,eAAA,MAAA,KAAA,KAAA,eAAA;MAAA;;;;;;;;MAUA,OAAA,QAAA,QAAA;AAAA,eAAA,IAAA,QAAA,CAAA,SAAA,WAAA;AAAA,cAAA,CAAA,KAAA;AAAA,mBAAA,OAAA,IAAA,MAAA,kBAAA,CAAA;AAAA,gBAAA,UAAA;YASA,SAAuB;YAEnB;YAEI;UAEA;AAAgB,eACZ,OAAS,KAAA,KAAA,SAAA,OAAA,OAAA,GAAA,CAAA,UAAA;AACT,gBAAA;AAAA,qBAAA,OAAA,KAAA;AACA,oBAAA;UACJ,CAAA;QAEA,CAAA;MAEI;;;;;;;;MAKZ,MAAA,UAAA,OAAA;AAAA,YAAA,OAAA,UAAA;AAAA,kBAAA,CAAA,KAAA;AAAA,cAAA,SAAA,MAAA,KAAA,KAAA,UAAA,KAAA;AAAA,YAAA,OAAA,UAAA,YAAA,OAAA,KAAA,MAAA;AASA,gBAAM,IAAA;YAEE,qCAAoC,QAAK,aAAA,OAAA,KAAA;UAE7C;AAEA,eAAI;MACA;;;;;;;;MAKR,MAAA,YAAA,OAAA;AAAA,YAAA,OAAA,UAAA;AAAA,kBAAA,CAAA,KAAA;AAAA,cAAA,SAAA,MAAA,KAAA,KAAA,WAAA,KAAA;AAAA,YAAA,OAAA,UAAA,YAAA,OAAA,KAAA,MAAA;AASA,gBAAM,IAAA,MAAY,8CAClB,MAAA;AACI,eAAI;MAEJ;;;;;;;;MAMJ,MAAA,MAAA,MAAA;AAAA,aAAA,OAAA,MAAA,QAAA,KAAA,IAAA;MAAA;;;;;;;MAYA,iBAAA,WAAA;AAAA,aAAA,YAAA;MAAA;;;;;;;MAWA,qBAAA,UAAA;AAAA,aAAA,qBAAA;MAAA;;;;;;;MAWA,iBAAA,gBAAA;AAAA,aAAA,iBAAA;MAAA;;;;;;;;;MAWA,SAAA,SAAA,SAAA;AAAA,qBAAA,KAAA,kBAAA;AAAA,aAAA,SAAA,KAAA,iBAAA,SAAA,OAAA;AAAA,aAAA,OAAA,iBAAA,QAAA,MAAA;AAUQ,eACJ,QAAA;AAIA,eAAA,KAAA,MAAkB;AAClB,eAAK,qBAAc;QAEnB,CAAA;AAEI,aAAA,OAAK,iBAAQ,WAAA,CAAA,EAAA,MAAA,QAAA,MAAA;AACb,cAAA,mBAAgB;AAChB,sBAAK,qBAAA,KAAA,OAAqB,EAAA,SAAA;AAC7B,cAAA;AAED,sBAAY,KAAA,SAAA,OAAiB,OAAY;UAErC,SAAI,OAAA;AACA;UAEJ;AAEI,cAAA,QAAU,gBAAc,KAAO,UAAO,QAAA,YAAA,EAAA,QAAA;AAC1C,gBAAA,CAAA,OACO,KACP,QAAA,MAAA,EAAA;AACI,qBAAA,KAAA,KAAA,QAAA,YAAA;AACJ,kBAAA,OAAA,CAAA,QAAA,YAAA;AAGA,gBAAI,QAAQ,OAAA,gBAAqB;AAAU,mBAAA,KAAQ,QAAY,MAAE;;AAGzD,uBAAO,IAAK,GAAA,IAAK,QAAQ,OAAA,QAAY;AAEzC,qBAAM,KAAQ,QAAQ,OAAA,CAAA,CAAA;AAEtB,mBAAI,QAAQ,QAAO,EAAA,KAAA,MAAgB;AAAgC,mBAAA,KAAA,MAAA,MAAA,IAAA;YAG/D,CAAA;UACI;AAIR,cAAA,CAAA,KAAO,MAAA,QAAQ,EAAQ,GAAE;AAGrB,gBAAA,QAAU,QAAM;AACnB,qBAAA,QAAA,QAAA,EAAA,KAAA,MAAA;AACL,qBAAA,KAAA,QAAA,QAAA,mCAAA,MAAA;cAEI,CAAC;YAGD;AAGI;UAEI;AAAyC,cAC7C,WAAC,YAAA,YAAA;AACL,iBAAA,MAAA,QAAA,EAAA,EAAA,QAAA,CAAA;cAEA,IAAA;gBACJ;cAGI;YACA;AAAgC,cAC5B,KAAI,MAAA,QAAA,EAAA,EAAA;AAAA,yBACA,KAAA,MAAA,QAAA,EAAA,EAAA,OAAA;AAAA,cAEJ,QAAA;AAAA,iBAAA,MAAA,QAAA,EAAA,EAAA,QAAA,CAAA,EAAA,QAAA,KAAA;;AACJ,iBAAA,MAAA,QAAA,EAAA,EAAA,QAAA,CAAA,EAAA,QAAA,MAAA;AAEJ,iBAAI,KAAK,MAAM,QAAU,EAAE;QACvB,CAAA;AAEJ,aAAA,OAAI,iBAAoB,SAAM,CAAA,UAAY,KAAS,KAAE,SAAa,KAAA,CAAA;AAAA,aAAA,OACxD,iBAAgB,SAAU,CAAC,EAAE,MAAA,OAAQ,MAAM;AAErD,cAAA,KAAO;AACV,uBAAA,MAAA,KAAA,KAAA,SAAA,MAAA,MAAA,GAAA,CAAA;AAED,eAAK,QAAO;AAEZ,eAAK,SAAO;AAER,cAAI,SAAK;AAAA;AAEL,eAAA;AAEJ,cAAA,KAAK,cAAQ,KAAA,iBAAA,KAAA,sBAAA,KAAA,mBAAA;AACb,iBAAK,qBAAS;cAEV,MAAA,KAAS,SAAM,SAAA,OAAA;cAEnB,KAAK;YAEL;QAKI,CAAA;MAA0B;IACc;AAG/C,IACL,SAAA,cAAA,aAAA;MACJ,YAAA,UAAA,uBAAA;;;QEzba,qBAAN;QAEH,iBACI;MACA,IACI,CAAA,GAAA,qBAAc;AACd;UACA;UACA;UAC4B;YAOhC;YACI;YACA;YACA;UAAA;UACI;QACA;MACA;IACA;;;", "names": ["EventEmitter", "EventEmitter"]}