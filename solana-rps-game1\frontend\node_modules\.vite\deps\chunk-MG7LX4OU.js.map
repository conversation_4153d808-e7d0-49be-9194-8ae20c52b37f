{"version": 3, "sources": ["../../base-x/src/index.js", "../../bs58/index.js"], "sourcesContent": ["'use strict'\n// base-x encoding / decoding\n// Copyright (c) 2018 base-x contributors\n// Copyright (c) 2014-2018 The Bitcoin Core developers (base58.cpp)\n// Distributed under the MIT software license, see the accompanying\n// file LICENSE or http://www.opensource.org/licenses/mit-license.php.\nfunction base (ALPHABET) {\n  if (ALPHABET.length >= 255) { throw new TypeError('Alphabet too long') }\n  var BASE_MAP = new Uint8Array(256)\n  for (var j = 0; j < BASE_MAP.length; j++) {\n    BASE_MAP[j] = 255\n  }\n  for (var i = 0; i < ALPHABET.length; i++) {\n    var x = ALPHABET.charAt(i)\n    var xc = x.charCodeAt(0)\n    if (BASE_MAP[xc] !== 255) { throw new TypeError(x + ' is ambiguous') }\n    BASE_MAP[xc] = i\n  }\n  var BASE = ALPHABET.length\n  var LEADER = ALPHABET.charAt(0)\n  var FACTOR = Math.log(BASE) / Math.log(256) // log(BASE) / log(256), rounded up\n  var iFACTOR = Math.log(256) / Math.log(BASE) // log(256) / log(BASE), rounded up\n  function encode (source) {\n    if (source instanceof Uint8Array) {\n    } else if (ArrayBuffer.isView(source)) {\n      source = new Uint8Array(source.buffer, source.byteOffset, source.byteLength)\n    } else if (Array.isArray(source)) {\n      source = Uint8Array.from(source)\n    }\n    if (!(source instanceof Uint8Array)) { throw new TypeError('Expected Uint8Array') }\n    if (source.length === 0) { return '' }\n        // Skip & count leading zeroes.\n    var zeroes = 0\n    var length = 0\n    var pbegin = 0\n    var pend = source.length\n    while (pbegin !== pend && source[pbegin] === 0) {\n      pbegin++\n      zeroes++\n    }\n        // Allocate enough space in big-endian base58 representation.\n    var size = ((pend - pbegin) * iFACTOR + 1) >>> 0\n    var b58 = new Uint8Array(size)\n        // Process the bytes.\n    while (pbegin !== pend) {\n      var carry = source[pbegin]\n            // Apply \"b58 = b58 * 256 + ch\".\n      var i = 0\n      for (var it1 = size - 1; (carry !== 0 || i < length) && (it1 !== -1); it1--, i++) {\n        carry += (256 * b58[it1]) >>> 0\n        b58[it1] = (carry % BASE) >>> 0\n        carry = (carry / BASE) >>> 0\n      }\n      if (carry !== 0) { throw new Error('Non-zero carry') }\n      length = i\n      pbegin++\n    }\n        // Skip leading zeroes in base58 result.\n    var it2 = size - length\n    while (it2 !== size && b58[it2] === 0) {\n      it2++\n    }\n        // Translate the result into a string.\n    var str = LEADER.repeat(zeroes)\n    for (; it2 < size; ++it2) { str += ALPHABET.charAt(b58[it2]) }\n    return str\n  }\n  function decodeUnsafe (source) {\n    if (typeof source !== 'string') { throw new TypeError('Expected String') }\n    if (source.length === 0) { return new Uint8Array() }\n    var psz = 0\n        // Skip and count leading '1's.\n    var zeroes = 0\n    var length = 0\n    while (source[psz] === LEADER) {\n      zeroes++\n      psz++\n    }\n        // Allocate enough space in big-endian base256 representation.\n    var size = (((source.length - psz) * FACTOR) + 1) >>> 0 // log(58) / log(256), rounded up.\n    var b256 = new Uint8Array(size)\n        // Process the characters.\n    while (source[psz]) {\n            // Find code of next character\n      var charCode = source.charCodeAt(psz)\n            // Base map can not be indexed using char code\n      if (charCode > 255) { return }\n            // Decode character\n      var carry = BASE_MAP[charCode]\n            // Invalid character\n      if (carry === 255) { return }\n      var i = 0\n      for (var it3 = size - 1; (carry !== 0 || i < length) && (it3 !== -1); it3--, i++) {\n        carry += (BASE * b256[it3]) >>> 0\n        b256[it3] = (carry % 256) >>> 0\n        carry = (carry / 256) >>> 0\n      }\n      if (carry !== 0) { throw new Error('Non-zero carry') }\n      length = i\n      psz++\n    }\n        // Skip leading zeroes in b256.\n    var it4 = size - length\n    while (it4 !== size && b256[it4] === 0) {\n      it4++\n    }\n    var vch = new Uint8Array(zeroes + (size - it4))\n    var j = zeroes\n    while (it4 !== size) {\n      vch[j++] = b256[it4++]\n    }\n    return vch\n  }\n  function decode (string) {\n    var buffer = decodeUnsafe(string)\n    if (buffer) { return buffer }\n    throw new Error('Non-base' + BASE + ' character')\n  }\n  return {\n    encode: encode,\n    decodeUnsafe: decodeUnsafe,\n    decode: decode\n  }\n}\nmodule.exports = base\n", "const basex = require('base-x')\nconst ALPHABET = '123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz'\n\nmodule.exports = basex(ALPHABET)\n"], "mappings": ";;;;;AAAA;AAAA;AAAA;AAMA,aAAS,KAAM,UAAU;AACvB,UAAI,SAAS,UAAU,KAAK;AAAE,cAAM,IAAI,UAAU,mBAAmB;AAAA,MAAE;AACvE,UAAI,WAAW,IAAI,WAAW,GAAG;AACjC,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,iBAAS,CAAC,IAAI;AAAA,MAChB;AACA,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,YAAI,IAAI,SAAS,OAAO,CAAC;AACzB,YAAI,KAAK,EAAE,WAAW,CAAC;AACvB,YAAI,SAAS,EAAE,MAAM,KAAK;AAAE,gBAAM,IAAI,UAAU,IAAI,eAAe;AAAA,QAAE;AACrE,iBAAS,EAAE,IAAI;AAAA,MACjB;AACA,UAAI,OAAO,SAAS;AACpB,UAAI,SAAS,SAAS,OAAO,CAAC;AAC9B,UAAI,SAAS,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,GAAG;AAC1C,UAAI,UAAU,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,IAAI;AAC3C,eAAS,OAAQ,QAAQ;AACvB,YAAI,kBAAkB,YAAY;AAAA,QAClC,WAAW,YAAY,OAAO,MAAM,GAAG;AACrC,mBAAS,IAAI,WAAW,OAAO,QAAQ,OAAO,YAAY,OAAO,UAAU;AAAA,QAC7E,WAAW,MAAM,QAAQ,MAAM,GAAG;AAChC,mBAAS,WAAW,KAAK,MAAM;AAAA,QACjC;AACA,YAAI,EAAE,kBAAkB,aAAa;AAAE,gBAAM,IAAI,UAAU,qBAAqB;AAAA,QAAE;AAClF,YAAI,OAAO,WAAW,GAAG;AAAE,iBAAO;AAAA,QAAG;AAErC,YAAI,SAAS;AACb,YAAI,SAAS;AACb,YAAI,SAAS;AACb,YAAI,OAAO,OAAO;AAClB,eAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,GAAG;AAC9C;AACA;AAAA,QACF;AAEA,YAAI,QAAS,OAAO,UAAU,UAAU,MAAO;AAC/C,YAAI,MAAM,IAAI,WAAW,IAAI;AAE7B,eAAO,WAAW,MAAM;AACtB,cAAI,QAAQ,OAAO,MAAM;AAEzB,cAAIA,KAAI;AACR,mBAAS,MAAM,OAAO,IAAI,UAAU,KAAKA,KAAI,WAAY,QAAQ,IAAK,OAAOA,MAAK;AAChF,qBAAU,MAAM,IAAI,GAAG,MAAO;AAC9B,gBAAI,GAAG,IAAK,QAAQ,SAAU;AAC9B,oBAAS,QAAQ,SAAU;AAAA,UAC7B;AACA,cAAI,UAAU,GAAG;AAAE,kBAAM,IAAI,MAAM,gBAAgB;AAAA,UAAE;AACrD,mBAASA;AACT;AAAA,QACF;AAEA,YAAI,MAAM,OAAO;AACjB,eAAO,QAAQ,QAAQ,IAAI,GAAG,MAAM,GAAG;AACrC;AAAA,QACF;AAEA,YAAI,MAAM,OAAO,OAAO,MAAM;AAC9B,eAAO,MAAM,MAAM,EAAE,KAAK;AAAE,iBAAO,SAAS,OAAO,IAAI,GAAG,CAAC;AAAA,QAAE;AAC7D,eAAO;AAAA,MACT;AACA,eAAS,aAAc,QAAQ;AAC7B,YAAI,OAAO,WAAW,UAAU;AAAE,gBAAM,IAAI,UAAU,iBAAiB;AAAA,QAAE;AACzE,YAAI,OAAO,WAAW,GAAG;AAAE,iBAAO,IAAI,WAAW;AAAA,QAAE;AACnD,YAAI,MAAM;AAEV,YAAI,SAAS;AACb,YAAI,SAAS;AACb,eAAO,OAAO,GAAG,MAAM,QAAQ;AAC7B;AACA;AAAA,QACF;AAEA,YAAI,QAAU,OAAO,SAAS,OAAO,SAAU,MAAO;AACtD,YAAI,OAAO,IAAI,WAAW,IAAI;AAE9B,eAAO,OAAO,GAAG,GAAG;AAElB,cAAI,WAAW,OAAO,WAAW,GAAG;AAEpC,cAAI,WAAW,KAAK;AAAE;AAAA,UAAO;AAE7B,cAAI,QAAQ,SAAS,QAAQ;AAE7B,cAAI,UAAU,KAAK;AAAE;AAAA,UAAO;AAC5B,cAAIA,KAAI;AACR,mBAAS,MAAM,OAAO,IAAI,UAAU,KAAKA,KAAI,WAAY,QAAQ,IAAK,OAAOA,MAAK;AAChF,qBAAU,OAAO,KAAK,GAAG,MAAO;AAChC,iBAAK,GAAG,IAAK,QAAQ,QAAS;AAC9B,oBAAS,QAAQ,QAAS;AAAA,UAC5B;AACA,cAAI,UAAU,GAAG;AAAE,kBAAM,IAAI,MAAM,gBAAgB;AAAA,UAAE;AACrD,mBAASA;AACT;AAAA,QACF;AAEA,YAAI,MAAM,OAAO;AACjB,eAAO,QAAQ,QAAQ,KAAK,GAAG,MAAM,GAAG;AACtC;AAAA,QACF;AACA,YAAI,MAAM,IAAI,WAAW,UAAU,OAAO,IAAI;AAC9C,YAAIC,KAAI;AACR,eAAO,QAAQ,MAAM;AACnB,cAAIA,IAAG,IAAI,KAAK,KAAK;AAAA,QACvB;AACA,eAAO;AAAA,MACT;AACA,eAAS,OAAQ,QAAQ;AACvB,YAAI,SAAS,aAAa,MAAM;AAChC,YAAI,QAAQ;AAAE,iBAAO;AAAA,QAAO;AAC5B,cAAM,IAAI,MAAM,aAAa,OAAO,YAAY;AAAA,MAClD;AACA,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,WAAO,UAAU;AAAA;AAAA;;;AC5HjB;AAAA;AAAA,QAAM,QAAQ;AACd,QAAM,WAAW;AAEjB,WAAO,UAAU,MAAM,QAAQ;AAAA;AAAA;", "names": ["i", "j"]}