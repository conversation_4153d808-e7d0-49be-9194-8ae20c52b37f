{"version": 3, "sources": ["../../@wallet-standard/wallet/src/register.ts", "../../@wallet-standard/wallet/src/util.ts", "../../@solana/wallet-standard-chains/src/index.ts"], "sourcesContent": ["import type {\n    DEPRECATED_WalletsWindow,\n    Wallet,\n    WalletEventsWindow,\n    WindowRegisterWalletEvent,\n    WindowRegisterWalletEventCallback,\n} from '@wallet-standard/base';\n\n/**\n * Register a {@link \"@wallet-standard/base\".Wallet} as a Standard Wallet with the app.\n *\n * This dispatches a {@link \"@wallet-standard/base\".WindowRegisterWalletEvent} to notify the app that the Wallet is\n * ready to be registered.\n *\n * This also adds a listener for {@link \"@wallet-standard/base\".WindowAppReadyEvent} to listen for a notification from\n * the app that the app is ready to register the Wallet.\n *\n * This combination of event dispatch and listener guarantees that the Wallet will be registered synchronously as soon\n * as the app is ready whether the Wallet loads before or after the app.\n *\n * @param wallet Wallet to register.\n *\n * @group Wallet\n */\nexport function registerWallet(wallet: Wallet): void {\n    const callback: WindowRegisterWalletEventCallback = ({ register }) => register(wallet);\n    try {\n        (window as WalletEventsWindow).dispatchEvent(new RegisterWalletEvent(callback));\n    } catch (error) {\n        console.error('wallet-standard:register-wallet event could not be dispatched\\n', error);\n    }\n    try {\n        (window as WalletEventsWindow).addEventListener('wallet-standard:app-ready', ({ detail: api }) =>\n            callback(api)\n        );\n    } catch (error) {\n        console.error('wallet-standard:app-ready event listener could not be added\\n', error);\n    }\n}\n\nclass RegisterWalletEvent extends Event implements WindowRegisterWalletEvent {\n    readonly #detail: WindowRegisterWalletEventCallback;\n\n    get detail() {\n        return this.#detail;\n    }\n\n    get type() {\n        return 'wallet-standard:register-wallet' as const;\n    }\n\n    constructor(callback: WindowRegisterWalletEventCallback) {\n        super('wallet-standard:register-wallet', {\n            bubbles: false,\n            cancelable: false,\n            composed: false,\n        });\n        this.#detail = callback;\n    }\n\n    /** @deprecated */\n    preventDefault(): never {\n        throw new Error('preventDefault cannot be called');\n    }\n\n    /** @deprecated */\n    stopImmediatePropagation(): never {\n        throw new Error('stopImmediatePropagation cannot be called');\n    }\n\n    /** @deprecated */\n    stopPropagation(): never {\n        throw new Error('stopPropagation cannot be called');\n    }\n}\n\n/**\n * @deprecated Use {@link registerWallet} instead.\n *\n * @group Deprecated\n */\nexport function DEPRECATED_registerWallet(wallet: Wallet): void {\n    registerWallet(wallet);\n    try {\n        ((window as DEPRECATED_WalletsWindow).navigator.wallets ||= []).push(({ register }) => register(wallet));\n    } catch (error) {\n        console.error('window.navigator.wallets could not be pushed\\n', error);\n    }\n}\n", "import type { ReadonlyUint8Array, WalletAccount } from '@wallet-standard/base';\n\n/**\n * Base implementation of a {@link \"@wallet-standard/base\".WalletAccount} to be used or extended by a\n * {@link \"@wallet-standard/base\".Wallet}.\n *\n * `WalletAccount` properties must be read-only. This class enforces this by making all properties\n * [truly private](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Classes/Private_class_fields) and\n * read-only, using getters for access, returning copies instead of references, and calling\n * [Object.freeze](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/freeze)\n * on the instance.\n *\n * @group Account\n */\nexport class ReadonlyWalletAccount implements WalletAccount {\n    readonly #address: WalletAccount['address'];\n    readonly #publicKey: WalletAccount['publicKey'];\n    readonly #chains: WalletAccount['chains'];\n    readonly #features: WalletAccount['features'];\n    readonly #label: WalletAccount['label'];\n    readonly #icon: WalletAccount['icon'];\n\n    /** Implementation of {@link \"@wallet-standard/base\".WalletAccount.address | WalletAccount::address} */\n    get address() {\n        return this.#address;\n    }\n\n    /** Implementation of {@link \"@wallet-standard/base\".WalletAccount.publicKey | WalletAccount::publicKey} */\n    get publicKey() {\n        return this.#publicKey.slice();\n    }\n\n    /** Implementation of {@link \"@wallet-standard/base\".WalletAccount.chains | WalletAccount::chains} */\n    get chains() {\n        return this.#chains.slice();\n    }\n\n    /** Implementation of {@link \"@wallet-standard/base\".WalletAccount.features | WalletAccount::features} */\n    get features() {\n        return this.#features.slice();\n    }\n\n    /** Implementation of {@link \"@wallet-standard/base\".WalletAccount.label | WalletAccount::label} */\n    get label() {\n        return this.#label;\n    }\n\n    /** Implementation of {@link \"@wallet-standard/base\".WalletAccount.icon | WalletAccount::icon} */\n    get icon() {\n        return this.#icon;\n    }\n\n    /**\n     * Create and freeze a read-only account.\n     *\n     * @param account Account to copy properties from.\n     */\n    constructor(account: WalletAccount) {\n        if (new.target === ReadonlyWalletAccount) {\n            Object.freeze(this);\n        }\n\n        this.#address = account.address;\n        this.#publicKey = account.publicKey.slice();\n        this.#chains = account.chains.slice();\n        this.#features = account.features.slice();\n        this.#label = account.label;\n        this.#icon = account.icon;\n    }\n}\n\n/**\n * Efficiently compare {@link Indexed} arrays (e.g. `Array` and `Uint8Array`).\n *\n * @param a An array.\n * @param b Another array.\n *\n * @return `true` if the arrays have the same length and elements, `false` otherwise.\n *\n * @group Util\n */\nexport function arraysEqual<T>(a: Indexed<T>, b: Indexed<T>): boolean {\n    if (a === b) return true;\n\n    const length = a.length;\n    if (length !== b.length) return false;\n\n    for (let i = 0; i < length; i++) {\n        if (a[i] !== b[i]) return false;\n    }\n\n    return true;\n}\n\n/**\n * Efficiently compare byte arrays, using {@link arraysEqual}.\n *\n * @param a A byte array.\n * @param b Another byte array.\n *\n * @return `true` if the byte arrays have the same length and bytes, `false` otherwise.\n *\n * @group Util\n */\nexport function bytesEqual(a: ReadonlyUint8Array, b: ReadonlyUint8Array): boolean {\n    return arraysEqual(a, b);\n}\n\n/**\n * Efficiently concatenate byte arrays without modifying them.\n *\n * @param first  A byte array.\n * @param others Additional byte arrays.\n *\n * @return New byte array containing the concatenation of all the byte arrays.\n *\n * @group Util\n */\nexport function concatBytes(first: ReadonlyUint8Array, ...others: ReadonlyUint8Array[]): Uint8Array {\n    const length = others.reduce((length, bytes) => length + bytes.length, first.length);\n    const bytes = new Uint8Array(length);\n\n    bytes.set(first, 0);\n    for (const other of others) {\n        bytes.set(other, bytes.length);\n    }\n\n    return bytes;\n}\n\n/**\n * Create a new object with a subset of fields from a source object.\n *\n * @param source Object to pick fields from.\n * @param keys   Names of fields to pick.\n *\n * @return New object with only the picked fields.\n *\n * @group Util\n */\nexport function pick<T, K extends keyof T>(source: T, ...keys: K[]): Pick<T, K> {\n    const picked = {} as Pick<T, K>;\n    for (const key of keys) {\n        picked[key] = source[key];\n    }\n    return picked;\n}\n\n/**\n * Call a callback function, catch an error if it throws, and log the error without rethrowing.\n *\n * @param callback Function to call.\n *\n * @group Util\n */\nexport function guard(callback: () => void) {\n    try {\n        callback();\n    } catch (error) {\n        console.error(error);\n    }\n}\n\n/**\n * @internal\n *\n * Type with a numeric `length` and numerically indexed elements of a generic type `T`.\n *\n * For example, `Array<T>` and `Uint8Array`.\n *\n * @group Internal\n */\nexport interface Indexed<T> {\n    length: number;\n    [index: number]: T;\n}\n", "import type { IdentifierString } from '@wallet-standard/base';\n\n/** Solana Mainnet (beta) cluster, e.g. https://api.mainnet-beta.solana.com */\nexport const SOLANA_MAINNET_CHAIN = 'solana:mainnet';\n\n/** Solana Devnet cluster, e.g. https://api.devnet.solana.com */\nexport const SOLANA_DEVNET_CHAIN = 'solana:devnet';\n\n/** Solana Testnet cluster, e.g. https://api.testnet.solana.com */\nexport const SOLANA_TESTNET_CHAIN = 'solana:testnet';\n\n/** Solana Localnet cluster, e.g. http://localhost:8899 */\nexport const SOLANA_LOCALNET_CHAIN = 'solana:localnet';\n\n/** Array of all Solana clusters */\nexport const SOLANA_CHAINS = [\n    SOLANA_MAINNET_CHAIN,\n    SOLANA_DEVNET_CHAIN,\n    SOLANA_TESTNET_CHAIN,\n    SOLANA_LOCALNET_CHAIN,\n] as const;\n\n/** Type of all Solana clusters */\nexport type SolanaChain = (typeof SOLANA_CHAINS)[number];\n\n/**\n * Check if a chain corresponds with one of the Solana clusters.\n */\nexport function isSolanaChain(chain: IdentifierString): chain is SolanaChain {\n    return SOLANA_CHAINS.includes(chain as SolanaChain);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAwBM,SAAU,eAAe,QAAc;AACzC,QAAM,WAA8C,CAAC,EAAE,SAAQ,MAAO,SAAS,MAAM;AACrF,MAAI;AACC,WAA8B,cAAc,IAAI,oBAAoB,QAAQ,CAAC;EAClF,SAAS,OAAO;AACZ,YAAQ,MAAM,mEAAmE,KAAK;EAC1F;AACA,MAAI;AACC,WAA8B,iBAAiB,6BAA6B,CAAC,EAAE,QAAQ,IAAG,MACvF,SAAS,GAAG,CAAC;EAErB,SAAS,OAAO;AACZ,YAAQ,MAAM,iEAAiE,KAAK;EACxF;AACJ;AAEA,IAAM,sBAAN,cAAkC,MAAK;EAGnC,IAAI,SAAM;AACN,WAAO,uBAAA,MAAI,6BAAA,GAAA;EACf;EAEA,IAAI,OAAI;AACJ,WAAO;EACX;EAEA,YAAY,UAA2C;AACnD,UAAM,mCAAmC;MACrC,SAAS;MACT,YAAY;MACZ,UAAU;KACb;AAfI,gCAAA,IAAA,MAAA,MAAA;AAgBL,2BAAA,MAAI,6BAAW,UAAQ,GAAA;EAC3B;;EAGA,iBAAc;AACV,UAAM,IAAI,MAAM,iCAAiC;EACrD;;EAGA,2BAAwB;AACpB,UAAM,IAAI,MAAM,2CAA2C;EAC/D;;EAGA,kBAAe;AACX,UAAM,IAAI,MAAM,kCAAkC;EACtD;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3DE,IAAO,wBAAP,MAAO,uBAAqB;;EAS9B,IAAI,UAAO;AACP,WAAOA,wBAAA,MAAI,gCAAA,GAAA;EACf;;EAGA,IAAI,YAAS;AACT,WAAOA,wBAAA,MAAI,kCAAA,GAAA,EAAY,MAAK;EAChC;;EAGA,IAAI,SAAM;AACN,WAAOA,wBAAA,MAAI,+BAAA,GAAA,EAAS,MAAK;EAC7B;;EAGA,IAAI,WAAQ;AACR,WAAOA,wBAAA,MAAI,iCAAA,GAAA,EAAW,MAAK;EAC/B;;EAGA,IAAI,QAAK;AACL,WAAOA,wBAAA,MAAI,8BAAA,GAAA;EACf;;EAGA,IAAI,OAAI;AACJ,WAAOA,wBAAA,MAAI,6BAAA,GAAA;EACf;;;;;;EAOA,YAAY,SAAsB;AA1CzB,mCAAA,IAAA,MAAA,MAAA;AACA,qCAAA,IAAA,MAAA,MAAA;AACA,kCAAA,IAAA,MAAA,MAAA;AACA,oCAAA,IAAA,MAAA,MAAA;AACA,iCAAA,IAAA,MAAA,MAAA;AACA,gCAAA,IAAA,MAAA,MAAA;AAsCL,QAAI,eAAe,wBAAuB;AACtC,aAAO,OAAO,IAAI;IACtB;AAEA,IAAAC,wBAAA,MAAI,gCAAY,QAAQ,SAAO,GAAA;AAC/B,IAAAA,wBAAA,MAAI,kCAAc,QAAQ,UAAU,MAAK,GAAE,GAAA;AAC3C,IAAAA,wBAAA,MAAI,+BAAW,QAAQ,OAAO,MAAK,GAAE,GAAA;AACrC,IAAAA,wBAAA,MAAI,iCAAa,QAAQ,SAAS,MAAK,GAAE,GAAA;AACzC,IAAAA,wBAAA,MAAI,8BAAU,QAAQ,OAAK,GAAA;AAC3B,IAAAA,wBAAA,MAAI,6BAAS,QAAQ,MAAI,GAAA;EAC7B;;;AAaE,SAAU,YAAe,GAAe,GAAa;AACvD,MAAI,MAAM;AAAG,WAAO;AAEpB,QAAM,SAAS,EAAE;AACjB,MAAI,WAAW,EAAE;AAAQ,WAAO;AAEhC,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC7B,QAAI,EAAE,CAAC,MAAM,EAAE,CAAC;AAAG,aAAO;EAC9B;AAEA,SAAO;AACX;AAYM,SAAU,WAAW,GAAuB,GAAqB;AACnE,SAAO,YAAY,GAAG,CAAC;AAC3B;;;ACvGO,IAAM,uBAAuB;AAG7B,IAAM,sBAAsB;AAG5B,IAAM,uBAAuB;AAG7B,IAAM,wBAAwB;", "names": ["__classPrivateFieldGet", "__classPrivateFieldSet"]}