{"version": 3, "sources": ["../../@ledgerhq/errors/src/helpers.ts", "../../@ledgerhq/errors/src/index.ts", "../../events/events.js", "../../@ledgerhq/hw-transport/src/Transport.ts"], "sourcesContent": ["/* eslint-disable no-continue */\n/* eslint-disable no-unused-vars */\n/* eslint-disable no-param-reassign */\n/* eslint-disable no-prototype-builtins */\n\nconst errorClasses = {};\nconst deserializers = {};\n\nexport const addCustomErrorDeserializer = (name: string, deserializer: (obj: any) => any): void => {\n  deserializers[name] = deserializer;\n};\n\nexport interface LedgerErrorConstructor<F extends { [key: string]: unknown }>\n  extends ErrorConstructor {\n  new (message?: string, fields?: F, options?: any): Error;\n  (message?: string, fields?: F, options?: any): Error;\n  readonly prototype: Error;\n}\n\nexport const createCustomErrorClass = <\n  F extends { [key: string]: unknown },\n  T extends LedgerErrorConstructor<F> = LedgerErrorConstructor<F>,\n>(\n  name: string,\n): T => {\n  class CustomErrorClass extends Error {\n    cause?: Error;\n    constructor(message?: string, fields?: F, options?: any) {\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n      // @ts-ignore\n      super(message || name, options);\n      // Set the prototype explicitly. See https://github.com/Microsoft/TypeScript/wiki/Breaking-Changes#extending-built-ins-like-error-array-and-map-may-no-longer-work\n      Object.setPrototypeOf(this, CustomErrorClass.prototype);\n      this.name = name;\n      if (fields) {\n        for (const k in fields) {\n          // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n          // @ts-ignore\n          this[k] = fields[k];\n        }\n      }\n\n      if (options && isObject(options) && \"cause\" in options && !this.cause) {\n        // .cause was specified but the superconstructor\n        // did not create an instance property.\n        const cause = options.cause;\n        this.cause = cause;\n        if (\"stack\" in cause) {\n          this.stack = this.stack + \"\\nCAUSE: \" + cause.stack;\n        }\n      }\n    }\n  }\n\n  errorClasses[name] = CustomErrorClass;\n\n  return CustomErrorClass as unknown as T;\n};\n\nfunction isObject(value) {\n  return typeof value === \"object\";\n}\n\n// inspired from https://github.com/programble/errio/blob/master/index.js\nexport const deserializeError = (object: any): Error | undefined => {\n  if (object && typeof object === \"object\") {\n    try {\n      if (typeof object.message === \"string\") {\n        const msg = JSON.parse(object.message);\n        if (msg.message && msg.name) {\n          object = msg;\n        }\n      }\n    } catch (e) {\n      // nothing\n    }\n\n    let error;\n    if (typeof object.name === \"string\") {\n      const { name } = object;\n      const des = deserializers[name];\n      if (des) {\n        error = des(object);\n      } else {\n        let constructor = name === \"Error\" ? Error : errorClasses[name];\n\n        if (!constructor) {\n          console.warn(\"deserializing an unknown class '\" + name + \"'\");\n          constructor = createCustomErrorClass(name);\n        }\n\n        error = Object.create(constructor.prototype);\n        try {\n          for (const prop in object) {\n            if (object.hasOwnProperty(prop)) {\n              error[prop] = object[prop];\n            }\n          }\n        } catch (e) {\n          // sometimes setting a property can fail (e.g. .name)\n        }\n      }\n    } else {\n      if (typeof object.message === \"string\") {\n        error = new Error(object.message);\n      }\n    }\n\n    if (error && !error.stack && Error.captureStackTrace) {\n      Error.captureStackTrace(error, deserializeError);\n    }\n    return error;\n  }\n  return new Error(String(object));\n};\n\n// inspired from https://github.com/sindresorhus/serialize-error/blob/master/index.js\nexport const serializeError = (\n  value: undefined | To | string | (() => unknown),\n): undefined | To | string => {\n  if (!value) return value;\n  if (typeof value === \"object\") {\n    return destroyCircular(value, []);\n  }\n  if (typeof value === \"function\") {\n    return `[Function: ${value.name || \"anonymous\"}]`;\n  }\n  return value;\n};\n\ninterface To {\n  name?: string;\n  message?: string;\n  stack?: string;\n}\n\n// https://www.npmjs.com/package/destroy-circular\nfunction destroyCircular(from: To, seen: Array<To>): To {\n  const to: To = {};\n  seen.push(from);\n  for (const key of Object.keys(from)) {\n    const value = from[key];\n    if (typeof value === \"function\") {\n      continue;\n    }\n    if (!value || typeof value !== \"object\") {\n      to[key] = value;\n      continue;\n    }\n    if (seen.indexOf(from[key]) === -1) {\n      to[key] = destroyCircular(from[key], seen.slice(0));\n      continue;\n    }\n    to[key] = \"[Circular]\";\n  }\n  if (typeof from.name === \"string\") {\n    to.name = from.name;\n  }\n  if (typeof from.message === \"string\") {\n    to.message = from.message;\n  }\n  if (typeof from.stack === \"string\") {\n    to.stack = from.stack;\n  }\n  return to;\n}\n", "import {\n  serializeError,\n  deserializeError,\n  createCustomErrorClass,\n  addCustomErrorDeserializer,\n  LedgerErrorConstructor,\n} from \"./helpers\";\n\nexport { serializeError, deserializeError, createCustomErrorClass, addCustomErrorDeserializer };\n\nexport const AccountNameRequiredError = createCustomErrorClass(\"AccountNameRequired\");\nexport const AccountNotSupported = createCustomErrorClass(\"AccountNotSupported\");\nexport const AccountAwaitingSendPendingOperations = createCustomErrorClass(\n  \"AccountAwaitingSendPendingOperations\",\n);\nexport const AmountRequired = createCustomErrorClass(\"AmountRequired\");\nexport const BluetoothRequired = createCustomErrorClass(\"BluetoothRequired\");\nexport const BtcUnmatchedApp = createCustomErrorClass(\"BtcUnmatchedApp\");\nexport const CantOpenDevice = createCustomErrorClass(\"CantOpenDevice\");\nexport const CashAddrNotSupported = createCustomErrorClass(\"CashAddrNotSupported\");\nexport const ClaimRewardsFeesWarning = createCustomErrorClass(\"ClaimRewardsFeesWarning\");\nexport const CurrencyNotSupported = createCustomErrorClass<\n  { currencyName: string },\n  LedgerErrorConstructor<{ currencyName: string }>\n>(\"CurrencyNotSupported\");\nexport const DeviceAppVerifyNotSupported = createCustomErrorClass(\"DeviceAppVerifyNotSupported\");\nexport const DeviceGenuineSocketEarlyClose = createCustomErrorClass(\n  \"DeviceGenuineSocketEarlyClose\",\n);\nexport const DeviceNotGenuineError = createCustomErrorClass(\"DeviceNotGenuine\");\nexport const DeviceOnDashboardExpected = createCustomErrorClass(\"DeviceOnDashboardExpected\");\nexport const DeviceOnDashboardUnexpected = createCustomErrorClass(\"DeviceOnDashboardUnexpected\");\nexport const DeviceInOSUExpected = createCustomErrorClass(\"DeviceInOSUExpected\");\nexport const DeviceHalted = createCustomErrorClass(\"DeviceHalted\");\nexport const DeviceNameInvalid = createCustomErrorClass(\"DeviceNameInvalid\");\nexport const DeviceSocketFail = createCustomErrorClass(\"DeviceSocketFail\");\nexport const DeviceSocketNoBulkStatus = createCustomErrorClass(\"DeviceSocketNoBulkStatus\");\nexport const DeviceNeedsRestart = createCustomErrorClass(\"DeviceSocketNoBulkStatus\");\nexport const UnresponsiveDeviceError = createCustomErrorClass(\"UnresponsiveDeviceError\");\nexport const DisconnectedDevice = createCustomErrorClass(\"DisconnectedDevice\");\nexport const DisconnectedDeviceDuringOperation = createCustomErrorClass(\n  \"DisconnectedDeviceDuringOperation\",\n);\nexport const DeviceExtractOnboardingStateError = createCustomErrorClass(\n  \"DeviceExtractOnboardingStateError\",\n);\nexport const DeviceOnboardingStatePollingError = createCustomErrorClass(\n  \"DeviceOnboardingStatePollingError\",\n);\nexport const EnpointConfigError = createCustomErrorClass(\"EnpointConfig\");\nexport const EthAppPleaseEnableContractData = createCustomErrorClass(\n  \"EthAppPleaseEnableContractData\",\n);\nexport const FeeEstimationFailed = createCustomErrorClass(\"FeeEstimationFailed\");\nexport const FirmwareNotRecognized = createCustomErrorClass(\"FirmwareNotRecognized\");\nexport const HardResetFail = createCustomErrorClass(\"HardResetFail\");\nexport const InvalidXRPTag = createCustomErrorClass(\"InvalidXRPTag\");\nexport const InvalidAddress = createCustomErrorClass(\"InvalidAddress\");\nexport const InvalidNonce = createCustomErrorClass(\"InvalidNonce\");\nexport const InvalidAddressBecauseDestinationIsAlsoSource = createCustomErrorClass(\n  \"InvalidAddressBecauseDestinationIsAlsoSource\",\n);\nexport const LatestMCUInstalledError = createCustomErrorClass(\"LatestMCUInstalledError\");\nexport const LatestFirmwareVersionRequired = createCustomErrorClass(\n  \"LatestFirmwareVersionRequired\",\n);\nexport const UnknownMCU = createCustomErrorClass(\"UnknownMCU\");\nexport const LedgerAPIError = createCustomErrorClass(\"LedgerAPIError\");\nexport const LedgerAPIErrorWithMessage = createCustomErrorClass(\"LedgerAPIErrorWithMessage\");\nexport const LedgerAPINotAvailable = createCustomErrorClass(\"LedgerAPINotAvailable\");\nexport const ManagerAppAlreadyInstalledError = createCustomErrorClass(\"ManagerAppAlreadyInstalled\");\nexport const ManagerAppRelyOnBTCError = createCustomErrorClass(\"ManagerAppRelyOnBTC\");\nexport const ManagerAppDepInstallRequired = createCustomErrorClass(\"ManagerAppDepInstallRequired\");\nexport const ManagerAppDepUninstallRequired = createCustomErrorClass(\n  \"ManagerAppDepUninstallRequired\",\n);\nexport const ManagerDeviceLockedError = createCustomErrorClass(\"ManagerDeviceLocked\");\nexport const ManagerFirmwareNotEnoughSpaceError = createCustomErrorClass(\n  \"ManagerFirmwareNotEnoughSpace\",\n);\nexport const ManagerNotEnoughSpaceError = createCustomErrorClass(\"ManagerNotEnoughSpace\");\nexport const ManagerUninstallBTCDep = createCustomErrorClass(\"ManagerUninstallBTCDep\");\nexport const NetworkDown = createCustomErrorClass(\"NetworkDown\");\nexport const NetworkError = createCustomErrorClass(\"NetworkError\");\nexport const NoAddressesFound = createCustomErrorClass(\"NoAddressesFound\");\nexport const NotEnoughBalance = createCustomErrorClass(\"NotEnoughBalance\");\nexport const NotEnoughBalanceFees = createCustomErrorClass(\"NotEnoughBalanceFees\");\nexport const NotEnoughBalanceSwap = createCustomErrorClass(\"NotEnoughBalanceSwap\");\nexport const NotEnoughBalanceToDelegate = createCustomErrorClass(\"NotEnoughBalanceToDelegate\");\nexport const NotEnoughBalanceInParentAccount = createCustomErrorClass(\n  \"NotEnoughBalanceInParentAccount\",\n);\nexport const NotEnoughSpendableBalance = createCustomErrorClass(\"NotEnoughSpendableBalance\");\nexport const NotEnoughBalanceBecauseDestinationNotCreated = createCustomErrorClass(\n  \"NotEnoughBalanceBecauseDestinationNotCreated\",\n);\nexport const NoAccessToCamera = createCustomErrorClass(\"NoAccessToCamera\");\nexport const NotEnoughGas = createCustomErrorClass(\"NotEnoughGas\");\n// Error message specifically for the PTX swap flow\nexport const NotEnoughGasSwap = createCustomErrorClass(\"NotEnoughGasSwap\");\nexport const TronEmptyAccount = createCustomErrorClass(\"TronEmptyAccount\");\nexport const MaybeKeepTronAccountAlive = createCustomErrorClass(\"MaybeKeepTronAccountAlive\");\nexport const NotSupportedLegacyAddress = createCustomErrorClass(\"NotSupportedLegacyAddress\");\nexport const GasLessThanEstimate = createCustomErrorClass(\"GasLessThanEstimate\");\nexport const PriorityFeeTooLow = createCustomErrorClass(\"PriorityFeeTooLow\");\nexport const PriorityFeeTooHigh = createCustomErrorClass(\"PriorityFeeTooHigh\");\nexport const PriorityFeeHigherThanMaxFee = createCustomErrorClass(\"PriorityFeeHigherThanMaxFee\");\nexport const MaxFeeTooLow = createCustomErrorClass(\"MaxFeeTooLow\");\nexport const PasswordsDontMatchError = createCustomErrorClass(\"PasswordsDontMatch\");\nexport const PasswordIncorrectError = createCustomErrorClass(\"PasswordIncorrect\");\nexport const RecommendSubAccountsToEmpty = createCustomErrorClass(\"RecommendSubAccountsToEmpty\");\nexport const RecommendUndelegation = createCustomErrorClass(\"RecommendUndelegation\");\nexport const TimeoutTagged = createCustomErrorClass(\"TimeoutTagged\");\nexport const UnexpectedBootloader = createCustomErrorClass(\"UnexpectedBootloader\");\nexport const MCUNotGenuineToDashboard = createCustomErrorClass(\"MCUNotGenuineToDashboard\");\nexport const RecipientRequired = createCustomErrorClass(\"RecipientRequired\");\nexport const UnavailableTezosOriginatedAccountReceive = createCustomErrorClass(\n  \"UnavailableTezosOriginatedAccountReceive\",\n);\nexport const UnavailableTezosOriginatedAccountSend = createCustomErrorClass(\n  \"UnavailableTezosOriginatedAccountSend\",\n);\nexport const UpdateFetchFileFail = createCustomErrorClass(\"UpdateFetchFileFail\");\nexport const UpdateIncorrectHash = createCustomErrorClass(\"UpdateIncorrectHash\");\nexport const UpdateIncorrectSig = createCustomErrorClass(\"UpdateIncorrectSig\");\nexport const UpdateYourApp = createCustomErrorClass(\"UpdateYourApp\");\nexport const UserRefusedDeviceNameChange = createCustomErrorClass(\"UserRefusedDeviceNameChange\");\nexport const UserRefusedAddress = createCustomErrorClass(\"UserRefusedAddress\");\nexport const UserRefusedFirmwareUpdate = createCustomErrorClass(\"UserRefusedFirmwareUpdate\");\nexport const UserRefusedAllowManager = createCustomErrorClass(\"UserRefusedAllowManager\");\nexport const UserRefusedOnDevice = createCustomErrorClass(\"UserRefusedOnDevice\"); // TODO rename because it's just for transaction refusal\nexport const PinNotSet = createCustomErrorClass(\"PinNotSet\");\nexport const ExpertModeRequired = createCustomErrorClass(\"ExpertModeRequired\");\nexport const TransportOpenUserCancelled = createCustomErrorClass(\"TransportOpenUserCancelled\");\nexport const TransportInterfaceNotAvailable = createCustomErrorClass(\n  \"TransportInterfaceNotAvailable\",\n);\nexport const TransportRaceCondition = createCustomErrorClass(\"TransportRaceCondition\");\nexport const TransportWebUSBGestureRequired = createCustomErrorClass(\n  \"TransportWebUSBGestureRequired\",\n);\nexport const TransactionHasBeenValidatedError = createCustomErrorClass(\n  \"TransactionHasBeenValidatedError\",\n);\nexport const TransportExchangeTimeoutError = createCustomErrorClass(\n  \"TransportExchangeTimeoutError\",\n);\nexport const DeviceShouldStayInApp = createCustomErrorClass(\"DeviceShouldStayInApp\");\nexport const WebsocketConnectionError = createCustomErrorClass(\"WebsocketConnectionError\");\nexport const WebsocketConnectionFailed = createCustomErrorClass(\"WebsocketConnectionFailed\");\nexport const WrongDeviceForAccount = createCustomErrorClass(\"WrongDeviceForAccount\");\nexport const WrongDeviceForAccountPayout = createCustomErrorClass(\"WrongDeviceForAccountPayout\");\nexport const WrongDeviceForAccountRefund = createCustomErrorClass(\"WrongDeviceForAccountRefund\");\nexport const WrongAppForCurrency = createCustomErrorClass(\"WrongAppForCurrency\");\n\nexport const ETHAddressNonEIP = createCustomErrorClass(\"ETHAddressNonEIP\");\nexport const CantScanQRCode = createCustomErrorClass(\"CantScanQRCode\");\nexport const FeeNotLoaded = createCustomErrorClass(\"FeeNotLoaded\");\nexport const FeeNotLoadedSwap = createCustomErrorClass(\"FeeNotLoadedSwap\");\nexport const FeeRequired = createCustomErrorClass(\"FeeRequired\");\nexport const FeeTooHigh = createCustomErrorClass(\"FeeTooHigh\");\nexport const PendingOperation = createCustomErrorClass(\"PendingOperation\");\nexport const SyncError = createCustomErrorClass(\"SyncError\");\nexport const PairingFailed = createCustomErrorClass(\"PairingFailed\");\nexport const PeerRemovedPairing = createCustomErrorClass(\"PeerRemovedPairing\");\nexport const GenuineCheckFailed = createCustomErrorClass(\"GenuineCheckFailed\");\ntype NetworkType = {\n  status: number;\n  url: string | undefined;\n  method: string;\n};\nexport const LedgerAPI4xx = createCustomErrorClass<\n  NetworkType,\n  LedgerErrorConstructor<NetworkType>\n>(\"LedgerAPI4xx\");\nexport const LedgerAPI5xx = createCustomErrorClass<\n  NetworkType,\n  LedgerErrorConstructor<NetworkType>\n>(\"LedgerAPI5xx\");\nexport const FirmwareOrAppUpdateRequired = createCustomErrorClass(\"FirmwareOrAppUpdateRequired\");\n\n// SpeedUp / Cancel EVM tx\nexport const ReplacementTransactionUnderpriced = createCustomErrorClass(\n  \"ReplacementTransactionUnderpriced\",\n);\n\n// Bitcoin family\nexport const OpReturnDataSizeLimit = createCustomErrorClass(\"OpReturnSizeLimit\");\nexport const DustLimit = createCustomErrorClass(\"DustLimit\");\n\n// Language\nexport const LanguageNotFound = createCustomErrorClass(\"LanguageNotFound\");\n\n// db stuff, no need to translate\nexport const NoDBPathGiven = createCustomErrorClass(\"NoDBPathGiven\");\nexport const DBWrongPassword = createCustomErrorClass(\"DBWrongPassword\");\nexport const DBNotReset = createCustomErrorClass(\"DBNotReset\");\n\nexport const SequenceNumberError = createCustomErrorClass(\"SequenceNumberError\");\nexport const DisabledTransactionBroadcastError = createCustomErrorClass(\n  \"DisabledTransactionBroadcastError\",\n);\n\n// Represents the type of all the classes created with createCustomErrorClass\nexport type CustomErrorClassType = ReturnType<typeof createCustomErrorClass>;\n\n/**\n * Type of a Transport error used to represent all equivalent errors coming from all possible implementation of Transport\n */\nexport enum HwTransportErrorType {\n  Unknown = \"Unknown\",\n  LocationServicesDisabled = \"LocationServicesDisabled\",\n  LocationServicesUnauthorized = \"LocationServicesUnauthorized\",\n  BluetoothScanStartFailed = \"BluetoothScanStartFailed\",\n}\n\n/**\n * Represents an error coming from the usage of any Transport implementation.\n *\n * Needed to map a specific implementation error into an error that\n * can be managed by any code unaware of the specific Transport implementation\n * that was used.\n */\nexport class HwTransportError extends Error {\n  type: HwTransportErrorType;\n\n  constructor(type: HwTransportErrorType, message: string) {\n    super(message);\n    this.name = \"HwTransportError\";\n    this.type = type;\n\n    // Needed as long as we target < ES6\n    Object.setPrototypeOf(this, HwTransportError.prototype);\n  }\n}\n\n/**\n * TransportError is used for any generic transport errors.\n * e.g. Error thrown when data received by exchanges are incorrect or if exchanged failed to communicate with the device for various reason.\n */\nexport class TransportError extends Error {\n  id: string;\n  constructor(message: string, id: string) {\n    const name = \"TransportError\";\n    super(message || name);\n    this.name = name;\n    this.message = message;\n    this.stack = new Error(message).stack;\n    this.id = id;\n  }\n}\n\naddCustomErrorDeserializer(\"TransportError\", e => new TransportError(e.message, e.id));\n\nexport const StatusCodes = {\n  ACCESS_CONDITION_NOT_FULFILLED: 0x9804,\n  ALGORITHM_NOT_SUPPORTED: 0x9484,\n  CLA_NOT_SUPPORTED: 0x6e00,\n  CODE_BLOCKED: 0x9840,\n  CODE_NOT_INITIALIZED: 0x9802,\n  COMMAND_INCOMPATIBLE_FILE_STRUCTURE: 0x6981,\n  CONDITIONS_OF_USE_NOT_SATISFIED: 0x6985,\n  CONTRADICTION_INVALIDATION: 0x9810,\n  CONTRADICTION_SECRET_CODE_STATUS: 0x9808,\n  DEVICE_IN_RECOVERY_MODE: 0x662f,\n  CUSTOM_IMAGE_EMPTY: 0x662e,\n  FILE_ALREADY_EXISTS: 0x6a89,\n  FILE_NOT_FOUND: 0x9404,\n  GP_AUTH_FAILED: 0x6300,\n  HALTED: 0x6faa,\n  INCONSISTENT_FILE: 0x9408,\n  INCORRECT_DATA: 0x6a80,\n  INCORRECT_LENGTH: 0x6700,\n  INCORRECT_P1_P2: 0x6b00,\n  INS_NOT_SUPPORTED: 0x6d00,\n  DEVICE_NOT_ONBOARDED: 0x6d07,\n  DEVICE_NOT_ONBOARDED_2: 0x6611,\n  INVALID_KCV: 0x9485,\n  INVALID_OFFSET: 0x9402,\n  LICENSING: 0x6f42,\n  LOCKED_DEVICE: 0x5515,\n  MAX_VALUE_REACHED: 0x9850,\n  MEMORY_PROBLEM: 0x9240,\n  MISSING_CRITICAL_PARAMETER: 0x6800,\n  NO_EF_SELECTED: 0x9400,\n  NOT_ENOUGH_MEMORY_SPACE: 0x6a84,\n  OK: 0x9000,\n  PIN_REMAINING_ATTEMPTS: 0x63c0,\n  REFERENCED_DATA_NOT_FOUND: 0x6a88,\n  SECURITY_STATUS_NOT_SATISFIED: 0x6982,\n  TECHNICAL_PROBLEM: 0x6f00,\n  UNKNOWN_APDU: 0x6d02,\n  USER_REFUSED_ON_DEVICE: 0x5501,\n  NOT_ENOUGH_SPACE: 0x5102,\n  APP_NOT_FOUND_OR_INVALID_CONTEXT: 0x5123,\n  INVALID_APP_NAME_LENGTH: 0x670a,\n  GEN_AES_KEY_FAILED: 0x5419,\n  INTERNAL_CRYPTO_OPERATION_FAILED: 0x541a,\n  INTERNAL_COMPUTE_AES_CMAC_FAILED: 0x541b,\n  ENCRYPT_APP_STORAGE_FAILED: 0x541c,\n  INVALID_BACKUP_STATE: 0x6642,\n  PIN_NOT_SET: 0x5502,\n  INVALID_BACKUP_LENGTH: 0x6733,\n  INVALID_RESTORE_STATE: 0x6643,\n  INVALID_CHUNK_LENGTH: 0x6734,\n  INVALID_BACKUP_HEADER: 0x684a,\n\n  // Not documented:\n  TRUSTCHAIN_WRONG_SEED: 0xb007,\n};\n\nexport function getAltStatusMessage(code: number): string | undefined | null {\n  switch (code) {\n    // improve text of most common errors\n    case 0x6700:\n      return \"Incorrect length\";\n    case 0x6800:\n      return \"Missing critical parameter\";\n    case 0x6982:\n      return \"Security not satisfied (dongle locked or have invalid access rights)\";\n    case 0x6985:\n      return \"Condition of use not satisfied (denied by the user?)\";\n    case 0x6a80:\n      return \"Invalid data received\";\n    case 0x6b00:\n      return \"Invalid parameter received\";\n    case 0x5515:\n      return \"Locked device\";\n  }\n  if (0x6f00 <= code && code <= 0x6fff) {\n    return \"Internal error, please report\";\n  }\n}\n\n/**\n * Error thrown when a device returned a non success status.\n * the error.statusCode is one of the `StatusCodes` exported by this library.\n */\nexport class TransportStatusError extends Error {\n  statusCode: number;\n  statusText: string;\n\n  /**\n   * @param statusCode The error status code coming from a Transport implementation\n   * @param options containing:\n   *  - canBeMappedToChildError: enable the mapping of TransportStatusError to an error extending/inheriting from it\n   *  . Ex: LockedDeviceError. Default to true.\n   */\n  constructor(\n    statusCode: number,\n    { canBeMappedToChildError = true }: { canBeMappedToChildError?: boolean } = {},\n  ) {\n    const statusText =\n      Object.keys(StatusCodes).find(k => StatusCodes[k] === statusCode) || \"UNKNOWN_ERROR\";\n    const smsg = getAltStatusMessage(statusCode) || statusText;\n    const statusCodeStr = statusCode.toString(16);\n    const message = `Ledger device: ${smsg} (0x${statusCodeStr})`;\n\n    super(message);\n    this.name = \"TransportStatusError\";\n\n    this.statusCode = statusCode;\n    this.statusText = statusText;\n\n    Object.setPrototypeOf(this, TransportStatusError.prototype);\n\n    // Maps to a LockedDeviceError\n    if (canBeMappedToChildError && statusCode === StatusCodes.LOCKED_DEVICE) {\n      return new LockedDeviceError(message);\n    }\n  }\n}\n\nexport class LockedDeviceError extends TransportStatusError {\n  constructor(message?: string) {\n    super(StatusCodes.LOCKED_DEVICE, { canBeMappedToChildError: false });\n    if (message) {\n      this.message = message;\n    }\n    this.name = \"LockedDeviceError\";\n    Object.setPrototypeOf(this, LockedDeviceError.prototype);\n  }\n}\n\nexport class DeviceMangementKitError extends Error {\n  constructor(name: string, message: string) {\n    super(message);\n    this.name = name;\n    Object.setPrototypeOf(this, DeviceMangementKitError.prototype);\n  }\n}\n\n// Represents the type of the class TransportStatusError and its children\nexport type TransportStatusErrorClassType = typeof TransportStatusError | typeof LockedDeviceError;\n\naddCustomErrorDeserializer(\"TransportStatusError\", e => new TransportStatusError(e.statusCode));\n", "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict';\n\nvar R = typeof Reflect === 'object' ? Reflect : null\nvar ReflectApply = R && typeof R.apply === 'function'\n  ? R.apply\n  : function ReflectApply(target, receiver, args) {\n    return Function.prototype.apply.call(target, receiver, args);\n  }\n\nvar ReflectOwnKeys\nif (R && typeof R.ownKeys === 'function') {\n  ReflectOwnKeys = R.ownKeys\n} else if (Object.getOwnPropertySymbols) {\n  ReflectOwnKeys = function ReflectOwnKeys(target) {\n    return Object.getOwnPropertyNames(target)\n      .concat(Object.getOwnPropertySymbols(target));\n  };\n} else {\n  ReflectOwnKeys = function ReflectOwnKeys(target) {\n    return Object.getOwnPropertyNames(target);\n  };\n}\n\nfunction ProcessEmitWarning(warning) {\n  if (console && console.warn) console.warn(warning);\n}\n\nvar NumberIsNaN = Number.isNaN || function NumberIsNaN(value) {\n  return value !== value;\n}\n\nfunction EventEmitter() {\n  EventEmitter.init.call(this);\n}\nmodule.exports = EventEmitter;\nmodule.exports.once = once;\n\n// Backwards-compat with node 0.10.x\nEventEmitter.EventEmitter = EventEmitter;\n\nEventEmitter.prototype._events = undefined;\nEventEmitter.prototype._eventsCount = 0;\nEventEmitter.prototype._maxListeners = undefined;\n\n// By default EventEmitters will print a warning if more than 10 listeners are\n// added to it. This is a useful default which helps finding memory leaks.\nvar defaultMaxListeners = 10;\n\nfunction checkListener(listener) {\n  if (typeof listener !== 'function') {\n    throw new TypeError('The \"listener\" argument must be of type Function. Received type ' + typeof listener);\n  }\n}\n\nObject.defineProperty(EventEmitter, 'defaultMaxListeners', {\n  enumerable: true,\n  get: function() {\n    return defaultMaxListeners;\n  },\n  set: function(arg) {\n    if (typeof arg !== 'number' || arg < 0 || NumberIsNaN(arg)) {\n      throw new RangeError('The value of \"defaultMaxListeners\" is out of range. It must be a non-negative number. Received ' + arg + '.');\n    }\n    defaultMaxListeners = arg;\n  }\n});\n\nEventEmitter.init = function() {\n\n  if (this._events === undefined ||\n      this._events === Object.getPrototypeOf(this)._events) {\n    this._events = Object.create(null);\n    this._eventsCount = 0;\n  }\n\n  this._maxListeners = this._maxListeners || undefined;\n};\n\n// Obviously not all Emitters should be limited to 10. This function allows\n// that to be increased. Set to zero for unlimited.\nEventEmitter.prototype.setMaxListeners = function setMaxListeners(n) {\n  if (typeof n !== 'number' || n < 0 || NumberIsNaN(n)) {\n    throw new RangeError('The value of \"n\" is out of range. It must be a non-negative number. Received ' + n + '.');\n  }\n  this._maxListeners = n;\n  return this;\n};\n\nfunction _getMaxListeners(that) {\n  if (that._maxListeners === undefined)\n    return EventEmitter.defaultMaxListeners;\n  return that._maxListeners;\n}\n\nEventEmitter.prototype.getMaxListeners = function getMaxListeners() {\n  return _getMaxListeners(this);\n};\n\nEventEmitter.prototype.emit = function emit(type) {\n  var args = [];\n  for (var i = 1; i < arguments.length; i++) args.push(arguments[i]);\n  var doError = (type === 'error');\n\n  var events = this._events;\n  if (events !== undefined)\n    doError = (doError && events.error === undefined);\n  else if (!doError)\n    return false;\n\n  // If there is no 'error' event listener then throw.\n  if (doError) {\n    var er;\n    if (args.length > 0)\n      er = args[0];\n    if (er instanceof Error) {\n      // Note: The comments on the `throw` lines are intentional, they show\n      // up in Node's output if this results in an unhandled exception.\n      throw er; // Unhandled 'error' event\n    }\n    // At least give some kind of context to the user\n    var err = new Error('Unhandled error.' + (er ? ' (' + er.message + ')' : ''));\n    err.context = er;\n    throw err; // Unhandled 'error' event\n  }\n\n  var handler = events[type];\n\n  if (handler === undefined)\n    return false;\n\n  if (typeof handler === 'function') {\n    ReflectApply(handler, this, args);\n  } else {\n    var len = handler.length;\n    var listeners = arrayClone(handler, len);\n    for (var i = 0; i < len; ++i)\n      ReflectApply(listeners[i], this, args);\n  }\n\n  return true;\n};\n\nfunction _addListener(target, type, listener, prepend) {\n  var m;\n  var events;\n  var existing;\n\n  checkListener(listener);\n\n  events = target._events;\n  if (events === undefined) {\n    events = target._events = Object.create(null);\n    target._eventsCount = 0;\n  } else {\n    // To avoid recursion in the case that type === \"newListener\"! Before\n    // adding it to the listeners, first emit \"newListener\".\n    if (events.newListener !== undefined) {\n      target.emit('newListener', type,\n                  listener.listener ? listener.listener : listener);\n\n      // Re-assign `events` because a newListener handler could have caused the\n      // this._events to be assigned to a new object\n      events = target._events;\n    }\n    existing = events[type];\n  }\n\n  if (existing === undefined) {\n    // Optimize the case of one listener. Don't need the extra array object.\n    existing = events[type] = listener;\n    ++target._eventsCount;\n  } else {\n    if (typeof existing === 'function') {\n      // Adding the second element, need to change to array.\n      existing = events[type] =\n        prepend ? [listener, existing] : [existing, listener];\n      // If we've already got an array, just append.\n    } else if (prepend) {\n      existing.unshift(listener);\n    } else {\n      existing.push(listener);\n    }\n\n    // Check for listener leak\n    m = _getMaxListeners(target);\n    if (m > 0 && existing.length > m && !existing.warned) {\n      existing.warned = true;\n      // No error code for this since it is a Warning\n      // eslint-disable-next-line no-restricted-syntax\n      var w = new Error('Possible EventEmitter memory leak detected. ' +\n                          existing.length + ' ' + String(type) + ' listeners ' +\n                          'added. Use emitter.setMaxListeners() to ' +\n                          'increase limit');\n      w.name = 'MaxListenersExceededWarning';\n      w.emitter = target;\n      w.type = type;\n      w.count = existing.length;\n      ProcessEmitWarning(w);\n    }\n  }\n\n  return target;\n}\n\nEventEmitter.prototype.addListener = function addListener(type, listener) {\n  return _addListener(this, type, listener, false);\n};\n\nEventEmitter.prototype.on = EventEmitter.prototype.addListener;\n\nEventEmitter.prototype.prependListener =\n    function prependListener(type, listener) {\n      return _addListener(this, type, listener, true);\n    };\n\nfunction onceWrapper() {\n  if (!this.fired) {\n    this.target.removeListener(this.type, this.wrapFn);\n    this.fired = true;\n    if (arguments.length === 0)\n      return this.listener.call(this.target);\n    return this.listener.apply(this.target, arguments);\n  }\n}\n\nfunction _onceWrap(target, type, listener) {\n  var state = { fired: false, wrapFn: undefined, target: target, type: type, listener: listener };\n  var wrapped = onceWrapper.bind(state);\n  wrapped.listener = listener;\n  state.wrapFn = wrapped;\n  return wrapped;\n}\n\nEventEmitter.prototype.once = function once(type, listener) {\n  checkListener(listener);\n  this.on(type, _onceWrap(this, type, listener));\n  return this;\n};\n\nEventEmitter.prototype.prependOnceListener =\n    function prependOnceListener(type, listener) {\n      checkListener(listener);\n      this.prependListener(type, _onceWrap(this, type, listener));\n      return this;\n    };\n\n// Emits a 'removeListener' event if and only if the listener was removed.\nEventEmitter.prototype.removeListener =\n    function removeListener(type, listener) {\n      var list, events, position, i, originalListener;\n\n      checkListener(listener);\n\n      events = this._events;\n      if (events === undefined)\n        return this;\n\n      list = events[type];\n      if (list === undefined)\n        return this;\n\n      if (list === listener || list.listener === listener) {\n        if (--this._eventsCount === 0)\n          this._events = Object.create(null);\n        else {\n          delete events[type];\n          if (events.removeListener)\n            this.emit('removeListener', type, list.listener || listener);\n        }\n      } else if (typeof list !== 'function') {\n        position = -1;\n\n        for (i = list.length - 1; i >= 0; i--) {\n          if (list[i] === listener || list[i].listener === listener) {\n            originalListener = list[i].listener;\n            position = i;\n            break;\n          }\n        }\n\n        if (position < 0)\n          return this;\n\n        if (position === 0)\n          list.shift();\n        else {\n          spliceOne(list, position);\n        }\n\n        if (list.length === 1)\n          events[type] = list[0];\n\n        if (events.removeListener !== undefined)\n          this.emit('removeListener', type, originalListener || listener);\n      }\n\n      return this;\n    };\n\nEventEmitter.prototype.off = EventEmitter.prototype.removeListener;\n\nEventEmitter.prototype.removeAllListeners =\n    function removeAllListeners(type) {\n      var listeners, events, i;\n\n      events = this._events;\n      if (events === undefined)\n        return this;\n\n      // not listening for removeListener, no need to emit\n      if (events.removeListener === undefined) {\n        if (arguments.length === 0) {\n          this._events = Object.create(null);\n          this._eventsCount = 0;\n        } else if (events[type] !== undefined) {\n          if (--this._eventsCount === 0)\n            this._events = Object.create(null);\n          else\n            delete events[type];\n        }\n        return this;\n      }\n\n      // emit removeListener for all listeners on all events\n      if (arguments.length === 0) {\n        var keys = Object.keys(events);\n        var key;\n        for (i = 0; i < keys.length; ++i) {\n          key = keys[i];\n          if (key === 'removeListener') continue;\n          this.removeAllListeners(key);\n        }\n        this.removeAllListeners('removeListener');\n        this._events = Object.create(null);\n        this._eventsCount = 0;\n        return this;\n      }\n\n      listeners = events[type];\n\n      if (typeof listeners === 'function') {\n        this.removeListener(type, listeners);\n      } else if (listeners !== undefined) {\n        // LIFO order\n        for (i = listeners.length - 1; i >= 0; i--) {\n          this.removeListener(type, listeners[i]);\n        }\n      }\n\n      return this;\n    };\n\nfunction _listeners(target, type, unwrap) {\n  var events = target._events;\n\n  if (events === undefined)\n    return [];\n\n  var evlistener = events[type];\n  if (evlistener === undefined)\n    return [];\n\n  if (typeof evlistener === 'function')\n    return unwrap ? [evlistener.listener || evlistener] : [evlistener];\n\n  return unwrap ?\n    unwrapListeners(evlistener) : arrayClone(evlistener, evlistener.length);\n}\n\nEventEmitter.prototype.listeners = function listeners(type) {\n  return _listeners(this, type, true);\n};\n\nEventEmitter.prototype.rawListeners = function rawListeners(type) {\n  return _listeners(this, type, false);\n};\n\nEventEmitter.listenerCount = function(emitter, type) {\n  if (typeof emitter.listenerCount === 'function') {\n    return emitter.listenerCount(type);\n  } else {\n    return listenerCount.call(emitter, type);\n  }\n};\n\nEventEmitter.prototype.listenerCount = listenerCount;\nfunction listenerCount(type) {\n  var events = this._events;\n\n  if (events !== undefined) {\n    var evlistener = events[type];\n\n    if (typeof evlistener === 'function') {\n      return 1;\n    } else if (evlistener !== undefined) {\n      return evlistener.length;\n    }\n  }\n\n  return 0;\n}\n\nEventEmitter.prototype.eventNames = function eventNames() {\n  return this._eventsCount > 0 ? ReflectOwnKeys(this._events) : [];\n};\n\nfunction arrayClone(arr, n) {\n  var copy = new Array(n);\n  for (var i = 0; i < n; ++i)\n    copy[i] = arr[i];\n  return copy;\n}\n\nfunction spliceOne(list, index) {\n  for (; index + 1 < list.length; index++)\n    list[index] = list[index + 1];\n  list.pop();\n}\n\nfunction unwrapListeners(arr) {\n  var ret = new Array(arr.length);\n  for (var i = 0; i < ret.length; ++i) {\n    ret[i] = arr[i].listener || arr[i];\n  }\n  return ret;\n}\n\nfunction once(emitter, name) {\n  return new Promise(function (resolve, reject) {\n    function errorListener(err) {\n      emitter.removeListener(name, resolver);\n      reject(err);\n    }\n\n    function resolver() {\n      if (typeof emitter.removeListener === 'function') {\n        emitter.removeListener('error', errorListener);\n      }\n      resolve([].slice.call(arguments));\n    };\n\n    eventTargetAgnosticAddListener(emitter, name, resolver, { once: true });\n    if (name !== 'error') {\n      addErrorHandlerIfEventEmitter(emitter, errorListener, { once: true });\n    }\n  });\n}\n\nfunction addErrorHandlerIfEventEmitter(emitter, handler, flags) {\n  if (typeof emitter.on === 'function') {\n    eventTargetAgnosticAddListener(emitter, 'error', handler, flags);\n  }\n}\n\nfunction eventTargetAgnosticAddListener(emitter, name, listener, flags) {\n  if (typeof emitter.on === 'function') {\n    if (flags.once) {\n      emitter.once(name, listener);\n    } else {\n      emitter.on(name, listener);\n    }\n  } else if (typeof emitter.addEventListener === 'function') {\n    // EventTarget does not have `error` event semantics like Node\n    // EventEmitters, we do not listen for `error` events here.\n    emitter.addEventListener(name, function wrapListener(arg) {\n      // IE does not have builtin `{ once: true }` support so we\n      // have to do it manually.\n      if (flags.once) {\n        emitter.removeEventListener(name, wrapListener);\n      }\n      listener(arg);\n    });\n  } else {\n    throw new TypeError('The \"emitter\" argument must be of type EventEmitter. Received type ' + typeof emitter);\n  }\n}\n", "import EventEmitter from \"events\";\nimport type { DeviceModel } from \"@ledgerhq/devices\";\nimport {\n  TransportRaceCondition,\n  TransportError,\n  StatusCodes,\n  getAltStatusMessage,\n  TransportStatusError,\n} from \"@ledgerhq/errors\";\nexport {\n  TransportError,\n  TransportStatusError,\n  StatusCodes,\n  getAltStatusMessage,\n};\n\n/**\n */\nexport type Subscription = {\n  unsubscribe: () => void;\n};\n\n/**\n */\nexport type Device = any; // Should be a union type of all possible Device object's shape\n\n/**\n * type: add or remove event\n * descriptor: a parameter that can be passed to open(descriptor)\n * deviceModel: device info on the model (is it a nano s, nano x, ...)\n * device: transport specific device info\n */\nexport interface DescriptorEvent<Descriptor> {\n  type: \"add\" | \"remove\";\n  descriptor: Descriptor;\n  deviceModel?: DeviceModel | null | undefined;\n  device?: Device;\n}\n\n/**\n */\nexport type Observer<Ev> = Readonly<{\n  next: (event: Ev) => unknown;\n  error: (e: any) => unknown;\n  complete: () => unknown;\n}>;\n/**\n * Transport defines the generic interface to share between node/u2f impl\n * A **Descriptor** is a parametric type that is up to be determined for the implementation.\n * it can be for instance an ID, an file path, a URL,...\n */\n\nexport default class Transport {\n  exchangeTimeout = 30000;\n  unresponsiveTimeout = 15000;\n  deviceModel: DeviceModel | null | undefined = null;\n\n  /**\n   * Statically check if a transport is supported on the user's platform/browser.\n   */\n  static readonly isSupported: () => Promise<boolean>;\n\n  /**\n   * List once all available descriptors. For a better granularity, checkout `listen()`.\n   * @return a promise of descriptors\n   * @example\n   * TransportFoo.list().then(descriptors => ...)\n   */\n  static readonly list: () => Promise<Array<any>>;\n\n  /**\n   * Listen all device events for a given Transport. The method takes an Obverver of DescriptorEvent and returns a Subscription (according to Observable paradigm https://github.com/tc39/proposal-observable )\n   * a DescriptorEvent is a `{ descriptor, type }` object. type can be `\"add\"` or `\"remove\"` and descriptor is a value you can pass to `open(descriptor)`.\n   * each listen() call will first emit all potential device already connected and then will emit events can come over times,\n   * for instance if you plug a USB device after listen() or a bluetooth device become discoverable.\n   * @param observer is an object with a next, error and complete function (compatible with observer pattern)\n   * @return a Subscription object on which you can `.unsubscribe()` to stop listening descriptors.\n   * @example\n  const sub = TransportFoo.listen({\n  next: e => {\n    if (e.type===\"add\") {\n      sub.unsubscribe();\n      const transport = await TransportFoo.open(e.descriptor);\n      ...\n    }\n  },\n  error: error => {},\n  complete: () => {}\n  })\n   */\n  static readonly listen: (\n    observer: Observer<DescriptorEvent<any>>\n  ) => Subscription;\n\n  /**\n   * attempt to create a Transport instance with potentially a descriptor.\n   * @param descriptor: the descriptor to open the transport with.\n   * @param timeout: an optional timeout\n   * @return a Promise of Transport instance\n   * @example\n  TransportFoo.open(descriptor).then(transport => ...)\n   */\n  static readonly open: (\n    descriptor?: any,\n    timeout?: number\n  ) => Promise<Transport>;\n\n  /**\n   * low level api to communicate with the device\n   * This method is for implementations to implement but should not be directly called.\n   * Instead, the recommanded way is to use send() method\n   * @param apdu the data to send\n   * @return a Promise of response data\n   */\n  exchange(_apdu: Buffer): Promise<Buffer> {\n    throw new Error(\"exchange not implemented\");\n  }\n\n  /**\n   * set the \"scramble key\" for the next exchanges with the device.\n   * Each App can have a different scramble key and they internally will set it at instanciation.\n   * @param key the scramble key\n   */\n  setScrambleKey(_key: string) {}\n\n  /**\n   * close the exchange with the device.\n   * @return a Promise that ends when the transport is closed.\n   */\n  close(): Promise<void> {\n    return Promise.resolve();\n  }\n\n  _events = new EventEmitter();\n\n  /**\n   * Listen to an event on an instance of transport.\n   * Transport implementation can have specific events. Here is the common events:\n   * * `\"disconnect\"` : triggered if Transport is disconnected\n   */\n  on(eventName: string, cb: (...args: Array<any>) => any): void {\n    this._events.on(eventName, cb);\n  }\n\n  /**\n   * Stop listening to an event on an instance of transport.\n   */\n  off(eventName: string, cb: (...args: Array<any>) => any): void {\n    this._events.removeListener(eventName, cb);\n  }\n\n  emit(event: string, ...args: any): void {\n    this._events.emit(event, ...args);\n  }\n\n  /**\n   * Enable or not logs of the binary exchange\n   */\n  setDebugMode() {\n    console.warn(\n      \"setDebugMode is deprecated. use @ledgerhq/logs instead. No logs are emitted in this anymore.\"\n    );\n  }\n\n  /**\n   * Set a timeout (in milliseconds) for the exchange call. Only some transport might implement it. (e.g. U2F)\n   */\n  setExchangeTimeout(exchangeTimeout: number): void {\n    this.exchangeTimeout = exchangeTimeout;\n  }\n\n  /**\n   * Define the delay before emitting \"unresponsive\" on an exchange that does not respond\n   */\n  setExchangeUnresponsiveTimeout(unresponsiveTimeout: number): void {\n    this.unresponsiveTimeout = unresponsiveTimeout;\n  }\n\n  /**\n   * wrapper on top of exchange to simplify work of the implementation.\n   * @param cla\n   * @param ins\n   * @param p1\n   * @param p2\n   * @param data\n   * @param statusList is a list of accepted status code (shorts). [0x9000] by default\n   * @return a Promise of response buffer\n   */\n  send = async (\n    cla: number,\n    ins: number,\n    p1: number,\n    p2: number,\n    data: Buffer = Buffer.alloc(0),\n    statusList: Array<number> = [StatusCodes.OK]\n  ): Promise<Buffer> => {\n    if (data.length >= 256) {\n      throw new TransportError(\n        \"data.length exceed 256 bytes limit. Got: \" + data.length,\n        \"DataLengthTooBig\"\n      );\n    }\n\n    const response = await this.exchange(\n      Buffer.concat([\n        Buffer.from([cla, ins, p1, p2]),\n        Buffer.from([data.length]),\n        data,\n      ])\n    );\n    const sw = response.readUInt16BE(response.length - 2);\n\n    if (!statusList.some((s) => s === sw)) {\n      throw new TransportStatusError(sw);\n    }\n\n    return response;\n  };\n\n  /**\n   * create() allows to open the first descriptor available or\n   * throw if there is none or if timeout is reached.\n   * This is a light helper, alternative to using listen() and open() (that you may need for any more advanced usecase)\n   * @example\n  TransportFoo.create().then(transport => ...)\n   */\n  static create(\n    openTimeout = 3000,\n    listenTimeout?: number\n  ): Promise<Transport> {\n    return new Promise((resolve, reject) => {\n      let found = false;\n      const sub = this.listen({\n        next: (e) => {\n          found = true;\n          if (sub) sub.unsubscribe();\n          if (listenTimeoutId) clearTimeout(listenTimeoutId);\n          this.open(e.descriptor, openTimeout).then(resolve, reject);\n        },\n        error: (e) => {\n          if (listenTimeoutId) clearTimeout(listenTimeoutId);\n          reject(e);\n        },\n        complete: () => {\n          if (listenTimeoutId) clearTimeout(listenTimeoutId);\n\n          if (!found) {\n            reject(\n              new TransportError(\n                this.ErrorMessage_NoDeviceFound,\n                \"NoDeviceFound\"\n              )\n            );\n          }\n        },\n      });\n      const listenTimeoutId = listenTimeout\n        ? setTimeout(() => {\n            sub.unsubscribe();\n            reject(\n              new TransportError(\n                this.ErrorMessage_ListenTimeout,\n                \"ListenTimeout\"\n              )\n            );\n          }, listenTimeout)\n        : null;\n    });\n  }\n\n  exchangeBusyPromise: Promise<void> | null | undefined;\n  exchangeAtomicImpl = async (\n    f: () => Promise<Buffer | void>\n  ): Promise<Buffer | void> => {\n    if (this.exchangeBusyPromise) {\n      throw new TransportRaceCondition(\n        \"An action was already pending on the Ledger device. Please deny or reconnect.\"\n      );\n    }\n\n    let resolveBusy;\n    const busyPromise: Promise<void> = new Promise((r) => {\n      resolveBusy = r;\n    });\n    this.exchangeBusyPromise = busyPromise;\n    let unresponsiveReached = false;\n    const timeout = setTimeout(() => {\n      unresponsiveReached = true;\n      this.emit(\"unresponsive\");\n    }, this.unresponsiveTimeout);\n\n    try {\n      const res = await f();\n\n      if (unresponsiveReached) {\n        this.emit(\"responsive\");\n      }\n\n      return res;\n    } finally {\n      clearTimeout(timeout);\n      if (resolveBusy) resolveBusy();\n      this.exchangeBusyPromise = null;\n    }\n  };\n\n  decorateAppAPIMethods(\n    self: Record<string, any>,\n    methods: Array<string>,\n    scrambleKey: string\n  ) {\n    for (const methodName of methods) {\n      self[methodName] = this.decorateAppAPIMethod(\n        methodName,\n        self[methodName],\n        self,\n        scrambleKey\n      );\n    }\n  }\n\n  _appAPIlock: string | null = null;\n\n  decorateAppAPIMethod<R, A extends any[]>(\n    methodName: string,\n    f: (...args: A) => Promise<R>,\n    ctx: any,\n    scrambleKey: string\n  ): (...args: A) => Promise<R> {\n    return async (...args) => {\n      const { _appAPIlock } = this;\n\n      if (_appAPIlock) {\n        return Promise.reject(\n          new TransportError(\n            \"Ledger Device is busy (lock \" + _appAPIlock + \")\",\n            \"TransportLocked\"\n          )\n        );\n      }\n\n      try {\n        this._appAPIlock = methodName;\n        this.setScrambleKey(scrambleKey);\n        return await f.apply(ctx, args);\n      } finally {\n        this._appAPIlock = null;\n      }\n    };\n  }\n\n  static ErrorMessage_ListenTimeout = \"No Ledger device found (timeout)\";\n  static ErrorMessage_NoDeviceFound = \"No Ledger device found\";\n}\n"], "mappings": ";;;;;;;;;AA2DA,SAAS,SAAS,OAAK;AACrB,SAAO,OAAO,UAAU;AAC1B;AA4EA,SAAS,gBAAgB,MAAU,MAAe;AAChD,QAAM,KAAS,CAAA;AACf,OAAK,KAAK,IAAI;AACd,aAAW,OAAO,OAAO,KAAK,IAAI,GAAG;AACnC,UAAM,QAAQ,KAAK,GAAG;AACtB,QAAI,OAAO,UAAU,YAAY;AAC/B;IACF;AACA,QAAI,CAAC,SAAS,OAAO,UAAU,UAAU;AACvC,SAAG,GAAG,IAAI;AACV;IACF;AACA,QAAI,KAAK,QAAQ,KAAK,GAAG,CAAC,MAAM,IAAI;AAClC,SAAG,GAAG,IAAI,gBAAgB,KAAK,GAAG,GAAG,KAAK,MAAM,CAAC,CAAC;AAClD;IACF;AACA,OAAG,GAAG,IAAI;EACZ;AACA,MAAI,OAAO,KAAK,SAAS,UAAU;AACjC,OAAG,OAAO,KAAK;EACjB;AACA,MAAI,OAAO,KAAK,YAAY,UAAU;AACpC,OAAG,UAAU,KAAK;EACpB;AACA,MAAI,OAAO,KAAK,UAAU,UAAU;AAClC,OAAG,QAAQ,KAAK;EAClB;AACA,SAAO;AACT;AArKA,IAKM,cACA,eAEO,4BAWA,wBA6CA,kBAqDA;AArHb;;AAKA,IAAM,eAAe,CAAA;AACrB,IAAM,gBAAgB,CAAA;AAEf,IAAM,6BAA6B,CAAC,MAAc,iBAAyC;AAChG,oBAAc,IAAI,IAAI;IACxB;AASO,IAAM,yBAAyB,CAIpC,SACK;MACL,MAAM,yBAAyB,MAAK;QAElC,YAAY,SAAkB,QAAY,SAAa;AAGrD,gBAAM,WAAW,MAAM,OAAO;AAJhC;AAME,iBAAO,eAAe,MAAM,iBAAiB,SAAS;AACtD,eAAK,OAAO;AACZ,cAAI,QAAQ;AACV,uBAAW,KAAK,QAAQ;AAGtB,mBAAK,CAAC,IAAI,OAAO,CAAC;YACpB;UACF;AAEA,cAAI,WAAW,SAAS,OAAO,KAAK,WAAW,WAAW,CAAC,KAAK,OAAO;AAGrE,kBAAM,QAAQ,QAAQ;AACtB,iBAAK,QAAQ;AACb,gBAAI,WAAW,OAAO;AACpB,mBAAK,QAAQ,KAAK,QAAQ,cAAc,MAAM;YAChD;UACF;QACF;;AAGF,mBAAa,IAAI,IAAI;AAErB,aAAO;IACT;AAOO,IAAM,mBAAmB,CAAC,WAAkC;AACjE,UAAI,UAAU,OAAO,WAAW,UAAU;AACxC,YAAI;AACF,cAAI,OAAO,OAAO,YAAY,UAAU;AACtC,kBAAM,MAAM,KAAK,MAAM,OAAO,OAAO;AACrC,gBAAI,IAAI,WAAW,IAAI,MAAM;AAC3B,uBAAS;YACX;UACF;QACF,SAAS,GAAG;QAEZ;AAEA,YAAI;AACJ,YAAI,OAAO,OAAO,SAAS,UAAU;AACnC,gBAAM,EAAE,KAAI,IAAK;AACjB,gBAAM,MAAM,cAAc,IAAI;AAC9B,cAAI,KAAK;AACP,oBAAQ,IAAI,MAAM;UACpB,OAAO;AACL,gBAAI,cAAc,SAAS,UAAU,QAAQ,aAAa,IAAI;AAE9D,gBAAI,CAAC,aAAa;AAChB,sBAAQ,KAAK,qCAAqC,OAAO,GAAG;AAC5D,4BAAc,uBAAuB,IAAI;YAC3C;AAEA,oBAAQ,OAAO,OAAO,YAAY,SAAS;AAC3C,gBAAI;AACF,yBAAW,QAAQ,QAAQ;AACzB,oBAAI,OAAO,eAAe,IAAI,GAAG;AAC/B,wBAAM,IAAI,IAAI,OAAO,IAAI;gBAC3B;cACF;YACF,SAAS,GAAG;YAEZ;UACF;QACF,OAAO;AACL,cAAI,OAAO,OAAO,YAAY,UAAU;AACtC,oBAAQ,IAAI,MAAM,OAAO,OAAO;UAClC;QACF;AAEA,YAAI,SAAS,CAAC,MAAM,SAAS,MAAM,mBAAmB;AACpD,gBAAM,kBAAkB,OAAO,gBAAgB;QACjD;AACA,eAAO;MACT;AACA,aAAO,IAAI,MAAM,OAAO,MAAM,CAAC;IACjC;AAGO,IAAM,iBAAiB,CAC5B,UAC2B;AAC3B,UAAI,CAAC;AAAO,eAAO;AACnB,UAAI,OAAO,UAAU,UAAU;AAC7B,eAAO,gBAAgB,OAAO,CAAA,CAAE;MAClC;AACA,UAAI,OAAO,UAAU,YAAY;AAC/B,eAAO,cAAc,MAAM,QAAQ,WAAW;MAChD;AACA,aAAO;IACT;;;;;AChIA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuTM,SAAU,oBAAoB,MAAY;AAC9C,UAAQ,MAAM;IAEZ,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;EACX;AACA,MAAI,SAAU,QAAQ,QAAQ,OAAQ;AACpC,WAAO;EACT;AACF;AA5UA,IAUa,0BACA,qBACA,sCAGA,gBACA,mBACA,iBACA,gBACA,sBACA,yBACA,sBAIA,6BACA,+BAGA,uBACA,2BACA,6BACA,qBACA,cACA,mBACA,kBACA,0BACA,oBACA,yBACA,oBACA,mCAGA,mCAGA,mCAGA,oBACA,gCAGA,qBACA,uBACA,eACA,eACA,gBACA,cACA,8CAGA,yBACA,+BAGA,YACA,gBACA,2BACA,uBACA,iCACA,0BACA,8BACA,gCAGA,0BACA,oCAGA,4BACA,wBACA,aACA,cACA,kBACA,kBACA,sBACA,sBACA,4BACA,iCAGA,2BACA,8CAGA,kBACA,cAEA,kBACA,kBACA,2BACA,2BACA,qBACA,mBACA,oBACA,6BACA,cACA,yBACA,wBACA,6BACA,uBACA,eACA,sBACA,0BACA,mBACA,0CAGA,uCAGA,qBACA,qBACA,oBACA,eACA,6BACA,oBACA,2BACA,yBACA,qBACA,WACA,oBACA,4BACA,gCAGA,wBACA,gCAGA,kCAGA,+BAGA,uBACA,0BACA,2BACA,uBACA,6BACA,6BACA,qBAEA,kBACA,gBACA,cACA,kBACA,aACA,YACA,kBACA,WACA,eACA,oBACA,oBAMA,cAIA,cAIA,6BAGA,mCAKA,uBACA,WAGA,kBAGA,eACA,iBACA,YAEA,qBACA,mCAUD,sBAcC,kBAiBA,gBAcA,aAoFA,sBAmCA,mBAWA;AAhYb;;;AAUO,IAAM,2BAA2B,uBAAuB,qBAAqB;AAC7E,IAAM,sBAAsB,uBAAuB,qBAAqB;AACxE,IAAM,uCAAuC,uBAClD,sCAAsC;AAEjC,IAAM,iBAAiB,uBAAuB,gBAAgB;AAC9D,IAAM,oBAAoB,uBAAuB,mBAAmB;AACpE,IAAM,kBAAkB,uBAAuB,iBAAiB;AAChE,IAAM,iBAAiB,uBAAuB,gBAAgB;AAC9D,IAAM,uBAAuB,uBAAuB,sBAAsB;AAC1E,IAAM,0BAA0B,uBAAuB,yBAAyB;AAChF,IAAM,uBAAuB,uBAGlC,sBAAsB;AACjB,IAAM,8BAA8B,uBAAuB,6BAA6B;AACxF,IAAM,gCAAgC,uBAC3C,+BAA+B;AAE1B,IAAM,wBAAwB,uBAAuB,kBAAkB;AACvE,IAAM,4BAA4B,uBAAuB,2BAA2B;AACpF,IAAM,8BAA8B,uBAAuB,6BAA6B;AACxF,IAAM,sBAAsB,uBAAuB,qBAAqB;AACxE,IAAM,eAAe,uBAAuB,cAAc;AAC1D,IAAM,oBAAoB,uBAAuB,mBAAmB;AACpE,IAAM,mBAAmB,uBAAuB,kBAAkB;AAClE,IAAM,2BAA2B,uBAAuB,0BAA0B;AAClF,IAAM,qBAAqB,uBAAuB,0BAA0B;AAC5E,IAAM,0BAA0B,uBAAuB,yBAAyB;AAChF,IAAM,qBAAqB,uBAAuB,oBAAoB;AACtE,IAAM,oCAAoC,uBAC/C,mCAAmC;AAE9B,IAAM,oCAAoC,uBAC/C,mCAAmC;AAE9B,IAAM,oCAAoC,uBAC/C,mCAAmC;AAE9B,IAAM,qBAAqB,uBAAuB,eAAe;AACjE,IAAM,iCAAiC,uBAC5C,gCAAgC;AAE3B,IAAM,sBAAsB,uBAAuB,qBAAqB;AACxE,IAAM,wBAAwB,uBAAuB,uBAAuB;AAC5E,IAAM,gBAAgB,uBAAuB,eAAe;AAC5D,IAAM,gBAAgB,uBAAuB,eAAe;AAC5D,IAAM,iBAAiB,uBAAuB,gBAAgB;AAC9D,IAAM,eAAe,uBAAuB,cAAc;AAC1D,IAAM,+CAA+C,uBAC1D,8CAA8C;AAEzC,IAAM,0BAA0B,uBAAuB,yBAAyB;AAChF,IAAM,gCAAgC,uBAC3C,+BAA+B;AAE1B,IAAM,aAAa,uBAAuB,YAAY;AACtD,IAAM,iBAAiB,uBAAuB,gBAAgB;AAC9D,IAAM,4BAA4B,uBAAuB,2BAA2B;AACpF,IAAM,wBAAwB,uBAAuB,uBAAuB;AAC5E,IAAM,kCAAkC,uBAAuB,4BAA4B;AAC3F,IAAM,2BAA2B,uBAAuB,qBAAqB;AAC7E,IAAM,+BAA+B,uBAAuB,8BAA8B;AAC1F,IAAM,iCAAiC,uBAC5C,gCAAgC;AAE3B,IAAM,2BAA2B,uBAAuB,qBAAqB;AAC7E,IAAM,qCAAqC,uBAChD,+BAA+B;AAE1B,IAAM,6BAA6B,uBAAuB,uBAAuB;AACjF,IAAM,yBAAyB,uBAAuB,wBAAwB;AAC9E,IAAM,cAAc,uBAAuB,aAAa;AACxD,IAAM,eAAe,uBAAuB,cAAc;AAC1D,IAAM,mBAAmB,uBAAuB,kBAAkB;AAClE,IAAM,mBAAmB,uBAAuB,kBAAkB;AAClE,IAAM,uBAAuB,uBAAuB,sBAAsB;AAC1E,IAAM,uBAAuB,uBAAuB,sBAAsB;AAC1E,IAAM,6BAA6B,uBAAuB,4BAA4B;AACtF,IAAM,kCAAkC,uBAC7C,iCAAiC;AAE5B,IAAM,4BAA4B,uBAAuB,2BAA2B;AACpF,IAAM,+CAA+C,uBAC1D,8CAA8C;AAEzC,IAAM,mBAAmB,uBAAuB,kBAAkB;AAClE,IAAM,eAAe,uBAAuB,cAAc;AAE1D,IAAM,mBAAmB,uBAAuB,kBAAkB;AAClE,IAAM,mBAAmB,uBAAuB,kBAAkB;AAClE,IAAM,4BAA4B,uBAAuB,2BAA2B;AACpF,IAAM,4BAA4B,uBAAuB,2BAA2B;AACpF,IAAM,sBAAsB,uBAAuB,qBAAqB;AACxE,IAAM,oBAAoB,uBAAuB,mBAAmB;AACpE,IAAM,qBAAqB,uBAAuB,oBAAoB;AACtE,IAAM,8BAA8B,uBAAuB,6BAA6B;AACxF,IAAM,eAAe,uBAAuB,cAAc;AAC1D,IAAM,0BAA0B,uBAAuB,oBAAoB;AAC3E,IAAM,yBAAyB,uBAAuB,mBAAmB;AACzE,IAAM,8BAA8B,uBAAuB,6BAA6B;AACxF,IAAM,wBAAwB,uBAAuB,uBAAuB;AAC5E,IAAM,gBAAgB,uBAAuB,eAAe;AAC5D,IAAM,uBAAuB,uBAAuB,sBAAsB;AAC1E,IAAM,2BAA2B,uBAAuB,0BAA0B;AAClF,IAAM,oBAAoB,uBAAuB,mBAAmB;AACpE,IAAM,2CAA2C,uBACtD,0CAA0C;AAErC,IAAM,wCAAwC,uBACnD,uCAAuC;AAElC,IAAM,sBAAsB,uBAAuB,qBAAqB;AACxE,IAAM,sBAAsB,uBAAuB,qBAAqB;AACxE,IAAM,qBAAqB,uBAAuB,oBAAoB;AACtE,IAAM,gBAAgB,uBAAuB,eAAe;AAC5D,IAAM,8BAA8B,uBAAuB,6BAA6B;AACxF,IAAM,qBAAqB,uBAAuB,oBAAoB;AACtE,IAAM,4BAA4B,uBAAuB,2BAA2B;AACpF,IAAM,0BAA0B,uBAAuB,yBAAyB;AAChF,IAAM,sBAAsB,uBAAuB,qBAAqB;AACxE,IAAM,YAAY,uBAAuB,WAAW;AACpD,IAAM,qBAAqB,uBAAuB,oBAAoB;AACtE,IAAM,6BAA6B,uBAAuB,4BAA4B;AACtF,IAAM,iCAAiC,uBAC5C,gCAAgC;AAE3B,IAAM,yBAAyB,uBAAuB,wBAAwB;AAC9E,IAAM,iCAAiC,uBAC5C,gCAAgC;AAE3B,IAAM,mCAAmC,uBAC9C,kCAAkC;AAE7B,IAAM,gCAAgC,uBAC3C,+BAA+B;AAE1B,IAAM,wBAAwB,uBAAuB,uBAAuB;AAC5E,IAAM,2BAA2B,uBAAuB,0BAA0B;AAClF,IAAM,4BAA4B,uBAAuB,2BAA2B;AACpF,IAAM,wBAAwB,uBAAuB,uBAAuB;AAC5E,IAAM,8BAA8B,uBAAuB,6BAA6B;AACxF,IAAM,8BAA8B,uBAAuB,6BAA6B;AACxF,IAAM,sBAAsB,uBAAuB,qBAAqB;AAExE,IAAM,mBAAmB,uBAAuB,kBAAkB;AAClE,IAAM,iBAAiB,uBAAuB,gBAAgB;AAC9D,IAAM,eAAe,uBAAuB,cAAc;AAC1D,IAAM,mBAAmB,uBAAuB,kBAAkB;AAClE,IAAM,cAAc,uBAAuB,aAAa;AACxD,IAAM,aAAa,uBAAuB,YAAY;AACtD,IAAM,mBAAmB,uBAAuB,kBAAkB;AAClE,IAAM,YAAY,uBAAuB,WAAW;AACpD,IAAM,gBAAgB,uBAAuB,eAAe;AAC5D,IAAM,qBAAqB,uBAAuB,oBAAoB;AACtE,IAAM,qBAAqB,uBAAuB,oBAAoB;AAMtE,IAAM,eAAe,uBAG1B,cAAc;AACT,IAAM,eAAe,uBAG1B,cAAc;AACT,IAAM,8BAA8B,uBAAuB,6BAA6B;AAGxF,IAAM,oCAAoC,uBAC/C,mCAAmC;AAI9B,IAAM,wBAAwB,uBAAuB,mBAAmB;AACxE,IAAM,YAAY,uBAAuB,WAAW;AAGpD,IAAM,mBAAmB,uBAAuB,kBAAkB;AAGlE,IAAM,gBAAgB,uBAAuB,eAAe;AAC5D,IAAM,kBAAkB,uBAAuB,iBAAiB;AAChE,IAAM,aAAa,uBAAuB,YAAY;AAEtD,IAAM,sBAAsB,uBAAuB,qBAAqB;AACxE,IAAM,oCAAoC,uBAC/C,mCAAmC;AASrC,KAAA,SAAYA,uBAAoB;AAC9B,MAAAA,sBAAA,SAAA,IAAA;AACA,MAAAA,sBAAA,0BAAA,IAAA;AACA,MAAAA,sBAAA,8BAAA,IAAA;AACA,MAAAA,sBAAA,0BAAA,IAAA;IACF,GALY,yBAAA,uBAAoB,CAAA,EAAA;AAc1B,IAAO,mBAAP,MAAO,0BAAyB,MAAK;MAGzC,YAAY,MAA4B,SAAe;AACrD,cAAM,OAAO;AAHf;AAIE,aAAK,OAAO;AACZ,aAAK,OAAO;AAGZ,eAAO,eAAe,MAAM,kBAAiB,SAAS;MACxD;;AAOI,IAAO,iBAAP,cAA8B,MAAK;MAEvC,YAAY,SAAiB,IAAU;AACrC,cAAM,OAAO;AACb,cAAM,WAAW,IAAI;AAHvB;AAIE,aAAK,OAAO;AACZ,aAAK,UAAU;AACf,aAAK,QAAQ,IAAI,MAAM,OAAO,EAAE;AAChC,aAAK,KAAK;MACZ;;AAGF,+BAA2B,kBAAkB,OAAK,IAAI,eAAe,EAAE,SAAS,EAAE,EAAE,CAAC;AAE9E,IAAM,cAAc;MACzB,gCAAgC;MAChC,yBAAyB;MACzB,mBAAmB;MACnB,cAAc;MACd,sBAAsB;MACtB,qCAAqC;MACrC,iCAAiC;MACjC,4BAA4B;MAC5B,kCAAkC;MAClC,yBAAyB;MACzB,oBAAoB;MACpB,qBAAqB;MACrB,gBAAgB;MAChB,gBAAgB;MAChB,QAAQ;MACR,mBAAmB;MACnB,gBAAgB;MAChB,kBAAkB;MAClB,iBAAiB;MACjB,mBAAmB;MACnB,sBAAsB;MACtB,wBAAwB;MACxB,aAAa;MACb,gBAAgB;MAChB,WAAW;MACX,eAAe;MACf,mBAAmB;MACnB,gBAAgB;MAChB,4BAA4B;MAC5B,gBAAgB;MAChB,yBAAyB;MACzB,IAAI;MACJ,wBAAwB;MACxB,2BAA2B;MAC3B,+BAA+B;MAC/B,mBAAmB;MACnB,cAAc;MACd,wBAAwB;MACxB,kBAAkB;MAClB,kCAAkC;MAClC,yBAAyB;MACzB,oBAAoB;MACpB,kCAAkC;MAClC,kCAAkC;MAClC,4BAA4B;MAC5B,sBAAsB;MACtB,aAAa;MACb,uBAAuB;MACvB,uBAAuB;MACvB,sBAAsB;MACtB,uBAAuB;;MAGvB,uBAAuB;;AA8BnB,IAAO,uBAAP,MAAO,8BAA6B,MAAK;;;;;;;MAU7C,YACE,YACA,EAAE,0BAA0B,KAAI,IAA4C,CAAA,GAAE;AAE9E,cAAM,aACJ,OAAO,KAAK,WAAW,EAAE,KAAK,OAAK,YAAY,CAAC,MAAM,UAAU,KAAK;AACvE,cAAM,OAAO,oBAAoB,UAAU,KAAK;AAChD,cAAM,gBAAgB,WAAW,SAAS,EAAE;AAC5C,cAAM,UAAU,kBAAkB,IAAI,OAAO,aAAa;AAE1D,cAAM,OAAO;AAnBf;AACA;AAmBE,aAAK,OAAO;AAEZ,aAAK,aAAa;AAClB,aAAK,aAAa;AAElB,eAAO,eAAe,MAAM,sBAAqB,SAAS;AAG1D,YAAI,2BAA2B,eAAe,YAAY,eAAe;AACvE,iBAAO,IAAI,kBAAkB,OAAO;QACtC;MACF;;AAGI,IAAO,oBAAP,MAAO,2BAA0B,qBAAoB;MACzD,YAAY,SAAgB;AAC1B,cAAM,YAAY,eAAe,EAAE,yBAAyB,MAAK,CAAE;AACnE,YAAI,SAAS;AACX,eAAK,UAAU;QACjB;AACA,aAAK,OAAO;AACZ,eAAO,eAAe,MAAM,mBAAkB,SAAS;MACzD;;AAGI,IAAO,0BAAP,MAAO,iCAAgC,MAAK;MAChD,YAAY,MAAc,SAAe;AACvC,cAAM,OAAO;AACb,aAAK,OAAO;AACZ,eAAO,eAAe,MAAM,yBAAwB,SAAS;MAC/D;;AAMF,+BAA2B,wBAAwB,OAAK,IAAI,qBAAqB,EAAE,UAAU,CAAC;;;;;AC3Y9F;AAAA;AAAA;AAuBA,QAAI,IAAI,OAAO,YAAY,WAAW,UAAU;AAChD,QAAI,eAAe,KAAK,OAAO,EAAE,UAAU,aACvC,EAAE,QACF,SAASC,cAAa,QAAQ,UAAU,MAAM;AAC9C,aAAO,SAAS,UAAU,MAAM,KAAK,QAAQ,UAAU,IAAI;AAAA,IAC7D;AAEF,QAAI;AACJ,QAAI,KAAK,OAAO,EAAE,YAAY,YAAY;AACxC,uBAAiB,EAAE;AAAA,IACrB,WAAW,OAAO,uBAAuB;AACvC,uBAAiB,SAASC,gBAAe,QAAQ;AAC/C,eAAO,OAAO,oBAAoB,MAAM,EACrC,OAAO,OAAO,sBAAsB,MAAM,CAAC;AAAA,MAChD;AAAA,IACF,OAAO;AACL,uBAAiB,SAASA,gBAAe,QAAQ;AAC/C,eAAO,OAAO,oBAAoB,MAAM;AAAA,MAC1C;AAAA,IACF;AAEA,aAAS,mBAAmB,SAAS;AACnC,UAAI,WAAW,QAAQ;AAAM,gBAAQ,KAAK,OAAO;AAAA,IACnD;AAEA,QAAI,cAAc,OAAO,SAAS,SAASC,aAAY,OAAO;AAC5D,aAAO,UAAU;AAAA,IACnB;AAEA,aAASC,gBAAe;AACtB,MAAAA,cAAa,KAAK,KAAK,IAAI;AAAA,IAC7B;AACA,WAAO,UAAUA;AACjB,WAAO,QAAQ,OAAO;AAGtB,IAAAA,cAAa,eAAeA;AAE5B,IAAAA,cAAa,UAAU,UAAU;AACjC,IAAAA,cAAa,UAAU,eAAe;AACtC,IAAAA,cAAa,UAAU,gBAAgB;AAIvC,QAAI,sBAAsB;AAE1B,aAAS,cAAc,UAAU;AAC/B,UAAI,OAAO,aAAa,YAAY;AAClC,cAAM,IAAI,UAAU,qEAAqE,OAAO,QAAQ;AAAA,MAC1G;AAAA,IACF;AAEA,WAAO,eAAeA,eAAc,uBAAuB;AAAA,MACzD,YAAY;AAAA,MACZ,KAAK,WAAW;AACd,eAAO;AAAA,MACT;AAAA,MACA,KAAK,SAAS,KAAK;AACjB,YAAI,OAAO,QAAQ,YAAY,MAAM,KAAK,YAAY,GAAG,GAAG;AAC1D,gBAAM,IAAI,WAAW,oGAAoG,MAAM,GAAG;AAAA,QACpI;AACA,8BAAsB;AAAA,MACxB;AAAA,IACF,CAAC;AAED,IAAAA,cAAa,OAAO,WAAW;AAE7B,UAAI,KAAK,YAAY,UACjB,KAAK,YAAY,OAAO,eAAe,IAAI,EAAE,SAAS;AACxD,aAAK,UAAU,uBAAO,OAAO,IAAI;AACjC,aAAK,eAAe;AAAA,MACtB;AAEA,WAAK,gBAAgB,KAAK,iBAAiB;AAAA,IAC7C;AAIA,IAAAA,cAAa,UAAU,kBAAkB,SAAS,gBAAgB,GAAG;AACnE,UAAI,OAAO,MAAM,YAAY,IAAI,KAAK,YAAY,CAAC,GAAG;AACpD,cAAM,IAAI,WAAW,kFAAkF,IAAI,GAAG;AAAA,MAChH;AACA,WAAK,gBAAgB;AACrB,aAAO;AAAA,IACT;AAEA,aAAS,iBAAiB,MAAM;AAC9B,UAAI,KAAK,kBAAkB;AACzB,eAAOA,cAAa;AACtB,aAAO,KAAK;AAAA,IACd;AAEA,IAAAA,cAAa,UAAU,kBAAkB,SAAS,kBAAkB;AAClE,aAAO,iBAAiB,IAAI;AAAA,IAC9B;AAEA,IAAAA,cAAa,UAAU,OAAO,SAAS,KAAK,MAAM;AAChD,UAAI,OAAO,CAAC;AACZ,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ;AAAK,aAAK,KAAK,UAAU,CAAC,CAAC;AACjE,UAAI,UAAW,SAAS;AAExB,UAAI,SAAS,KAAK;AAClB,UAAI,WAAW;AACb,kBAAW,WAAW,OAAO,UAAU;AAAA,eAChC,CAAC;AACR,eAAO;AAGT,UAAI,SAAS;AACX,YAAI;AACJ,YAAI,KAAK,SAAS;AAChB,eAAK,KAAK,CAAC;AACb,YAAI,cAAc,OAAO;AAGvB,gBAAM;AAAA,QACR;AAEA,YAAI,MAAM,IAAI,MAAM,sBAAsB,KAAK,OAAO,GAAG,UAAU,MAAM,GAAG;AAC5E,YAAI,UAAU;AACd,cAAM;AAAA,MACR;AAEA,UAAI,UAAU,OAAO,IAAI;AAEzB,UAAI,YAAY;AACd,eAAO;AAET,UAAI,OAAO,YAAY,YAAY;AACjC,qBAAa,SAAS,MAAM,IAAI;AAAA,MAClC,OAAO;AACL,YAAI,MAAM,QAAQ;AAClB,YAAI,YAAY,WAAW,SAAS,GAAG;AACvC,iBAAS,IAAI,GAAG,IAAI,KAAK,EAAE;AACzB,uBAAa,UAAU,CAAC,GAAG,MAAM,IAAI;AAAA,MACzC;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,aAAa,QAAQ,MAAM,UAAU,SAAS;AACrD,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,oBAAc,QAAQ;AAEtB,eAAS,OAAO;AAChB,UAAI,WAAW,QAAW;AACxB,iBAAS,OAAO,UAAU,uBAAO,OAAO,IAAI;AAC5C,eAAO,eAAe;AAAA,MACxB,OAAO;AAGL,YAAI,OAAO,gBAAgB,QAAW;AACpC,iBAAO;AAAA,YAAK;AAAA,YAAe;AAAA,YACf,SAAS,WAAW,SAAS,WAAW;AAAA,UAAQ;AAI5D,mBAAS,OAAO;AAAA,QAClB;AACA,mBAAW,OAAO,IAAI;AAAA,MACxB;AAEA,UAAI,aAAa,QAAW;AAE1B,mBAAW,OAAO,IAAI,IAAI;AAC1B,UAAE,OAAO;AAAA,MACX,OAAO;AACL,YAAI,OAAO,aAAa,YAAY;AAElC,qBAAW,OAAO,IAAI,IACpB,UAAU,CAAC,UAAU,QAAQ,IAAI,CAAC,UAAU,QAAQ;AAAA,QAExD,WAAW,SAAS;AAClB,mBAAS,QAAQ,QAAQ;AAAA,QAC3B,OAAO;AACL,mBAAS,KAAK,QAAQ;AAAA,QACxB;AAGA,YAAI,iBAAiB,MAAM;AAC3B,YAAI,IAAI,KAAK,SAAS,SAAS,KAAK,CAAC,SAAS,QAAQ;AACpD,mBAAS,SAAS;AAGlB,cAAI,IAAI,IAAI,MAAM,iDACE,SAAS,SAAS,MAAM,OAAO,IAAI,IAAI,mEAEvB;AACpC,YAAE,OAAO;AACT,YAAE,UAAU;AACZ,YAAE,OAAO;AACT,YAAE,QAAQ,SAAS;AACnB,6BAAmB,CAAC;AAAA,QACtB;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,IAAAA,cAAa,UAAU,cAAc,SAAS,YAAY,MAAM,UAAU;AACxE,aAAO,aAAa,MAAM,MAAM,UAAU,KAAK;AAAA,IACjD;AAEA,IAAAA,cAAa,UAAU,KAAKA,cAAa,UAAU;AAEnD,IAAAA,cAAa,UAAU,kBACnB,SAAS,gBAAgB,MAAM,UAAU;AACvC,aAAO,aAAa,MAAM,MAAM,UAAU,IAAI;AAAA,IAChD;AAEJ,aAAS,cAAc;AACrB,UAAI,CAAC,KAAK,OAAO;AACf,aAAK,OAAO,eAAe,KAAK,MAAM,KAAK,MAAM;AACjD,aAAK,QAAQ;AACb,YAAI,UAAU,WAAW;AACvB,iBAAO,KAAK,SAAS,KAAK,KAAK,MAAM;AACvC,eAAO,KAAK,SAAS,MAAM,KAAK,QAAQ,SAAS;AAAA,MACnD;AAAA,IACF;AAEA,aAAS,UAAU,QAAQ,MAAM,UAAU;AACzC,UAAI,QAAQ,EAAE,OAAO,OAAO,QAAQ,QAAW,QAAgB,MAAY,SAAmB;AAC9F,UAAI,UAAU,YAAY,KAAK,KAAK;AACpC,cAAQ,WAAW;AACnB,YAAM,SAAS;AACf,aAAO;AAAA,IACT;AAEA,IAAAA,cAAa,UAAU,OAAO,SAASC,MAAK,MAAM,UAAU;AAC1D,oBAAc,QAAQ;AACtB,WAAK,GAAG,MAAM,UAAU,MAAM,MAAM,QAAQ,CAAC;AAC7C,aAAO;AAAA,IACT;AAEA,IAAAD,cAAa,UAAU,sBACnB,SAAS,oBAAoB,MAAM,UAAU;AAC3C,oBAAc,QAAQ;AACtB,WAAK,gBAAgB,MAAM,UAAU,MAAM,MAAM,QAAQ,CAAC;AAC1D,aAAO;AAAA,IACT;AAGJ,IAAAA,cAAa,UAAU,iBACnB,SAAS,eAAe,MAAM,UAAU;AACtC,UAAI,MAAM,QAAQ,UAAU,GAAG;AAE/B,oBAAc,QAAQ;AAEtB,eAAS,KAAK;AACd,UAAI,WAAW;AACb,eAAO;AAET,aAAO,OAAO,IAAI;AAClB,UAAI,SAAS;AACX,eAAO;AAET,UAAI,SAAS,YAAY,KAAK,aAAa,UAAU;AACnD,YAAI,EAAE,KAAK,iBAAiB;AAC1B,eAAK,UAAU,uBAAO,OAAO,IAAI;AAAA,aAC9B;AACH,iBAAO,OAAO,IAAI;AAClB,cAAI,OAAO;AACT,iBAAK,KAAK,kBAAkB,MAAM,KAAK,YAAY,QAAQ;AAAA,QAC/D;AAAA,MACF,WAAW,OAAO,SAAS,YAAY;AACrC,mBAAW;AAEX,aAAK,IAAI,KAAK,SAAS,GAAG,KAAK,GAAG,KAAK;AACrC,cAAI,KAAK,CAAC,MAAM,YAAY,KAAK,CAAC,EAAE,aAAa,UAAU;AACzD,+BAAmB,KAAK,CAAC,EAAE;AAC3B,uBAAW;AACX;AAAA,UACF;AAAA,QACF;AAEA,YAAI,WAAW;AACb,iBAAO;AAET,YAAI,aAAa;AACf,eAAK,MAAM;AAAA,aACR;AACH,oBAAU,MAAM,QAAQ;AAAA,QAC1B;AAEA,YAAI,KAAK,WAAW;AAClB,iBAAO,IAAI,IAAI,KAAK,CAAC;AAEvB,YAAI,OAAO,mBAAmB;AAC5B,eAAK,KAAK,kBAAkB,MAAM,oBAAoB,QAAQ;AAAA,MAClE;AAEA,aAAO;AAAA,IACT;AAEJ,IAAAA,cAAa,UAAU,MAAMA,cAAa,UAAU;AAEpD,IAAAA,cAAa,UAAU,qBACnB,SAAS,mBAAmB,MAAM;AAChC,UAAI,WAAW,QAAQ;AAEvB,eAAS,KAAK;AACd,UAAI,WAAW;AACb,eAAO;AAGT,UAAI,OAAO,mBAAmB,QAAW;AACvC,YAAI,UAAU,WAAW,GAAG;AAC1B,eAAK,UAAU,uBAAO,OAAO,IAAI;AACjC,eAAK,eAAe;AAAA,QACtB,WAAW,OAAO,IAAI,MAAM,QAAW;AACrC,cAAI,EAAE,KAAK,iBAAiB;AAC1B,iBAAK,UAAU,uBAAO,OAAO,IAAI;AAAA;AAEjC,mBAAO,OAAO,IAAI;AAAA,QACtB;AACA,eAAO;AAAA,MACT;AAGA,UAAI,UAAU,WAAW,GAAG;AAC1B,YAAI,OAAO,OAAO,KAAK,MAAM;AAC7B,YAAI;AACJ,aAAK,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AAChC,gBAAM,KAAK,CAAC;AACZ,cAAI,QAAQ;AAAkB;AAC9B,eAAK,mBAAmB,GAAG;AAAA,QAC7B;AACA,aAAK,mBAAmB,gBAAgB;AACxC,aAAK,UAAU,uBAAO,OAAO,IAAI;AACjC,aAAK,eAAe;AACpB,eAAO;AAAA,MACT;AAEA,kBAAY,OAAO,IAAI;AAEvB,UAAI,OAAO,cAAc,YAAY;AACnC,aAAK,eAAe,MAAM,SAAS;AAAA,MACrC,WAAW,cAAc,QAAW;AAElC,aAAK,IAAI,UAAU,SAAS,GAAG,KAAK,GAAG,KAAK;AAC1C,eAAK,eAAe,MAAM,UAAU,CAAC,CAAC;AAAA,QACxC;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEJ,aAAS,WAAW,QAAQ,MAAM,QAAQ;AACxC,UAAI,SAAS,OAAO;AAEpB,UAAI,WAAW;AACb,eAAO,CAAC;AAEV,UAAI,aAAa,OAAO,IAAI;AAC5B,UAAI,eAAe;AACjB,eAAO,CAAC;AAEV,UAAI,OAAO,eAAe;AACxB,eAAO,SAAS,CAAC,WAAW,YAAY,UAAU,IAAI,CAAC,UAAU;AAEnE,aAAO,SACL,gBAAgB,UAAU,IAAI,WAAW,YAAY,WAAW,MAAM;AAAA,IAC1E;AAEA,IAAAA,cAAa,UAAU,YAAY,SAAS,UAAU,MAAM;AAC1D,aAAO,WAAW,MAAM,MAAM,IAAI;AAAA,IACpC;AAEA,IAAAA,cAAa,UAAU,eAAe,SAAS,aAAa,MAAM;AAChE,aAAO,WAAW,MAAM,MAAM,KAAK;AAAA,IACrC;AAEA,IAAAA,cAAa,gBAAgB,SAAS,SAAS,MAAM;AACnD,UAAI,OAAO,QAAQ,kBAAkB,YAAY;AAC/C,eAAO,QAAQ,cAAc,IAAI;AAAA,MACnC,OAAO;AACL,eAAO,cAAc,KAAK,SAAS,IAAI;AAAA,MACzC;AAAA,IACF;AAEA,IAAAA,cAAa,UAAU,gBAAgB;AACvC,aAAS,cAAc,MAAM;AAC3B,UAAI,SAAS,KAAK;AAElB,UAAI,WAAW,QAAW;AACxB,YAAI,aAAa,OAAO,IAAI;AAE5B,YAAI,OAAO,eAAe,YAAY;AACpC,iBAAO;AAAA,QACT,WAAW,eAAe,QAAW;AACnC,iBAAO,WAAW;AAAA,QACpB;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,IAAAA,cAAa,UAAU,aAAa,SAAS,aAAa;AACxD,aAAO,KAAK,eAAe,IAAI,eAAe,KAAK,OAAO,IAAI,CAAC;AAAA,IACjE;AAEA,aAAS,WAAW,KAAK,GAAG;AAC1B,UAAI,OAAO,IAAI,MAAM,CAAC;AACtB,eAAS,IAAI,GAAG,IAAI,GAAG,EAAE;AACvB,aAAK,CAAC,IAAI,IAAI,CAAC;AACjB,aAAO;AAAA,IACT;AAEA,aAAS,UAAU,MAAM,OAAO;AAC9B,aAAO,QAAQ,IAAI,KAAK,QAAQ;AAC9B,aAAK,KAAK,IAAI,KAAK,QAAQ,CAAC;AAC9B,WAAK,IAAI;AAAA,IACX;AAEA,aAAS,gBAAgB,KAAK;AAC5B,UAAI,MAAM,IAAI,MAAM,IAAI,MAAM;AAC9B,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AACnC,YAAI,CAAC,IAAI,IAAI,CAAC,EAAE,YAAY,IAAI,CAAC;AAAA,MACnC;AACA,aAAO;AAAA,IACT;AAEA,aAAS,KAAK,SAAS,MAAM;AAC3B,aAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,iBAAS,cAAc,KAAK;AAC1B,kBAAQ,eAAe,MAAM,QAAQ;AACrC,iBAAO,GAAG;AAAA,QACZ;AAEA,iBAAS,WAAW;AAClB,cAAI,OAAO,QAAQ,mBAAmB,YAAY;AAChD,oBAAQ,eAAe,SAAS,aAAa;AAAA,UAC/C;AACA,kBAAQ,CAAC,EAAE,MAAM,KAAK,SAAS,CAAC;AAAA,QAClC;AAAC;AAED,uCAA+B,SAAS,MAAM,UAAU,EAAE,MAAM,KAAK,CAAC;AACtE,YAAI,SAAS,SAAS;AACpB,wCAA8B,SAAS,eAAe,EAAE,MAAM,KAAK,CAAC;AAAA,QACtE;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,8BAA8B,SAAS,SAAS,OAAO;AAC9D,UAAI,OAAO,QAAQ,OAAO,YAAY;AACpC,uCAA+B,SAAS,SAAS,SAAS,KAAK;AAAA,MACjE;AAAA,IACF;AAEA,aAAS,+BAA+B,SAAS,MAAM,UAAU,OAAO;AACtE,UAAI,OAAO,QAAQ,OAAO,YAAY;AACpC,YAAI,MAAM,MAAM;AACd,kBAAQ,KAAK,MAAM,QAAQ;AAAA,QAC7B,OAAO;AACL,kBAAQ,GAAG,MAAM,QAAQ;AAAA,QAC3B;AAAA,MACF,WAAW,OAAO,QAAQ,qBAAqB,YAAY;AAGzD,gBAAQ,iBAAiB,MAAM,SAAS,aAAa,KAAK;AAGxD,cAAI,MAAM,MAAM;AACd,oBAAQ,oBAAoB,MAAM,YAAY;AAAA,UAChD;AACA,mBAAS,GAAG;AAAA,QACd,CAAC;AAAA,MACH,OAAO;AACL,cAAM,IAAI,UAAU,wEAAwE,OAAO,OAAO;AAAA,MAC5G;AAAA,IACF;AAAA;AAAA;;;AChfA,oBAAyB;AAEzB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkDA,IAAA;;EAAA,WAAA;AAAA,aAAAE,aAAA;AAAA,UAAA,QAAA;AACE,WAAA,kBAAkB;AAClB,WAAA,sBAAsB;AACtB,WAAA,cAA8C;AA8E9C,WAAA,UAAU,IAAI,cAAAC,QAAY;AAuD1B,WAAA,OAAO,SACL,KACA,KACA,IACA,IACA,MACA,YAA4C;AAD5C,YAAA,SAAA,QAAA;AAAA,iBAAe,OAAO,MAAM,CAAC;QAAC;AAC9B,YAAA,eAAA,QAAA;AAAA,uBAAA,CAA6B,YAAY,EAAE;QAAC;;;;;;AAE5C,oBAAI,KAAK,UAAU,KAAK;AACtB,wBAAM,IAAI,eACR,8CAA8C,KAAK,QACnD,kBAAkB;;AAIL,uBAAA,CAAA,GAAM,KAAK,SAC1B,OAAO,OAAO;kBACZ,OAAO,KAAK,CAAC,KAAK,KAAK,IAAI,EAAE,CAAC;kBAC9B,OAAO,KAAK,CAAC,KAAK,MAAM,CAAC;kBACzB;iBACD,CAAC,CACH;;AANK,2BAAW,GAAA,KAAA;AAOX,qBAAK,SAAS,aAAa,SAAS,SAAS,CAAC;AAEpD,oBAAI,CAAC,WAAW,KAAK,SAAC,GAAC;AAAK,yBAAA,MAAM;gBAAN,CAAQ,GAAG;AACrC,wBAAM,IAAI,qBAAqB,EAAE;;AAGnC,uBAAA,CAAA,GAAO,QAAQ;;;;;AAuDjB,WAAA,qBAAqB,SACnB,GAA+B;AAAA,eAAA,UAAA,OAAA,QAAA,QAAA,WAAA;;;;;;AAE/B,oBAAI,KAAK,qBAAqB;AAC5B,wBAAM,IAAI,uBACR,+EAA+E;;AAK7E,8BAA6B,IAAI,QAAQ,SAAC,GAAC;AAC/C,gCAAc;gBAChB,CAAC;AACD,qBAAK,sBAAsB;AACvB,sCAAsB;AACpB,0BAAU,WAAW,WAAA;AACzB,wCAAsB;AACtB,kBAAAC,OAAK,KAAK,cAAc;gBAC1B,GAAG,KAAK,mBAAmB;;;;AAGb,uBAAA,CAAA,GAAM,EAAC,CAAE;;AAAf,sBAAM,GAAA,KAAA;AAEZ,oBAAI,qBAAqB;AACvB,uBAAK,KAAK,YAAY;;AAGxB,uBAAA,CAAA,GAAO,GAAG;;AAEV,6BAAa,OAAO;AACpB,oBAAI;AAAa,8BAAW;AAC5B,qBAAK,sBAAsB;;;;;;;;;;;;;;AAmB/B,WAAA,cAA6B;IAgC/B;AA/OE,IAAAF,WAAA,UAAA,WAAA,SAAS,OAAa;AACpB,YAAM,IAAI,MAAM,0BAA0B;IAC5C;AAOA,IAAAA,WAAA,UAAA,iBAAA,SAAe,MAAY;IAAG;AAM9B,IAAAA,WAAA,UAAA,QAAA,WAAA;AACE,aAAO,QAAQ,QAAO;IACxB;AASA,IAAAA,WAAA,UAAA,KAAA,SAAG,WAAmB,IAAgC;AACpD,WAAK,QAAQ,GAAG,WAAW,EAAE;IAC/B;AAKA,IAAAA,WAAA,UAAA,MAAA,SAAI,WAAmB,IAAgC;AACrD,WAAK,QAAQ,eAAe,WAAW,EAAE;IAC3C;AAEA,IAAAA,WAAA,UAAA,OAAA,SAAK,OAAa;;AAAE,UAAA,OAAA,CAAA;eAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAY;AAAZ,aAAA,KAAA,CAAA,IAAA,UAAA,EAAA;;AAClB,OAAA,KAAA,KAAK,SAAQ,KAAI,MAAA,IAAA,cAAA,CAAC,KAAK,GAAA,OAAK,IAAI,GAAA,KAAA,CAAA;IAClC;AAKA,IAAAA,WAAA,UAAA,eAAA,WAAA;AACE,cAAQ,KACN,8FAA8F;IAElG;AAKA,IAAAA,WAAA,UAAA,qBAAA,SAAmB,iBAAuB;AACxC,WAAK,kBAAkB;IACzB;AAKA,IAAAA,WAAA,UAAA,iCAAA,SAA+B,qBAA2B;AACxD,WAAK,sBAAsB;IAC7B;AAkDO,IAAAA,WAAA,SAAP,SACE,aACA,eAAsB;AAFxB,UAAA,QAAA;AACE,UAAA,gBAAA,QAAA;AAAA,sBAAA;MAAkB;AAGlB,aAAO,IAAI,QAAQ,SAAC,SAAS,QAAM;AACjC,YAAI,QAAQ;AACZ,YAAM,MAAM,MAAK,OAAO;UACtB,MAAM,SAAC,GAAC;AACN,oBAAQ;AACR,gBAAI;AAAK,kBAAI,YAAW;AACxB,gBAAI;AAAiB,2BAAa,eAAe;AACjD,kBAAK,KAAK,EAAE,YAAY,WAAW,EAAE,KAAK,SAAS,MAAM;UAC3D;UACA,OAAO,SAAC,GAAC;AACP,gBAAI;AAAiB,2BAAa,eAAe;AACjD,mBAAO,CAAC;UACV;UACA,UAAU,WAAA;AACR,gBAAI;AAAiB,2BAAa,eAAe;AAEjD,gBAAI,CAAC,OAAO;AACV,qBACE,IAAI,eACF,MAAK,4BACL,eAAe,CAChB;;UAGP;SACD;AACD,YAAM,kBAAkB,gBACpB,WAAW,WAAA;AACT,cAAI,YAAW;AACf,iBACE,IAAI,eACF,MAAK,4BACL,eAAe,CAChB;QAEL,GAAG,aAAa,IAChB;MACN,CAAC;IACH;AAsCA,IAAAA,WAAA,UAAA,wBAAA,SACE,MACA,SACA,aAAmB;;;AAEnB,iBAAyB,YAAA,SAAA,OAAO,GAAA,cAAA,UAAA,KAAA,GAAA,CAAA,YAAA,MAAA,cAAA,UAAA,KAAA,GAAE;AAA7B,cAAM,aAAU,YAAA;AACnB,eAAK,UAAU,IAAI,KAAK,qBACtB,YACA,KAAK,UAAU,GACf,MACA,WAAW;;;;;;;;;;;;;IAGjB;AAIA,IAAAA,WAAA,UAAA,uBAAA,SACE,YACA,GACA,KACA,aAAmB;AAJrB,UAAA,QAAA;AAME,aAAO,WAAA;AAAO,YAAA,OAAA,CAAA;iBAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAO;AAAP,eAAA,EAAA,IAAA,UAAA,EAAA;;;;;;;AACJ,8BAAgB,KAAI;AAE5B,oBAAI,aAAa;AACf,yBAAA,CAAA,GAAO,QAAQ,OACb,IAAI,eACF,iCAAiC,cAAc,KAC/C,iBAAiB,CAClB,CACF;;;;;AAID,qBAAK,cAAc;AACnB,qBAAK,eAAe,WAAW;AACxB,uBAAA,CAAA,GAAM,EAAE,MAAM,KAAK,IAAI,CAAC;;AAA/B,uBAAA,CAAA,GAAO,GAAA,KAAA,CAAwB;;AAE/B,qBAAK,cAAc;;;;;;;;;;;;;;IAGzB;AAEO,IAAAA,WAAA,6BAA6B;AAC7B,IAAAA,WAAA,6BAA6B;AACtC,WAAAA;IA7SA;;wBAAqB;", "names": ["HwTransportErrorType", "ReflectApply", "ReflectOwnKeys", "NumberIsNaN", "EventEmitter", "once", "Transport", "EventEmitter", "_this"]}