{"version": 3, "sources": ["../../@solflare-wallet/sdk/node_modules/eventemitter3/index.js", "../../@solflare-wallet/sdk/lib/esm/index.js", "../../@solflare-wallet/sdk/node_modules/eventemitter3/index.mjs", "../../@solflare-wallet/sdk/lib/esm/adapters/base.js", "../../@solflare-wallet/sdk/lib/esm/adapters/WalletProvider.js", "../../@solflare-wallet/sdk/lib/esm/adapters/web.js", "../../@solflare-wallet/sdk/lib/esm/adapters/iframe.js", "../../@solflare-wallet/sdk/node_modules/uuid/dist/esm-browser/rng.js", "../../@solflare-wallet/sdk/node_modules/uuid/dist/esm-browser/regex.js", "../../@solflare-wallet/sdk/node_modules/uuid/dist/esm-browser/validate.js", "../../@solflare-wallet/sdk/node_modules/uuid/dist/esm-browser/stringify.js", "../../@solflare-wallet/sdk/node_modules/uuid/dist/esm-browser/parse.js", "../../@solflare-wallet/sdk/node_modules/uuid/dist/esm-browser/v35.js", "../../@solflare-wallet/sdk/node_modules/uuid/dist/esm-browser/md5.js", "../../@solflare-wallet/sdk/node_modules/uuid/dist/esm-browser/v3.js", "../../@solflare-wallet/sdk/node_modules/uuid/dist/esm-browser/native.js", "../../@solflare-wallet/sdk/node_modules/uuid/dist/esm-browser/v4.js", "../../@solflare-wallet/sdk/node_modules/uuid/dist/esm-browser/sha1.js", "../../@solflare-wallet/sdk/node_modules/uuid/dist/esm-browser/v5.js", "../../@solflare-wallet/sdk/lib/esm/utils.js", "../../@solflare-wallet/sdk/lib/esm/version.js"], "sourcesContent": ["'use strict';\n\nvar has = Object.prototype.hasOwnProperty\n  , prefix = '~';\n\n/**\n * Constructor to create a storage for our `EE` objects.\n * An `Events` instance is a plain object whose properties are event names.\n *\n * @constructor\n * @private\n */\nfunction Events() {}\n\n//\n// We try to not inherit from `Object.prototype`. In some engines creating an\n// instance in this way is faster than calling `Object.create(null)` directly.\n// If `Object.create(null)` is not supported we prefix the event names with a\n// character to make sure that the built-in object properties are not\n// overridden or used as an attack vector.\n//\nif (Object.create) {\n  Events.prototype = Object.create(null);\n\n  //\n  // This hack is needed because the `__proto__` property is still inherited in\n  // some old browsers like Android 4, iPhone 5.1, Opera 11 and Safari 5.\n  //\n  if (!new Events().__proto__) prefix = false;\n}\n\n/**\n * Representation of a single event listener.\n *\n * @param {Function} fn The listener function.\n * @param {*} context The context to invoke the listener with.\n * @param {Boolean} [once=false] Specify if the listener is a one-time listener.\n * @constructor\n * @private\n */\nfunction EE(fn, context, once) {\n  this.fn = fn;\n  this.context = context;\n  this.once = once || false;\n}\n\n/**\n * Add a listener for a given event.\n *\n * @param {EventEmitter} emitter Reference to the `EventEmitter` instance.\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} context The context to invoke the listener with.\n * @param {Boolean} once Specify if the listener is a one-time listener.\n * @returns {EventEmitter}\n * @private\n */\nfunction addListener(emitter, event, fn, context, once) {\n  if (typeof fn !== 'function') {\n    throw new TypeError('The listener must be a function');\n  }\n\n  var listener = new EE(fn, context || emitter, once)\n    , evt = prefix ? prefix + event : event;\n\n  if (!emitter._events[evt]) emitter._events[evt] = listener, emitter._eventsCount++;\n  else if (!emitter._events[evt].fn) emitter._events[evt].push(listener);\n  else emitter._events[evt] = [emitter._events[evt], listener];\n\n  return emitter;\n}\n\n/**\n * Clear event by name.\n *\n * @param {EventEmitter} emitter Reference to the `EventEmitter` instance.\n * @param {(String|Symbol)} evt The Event name.\n * @private\n */\nfunction clearEvent(emitter, evt) {\n  if (--emitter._eventsCount === 0) emitter._events = new Events();\n  else delete emitter._events[evt];\n}\n\n/**\n * Minimal `EventEmitter` interface that is molded against the Node.js\n * `EventEmitter` interface.\n *\n * @constructor\n * @public\n */\nfunction EventEmitter() {\n  this._events = new Events();\n  this._eventsCount = 0;\n}\n\n/**\n * Return an array listing the events for which the emitter has registered\n * listeners.\n *\n * @returns {Array}\n * @public\n */\nEventEmitter.prototype.eventNames = function eventNames() {\n  var names = []\n    , events\n    , name;\n\n  if (this._eventsCount === 0) return names;\n\n  for (name in (events = this._events)) {\n    if (has.call(events, name)) names.push(prefix ? name.slice(1) : name);\n  }\n\n  if (Object.getOwnPropertySymbols) {\n    return names.concat(Object.getOwnPropertySymbols(events));\n  }\n\n  return names;\n};\n\n/**\n * Return the listeners registered for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Array} The registered listeners.\n * @public\n */\nEventEmitter.prototype.listeners = function listeners(event) {\n  var evt = prefix ? prefix + event : event\n    , handlers = this._events[evt];\n\n  if (!handlers) return [];\n  if (handlers.fn) return [handlers.fn];\n\n  for (var i = 0, l = handlers.length, ee = new Array(l); i < l; i++) {\n    ee[i] = handlers[i].fn;\n  }\n\n  return ee;\n};\n\n/**\n * Return the number of listeners listening to a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Number} The number of listeners.\n * @public\n */\nEventEmitter.prototype.listenerCount = function listenerCount(event) {\n  var evt = prefix ? prefix + event : event\n    , listeners = this._events[evt];\n\n  if (!listeners) return 0;\n  if (listeners.fn) return 1;\n  return listeners.length;\n};\n\n/**\n * Calls each of the listeners registered for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Boolean} `true` if the event had listeners, else `false`.\n * @public\n */\nEventEmitter.prototype.emit = function emit(event, a1, a2, a3, a4, a5) {\n  var evt = prefix ? prefix + event : event;\n\n  if (!this._events[evt]) return false;\n\n  var listeners = this._events[evt]\n    , len = arguments.length\n    , args\n    , i;\n\n  if (listeners.fn) {\n    if (listeners.once) this.removeListener(event, listeners.fn, undefined, true);\n\n    switch (len) {\n      case 1: return listeners.fn.call(listeners.context), true;\n      case 2: return listeners.fn.call(listeners.context, a1), true;\n      case 3: return listeners.fn.call(listeners.context, a1, a2), true;\n      case 4: return listeners.fn.call(listeners.context, a1, a2, a3), true;\n      case 5: return listeners.fn.call(listeners.context, a1, a2, a3, a4), true;\n      case 6: return listeners.fn.call(listeners.context, a1, a2, a3, a4, a5), true;\n    }\n\n    for (i = 1, args = new Array(len -1); i < len; i++) {\n      args[i - 1] = arguments[i];\n    }\n\n    listeners.fn.apply(listeners.context, args);\n  } else {\n    var length = listeners.length\n      , j;\n\n    for (i = 0; i < length; i++) {\n      if (listeners[i].once) this.removeListener(event, listeners[i].fn, undefined, true);\n\n      switch (len) {\n        case 1: listeners[i].fn.call(listeners[i].context); break;\n        case 2: listeners[i].fn.call(listeners[i].context, a1); break;\n        case 3: listeners[i].fn.call(listeners[i].context, a1, a2); break;\n        case 4: listeners[i].fn.call(listeners[i].context, a1, a2, a3); break;\n        default:\n          if (!args) for (j = 1, args = new Array(len -1); j < len; j++) {\n            args[j - 1] = arguments[j];\n          }\n\n          listeners[i].fn.apply(listeners[i].context, args);\n      }\n    }\n  }\n\n  return true;\n};\n\n/**\n * Add a listener for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} [context=this] The context to invoke the listener with.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.on = function on(event, fn, context) {\n  return addListener(this, event, fn, context, false);\n};\n\n/**\n * Add a one-time listener for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} [context=this] The context to invoke the listener with.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.once = function once(event, fn, context) {\n  return addListener(this, event, fn, context, true);\n};\n\n/**\n * Remove the listeners of a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn Only remove the listeners that match this function.\n * @param {*} context Only remove the listeners that have this context.\n * @param {Boolean} once Only remove one-time listeners.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.removeListener = function removeListener(event, fn, context, once) {\n  var evt = prefix ? prefix + event : event;\n\n  if (!this._events[evt]) return this;\n  if (!fn) {\n    clearEvent(this, evt);\n    return this;\n  }\n\n  var listeners = this._events[evt];\n\n  if (listeners.fn) {\n    if (\n      listeners.fn === fn &&\n      (!once || listeners.once) &&\n      (!context || listeners.context === context)\n    ) {\n      clearEvent(this, evt);\n    }\n  } else {\n    for (var i = 0, events = [], length = listeners.length; i < length; i++) {\n      if (\n        listeners[i].fn !== fn ||\n        (once && !listeners[i].once) ||\n        (context && listeners[i].context !== context)\n      ) {\n        events.push(listeners[i]);\n      }\n    }\n\n    //\n    // Reset the array, or remove it completely if we have no more listeners.\n    //\n    if (events.length) this._events[evt] = events.length === 1 ? events[0] : events;\n    else clearEvent(this, evt);\n  }\n\n  return this;\n};\n\n/**\n * Remove all listeners, or those of the specified event.\n *\n * @param {(String|Symbol)} [event] The event name.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.removeAllListeners = function removeAllListeners(event) {\n  var evt;\n\n  if (event) {\n    evt = prefix ? prefix + event : event;\n    if (this._events[evt]) clearEvent(this, evt);\n  } else {\n    this._events = new Events();\n    this._eventsCount = 0;\n  }\n\n  return this;\n};\n\n//\n// Alias methods names because people roll like that.\n//\nEventEmitter.prototype.off = EventEmitter.prototype.removeListener;\nEventEmitter.prototype.addListener = EventEmitter.prototype.on;\n\n//\n// Expose the prefix.\n//\nEventEmitter.prefixed = prefix;\n\n//\n// Allow `EventEmitter` to be imported as module namespace.\n//\nEventEmitter.EventEmitter = EventEmitter;\n\n//\n// Expose the module.\n//\nif ('undefined' !== typeof module) {\n  module.exports = EventEmitter;\n}\n", "var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport { Transaction, VersionedTransaction } from '@solana/web3.js';\nimport EventEmitter from 'eventemitter3';\nimport WebAdapter from './adapters/web';\nimport IframeAdapter from './adapters/iframe';\nimport { isLegacyTransactionInstance } from './utils';\nimport { VERSION } from './version';\nvar Solflare = /** @class */ (function (_super) {\n    __extends(Solflare, _super);\n    function Solflare(config) {\n        var _this = _super.call(this) || this;\n        _this._network = 'mainnet-beta';\n        _this._provider = null;\n        _this._iframeParams = {};\n        _this._adapterInstance = null;\n        _this._element = null;\n        _this._iframe = null;\n        _this._connectHandler = null;\n        _this._flutterHandlerInterval = null;\n        _this._handleEvent = function (event) {\n            var _a, _b, _c, _d;\n            switch (event.type) {\n                case 'connect_native_web': {\n                    _this._collapseIframe();\n                    _this._adapterInstance = new WebAdapter(_this._iframe, _this._network, ((_a = event.data) === null || _a === void 0 ? void 0 : _a.provider) || _this._provider || 'https://solflare.com/provider');\n                    _this._adapterInstance.on('connect', _this._webConnected);\n                    _this._adapterInstance.on('disconnect', _this._webDisconnected);\n                    _this._adapterInstance.connect();\n                    _this._setPreferredAdapter('native_web');\n                    return;\n                }\n                case 'connect': {\n                    _this._collapseIframe();\n                    _this._adapterInstance = new IframeAdapter(_this._iframe, ((_b = event.data) === null || _b === void 0 ? void 0 : _b.publicKey) || '');\n                    _this._adapterInstance.connect();\n                    _this._setPreferredAdapter((_c = event.data) === null || _c === void 0 ? void 0 : _c.adapter);\n                    if (_this._connectHandler) {\n                        _this._connectHandler.resolve();\n                        _this._connectHandler = null;\n                    }\n                    _this.emit('connect', _this.publicKey);\n                    return;\n                }\n                case 'disconnect': {\n                    if (_this._connectHandler) {\n                        _this._connectHandler.reject();\n                        _this._connectHandler = null;\n                    }\n                    _this._disconnected();\n                    _this.emit('disconnect');\n                    return;\n                }\n                case 'accountChanged': {\n                    if ((_d = event.data) === null || _d === void 0 ? void 0 : _d.publicKey) {\n                        _this._adapterInstance = new IframeAdapter(_this._iframe, event.data.publicKey);\n                        _this._adapterInstance.connect();\n                        _this.emit('accountChanged', _this.publicKey);\n                    }\n                    else {\n                        _this.emit('accountChanged', undefined);\n                    }\n                    return;\n                }\n                // legacy event, use resize message type instead\n                case 'collapse': {\n                    _this._collapseIframe();\n                    return;\n                }\n                default: {\n                    return;\n                }\n            }\n        };\n        _this._handleResize = function (data) {\n            if (data.resizeMode === 'full') {\n                if (data.params.mode === 'fullscreen') {\n                    _this._expandIframe();\n                }\n                else if (data.params.mode === 'hide') {\n                    _this._collapseIframe();\n                }\n            }\n            else if (data.resizeMode === 'coordinates') {\n                if (_this._iframe) {\n                    _this._iframe.style.top = isFinite(data.params.top) ? \"\".concat(data.params.top, \"px\") : '';\n                    _this._iframe.style.bottom = isFinite(data.params.bottom) ? \"\".concat(data.params.bottom, \"px\") : '';\n                    _this._iframe.style.left = isFinite(data.params.left) ? \"\".concat(data.params.left, \"px\") : '';\n                    _this._iframe.style.right = isFinite(data.params.right) ? \"\".concat(data.params.right, \"px\") : '';\n                    _this._iframe.style.width = isFinite(data.params.width) ? \"\".concat(data.params.width, \"px\") : data.params.width;\n                    _this._iframe.style.height = isFinite(data.params.height) ? \"\".concat(data.params.height, \"px\") : data.params.height;\n                }\n            }\n        };\n        _this._handleMessage = function (event) {\n            var _a;\n            if (((_a = event.data) === null || _a === void 0 ? void 0 : _a.channel) !== 'solflareIframeToWalletAdapter') {\n                return;\n            }\n            var data = event.data.data || {};\n            if (data.type === 'event') {\n                _this._handleEvent(data.event);\n            }\n            else if (data.type === 'resize') {\n                _this._handleResize(data);\n            }\n            else if (data.type === 'response') {\n                if (_this._adapterInstance) {\n                    _this._adapterInstance.handleMessage(data);\n                }\n            }\n        };\n        _this._removeElement = function () {\n            if (_this._flutterHandlerInterval !== null) {\n                clearInterval(_this._flutterHandlerInterval);\n                _this._flutterHandlerInterval = null;\n            }\n            if (_this._element) {\n                _this._element.remove();\n                _this._element = null;\n            }\n        };\n        _this._removeDanglingElements = function () {\n            var e_1, _a;\n            var elements = document.getElementsByClassName('solflare-wallet-adapter-iframe');\n            try {\n                for (var elements_1 = __values(elements), elements_1_1 = elements_1.next(); !elements_1_1.done; elements_1_1 = elements_1.next()) {\n                    var element = elements_1_1.value;\n                    if (element.parentElement) {\n                        element.remove();\n                    }\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (elements_1_1 && !elements_1_1.done && (_a = elements_1.return)) _a.call(elements_1);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n        };\n        _this._injectElement = function () {\n            _this._removeElement();\n            _this._removeDanglingElements();\n            var params = __assign(__assign({}, _this._iframeParams), { cluster: _this._network || 'mainnet-beta', origin: window.location.origin || '', title: document.title || '', version: 1, sdkVersion: VERSION || 'unknown' });\n            var preferredAdapter = _this._getPreferredAdapter();\n            if (preferredAdapter) {\n                params.adapter = preferredAdapter;\n            }\n            if (_this._provider) {\n                params.provider = _this._provider;\n            }\n            var queryString = Object.keys(params)\n                .map(function (key) { return \"\".concat(key, \"=\").concat(encodeURIComponent(params[key])); })\n                .join('&');\n            var iframeUrl = \"\".concat(Solflare.IFRAME_URL, \"?\").concat(queryString);\n            _this._element = document.createElement('div');\n            _this._element.className = 'solflare-wallet-adapter-iframe';\n            _this._element.innerHTML = \"\\n      <iframe src='\".concat(iframeUrl, \"' referrerPolicy='strict-origin-when-cross-origin' style='position: fixed; top: 0; bottom: 0; left: 0; right: 0; width: 100%; height: 100%; border: none; border-radius: 0; z-index: 99999; color-scheme: auto;' allowtransparency='true'></iframe>\\n    \");\n            document.body.appendChild(_this._element);\n            _this._iframe = _this._element.querySelector('iframe');\n            // @ts-ignore\n            window.fromFlutter = _this._handleMobileMessage;\n            _this._flutterHandlerInterval = setInterval(function () {\n                // @ts-ignore\n                window.fromFlutter = _this._handleMobileMessage;\n            }, 100);\n            window.addEventListener('message', _this._handleMessage, false);\n        };\n        _this._collapseIframe = function () {\n            if (_this._iframe) {\n                _this._iframe.style.top = '';\n                _this._iframe.style.right = '';\n                _this._iframe.style.height = '2px';\n                _this._iframe.style.width = '2px';\n            }\n        };\n        _this._expandIframe = function () {\n            if (_this._iframe) {\n                _this._iframe.style.top = '0px';\n                _this._iframe.style.bottom = '0px';\n                _this._iframe.style.left = '0px';\n                _this._iframe.style.right = '0px';\n                _this._iframe.style.width = '100%';\n                _this._iframe.style.height = '100%';\n            }\n        };\n        _this._getPreferredAdapter = function () {\n            if (localStorage) {\n                return localStorage.getItem('solflarePreferredWalletAdapter') || null;\n            }\n            return null;\n        };\n        _this._setPreferredAdapter = function (adapter) {\n            if (localStorage && adapter) {\n                localStorage.setItem('solflarePreferredWalletAdapter', adapter);\n            }\n        };\n        _this._clearPreferredAdapter = function () {\n            if (localStorage) {\n                localStorage.removeItem('solflarePreferredWalletAdapter');\n            }\n        };\n        _this._webConnected = function () {\n            if (_this._connectHandler) {\n                _this._connectHandler.resolve();\n                _this._connectHandler = null;\n            }\n            _this.emit('connect', _this.publicKey);\n        };\n        _this._webDisconnected = function () {\n            if (_this._connectHandler) {\n                _this._connectHandler.reject();\n                _this._connectHandler = null;\n            }\n            _this._disconnected();\n            _this.emit('disconnect');\n        };\n        _this._disconnected = function () {\n            window.removeEventListener('message', _this._handleMessage, false);\n            _this._removeElement();\n            _this._clearPreferredAdapter();\n            _this._adapterInstance = null;\n        };\n        _this._handleMobileMessage = function (data) {\n            var _a, _b;\n            (_b = (_a = _this._iframe) === null || _a === void 0 ? void 0 : _a.contentWindow) === null || _b === void 0 ? void 0 : _b.postMessage({\n                channel: 'solflareMobileToIframe',\n                data: data\n            }, '*');\n        };\n        if (config === null || config === void 0 ? void 0 : config.network) {\n            _this._network = config === null || config === void 0 ? void 0 : config.network;\n        }\n        if (config === null || config === void 0 ? void 0 : config.provider) {\n            _this._provider = config === null || config === void 0 ? void 0 : config.provider;\n        }\n        if (config === null || config === void 0 ? void 0 : config.params) {\n            _this._iframeParams = __assign({}, config === null || config === void 0 ? void 0 : config.params);\n        }\n        return _this;\n    }\n    Object.defineProperty(Solflare.prototype, \"publicKey\", {\n        get: function () {\n            var _a;\n            return ((_a = this._adapterInstance) === null || _a === void 0 ? void 0 : _a.publicKey) || null;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Solflare.prototype, \"isConnected\", {\n        get: function () {\n            var _a;\n            return !!((_a = this._adapterInstance) === null || _a === void 0 ? void 0 : _a.connected);\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Solflare.prototype, \"connected\", {\n        get: function () {\n            return this.isConnected;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Solflare.prototype, \"autoApprove\", {\n        get: function () {\n            return false;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Solflare.prototype.connect = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            var _this = this;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (this.connected) {\n                            return [2 /*return*/];\n                        }\n                        this._injectElement();\n                        return [4 /*yield*/, new Promise(function (resolve, reject) {\n                                _this._connectHandler = { resolve: resolve, reject: reject };\n                            })];\n                    case 1:\n                        _a.sent();\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    Solflare.prototype.disconnect = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this._adapterInstance) {\n                            return [2 /*return*/];\n                        }\n                        return [4 /*yield*/, this._adapterInstance.disconnect()];\n                    case 1:\n                        _a.sent();\n                        this._disconnected();\n                        this.emit('disconnect');\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    Solflare.prototype.signTransaction = function (transaction) {\n        return __awaiter(this, void 0, void 0, function () {\n            var serializedTransaction, signedTransaction;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        serializedTransaction = isLegacyTransactionInstance(transaction) ?\n                            Uint8Array.from(transaction.serialize({ verifySignatures: false, requireAllSignatures: false })) :\n                            transaction.serialize();\n                        return [4 /*yield*/, this._adapterInstance.signTransaction(serializedTransaction)];\n                    case 1:\n                        signedTransaction = _a.sent();\n                        return [2 /*return*/, isLegacyTransactionInstance(transaction) ? Transaction.from(signedTransaction) : VersionedTransaction.deserialize(signedTransaction)];\n                }\n            });\n        });\n    };\n    Solflare.prototype.signAllTransactions = function (transactions) {\n        return __awaiter(this, void 0, void 0, function () {\n            var serializedTransactions, signedTransactions;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        serializedTransactions = transactions.map(function (transaction) {\n                            return isLegacyTransactionInstance(transaction) ?\n                                Uint8Array.from(transaction.serialize({ verifySignatures: false, requireAllSignatures: false })) :\n                                transaction.serialize();\n                        });\n                        return [4 /*yield*/, this._adapterInstance.signAllTransactions(serializedTransactions)];\n                    case 1:\n                        signedTransactions = _a.sent();\n                        if (signedTransactions.length !== transactions.length) {\n                            throw new Error('Failed to sign all transactions');\n                        }\n                        return [2 /*return*/, signedTransactions.map(function (signedTransaction, index) {\n                                return isLegacyTransactionInstance(transactions[index]) ? Transaction.from(signedTransaction) : VersionedTransaction.deserialize(signedTransaction);\n                            })];\n                }\n            });\n        });\n    };\n    Solflare.prototype.signAndSendTransaction = function (transaction, options) {\n        return __awaiter(this, void 0, void 0, function () {\n            var serializedTransaction;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        serializedTransaction = isLegacyTransactionInstance(transaction) ? transaction.serialize({ verifySignatures: false, requireAllSignatures: false }) : transaction.serialize();\n                        return [4 /*yield*/, this._adapterInstance.signAndSendTransaction(serializedTransaction, options)];\n                    case 1: return [2 /*return*/, _a.sent()];\n                }\n            });\n        });\n    };\n    Solflare.prototype.signMessage = function (data, display) {\n        if (display === void 0) { display = 'utf8'; }\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        return [4 /*yield*/, this._adapterInstance.signMessage(data, display)];\n                    case 1: return [2 /*return*/, _a.sent()];\n                }\n            });\n        });\n    };\n    Solflare.prototype.sign = function (data, display) {\n        if (display === void 0) { display = 'utf8'; }\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0: return [4 /*yield*/, this.signMessage(data, display)];\n                    case 1: return [2 /*return*/, _a.sent()];\n                }\n            });\n        });\n    };\n    Solflare.prototype.detectWallet = function (timeout) {\n        var _a;\n        if (timeout === void 0) { timeout = 10; }\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_b) {\n                if (window.SolflareApp || ((_a = window.solflare) === null || _a === void 0 ? void 0 : _a.isSolflare)) {\n                    return [2 /*return*/, true];\n                }\n                return [2 /*return*/, new Promise(function (resolve) {\n                        var pollInterval, pollTimeout;\n                        pollInterval = setInterval(function () {\n                            var _a;\n                            if (window.SolflareApp || ((_a = window.solflare) === null || _a === void 0 ? void 0 : _a.isSolflare)) {\n                                clearInterval(pollInterval);\n                                clearTimeout(pollTimeout);\n                                resolve(true);\n                            }\n                        }, 500);\n                        pollTimeout = setTimeout(function () {\n                            clearInterval(pollInterval);\n                            resolve(false);\n                        }, timeout * 1000);\n                    })];\n            });\n        });\n    };\n    Solflare.IFRAME_URL = 'https://connect.solflare.com/';\n    return Solflare;\n}(EventEmitter));\nexport default Solflare;\n", "import EventEmitter from './index.js'\n\nexport { EventEmitter }\nexport default EventEmitter\n", "var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport EventEmitter from 'eventemitter3';\nvar WalletAdapter = /** @class */ (function (_super) {\n    __extends(WalletAdapter, _super);\n    function WalletAdapter() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    return WalletAdapter;\n}(EventEmitter));\nexport default WalletAdapter;\n", "var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nimport EventEmitter from 'eventemitter3';\nimport { PublicKey } from '@solana/web3.js';\nimport bs58 from 'bs58';\nvar Wallet = /** @class */ (function (_super) {\n    __extends(Wallet, _super);\n    function Wallet(provider, network) {\n        var _this = _super.call(this) || this;\n        _this._handleMessage = function (e) {\n            if ((_this._injectedProvider && e.source === window) ||\n                (e.origin === _this._providerUrl.origin && e.source === _this._popup)) {\n                if (e.data.method === 'connected') {\n                    var newPublicKey = new PublicKey(e.data.params.publicKey);\n                    if (!_this._publicKey || !_this._publicKey.equals(newPublicKey)) {\n                        if (_this._publicKey && !_this._publicKey.equals(newPublicKey)) {\n                            _this._handleDisconnect();\n                        }\n                        _this._publicKey = newPublicKey;\n                        _this._autoApprove = !!e.data.params.autoApprove;\n                        _this.emit('connect', _this._publicKey);\n                    }\n                }\n                else if (e.data.method === 'disconnected') {\n                    _this._handleDisconnect();\n                }\n                else if (e.data.result || e.data.error) {\n                    if (_this._responsePromises.has(e.data.id)) {\n                        var _a = __read(_this._responsePromises.get(e.data.id), 2), resolve = _a[0], reject = _a[1];\n                        if (e.data.result) {\n                            resolve(e.data.result);\n                        }\n                        else {\n                            reject(new Error(e.data.error));\n                        }\n                    }\n                }\n            }\n        };\n        _this._handleConnect = function () {\n            if (!_this._handlerAdded) {\n                _this._handlerAdded = true;\n                window.addEventListener('message', _this._handleMessage);\n                window.addEventListener('beforeunload', _this.disconnect);\n            }\n            if (_this._injectedProvider) {\n                return new Promise(function (resolve) {\n                    _this._sendRequest('connect', {});\n                    resolve();\n                });\n            }\n            else {\n                window.name = 'parent';\n                _this._popup = window.open(_this._providerUrl.toString(), '_blank', 'location,resizable,width=460,height=675');\n                return new Promise(function (resolve) {\n                    _this.once('connect', resolve);\n                });\n            }\n        };\n        _this._handleDisconnect = function () {\n            if (_this._handlerAdded) {\n                _this._handlerAdded = false;\n                window.removeEventListener('message', _this._handleMessage);\n                window.removeEventListener('beforeunload', _this.disconnect);\n            }\n            if (_this._publicKey) {\n                _this._publicKey = null;\n                _this.emit('disconnect');\n            }\n            _this._responsePromises.forEach(function (_a, id) {\n                var _b = __read(_a, 2), resolve = _b[0], reject = _b[1];\n                _this._responsePromises.delete(id);\n                reject('Wallet disconnected');\n            });\n        };\n        _this._sendRequest = function (method, params) { return __awaiter(_this, void 0, void 0, function () {\n            var requestId;\n            var _this = this;\n            return __generator(this, function (_a) {\n                if (method !== 'connect' && !this.connected) {\n                    throw new Error('Wallet not connected');\n                }\n                requestId = this._nextRequestId;\n                ++this._nextRequestId;\n                return [2 /*return*/, new Promise(function (resolve, reject) {\n                        _this._responsePromises.set(requestId, [resolve, reject]);\n                        if (_this._injectedProvider) {\n                            _this._injectedProvider.postMessage({\n                                jsonrpc: '2.0',\n                                id: requestId,\n                                method: method,\n                                params: __assign({ network: _this._network }, params),\n                            });\n                        }\n                        else {\n                            _this._popup.postMessage({\n                                jsonrpc: '2.0',\n                                id: requestId,\n                                method: method,\n                                params: params,\n                            }, _this._providerUrl.origin);\n                            if (!_this.autoApprove) {\n                                _this._popup.focus();\n                            }\n                        }\n                    })];\n            });\n        }); };\n        _this.connect = function () {\n            if (_this._popup) {\n                _this._popup.close();\n            }\n            return _this._handleConnect();\n        };\n        _this.disconnect = function () { return __awaiter(_this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this._injectedProvider) return [3 /*break*/, 2];\n                        return [4 /*yield*/, this._sendRequest('disconnect', {})];\n                    case 1:\n                        _a.sent();\n                        _a.label = 2;\n                    case 2:\n                        if (this._popup) {\n                            this._popup.close();\n                        }\n                        this._handleDisconnect();\n                        return [2 /*return*/];\n                }\n            });\n        }); };\n        _this.sign = function (data, display) { return __awaiter(_this, void 0, void 0, function () {\n            var response, signature, publicKey;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!(data instanceof Uint8Array)) {\n                            throw new Error('Data must be an instance of Uint8Array');\n                        }\n                        return [4 /*yield*/, this._sendRequest('sign', {\n                                data: data,\n                                display: display,\n                            })];\n                    case 1:\n                        response = _a.sent();\n                        signature = bs58.decode(response.signature);\n                        publicKey = new PublicKey(response.publicKey);\n                        return [2 /*return*/, {\n                                signature: signature,\n                                publicKey: publicKey,\n                            }];\n                }\n            });\n        }); };\n        if (isInjectedProvider(provider)) {\n            _this._injectedProvider = provider;\n        }\n        else if (isString(provider)) {\n            _this._providerUrl = new URL(provider);\n            _this._providerUrl.hash = new URLSearchParams({\n                origin: window.location.origin,\n                network: network,\n            }).toString();\n        }\n        else {\n            throw new Error('provider parameter must be an injected provider or a URL string.');\n        }\n        _this._network = network;\n        _this._publicKey = null;\n        _this._autoApprove = false;\n        _this._popup = null;\n        _this._handlerAdded = false;\n        _this._nextRequestId = 1;\n        _this._responsePromises = new Map();\n        return _this;\n    }\n    Object.defineProperty(Wallet.prototype, \"publicKey\", {\n        get: function () {\n            return this._publicKey;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Wallet.prototype, \"connected\", {\n        get: function () {\n            return this._publicKey !== null;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Wallet.prototype, \"autoApprove\", {\n        get: function () {\n            return this._autoApprove;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return Wallet;\n}(EventEmitter));\nexport default Wallet;\nfunction isString(a) {\n    return typeof a === 'string';\n}\nfunction isInjectedProvider(a) {\n    return isObject(a) && isFunction(a.postMessage);\n}\nfunction isObject(a) {\n    return typeof a === 'object' && a !== null;\n}\nfunction isFunction(a) {\n    return typeof a === 'function';\n}\n", "var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nimport WalletAdapter from './base';\nimport Wallet from './WalletProvider';\nimport bs58 from 'bs58';\nvar WebAdapter = /** @class */ (function (_super) {\n    __extends(WebAdapter, _super);\n    // @ts-ignore\n    function WebAdapter(iframe, network, provider) {\n        var _this = _super.call(this) || this;\n        _this._instance = null;\n        // @ts-ignore\n        _this.handleMessage = function (data) {\n            // nothing to do here\n        };\n        _this._sendRequest = function (method, params) { return __awaiter(_this, void 0, void 0, function () {\n            var _a, _b;\n            return __generator(this, function (_c) {\n                switch (_c.label) {\n                    case 0:\n                        if (!((_a = this._instance) === null || _a === void 0 ? void 0 : _a.sendRequest)) return [3 /*break*/, 2];\n                        return [4 /*yield*/, this._instance.sendRequest(method, params)];\n                    case 1: return [2 /*return*/, _c.sent()];\n                    case 2:\n                        if (!((_b = this._instance) === null || _b === void 0 ? void 0 : _b._sendRequest)) return [3 /*break*/, 4];\n                        return [4 /*yield*/, this._instance._sendRequest(method, params)];\n                    case 3: return [2 /*return*/, _c.sent()];\n                    case 4: throw new Error('Unsupported version of `@project-serum/sol-wallet-adapter`');\n                }\n            });\n        }); };\n        _this._handleConnect = function () {\n            _this.emit('connect');\n        };\n        _this._handleDisconnect = function () {\n            window.clearInterval(_this._pollTimer);\n            _this.emit('disconnect');\n        };\n        _this._network = network;\n        _this._provider = provider;\n        return _this;\n    }\n    Object.defineProperty(WebAdapter.prototype, \"publicKey\", {\n        get: function () {\n            return this._instance.publicKey || null;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(WebAdapter.prototype, \"connected\", {\n        get: function () {\n            return this._instance.connected || false;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    WebAdapter.prototype.connect = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            var _this = this;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        this._instance = new Wallet(this._provider, this._network);\n                        this._instance.on('connect', this._handleConnect);\n                        this._instance.on('disconnect', this._handleDisconnect);\n                        this._pollTimer = window.setInterval(function () {\n                            var _a, _b;\n                            // @ts-ignore\n                            if (((_b = (_a = _this._instance) === null || _a === void 0 ? void 0 : _a._popup) === null || _b === void 0 ? void 0 : _b.closed) !== false) {\n                                _this._handleDisconnect();\n                            }\n                        }, 200);\n                        return [4 /*yield*/, this._instance.connect()];\n                    case 1:\n                        _a.sent();\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    WebAdapter.prototype.disconnect = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        this._instance.removeAllListeners('connect');\n                        this._instance.removeAllListeners('disconnect');\n                        return [4 /*yield*/, this._instance.disconnect()];\n                    case 1:\n                        _a.sent();\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    WebAdapter.prototype.signTransaction = function (transaction) {\n        return __awaiter(this, void 0, void 0, function () {\n            var signedTransaction;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        return [4 /*yield*/, this._sendRequest('signTransactionV2', {\n                                transaction: bs58.encode(transaction)\n                            })];\n                    case 1:\n                        signedTransaction = (_a.sent()).transaction;\n                        return [2 /*return*/, bs58.decode(signedTransaction)];\n                }\n            });\n        });\n    };\n    WebAdapter.prototype.signAllTransactions = function (transactions) {\n        return __awaiter(this, void 0, void 0, function () {\n            var signedTransactions;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        return [4 /*yield*/, this._sendRequest('signAllTransactionsV2', {\n                                transactions: transactions.map(function (transaction) { return bs58.encode(transaction); })\n                            })];\n                    case 1:\n                        signedTransactions = (_a.sent()).transactions;\n                        return [2 /*return*/, signedTransactions.map(function (transaction) { return bs58.decode(transaction); })];\n                }\n            });\n        });\n    };\n    WebAdapter.prototype.signAndSendTransaction = function (transaction, options) {\n        return __awaiter(this, void 0, void 0, function () {\n            var response;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        return [4 /*yield*/, this._sendRequest('signAndSendTransaction', {\n                                transaction: bs58.encode(transaction),\n                                options: options\n                            })];\n                    case 1:\n                        response = (_a.sent());\n                        return [2 /*return*/, response.signature];\n                }\n            });\n        });\n    };\n    WebAdapter.prototype.signMessage = function (data, display) {\n        if (display === void 0) { display = 'hex'; }\n        return __awaiter(this, void 0, void 0, function () {\n            var signature;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        return [4 /*yield*/, this._instance.sign(data, display)];\n                    case 1:\n                        signature = (_a.sent()).signature;\n                        return [2 /*return*/, Uint8Array.from(signature)];\n                }\n            });\n        });\n    };\n    return WebAdapter;\n}(WalletAdapter));\nexport default WebAdapter;\n", "var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nimport { PublicKey } from '@solana/web3.js';\nimport WalletAdapter from './base';\nimport { v4 as uuidv4 } from 'uuid';\nimport bs58 from 'bs58';\nvar IframeAdapter = /** @class */ (function (_super) {\n    __extends(IframeAdapter, _super);\n    function IframeAdapter(iframe, publicKey) {\n        var _this = this;\n        var _a;\n        _this = _super.call(this) || this;\n        _this._publicKey = null;\n        _this._messageHandlers = {};\n        _this.handleMessage = function (data) {\n            if (_this._messageHandlers[data.id]) {\n                var _a = _this._messageHandlers[data.id], resolve = _a.resolve, reject = _a.reject;\n                delete _this._messageHandlers[data.id];\n                if (data.error) {\n                    reject(data.error);\n                }\n                else {\n                    resolve(data.result);\n                }\n            }\n        };\n        _this._sendMessage = function (data) {\n            if (!_this.connected) {\n                throw new Error('Wallet not connected');\n            }\n            return new Promise(function (resolve, reject) {\n                var _a, _b;\n                var messageId = uuidv4();\n                _this._messageHandlers[messageId] = { resolve: resolve, reject: reject };\n                (_b = (_a = _this._iframe) === null || _a === void 0 ? void 0 : _a.contentWindow) === null || _b === void 0 ? void 0 : _b.postMessage({\n                    channel: 'solflareWalletAdapterToIframe',\n                    data: __assign({ id: messageId }, data)\n                }, '*');\n            });\n        };\n        _this._iframe = iframe;\n        _this._publicKey = new PublicKey((_a = publicKey === null || publicKey === void 0 ? void 0 : publicKey.toString) === null || _a === void 0 ? void 0 : _a.call(publicKey));\n        return _this;\n    }\n    Object.defineProperty(IframeAdapter.prototype, \"publicKey\", {\n        get: function () {\n            return this._publicKey || null;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(IframeAdapter.prototype, \"connected\", {\n        get: function () {\n            return true;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    IframeAdapter.prototype.connect = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                return [2 /*return*/];\n            });\n        });\n    };\n    IframeAdapter.prototype.disconnect = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0: return [4 /*yield*/, this._sendMessage({\n                            method: 'disconnect'\n                        })];\n                    case 1:\n                        _a.sent();\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    IframeAdapter.prototype.signTransaction = function (transaction) {\n        var _a;\n        return __awaiter(this, void 0, void 0, function () {\n            var signedTransaction, e_1;\n            return __generator(this, function (_b) {\n                switch (_b.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        _b.label = 1;\n                    case 1:\n                        _b.trys.push([1, 3, , 4]);\n                        return [4 /*yield*/, this._sendMessage({\n                                method: 'signTransaction',\n                                params: {\n                                    transaction: bs58.encode(transaction)\n                                }\n                            })];\n                    case 2:\n                        signedTransaction = _b.sent();\n                        return [2 /*return*/, bs58.decode(signedTransaction)];\n                    case 3:\n                        e_1 = _b.sent();\n                        throw new Error(((_a = e_1 === null || e_1 === void 0 ? void 0 : e_1.toString) === null || _a === void 0 ? void 0 : _a.call(e_1)) || 'Failed to sign transaction');\n                    case 4: return [2 /*return*/];\n                }\n            });\n        });\n    };\n    IframeAdapter.prototype.signAllTransactions = function (transactions) {\n        var _a;\n        return __awaiter(this, void 0, void 0, function () {\n            var signedTransactions, e_2;\n            return __generator(this, function (_b) {\n                switch (_b.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        _b.label = 1;\n                    case 1:\n                        _b.trys.push([1, 3, , 4]);\n                        return [4 /*yield*/, this._sendMessage({\n                                method: 'signAllTransactions',\n                                params: {\n                                    transactions: transactions.map(function (transaction) { return bs58.encode(transaction); })\n                                }\n                            })];\n                    case 2:\n                        signedTransactions = _b.sent();\n                        return [2 /*return*/, signedTransactions.map(function (transaction) { return bs58.decode(transaction); })];\n                    case 3:\n                        e_2 = _b.sent();\n                        throw new Error(((_a = e_2 === null || e_2 === void 0 ? void 0 : e_2.toString) === null || _a === void 0 ? void 0 : _a.call(e_2)) || 'Failed to sign transactions');\n                    case 4: return [2 /*return*/];\n                }\n            });\n        });\n    };\n    IframeAdapter.prototype.signAndSendTransaction = function (transaction, options) {\n        var _a;\n        return __awaiter(this, void 0, void 0, function () {\n            var result, e_3;\n            return __generator(this, function (_b) {\n                switch (_b.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        _b.label = 1;\n                    case 1:\n                        _b.trys.push([1, 3, , 4]);\n                        return [4 /*yield*/, this._sendMessage({\n                                method: 'signAndSendTransaction',\n                                params: {\n                                    transaction: bs58.encode(transaction),\n                                    options: options\n                                }\n                            })];\n                    case 2:\n                        result = _b.sent();\n                        return [2 /*return*/, result];\n                    case 3:\n                        e_3 = _b.sent();\n                        throw new Error(((_a = e_3 === null || e_3 === void 0 ? void 0 : e_3.toString) === null || _a === void 0 ? void 0 : _a.call(e_3)) || 'Failed to sign and send transaction');\n                    case 4: return [2 /*return*/];\n                }\n            });\n        });\n    };\n    IframeAdapter.prototype.signMessage = function (data, display) {\n        var _a;\n        if (display === void 0) { display = 'hex'; }\n        return __awaiter(this, void 0, void 0, function () {\n            var result, e_4;\n            return __generator(this, function (_b) {\n                switch (_b.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        _b.label = 1;\n                    case 1:\n                        _b.trys.push([1, 3, , 4]);\n                        return [4 /*yield*/, this._sendMessage({\n                                method: 'signMessage',\n                                params: {\n                                    data: data,\n                                    display: display\n                                }\n                            })];\n                    case 2:\n                        result = _b.sent();\n                        return [2 /*return*/, Uint8Array.from(bs58.decode(result))];\n                    case 3:\n                        e_4 = _b.sent();\n                        throw new Error(((_a = e_4 === null || e_4 === void 0 ? void 0 : e_4.toString) === null || _a === void 0 ? void 0 : _a.call(e_4)) || 'Failed to sign message');\n                    case 4: return [2 /*return*/];\n                }\n            });\n        });\n    };\n    return IframeAdapter;\n}(WalletAdapter));\nexport default IframeAdapter;\n", "// Unique ID creation requires a high quality random # generator. In the browser we therefore\n// require the crypto API and do not support built-in fallback to lower quality random number\n// generators (like Math.random()).\nlet getRandomValues;\nconst rnds8 = new Uint8Array(16);\nexport default function rng() {\n  // lazy load so that environments that need to polyfill have a chance to do so\n  if (!getRandomValues) {\n    // getRandomValues needs to be invoked in a context where \"this\" is a Crypto implementation.\n    getRandomValues = typeof crypto !== 'undefined' && crypto.getRandomValues && crypto.getRandomValues.bind(crypto);\n\n    if (!getRandomValues) {\n      throw new Error('crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported');\n    }\n  }\n\n  return getRandomValues(rnds8);\n}", "export default /^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;", "import REGEX from './regex.js';\n\nfunction validate(uuid) {\n  return typeof uuid === 'string' && REGEX.test(uuid);\n}\n\nexport default validate;", "import validate from './validate.js';\n/**\n * Convert array of 16 byte values to UUID string format of the form:\n * XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX\n */\n\nconst byteToHex = [];\n\nfor (let i = 0; i < 256; ++i) {\n  byteToHex.push((i + 0x100).toString(16).slice(1));\n}\n\nexport function unsafeStringify(arr, offset = 0) {\n  // Note: Be careful editing this code!  It's been tuned for performance\n  // and works in ways you may not expect. See https://github.com/uuidjs/uuid/pull/434\n  return byteToHex[arr[offset + 0]] + byteToHex[arr[offset + 1]] + byteToHex[arr[offset + 2]] + byteToHex[arr[offset + 3]] + '-' + byteToHex[arr[offset + 4]] + byteToHex[arr[offset + 5]] + '-' + byteToHex[arr[offset + 6]] + byteToHex[arr[offset + 7]] + '-' + byteToHex[arr[offset + 8]] + byteToHex[arr[offset + 9]] + '-' + byteToHex[arr[offset + 10]] + byteToHex[arr[offset + 11]] + byteToHex[arr[offset + 12]] + byteToHex[arr[offset + 13]] + byteToHex[arr[offset + 14]] + byteToHex[arr[offset + 15]];\n}\n\nfunction stringify(arr, offset = 0) {\n  const uuid = unsafeStringify(arr, offset); // Consistency check for valid UUID.  If this throws, it's likely due to one\n  // of the following:\n  // - One or more input array values don't map to a hex octet (leading to\n  // \"undefined\" in the uuid)\n  // - Invalid input values for the RFC `version` or `variant` fields\n\n  if (!validate(uuid)) {\n    throw TypeError('Stringified UUID is invalid');\n  }\n\n  return uuid;\n}\n\nexport default stringify;", "import validate from './validate.js';\n\nfunction parse(uuid) {\n  if (!validate(uuid)) {\n    throw TypeError('Invalid UUID');\n  }\n\n  let v;\n  const arr = new Uint8Array(16); // Parse ########-....-....-....-............\n\n  arr[0] = (v = parseInt(uuid.slice(0, 8), 16)) >>> 24;\n  arr[1] = v >>> 16 & 0xff;\n  arr[2] = v >>> 8 & 0xff;\n  arr[3] = v & 0xff; // Parse ........-####-....-....-............\n\n  arr[4] = (v = parseInt(uuid.slice(9, 13), 16)) >>> 8;\n  arr[5] = v & 0xff; // Parse ........-....-####-....-............\n\n  arr[6] = (v = parseInt(uuid.slice(14, 18), 16)) >>> 8;\n  arr[7] = v & 0xff; // Parse ........-....-....-####-............\n\n  arr[8] = (v = parseInt(uuid.slice(19, 23), 16)) >>> 8;\n  arr[9] = v & 0xff; // Parse ........-....-....-....-############\n  // (Use \"/\" to avoid 32-bit truncation when bit-shifting high-order bytes)\n\n  arr[10] = (v = parseInt(uuid.slice(24, 36), 16)) / 0x10000000000 & 0xff;\n  arr[11] = v / 0x100000000 & 0xff;\n  arr[12] = v >>> 24 & 0xff;\n  arr[13] = v >>> 16 & 0xff;\n  arr[14] = v >>> 8 & 0xff;\n  arr[15] = v & 0xff;\n  return arr;\n}\n\nexport default parse;", "import { unsafeStringify } from './stringify.js';\nimport parse from './parse.js';\n\nfunction stringToBytes(str) {\n  str = unescape(encodeURIComponent(str)); // UTF8 escape\n\n  const bytes = [];\n\n  for (let i = 0; i < str.length; ++i) {\n    bytes.push(str.charCodeAt(i));\n  }\n\n  return bytes;\n}\n\nexport const DNS = '6ba7b810-9dad-11d1-80b4-00c04fd430c8';\nexport const URL = '6ba7b811-9dad-11d1-80b4-00c04fd430c8';\nexport default function v35(name, version, hashfunc) {\n  function generateUUID(value, namespace, buf, offset) {\n    var _namespace;\n\n    if (typeof value === 'string') {\n      value = stringToBytes(value);\n    }\n\n    if (typeof namespace === 'string') {\n      namespace = parse(namespace);\n    }\n\n    if (((_namespace = namespace) === null || _namespace === void 0 ? void 0 : _namespace.length) !== 16) {\n      throw TypeError('Namespace must be array-like (16 iterable integer values, 0-255)');\n    } // Compute hash of namespace and value, Per 4.3\n    // Future: Use spread syntax when supported on all platforms, e.g. `bytes =\n    // hashfunc([...namespace, ... value])`\n\n\n    let bytes = new Uint8Array(16 + value.length);\n    bytes.set(namespace);\n    bytes.set(value, namespace.length);\n    bytes = hashfunc(bytes);\n    bytes[6] = bytes[6] & 0x0f | version;\n    bytes[8] = bytes[8] & 0x3f | 0x80;\n\n    if (buf) {\n      offset = offset || 0;\n\n      for (let i = 0; i < 16; ++i) {\n        buf[offset + i] = bytes[i];\n      }\n\n      return buf;\n    }\n\n    return unsafeStringify(bytes);\n  } // Function#name is not settable on some platforms (#270)\n\n\n  try {\n    generateUUID.name = name; // eslint-disable-next-line no-empty\n  } catch (err) {} // For CommonJS default export support\n\n\n  generateUUID.DNS = DNS;\n  generateUUID.URL = URL;\n  return generateUUID;\n}", "/*\n * Browser-compatible JavaScript MD5\n *\n * Modification of JavaScript MD5\n * https://github.com/blueimp/JavaScript-MD5\n *\n * Copyright 2011, <PERSON>\n * https://blueimp.net\n *\n * Licensed under the MIT license:\n * https://opensource.org/licenses/MIT\n *\n * Based on\n * A JavaScript implementation of the RSA Data Security, Inc. MD5 Message\n * Digest Algorithm, as defined in RFC 1321.\n * Version 2.2 Copyright (C) <PERSON> 1999 - 2009\n * Other contributors: <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Lostinet\n * Distributed under the BSD License\n * See http://pajhome.org.uk/crypt/md5 for more info.\n */\nfunction md5(bytes) {\n  if (typeof bytes === 'string') {\n    const msg = unescape(encodeURIComponent(bytes)); // UTF8 escape\n\n    bytes = new Uint8Array(msg.length);\n\n    for (let i = 0; i < msg.length; ++i) {\n      bytes[i] = msg.charCodeAt(i);\n    }\n  }\n\n  return md5ToHexEncodedArray(wordsToMd5(bytesToWords(bytes), bytes.length * 8));\n}\n/*\n * Convert an array of little-endian words to an array of bytes\n */\n\n\nfunction md5ToHexEncodedArray(input) {\n  const output = [];\n  const length32 = input.length * 32;\n  const hexTab = '0123456789abcdef';\n\n  for (let i = 0; i < length32; i += 8) {\n    const x = input[i >> 5] >>> i % 32 & 0xff;\n    const hex = parseInt(hexTab.charAt(x >>> 4 & 0x0f) + hexTab.charAt(x & 0x0f), 16);\n    output.push(hex);\n  }\n\n  return output;\n}\n/**\n * Calculate output length with padding and bit length\n */\n\n\nfunction getOutputLength(inputLength8) {\n  return (inputLength8 + 64 >>> 9 << 4) + 14 + 1;\n}\n/*\n * Calculate the MD5 of an array of little-endian words, and a bit length.\n */\n\n\nfunction wordsToMd5(x, len) {\n  /* append padding */\n  x[len >> 5] |= 0x80 << len % 32;\n  x[getOutputLength(len) - 1] = len;\n  let a = 1732584193;\n  let b = -271733879;\n  let c = -1732584194;\n  let d = 271733878;\n\n  for (let i = 0; i < x.length; i += 16) {\n    const olda = a;\n    const oldb = b;\n    const oldc = c;\n    const oldd = d;\n    a = md5ff(a, b, c, d, x[i], 7, -680876936);\n    d = md5ff(d, a, b, c, x[i + 1], 12, -389564586);\n    c = md5ff(c, d, a, b, x[i + 2], 17, 606105819);\n    b = md5ff(b, c, d, a, x[i + 3], 22, -1044525330);\n    a = md5ff(a, b, c, d, x[i + 4], 7, -176418897);\n    d = md5ff(d, a, b, c, x[i + 5], 12, 1200080426);\n    c = md5ff(c, d, a, b, x[i + 6], 17, -1473231341);\n    b = md5ff(b, c, d, a, x[i + 7], 22, -45705983);\n    a = md5ff(a, b, c, d, x[i + 8], 7, 1770035416);\n    d = md5ff(d, a, b, c, x[i + 9], 12, -1958414417);\n    c = md5ff(c, d, a, b, x[i + 10], 17, -42063);\n    b = md5ff(b, c, d, a, x[i + 11], 22, -1990404162);\n    a = md5ff(a, b, c, d, x[i + 12], 7, 1804603682);\n    d = md5ff(d, a, b, c, x[i + 13], 12, -40341101);\n    c = md5ff(c, d, a, b, x[i + 14], 17, -1502002290);\n    b = md5ff(b, c, d, a, x[i + 15], 22, 1236535329);\n    a = md5gg(a, b, c, d, x[i + 1], 5, -165796510);\n    d = md5gg(d, a, b, c, x[i + 6], 9, -1069501632);\n    c = md5gg(c, d, a, b, x[i + 11], 14, 643717713);\n    b = md5gg(b, c, d, a, x[i], 20, -373897302);\n    a = md5gg(a, b, c, d, x[i + 5], 5, -701558691);\n    d = md5gg(d, a, b, c, x[i + 10], 9, 38016083);\n    c = md5gg(c, d, a, b, x[i + 15], 14, -660478335);\n    b = md5gg(b, c, d, a, x[i + 4], 20, -405537848);\n    a = md5gg(a, b, c, d, x[i + 9], 5, 568446438);\n    d = md5gg(d, a, b, c, x[i + 14], 9, -1019803690);\n    c = md5gg(c, d, a, b, x[i + 3], 14, -187363961);\n    b = md5gg(b, c, d, a, x[i + 8], 20, 1163531501);\n    a = md5gg(a, b, c, d, x[i + 13], 5, -1444681467);\n    d = md5gg(d, a, b, c, x[i + 2], 9, -51403784);\n    c = md5gg(c, d, a, b, x[i + 7], 14, 1735328473);\n    b = md5gg(b, c, d, a, x[i + 12], 20, -1926607734);\n    a = md5hh(a, b, c, d, x[i + 5], 4, -378558);\n    d = md5hh(d, a, b, c, x[i + 8], 11, -2022574463);\n    c = md5hh(c, d, a, b, x[i + 11], 16, 1839030562);\n    b = md5hh(b, c, d, a, x[i + 14], 23, -35309556);\n    a = md5hh(a, b, c, d, x[i + 1], 4, -1530992060);\n    d = md5hh(d, a, b, c, x[i + 4], 11, 1272893353);\n    c = md5hh(c, d, a, b, x[i + 7], 16, -155497632);\n    b = md5hh(b, c, d, a, x[i + 10], 23, -1094730640);\n    a = md5hh(a, b, c, d, x[i + 13], 4, 681279174);\n    d = md5hh(d, a, b, c, x[i], 11, -358537222);\n    c = md5hh(c, d, a, b, x[i + 3], 16, -722521979);\n    b = md5hh(b, c, d, a, x[i + 6], 23, 76029189);\n    a = md5hh(a, b, c, d, x[i + 9], 4, -640364487);\n    d = md5hh(d, a, b, c, x[i + 12], 11, -421815835);\n    c = md5hh(c, d, a, b, x[i + 15], 16, 530742520);\n    b = md5hh(b, c, d, a, x[i + 2], 23, -995338651);\n    a = md5ii(a, b, c, d, x[i], 6, -198630844);\n    d = md5ii(d, a, b, c, x[i + 7], 10, 1126891415);\n    c = md5ii(c, d, a, b, x[i + 14], 15, -1416354905);\n    b = md5ii(b, c, d, a, x[i + 5], 21, -57434055);\n    a = md5ii(a, b, c, d, x[i + 12], 6, 1700485571);\n    d = md5ii(d, a, b, c, x[i + 3], 10, -1894986606);\n    c = md5ii(c, d, a, b, x[i + 10], 15, -1051523);\n    b = md5ii(b, c, d, a, x[i + 1], 21, -2054922799);\n    a = md5ii(a, b, c, d, x[i + 8], 6, 1873313359);\n    d = md5ii(d, a, b, c, x[i + 15], 10, -30611744);\n    c = md5ii(c, d, a, b, x[i + 6], 15, -1560198380);\n    b = md5ii(b, c, d, a, x[i + 13], 21, 1309151649);\n    a = md5ii(a, b, c, d, x[i + 4], 6, -145523070);\n    d = md5ii(d, a, b, c, x[i + 11], 10, -1120210379);\n    c = md5ii(c, d, a, b, x[i + 2], 15, 718787259);\n    b = md5ii(b, c, d, a, x[i + 9], 21, -343485551);\n    a = safeAdd(a, olda);\n    b = safeAdd(b, oldb);\n    c = safeAdd(c, oldc);\n    d = safeAdd(d, oldd);\n  }\n\n  return [a, b, c, d];\n}\n/*\n * Convert an array bytes to an array of little-endian words\n * Characters >255 have their high-byte silently ignored.\n */\n\n\nfunction bytesToWords(input) {\n  if (input.length === 0) {\n    return [];\n  }\n\n  const length8 = input.length * 8;\n  const output = new Uint32Array(getOutputLength(length8));\n\n  for (let i = 0; i < length8; i += 8) {\n    output[i >> 5] |= (input[i / 8] & 0xff) << i % 32;\n  }\n\n  return output;\n}\n/*\n * Add integers, wrapping at 2^32. This uses 16-bit operations internally\n * to work around bugs in some JS interpreters.\n */\n\n\nfunction safeAdd(x, y) {\n  const lsw = (x & 0xffff) + (y & 0xffff);\n  const msw = (x >> 16) + (y >> 16) + (lsw >> 16);\n  return msw << 16 | lsw & 0xffff;\n}\n/*\n * Bitwise rotate a 32-bit number to the left.\n */\n\n\nfunction bitRotateLeft(num, cnt) {\n  return num << cnt | num >>> 32 - cnt;\n}\n/*\n * These functions implement the four basic operations the algorithm uses.\n */\n\n\nfunction md5cmn(q, a, b, x, s, t) {\n  return safeAdd(bitRotateLeft(safeAdd(safeAdd(a, q), safeAdd(x, t)), s), b);\n}\n\nfunction md5ff(a, b, c, d, x, s, t) {\n  return md5cmn(b & c | ~b & d, a, b, x, s, t);\n}\n\nfunction md5gg(a, b, c, d, x, s, t) {\n  return md5cmn(b & d | c & ~d, a, b, x, s, t);\n}\n\nfunction md5hh(a, b, c, d, x, s, t) {\n  return md5cmn(b ^ c ^ d, a, b, x, s, t);\n}\n\nfunction md5ii(a, b, c, d, x, s, t) {\n  return md5cmn(c ^ (b | ~d), a, b, x, s, t);\n}\n\nexport default md5;", "import v35 from './v35.js';\nimport md5 from './md5.js';\nconst v3 = v35('v3', 0x30, md5);\nexport default v3;", "const randomUUID = typeof crypto !== 'undefined' && crypto.randomUUID && crypto.randomUUID.bind(crypto);\nexport default {\n  randomUUID\n};", "import native from './native.js';\nimport rng from './rng.js';\nimport { unsafeStringify } from './stringify.js';\n\nfunction v4(options, buf, offset) {\n  if (native.randomUUID && !buf && !options) {\n    return native.randomUUID();\n  }\n\n  options = options || {};\n  const rnds = options.random || (options.rng || rng)(); // Per 4.4, set bits for version and `clock_seq_hi_and_reserved`\n\n  rnds[6] = rnds[6] & 0x0f | 0x40;\n  rnds[8] = rnds[8] & 0x3f | 0x80; // Copy bytes to buffer, if provided\n\n  if (buf) {\n    offset = offset || 0;\n\n    for (let i = 0; i < 16; ++i) {\n      buf[offset + i] = rnds[i];\n    }\n\n    return buf;\n  }\n\n  return unsafeStringify(rnds);\n}\n\nexport default v4;", "// Adapted from <PERSON>' SHA1 code at\n// http://www.movable-type.co.uk/scripts/sha1.html\nfunction f(s, x, y, z) {\n  switch (s) {\n    case 0:\n      return x & y ^ ~x & z;\n\n    case 1:\n      return x ^ y ^ z;\n\n    case 2:\n      return x & y ^ x & z ^ y & z;\n\n    case 3:\n      return x ^ y ^ z;\n  }\n}\n\nfunction ROTL(x, n) {\n  return x << n | x >>> 32 - n;\n}\n\nfunction sha1(bytes) {\n  const K = [0x5a827999, 0x6ed9eba1, 0x8f1bbcdc, 0xca62c1d6];\n  const H = [0x67452301, 0xefcdab89, 0x98badcfe, 0x10325476, 0xc3d2e1f0];\n\n  if (typeof bytes === 'string') {\n    const msg = unescape(encodeURIComponent(bytes)); // UTF8 escape\n\n    bytes = [];\n\n    for (let i = 0; i < msg.length; ++i) {\n      bytes.push(msg.charCodeAt(i));\n    }\n  } else if (!Array.isArray(bytes)) {\n    // Convert Array-like to Array\n    bytes = Array.prototype.slice.call(bytes);\n  }\n\n  bytes.push(0x80);\n  const l = bytes.length / 4 + 2;\n  const N = Math.ceil(l / 16);\n  const M = new Array(N);\n\n  for (let i = 0; i < N; ++i) {\n    const arr = new Uint32Array(16);\n\n    for (let j = 0; j < 16; ++j) {\n      arr[j] = bytes[i * 64 + j * 4] << 24 | bytes[i * 64 + j * 4 + 1] << 16 | bytes[i * 64 + j * 4 + 2] << 8 | bytes[i * 64 + j * 4 + 3];\n    }\n\n    M[i] = arr;\n  }\n\n  M[N - 1][14] = (bytes.length - 1) * 8 / Math.pow(2, 32);\n  M[N - 1][14] = Math.floor(M[N - 1][14]);\n  M[N - 1][15] = (bytes.length - 1) * 8 & 0xffffffff;\n\n  for (let i = 0; i < N; ++i) {\n    const W = new Uint32Array(80);\n\n    for (let t = 0; t < 16; ++t) {\n      W[t] = M[i][t];\n    }\n\n    for (let t = 16; t < 80; ++t) {\n      W[t] = ROTL(W[t - 3] ^ W[t - 8] ^ W[t - 14] ^ W[t - 16], 1);\n    }\n\n    let a = H[0];\n    let b = H[1];\n    let c = H[2];\n    let d = H[3];\n    let e = H[4];\n\n    for (let t = 0; t < 80; ++t) {\n      const s = Math.floor(t / 20);\n      const T = ROTL(a, 5) + f(s, b, c, d) + e + K[s] + W[t] >>> 0;\n      e = d;\n      d = c;\n      c = ROTL(b, 30) >>> 0;\n      b = a;\n      a = T;\n    }\n\n    H[0] = H[0] + a >>> 0;\n    H[1] = H[1] + b >>> 0;\n    H[2] = H[2] + c >>> 0;\n    H[3] = H[3] + d >>> 0;\n    H[4] = H[4] + e >>> 0;\n  }\n\n  return [H[0] >> 24 & 0xff, H[0] >> 16 & 0xff, H[0] >> 8 & 0xff, H[0] & 0xff, H[1] >> 24 & 0xff, H[1] >> 16 & 0xff, H[1] >> 8 & 0xff, H[1] & 0xff, H[2] >> 24 & 0xff, H[2] >> 16 & 0xff, H[2] >> 8 & 0xff, H[2] & 0xff, H[3] >> 24 & 0xff, H[3] >> 16 & 0xff, H[3] >> 8 & 0xff, H[3] & 0xff, H[4] >> 24 & 0xff, H[4] >> 16 & 0xff, H[4] >> 8 & 0xff, H[4] & 0xff];\n}\n\nexport default sha1;", "import v35 from './v35.js';\nimport sha1 from './sha1.js';\nconst v5 = v35('v5', 0x50, sha1);\nexport default v5;", "export function isLegacyTransactionInstance(transaction) {\n    return transaction.version === undefined;\n}\n", "export var VERSION = \"1.4.2\";\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAEA,QAAI,MAAM,OAAO,UAAU;AAA3B,QACI,SAAS;AASb,aAAS,SAAS;AAAA,IAAC;AASnB,QAAI,OAAO,QAAQ;AACjB,aAAO,YAAY,uBAAO,OAAO,IAAI;AAMrC,UAAI,CAAC,IAAI,OAAO,EAAE;AAAW,iBAAS;AAAA,IACxC;AAWA,aAAS,GAAG,IAAI,SAAS,MAAM;AAC7B,WAAK,KAAK;AACV,WAAK,UAAU;AACf,WAAK,OAAO,QAAQ;AAAA,IACtB;AAaA,aAAS,YAAY,SAAS,OAAO,IAAI,SAAS,MAAM;AACtD,UAAI,OAAO,OAAO,YAAY;AAC5B,cAAM,IAAI,UAAU,iCAAiC;AAAA,MACvD;AAEA,UAAI,WAAW,IAAI,GAAG,IAAI,WAAW,SAAS,IAAI,GAC9C,MAAM,SAAS,SAAS,QAAQ;AAEpC,UAAI,CAAC,QAAQ,QAAQ,GAAG;AAAG,gBAAQ,QAAQ,GAAG,IAAI,UAAU,QAAQ;AAAA,eAC3D,CAAC,QAAQ,QAAQ,GAAG,EAAE;AAAI,gBAAQ,QAAQ,GAAG,EAAE,KAAK,QAAQ;AAAA;AAChE,gBAAQ,QAAQ,GAAG,IAAI,CAAC,QAAQ,QAAQ,GAAG,GAAG,QAAQ;AAE3D,aAAO;AAAA,IACT;AASA,aAAS,WAAW,SAAS,KAAK;AAChC,UAAI,EAAE,QAAQ,iBAAiB;AAAG,gBAAQ,UAAU,IAAI,OAAO;AAAA;AAC1D,eAAO,QAAQ,QAAQ,GAAG;AAAA,IACjC;AASA,aAASA,gBAAe;AACtB,WAAK,UAAU,IAAI,OAAO;AAC1B,WAAK,eAAe;AAAA,IACtB;AASA,IAAAA,cAAa,UAAU,aAAa,SAAS,aAAa;AACxD,UAAI,QAAQ,CAAC,GACT,QACA;AAEJ,UAAI,KAAK,iBAAiB;AAAG,eAAO;AAEpC,WAAK,QAAS,SAAS,KAAK,SAAU;AACpC,YAAI,IAAI,KAAK,QAAQ,IAAI;AAAG,gBAAM,KAAK,SAAS,KAAK,MAAM,CAAC,IAAI,IAAI;AAAA,MACtE;AAEA,UAAI,OAAO,uBAAuB;AAChC,eAAO,MAAM,OAAO,OAAO,sBAAsB,MAAM,CAAC;AAAA,MAC1D;AAEA,aAAO;AAAA,IACT;AASA,IAAAA,cAAa,UAAU,YAAY,SAAS,UAAU,OAAO;AAC3D,UAAI,MAAM,SAAS,SAAS,QAAQ,OAChC,WAAW,KAAK,QAAQ,GAAG;AAE/B,UAAI,CAAC;AAAU,eAAO,CAAC;AACvB,UAAI,SAAS;AAAI,eAAO,CAAC,SAAS,EAAE;AAEpC,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,KAAK;AAClE,WAAG,CAAC,IAAI,SAAS,CAAC,EAAE;AAAA,MACtB;AAEA,aAAO;AAAA,IACT;AASA,IAAAA,cAAa,UAAU,gBAAgB,SAAS,cAAc,OAAO;AACnE,UAAI,MAAM,SAAS,SAAS,QAAQ,OAChC,YAAY,KAAK,QAAQ,GAAG;AAEhC,UAAI,CAAC;AAAW,eAAO;AACvB,UAAI,UAAU;AAAI,eAAO;AACzB,aAAO,UAAU;AAAA,IACnB;AASA,IAAAA,cAAa,UAAU,OAAO,SAAS,KAAK,OAAO,IAAI,IAAI,IAAI,IAAI,IAAI;AACrE,UAAI,MAAM,SAAS,SAAS,QAAQ;AAEpC,UAAI,CAAC,KAAK,QAAQ,GAAG;AAAG,eAAO;AAE/B,UAAI,YAAY,KAAK,QAAQ,GAAG,GAC5B,MAAM,UAAU,QAChB,MACA;AAEJ,UAAI,UAAU,IAAI;AAChB,YAAI,UAAU;AAAM,eAAK,eAAe,OAAO,UAAU,IAAI,QAAW,IAAI;AAE5E,gBAAQ,KAAK;AAAA,UACX,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,OAAO,GAAG;AAAA,UACrD,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,EAAE,GAAG;AAAA,UACzD,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,EAAE,GAAG;AAAA,UAC7D,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,IAAI,EAAE,GAAG;AAAA,UACjE,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,IAAI,IAAI,EAAE,GAAG;AAAA,UACrE,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG;AAAA,QAC3E;AAEA,aAAK,IAAI,GAAG,OAAO,IAAI,MAAM,MAAK,CAAC,GAAG,IAAI,KAAK,KAAK;AAClD,eAAK,IAAI,CAAC,IAAI,UAAU,CAAC;AAAA,QAC3B;AAEA,kBAAU,GAAG,MAAM,UAAU,SAAS,IAAI;AAAA,MAC5C,OAAO;AACL,YAAI,SAAS,UAAU,QACnB;AAEJ,aAAK,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC3B,cAAI,UAAU,CAAC,EAAE;AAAM,iBAAK,eAAe,OAAO,UAAU,CAAC,EAAE,IAAI,QAAW,IAAI;AAElF,kBAAQ,KAAK;AAAA,YACX,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,OAAO;AAAG;AAAA,YACpD,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,SAAS,EAAE;AAAG;AAAA,YACxD,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,SAAS,IAAI,EAAE;AAAG;AAAA,YAC5D,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,SAAS,IAAI,IAAI,EAAE;AAAG;AAAA,YAChE;AACE,kBAAI,CAAC;AAAM,qBAAK,IAAI,GAAG,OAAO,IAAI,MAAM,MAAK,CAAC,GAAG,IAAI,KAAK,KAAK;AAC7D,uBAAK,IAAI,CAAC,IAAI,UAAU,CAAC;AAAA,gBAC3B;AAEA,wBAAU,CAAC,EAAE,GAAG,MAAM,UAAU,CAAC,EAAE,SAAS,IAAI;AAAA,UACpD;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAWA,IAAAA,cAAa,UAAU,KAAK,SAAS,GAAG,OAAO,IAAI,SAAS;AAC1D,aAAO,YAAY,MAAM,OAAO,IAAI,SAAS,KAAK;AAAA,IACpD;AAWA,IAAAA,cAAa,UAAU,OAAO,SAAS,KAAK,OAAO,IAAI,SAAS;AAC9D,aAAO,YAAY,MAAM,OAAO,IAAI,SAAS,IAAI;AAAA,IACnD;AAYA,IAAAA,cAAa,UAAU,iBAAiB,SAAS,eAAe,OAAO,IAAI,SAAS,MAAM;AACxF,UAAI,MAAM,SAAS,SAAS,QAAQ;AAEpC,UAAI,CAAC,KAAK,QAAQ,GAAG;AAAG,eAAO;AAC/B,UAAI,CAAC,IAAI;AACP,mBAAW,MAAM,GAAG;AACpB,eAAO;AAAA,MACT;AAEA,UAAI,YAAY,KAAK,QAAQ,GAAG;AAEhC,UAAI,UAAU,IAAI;AAChB,YACE,UAAU,OAAO,OAChB,CAAC,QAAQ,UAAU,UACnB,CAAC,WAAW,UAAU,YAAY,UACnC;AACA,qBAAW,MAAM,GAAG;AAAA,QACtB;AAAA,MACF,OAAO;AACL,iBAAS,IAAI,GAAG,SAAS,CAAC,GAAG,SAAS,UAAU,QAAQ,IAAI,QAAQ,KAAK;AACvE,cACE,UAAU,CAAC,EAAE,OAAO,MACnB,QAAQ,CAAC,UAAU,CAAC,EAAE,QACtB,WAAW,UAAU,CAAC,EAAE,YAAY,SACrC;AACA,mBAAO,KAAK,UAAU,CAAC,CAAC;AAAA,UAC1B;AAAA,QACF;AAKA,YAAI,OAAO;AAAQ,eAAK,QAAQ,GAAG,IAAI,OAAO,WAAW,IAAI,OAAO,CAAC,IAAI;AAAA;AACpE,qBAAW,MAAM,GAAG;AAAA,MAC3B;AAEA,aAAO;AAAA,IACT;AASA,IAAAA,cAAa,UAAU,qBAAqB,SAAS,mBAAmB,OAAO;AAC7E,UAAI;AAEJ,UAAI,OAAO;AACT,cAAM,SAAS,SAAS,QAAQ;AAChC,YAAI,KAAK,QAAQ,GAAG;AAAG,qBAAW,MAAM,GAAG;AAAA,MAC7C,OAAO;AACL,aAAK,UAAU,IAAI,OAAO;AAC1B,aAAK,eAAe;AAAA,MACtB;AAEA,aAAO;AAAA,IACT;AAKA,IAAAA,cAAa,UAAU,MAAMA,cAAa,UAAU;AACpD,IAAAA,cAAa,UAAU,cAAcA,cAAa,UAAU;AAK5D,IAAAA,cAAa,WAAW;AAKxB,IAAAA,cAAa,eAAeA;AAK5B,QAAI,gBAAgB,OAAO,QAAQ;AACjC,aAAO,UAAUA;AAAA,IACnB;AAAA;AAAA;;;ACtQA;;;ACzEA,mBAAyB;AAGzB,IAAO,wBAAQ,aAAAC;;;ACHf,IAAI,YAAyC,2BAAY;AACrD,MAAI,gBAAgB,SAAU,GAAG,GAAG;AAChC,oBAAgB,OAAO,kBAClB,EAAE,WAAW,CAAC,EAAE,aAAa,SAAS,SAAUC,IAAGC,IAAG;AAAE,MAAAD,GAAE,YAAYC;AAAA,IAAG,KAC1E,SAAUD,IAAGC,IAAG;AAAE,eAAS,KAAKA;AAAG,YAAI,OAAO,UAAU,eAAe,KAAKA,IAAG,CAAC;AAAG,UAAAD,GAAE,CAAC,IAAIC,GAAE,CAAC;AAAA,IAAG;AACpG,WAAO,cAAc,GAAG,CAAC;AAAA,EAC7B;AACA,SAAO,SAAU,GAAG,GAAG;AACnB,QAAI,OAAO,MAAM,cAAc,MAAM;AACjC,YAAM,IAAI,UAAU,yBAAyB,OAAO,CAAC,IAAI,+BAA+B;AAC5F,kBAAc,GAAG,CAAC;AAClB,aAAS,KAAK;AAAE,WAAK,cAAc;AAAA,IAAG;AACtC,MAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,WAAW,IAAI,GAAG;AAAA,EACtF;AACJ,EAAG;AAEH,IAAI;AAAA;AAAA,EAA+B,SAAU,QAAQ;AACjD,cAAUC,gBAAe,MAAM;AAC/B,aAASA,iBAAgB;AACrB,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC/D;AACA,WAAOA;AAAA,EACX,EAAE,qBAAY;AAAA;AACd,IAAO,eAAQ;;;ACwDf;AACA,kBAAiB;AAhFjB,IAAIC,aAAyC,2BAAY;AACrD,MAAI,gBAAgB,SAAU,GAAG,GAAG;AAChC,oBAAgB,OAAO,kBAClB,EAAE,WAAW,CAAC,EAAE,aAAa,SAAS,SAAUC,IAAGC,IAAG;AAAE,MAAAD,GAAE,YAAYC;AAAA,IAAG,KAC1E,SAAUD,IAAGC,IAAG;AAAE,eAAS,KAAKA;AAAG,YAAI,OAAO,UAAU,eAAe,KAAKA,IAAG,CAAC;AAAG,UAAAD,GAAE,CAAC,IAAIC,GAAE,CAAC;AAAA,IAAG;AACpG,WAAO,cAAc,GAAG,CAAC;AAAA,EAC7B;AACA,SAAO,SAAU,GAAG,GAAG;AACnB,QAAI,OAAO,MAAM,cAAc,MAAM;AACjC,YAAM,IAAI,UAAU,yBAAyB,OAAO,CAAC,IAAI,+BAA+B;AAC5F,kBAAc,GAAG,CAAC;AAClB,aAAS,KAAK;AAAE,WAAK,cAAc;AAAA,IAAG;AACtC,MAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,WAAW,IAAI,GAAG;AAAA,EACtF;AACJ,EAAG;AACH,IAAI,WAAsC,WAAY;AAClD,aAAW,OAAO,UAAU,SAAS,GAAG;AACpC,aAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,UAAI,UAAU,CAAC;AACf,eAAS,KAAK;AAAG,YAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC;AAC1D,YAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAClB;AACA,WAAO;AAAA,EACX;AACA,SAAO,SAAS,MAAM,MAAM,SAAS;AACzC;AACA,IAAI,YAAwC,SAAU,SAAS,YAAY,GAAG,WAAW;AACrF,WAAS,MAAM,OAAO;AAAE,WAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAAE,cAAQ,KAAK;AAAA,IAAG,CAAC;AAAA,EAAG;AAC3G,SAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,aAAS,UAAU,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,KAAK,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC1F,aAAS,SAAS,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC7F,aAAS,KAAK,QAAQ;AAAE,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,IAAG;AAC7G,UAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EACxE,CAAC;AACL;AACA,IAAI,cAA4C,SAAU,SAAS,MAAM;AACrE,MAAI,IAAI,EAAE,OAAO,GAAG,MAAM,WAAW;AAAE,QAAI,EAAE,CAAC,IAAI;AAAG,YAAM,EAAE,CAAC;AAAG,WAAO,EAAE,CAAC;AAAA,EAAG,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,EAAE,GAAGC,IAAG,GAAG,GAAG;AAC/G,SAAO,IAAI,EAAE,MAAM,KAAK,CAAC,GAAG,SAAS,KAAK,CAAC,GAAG,UAAU,KAAK,CAAC,EAAE,GAAG,OAAO,WAAW,eAAe,EAAE,OAAO,QAAQ,IAAI,WAAW;AAAE,WAAO;AAAA,EAAM,IAAI;AACvJ,WAAS,KAAK,GAAG;AAAE,WAAO,SAAU,GAAG;AAAE,aAAO,KAAK,CAAC,GAAG,CAAC,CAAC;AAAA,IAAG;AAAA,EAAG;AACjE,WAAS,KAAK,IAAI;AACd,QAAIA;AAAG,YAAM,IAAI,UAAU,iCAAiC;AAC5D,WAAO,MAAM,IAAI,GAAG,GAAG,CAAC,MAAM,IAAI,KAAK;AAAG,UAAI;AAC1C,YAAIA,KAAI,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,IAAI,EAAE,QAAQ,IAAI,GAAG,CAAC,IAAI,EAAE,OAAO,OAAO,IAAI,EAAE,QAAQ,MAAM,EAAE,KAAK,CAAC,GAAG,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG;AAAM,iBAAO;AAC3J,YAAI,IAAI,GAAG;AAAG,eAAK,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,KAAK;AACtC,gBAAQ,GAAG,CAAC,GAAG;AAAA,UACX,KAAK;AAAA,UAAG,KAAK;AAAG,gBAAI;AAAI;AAAA,UACxB,KAAK;AAAG,cAAE;AAAS,mBAAO,EAAE,OAAO,GAAG,CAAC,GAAG,MAAM,MAAM;AAAA,UACtD,KAAK;AAAG,cAAE;AAAS,gBAAI,GAAG,CAAC;AAAG,iBAAK,CAAC,CAAC;AAAG;AAAA,UACxC,KAAK;AAAG,iBAAK,EAAE,IAAI,IAAI;AAAG,cAAE,KAAK,IAAI;AAAG;AAAA,UACxC;AACI,gBAAI,EAAE,IAAI,EAAE,MAAM,IAAI,EAAE,SAAS,KAAK,EAAE,EAAE,SAAS,CAAC,OAAO,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,IAAI;AAAE,kBAAI;AAAG;AAAA,YAAU;AAC3G,gBAAI,GAAG,CAAC,MAAM,MAAM,CAAC,KAAM,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,IAAK;AAAE,gBAAE,QAAQ,GAAG,CAAC;AAAG;AAAA,YAAO;AACrF,gBAAI,GAAG,CAAC,MAAM,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,gBAAE,QAAQ,EAAE,CAAC;AAAG,kBAAI;AAAI;AAAA,YAAO;AACpE,gBAAI,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,gBAAE,QAAQ,EAAE,CAAC;AAAG,gBAAE,IAAI,KAAK,EAAE;AAAG;AAAA,YAAO;AAClE,gBAAI,EAAE,CAAC;AAAG,gBAAE,IAAI,IAAI;AACpB,cAAE,KAAK,IAAI;AAAG;AAAA,QACtB;AACA,aAAK,KAAK,KAAK,SAAS,CAAC;AAAA,MAC7B,SAAS,GAAG;AAAE,aAAK,CAAC,GAAG,CAAC;AAAG,YAAI;AAAA,MAAG,UAAE;AAAU,QAAAA,KAAI,IAAI;AAAA,MAAG;AACzD,QAAI,GAAG,CAAC,IAAI;AAAG,YAAM,GAAG,CAAC;AAAG,WAAO,EAAE,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,QAAQ,MAAM,KAAK;AAAA,EACnF;AACJ;AACA,IAAI,SAAkC,SAAU,GAAG,GAAG;AAClD,MAAI,IAAI,OAAO,WAAW,cAAc,EAAE,OAAO,QAAQ;AACzD,MAAI,CAAC;AAAG,WAAO;AACf,MAAI,IAAI,EAAE,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG;AAC/B,MAAI;AACA,YAAQ,MAAM,UAAU,MAAM,MAAM,EAAE,IAAI,EAAE,KAAK,GAAG;AAAM,SAAG,KAAK,EAAE,KAAK;AAAA,EAC7E,SACO,OAAO;AAAE,QAAI,EAAE,MAAa;AAAA,EAAG,UACtC;AACI,QAAI;AACA,UAAI,KAAK,CAAC,EAAE,SAAS,IAAI,EAAE,QAAQ;AAAI,UAAE,KAAK,CAAC;AAAA,IACnD,UACA;AAAU,UAAI;AAAG,cAAM,EAAE;AAAA,IAAO;AAAA,EACpC;AACA,SAAO;AACX;AAIA,IAAI;AAAA;AAAA,EAAwB,SAAU,QAAQ;AAC1C,IAAAH,WAAUI,SAAQ,MAAM;AACxB,aAASA,QAAO,UAAU,SAAS;AAC/B,UAAI,QAAQ,OAAO,KAAK,IAAI,KAAK;AACjC,YAAM,iBAAiB,SAAU,GAAG;AAChC,YAAK,MAAM,qBAAqB,EAAE,WAAW,UACxC,EAAE,WAAW,MAAM,aAAa,UAAU,EAAE,WAAW,MAAM,QAAS;AACvE,cAAI,EAAE,KAAK,WAAW,aAAa;AAC/B,gBAAI,eAAe,IAAI,UAAU,EAAE,KAAK,OAAO,SAAS;AACxD,gBAAI,CAAC,MAAM,cAAc,CAAC,MAAM,WAAW,OAAO,YAAY,GAAG;AAC7D,kBAAI,MAAM,cAAc,CAAC,MAAM,WAAW,OAAO,YAAY,GAAG;AAC5D,sBAAM,kBAAkB;AAAA,cAC5B;AACA,oBAAM,aAAa;AACnB,oBAAM,eAAe,CAAC,CAAC,EAAE,KAAK,OAAO;AACrC,oBAAM,KAAK,WAAW,MAAM,UAAU;AAAA,YAC1C;AAAA,UACJ,WACS,EAAE,KAAK,WAAW,gBAAgB;AACvC,kBAAM,kBAAkB;AAAA,UAC5B,WACS,EAAE,KAAK,UAAU,EAAE,KAAK,OAAO;AACpC,gBAAI,MAAM,kBAAkB,IAAI,EAAE,KAAK,EAAE,GAAG;AACxC,kBAAI,KAAK,OAAO,MAAM,kBAAkB,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC;AAC1F,kBAAI,EAAE,KAAK,QAAQ;AACf,wBAAQ,EAAE,KAAK,MAAM;AAAA,cACzB,OACK;AACD,uBAAO,IAAI,MAAM,EAAE,KAAK,KAAK,CAAC;AAAA,cAClC;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AACA,YAAM,iBAAiB,WAAY;AAC/B,YAAI,CAAC,MAAM,eAAe;AACtB,gBAAM,gBAAgB;AACtB,iBAAO,iBAAiB,WAAW,MAAM,cAAc;AACvD,iBAAO,iBAAiB,gBAAgB,MAAM,UAAU;AAAA,QAC5D;AACA,YAAI,MAAM,mBAAmB;AACzB,iBAAO,IAAI,QAAQ,SAAU,SAAS;AAClC,kBAAM,aAAa,WAAW,CAAC,CAAC;AAChC,oBAAQ;AAAA,UACZ,CAAC;AAAA,QACL,OACK;AACD,iBAAO,OAAO;AACd,gBAAM,SAAS,OAAO,KAAK,MAAM,aAAa,SAAS,GAAG,UAAU,yCAAyC;AAC7G,iBAAO,IAAI,QAAQ,SAAU,SAAS;AAClC,kBAAM,KAAK,WAAW,OAAO;AAAA,UACjC,CAAC;AAAA,QACL;AAAA,MACJ;AACA,YAAM,oBAAoB,WAAY;AAClC,YAAI,MAAM,eAAe;AACrB,gBAAM,gBAAgB;AACtB,iBAAO,oBAAoB,WAAW,MAAM,cAAc;AAC1D,iBAAO,oBAAoB,gBAAgB,MAAM,UAAU;AAAA,QAC/D;AACA,YAAI,MAAM,YAAY;AAClB,gBAAM,aAAa;AACnB,gBAAM,KAAK,YAAY;AAAA,QAC3B;AACA,cAAM,kBAAkB,QAAQ,SAAU,IAAI,IAAI;AAC9C,cAAI,KAAK,OAAO,IAAI,CAAC,GAAG,UAAU,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC;AACtD,gBAAM,kBAAkB,OAAO,EAAE;AACjC,iBAAO,qBAAqB;AAAA,QAChC,CAAC;AAAA,MACL;AACA,YAAM,eAAe,SAAU,QAAQ,QAAQ;AAAE,eAAO,UAAU,OAAO,QAAQ,QAAQ,WAAY;AACjG,cAAI;AACJ,cAAIC,SAAQ;AACZ,iBAAO,YAAY,MAAM,SAAU,IAAI;AACnC,gBAAI,WAAW,aAAa,CAAC,KAAK,WAAW;AACzC,oBAAM,IAAI,MAAM,sBAAsB;AAAA,YAC1C;AACA,wBAAY,KAAK;AACjB,cAAE,KAAK;AACP,mBAAO,CAAC,GAAc,IAAI,QAAQ,SAAU,SAAS,QAAQ;AACrD,cAAAA,OAAM,kBAAkB,IAAI,WAAW,CAAC,SAAS,MAAM,CAAC;AACxD,kBAAIA,OAAM,mBAAmB;AACzB,gBAAAA,OAAM,kBAAkB,YAAY;AAAA,kBAChC,SAAS;AAAA,kBACT,IAAI;AAAA,kBACJ;AAAA,kBACA,QAAQ,SAAS,EAAE,SAASA,OAAM,SAAS,GAAG,MAAM;AAAA,gBACxD,CAAC;AAAA,cACL,OACK;AACD,gBAAAA,OAAM,OAAO,YAAY;AAAA,kBACrB,SAAS;AAAA,kBACT,IAAI;AAAA,kBACJ;AAAA,kBACA;AAAA,gBACJ,GAAGA,OAAM,aAAa,MAAM;AAC5B,oBAAI,CAACA,OAAM,aAAa;AACpB,kBAAAA,OAAM,OAAO,MAAM;AAAA,gBACvB;AAAA,cACJ;AAAA,YACJ,CAAC,CAAC;AAAA,UACV,CAAC;AAAA,QACL,CAAC;AAAA,MAAG;AACJ,YAAM,UAAU,WAAY;AACxB,YAAI,MAAM,QAAQ;AACd,gBAAM,OAAO,MAAM;AAAA,QACvB;AACA,eAAO,MAAM,eAAe;AAAA,MAChC;AACA,YAAM,aAAa,WAAY;AAAE,eAAO,UAAU,OAAO,QAAQ,QAAQ,WAAY;AACjF,iBAAO,YAAY,MAAM,SAAU,IAAI;AACnC,oBAAQ,GAAG,OAAO;AAAA,cACd,KAAK;AACD,oBAAI,CAAC,KAAK;AAAmB,yBAAO,CAAC,GAAa,CAAC;AACnD,uBAAO,CAAC,GAAa,KAAK,aAAa,cAAc,CAAC,CAAC,CAAC;AAAA,cAC5D,KAAK;AACD,mBAAG,KAAK;AACR,mBAAG,QAAQ;AAAA,cACf,KAAK;AACD,oBAAI,KAAK,QAAQ;AACb,uBAAK,OAAO,MAAM;AAAA,gBACtB;AACA,qBAAK,kBAAkB;AACvB,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,YAC5B;AAAA,UACJ,CAAC;AAAA,QACL,CAAC;AAAA,MAAG;AACJ,YAAM,OAAO,SAAU,MAAM,SAAS;AAAE,eAAO,UAAU,OAAO,QAAQ,QAAQ,WAAY;AACxF,cAAI,UAAU,WAAW;AACzB,iBAAO,YAAY,MAAM,SAAU,IAAI;AACnC,oBAAQ,GAAG,OAAO;AAAA,cACd,KAAK;AACD,oBAAI,EAAE,gBAAgB,aAAa;AAC/B,wBAAM,IAAI,MAAM,wCAAwC;AAAA,gBAC5D;AACA,uBAAO,CAAC,GAAa,KAAK,aAAa,QAAQ;AAAA,kBACvC;AAAA,kBACA;AAAA,gBACJ,CAAC,CAAC;AAAA,cACV,KAAK;AACD,2BAAW,GAAG,KAAK;AACnB,4BAAY,YAAAC,QAAK,OAAO,SAAS,SAAS;AAC1C,4BAAY,IAAI,UAAU,SAAS,SAAS;AAC5C,uBAAO,CAAC,GAAc;AAAA,kBACd;AAAA,kBACA;AAAA,gBACJ,CAAC;AAAA,YACb;AAAA,UACJ,CAAC;AAAA,QACL,CAAC;AAAA,MAAG;AACJ,UAAI,mBAAmB,QAAQ,GAAG;AAC9B,cAAM,oBAAoB;AAAA,MAC9B,WACS,SAAS,QAAQ,GAAG;AACzB,cAAM,eAAe,IAAI,IAAI,QAAQ;AACrC,cAAM,aAAa,OAAO,IAAI,gBAAgB;AAAA,UAC1C,QAAQ,OAAO,SAAS;AAAA,UACxB;AAAA,QACJ,CAAC,EAAE,SAAS;AAAA,MAChB,OACK;AACD,cAAM,IAAI,MAAM,kEAAkE;AAAA,MACtF;AACA,YAAM,WAAW;AACjB,YAAM,aAAa;AACnB,YAAM,eAAe;AACrB,YAAM,SAAS;AACf,YAAM,gBAAgB;AACtB,YAAM,iBAAiB;AACvB,YAAM,oBAAoB,oBAAI,IAAI;AAClC,aAAO;AAAA,IACX;AACA,WAAO,eAAeF,QAAO,WAAW,aAAa;AAAA,MACjD,KAAK,WAAY;AACb,eAAO,KAAK;AAAA,MAChB;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,WAAO,eAAeA,QAAO,WAAW,aAAa;AAAA,MACjD,KAAK,WAAY;AACb,eAAO,KAAK,eAAe;AAAA,MAC/B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,WAAO,eAAeA,QAAO,WAAW,eAAe;AAAA,MACnD,KAAK,WAAY;AACb,eAAO,KAAK;AAAA,MAChB;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,WAAOA;AAAA,EACX,EAAE,qBAAY;AAAA;AACd,IAAO,yBAAQ;AACf,SAAS,SAAS,GAAG;AACjB,SAAO,OAAO,MAAM;AACxB;AACA,SAAS,mBAAmB,GAAG;AAC3B,SAAO,SAAS,CAAC,KAAK,WAAW,EAAE,WAAW;AAClD;AACA,SAAS,SAAS,GAAG;AACjB,SAAO,OAAO,MAAM,YAAY,MAAM;AAC1C;AACA,SAAS,WAAW,GAAG;AACnB,SAAO,OAAO,MAAM;AACxB;;;AC3OA,IAAAG,eAAiB;AArDjB,IAAIC,aAAyC,2BAAY;AACrD,MAAI,gBAAgB,SAAU,GAAG,GAAG;AAChC,oBAAgB,OAAO,kBAClB,EAAE,WAAW,CAAC,EAAE,aAAa,SAAS,SAAUC,IAAGC,IAAG;AAAE,MAAAD,GAAE,YAAYC;AAAA,IAAG,KAC1E,SAAUD,IAAGC,IAAG;AAAE,eAAS,KAAKA;AAAG,YAAI,OAAO,UAAU,eAAe,KAAKA,IAAG,CAAC;AAAG,UAAAD,GAAE,CAAC,IAAIC,GAAE,CAAC;AAAA,IAAG;AACpG,WAAO,cAAc,GAAG,CAAC;AAAA,EAC7B;AACA,SAAO,SAAU,GAAG,GAAG;AACnB,QAAI,OAAO,MAAM,cAAc,MAAM;AACjC,YAAM,IAAI,UAAU,yBAAyB,OAAO,CAAC,IAAI,+BAA+B;AAC5F,kBAAc,GAAG,CAAC;AAClB,aAAS,KAAK;AAAE,WAAK,cAAc;AAAA,IAAG;AACtC,MAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,WAAW,IAAI,GAAG;AAAA,EACtF;AACJ,EAAG;AACH,IAAIC,aAAwC,SAAU,SAAS,YAAY,GAAG,WAAW;AACrF,WAAS,MAAM,OAAO;AAAE,WAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAAE,cAAQ,KAAK;AAAA,IAAG,CAAC;AAAA,EAAG;AAC3G,SAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,aAAS,UAAU,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,KAAK,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC1F,aAAS,SAAS,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC7F,aAAS,KAAK,QAAQ;AAAE,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,IAAG;AAC7G,UAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EACxE,CAAC;AACL;AACA,IAAIC,eAA4C,SAAU,SAAS,MAAM;AACrE,MAAI,IAAI,EAAE,OAAO,GAAG,MAAM,WAAW;AAAE,QAAI,EAAE,CAAC,IAAI;AAAG,YAAM,EAAE,CAAC;AAAG,WAAO,EAAE,CAAC;AAAA,EAAG,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,EAAE,GAAGC,IAAG,GAAG,GAAG;AAC/G,SAAO,IAAI,EAAE,MAAM,KAAK,CAAC,GAAG,SAAS,KAAK,CAAC,GAAG,UAAU,KAAK,CAAC,EAAE,GAAG,OAAO,WAAW,eAAe,EAAE,OAAO,QAAQ,IAAI,WAAW;AAAE,WAAO;AAAA,EAAM,IAAI;AACvJ,WAAS,KAAK,GAAG;AAAE,WAAO,SAAU,GAAG;AAAE,aAAO,KAAK,CAAC,GAAG,CAAC,CAAC;AAAA,IAAG;AAAA,EAAG;AACjE,WAAS,KAAK,IAAI;AACd,QAAIA;AAAG,YAAM,IAAI,UAAU,iCAAiC;AAC5D,WAAO,MAAM,IAAI,GAAG,GAAG,CAAC,MAAM,IAAI,KAAK;AAAG,UAAI;AAC1C,YAAIA,KAAI,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,IAAI,EAAE,QAAQ,IAAI,GAAG,CAAC,IAAI,EAAE,OAAO,OAAO,IAAI,EAAE,QAAQ,MAAM,EAAE,KAAK,CAAC,GAAG,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG;AAAM,iBAAO;AAC3J,YAAI,IAAI,GAAG;AAAG,eAAK,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,KAAK;AACtC,gBAAQ,GAAG,CAAC,GAAG;AAAA,UACX,KAAK;AAAA,UAAG,KAAK;AAAG,gBAAI;AAAI;AAAA,UACxB,KAAK;AAAG,cAAE;AAAS,mBAAO,EAAE,OAAO,GAAG,CAAC,GAAG,MAAM,MAAM;AAAA,UACtD,KAAK;AAAG,cAAE;AAAS,gBAAI,GAAG,CAAC;AAAG,iBAAK,CAAC,CAAC;AAAG;AAAA,UACxC,KAAK;AAAG,iBAAK,EAAE,IAAI,IAAI;AAAG,cAAE,KAAK,IAAI;AAAG;AAAA,UACxC;AACI,gBAAI,EAAE,IAAI,EAAE,MAAM,IAAI,EAAE,SAAS,KAAK,EAAE,EAAE,SAAS,CAAC,OAAO,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,IAAI;AAAE,kBAAI;AAAG;AAAA,YAAU;AAC3G,gBAAI,GAAG,CAAC,MAAM,MAAM,CAAC,KAAM,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,IAAK;AAAE,gBAAE,QAAQ,GAAG,CAAC;AAAG;AAAA,YAAO;AACrF,gBAAI,GAAG,CAAC,MAAM,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,gBAAE,QAAQ,EAAE,CAAC;AAAG,kBAAI;AAAI;AAAA,YAAO;AACpE,gBAAI,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,gBAAE,QAAQ,EAAE,CAAC;AAAG,gBAAE,IAAI,KAAK,EAAE;AAAG;AAAA,YAAO;AAClE,gBAAI,EAAE,CAAC;AAAG,gBAAE,IAAI,IAAI;AACpB,cAAE,KAAK,IAAI;AAAG;AAAA,QACtB;AACA,aAAK,KAAK,KAAK,SAAS,CAAC;AAAA,MAC7B,SAAS,GAAG;AAAE,aAAK,CAAC,GAAG,CAAC;AAAG,YAAI;AAAA,MAAG,UAAE;AAAU,QAAAA,KAAI,IAAI;AAAA,MAAG;AACzD,QAAI,GAAG,CAAC,IAAI;AAAG,YAAM,GAAG,CAAC;AAAG,WAAO,EAAE,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,QAAQ,MAAM,KAAK;AAAA,EACnF;AACJ;AAIA,IAAI;AAAA;AAAA,EAA4B,SAAU,QAAQ;AAC9C,IAAAL,WAAUM,aAAY,MAAM;AAE5B,aAASA,YAAW,QAAQ,SAAS,UAAU;AAC3C,UAAI,QAAQ,OAAO,KAAK,IAAI,KAAK;AACjC,YAAM,YAAY;AAElB,YAAM,gBAAgB,SAAU,MAAM;AAAA,MAEtC;AACA,YAAM,eAAe,SAAU,QAAQ,QAAQ;AAAE,eAAOH,WAAU,OAAO,QAAQ,QAAQ,WAAY;AACjG,cAAI,IAAI;AACR,iBAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,oBAAQ,GAAG,OAAO;AAAA,cACd,KAAK;AACD,oBAAI,GAAG,KAAK,KAAK,eAAe,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAc,yBAAO,CAAC,GAAa,CAAC;AACxG,uBAAO,CAAC,GAAa,KAAK,UAAU,YAAY,QAAQ,MAAM,CAAC;AAAA,cACnE,KAAK;AAAG,uBAAO,CAAC,GAAc,GAAG,KAAK,CAAC;AAAA,cACvC,KAAK;AACD,oBAAI,GAAG,KAAK,KAAK,eAAe,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAe,yBAAO,CAAC,GAAa,CAAC;AACzG,uBAAO,CAAC,GAAa,KAAK,UAAU,aAAa,QAAQ,MAAM,CAAC;AAAA,cACpE,KAAK;AAAG,uBAAO,CAAC,GAAc,GAAG,KAAK,CAAC;AAAA,cACvC,KAAK;AAAG,sBAAM,IAAI,MAAM,4DAA4D;AAAA,YACxF;AAAA,UACJ,CAAC;AAAA,QACL,CAAC;AAAA,MAAG;AACJ,YAAM,iBAAiB,WAAY;AAC/B,cAAM,KAAK,SAAS;AAAA,MACxB;AACA,YAAM,oBAAoB,WAAY;AAClC,eAAO,cAAc,MAAM,UAAU;AACrC,cAAM,KAAK,YAAY;AAAA,MAC3B;AACA,YAAM,WAAW;AACjB,YAAM,YAAY;AAClB,aAAO;AAAA,IACX;AACA,WAAO,eAAeE,YAAW,WAAW,aAAa;AAAA,MACrD,KAAK,WAAY;AACb,eAAO,KAAK,UAAU,aAAa;AAAA,MACvC;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,WAAO,eAAeA,YAAW,WAAW,aAAa;AAAA,MACrD,KAAK,WAAY;AACb,eAAO,KAAK,UAAU,aAAa;AAAA,MACvC;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,IAAAA,YAAW,UAAU,UAAU,WAAY;AACvC,aAAOH,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,QAAQ;AACZ,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,mBAAK,YAAY,IAAI,uBAAO,KAAK,WAAW,KAAK,QAAQ;AACzD,mBAAK,UAAU,GAAG,WAAW,KAAK,cAAc;AAChD,mBAAK,UAAU,GAAG,cAAc,KAAK,iBAAiB;AACtD,mBAAK,aAAa,OAAO,YAAY,WAAY;AAC7C,oBAAIG,KAAI;AAER,sBAAM,MAAMA,MAAK,MAAM,eAAe,QAAQA,QAAO,SAAS,SAASA,IAAG,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,OAAO;AACzI,wBAAM,kBAAkB;AAAA,gBAC5B;AAAA,cACJ,GAAG,GAAG;AACN,qBAAO,CAAC,GAAa,KAAK,UAAU,QAAQ,CAAC;AAAA,YACjD,KAAK;AACD,iBAAG,KAAK;AACR,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UAC5B;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAD,YAAW,UAAU,aAAa,WAAY;AAC1C,aAAOH,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,kBAAI,CAAC,KAAK,WAAW;AACjB,sBAAM,IAAI,MAAM,sBAAsB;AAAA,cAC1C;AACA,mBAAK,UAAU,mBAAmB,SAAS;AAC3C,mBAAK,UAAU,mBAAmB,YAAY;AAC9C,qBAAO,CAAC,GAAa,KAAK,UAAU,WAAW,CAAC;AAAA,YACpD,KAAK;AACD,iBAAG,KAAK;AACR,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UAC5B;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAE,YAAW,UAAU,kBAAkB,SAAU,aAAa;AAC1D,aAAOH,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI;AACJ,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,kBAAI,CAAC,KAAK,WAAW;AACjB,sBAAM,IAAI,MAAM,sBAAsB;AAAA,cAC1C;AACA,qBAAO,CAAC,GAAa,KAAK,aAAa,qBAAqB;AAAA,gBACpD,aAAa,aAAAI,QAAK,OAAO,WAAW;AAAA,cACxC,CAAC,CAAC;AAAA,YACV,KAAK;AACD,kCAAqB,GAAG,KAAK,EAAG;AAChC,qBAAO,CAAC,GAAc,aAAAA,QAAK,OAAO,iBAAiB,CAAC;AAAA,UAC5D;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAF,YAAW,UAAU,sBAAsB,SAAU,cAAc;AAC/D,aAAOH,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI;AACJ,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,kBAAI,CAAC,KAAK,WAAW;AACjB,sBAAM,IAAI,MAAM,sBAAsB;AAAA,cAC1C;AACA,qBAAO,CAAC,GAAa,KAAK,aAAa,yBAAyB;AAAA,gBACxD,cAAc,aAAa,IAAI,SAAU,aAAa;AAAE,yBAAO,aAAAI,QAAK,OAAO,WAAW;AAAA,gBAAG,CAAC;AAAA,cAC9F,CAAC,CAAC;AAAA,YACV,KAAK;AACD,mCAAsB,GAAG,KAAK,EAAG;AACjC,qBAAO,CAAC,GAAc,mBAAmB,IAAI,SAAU,aAAa;AAAE,uBAAO,aAAAA,QAAK,OAAO,WAAW;AAAA,cAAG,CAAC,CAAC;AAAA,UACjH;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAF,YAAW,UAAU,yBAAyB,SAAU,aAAa,SAAS;AAC1E,aAAOH,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI;AACJ,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,kBAAI,CAAC,KAAK,WAAW;AACjB,sBAAM,IAAI,MAAM,sBAAsB;AAAA,cAC1C;AACA,qBAAO,CAAC,GAAa,KAAK,aAAa,0BAA0B;AAAA,gBACzD,aAAa,aAAAI,QAAK,OAAO,WAAW;AAAA,gBACpC;AAAA,cACJ,CAAC,CAAC;AAAA,YACV,KAAK;AACD,yBAAY,GAAG,KAAK;AACpB,qBAAO,CAAC,GAAc,SAAS,SAAS;AAAA,UAChD;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAF,YAAW,UAAU,cAAc,SAAU,MAAM,SAAS;AACxD,UAAI,YAAY,QAAQ;AAAE,kBAAU;AAAA,MAAO;AAC3C,aAAOH,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI;AACJ,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,kBAAI,CAAC,KAAK,WAAW;AACjB,sBAAM,IAAI,MAAM,sBAAsB;AAAA,cAC1C;AACA,qBAAO,CAAC,GAAa,KAAK,UAAU,KAAK,MAAM,OAAO,CAAC;AAAA,YAC3D,KAAK;AACD,0BAAa,GAAG,KAAK,EAAG;AACxB,qBAAO,CAAC,GAAc,WAAW,KAAK,SAAS,CAAC;AAAA,UACxD;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,WAAOE;AAAA,EACX,EAAE,YAAa;AAAA;AACf,IAAO,cAAQ;;;ACnKf;;;AC3DA,IAAI;AACJ,IAAM,QAAQ,IAAI,WAAW,EAAE;AAChB,SAAR,MAAuB;AAE5B,MAAI,CAAC,iBAAiB;AAEpB,sBAAkB,OAAO,WAAW,eAAe,OAAO,mBAAmB,OAAO,gBAAgB,KAAK,MAAM;AAE/G,QAAI,CAAC,iBAAiB;AACpB,YAAM,IAAI,MAAM,0GAA0G;AAAA,IAC5H;AAAA,EACF;AAEA,SAAO,gBAAgB,KAAK;AAC9B;;;ACjBA,IAAO,gBAAQ;;;ACEf,SAAS,SAAS,MAAM;AACtB,SAAO,OAAO,SAAS,YAAY,cAAM,KAAK,IAAI;AACpD;AAEA,IAAO,mBAAQ;;;ACAf,IAAM,YAAY,CAAC;AAEnB,SAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAC5B,YAAU,MAAM,IAAI,KAAO,SAAS,EAAE,EAAE,MAAM,CAAC,CAAC;AAClD;AAEO,SAAS,gBAAgB,KAAK,SAAS,GAAG;AAG/C,SAAO,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,MAAM,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,MAAM,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,MAAM,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,MAAM,UAAU,IAAI,SAAS,EAAE,CAAC,IAAI,UAAU,IAAI,SAAS,EAAE,CAAC,IAAI,UAAU,IAAI,SAAS,EAAE,CAAC,IAAI,UAAU,IAAI,SAAS,EAAE,CAAC,IAAI,UAAU,IAAI,SAAS,EAAE,CAAC,IAAI,UAAU,IAAI,SAAS,EAAE,CAAC;AACnf;;;ACdA,SAAS,MAAM,MAAM;AACnB,MAAI,CAAC,iBAAS,IAAI,GAAG;AACnB,UAAM,UAAU,cAAc;AAAA,EAChC;AAEA,MAAI;AACJ,QAAM,MAAM,IAAI,WAAW,EAAE;AAE7B,MAAI,CAAC,KAAK,IAAI,SAAS,KAAK,MAAM,GAAG,CAAC,GAAG,EAAE,OAAO;AAClD,MAAI,CAAC,IAAI,MAAM,KAAK;AACpB,MAAI,CAAC,IAAI,MAAM,IAAI;AACnB,MAAI,CAAC,IAAI,IAAI;AAEb,MAAI,CAAC,KAAK,IAAI,SAAS,KAAK,MAAM,GAAG,EAAE,GAAG,EAAE,OAAO;AACnD,MAAI,CAAC,IAAI,IAAI;AAEb,MAAI,CAAC,KAAK,IAAI,SAAS,KAAK,MAAM,IAAI,EAAE,GAAG,EAAE,OAAO;AACpD,MAAI,CAAC,IAAI,IAAI;AAEb,MAAI,CAAC,KAAK,IAAI,SAAS,KAAK,MAAM,IAAI,EAAE,GAAG,EAAE,OAAO;AACpD,MAAI,CAAC,IAAI,IAAI;AAGb,MAAI,EAAE,KAAK,IAAI,SAAS,KAAK,MAAM,IAAI,EAAE,GAAG,EAAE,KAAK,gBAAgB;AACnE,MAAI,EAAE,IAAI,IAAI,aAAc;AAC5B,MAAI,EAAE,IAAI,MAAM,KAAK;AACrB,MAAI,EAAE,IAAI,MAAM,KAAK;AACrB,MAAI,EAAE,IAAI,MAAM,IAAI;AACpB,MAAI,EAAE,IAAI,IAAI;AACd,SAAO;AACT;AAEA,IAAO,gBAAQ;;;AC/Bf,SAAS,cAAc,KAAK;AAC1B,QAAM,SAAS,mBAAmB,GAAG,CAAC;AAEtC,QAAM,QAAQ,CAAC;AAEf,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AACnC,UAAM,KAAK,IAAI,WAAW,CAAC,CAAC;AAAA,EAC9B;AAEA,SAAO;AACT;AAEO,IAAM,MAAM;AACZ,IAAMG,OAAM;AACJ,SAAR,IAAqB,MAAM,SAAS,UAAU;AACnD,WAAS,aAAa,OAAO,WAAW,KAAK,QAAQ;AACnD,QAAI;AAEJ,QAAI,OAAO,UAAU,UAAU;AAC7B,cAAQ,cAAc,KAAK;AAAA,IAC7B;AAEA,QAAI,OAAO,cAAc,UAAU;AACjC,kBAAY,cAAM,SAAS;AAAA,IAC7B;AAEA,UAAM,aAAa,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,YAAY,IAAI;AACpG,YAAM,UAAU,kEAAkE;AAAA,IACpF;AAKA,QAAI,QAAQ,IAAI,WAAW,KAAK,MAAM,MAAM;AAC5C,UAAM,IAAI,SAAS;AACnB,UAAM,IAAI,OAAO,UAAU,MAAM;AACjC,YAAQ,SAAS,KAAK;AACtB,UAAM,CAAC,IAAI,MAAM,CAAC,IAAI,KAAO;AAC7B,UAAM,CAAC,IAAI,MAAM,CAAC,IAAI,KAAO;AAE7B,QAAI,KAAK;AACP,eAAS,UAAU;AAEnB,eAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AAC3B,YAAI,SAAS,CAAC,IAAI,MAAM,CAAC;AAAA,MAC3B;AAEA,aAAO;AAAA,IACT;AAEA,WAAO,gBAAgB,KAAK;AAAA,EAC9B;AAGA,MAAI;AACF,iBAAa,OAAO;AAAA,EACtB,SAAS,KAAK;AAAA,EAAC;AAGf,eAAa,MAAM;AACnB,eAAa,MAAMA;AACnB,SAAO;AACT;;;AC7CA,SAAS,IAAI,OAAO;AAClB,MAAI,OAAO,UAAU,UAAU;AAC7B,UAAM,MAAM,SAAS,mBAAmB,KAAK,CAAC;AAE9C,YAAQ,IAAI,WAAW,IAAI,MAAM;AAEjC,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AACnC,YAAM,CAAC,IAAI,IAAI,WAAW,CAAC;AAAA,IAC7B;AAAA,EACF;AAEA,SAAO,qBAAqB,WAAW,aAAa,KAAK,GAAG,MAAM,SAAS,CAAC,CAAC;AAC/E;AAMA,SAAS,qBAAqB,OAAO;AACnC,QAAM,SAAS,CAAC;AAChB,QAAM,WAAW,MAAM,SAAS;AAChC,QAAM,SAAS;AAEf,WAAS,IAAI,GAAG,IAAI,UAAU,KAAK,GAAG;AACpC,UAAM,IAAI,MAAM,KAAK,CAAC,MAAM,IAAI,KAAK;AACrC,UAAM,MAAM,SAAS,OAAO,OAAO,MAAM,IAAI,EAAI,IAAI,OAAO,OAAO,IAAI,EAAI,GAAG,EAAE;AAChF,WAAO,KAAK,GAAG;AAAA,EACjB;AAEA,SAAO;AACT;AAMA,SAAS,gBAAgB,cAAc;AACrC,UAAQ,eAAe,OAAO,KAAK,KAAK,KAAK;AAC/C;AAMA,SAAS,WAAW,GAAG,KAAK;AAE1B,IAAE,OAAO,CAAC,KAAK,OAAQ,MAAM;AAC7B,IAAE,gBAAgB,GAAG,IAAI,CAAC,IAAI;AAC9B,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AAER,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK,IAAI;AACrC,UAAM,OAAO;AACb,UAAM,OAAO;AACb,UAAM,OAAO;AACb,UAAM,OAAO;AACb,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,UAAU;AACzC,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,SAAS;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,SAAS;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,MAAM;AAC3C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,SAAS;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,UAAU;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,WAAW;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,SAAS;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,UAAU;AAC1C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,QAAQ;AAC5C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,UAAU;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,SAAS;AAC5C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,SAAS;AAC5C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,OAAO;AAC1C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,UAAU;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,SAAS;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,WAAW;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,SAAS;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,UAAU;AAC1C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,QAAQ;AAC5C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,UAAU;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,SAAS;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,UAAU;AACzC,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,SAAS;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,QAAQ;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,SAAS;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,UAAU;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,SAAS;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,QAAQ,GAAG,IAAI;AACnB,QAAI,QAAQ,GAAG,IAAI;AACnB,QAAI,QAAQ,GAAG,IAAI;AACnB,QAAI,QAAQ,GAAG,IAAI;AAAA,EACrB;AAEA,SAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AACpB;AAOA,SAAS,aAAa,OAAO;AAC3B,MAAI,MAAM,WAAW,GAAG;AACtB,WAAO,CAAC;AAAA,EACV;AAEA,QAAM,UAAU,MAAM,SAAS;AAC/B,QAAM,SAAS,IAAI,YAAY,gBAAgB,OAAO,CAAC;AAEvD,WAAS,IAAI,GAAG,IAAI,SAAS,KAAK,GAAG;AACnC,WAAO,KAAK,CAAC,MAAM,MAAM,IAAI,CAAC,IAAI,QAAS,IAAI;AAAA,EACjD;AAEA,SAAO;AACT;AAOA,SAAS,QAAQ,GAAG,GAAG;AACrB,QAAM,OAAO,IAAI,UAAW,IAAI;AAChC,QAAM,OAAO,KAAK,OAAO,KAAK,OAAO,OAAO;AAC5C,SAAO,OAAO,KAAK,MAAM;AAC3B;AAMA,SAAS,cAAc,KAAK,KAAK;AAC/B,SAAO,OAAO,MAAM,QAAQ,KAAK;AACnC;AAMA,SAAS,OAAO,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAChC,SAAO,QAAQ,cAAc,QAAQ,QAAQ,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;AAC3E;AAEA,SAAS,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAClC,SAAO,OAAO,IAAI,IAAI,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC7C;AAEA,SAAS,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAClC,SAAO,OAAO,IAAI,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC7C;AAEA,SAAS,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAClC,SAAO,OAAO,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AACxC;AAEA,SAAS,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAClC,SAAO,OAAO,KAAK,IAAI,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC;AAC3C;AAEA,IAAO,cAAQ;;;ACpNf,IAAM,KAAK,IAAI,MAAM,IAAM,WAAG;;;ACF9B,IAAM,aAAa,OAAO,WAAW,eAAe,OAAO,cAAc,OAAO,WAAW,KAAK,MAAM;AACtG,IAAO,iBAAQ;AAAA,EACb;AACF;;;ACCA,SAAS,GAAG,SAAS,KAAK,QAAQ;AAChC,MAAI,eAAO,cAAc,CAAC,OAAO,CAAC,SAAS;AACzC,WAAO,eAAO,WAAW;AAAA,EAC3B;AAEA,YAAU,WAAW,CAAC;AACtB,QAAM,OAAO,QAAQ,WAAW,QAAQ,OAAO,KAAK;AAEpD,OAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAO;AAC3B,OAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAO;AAE3B,MAAI,KAAK;AACP,aAAS,UAAU;AAEnB,aAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AAC3B,UAAI,SAAS,CAAC,IAAI,KAAK,CAAC;AAAA,IAC1B;AAEA,WAAO;AAAA,EACT;AAEA,SAAO,gBAAgB,IAAI;AAC7B;AAEA,IAAO,aAAQ;;;AC1Bf,SAAS,EAAE,GAAG,GAAG,GAAG,GAAG;AACrB,UAAQ,GAAG;AAAA,IACT,KAAK;AACH,aAAO,IAAI,IAAI,CAAC,IAAI;AAAA,IAEtB,KAAK;AACH,aAAO,IAAI,IAAI;AAAA,IAEjB,KAAK;AACH,aAAO,IAAI,IAAI,IAAI,IAAI,IAAI;AAAA,IAE7B,KAAK;AACH,aAAO,IAAI,IAAI;AAAA,EACnB;AACF;AAEA,SAAS,KAAK,GAAG,GAAG;AAClB,SAAO,KAAK,IAAI,MAAM,KAAK;AAC7B;AAEA,SAAS,KAAK,OAAO;AACnB,QAAM,IAAI,CAAC,YAAY,YAAY,YAAY,UAAU;AACzD,QAAM,IAAI,CAAC,YAAY,YAAY,YAAY,WAAY,UAAU;AAErE,MAAI,OAAO,UAAU,UAAU;AAC7B,UAAM,MAAM,SAAS,mBAAmB,KAAK,CAAC;AAE9C,YAAQ,CAAC;AAET,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AACnC,YAAM,KAAK,IAAI,WAAW,CAAC,CAAC;AAAA,IAC9B;AAAA,EACF,WAAW,CAAC,MAAM,QAAQ,KAAK,GAAG;AAEhC,YAAQ,MAAM,UAAU,MAAM,KAAK,KAAK;AAAA,EAC1C;AAEA,QAAM,KAAK,GAAI;AACf,QAAM,IAAI,MAAM,SAAS,IAAI;AAC7B,QAAM,IAAI,KAAK,KAAK,IAAI,EAAE;AAC1B,QAAM,IAAI,IAAI,MAAM,CAAC;AAErB,WAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,UAAM,MAAM,IAAI,YAAY,EAAE;AAE9B,aAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AAC3B,UAAI,CAAC,IAAI,MAAM,IAAI,KAAK,IAAI,CAAC,KAAK,KAAK,MAAM,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,KAAK,MAAM,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,IAAI,CAAC;AAAA,IACpI;AAEA,MAAE,CAAC,IAAI;AAAA,EACT;AAEA,IAAE,IAAI,CAAC,EAAE,EAAE,KAAK,MAAM,SAAS,KAAK,IAAI,KAAK,IAAI,GAAG,EAAE;AACtD,IAAE,IAAI,CAAC,EAAE,EAAE,IAAI,KAAK,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC;AACtC,IAAE,IAAI,CAAC,EAAE,EAAE,KAAK,MAAM,SAAS,KAAK,IAAI;AAExC,WAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,UAAM,IAAI,IAAI,YAAY,EAAE;AAE5B,aAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AAC3B,QAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AAAA,IACf;AAEA,aAAS,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG;AAC5B,QAAE,CAAC,IAAI,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC;AAAA,IAC5D;AAEA,QAAI,IAAI,EAAE,CAAC;AACX,QAAI,IAAI,EAAE,CAAC;AACX,QAAI,IAAI,EAAE,CAAC;AACX,QAAI,IAAI,EAAE,CAAC;AACX,QAAI,IAAI,EAAE,CAAC;AAEX,aAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AAC3B,YAAM,IAAI,KAAK,MAAM,IAAI,EAAE;AAC3B,YAAM,IAAI,KAAK,GAAG,CAAC,IAAI,EAAE,GAAG,GAAG,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM;AAC3D,UAAI;AACJ,UAAI;AACJ,UAAI,KAAK,GAAG,EAAE,MAAM;AACpB,UAAI;AACJ,UAAI;AAAA,IACN;AAEA,MAAE,CAAC,IAAI,EAAE,CAAC,IAAI,MAAM;AACpB,MAAE,CAAC,IAAI,EAAE,CAAC,IAAI,MAAM;AACpB,MAAE,CAAC,IAAI,EAAE,CAAC,IAAI,MAAM;AACpB,MAAE,CAAC,IAAI,EAAE,CAAC,IAAI,MAAM;AACpB,MAAE,CAAC,IAAI,EAAE,CAAC,IAAI,MAAM;AAAA,EACtB;AAEA,SAAO,CAAC,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,IAAI,KAAM,EAAE,CAAC,IAAI,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,IAAI,KAAM,EAAE,CAAC,IAAI,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,IAAI,KAAM,EAAE,CAAC,IAAI,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,IAAI,KAAM,EAAE,CAAC,IAAI,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,IAAI,KAAM,EAAE,CAAC,IAAI,GAAI;AACjW;AAEA,IAAO,eAAQ;;;AC7Ff,IAAM,KAAK,IAAI,MAAM,IAAM,YAAI;;;AZ+D/B,IAAAC,eAAiB;AAjEjB,IAAIC,aAAyC,2BAAY;AACrD,MAAI,gBAAgB,SAAU,GAAG,GAAG;AAChC,oBAAgB,OAAO,kBAClB,EAAE,WAAW,CAAC,EAAE,aAAa,SAAS,SAAUC,IAAGC,IAAG;AAAE,MAAAD,GAAE,YAAYC;AAAA,IAAG,KAC1E,SAAUD,IAAGC,IAAG;AAAE,eAAS,KAAKA;AAAG,YAAI,OAAO,UAAU,eAAe,KAAKA,IAAG,CAAC;AAAG,UAAAD,GAAE,CAAC,IAAIC,GAAE,CAAC;AAAA,IAAG;AACpG,WAAO,cAAc,GAAG,CAAC;AAAA,EAC7B;AACA,SAAO,SAAU,GAAG,GAAG;AACnB,QAAI,OAAO,MAAM,cAAc,MAAM;AACjC,YAAM,IAAI,UAAU,yBAAyB,OAAO,CAAC,IAAI,+BAA+B;AAC5F,kBAAc,GAAG,CAAC;AAClB,aAAS,KAAK;AAAE,WAAK,cAAc;AAAA,IAAG;AACtC,MAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,WAAW,IAAI,GAAG;AAAA,EACtF;AACJ,EAAG;AACH,IAAIC,YAAsC,WAAY;AAClD,EAAAA,YAAW,OAAO,UAAU,SAAS,GAAG;AACpC,aAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,UAAI,UAAU,CAAC;AACf,eAAS,KAAK;AAAG,YAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC;AAC1D,YAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAClB;AACA,WAAO;AAAA,EACX;AACA,SAAOA,UAAS,MAAM,MAAM,SAAS;AACzC;AACA,IAAIC,aAAwC,SAAU,SAAS,YAAY,GAAG,WAAW;AACrF,WAAS,MAAM,OAAO;AAAE,WAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAAE,cAAQ,KAAK;AAAA,IAAG,CAAC;AAAA,EAAG;AAC3G,SAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,aAAS,UAAU,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,KAAK,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC1F,aAAS,SAAS,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC7F,aAAS,KAAK,QAAQ;AAAE,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,IAAG;AAC7G,UAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EACxE,CAAC;AACL;AACA,IAAIC,eAA4C,SAAU,SAAS,MAAM;AACrE,MAAI,IAAI,EAAE,OAAO,GAAG,MAAM,WAAW;AAAE,QAAI,EAAE,CAAC,IAAI;AAAG,YAAM,EAAE,CAAC;AAAG,WAAO,EAAE,CAAC;AAAA,EAAG,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,EAAE,GAAGC,IAAG,GAAG,GAAG;AAC/G,SAAO,IAAI,EAAE,MAAM,KAAK,CAAC,GAAG,SAAS,KAAK,CAAC,GAAG,UAAU,KAAK,CAAC,EAAE,GAAG,OAAO,WAAW,eAAe,EAAE,OAAO,QAAQ,IAAI,WAAW;AAAE,WAAO;AAAA,EAAM,IAAI;AACvJ,WAAS,KAAK,GAAG;AAAE,WAAO,SAAU,GAAG;AAAE,aAAO,KAAK,CAAC,GAAG,CAAC,CAAC;AAAA,IAAG;AAAA,EAAG;AACjE,WAAS,KAAK,IAAI;AACd,QAAIA;AAAG,YAAM,IAAI,UAAU,iCAAiC;AAC5D,WAAO,MAAM,IAAI,GAAG,GAAG,CAAC,MAAM,IAAI,KAAK;AAAG,UAAI;AAC1C,YAAIA,KAAI,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,IAAI,EAAE,QAAQ,IAAI,GAAG,CAAC,IAAI,EAAE,OAAO,OAAO,IAAI,EAAE,QAAQ,MAAM,EAAE,KAAK,CAAC,GAAG,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG;AAAM,iBAAO;AAC3J,YAAI,IAAI,GAAG;AAAG,eAAK,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,KAAK;AACtC,gBAAQ,GAAG,CAAC,GAAG;AAAA,UACX,KAAK;AAAA,UAAG,KAAK;AAAG,gBAAI;AAAI;AAAA,UACxB,KAAK;AAAG,cAAE;AAAS,mBAAO,EAAE,OAAO,GAAG,CAAC,GAAG,MAAM,MAAM;AAAA,UACtD,KAAK;AAAG,cAAE;AAAS,gBAAI,GAAG,CAAC;AAAG,iBAAK,CAAC,CAAC;AAAG;AAAA,UACxC,KAAK;AAAG,iBAAK,EAAE,IAAI,IAAI;AAAG,cAAE,KAAK,IAAI;AAAG;AAAA,UACxC;AACI,gBAAI,EAAE,IAAI,EAAE,MAAM,IAAI,EAAE,SAAS,KAAK,EAAE,EAAE,SAAS,CAAC,OAAO,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,IAAI;AAAE,kBAAI;AAAG;AAAA,YAAU;AAC3G,gBAAI,GAAG,CAAC,MAAM,MAAM,CAAC,KAAM,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,IAAK;AAAE,gBAAE,QAAQ,GAAG,CAAC;AAAG;AAAA,YAAO;AACrF,gBAAI,GAAG,CAAC,MAAM,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,gBAAE,QAAQ,EAAE,CAAC;AAAG,kBAAI;AAAI;AAAA,YAAO;AACpE,gBAAI,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,gBAAE,QAAQ,EAAE,CAAC;AAAG,gBAAE,IAAI,KAAK,EAAE;AAAG;AAAA,YAAO;AAClE,gBAAI,EAAE,CAAC;AAAG,gBAAE,IAAI,IAAI;AACpB,cAAE,KAAK,IAAI;AAAG;AAAA,QACtB;AACA,aAAK,KAAK,KAAK,SAAS,CAAC;AAAA,MAC7B,SAAS,GAAG;AAAE,aAAK,CAAC,GAAG,CAAC;AAAG,YAAI;AAAA,MAAG,UAAE;AAAU,QAAAA,KAAI,IAAI;AAAA,MAAG;AACzD,QAAI,GAAG,CAAC,IAAI;AAAG,YAAM,GAAG,CAAC;AAAG,WAAO,EAAE,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,QAAQ,MAAM,KAAK;AAAA,EACnF;AACJ;AAKA,IAAI;AAAA;AAAA,EAA+B,SAAU,QAAQ;AACjD,IAAAN,WAAUO,gBAAe,MAAM;AAC/B,aAASA,eAAc,QAAQ,WAAW;AACtC,UAAI,QAAQ;AACZ,UAAI;AACJ,cAAQ,OAAO,KAAK,IAAI,KAAK;AAC7B,YAAM,aAAa;AACnB,YAAM,mBAAmB,CAAC;AAC1B,YAAM,gBAAgB,SAAU,MAAM;AAClC,YAAI,MAAM,iBAAiB,KAAK,EAAE,GAAG;AACjC,cAAIC,MAAK,MAAM,iBAAiB,KAAK,EAAE,GAAG,UAAUA,IAAG,SAAS,SAASA,IAAG;AAC5E,iBAAO,MAAM,iBAAiB,KAAK,EAAE;AACrC,cAAI,KAAK,OAAO;AACZ,mBAAO,KAAK,KAAK;AAAA,UACrB,OACK;AACD,oBAAQ,KAAK,MAAM;AAAA,UACvB;AAAA,QACJ;AAAA,MACJ;AACA,YAAM,eAAe,SAAU,MAAM;AACjC,YAAI,CAAC,MAAM,WAAW;AAClB,gBAAM,IAAI,MAAM,sBAAsB;AAAA,QAC1C;AACA,eAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC1C,cAAIA,KAAI;AACR,cAAI,YAAY,WAAO;AACvB,gBAAM,iBAAiB,SAAS,IAAI,EAAE,SAAkB,OAAe;AACvE,WAAC,MAAMA,MAAK,MAAM,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,mBAAmB,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY;AAAA,YAClI,SAAS;AAAA,YACT,MAAML,UAAS,EAAE,IAAI,UAAU,GAAG,IAAI;AAAA,UAC1C,GAAG,GAAG;AAAA,QACV,CAAC;AAAA,MACL;AACA,YAAM,UAAU;AAChB,YAAM,aAAa,IAAI,WAAW,KAAK,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,SAAS,CAAC;AACxK,aAAO;AAAA,IACX;AACA,WAAO,eAAeI,eAAc,WAAW,aAAa;AAAA,MACxD,KAAK,WAAY;AACb,eAAO,KAAK,cAAc;AAAA,MAC9B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,WAAO,eAAeA,eAAc,WAAW,aAAa;AAAA,MACxD,KAAK,WAAY;AACb,eAAO;AAAA,MACX;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,IAAAA,eAAc,UAAU,UAAU,WAAY;AAC1C,aAAOH,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,iBAAO;AAAA,YAAC;AAAA;AAAA,UAAY;AAAA,QACxB,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAE,eAAc,UAAU,aAAa,WAAY;AAC7C,aAAOH,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AAAG,qBAAO,CAAC,GAAa,KAAK,aAAa;AAAA,gBACvC,QAAQ;AAAA,cACZ,CAAC,CAAC;AAAA,YACN,KAAK;AACD,iBAAG,KAAK;AACR,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UAC5B;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAE,eAAc,UAAU,kBAAkB,SAAU,aAAa;AAC7D,UAAI;AACJ,aAAOH,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,mBAAmB;AACvB,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,kBAAI,CAAC,KAAK,WAAW;AACjB,sBAAM,IAAI,MAAM,sBAAsB;AAAA,cAC1C;AACA,iBAAG,QAAQ;AAAA,YACf,KAAK;AACD,iBAAG,KAAK,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC;AACxB,qBAAO,CAAC,GAAa,KAAK,aAAa;AAAA,gBAC/B,QAAQ;AAAA,gBACR,QAAQ;AAAA,kBACJ,aAAa,aAAAI,QAAK,OAAO,WAAW;AAAA,gBACxC;AAAA,cACJ,CAAC,CAAC;AAAA,YACV,KAAK;AACD,kCAAoB,GAAG,KAAK;AAC5B,qBAAO,CAAC,GAAc,aAAAA,QAAK,OAAO,iBAAiB,CAAC;AAAA,YACxD,KAAK;AACD,oBAAM,GAAG,KAAK;AACd,oBAAM,IAAI,QAAQ,KAAK,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,GAAG,MAAM,4BAA4B;AAAA,YACrK,KAAK;AAAG,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UAChC;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAF,eAAc,UAAU,sBAAsB,SAAU,cAAc;AAClE,UAAI;AACJ,aAAOH,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,oBAAoB;AACxB,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,kBAAI,CAAC,KAAK,WAAW;AACjB,sBAAM,IAAI,MAAM,sBAAsB;AAAA,cAC1C;AACA,iBAAG,QAAQ;AAAA,YACf,KAAK;AACD,iBAAG,KAAK,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC;AACxB,qBAAO,CAAC,GAAa,KAAK,aAAa;AAAA,gBAC/B,QAAQ;AAAA,gBACR,QAAQ;AAAA,kBACJ,cAAc,aAAa,IAAI,SAAU,aAAa;AAAE,2BAAO,aAAAI,QAAK,OAAO,WAAW;AAAA,kBAAG,CAAC;AAAA,gBAC9F;AAAA,cACJ,CAAC,CAAC;AAAA,YACV,KAAK;AACD,mCAAqB,GAAG,KAAK;AAC7B,qBAAO,CAAC,GAAc,mBAAmB,IAAI,SAAU,aAAa;AAAE,uBAAO,aAAAA,QAAK,OAAO,WAAW;AAAA,cAAG,CAAC,CAAC;AAAA,YAC7G,KAAK;AACD,oBAAM,GAAG,KAAK;AACd,oBAAM,IAAI,QAAQ,KAAK,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,GAAG,MAAM,6BAA6B;AAAA,YACtK,KAAK;AAAG,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UAChC;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAF,eAAc,UAAU,yBAAyB,SAAU,aAAa,SAAS;AAC7E,UAAI;AACJ,aAAOH,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,QAAQ;AACZ,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,kBAAI,CAAC,KAAK,WAAW;AACjB,sBAAM,IAAI,MAAM,sBAAsB;AAAA,cAC1C;AACA,iBAAG,QAAQ;AAAA,YACf,KAAK;AACD,iBAAG,KAAK,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC;AACxB,qBAAO,CAAC,GAAa,KAAK,aAAa;AAAA,gBAC/B,QAAQ;AAAA,gBACR,QAAQ;AAAA,kBACJ,aAAa,aAAAI,QAAK,OAAO,WAAW;AAAA,kBACpC;AAAA,gBACJ;AAAA,cACJ,CAAC,CAAC;AAAA,YACV,KAAK;AACD,uBAAS,GAAG,KAAK;AACjB,qBAAO,CAAC,GAAc,MAAM;AAAA,YAChC,KAAK;AACD,oBAAM,GAAG,KAAK;AACd,oBAAM,IAAI,QAAQ,KAAK,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,GAAG,MAAM,qCAAqC;AAAA,YAC9K,KAAK;AAAG,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UAChC;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAF,eAAc,UAAU,cAAc,SAAU,MAAM,SAAS;AAC3D,UAAI;AACJ,UAAI,YAAY,QAAQ;AAAE,kBAAU;AAAA,MAAO;AAC3C,aAAOH,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,QAAQ;AACZ,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,kBAAI,CAAC,KAAK,WAAW;AACjB,sBAAM,IAAI,MAAM,sBAAsB;AAAA,cAC1C;AACA,iBAAG,QAAQ;AAAA,YACf,KAAK;AACD,iBAAG,KAAK,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC;AACxB,qBAAO,CAAC,GAAa,KAAK,aAAa;AAAA,gBAC/B,QAAQ;AAAA,gBACR,QAAQ;AAAA,kBACJ;AAAA,kBACA;AAAA,gBACJ;AAAA,cACJ,CAAC,CAAC;AAAA,YACV,KAAK;AACD,uBAAS,GAAG,KAAK;AACjB,qBAAO,CAAC,GAAc,WAAW,KAAK,aAAAI,QAAK,OAAO,MAAM,CAAC,CAAC;AAAA,YAC9D,KAAK;AACD,oBAAM,GAAG,KAAK;AACd,oBAAM,IAAI,QAAQ,KAAK,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,GAAG,MAAM,wBAAwB;AAAA,YACjK,KAAK;AAAG,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UAChC;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,WAAOF;AAAA,EACX,EAAE,YAAa;AAAA;AACf,IAAO,iBAAQ;;;AaxQR,SAAS,4BAA4B,aAAa;AACrD,SAAO,YAAY,YAAY;AACnC;;;ACFO,IAAI,UAAU;;;AnBArB,IAAIG,aAAyC,2BAAY;AACrD,MAAI,gBAAgB,SAAU,GAAG,GAAG;AAChC,oBAAgB,OAAO,kBAClB,EAAE,WAAW,CAAC,EAAE,aAAa,SAAS,SAAUC,IAAGC,IAAG;AAAE,MAAAD,GAAE,YAAYC;AAAA,IAAG,KAC1E,SAAUD,IAAGC,IAAG;AAAE,eAAS,KAAKA;AAAG,YAAI,OAAO,UAAU,eAAe,KAAKA,IAAG,CAAC;AAAG,UAAAD,GAAE,CAAC,IAAIC,GAAE,CAAC;AAAA,IAAG;AACpG,WAAO,cAAc,GAAG,CAAC;AAAA,EAC7B;AACA,SAAO,SAAU,GAAG,GAAG;AACnB,QAAI,OAAO,MAAM,cAAc,MAAM;AACjC,YAAM,IAAI,UAAU,yBAAyB,OAAO,CAAC,IAAI,+BAA+B;AAC5F,kBAAc,GAAG,CAAC;AAClB,aAAS,KAAK;AAAE,WAAK,cAAc;AAAA,IAAG;AACtC,MAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,WAAW,IAAI,GAAG;AAAA,EACtF;AACJ,EAAG;AACH,IAAIC,YAAsC,WAAY;AAClD,EAAAA,YAAW,OAAO,UAAU,SAAS,GAAG;AACpC,aAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,UAAI,UAAU,CAAC;AACf,eAAS,KAAK;AAAG,YAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC;AAC1D,YAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAClB;AACA,WAAO;AAAA,EACX;AACA,SAAOA,UAAS,MAAM,MAAM,SAAS;AACzC;AACA,IAAIC,aAAwC,SAAU,SAAS,YAAY,GAAG,WAAW;AACrF,WAAS,MAAM,OAAO;AAAE,WAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAAE,cAAQ,KAAK;AAAA,IAAG,CAAC;AAAA,EAAG;AAC3G,SAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,aAAS,UAAU,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,KAAK,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC1F,aAAS,SAAS,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC7F,aAAS,KAAK,QAAQ;AAAE,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,IAAG;AAC7G,UAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EACxE,CAAC;AACL;AACA,IAAIC,eAA4C,SAAU,SAAS,MAAM;AACrE,MAAI,IAAI,EAAE,OAAO,GAAG,MAAM,WAAW;AAAE,QAAI,EAAE,CAAC,IAAI;AAAG,YAAM,EAAE,CAAC;AAAG,WAAO,EAAE,CAAC;AAAA,EAAG,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,EAAE,GAAGC,IAAG,GAAG,GAAG;AAC/G,SAAO,IAAI,EAAE,MAAM,KAAK,CAAC,GAAG,SAAS,KAAK,CAAC,GAAG,UAAU,KAAK,CAAC,EAAE,GAAG,OAAO,WAAW,eAAe,EAAE,OAAO,QAAQ,IAAI,WAAW;AAAE,WAAO;AAAA,EAAM,IAAI;AACvJ,WAAS,KAAK,GAAG;AAAE,WAAO,SAAU,GAAG;AAAE,aAAO,KAAK,CAAC,GAAG,CAAC,CAAC;AAAA,IAAG;AAAA,EAAG;AACjE,WAAS,KAAK,IAAI;AACd,QAAIA;AAAG,YAAM,IAAI,UAAU,iCAAiC;AAC5D,WAAO,MAAM,IAAI,GAAG,GAAG,CAAC,MAAM,IAAI,KAAK;AAAG,UAAI;AAC1C,YAAIA,KAAI,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,IAAI,EAAE,QAAQ,IAAI,GAAG,CAAC,IAAI,EAAE,OAAO,OAAO,IAAI,EAAE,QAAQ,MAAM,EAAE,KAAK,CAAC,GAAG,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG;AAAM,iBAAO;AAC3J,YAAI,IAAI,GAAG;AAAG,eAAK,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,KAAK;AACtC,gBAAQ,GAAG,CAAC,GAAG;AAAA,UACX,KAAK;AAAA,UAAG,KAAK;AAAG,gBAAI;AAAI;AAAA,UACxB,KAAK;AAAG,cAAE;AAAS,mBAAO,EAAE,OAAO,GAAG,CAAC,GAAG,MAAM,MAAM;AAAA,UACtD,KAAK;AAAG,cAAE;AAAS,gBAAI,GAAG,CAAC;AAAG,iBAAK,CAAC,CAAC;AAAG;AAAA,UACxC,KAAK;AAAG,iBAAK,EAAE,IAAI,IAAI;AAAG,cAAE,KAAK,IAAI;AAAG;AAAA,UACxC;AACI,gBAAI,EAAE,IAAI,EAAE,MAAM,IAAI,EAAE,SAAS,KAAK,EAAE,EAAE,SAAS,CAAC,OAAO,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,IAAI;AAAE,kBAAI;AAAG;AAAA,YAAU;AAC3G,gBAAI,GAAG,CAAC,MAAM,MAAM,CAAC,KAAM,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,IAAK;AAAE,gBAAE,QAAQ,GAAG,CAAC;AAAG;AAAA,YAAO;AACrF,gBAAI,GAAG,CAAC,MAAM,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,gBAAE,QAAQ,EAAE,CAAC;AAAG,kBAAI;AAAI;AAAA,YAAO;AACpE,gBAAI,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,gBAAE,QAAQ,EAAE,CAAC;AAAG,gBAAE,IAAI,KAAK,EAAE;AAAG;AAAA,YAAO;AAClE,gBAAI,EAAE,CAAC;AAAG,gBAAE,IAAI,IAAI;AACpB,cAAE,KAAK,IAAI;AAAG;AAAA,QACtB;AACA,aAAK,KAAK,KAAK,SAAS,CAAC;AAAA,MAC7B,SAAS,GAAG;AAAE,aAAK,CAAC,GAAG,CAAC;AAAG,YAAI;AAAA,MAAG,UAAE;AAAU,QAAAA,KAAI,IAAI;AAAA,MAAG;AACzD,QAAI,GAAG,CAAC,IAAI;AAAG,YAAM,GAAG,CAAC;AAAG,WAAO,EAAE,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,QAAQ,MAAM,KAAK;AAAA,EACnF;AACJ;AACA,IAAI,WAAsC,SAAS,GAAG;AAClD,MAAI,IAAI,OAAO,WAAW,cAAc,OAAO,UAAU,IAAI,KAAK,EAAE,CAAC,GAAG,IAAI;AAC5E,MAAI;AAAG,WAAO,EAAE,KAAK,CAAC;AACtB,MAAI,KAAK,OAAO,EAAE,WAAW;AAAU,WAAO;AAAA,MAC1C,MAAM,WAAY;AACd,YAAI,KAAK,KAAK,EAAE;AAAQ,cAAI;AAC5B,eAAO,EAAE,OAAO,KAAK,EAAE,GAAG,GAAG,MAAM,CAAC,EAAE;AAAA,MAC1C;AAAA,IACJ;AACA,QAAM,IAAI,UAAU,IAAI,4BAA4B,iCAAiC;AACzF;AAOA,IAAI;AAAA;AAAA,EAA0B,SAAU,QAAQ;AAC5C,IAAAN,WAAUO,WAAU,MAAM;AAC1B,aAASA,UAAS,QAAQ;AACtB,UAAI,QAAQ,OAAO,KAAK,IAAI,KAAK;AACjC,YAAM,WAAW;AACjB,YAAM,YAAY;AAClB,YAAM,gBAAgB,CAAC;AACvB,YAAM,mBAAmB;AACzB,YAAM,WAAW;AACjB,YAAM,UAAU;AAChB,YAAM,kBAAkB;AACxB,YAAM,0BAA0B;AAChC,YAAM,eAAe,SAAU,OAAO;AAClC,YAAI,IAAI,IAAI,IAAI;AAChB,gBAAQ,MAAM,MAAM;AAAA,UAChB,KAAK,sBAAsB;AACvB,kBAAM,gBAAgB;AACtB,kBAAM,mBAAmB,IAAI,YAAW,MAAM,SAAS,MAAM,YAAY,KAAK,MAAM,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,aAAa,MAAM,aAAa,+BAA+B;AACjM,kBAAM,iBAAiB,GAAG,WAAW,MAAM,aAAa;AACxD,kBAAM,iBAAiB,GAAG,cAAc,MAAM,gBAAgB;AAC9D,kBAAM,iBAAiB,QAAQ;AAC/B,kBAAM,qBAAqB,YAAY;AACvC;AAAA,UACJ;AAAA,UACA,KAAK,WAAW;AACZ,kBAAM,gBAAgB;AACtB,kBAAM,mBAAmB,IAAI,eAAc,MAAM,WAAW,KAAK,MAAM,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,cAAc,EAAE;AACrI,kBAAM,iBAAiB,QAAQ;AAC/B,kBAAM,sBAAsB,KAAK,MAAM,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,OAAO;AAC5F,gBAAI,MAAM,iBAAiB;AACvB,oBAAM,gBAAgB,QAAQ;AAC9B,oBAAM,kBAAkB;AAAA,YAC5B;AACA,kBAAM,KAAK,WAAW,MAAM,SAAS;AACrC;AAAA,UACJ;AAAA,UACA,KAAK,cAAc;AACf,gBAAI,MAAM,iBAAiB;AACvB,oBAAM,gBAAgB,OAAO;AAC7B,oBAAM,kBAAkB;AAAA,YAC5B;AACA,kBAAM,cAAc;AACpB,kBAAM,KAAK,YAAY;AACvB;AAAA,UACJ;AAAA,UACA,KAAK,kBAAkB;AACnB,iBAAK,KAAK,MAAM,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW;AACrE,oBAAM,mBAAmB,IAAI,eAAc,MAAM,SAAS,MAAM,KAAK,SAAS;AAC9E,oBAAM,iBAAiB,QAAQ;AAC/B,oBAAM,KAAK,kBAAkB,MAAM,SAAS;AAAA,YAChD,OACK;AACD,oBAAM,KAAK,kBAAkB,MAAS;AAAA,YAC1C;AACA;AAAA,UACJ;AAAA,UAEA,KAAK,YAAY;AACb,kBAAM,gBAAgB;AACtB;AAAA,UACJ;AAAA,UACA,SAAS;AACL;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AACA,YAAM,gBAAgB,SAAU,MAAM;AAClC,YAAI,KAAK,eAAe,QAAQ;AAC5B,cAAI,KAAK,OAAO,SAAS,cAAc;AACnC,kBAAM,cAAc;AAAA,UACxB,WACS,KAAK,OAAO,SAAS,QAAQ;AAClC,kBAAM,gBAAgB;AAAA,UAC1B;AAAA,QACJ,WACS,KAAK,eAAe,eAAe;AACxC,cAAI,MAAM,SAAS;AACf,kBAAM,QAAQ,MAAM,MAAM,SAAS,KAAK,OAAO,GAAG,IAAI,GAAG,OAAO,KAAK,OAAO,KAAK,IAAI,IAAI;AACzF,kBAAM,QAAQ,MAAM,SAAS,SAAS,KAAK,OAAO,MAAM,IAAI,GAAG,OAAO,KAAK,OAAO,QAAQ,IAAI,IAAI;AAClG,kBAAM,QAAQ,MAAM,OAAO,SAAS,KAAK,OAAO,IAAI,IAAI,GAAG,OAAO,KAAK,OAAO,MAAM,IAAI,IAAI;AAC5F,kBAAM,QAAQ,MAAM,QAAQ,SAAS,KAAK,OAAO,KAAK,IAAI,GAAG,OAAO,KAAK,OAAO,OAAO,IAAI,IAAI;AAC/F,kBAAM,QAAQ,MAAM,QAAQ,SAAS,KAAK,OAAO,KAAK,IAAI,GAAG,OAAO,KAAK,OAAO,OAAO,IAAI,IAAI,KAAK,OAAO;AAC3G,kBAAM,QAAQ,MAAM,SAAS,SAAS,KAAK,OAAO,MAAM,IAAI,GAAG,OAAO,KAAK,OAAO,QAAQ,IAAI,IAAI,KAAK,OAAO;AAAA,UAClH;AAAA,QACJ;AAAA,MACJ;AACA,YAAM,iBAAiB,SAAU,OAAO;AACpC,YAAI;AACJ,cAAM,KAAK,MAAM,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,aAAa,iCAAiC;AACzG;AAAA,QACJ;AACA,YAAI,OAAO,MAAM,KAAK,QAAQ,CAAC;AAC/B,YAAI,KAAK,SAAS,SAAS;AACvB,gBAAM,aAAa,KAAK,KAAK;AAAA,QACjC,WACS,KAAK,SAAS,UAAU;AAC7B,gBAAM,cAAc,IAAI;AAAA,QAC5B,WACS,KAAK,SAAS,YAAY;AAC/B,cAAI,MAAM,kBAAkB;AACxB,kBAAM,iBAAiB,cAAc,IAAI;AAAA,UAC7C;AAAA,QACJ;AAAA,MACJ;AACA,YAAM,iBAAiB,WAAY;AAC/B,YAAI,MAAM,4BAA4B,MAAM;AACxC,wBAAc,MAAM,uBAAuB;AAC3C,gBAAM,0BAA0B;AAAA,QACpC;AACA,YAAI,MAAM,UAAU;AAChB,gBAAM,SAAS,OAAO;AACtB,gBAAM,WAAW;AAAA,QACrB;AAAA,MACJ;AACA,YAAM,0BAA0B,WAAY;AACxC,YAAI,KAAK;AACT,YAAI,WAAW,SAAS,uBAAuB,gCAAgC;AAC/E,YAAI;AACA,mBAAS,aAAa,SAAS,QAAQ,GAAG,eAAe,WAAW,KAAK,GAAG,CAAC,aAAa,MAAM,eAAe,WAAW,KAAK,GAAG;AAC9H,gBAAI,UAAU,aAAa;AAC3B,gBAAI,QAAQ,eAAe;AACvB,sBAAQ,OAAO;AAAA,YACnB;AAAA,UACJ;AAAA,QACJ,SACO,OAAO;AAAE,gBAAM,EAAE,OAAO,MAAM;AAAA,QAAG,UACxC;AACI,cAAI;AACA,gBAAI,gBAAgB,CAAC,aAAa,SAAS,KAAK,WAAW;AAAS,iBAAG,KAAK,UAAU;AAAA,UAC1F,UACA;AAAU,gBAAI;AAAK,oBAAM,IAAI;AAAA,UAAO;AAAA,QACxC;AAAA,MACJ;AACA,YAAM,iBAAiB,WAAY;AAC/B,cAAM,eAAe;AACrB,cAAM,wBAAwB;AAC9B,YAAI,SAASJ,UAASA,UAAS,CAAC,GAAG,MAAM,aAAa,GAAG,EAAE,SAAS,MAAM,YAAY,gBAAgB,QAAQ,OAAO,SAAS,UAAU,IAAI,OAAO,SAAS,SAAS,IAAI,SAAS,GAAG,YAAY,WAAW,UAAU,CAAC;AACvN,YAAI,mBAAmB,MAAM,qBAAqB;AAClD,YAAI,kBAAkB;AAClB,iBAAO,UAAU;AAAA,QACrB;AACA,YAAI,MAAM,WAAW;AACjB,iBAAO,WAAW,MAAM;AAAA,QAC5B;AACA,YAAI,cAAc,OAAO,KAAK,MAAM,EAC/B,IAAI,SAAU,KAAK;AAAE,iBAAO,GAAG,OAAO,KAAK,GAAG,EAAE,OAAO,mBAAmB,OAAO,GAAG,CAAC,CAAC;AAAA,QAAG,CAAC,EAC1F,KAAK,GAAG;AACb,YAAI,YAAY,GAAG,OAAOI,UAAS,YAAY,GAAG,EAAE,OAAO,WAAW;AACtE,cAAM,WAAW,SAAS,cAAc,KAAK;AAC7C,cAAM,SAAS,YAAY;AAC3B,cAAM,SAAS,YAAY,wBAAwB,OAAO,WAAW,2PAA2P;AAChU,iBAAS,KAAK,YAAY,MAAM,QAAQ;AACxC,cAAM,UAAU,MAAM,SAAS,cAAc,QAAQ;AAErD,eAAO,cAAc,MAAM;AAC3B,cAAM,0BAA0B,YAAY,WAAY;AAEpD,iBAAO,cAAc,MAAM;AAAA,QAC/B,GAAG,GAAG;AACN,eAAO,iBAAiB,WAAW,MAAM,gBAAgB,KAAK;AAAA,MAClE;AACA,YAAM,kBAAkB,WAAY;AAChC,YAAI,MAAM,SAAS;AACf,gBAAM,QAAQ,MAAM,MAAM;AAC1B,gBAAM,QAAQ,MAAM,QAAQ;AAC5B,gBAAM,QAAQ,MAAM,SAAS;AAC7B,gBAAM,QAAQ,MAAM,QAAQ;AAAA,QAChC;AAAA,MACJ;AACA,YAAM,gBAAgB,WAAY;AAC9B,YAAI,MAAM,SAAS;AACf,gBAAM,QAAQ,MAAM,MAAM;AAC1B,gBAAM,QAAQ,MAAM,SAAS;AAC7B,gBAAM,QAAQ,MAAM,OAAO;AAC3B,gBAAM,QAAQ,MAAM,QAAQ;AAC5B,gBAAM,QAAQ,MAAM,QAAQ;AAC5B,gBAAM,QAAQ,MAAM,SAAS;AAAA,QACjC;AAAA,MACJ;AACA,YAAM,uBAAuB,WAAY;AACrC,YAAI,cAAc;AACd,iBAAO,aAAa,QAAQ,gCAAgC,KAAK;AAAA,QACrE;AACA,eAAO;AAAA,MACX;AACA,YAAM,uBAAuB,SAAU,SAAS;AAC5C,YAAI,gBAAgB,SAAS;AACzB,uBAAa,QAAQ,kCAAkC,OAAO;AAAA,QAClE;AAAA,MACJ;AACA,YAAM,yBAAyB,WAAY;AACvC,YAAI,cAAc;AACd,uBAAa,WAAW,gCAAgC;AAAA,QAC5D;AAAA,MACJ;AACA,YAAM,gBAAgB,WAAY;AAC9B,YAAI,MAAM,iBAAiB;AACvB,gBAAM,gBAAgB,QAAQ;AAC9B,gBAAM,kBAAkB;AAAA,QAC5B;AACA,cAAM,KAAK,WAAW,MAAM,SAAS;AAAA,MACzC;AACA,YAAM,mBAAmB,WAAY;AACjC,YAAI,MAAM,iBAAiB;AACvB,gBAAM,gBAAgB,OAAO;AAC7B,gBAAM,kBAAkB;AAAA,QAC5B;AACA,cAAM,cAAc;AACpB,cAAM,KAAK,YAAY;AAAA,MAC3B;AACA,YAAM,gBAAgB,WAAY;AAC9B,eAAO,oBAAoB,WAAW,MAAM,gBAAgB,KAAK;AACjE,cAAM,eAAe;AACrB,cAAM,uBAAuB;AAC7B,cAAM,mBAAmB;AAAA,MAC7B;AACA,YAAM,uBAAuB,SAAU,MAAM;AACzC,YAAI,IAAI;AACR,SAAC,MAAM,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,mBAAmB,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY;AAAA,UAClI,SAAS;AAAA,UACT;AAAA,QACJ,GAAG,GAAG;AAAA,MACV;AACA,UAAI,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,SAAS;AAChE,cAAM,WAAW,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO;AAAA,MAC5E;AACA,UAAI,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,UAAU;AACjE,cAAM,YAAY,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO;AAAA,MAC7E;AACA,UAAI,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,QAAQ;AAC/D,cAAM,gBAAgBJ,UAAS,CAAC,GAAG,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,MAAM;AAAA,MACpG;AACA,aAAO;AAAA,IACX;AACA,WAAO,eAAeI,UAAS,WAAW,aAAa;AAAA,MACnD,KAAK,WAAY;AACb,YAAI;AACJ,iBAAS,KAAK,KAAK,sBAAsB,QAAQ,OAAO,SAAS,SAAS,GAAG,cAAc;AAAA,MAC/F;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,WAAO,eAAeA,UAAS,WAAW,eAAe;AAAA,MACrD,KAAK,WAAY;AACb,YAAI;AACJ,eAAO,CAAC,GAAG,KAAK,KAAK,sBAAsB,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,MACnF;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,WAAO,eAAeA,UAAS,WAAW,aAAa;AAAA,MACnD,KAAK,WAAY;AACb,eAAO,KAAK;AAAA,MAChB;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,WAAO,eAAeA,UAAS,WAAW,eAAe;AAAA,MACrD,KAAK,WAAY;AACb,eAAO;AAAA,MACX;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,IAAAA,UAAS,UAAU,UAAU,WAAY;AACrC,aAAOH,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,QAAQ;AACZ,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,kBAAI,KAAK,WAAW;AAChB,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,mBAAK,eAAe;AACpB,qBAAO,CAAC,GAAa,IAAI,QAAQ,SAAU,SAAS,QAAQ;AACpD,sBAAM,kBAAkB,EAAE,SAAkB,OAAe;AAAA,cAC/D,CAAC,CAAC;AAAA,YACV,KAAK;AACD,iBAAG,KAAK;AACR,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UAC5B;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAE,UAAS,UAAU,aAAa,WAAY;AACxC,aAAOH,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,kBAAI,CAAC,KAAK,kBAAkB;AACxB,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,qBAAO,CAAC,GAAa,KAAK,iBAAiB,WAAW,CAAC;AAAA,YAC3D,KAAK;AACD,iBAAG,KAAK;AACR,mBAAK,cAAc;AACnB,mBAAK,KAAK,YAAY;AACtB,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UAC5B;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAE,UAAS,UAAU,kBAAkB,SAAU,aAAa;AACxD,aAAOH,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,uBAAuB;AAC3B,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,kBAAI,CAAC,KAAK,WAAW;AACjB,sBAAM,IAAI,MAAM,sBAAsB;AAAA,cAC1C;AACA,sCAAwB,4BAA4B,WAAW,IAC3D,WAAW,KAAK,YAAY,UAAU,EAAE,kBAAkB,OAAO,sBAAsB,MAAM,CAAC,CAAC,IAC/F,YAAY,UAAU;AAC1B,qBAAO,CAAC,GAAa,KAAK,iBAAiB,gBAAgB,qBAAqB,CAAC;AAAA,YACrF,KAAK;AACD,kCAAoB,GAAG,KAAK;AAC5B,qBAAO,CAAC,GAAc,4BAA4B,WAAW,IAAI,YAAY,KAAK,iBAAiB,IAAI,qBAAqB,YAAY,iBAAiB,CAAC;AAAA,UAClK;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAE,UAAS,UAAU,sBAAsB,SAAU,cAAc;AAC7D,aAAOH,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,wBAAwB;AAC5B,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,kBAAI,CAAC,KAAK,WAAW;AACjB,sBAAM,IAAI,MAAM,sBAAsB;AAAA,cAC1C;AACA,uCAAyB,aAAa,IAAI,SAAU,aAAa;AAC7D,uBAAO,4BAA4B,WAAW,IAC1C,WAAW,KAAK,YAAY,UAAU,EAAE,kBAAkB,OAAO,sBAAsB,MAAM,CAAC,CAAC,IAC/F,YAAY,UAAU;AAAA,cAC9B,CAAC;AACD,qBAAO,CAAC,GAAa,KAAK,iBAAiB,oBAAoB,sBAAsB,CAAC;AAAA,YAC1F,KAAK;AACD,mCAAqB,GAAG,KAAK;AAC7B,kBAAI,mBAAmB,WAAW,aAAa,QAAQ;AACnD,sBAAM,IAAI,MAAM,iCAAiC;AAAA,cACrD;AACA,qBAAO,CAAC,GAAc,mBAAmB,IAAI,SAAU,mBAAmB,OAAO;AACzE,uBAAO,4BAA4B,aAAa,KAAK,CAAC,IAAI,YAAY,KAAK,iBAAiB,IAAI,qBAAqB,YAAY,iBAAiB;AAAA,cACtJ,CAAC,CAAC;AAAA,UACd;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAE,UAAS,UAAU,yBAAyB,SAAU,aAAa,SAAS;AACxE,aAAOH,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI;AACJ,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,kBAAI,CAAC,KAAK,WAAW;AACjB,sBAAM,IAAI,MAAM,sBAAsB;AAAA,cAC1C;AACA,sCAAwB,4BAA4B,WAAW,IAAI,YAAY,UAAU,EAAE,kBAAkB,OAAO,sBAAsB,MAAM,CAAC,IAAI,YAAY,UAAU;AAC3K,qBAAO,CAAC,GAAa,KAAK,iBAAiB,uBAAuB,uBAAuB,OAAO,CAAC;AAAA,YACrG,KAAK;AAAG,qBAAO,CAAC,GAAc,GAAG,KAAK,CAAC;AAAA,UAC3C;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAE,UAAS,UAAU,cAAc,SAAU,MAAM,SAAS;AACtD,UAAI,YAAY,QAAQ;AAAE,kBAAU;AAAA,MAAQ;AAC5C,aAAOH,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,kBAAI,CAAC,KAAK,WAAW;AACjB,sBAAM,IAAI,MAAM,sBAAsB;AAAA,cAC1C;AACA,qBAAO,CAAC,GAAa,KAAK,iBAAiB,YAAY,MAAM,OAAO,CAAC;AAAA,YACzE,KAAK;AAAG,qBAAO,CAAC,GAAc,GAAG,KAAK,CAAC;AAAA,UAC3C;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAE,UAAS,UAAU,OAAO,SAAU,MAAM,SAAS;AAC/C,UAAI,YAAY,QAAQ;AAAE,kBAAU;AAAA,MAAQ;AAC5C,aAAOH,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AAAG,qBAAO,CAAC,GAAa,KAAK,YAAY,MAAM,OAAO,CAAC;AAAA,YAC5D,KAAK;AAAG,qBAAO,CAAC,GAAc,GAAG,KAAK,CAAC;AAAA,UAC3C;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAE,UAAS,UAAU,eAAe,SAAU,SAAS;AACjD,UAAI;AACJ,UAAI,YAAY,QAAQ;AAAE,kBAAU;AAAA,MAAI;AACxC,aAAOH,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,cAAI,OAAO,iBAAiB,KAAK,OAAO,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,aAAa;AACnG,mBAAO,CAAC,GAAc,IAAI;AAAA,UAC9B;AACA,iBAAO,CAAC,GAAc,IAAI,QAAQ,SAAU,SAAS;AAC7C,gBAAI,cAAc;AAClB,2BAAe,YAAY,WAAY;AACnC,kBAAIG;AACJ,kBAAI,OAAO,iBAAiBA,MAAK,OAAO,cAAc,QAAQA,QAAO,SAAS,SAASA,IAAG,aAAa;AACnG,8BAAc,YAAY;AAC1B,6BAAa,WAAW;AACxB,wBAAQ,IAAI;AAAA,cAChB;AAAA,YACJ,GAAG,GAAG;AACN,0BAAc,WAAW,WAAY;AACjC,4BAAc,YAAY;AAC1B,sBAAQ,KAAK;AAAA,YACjB,GAAG,UAAU,GAAI;AAAA,UACrB,CAAC,CAAC;AAAA,QACV,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAD,UAAS,aAAa;AACtB,WAAOA;AAAA,EACX,EAAE,qBAAY;AAAA;AACd,IAAO,cAAQ;", "names": ["EventEmitter", "EventEmitter", "d", "b", "WalletAdapter", "__extends", "d", "b", "f", "Wallet", "_this", "bs58", "import_bs58", "__extends", "d", "b", "__awaiter", "__generator", "f", "WebAdapter", "_a", "bs58", "URL", "import_bs58", "__extends", "d", "b", "__assign", "__awaiter", "__generator", "f", "IframeAdapter", "_a", "bs58", "__extends", "d", "b", "__assign", "__awaiter", "__generator", "f", "Solflare", "_a"]}