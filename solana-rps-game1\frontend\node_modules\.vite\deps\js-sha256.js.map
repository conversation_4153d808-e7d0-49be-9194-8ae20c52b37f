{"version": 3, "sources": ["browser-external:crypto", "../../js-sha256/src/sha256.js"], "sourcesContent": ["module.exports = Object.create(new Proxy({}, {\n  get(_, key) {\n    if (\n      key !== '__esModule' &&\n      key !== '__proto__' &&\n      key !== 'constructor' &&\n      key !== 'splice'\n    ) {\n      console.warn(`Module \"crypto\" has been externalized for browser compatibility. Cannot access \"crypto.${key}\" in client code. See https://vitejs.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`)\n    }\n  }\n}))", "/**\n * [js-sha256]{@link https://github.com/emn178/js-sha256}\n *\n * @version 0.11.0\n * <AUTHOR> <PERSON><PERSON><PERSON><PERSON> [<EMAIL>]\n * @copyright Chen, <PERSON><PERSON><PERSON><PERSON> 2014-2024\n * @license MIT\n */\n/*jslint bitwise: true */\n(function () {\n  'use strict';\n\n  var ERROR = 'input is invalid type';\n  var WINDOW = typeof window === 'object';\n  var root = WINDOW ? window : {};\n  if (root.JS_SHA256_NO_WINDOW) {\n    WINDOW = false;\n  }\n  var WEB_WORKER = !WINDOW && typeof self === 'object';\n  var NODE_JS = !root.JS_SHA256_NO_NODE_JS && typeof process === 'object' && process.versions && process.versions.node;\n  if (NODE_JS) {\n    root = global;\n  } else if (WEB_WORKER) {\n    root = self;\n  }\n  var COMMON_JS = !root.JS_SHA256_NO_COMMON_JS && typeof module === 'object' && module.exports;\n  var AMD = typeof define === 'function' && define.amd;\n  var ARRAY_BUFFER = !root.JS_SHA256_NO_ARRAY_BUFFER && typeof ArrayBuffer !== 'undefined';\n  var HEX_CHARS = '0123456789abcdef'.split('');\n  var EXTRA = [-**********, 8388608, 32768, 128];\n  var SHIFT = [24, 16, 8, 0];\n  var K = [\n    0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5,\n    0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174,\n    0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,\n    0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967,\n    0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,\n    0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,\n    0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3,\n    0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2\n  ];\n  var OUTPUT_TYPES = ['hex', 'array', 'digest', 'arrayBuffer'];\n\n  var blocks = [];\n\n  if (root.JS_SHA256_NO_NODE_JS || !Array.isArray) {\n    Array.isArray = function (obj) {\n      return Object.prototype.toString.call(obj) === '[object Array]';\n    };\n  }\n\n  if (ARRAY_BUFFER && (root.JS_SHA256_NO_ARRAY_BUFFER_IS_VIEW || !ArrayBuffer.isView)) {\n    ArrayBuffer.isView = function (obj) {\n      return typeof obj === 'object' && obj.buffer && obj.buffer.constructor === ArrayBuffer;\n    };\n  }\n\n  var createOutputMethod = function (outputType, is224) {\n    return function (message) {\n      return new Sha256(is224, true).update(message)[outputType]();\n    };\n  };\n\n  var createMethod = function (is224) {\n    var method = createOutputMethod('hex', is224);\n    if (NODE_JS) {\n      method = nodeWrap(method, is224);\n    }\n    method.create = function () {\n      return new Sha256(is224);\n    };\n    method.update = function (message) {\n      return method.create().update(message);\n    };\n    for (var i = 0; i < OUTPUT_TYPES.length; ++i) {\n      var type = OUTPUT_TYPES[i];\n      method[type] = createOutputMethod(type, is224);\n    }\n    return method;\n  };\n\n  var nodeWrap = function (method, is224) {\n    var crypto = require('crypto')\n    var Buffer = require('buffer').Buffer;\n    var algorithm = is224 ? 'sha224' : 'sha256';\n    var bufferFrom;\n    if (Buffer.from && !root.JS_SHA256_NO_BUFFER_FROM) {\n      bufferFrom = Buffer.from;\n    } else {\n      bufferFrom = function (message) {\n        return new Buffer(message);\n      };\n    }\n    var nodeMethod = function (message) {\n      if (typeof message === 'string') {\n        return crypto.createHash(algorithm).update(message, 'utf8').digest('hex');\n      } else {\n        if (message === null || message === undefined) {\n          throw new Error(ERROR);\n        } else if (message.constructor === ArrayBuffer) {\n          message = new Uint8Array(message);\n        }\n      }\n      if (Array.isArray(message) || ArrayBuffer.isView(message) ||\n        message.constructor === Buffer) {\n        return crypto.createHash(algorithm).update(bufferFrom(message)).digest('hex');\n      } else {\n        return method(message);\n      }\n    };\n    return nodeMethod;\n  };\n\n  var createHmacOutputMethod = function (outputType, is224) {\n    return function (key, message) {\n      return new HmacSha256(key, is224, true).update(message)[outputType]();\n    };\n  };\n\n  var createHmacMethod = function (is224) {\n    var method = createHmacOutputMethod('hex', is224);\n    method.create = function (key) {\n      return new HmacSha256(key, is224);\n    };\n    method.update = function (key, message) {\n      return method.create(key).update(message);\n    };\n    for (var i = 0; i < OUTPUT_TYPES.length; ++i) {\n      var type = OUTPUT_TYPES[i];\n      method[type] = createHmacOutputMethod(type, is224);\n    }\n    return method;\n  };\n\n  function Sha256(is224, sharedMemory) {\n    if (sharedMemory) {\n      blocks[0] = blocks[16] = blocks[1] = blocks[2] = blocks[3] =\n        blocks[4] = blocks[5] = blocks[6] = blocks[7] =\n        blocks[8] = blocks[9] = blocks[10] = blocks[11] =\n        blocks[12] = blocks[13] = blocks[14] = blocks[15] = 0;\n      this.blocks = blocks;\n    } else {\n      this.blocks = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];\n    }\n\n    if (is224) {\n      this.h0 = 0xc1059ed8;\n      this.h1 = 0x367cd507;\n      this.h2 = 0x3070dd17;\n      this.h3 = 0xf70e5939;\n      this.h4 = 0xffc00b31;\n      this.h5 = 0x68581511;\n      this.h6 = 0x64f98fa7;\n      this.h7 = 0xbefa4fa4;\n    } else { // 256\n      this.h0 = 0x6a09e667;\n      this.h1 = 0xbb67ae85;\n      this.h2 = 0x3c6ef372;\n      this.h3 = 0xa54ff53a;\n      this.h4 = 0x510e527f;\n      this.h5 = 0x9b05688c;\n      this.h6 = 0x1f83d9ab;\n      this.h7 = 0x5be0cd19;\n    }\n\n    this.block = this.start = this.bytes = this.hBytes = 0;\n    this.finalized = this.hashed = false;\n    this.first = true;\n    this.is224 = is224;\n  }\n\n  Sha256.prototype.update = function (message) {\n    if (this.finalized) {\n      return;\n    }\n    var notString, type = typeof message;\n    if (type !== 'string') {\n      if (type === 'object') {\n        if (message === null) {\n          throw new Error(ERROR);\n        } else if (ARRAY_BUFFER && message.constructor === ArrayBuffer) {\n          message = new Uint8Array(message);\n        } else if (!Array.isArray(message)) {\n          if (!ARRAY_BUFFER || !ArrayBuffer.isView(message)) {\n            throw new Error(ERROR);\n          }\n        }\n      } else {\n        throw new Error(ERROR);\n      }\n      notString = true;\n    }\n    var code, index = 0, i, length = message.length, blocks = this.blocks;\n    while (index < length) {\n      if (this.hashed) {\n        this.hashed = false;\n        blocks[0] = this.block;\n        this.block = blocks[16] = blocks[1] = blocks[2] = blocks[3] =\n          blocks[4] = blocks[5] = blocks[6] = blocks[7] =\n          blocks[8] = blocks[9] = blocks[10] = blocks[11] =\n          blocks[12] = blocks[13] = blocks[14] = blocks[15] = 0;\n      }\n\n      if (notString) {\n        for (i = this.start; index < length && i < 64; ++index) {\n          blocks[i >>> 2] |= message[index] << SHIFT[i++ & 3];\n        }\n      } else {\n        for (i = this.start; index < length && i < 64; ++index) {\n          code = message.charCodeAt(index);\n          if (code < 0x80) {\n            blocks[i >>> 2] |= code << SHIFT[i++ & 3];\n          } else if (code < 0x800) {\n            blocks[i >>> 2] |= (0xc0 | (code >>> 6)) << SHIFT[i++ & 3];\n            blocks[i >>> 2] |= (0x80 | (code & 0x3f)) << SHIFT[i++ & 3];\n          } else if (code < 0xd800 || code >= 0xe000) {\n            blocks[i >>> 2] |= (0xe0 | (code >>> 12)) << SHIFT[i++ & 3];\n            blocks[i >>> 2] |= (0x80 | ((code >>> 6) & 0x3f)) << SHIFT[i++ & 3];\n            blocks[i >>> 2] |= (0x80 | (code & 0x3f)) << SHIFT[i++ & 3];\n          } else {\n            code = 0x10000 + (((code & 0x3ff) << 10) | (message.charCodeAt(++index) & 0x3ff));\n            blocks[i >>> 2] |= (0xf0 | (code >>> 18)) << SHIFT[i++ & 3];\n            blocks[i >>> 2] |= (0x80 | ((code >>> 12) & 0x3f)) << SHIFT[i++ & 3];\n            blocks[i >>> 2] |= (0x80 | ((code >>> 6) & 0x3f)) << SHIFT[i++ & 3];\n            blocks[i >>> 2] |= (0x80 | (code & 0x3f)) << SHIFT[i++ & 3];\n          }\n        }\n      }\n\n      this.lastByteIndex = i;\n      this.bytes += i - this.start;\n      if (i >= 64) {\n        this.block = blocks[16];\n        this.start = i - 64;\n        this.hash();\n        this.hashed = true;\n      } else {\n        this.start = i;\n      }\n    }\n    if (this.bytes > 4294967295) {\n      this.hBytes += this.bytes / 4294967296 << 0;\n      this.bytes = this.bytes % 4294967296;\n    }\n    return this;\n  };\n\n  Sha256.prototype.finalize = function () {\n    if (this.finalized) {\n      return;\n    }\n    this.finalized = true;\n    var blocks = this.blocks, i = this.lastByteIndex;\n    blocks[16] = this.block;\n    blocks[i >>> 2] |= EXTRA[i & 3];\n    this.block = blocks[16];\n    if (i >= 56) {\n      if (!this.hashed) {\n        this.hash();\n      }\n      blocks[0] = this.block;\n      blocks[16] = blocks[1] = blocks[2] = blocks[3] =\n        blocks[4] = blocks[5] = blocks[6] = blocks[7] =\n        blocks[8] = blocks[9] = blocks[10] = blocks[11] =\n        blocks[12] = blocks[13] = blocks[14] = blocks[15] = 0;\n    }\n    blocks[14] = this.hBytes << 3 | this.bytes >>> 29;\n    blocks[15] = this.bytes << 3;\n    this.hash();\n  };\n\n  Sha256.prototype.hash = function () {\n    var a = this.h0, b = this.h1, c = this.h2, d = this.h3, e = this.h4, f = this.h5, g = this.h6,\n      h = this.h7, blocks = this.blocks, j, s0, s1, maj, t1, t2, ch, ab, da, cd, bc;\n\n    for (j = 16; j < 64; ++j) {\n      // rightrotate\n      t1 = blocks[j - 15];\n      s0 = ((t1 >>> 7) | (t1 << 25)) ^ ((t1 >>> 18) | (t1 << 14)) ^ (t1 >>> 3);\n      t1 = blocks[j - 2];\n      s1 = ((t1 >>> 17) | (t1 << 15)) ^ ((t1 >>> 19) | (t1 << 13)) ^ (t1 >>> 10);\n      blocks[j] = blocks[j - 16] + s0 + blocks[j - 7] + s1 << 0;\n    }\n\n    bc = b & c;\n    for (j = 0; j < 64; j += 4) {\n      if (this.first) {\n        if (this.is224) {\n          ab = 300032;\n          t1 = blocks[0] - 1413257819;\n          h = t1 - 150054599 << 0;\n          d = t1 + 24177077 << 0;\n        } else {\n          ab = 704751109;\n          t1 = blocks[0] - 210244248;\n          h = t1 - 1521486534 << 0;\n          d = t1 + 143694565 << 0;\n        }\n        this.first = false;\n      } else {\n        s0 = ((a >>> 2) | (a << 30)) ^ ((a >>> 13) | (a << 19)) ^ ((a >>> 22) | (a << 10));\n        s1 = ((e >>> 6) | (e << 26)) ^ ((e >>> 11) | (e << 21)) ^ ((e >>> 25) | (e << 7));\n        ab = a & b;\n        maj = ab ^ (a & c) ^ bc;\n        ch = (e & f) ^ (~e & g);\n        t1 = h + s1 + ch + K[j] + blocks[j];\n        t2 = s0 + maj;\n        h = d + t1 << 0;\n        d = t1 + t2 << 0;\n      }\n      s0 = ((d >>> 2) | (d << 30)) ^ ((d >>> 13) | (d << 19)) ^ ((d >>> 22) | (d << 10));\n      s1 = ((h >>> 6) | (h << 26)) ^ ((h >>> 11) | (h << 21)) ^ ((h >>> 25) | (h << 7));\n      da = d & a;\n      maj = da ^ (d & b) ^ ab;\n      ch = (h & e) ^ (~h & f);\n      t1 = g + s1 + ch + K[j + 1] + blocks[j + 1];\n      t2 = s0 + maj;\n      g = c + t1 << 0;\n      c = t1 + t2 << 0;\n      s0 = ((c >>> 2) | (c << 30)) ^ ((c >>> 13) | (c << 19)) ^ ((c >>> 22) | (c << 10));\n      s1 = ((g >>> 6) | (g << 26)) ^ ((g >>> 11) | (g << 21)) ^ ((g >>> 25) | (g << 7));\n      cd = c & d;\n      maj = cd ^ (c & a) ^ da;\n      ch = (g & h) ^ (~g & e);\n      t1 = f + s1 + ch + K[j + 2] + blocks[j + 2];\n      t2 = s0 + maj;\n      f = b + t1 << 0;\n      b = t1 + t2 << 0;\n      s0 = ((b >>> 2) | (b << 30)) ^ ((b >>> 13) | (b << 19)) ^ ((b >>> 22) | (b << 10));\n      s1 = ((f >>> 6) | (f << 26)) ^ ((f >>> 11) | (f << 21)) ^ ((f >>> 25) | (f << 7));\n      bc = b & c;\n      maj = bc ^ (b & d) ^ cd;\n      ch = (f & g) ^ (~f & h);\n      t1 = e + s1 + ch + K[j + 3] + blocks[j + 3];\n      t2 = s0 + maj;\n      e = a + t1 << 0;\n      a = t1 + t2 << 0;\n      this.chromeBugWorkAround = true;\n    }\n\n    this.h0 = this.h0 + a << 0;\n    this.h1 = this.h1 + b << 0;\n    this.h2 = this.h2 + c << 0;\n    this.h3 = this.h3 + d << 0;\n    this.h4 = this.h4 + e << 0;\n    this.h5 = this.h5 + f << 0;\n    this.h6 = this.h6 + g << 0;\n    this.h7 = this.h7 + h << 0;\n  };\n\n  Sha256.prototype.hex = function () {\n    this.finalize();\n\n    var h0 = this.h0, h1 = this.h1, h2 = this.h2, h3 = this.h3, h4 = this.h4, h5 = this.h5,\n      h6 = this.h6, h7 = this.h7;\n\n    var hex = HEX_CHARS[(h0 >>> 28) & 0x0F] + HEX_CHARS[(h0 >>> 24) & 0x0F] +\n      HEX_CHARS[(h0 >>> 20) & 0x0F] + HEX_CHARS[(h0 >>> 16) & 0x0F] +\n      HEX_CHARS[(h0 >>> 12) & 0x0F] + HEX_CHARS[(h0 >>> 8) & 0x0F] +\n      HEX_CHARS[(h0 >>> 4) & 0x0F] + HEX_CHARS[h0 & 0x0F] +\n      HEX_CHARS[(h1 >>> 28) & 0x0F] + HEX_CHARS[(h1 >>> 24) & 0x0F] +\n      HEX_CHARS[(h1 >>> 20) & 0x0F] + HEX_CHARS[(h1 >>> 16) & 0x0F] +\n      HEX_CHARS[(h1 >>> 12) & 0x0F] + HEX_CHARS[(h1 >>> 8) & 0x0F] +\n      HEX_CHARS[(h1 >>> 4) & 0x0F] + HEX_CHARS[h1 & 0x0F] +\n      HEX_CHARS[(h2 >>> 28) & 0x0F] + HEX_CHARS[(h2 >>> 24) & 0x0F] +\n      HEX_CHARS[(h2 >>> 20) & 0x0F] + HEX_CHARS[(h2 >>> 16) & 0x0F] +\n      HEX_CHARS[(h2 >>> 12) & 0x0F] + HEX_CHARS[(h2 >>> 8) & 0x0F] +\n      HEX_CHARS[(h2 >>> 4) & 0x0F] + HEX_CHARS[h2 & 0x0F] +\n      HEX_CHARS[(h3 >>> 28) & 0x0F] + HEX_CHARS[(h3 >>> 24) & 0x0F] +\n      HEX_CHARS[(h3 >>> 20) & 0x0F] + HEX_CHARS[(h3 >>> 16) & 0x0F] +\n      HEX_CHARS[(h3 >>> 12) & 0x0F] + HEX_CHARS[(h3 >>> 8) & 0x0F] +\n      HEX_CHARS[(h3 >>> 4) & 0x0F] + HEX_CHARS[h3 & 0x0F] +\n      HEX_CHARS[(h4 >>> 28) & 0x0F] + HEX_CHARS[(h4 >>> 24) & 0x0F] +\n      HEX_CHARS[(h4 >>> 20) & 0x0F] + HEX_CHARS[(h4 >>> 16) & 0x0F] +\n      HEX_CHARS[(h4 >>> 12) & 0x0F] + HEX_CHARS[(h4 >>> 8) & 0x0F] +\n      HEX_CHARS[(h4 >>> 4) & 0x0F] + HEX_CHARS[h4 & 0x0F] +\n      HEX_CHARS[(h5 >>> 28) & 0x0F] + HEX_CHARS[(h5 >>> 24) & 0x0F] +\n      HEX_CHARS[(h5 >>> 20) & 0x0F] + HEX_CHARS[(h5 >>> 16) & 0x0F] +\n      HEX_CHARS[(h5 >>> 12) & 0x0F] + HEX_CHARS[(h5 >>> 8) & 0x0F] +\n      HEX_CHARS[(h5 >>> 4) & 0x0F] + HEX_CHARS[h5 & 0x0F] +\n      HEX_CHARS[(h6 >>> 28) & 0x0F] + HEX_CHARS[(h6 >>> 24) & 0x0F] +\n      HEX_CHARS[(h6 >>> 20) & 0x0F] + HEX_CHARS[(h6 >>> 16) & 0x0F] +\n      HEX_CHARS[(h6 >>> 12) & 0x0F] + HEX_CHARS[(h6 >>> 8) & 0x0F] +\n      HEX_CHARS[(h6 >>> 4) & 0x0F] + HEX_CHARS[h6 & 0x0F];\n    if (!this.is224) {\n      hex += HEX_CHARS[(h7 >>> 28) & 0x0F] + HEX_CHARS[(h7 >>> 24) & 0x0F] +\n        HEX_CHARS[(h7 >>> 20) & 0x0F] + HEX_CHARS[(h7 >>> 16) & 0x0F] +\n        HEX_CHARS[(h7 >>> 12) & 0x0F] + HEX_CHARS[(h7 >>> 8) & 0x0F] +\n        HEX_CHARS[(h7 >>> 4) & 0x0F] + HEX_CHARS[h7 & 0x0F];\n    }\n    return hex;\n  };\n\n  Sha256.prototype.toString = Sha256.prototype.hex;\n\n  Sha256.prototype.digest = function () {\n    this.finalize();\n\n    var h0 = this.h0, h1 = this.h1, h2 = this.h2, h3 = this.h3, h4 = this.h4, h5 = this.h5,\n      h6 = this.h6, h7 = this.h7;\n\n    var arr = [\n      (h0 >>> 24) & 0xFF, (h0 >>> 16) & 0xFF, (h0 >>> 8) & 0xFF, h0 & 0xFF,\n      (h1 >>> 24) & 0xFF, (h1 >>> 16) & 0xFF, (h1 >>> 8) & 0xFF, h1 & 0xFF,\n      (h2 >>> 24) & 0xFF, (h2 >>> 16) & 0xFF, (h2 >>> 8) & 0xFF, h2 & 0xFF,\n      (h3 >>> 24) & 0xFF, (h3 >>> 16) & 0xFF, (h3 >>> 8) & 0xFF, h3 & 0xFF,\n      (h4 >>> 24) & 0xFF, (h4 >>> 16) & 0xFF, (h4 >>> 8) & 0xFF, h4 & 0xFF,\n      (h5 >>> 24) & 0xFF, (h5 >>> 16) & 0xFF, (h5 >>> 8) & 0xFF, h5 & 0xFF,\n      (h6 >>> 24) & 0xFF, (h6 >>> 16) & 0xFF, (h6 >>> 8) & 0xFF, h6 & 0xFF\n    ];\n    if (!this.is224) {\n      arr.push((h7 >>> 24) & 0xFF, (h7 >>> 16) & 0xFF, (h7 >>> 8) & 0xFF, h7 & 0xFF);\n    }\n    return arr;\n  };\n\n  Sha256.prototype.array = Sha256.prototype.digest;\n\n  Sha256.prototype.arrayBuffer = function () {\n    this.finalize();\n\n    var buffer = new ArrayBuffer(this.is224 ? 28 : 32);\n    var dataView = new DataView(buffer);\n    dataView.setUint32(0, this.h0);\n    dataView.setUint32(4, this.h1);\n    dataView.setUint32(8, this.h2);\n    dataView.setUint32(12, this.h3);\n    dataView.setUint32(16, this.h4);\n    dataView.setUint32(20, this.h5);\n    dataView.setUint32(24, this.h6);\n    if (!this.is224) {\n      dataView.setUint32(28, this.h7);\n    }\n    return buffer;\n  };\n\n  function HmacSha256(key, is224, sharedMemory) {\n    var i, type = typeof key;\n    if (type === 'string') {\n      var bytes = [], length = key.length, index = 0, code;\n      for (i = 0; i < length; ++i) {\n        code = key.charCodeAt(i);\n        if (code < 0x80) {\n          bytes[index++] = code;\n        } else if (code < 0x800) {\n          bytes[index++] = (0xc0 | (code >>> 6));\n          bytes[index++] = (0x80 | (code & 0x3f));\n        } else if (code < 0xd800 || code >= 0xe000) {\n          bytes[index++] = (0xe0 | (code >>> 12));\n          bytes[index++] = (0x80 | ((code >>> 6) & 0x3f));\n          bytes[index++] = (0x80 | (code & 0x3f));\n        } else {\n          code = 0x10000 + (((code & 0x3ff) << 10) | (key.charCodeAt(++i) & 0x3ff));\n          bytes[index++] = (0xf0 | (code >>> 18));\n          bytes[index++] = (0x80 | ((code >>> 12) & 0x3f));\n          bytes[index++] = (0x80 | ((code >>> 6) & 0x3f));\n          bytes[index++] = (0x80 | (code & 0x3f));\n        }\n      }\n      key = bytes;\n    } else {\n      if (type === 'object') {\n        if (key === null) {\n          throw new Error(ERROR);\n        } else if (ARRAY_BUFFER && key.constructor === ArrayBuffer) {\n          key = new Uint8Array(key);\n        } else if (!Array.isArray(key)) {\n          if (!ARRAY_BUFFER || !ArrayBuffer.isView(key)) {\n            throw new Error(ERROR);\n          }\n        }\n      } else {\n        throw new Error(ERROR);\n      }\n    }\n\n    if (key.length > 64) {\n      key = (new Sha256(is224, true)).update(key).array();\n    }\n\n    var oKeyPad = [], iKeyPad = [];\n    for (i = 0; i < 64; ++i) {\n      var b = key[i] || 0;\n      oKeyPad[i] = 0x5c ^ b;\n      iKeyPad[i] = 0x36 ^ b;\n    }\n\n    Sha256.call(this, is224, sharedMemory);\n\n    this.update(iKeyPad);\n    this.oKeyPad = oKeyPad;\n    this.inner = true;\n    this.sharedMemory = sharedMemory;\n  }\n  HmacSha256.prototype = new Sha256();\n\n  HmacSha256.prototype.finalize = function () {\n    Sha256.prototype.finalize.call(this);\n    if (this.inner) {\n      this.inner = false;\n      var innerHash = this.array();\n      Sha256.call(this, this.is224, this.sharedMemory);\n      this.update(this.oKeyPad);\n      this.update(innerHash);\n      Sha256.prototype.finalize.call(this);\n    }\n  };\n\n  var exports = createMethod();\n  exports.sha256 = exports;\n  exports.sha224 = createMethod(true);\n  exports.sha256.hmac = createHmacMethod();\n  exports.sha224.hmac = createHmacMethod(true);\n\n  if (COMMON_JS) {\n    module.exports = exports;\n  } else {\n    root.sha256 = exports.sha256;\n    root.sha224 = exports.sha224;\n    if (AMD) {\n      define(function () {\n        return exports;\n      });\n    }\n  }\n})();\n"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA,WAAO,UAAU,OAAO,OAAO,IAAI,MAAM,CAAC,GAAG;AAAA,MAC3C,IAAI,GAAG,KAAK;AACV,YACE,QAAQ,gBACR,QAAQ,eACR,QAAQ,iBACR,QAAQ,UACR;AACA,kBAAQ,KAAK,0FAA0F,GAAG,qIAAqI;AAAA,QACjP;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AAAA;AAAA;;;ACXF;AAAA;AASA,KAAC,WAAY;AACX;AAEA,UAAI,QAAQ;AACZ,UAAI,SAAS,OAAO,WAAW;AAC/B,UAAI,OAAO,SAAS,SAAS,CAAC;AAC9B,UAAI,KAAK,qBAAqB;AAC5B,iBAAS;AAAA,MACX;AACA,UAAI,aAAa,CAAC,UAAU,OAAO,SAAS;AAC5C,UAAI,UAAU,CAAC,KAAK,wBAAwB,OAAO,YAAY,YAAY,QAAQ,YAAY,QAAQ,SAAS;AAChH,UAAI,SAAS;AACX,eAAO;AAAA,MACT,WAAW,YAAY;AACrB,eAAO;AAAA,MACT;AACA,UAAI,YAAY,CAAC,KAAK,0BAA0B,OAAO,WAAW,YAAY,OAAO;AACrF,UAAI,MAAM,OAAO,WAAW,cAAc,OAAO;AACjD,UAAI,eAAe,CAAC,KAAK,6BAA6B,OAAO,gBAAgB;AAC7E,UAAI,YAAY,mBAAmB,MAAM,EAAE;AAC3C,UAAI,QAAQ,CAAC,aAAa,SAAS,OAAO,GAAG;AAC7C,UAAI,QAAQ,CAAC,IAAI,IAAI,GAAG,CAAC;AACzB,UAAI,IAAI;AAAA,QACN;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QACpF;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QACpF;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QACpF;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QACpF;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QACpF;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QACpF;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QACpF;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,QAAY;AAAA,MACtF;AACA,UAAI,eAAe,CAAC,OAAO,SAAS,UAAU,aAAa;AAE3D,UAAI,SAAS,CAAC;AAEd,UAAI,KAAK,wBAAwB,CAAC,MAAM,SAAS;AAC/C,cAAM,UAAU,SAAU,KAAK;AAC7B,iBAAO,OAAO,UAAU,SAAS,KAAK,GAAG,MAAM;AAAA,QACjD;AAAA,MACF;AAEA,UAAI,iBAAiB,KAAK,qCAAqC,CAAC,YAAY,SAAS;AACnF,oBAAY,SAAS,SAAU,KAAK;AAClC,iBAAO,OAAO,QAAQ,YAAY,IAAI,UAAU,IAAI,OAAO,gBAAgB;AAAA,QAC7E;AAAA,MACF;AAEA,UAAI,qBAAqB,SAAU,YAAY,OAAO;AACpD,eAAO,SAAU,SAAS;AACxB,iBAAO,IAAI,OAAO,OAAO,IAAI,EAAE,OAAO,OAAO,EAAE,UAAU,EAAE;AAAA,QAC7D;AAAA,MACF;AAEA,UAAI,eAAe,SAAU,OAAO;AAClC,YAAI,SAAS,mBAAmB,OAAO,KAAK;AAC5C,YAAI,SAAS;AACX,mBAAS,SAAS,QAAQ,KAAK;AAAA,QACjC;AACA,eAAO,SAAS,WAAY;AAC1B,iBAAO,IAAI,OAAO,KAAK;AAAA,QACzB;AACA,eAAO,SAAS,SAAU,SAAS;AACjC,iBAAO,OAAO,OAAO,EAAE,OAAO,OAAO;AAAA,QACvC;AACA,iBAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,EAAE,GAAG;AAC5C,cAAI,OAAO,aAAa,CAAC;AACzB,iBAAO,IAAI,IAAI,mBAAmB,MAAM,KAAK;AAAA,QAC/C;AACA,eAAO;AAAA,MACT;AAEA,UAAI,WAAW,SAAU,QAAQ,OAAO;AACtC,YAAI,SAAS;AACb,YAAI,SAAS,iBAAkB;AAC/B,YAAI,YAAY,QAAQ,WAAW;AACnC,YAAI;AACJ,YAAI,OAAO,QAAQ,CAAC,KAAK,0BAA0B;AACjD,uBAAa,OAAO;AAAA,QACtB,OAAO;AACL,uBAAa,SAAU,SAAS;AAC9B,mBAAO,IAAI,OAAO,OAAO;AAAA,UAC3B;AAAA,QACF;AACA,YAAI,aAAa,SAAU,SAAS;AAClC,cAAI,OAAO,YAAY,UAAU;AAC/B,mBAAO,OAAO,WAAW,SAAS,EAAE,OAAO,SAAS,MAAM,EAAE,OAAO,KAAK;AAAA,UAC1E,OAAO;AACL,gBAAI,YAAY,QAAQ,YAAY,QAAW;AAC7C,oBAAM,IAAI,MAAM,KAAK;AAAA,YACvB,WAAW,QAAQ,gBAAgB,aAAa;AAC9C,wBAAU,IAAI,WAAW,OAAO;AAAA,YAClC;AAAA,UACF;AACA,cAAI,MAAM,QAAQ,OAAO,KAAK,YAAY,OAAO,OAAO,KACtD,QAAQ,gBAAgB,QAAQ;AAChC,mBAAO,OAAO,WAAW,SAAS,EAAE,OAAO,WAAW,OAAO,CAAC,EAAE,OAAO,KAAK;AAAA,UAC9E,OAAO;AACL,mBAAO,OAAO,OAAO;AAAA,UACvB;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAEA,UAAI,yBAAyB,SAAU,YAAY,OAAO;AACxD,eAAO,SAAU,KAAK,SAAS;AAC7B,iBAAO,IAAI,WAAW,KAAK,OAAO,IAAI,EAAE,OAAO,OAAO,EAAE,UAAU,EAAE;AAAA,QACtE;AAAA,MACF;AAEA,UAAI,mBAAmB,SAAU,OAAO;AACtC,YAAI,SAAS,uBAAuB,OAAO,KAAK;AAChD,eAAO,SAAS,SAAU,KAAK;AAC7B,iBAAO,IAAI,WAAW,KAAK,KAAK;AAAA,QAClC;AACA,eAAO,SAAS,SAAU,KAAK,SAAS;AACtC,iBAAO,OAAO,OAAO,GAAG,EAAE,OAAO,OAAO;AAAA,QAC1C;AACA,iBAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,EAAE,GAAG;AAC5C,cAAI,OAAO,aAAa,CAAC;AACzB,iBAAO,IAAI,IAAI,uBAAuB,MAAM,KAAK;AAAA,QACnD;AACA,eAAO;AAAA,MACT;AAEA,eAAS,OAAO,OAAO,cAAc;AACnC,YAAI,cAAc;AAChB,iBAAO,CAAC,IAAI,OAAO,EAAE,IAAI,OAAO,CAAC,IAAI,OAAO,CAAC,IAAI,OAAO,CAAC,IACvD,OAAO,CAAC,IAAI,OAAO,CAAC,IAAI,OAAO,CAAC,IAAI,OAAO,CAAC,IAC5C,OAAO,CAAC,IAAI,OAAO,CAAC,IAAI,OAAO,EAAE,IAAI,OAAO,EAAE,IAC9C,OAAO,EAAE,IAAI,OAAO,EAAE,IAAI,OAAO,EAAE,IAAI,OAAO,EAAE,IAAI;AACtD,eAAK,SAAS;AAAA,QAChB,OAAO;AACL,eAAK,SAAS,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,QAClE;AAEA,YAAI,OAAO;AACT,eAAK,KAAK;AACV,eAAK,KAAK;AACV,eAAK,KAAK;AACV,eAAK,KAAK;AACV,eAAK,KAAK;AACV,eAAK,KAAK;AACV,eAAK,KAAK;AACV,eAAK,KAAK;AAAA,QACZ,OAAO;AACL,eAAK,KAAK;AACV,eAAK,KAAK;AACV,eAAK,KAAK;AACV,eAAK,KAAK;AACV,eAAK,KAAK;AACV,eAAK,KAAK;AACV,eAAK,KAAK;AACV,eAAK,KAAK;AAAA,QACZ;AAEA,aAAK,QAAQ,KAAK,QAAQ,KAAK,QAAQ,KAAK,SAAS;AACrD,aAAK,YAAY,KAAK,SAAS;AAC/B,aAAK,QAAQ;AACb,aAAK,QAAQ;AAAA,MACf;AAEA,aAAO,UAAU,SAAS,SAAU,SAAS;AAC3C,YAAI,KAAK,WAAW;AAClB;AAAA,QACF;AACA,YAAI,WAAW,OAAO,OAAO;AAC7B,YAAI,SAAS,UAAU;AACrB,cAAI,SAAS,UAAU;AACrB,gBAAI,YAAY,MAAM;AACpB,oBAAM,IAAI,MAAM,KAAK;AAAA,YACvB,WAAW,gBAAgB,QAAQ,gBAAgB,aAAa;AAC9D,wBAAU,IAAI,WAAW,OAAO;AAAA,YAClC,WAAW,CAAC,MAAM,QAAQ,OAAO,GAAG;AAClC,kBAAI,CAAC,gBAAgB,CAAC,YAAY,OAAO,OAAO,GAAG;AACjD,sBAAM,IAAI,MAAM,KAAK;AAAA,cACvB;AAAA,YACF;AAAA,UACF,OAAO;AACL,kBAAM,IAAI,MAAM,KAAK;AAAA,UACvB;AACA,sBAAY;AAAA,QACd;AACA,YAAI,MAAM,QAAQ,GAAG,GAAG,SAAS,QAAQ,QAAQA,UAAS,KAAK;AAC/D,eAAO,QAAQ,QAAQ;AACrB,cAAI,KAAK,QAAQ;AACf,iBAAK,SAAS;AACd,YAAAA,QAAO,CAAC,IAAI,KAAK;AACjB,iBAAK,QAAQA,QAAO,EAAE,IAAIA,QAAO,CAAC,IAAIA,QAAO,CAAC,IAAIA,QAAO,CAAC,IACxDA,QAAO,CAAC,IAAIA,QAAO,CAAC,IAAIA,QAAO,CAAC,IAAIA,QAAO,CAAC,IAC5CA,QAAO,CAAC,IAAIA,QAAO,CAAC,IAAIA,QAAO,EAAE,IAAIA,QAAO,EAAE,IAC9CA,QAAO,EAAE,IAAIA,QAAO,EAAE,IAAIA,QAAO,EAAE,IAAIA,QAAO,EAAE,IAAI;AAAA,UACxD;AAEA,cAAI,WAAW;AACb,iBAAK,IAAI,KAAK,OAAO,QAAQ,UAAU,IAAI,IAAI,EAAE,OAAO;AACtD,cAAAA,QAAO,MAAM,CAAC,KAAK,QAAQ,KAAK,KAAK,MAAM,MAAM,CAAC;AAAA,YACpD;AAAA,UACF,OAAO;AACL,iBAAK,IAAI,KAAK,OAAO,QAAQ,UAAU,IAAI,IAAI,EAAE,OAAO;AACtD,qBAAO,QAAQ,WAAW,KAAK;AAC/B,kBAAI,OAAO,KAAM;AACf,gBAAAA,QAAO,MAAM,CAAC,KAAK,QAAQ,MAAM,MAAM,CAAC;AAAA,cAC1C,WAAW,OAAO,MAAO;AACvB,gBAAAA,QAAO,MAAM,CAAC,MAAM,MAAQ,SAAS,MAAO,MAAM,MAAM,CAAC;AACzD,gBAAAA,QAAO,MAAM,CAAC,MAAM,MAAQ,OAAO,OAAU,MAAM,MAAM,CAAC;AAAA,cAC5D,WAAW,OAAO,SAAU,QAAQ,OAAQ;AAC1C,gBAAAA,QAAO,MAAM,CAAC,MAAM,MAAQ,SAAS,OAAQ,MAAM,MAAM,CAAC;AAC1D,gBAAAA,QAAO,MAAM,CAAC,MAAM,MAAS,SAAS,IAAK,OAAU,MAAM,MAAM,CAAC;AAClE,gBAAAA,QAAO,MAAM,CAAC,MAAM,MAAQ,OAAO,OAAU,MAAM,MAAM,CAAC;AAAA,cAC5D,OAAO;AACL,uBAAO,UAAa,OAAO,SAAU,KAAO,QAAQ,WAAW,EAAE,KAAK,IAAI;AAC1E,gBAAAA,QAAO,MAAM,CAAC,MAAM,MAAQ,SAAS,OAAQ,MAAM,MAAM,CAAC;AAC1D,gBAAAA,QAAO,MAAM,CAAC,MAAM,MAAS,SAAS,KAAM,OAAU,MAAM,MAAM,CAAC;AACnE,gBAAAA,QAAO,MAAM,CAAC,MAAM,MAAS,SAAS,IAAK,OAAU,MAAM,MAAM,CAAC;AAClE,gBAAAA,QAAO,MAAM,CAAC,MAAM,MAAQ,OAAO,OAAU,MAAM,MAAM,CAAC;AAAA,cAC5D;AAAA,YACF;AAAA,UACF;AAEA,eAAK,gBAAgB;AACrB,eAAK,SAAS,IAAI,KAAK;AACvB,cAAI,KAAK,IAAI;AACX,iBAAK,QAAQA,QAAO,EAAE;AACtB,iBAAK,QAAQ,IAAI;AACjB,iBAAK,KAAK;AACV,iBAAK,SAAS;AAAA,UAChB,OAAO;AACL,iBAAK,QAAQ;AAAA,UACf;AAAA,QACF;AACA,YAAI,KAAK,QAAQ,YAAY;AAC3B,eAAK,UAAU,KAAK,QAAQ,cAAc;AAC1C,eAAK,QAAQ,KAAK,QAAQ;AAAA,QAC5B;AACA,eAAO;AAAA,MACT;AAEA,aAAO,UAAU,WAAW,WAAY;AACtC,YAAI,KAAK,WAAW;AAClB;AAAA,QACF;AACA,aAAK,YAAY;AACjB,YAAIA,UAAS,KAAK,QAAQ,IAAI,KAAK;AACnC,QAAAA,QAAO,EAAE,IAAI,KAAK;AAClB,QAAAA,QAAO,MAAM,CAAC,KAAK,MAAM,IAAI,CAAC;AAC9B,aAAK,QAAQA,QAAO,EAAE;AACtB,YAAI,KAAK,IAAI;AACX,cAAI,CAAC,KAAK,QAAQ;AAChB,iBAAK,KAAK;AAAA,UACZ;AACA,UAAAA,QAAO,CAAC,IAAI,KAAK;AACjB,UAAAA,QAAO,EAAE,IAAIA,QAAO,CAAC,IAAIA,QAAO,CAAC,IAAIA,QAAO,CAAC,IAC3CA,QAAO,CAAC,IAAIA,QAAO,CAAC,IAAIA,QAAO,CAAC,IAAIA,QAAO,CAAC,IAC5CA,QAAO,CAAC,IAAIA,QAAO,CAAC,IAAIA,QAAO,EAAE,IAAIA,QAAO,EAAE,IAC9CA,QAAO,EAAE,IAAIA,QAAO,EAAE,IAAIA,QAAO,EAAE,IAAIA,QAAO,EAAE,IAAI;AAAA,QACxD;AACA,QAAAA,QAAO,EAAE,IAAI,KAAK,UAAU,IAAI,KAAK,UAAU;AAC/C,QAAAA,QAAO,EAAE,IAAI,KAAK,SAAS;AAC3B,aAAK,KAAK;AAAA,MACZ;AAEA,aAAO,UAAU,OAAO,WAAY;AAClC,YAAI,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,IACzF,IAAI,KAAK,IAAIA,UAAS,KAAK,QAAQ,GAAG,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAE7E,aAAK,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG;AAExB,eAAKA,QAAO,IAAI,EAAE;AAClB,gBAAO,OAAO,IAAM,MAAM,OAAS,OAAO,KAAO,MAAM,MAAQ,OAAO;AACtE,eAAKA,QAAO,IAAI,CAAC;AACjB,gBAAO,OAAO,KAAO,MAAM,OAAS,OAAO,KAAO,MAAM,MAAQ,OAAO;AACvE,UAAAA,QAAO,CAAC,IAAIA,QAAO,IAAI,EAAE,IAAI,KAAKA,QAAO,IAAI,CAAC,IAAI,MAAM;AAAA,QAC1D;AAEA,aAAK,IAAI;AACT,aAAK,IAAI,GAAG,IAAI,IAAI,KAAK,GAAG;AAC1B,cAAI,KAAK,OAAO;AACd,gBAAI,KAAK,OAAO;AACd,mBAAK;AACL,mBAAKA,QAAO,CAAC,IAAI;AACjB,kBAAI,KAAK,aAAa;AACtB,kBAAI,KAAK,YAAY;AAAA,YACvB,OAAO;AACL,mBAAK;AACL,mBAAKA,QAAO,CAAC,IAAI;AACjB,kBAAI,KAAK,cAAc;AACvB,kBAAI,KAAK,aAAa;AAAA,YACxB;AACA,iBAAK,QAAQ;AAAA,UACf,OAAO;AACL,kBAAO,MAAM,IAAM,KAAK,OAAS,MAAM,KAAO,KAAK,OAAS,MAAM,KAAO,KAAK;AAC9E,kBAAO,MAAM,IAAM,KAAK,OAAS,MAAM,KAAO,KAAK,OAAS,MAAM,KAAO,KAAK;AAC9E,iBAAK,IAAI;AACT,kBAAM,KAAM,IAAI,IAAK;AACrB,iBAAM,IAAI,IAAM,CAAC,IAAI;AACrB,iBAAK,IAAI,KAAK,KAAK,EAAE,CAAC,IAAIA,QAAO,CAAC;AAClC,iBAAK,KAAK;AACV,gBAAI,IAAI,MAAM;AACd,gBAAI,KAAK,MAAM;AAAA,UACjB;AACA,gBAAO,MAAM,IAAM,KAAK,OAAS,MAAM,KAAO,KAAK,OAAS,MAAM,KAAO,KAAK;AAC9E,gBAAO,MAAM,IAAM,KAAK,OAAS,MAAM,KAAO,KAAK,OAAS,MAAM,KAAO,KAAK;AAC9E,eAAK,IAAI;AACT,gBAAM,KAAM,IAAI,IAAK;AACrB,eAAM,IAAI,IAAM,CAAC,IAAI;AACrB,eAAK,IAAI,KAAK,KAAK,EAAE,IAAI,CAAC,IAAIA,QAAO,IAAI,CAAC;AAC1C,eAAK,KAAK;AACV,cAAI,IAAI,MAAM;AACd,cAAI,KAAK,MAAM;AACf,gBAAO,MAAM,IAAM,KAAK,OAAS,MAAM,KAAO,KAAK,OAAS,MAAM,KAAO,KAAK;AAC9E,gBAAO,MAAM,IAAM,KAAK,OAAS,MAAM,KAAO,KAAK,OAAS,MAAM,KAAO,KAAK;AAC9E,eAAK,IAAI;AACT,gBAAM,KAAM,IAAI,IAAK;AACrB,eAAM,IAAI,IAAM,CAAC,IAAI;AACrB,eAAK,IAAI,KAAK,KAAK,EAAE,IAAI,CAAC,IAAIA,QAAO,IAAI,CAAC;AAC1C,eAAK,KAAK;AACV,cAAI,IAAI,MAAM;AACd,cAAI,KAAK,MAAM;AACf,gBAAO,MAAM,IAAM,KAAK,OAAS,MAAM,KAAO,KAAK,OAAS,MAAM,KAAO,KAAK;AAC9E,gBAAO,MAAM,IAAM,KAAK,OAAS,MAAM,KAAO,KAAK,OAAS,MAAM,KAAO,KAAK;AAC9E,eAAK,IAAI;AACT,gBAAM,KAAM,IAAI,IAAK;AACrB,eAAM,IAAI,IAAM,CAAC,IAAI;AACrB,eAAK,IAAI,KAAK,KAAK,EAAE,IAAI,CAAC,IAAIA,QAAO,IAAI,CAAC;AAC1C,eAAK,KAAK;AACV,cAAI,IAAI,MAAM;AACd,cAAI,KAAK,MAAM;AACf,eAAK,sBAAsB;AAAA,QAC7B;AAEA,aAAK,KAAK,KAAK,KAAK,KAAK;AACzB,aAAK,KAAK,KAAK,KAAK,KAAK;AACzB,aAAK,KAAK,KAAK,KAAK,KAAK;AACzB,aAAK,KAAK,KAAK,KAAK,KAAK;AACzB,aAAK,KAAK,KAAK,KAAK,KAAK;AACzB,aAAK,KAAK,KAAK,KAAK,KAAK;AACzB,aAAK,KAAK,KAAK,KAAK,KAAK;AACzB,aAAK,KAAK,KAAK,KAAK,KAAK;AAAA,MAC3B;AAEA,aAAO,UAAU,MAAM,WAAY;AACjC,aAAK,SAAS;AAEd,YAAI,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,IAClF,KAAK,KAAK,IAAI,KAAK,KAAK;AAE1B,YAAI,MAAM,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,KAAM,EAAI,IACpE,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,KAAM,EAAI,IAC5D,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,IAAK,EAAI,IAC3D,UAAW,OAAO,IAAK,EAAI,IAAI,UAAU,KAAK,EAAI,IAClD,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,KAAM,EAAI,IAC5D,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,KAAM,EAAI,IAC5D,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,IAAK,EAAI,IAC3D,UAAW,OAAO,IAAK,EAAI,IAAI,UAAU,KAAK,EAAI,IAClD,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,KAAM,EAAI,IAC5D,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,KAAM,EAAI,IAC5D,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,IAAK,EAAI,IAC3D,UAAW,OAAO,IAAK,EAAI,IAAI,UAAU,KAAK,EAAI,IAClD,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,KAAM,EAAI,IAC5D,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,KAAM,EAAI,IAC5D,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,IAAK,EAAI,IAC3D,UAAW,OAAO,IAAK,EAAI,IAAI,UAAU,KAAK,EAAI,IAClD,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,KAAM,EAAI,IAC5D,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,KAAM,EAAI,IAC5D,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,IAAK,EAAI,IAC3D,UAAW,OAAO,IAAK,EAAI,IAAI,UAAU,KAAK,EAAI,IAClD,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,KAAM,EAAI,IAC5D,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,KAAM,EAAI,IAC5D,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,IAAK,EAAI,IAC3D,UAAW,OAAO,IAAK,EAAI,IAAI,UAAU,KAAK,EAAI,IAClD,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,KAAM,EAAI,IAC5D,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,KAAM,EAAI,IAC5D,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,IAAK,EAAI,IAC3D,UAAW,OAAO,IAAK,EAAI,IAAI,UAAU,KAAK,EAAI;AACpD,YAAI,CAAC,KAAK,OAAO;AACf,iBAAO,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,KAAM,EAAI,IACjE,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,KAAM,EAAI,IAC5D,UAAW,OAAO,KAAM,EAAI,IAAI,UAAW,OAAO,IAAK,EAAI,IAC3D,UAAW,OAAO,IAAK,EAAI,IAAI,UAAU,KAAK,EAAI;AAAA,QACtD;AACA,eAAO;AAAA,MACT;AAEA,aAAO,UAAU,WAAW,OAAO,UAAU;AAE7C,aAAO,UAAU,SAAS,WAAY;AACpC,aAAK,SAAS;AAEd,YAAI,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,IAClF,KAAK,KAAK,IAAI,KAAK,KAAK;AAE1B,YAAI,MAAM;AAAA,UACP,OAAO,KAAM;AAAA,UAAO,OAAO,KAAM;AAAA,UAAO,OAAO,IAAK;AAAA,UAAM,KAAK;AAAA,UAC/D,OAAO,KAAM;AAAA,UAAO,OAAO,KAAM;AAAA,UAAO,OAAO,IAAK;AAAA,UAAM,KAAK;AAAA,UAC/D,OAAO,KAAM;AAAA,UAAO,OAAO,KAAM;AAAA,UAAO,OAAO,IAAK;AAAA,UAAM,KAAK;AAAA,UAC/D,OAAO,KAAM;AAAA,UAAO,OAAO,KAAM;AAAA,UAAO,OAAO,IAAK;AAAA,UAAM,KAAK;AAAA,UAC/D,OAAO,KAAM;AAAA,UAAO,OAAO,KAAM;AAAA,UAAO,OAAO,IAAK;AAAA,UAAM,KAAK;AAAA,UAC/D,OAAO,KAAM;AAAA,UAAO,OAAO,KAAM;AAAA,UAAO,OAAO,IAAK;AAAA,UAAM,KAAK;AAAA,UAC/D,OAAO,KAAM;AAAA,UAAO,OAAO,KAAM;AAAA,UAAO,OAAO,IAAK;AAAA,UAAM,KAAK;AAAA,QAClE;AACA,YAAI,CAAC,KAAK,OAAO;AACf,cAAI,KAAM,OAAO,KAAM,KAAO,OAAO,KAAM,KAAO,OAAO,IAAK,KAAM,KAAK,GAAI;AAAA,QAC/E;AACA,eAAO;AAAA,MACT;AAEA,aAAO,UAAU,QAAQ,OAAO,UAAU;AAE1C,aAAO,UAAU,cAAc,WAAY;AACzC,aAAK,SAAS;AAEd,YAAI,SAAS,IAAI,YAAY,KAAK,QAAQ,KAAK,EAAE;AACjD,YAAI,WAAW,IAAI,SAAS,MAAM;AAClC,iBAAS,UAAU,GAAG,KAAK,EAAE;AAC7B,iBAAS,UAAU,GAAG,KAAK,EAAE;AAC7B,iBAAS,UAAU,GAAG,KAAK,EAAE;AAC7B,iBAAS,UAAU,IAAI,KAAK,EAAE;AAC9B,iBAAS,UAAU,IAAI,KAAK,EAAE;AAC9B,iBAAS,UAAU,IAAI,KAAK,EAAE;AAC9B,iBAAS,UAAU,IAAI,KAAK,EAAE;AAC9B,YAAI,CAAC,KAAK,OAAO;AACf,mBAAS,UAAU,IAAI,KAAK,EAAE;AAAA,QAChC;AACA,eAAO;AAAA,MACT;AAEA,eAAS,WAAW,KAAK,OAAO,cAAc;AAC5C,YAAI,GAAG,OAAO,OAAO;AACrB,YAAI,SAAS,UAAU;AACrB,cAAI,QAAQ,CAAC,GAAG,SAAS,IAAI,QAAQ,QAAQ,GAAG;AAChD,eAAK,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAC3B,mBAAO,IAAI,WAAW,CAAC;AACvB,gBAAI,OAAO,KAAM;AACf,oBAAM,OAAO,IAAI;AAAA,YACnB,WAAW,OAAO,MAAO;AACvB,oBAAM,OAAO,IAAK,MAAQ,SAAS;AACnC,oBAAM,OAAO,IAAK,MAAQ,OAAO;AAAA,YACnC,WAAW,OAAO,SAAU,QAAQ,OAAQ;AAC1C,oBAAM,OAAO,IAAK,MAAQ,SAAS;AACnC,oBAAM,OAAO,IAAK,MAAS,SAAS,IAAK;AACzC,oBAAM,OAAO,IAAK,MAAQ,OAAO;AAAA,YACnC,OAAO;AACL,qBAAO,UAAa,OAAO,SAAU,KAAO,IAAI,WAAW,EAAE,CAAC,IAAI;AAClE,oBAAM,OAAO,IAAK,MAAQ,SAAS;AACnC,oBAAM,OAAO,IAAK,MAAS,SAAS,KAAM;AAC1C,oBAAM,OAAO,IAAK,MAAS,SAAS,IAAK;AACzC,oBAAM,OAAO,IAAK,MAAQ,OAAO;AAAA,YACnC;AAAA,UACF;AACA,gBAAM;AAAA,QACR,OAAO;AACL,cAAI,SAAS,UAAU;AACrB,gBAAI,QAAQ,MAAM;AAChB,oBAAM,IAAI,MAAM,KAAK;AAAA,YACvB,WAAW,gBAAgB,IAAI,gBAAgB,aAAa;AAC1D,oBAAM,IAAI,WAAW,GAAG;AAAA,YAC1B,WAAW,CAAC,MAAM,QAAQ,GAAG,GAAG;AAC9B,kBAAI,CAAC,gBAAgB,CAAC,YAAY,OAAO,GAAG,GAAG;AAC7C,sBAAM,IAAI,MAAM,KAAK;AAAA,cACvB;AAAA,YACF;AAAA,UACF,OAAO;AACL,kBAAM,IAAI,MAAM,KAAK;AAAA,UACvB;AAAA,QACF;AAEA,YAAI,IAAI,SAAS,IAAI;AACnB,gBAAO,IAAI,OAAO,OAAO,IAAI,EAAG,OAAO,GAAG,EAAE,MAAM;AAAA,QACpD;AAEA,YAAI,UAAU,CAAC,GAAG,UAAU,CAAC;AAC7B,aAAK,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AACvB,cAAI,IAAI,IAAI,CAAC,KAAK;AAClB,kBAAQ,CAAC,IAAI,KAAO;AACpB,kBAAQ,CAAC,IAAI,KAAO;AAAA,QACtB;AAEA,eAAO,KAAK,MAAM,OAAO,YAAY;AAErC,aAAK,OAAO,OAAO;AACnB,aAAK,UAAU;AACf,aAAK,QAAQ;AACb,aAAK,eAAe;AAAA,MACtB;AACA,iBAAW,YAAY,IAAI,OAAO;AAElC,iBAAW,UAAU,WAAW,WAAY;AAC1C,eAAO,UAAU,SAAS,KAAK,IAAI;AACnC,YAAI,KAAK,OAAO;AACd,eAAK,QAAQ;AACb,cAAI,YAAY,KAAK,MAAM;AAC3B,iBAAO,KAAK,MAAM,KAAK,OAAO,KAAK,YAAY;AAC/C,eAAK,OAAO,KAAK,OAAO;AACxB,eAAK,OAAO,SAAS;AACrB,iBAAO,UAAU,SAAS,KAAK,IAAI;AAAA,QACrC;AAAA,MACF;AAEA,UAAIC,WAAU,aAAa;AAC3B,MAAAA,SAAQ,SAASA;AACjB,MAAAA,SAAQ,SAAS,aAAa,IAAI;AAClC,MAAAA,SAAQ,OAAO,OAAO,iBAAiB;AACvC,MAAAA,SAAQ,OAAO,OAAO,iBAAiB,IAAI;AAE3C,UAAI,WAAW;AACb,eAAO,UAAUA;AAAA,MACnB,OAAO;AACL,aAAK,SAASA,SAAQ;AACtB,aAAK,SAASA,SAAQ;AACtB,YAAI,KAAK;AACP,iBAAO,WAAY;AACjB,mBAAOA;AAAA,UACT,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF,GAAG;AAAA;AAAA;", "names": ["blocks", "exports"]}