{"version": 3, "file": "errors.js", "sourceRoot": "", "sources": ["../../src/errors.ts"], "names": [], "mappings": ";;;AAAA,4BAA4B;AAC5B,MAAsB,UAAW,SAAQ,KAAK;IAC1C,YAAY,OAAgB;QACxB,KAAK,CAAC,OAAO,CAAC,CAAC;IACnB,CAAC;CACJ;AAJD,gCAIC;AAED,gEAAgE;AAChE,MAAa,yBAA0B,SAAQ,UAAU;IAAzD;;QACI,SAAI,GAAG,2BAA2B,CAAC;IACvC,CAAC;CAAA;AAFD,8DAEC;AAED,+DAA+D;AAC/D,MAAa,wBAAyB,SAAQ,UAAU;IAAxD;;QACI,SAAI,GAAG,0BAA0B,CAAC;IACtC,CAAC;CAAA;AAFD,4DAEC;AAED,oEAAoE;AACpE,MAAa,4BAA6B,SAAQ,UAAU;IAA5D;;QACI,SAAI,GAAG,8BAA8B,CAAC;IAC1C,CAAC;CAAA;AAFD,oEAEC;AAED,mFAAmF;AACnF,MAAa,6BAA8B,SAAQ,UAAU;IAA7D;;QACI,SAAI,GAAG,+BAA+B,CAAC;IAC3C,CAAC;CAAA;AAFD,sEAEC;AAED,4FAA4F;AAC5F,MAAa,4BAA6B,SAAQ,UAAU;IAA5D;;QACI,SAAI,GAAG,8BAA8B,CAAC;IAC1C,CAAC;CAAA;AAFD,oEAEC;AAED,4EAA4E;AAC5E,MAAa,qBAAsB,SAAQ,UAAU;IAArD;;QACI,SAAI,GAAG,uBAAuB,CAAC;IACnC,CAAC;CAAA;AAFD,sDAEC;AAED,8EAA8E;AAC9E,MAAa,sBAAuB,SAAQ,UAAU;IAAtD;;QACI,SAAI,GAAG,wBAAwB,CAAC;IACpC,CAAC;CAAA;AAFD,wDAEC;AAED,gFAAgF;AAChF,MAAa,uBAAwB,SAAQ,UAAU;IAAvD;;QACI,SAAI,GAAG,yBAAyB,CAAC;IACrC,CAAC;CAAA;AAFD,0DAEC;AAED,oDAAoD;AACpD,MAAa,mCAAoC,SAAQ,UAAU;IAAnE;;QACI,SAAI,GAAG,qCAAqC,CAAC;IACjD,CAAC;CAAA;AAFD,kFAEC;AAED,kDAAkD;AAClD,MAAa,gCAAiC,SAAQ,UAAU;IAAhE;;QACI,SAAI,GAAG,kCAAkC,CAAC;IAC9C,CAAC;CAAA;AAFD,4EAEC;AAED,iDAAiD;AACjD,MAAa,gCAAiC,SAAQ,UAAU;IAAhE;;QACI,SAAI,GAAG,kCAAkC,CAAC;IAC9C,CAAC;CAAA;AAFD,4EAEC;AAED,iDAAiD;AACjD,MAAa,gCAAiC,SAAQ,UAAU;IAAhE;;QACI,SAAI,GAAG,kCAAkC,CAAC;IAC9C,CAAC;CAAA;AAFD,4EAEC;AAED,qEAAqE;AACrE,MAAa,gCAAiC,SAAQ,UAAU;IAAhE;;QACI,SAAI,GAAG,kCAAkC,CAAC;IAC9C,CAAC;CAAA;AAFD,4EAEC;AAED,mFAAmF;AACnF,MAAa,gCAAiC,SAAQ,UAAU;IAAhE;;QACI,SAAI,GAAG,kCAAkC,CAAC;IAC9C,CAAC;CAAA;AAFD,4EAEC;AAED,0EAA0E;AAC1E,MAAa,4BAA6B,SAAQ,UAAU;IAA5D;;QACI,SAAI,GAAG,8BAA8B,CAAC;IAC1C,CAAC;CAAA;AAFD,oEAEC;AAED,gGAAgG;AAChG,MAAa,oCAAqC,SAAQ,UAAU;IAApE;;QACI,SAAI,GAAG,sCAAsC,CAAC;IAClD,CAAC;CAAA;AAFD,oFAEC;AAED,6DAA6D;AAC7D,MAAa,kCAAmC,SAAQ,UAAU;IAAlE;;QACI,SAAI,GAAG,oCAAoC,CAAC;IAChD,CAAC;CAAA;AAFD,gFAEC;AAED,6DAA6D;AAC7D,MAAa,mCAAoC,SAAQ,UAAU;IAAnE;;QACI,SAAI,GAAG,qCAAqC,CAAC;IACjD,CAAC;CAAA;AAFD,kFAEC"}