{"version": 3, "file": "extensionType.js", "sourceRoot": "", "sources": ["../../../src/extensions/extensionType.ts"], "names": [], "mappings": ";;;AA2EA,gCAuDC;AAED,0CAgCC;AAED,gDAgCC;AAED,4DAkCC;AAiCD,gCAKC;AAED,sCAGC;AAED,4CAYC;AAED,8CAUC;AAED,oDAIC;AAED,0EAcC;AAnUD,oDAAmD;AAEnD,8CAAyD;AACzD,sDAAqD;AACrD,qDAAqD;AACrD,kDAAqD;AACrD,6DAA4E;AAC5E,oDAAkF;AAClF,4DAA0E;AAC1E,sDAA6D;AAC7D,2DAA2D;AAC3D,6DAAyF;AACzF,sDAA6D;AAC7D,yDAAmE;AACnE,mEAAoE;AACpE,6DAA4F;AAC5F,kDAAkF;AAClF,iEAAiE;AACjE,wDAAyE;AACzE,qDAA4F;AAC5F,sDAAyF;AACzF,kDAAwD;AAExD,+HAA+H;AAC/H,IAAY,aA6BX;AA7BD,WAAY,aAAa;IACrB,mEAAa,CAAA;IACb,2EAAiB,CAAA;IACjB,2EAAiB,CAAA;IACjB,6EAAkB,CAAA;IAClB,yFAAwB,CAAA;IACxB,+FAA2B,CAAA;IAC3B,+EAAmB,CAAA;IACnB,qEAAc,CAAA;IACd,iEAAY,CAAA;IACZ,uEAAe,CAAA;IACf,oFAAqB,CAAA;IACrB,0DAAQ,CAAA;IACR,4EAAiB,CAAA;IACjB,sFAAsB,CAAA;IACtB,kEAAY,CAAA;IACZ,gFAAmB,CAAA;IACnB,kDAAkD;IAClD,wDAAwD;IACxD,wEAAoB,CAAA;IACpB,oEAAkB,CAAA;IAClB,kEAAiB,CAAA;IACjB,8DAAe,CAAA;IACf,8EAAuB,CAAA;IACvB,0EAAqB,CAAA;IACrB,+CAA+C;IAC/C,kFAAyB,CAAA;IACzB,sEAAmB,CAAA;IACnB,wEAAoB,CAAA;AACxB,CAAC,EA7BW,aAAa,6BAAb,aAAa,QA6BxB;AAEY,QAAA,SAAS,GAAG,CAAC,CAAC;AACd,QAAA,WAAW,GAAG,CAAC,CAAC;AAE7B,SAAS,qBAAqB,CAAC,GAAW;IACtC,OAAO,GAAG,GAAG,iBAAS,GAAG,mBAAW,CAAC;AACzC,CAAC;AAED,SAAS,yBAAyB,CAAC,CAAgB;IAC/C,QAAQ,CAAC,EAAE,CAAC;QACR,KAAK,aAAa,CAAC,aAAa;YAC5B,OAAO,IAAI,CAAC;QAChB;YACI,OAAO,KAAK,CAAC;IACrB,CAAC;AACL,CAAC;AAED,8EAA8E;AAC9E,8DAA8D;AAC9D,SAAgB,UAAU,CAAC,CAAgB;IACvC,QAAQ,CAAC,EAAE,CAAC;QACR,KAAK,aAAa,CAAC,aAAa;YAC5B,OAAO,CAAC,CAAC;QACb,KAAK,aAAa,CAAC,iBAAiB;YAChC,OAAO,mCAAwB,CAAC;QACpC,KAAK,aAAa,CAAC,iBAAiB;YAChC,OAAO,mCAAwB,CAAC;QACpC,KAAK,aAAa,CAAC,kBAAkB;YACjC,OAAO,iDAAyB,CAAC;QACrC,KAAK,aAAa,CAAC,wBAAwB;YACvC,OAAO,EAAE,CAAC;QACd,KAAK,aAAa,CAAC,2BAA2B;YAC1C,OAAO,GAAG,CAAC;QACf,KAAK,aAAa,CAAC,QAAQ;YACvB,OAAO,yBAAc,CAAC;QAC1B,KAAK,aAAa,CAAC,mBAAmB;YAClC,OAAO,qCAA0B,CAAC;QACtC,KAAK,aAAa,CAAC,cAAc;YAC7B,OAAO,wCAAoB,CAAC;QAChC,KAAK,aAAa,CAAC,YAAY;YAC3B,OAAO,6BAAkB,CAAC;QAC9B,KAAK,aAAa,CAAC,eAAe;YAC9B,OAAO,gCAAqB,CAAC;QACjC,KAAK,aAAa,CAAC,eAAe;YAC9B,OAAO,0CAAqB,CAAC;QACjC,KAAK,aAAa,CAAC,qBAAqB;YACpC,OAAO,kDAAuC,CAAC;QACnD,KAAK,aAAa,CAAC,iBAAiB;YAChC,OAAO,8CAAuB,CAAC;QACnC,KAAK,aAAa,CAAC,sBAAsB;YACrC,OAAO,kDAA6B,CAAC;QACzC,KAAK,aAAa,CAAC,YAAY;YAC3B,OAAO,6BAAkB,CAAC;QAC9B,KAAK,aAAa,CAAC,mBAAmB;YAClC,OAAO,qCAA0B,CAAC;QACtC,KAAK,aAAa,CAAC,YAAY;YAC3B,OAAO,6BAAkB,CAAC;QAC9B,KAAK,aAAa,CAAC,kBAAkB;YACjC,OAAO,oCAAyB,CAAC;QACrC,KAAK,aAAa,CAAC,UAAU;YACzB,OAAO,2BAAgB,CAAC;QAC5B,KAAK,aAAa,CAAC,gBAAgB;YAC/B,OAAO,kCAAuB,CAAC;QACnC,KAAK,aAAa,CAAC,oBAAoB;YACnC,OAAO,uCAA4B,CAAC;QACxC,KAAK,aAAa,CAAC,cAAc;YAC7B,OAAO,+BAAoB,CAAC;QAChC,KAAK,aAAa,CAAC,eAAe;YAC9B,OAAO,gCAAqB,CAAC;QACjC,KAAK,aAAa,CAAC,aAAa;YAC5B,MAAM,KAAK,CAAC,uDAAuD,CAAC,EAAE,CAAC,CAAC;QAC5E;YACI,MAAM,KAAK,CAAC,2BAA2B,CAAC,EAAE,CAAC,CAAC;IACpD,CAAC;AACL,CAAC;AAED,SAAgB,eAAe,CAAC,CAAgB;IAC5C,QAAQ,CAAC,EAAE,CAAC;QACR,KAAK,aAAa,CAAC,iBAAiB,CAAC;QACrC,KAAK,aAAa,CAAC,kBAAkB,CAAC;QACtC,KAAK,aAAa,CAAC,wBAAwB,CAAC;QAC5C,KAAK,aAAa,CAAC,mBAAmB,CAAC;QACvC,KAAK,aAAa,CAAC,eAAe,CAAC;QACnC,KAAK,aAAa,CAAC,qBAAqB,CAAC;QACzC,KAAK,aAAa,CAAC,iBAAiB,CAAC;QACrC,KAAK,aAAa,CAAC,YAAY,CAAC;QAChC,KAAK,aAAa,CAAC,eAAe,CAAC;QACnC,KAAK,aAAa,CAAC,aAAa,CAAC;QACjC,KAAK,aAAa,CAAC,YAAY,CAAC;QAChC,KAAK,aAAa,CAAC,kBAAkB,CAAC;QACtC,KAAK,aAAa,CAAC,UAAU,CAAC;QAC9B,KAAK,aAAa,CAAC,gBAAgB,CAAC;QACpC,KAAK,aAAa,CAAC,oBAAoB,CAAC;QACxC,KAAK,aAAa,CAAC,cAAc;YAC7B,OAAO,IAAI,CAAC;QAChB,KAAK,aAAa,CAAC,aAAa,CAAC;QACjC,KAAK,aAAa,CAAC,iBAAiB,CAAC;QACrC,KAAK,aAAa,CAAC,2BAA2B,CAAC;QAC/C,KAAK,aAAa,CAAC,cAAc,CAAC;QAClC,KAAK,aAAa,CAAC,YAAY,CAAC;QAChC,KAAK,aAAa,CAAC,QAAQ,CAAC;QAC5B,KAAK,aAAa,CAAC,sBAAsB,CAAC;QAC1C,KAAK,aAAa,CAAC,mBAAmB,CAAC;QACvC,KAAK,aAAa,CAAC,eAAe;YAC9B,OAAO,KAAK,CAAC;QACjB;YACI,MAAM,KAAK,CAAC,2BAA2B,CAAC,EAAE,CAAC,CAAC;IACpD,CAAC;AACL,CAAC;AAED,SAAgB,kBAAkB,CAAC,CAAgB;IAC/C,QAAQ,CAAC,EAAE,CAAC;QACR,KAAK,aAAa,CAAC,iBAAiB,CAAC;QACrC,KAAK,aAAa,CAAC,2BAA2B,CAAC;QAC/C,KAAK,aAAa,CAAC,cAAc,CAAC;QAClC,KAAK,aAAa,CAAC,YAAY,CAAC;QAChC,KAAK,aAAa,CAAC,QAAQ,CAAC;QAC5B,KAAK,aAAa,CAAC,sBAAsB,CAAC;QAC1C,KAAK,aAAa,CAAC,mBAAmB,CAAC;QACvC,KAAK,aAAa,CAAC,eAAe;YAC9B,OAAO,IAAI,CAAC;QAChB,KAAK,aAAa,CAAC,aAAa,CAAC;QACjC,KAAK,aAAa,CAAC,iBAAiB,CAAC;QACrC,KAAK,aAAa,CAAC,kBAAkB,CAAC;QACtC,KAAK,aAAa,CAAC,wBAAwB,CAAC;QAC5C,KAAK,aAAa,CAAC,mBAAmB,CAAC;QACvC,KAAK,aAAa,CAAC,eAAe,CAAC;QACnC,KAAK,aAAa,CAAC,qBAAqB,CAAC;QACzC,KAAK,aAAa,CAAC,iBAAiB,CAAC;QACrC,KAAK,aAAa,CAAC,YAAY,CAAC;QAChC,KAAK,aAAa,CAAC,eAAe,CAAC;QACnC,KAAK,aAAa,CAAC,aAAa,CAAC;QACjC,KAAK,aAAa,CAAC,YAAY,CAAC;QAChC,KAAK,aAAa,CAAC,kBAAkB,CAAC;QACtC,KAAK,aAAa,CAAC,UAAU,CAAC;QAC9B,KAAK,aAAa,CAAC,gBAAgB,CAAC;QACpC,KAAK,aAAa,CAAC,oBAAoB,CAAC;QACxC,KAAK,aAAa,CAAC,cAAc;YAC7B,OAAO,KAAK,CAAC;QACjB;YACI,MAAM,KAAK,CAAC,2BAA2B,CAAC,EAAE,CAAC,CAAC;IACpD,CAAC;AACL,CAAC;AAED,SAAgB,wBAAwB,CAAC,CAAgB;IACrD,QAAQ,CAAC,EAAE,CAAC;QACR,KAAK,aAAa,CAAC,iBAAiB;YAChC,OAAO,aAAa,CAAC,iBAAiB,CAAC;QAC3C,KAAK,aAAa,CAAC,wBAAwB;YACvC,OAAO,aAAa,CAAC,2BAA2B,CAAC;QACrD,KAAK,aAAa,CAAC,eAAe;YAC9B,OAAO,aAAa,CAAC,sBAAsB,CAAC;QAChD,KAAK,aAAa,CAAC,YAAY;YAC3B,OAAO,aAAa,CAAC,mBAAmB,CAAC;QAC7C,KAAK,aAAa,CAAC,cAAc;YAC7B,OAAO,aAAa,CAAC,eAAe,CAAC;QACzC,KAAK,aAAa,CAAC,iBAAiB,CAAC;QACrC,KAAK,aAAa,CAAC,2BAA2B,CAAC;QAC/C,KAAK,aAAa,CAAC,QAAQ,CAAC;QAC5B,KAAK,aAAa,CAAC,mBAAmB,CAAC;QACvC,KAAK,aAAa,CAAC,cAAc,CAAC;QAClC,KAAK,aAAa,CAAC,YAAY,CAAC;QAChC,KAAK,aAAa,CAAC,kBAAkB,CAAC;QACtC,KAAK,aAAa,CAAC,eAAe,CAAC;QACnC,KAAK,aAAa,CAAC,aAAa,CAAC;QACjC,KAAK,aAAa,CAAC,aAAa,CAAC;QACjC,KAAK,aAAa,CAAC,qBAAqB,CAAC;QACzC,KAAK,aAAa,CAAC,iBAAiB,CAAC;QACrC,KAAK,aAAa,CAAC,sBAAsB,CAAC;QAC1C,KAAK,aAAa,CAAC,mBAAmB,CAAC;QACvC,KAAK,aAAa,CAAC,YAAY,CAAC;QAChC,KAAK,aAAa,CAAC,kBAAkB,CAAC;QACtC,KAAK,aAAa,CAAC,UAAU,CAAC;QAC9B,KAAK,aAAa,CAAC,gBAAgB,CAAC;QACpC,KAAK,aAAa,CAAC,oBAAoB,CAAC;QACxC,KAAK,aAAa,CAAC,eAAe;YAC9B,OAAO,aAAa,CAAC,aAAa,CAAC;IAC3C,CAAC;AACL,CAAC;AAED,SAAS,MAAM,CACX,cAA+B,EAC/B,QAAgB,EAChB,2BAA8D,EAAE;IAEhE,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACpF,OAAO,QAAQ,CAAC;IACpB,CAAC;SAAM,CAAC;QACJ,MAAM,aAAa,GACf,yBAAY;YACZ,kCAAiB;YACjB,cAAc;iBACT,MAAM,CAAC,CAAC,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;iBAC7D,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,qBAAqB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC;iBAC1D,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAC/B,MAAM,CAAC,OAAO,CAAC,wBAAwB,CAAC;iBACnC,GAAG,CAAC,CAAC,CAAC,SAAS,EAAE,GAAG,CAAC,EAAE,EAAE;gBACtB,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;oBAChD,MAAM,KAAK,CAAC,aAAa,SAAS,yBAAyB,CAAC,CAAC;gBACjE,CAAC;gBACD,OAAO,qBAAqB,CAAC,GAAG,CAAC,CAAC;YACtC,CAAC,CAAC;iBACD,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QACpC,IAAI,aAAa,KAAK,2BAAa,EAAE,CAAC;YAClC,OAAO,aAAa,GAAG,iBAAS,CAAC;QACrC,CAAC;aAAM,CAAC;YACJ,OAAO,aAAa,CAAC;QACzB,CAAC;IACL,CAAC;AACL,CAAC;AAED,SAAgB,UAAU,CACtB,cAA+B,EAC/B,2BAA8D,EAAE;IAEhE,OAAO,MAAM,CAAC,cAAc,EAAE,mBAAS,EAAE,wBAAwB,CAAC,CAAC;AACvE,CAAC;AAED,SAAgB,aAAa,CAAC,cAA+B;IACzD,iEAAiE;IACjE,OAAO,MAAM,CAAC,cAAc,EAAE,yBAAY,CAAC,CAAC;AAChD,CAAC;AAED,SAAgB,gBAAgB,CAAC,SAAwB,EAAE,OAAe;IACtE,IAAI,kBAAkB,GAAG,CAAC,CAAC;IAC3B,OAAO,qBAAqB,CAAC,kBAAkB,CAAC,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;QACjE,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,CAAC,kBAAkB,CAAC,CAAC;QAC3D,MAAM,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC,kBAAkB,GAAG,iBAAS,CAAC,CAAC;QACzE,MAAM,SAAS,GAAG,qBAAqB,CAAC,kBAAkB,CAAC,CAAC;QAC5D,IAAI,SAAS,IAAI,SAAS,EAAE,CAAC;YACzB,OAAO,OAAO,CAAC,KAAK,CAAC,SAAS,EAAE,SAAS,GAAG,WAAW,CAAC,CAAC;QAC7D,CAAC;QACD,kBAAkB,GAAG,SAAS,GAAG,WAAW,CAAC;IACjD,CAAC;IACD,OAAO,IAAI,CAAC;AAChB,CAAC;AAED,SAAgB,iBAAiB,CAAC,OAAe;IAC7C,MAAM,cAAc,GAAG,EAAE,CAAC;IAC1B,IAAI,kBAAkB,GAAG,CAAC,CAAC;IAC3B,OAAO,kBAAkB,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;QACzC,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,CAAC,kBAAkB,CAAC,CAAC;QAC3D,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC/B,MAAM,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC,kBAAkB,GAAG,iBAAS,CAAC,CAAC;QACzE,kBAAkB,IAAI,qBAAqB,CAAC,WAAW,CAAC,CAAC;IAC7D,CAAC;IACD,OAAO,cAAc,CAAC;AAC1B,CAAC;AAED,SAAgB,oBAAoB,CAAC,IAAU;IAC3C,MAAM,cAAc,GAAG,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACvD,MAAM,iBAAiB,GAAG,cAAc,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;IACvE,OAAO,aAAa,CAAC,iBAAiB,CAAC,CAAC;AAC5C,CAAC;AAED,SAAgB,+BAA+B,CAC3C,IAAyB,EACzB,OAAkB,EAClB,aAA4B,EAC5B,YAAoB,EACpB,SAAS,GAAG,oCAAqB;IAEjC,MAAM,IAAI,GAAG,IAAA,oBAAU,EAAC,OAAO,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;IAClD,MAAM,aAAa,GAAG,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;IAEpE,MAAM,mBAAmB,GAAG,aAAa,CAAC,CAAC,CAAC,qBAAqB,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5F,MAAM,eAAe,GAAG,qBAAqB,CAAC,YAAY,CAAC,CAAC;IAE5D,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,eAAe,GAAG,mBAAmB,CAAC;AACpE,CAAC"}