{"version": 3, "file": "state.js", "sourceRoot": "", "sources": ["../../../../src/extensions/memoTransfer/state.ts"], "names": [], "mappings": ";;;AAgBA,0CAOC;AAvBD,yDAA+C;AAC/C,qEAAmD;AAEnD,0DAAsE;AAQtE,iEAAiE;AACpD,QAAA,kBAAkB,GAAG,IAAA,sBAAM,EAAe,CAAC,IAAA,0BAAI,EAAC,8BAA8B,CAAC,CAAC,CAAC,CAAC;AAElF,QAAA,kBAAkB,GAAG,0BAAkB,CAAC,IAAI,CAAC;AAE1D,SAAgB,eAAe,CAAC,OAAgB;IAC5C,MAAM,aAAa,GAAG,IAAA,mCAAgB,EAAC,gCAAa,CAAC,YAAY,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;IACpF,IAAI,aAAa,KAAK,IAAI,EAAE,CAAC;QACzB,OAAO,0BAAkB,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;IACpD,CAAC;SAAM,CAAC;QACJ,OAAO,IAAI,CAAC;IAChB,CAAC;AACL,CAAC"}