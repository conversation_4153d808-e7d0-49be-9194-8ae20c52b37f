{"version": 3, "file": "immutableOwner.js", "sourceRoot": "", "sources": ["../../../src/extensions/immutableOwner.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAE/C,OAAO,EAAE,aAAa,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AAKrE,kDAAkD;AAClD,MAAM,CAAC,MAAM,oBAAoB,GAAG,MAAM,CAAiB,EAAE,CAAC,CAAC;AAE/D,MAAM,CAAC,MAAM,oBAAoB,GAAG,oBAAoB,CAAC,IAAI,CAAC;AAE9D,MAAM,UAAU,iBAAiB,CAAC,OAAgB;IAC9C,MAAM,aAAa,GAAG,gBAAgB,CAAC,aAAa,CAAC,cAAc,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;IACtF,IAAI,aAAa,KAAK,IAAI,EAAE,CAAC;QACzB,OAAO,oBAAoB,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;IACtD,CAAC;SAAM,CAAC;QACJ,OAAO,IAAI,CAAC;IAChB,CAAC;AACL,CAAC"}