{"version": 3, "file": "state.js", "sourceRoot": "", "sources": ["../../../../src/extensions/memoTransfer/state.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAC/C,OAAO,EAAE,IAAI,EAAE,MAAM,6BAA6B,CAAC;AAEnD,OAAO,EAAE,aAAa,EAAE,gBAAgB,EAAE,MAAM,qBAAqB,CAAC;AAQtE,iEAAiE;AACjE,MAAM,CAAC,MAAM,kBAAkB,GAAG,MAAM,CAAe,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC,CAAC,CAAC;AAE/F,MAAM,CAAC,MAAM,kBAAkB,GAAG,kBAAkB,CAAC,IAAI,CAAC;AAE1D,MAAM,UAAU,eAAe,CAAC,OAAgB;IAC5C,MAAM,aAAa,GAAG,gBAAgB,CAAC,aAAa,CAAC,YAAY,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;IACpF,IAAI,aAAa,KAAK,IAAI,EAAE,CAAC;QACzB,OAAO,kBAAkB,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;IACpD,CAAC;SAAM,CAAC;QACJ,OAAO,IAAI,CAAC;IAChB,CAAC;AACL,CAAC"}