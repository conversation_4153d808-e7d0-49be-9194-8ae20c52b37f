{"version": 3, "file": "extensionType.d.ts", "sourceRoot": "", "sources": ["../../../src/extensions/extensionType.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAG9D,OAAO,KAAK,EAAE,IAAI,EAAE,MAAM,kBAAkB,CAAC;AAuB7C,oBAAY,aAAa;IACrB,aAAa,IAAA;IACb,iBAAiB,IAAA;IACjB,iBAAiB,IAAA;IACjB,kBAAkB,IAAA;IAClB,wBAAwB,IAAA;IACxB,2BAA2B,IAAA;IAC3B,mBAAmB,IAAA;IACnB,cAAc,IAAA;IACd,YAAY,IAAA;IACZ,eAAe,IAAA;IACf,qBAAqB,KAAA;IACrB,QAAQ,KAAA;IACR,iBAAiB,KAAA;IACjB,sBAAsB,KAAA;IACtB,YAAY,KAAA;IACZ,mBAAmB,KAAA;IAGnB,eAAe,KAAK,CAAE,kDAAkD;IACxE,aAAa,KAAK,CAAE,kDAAkD;IACtE,YAAY,KAAK;IACjB,UAAU,KAAK;IACf,kBAAkB,KAAK;IACvB,gBAAgB,KAAK;IAErB,oBAAoB,KAAK;IACzB,cAAc,KAAK;IACnB,eAAe,KAAK;CACvB;AAED,eAAO,MAAM,SAAS,IAAI,CAAC;AAC3B,eAAO,MAAM,WAAW,IAAI,CAAC;AAiB7B,wBAAgB,UAAU,CAAC,CAAC,EAAE,aAAa,GAAG,MAAM,CAuDnD;AAED,wBAAgB,eAAe,CAAC,CAAC,EAAE,aAAa,GAAG,OAAO,CAgCzD;AAED,wBAAgB,kBAAkB,CAAC,CAAC,EAAE,aAAa,GAAG,OAAO,CAgC5D;AAED,wBAAgB,wBAAwB,CAAC,CAAC,EAAE,aAAa,GAAG,aAAa,CAkCxE;AAiCD,wBAAgB,UAAU,CACtB,cAAc,EAAE,aAAa,EAAE,EAC/B,wBAAwB,GAAE;KAAG,CAAC,IAAI,aAAa,CAAC,CAAC,EAAE,MAAM;CAAO,GACjE,MAAM,CAER;AAED,wBAAgB,aAAa,CAAC,cAAc,EAAE,aAAa,EAAE,GAAG,MAAM,CAGrE;AAED,wBAAgB,gBAAgB,CAAC,SAAS,EAAE,aAAa,EAAE,OAAO,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI,CAYzF;AAED,wBAAgB,iBAAiB,CAAC,OAAO,EAAE,MAAM,GAAG,aAAa,EAAE,CAUlE;AAED,wBAAgB,oBAAoB,CAAC,IAAI,EAAE,IAAI,GAAG,MAAM,CAIvD;AAED,wBAAgB,+BAA+B,CAC3C,IAAI,EAAE,WAAW,CAAC,MAAM,CAAC,EACzB,OAAO,EAAE,SAAS,EAClB,aAAa,EAAE,aAAa,EAC5B,YAAY,EAAE,MAAM,EACpB,SAAS,YAAwB,GAClC,MAAM,CAQR"}