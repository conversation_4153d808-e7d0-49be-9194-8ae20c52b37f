{"version": 3, "file": "state.d.ts", "sourceRoot": "", "sources": ["../../../../src/extensions/transferFee/state.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAGpD,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AACjD,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,wBAAwB,CAAC;AACtD,OAAO,KAAK,EAAE,IAAI,EAAE,MAAM,qBAAqB,CAAC;AAGhD,eAAO,MAAM,oBAAoB,QAAQ,CAAC;AAC1C,eAAO,MAAM,mBAAmB,QAA+B,CAAC;AAEhE,iDAAiD;AACjD,MAAM,WAAW,WAAW;IACxB,sDAAsD;IACtD,KAAK,EAAE,MAAM,CAAC;IACd,0EAA0E;IAC1E,UAAU,EAAE,MAAM,CAAC;IACnB;;;OAGG;IACH,sBAAsB,EAAE,MAAM,CAAC;CAClC;AAED,6CAA6C;AAC7C,MAAM,WAAW,iBAAiB;IAC9B,wCAAwC;IACxC,0BAA0B,EAAE,SAAS,CAAC;IACtC,iEAAiE;IACjE,yBAAyB,EAAE,SAAS,CAAC;IACrC,mFAAmF;IACnF,cAAc,EAAE,MAAM,CAAC;IACvB,6EAA6E;IAC7E,gBAAgB,EAAE,WAAW,CAAC;IAC9B,8EAA8E;IAC9E,gBAAgB,EAAE,WAAW,CAAC;CACjC;AAED,sDAAsD;AACtD,wBAAgB,iBAAiB,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC,WAAW,CAAC,CAExE;AAED,iCAAiC;AACjC,wBAAgB,YAAY,CAAC,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,MAAM,GAAG,MAAM,CAUnF;AAED,uEAAuE;AACvE,eAAO,MAAM,uBAAuB,8DAMlC,CAAC;AAEH,eAAO,MAAM,wBAAwB,QAA+B,CAAC;AAErE,kCAAkC;AAClC,wBAAgB,WAAW,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,KAAK,EAAE,MAAM,GAAG,WAAW,CAM5F;AAED,6DAA6D;AAC7D,wBAAgB,iBAAiB,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,GAAG,MAAM,CAGnH;AAED,6CAA6C;AAC7C,MAAM,WAAW,iBAAiB;IAC9B,4EAA4E;IAC5E,cAAc,EAAE,MAAM,CAAC;CAC1B;AACD,uCAAuC;AACvC,eAAO,MAAM,uBAAuB,8DAAqD,CAAC;AAC1F,eAAO,MAAM,wBAAwB,QAA+B,CAAC;AAErE,wBAAgB,oBAAoB,CAAC,IAAI,EAAE,IAAI,GAAG,iBAAiB,GAAG,IAAI,CAOzE;AAED,wBAAgB,oBAAoB,CAAC,OAAO,EAAE,OAAO,GAAG,iBAAiB,GAAG,IAAI,CAO/E"}