{"version": 3, "file": "approve.d.ts", "sourceRoot": "", "sources": ["../../../src/instructions/approve.ts"], "names": [], "mappings": "AAEA,OAAO,KAAK,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,iBAAiB,CAAC;AACtE,OAAO,EAAE,sBAAsB,EAAE,MAAM,iBAAiB,CAAC;AASzD,OAAO,EAAE,gBAAgB,EAAE,MAAM,YAAY,CAAC;AAE9C,iBAAiB;AACjB,MAAM,WAAW,sBAAsB;IACnC,WAAW,EAAE,gBAAgB,CAAC,OAAO,CAAC;IACtC,MAAM,EAAE,MAAM,CAAC;CAClB;AAED,iBAAiB;AACjB,eAAO,MAAM,sBAAsB,mEAAqE,CAAC;AAEzG;;;;;;;;;;;GAWG;AACH,wBAAgB,wBAAwB,CACpC,OAAO,EAAE,SAAS,EAClB,QAAQ,EAAE,SAAS,EACnB,KAAK,EAAE,SAAS,EAChB,MAAM,EAAE,MAAM,GAAG,MAAM,EACvB,YAAY,GAAE,CAAC,MAAM,GAAG,SAAS,CAAC,EAAO,EACzC,SAAS,YAAmB,GAC7B,sBAAsB,CAoBxB;AAED,2CAA2C;AAC3C,MAAM,WAAW,yBAAyB;IACtC,SAAS,EAAE,SAAS,CAAC;IACrB,IAAI,EAAE;QACF,OAAO,EAAE,WAAW,CAAC;QACrB,QAAQ,EAAE,WAAW,CAAC;QACtB,KAAK,EAAE,WAAW,CAAC;QACnB,YAAY,EAAE,WAAW,EAAE,CAAC;KAC/B,CAAC;IACF,IAAI,EAAE;QACF,WAAW,EAAE,gBAAgB,CAAC,OAAO,CAAC;QACtC,MAAM,EAAE,MAAM,CAAC;KAClB,CAAC;CACL;AAED;;;;;;;GAOG;AACH,wBAAgB,wBAAwB,CACpC,WAAW,EAAE,sBAAsB,EACnC,SAAS,YAAmB,GAC7B,yBAAyB,CAuB3B;AAED,mDAAmD;AACnD,MAAM,WAAW,kCAAkC;IAC/C,SAAS,EAAE,SAAS,CAAC;IACrB,IAAI,EAAE;QACF,OAAO,EAAE,WAAW,GAAG,SAAS,CAAC;QACjC,QAAQ,EAAE,WAAW,GAAG,SAAS,CAAC;QAClC,KAAK,EAAE,WAAW,GAAG,SAAS,CAAC;QAC/B,YAAY,EAAE,WAAW,EAAE,CAAC;KAC/B,CAAC;IACF,IAAI,EAAE;QACF,WAAW,EAAE,MAAM,CAAC;QACpB,MAAM,EAAE,MAAM,CAAC;KAClB,CAAC;CACL;AAED;;;;;;GAMG;AACH,wBAAgB,iCAAiC,CAAC,EAC9C,SAAS,EACT,IAAI,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,YAAY,CAAC,EACjD,IAAI,GACP,EAAE,sBAAsB,GAAG,kCAAkC,CAW7D"}