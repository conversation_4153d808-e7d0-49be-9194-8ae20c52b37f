{"version": 3, "sources": ["../node_modules/esbuild-plugin-polyfill-node/polyfills/buffer.js", "../src/lib/client/websocket.browser.ts", "../src/lib/client.ts", "../src/lib/utils.ts", "../src/index.browser.ts"], "names": ["EventEmitter"], "mappings": ";AAAA,SAAS,cAAc;;;ACOvB,SAAS,oBAAoB;AAQ7B,IAAM,uBAAN,cAAmC,aACnC;AAAA,EACI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,YAAY,SAAiB,SAAa,WAC1C;AACI,UAAM;AAEN,SAAK,SAAS,IAAI,OAAO,UAAU,SAAS,SAAS;AAErD,SAAK,OAAO,SAAS,MAAM,KAAK,KAAK,MAAM;AAC3C,SAAK,OAAO,YAAY,CAAC,UAAU,KAAK,KAAK,WAAW,MAAM,IAAI;AAClE,SAAK,OAAO,UAAU,CAAC,UAAU,KAAK,KAAK,SAAS,KAAK;AACzD,SAAK,OAAO,UAAU,CAAC,UACvB;AACI,WAAK,KAAK,SAAS,MAAM,MAAM,MAAM,MAAM;AAAA,IAC/C;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,KACI,MACA,mBAGA,UAEJ;AACI,UAAM,KAAK,YAAY;AAEvB,QACA;AACI,WAAK,OAAO,KAAK,IAAI;AACrB,SAAG;AAAA,IACP,SACO,OACP;AACI,SAAG,KAAK;AAAA,IACZ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,MAAe,QACrB;AACI,SAAK,OAAO,MAAM,MAAM,MAAM;AAAA,EAClC;AAAA,EAEA,iBACI,MACA,UACA,SAEJ;AACI,SAAK,OAAO,iBAAiB,MAAM,UAAU,OAAO;AAAA,EACxD;AACJ;AASO,SAAS,UACZ,SACA,SAEJ;AACI,SAAO,IAAI,qBAAqB,SAAS,OAAO;AACpD;;;ACjGA,SAAS,gBAAAA,qBAAoB;;;ACCtB,IAAM,kBAAN,MACP;AAAA,EACI,OAAO,OACP;AACI,WAAO,KAAK,UAAU,KAAK;AAAA,EAC/B;AAAA,EAEA,OAAO,OACP;AACI,WAAO,KAAK,MAAM,KAAK;AAAA,EAC3B;AACJ;;;ADeO,IAAM,eAAN,cAA2BA,cAClC;AAAA,EACY;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EAEA;AAAA,EACA;AAAA,EAIA;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYR,YACI,kBACA,UAAU,uBACV;AAAA,IACI,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,qBAAqB;AAAA,IACrB,iBAAiB;AAAA,IACjB,GAAG;AAAA,EACP,IAAI,CAAC,GACL,qBAIA,UAEJ;AACI,UAAM;AAEN,SAAK,mBAAmB;AAExB,SAAK,QAAQ,CAAC;AACd,SAAK,SAAS;AAEd,SAAK,UAAU;AACf,SAAK,cAAc;AACnB,SAAK,QAAQ;AACb,SAAK,YAAY;AACjB,SAAK,qBAAqB;AAC1B,SAAK,qBAAqB;AAC1B,SAAK,iBAAiB;AACtB,SAAK,eAAe;AACpB,SAAK,qBAAqB;AAC1B,SAAK,sBAAsB,wBAAwB,MAAM,OAAO,KAAK,WAAW,WAC1E,EAAE,KAAK,SACP,OAAO,KAAK,MAAM,IAAI;AAE5B,QAAI,CAAC,SAAU,MAAK,WAAW,IAAI,gBAAgB;AAAA,QAC9C,MAAK,WAAW;AAErB,QAAI,KAAK;AACL,WAAK,SAAS,KAAK,SAAS;AAAA,QACxB,aAAa,KAAK;AAAA,QAClB,WAAW,KAAK;AAAA,QAChB,oBAAoB,KAAK;AAAA,QACzB,gBAAgB,KAAK;AAAA,QACrB,GAAG,KAAK;AAAA,MACZ,CAAC;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UACA;AACI,QAAI,KAAK,OAAQ;AAEjB,SAAK,SAAS,KAAK,SAAS;AAAA,MACxB,aAAa,KAAK;AAAA,MAClB,WAAW,KAAK;AAAA,MAChB,oBAAoB,KAAK;AAAA,MACzB,gBAAgB,KAAK;AAAA,MACrB,GAAG,KAAK;AAAA,IACZ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,KACI,QACA,QACA,SACA,SAEJ;AACI,QAAI,CAAC,WAAW,aAAa,OAAO,SACpC;AACI,gBAAU;AACV,gBAAU;AAAA,IACd;AAEA,WAAO,IAAI,QAAQ,CAAC,SAAS,WAC7B;AACI,UAAI,CAAC,KAAK,MAAO,QAAO,OAAO,IAAI,MAAM,kBAAkB,CAAC;AAE5D,YAAM,SAAS,KAAK,oBAAoB,QAAQ,MAAM;AAEtD,YAAM,UAAU;AAAA,QACZ,SAAS;AAAA,QACT;AAAA,QACA,QAAQ,UAAU;AAAA,QAClB,IAAI;AAAA,MACR;AAEA,WAAK,OAAO,KAAK,KAAK,SAAS,OAAO,OAAO,GAAG,SAAS,CAAC,UAC1D;AACI,YAAI,MAAO,QAAO,OAAO,KAAK;AAE9B,aAAK,MAAM,MAAM,IAAI,EAAE,SAAS,CAAC,SAAS,MAAM,EAAE;AAElD,YAAI,SACJ;AACI,eAAK,MAAM,MAAM,EAAE,UAAU,WAAW,MACxC;AACI,mBAAO,KAAK,MAAM,MAAM;AACxB,mBAAO,IAAI,MAAM,eAAe,CAAC;AAAA,UACrC,GAAG,OAAO;AAAA,QACd;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,MAAM,QACZ;AACI,UAAM,OAAO,MAAM,KAAK,KAAK,aAAa,MAAM;AAEhD,QAAI,CAAC,KAAM,OAAM,IAAI,MAAM,uBAAuB;AAElD,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,cACN;AACI,WAAO,MAAM,KAAK,KAAK,eAAe;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,QAAgB,QACvB;AACI,WAAO,IAAI,QAAc,CAAC,SAAS,WACnC;AACI,UAAI,CAAC,KAAK,MAAO,QAAO,OAAO,IAAI,MAAM,kBAAkB,CAAC;AAE5D,YAAM,UAAU;AAAA,QACZ,SAAS;AAAA,QACT;AAAA,QACA;AAAA,MACJ;AAEA,WAAK,OAAO,KAAK,KAAK,SAAS,OAAO,OAAO,GAAG,CAAC,UACjD;AACI,YAAI,MAAO,QAAO,OAAO,KAAK;AAE9B,gBAAQ;AAAA,MACZ,CAAC;AAAA,IACL,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,UAAU,OAChB;AACI,QAAI,OAAO,UAAU,SAAU,SAAQ,CAAC,KAAK;AAE7C,UAAM,SAAS,MAAM,KAAK,KAAK,UAAU,KAAK;AAE9C,QAAI,OAAO,UAAU,YAAY,OAAO,KAAK,MAAM;AAC/C,YAAM,IAAI;AAAA,QACN,qCAAqC,QAAQ,aAAa,OAAO,KAAK;AAAA,MAC1E;AAEJ,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,YAAY,OAClB;AACI,QAAI,OAAO,UAAU,SAAU,SAAQ,CAAC,KAAK;AAE7C,UAAM,SAAS,MAAM,KAAK,KAAK,WAAW,KAAK;AAE/C,QAAI,OAAO,UAAU,YAAY,OAAO,KAAK,MAAM;AAC/C,YAAM,IAAI,MAAM,8CAA8C,MAAM;AAExE,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,MAAe,MACrB;AACI,SAAK,OAAO,MAAM,QAAQ,KAAM,IAAI;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iBAAiB,WACjB;AACI,SAAK,YAAY;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,qBAAqB,UACrB;AACI,SAAK,qBAAqB;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iBAAiB,gBACjB;AACI,SAAK,iBAAiB;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUQ,SACJ,SACA,SAEJ;AACI,iBAAa,KAAK,kBAAkB;AACpC,SAAK,SAAS,KAAK,iBAAiB,SAAS,OAAO;AAEpD,SAAK,OAAO,iBAAiB,QAAQ,MACrC;AACI,WAAK,QAAQ;AACb,WAAK,KAAK,MAAM;AAChB,WAAK,qBAAqB;AAAA,IAC9B,CAAC;AAED,SAAK,OAAO,iBAAiB,WAAW,CAAC,EAAE,MAAM,QAAQ,MACzD;AACI,UAAI,mBAAmB;AACnB,kBAAU,OAAO,KAAK,OAAO,EAAE,SAAS;AAE5C,UACA;AACI,kBAAU,KAAK,SAAS,OAAO,OAAO;AAAA,MAC1C,SACO,OACP;AACI;AAAA,MACJ;AAGA,UAAI,QAAQ,gBAAgB,KAAK,UAAU,QAAQ,YAAY,EAAE,QACjE;AACI,YAAI,CAAC,OAAO,KAAK,QAAQ,MAAM,EAAE;AAC7B,iBAAO,KAAK,KAAK,QAAQ,YAAY;AAEzC,cAAM,OAAO,CAAC,QAAQ,YAAY;AAElC,YAAI,QAAQ,OAAO,gBAAgB,OAAQ,MAAK,KAAK,QAAQ,MAAM;AAAA;AAG/D,mBAAS,IAAI,GAAG,IAAI,QAAQ,OAAO,QAAQ;AACvC,iBAAK,KAAK,QAAQ,OAAO,CAAC,CAAC;AAInC,eAAO,QAAQ,QAAQ,EAAE,KAAK,MAC9B;AAEI,eAAK,KAAK,MAAM,MAAM,IAAI;AAAA,QAC9B,CAAC;AAAA,MACL;AAEA,UAAI,CAAC,KAAK,MAAM,QAAQ,EAAE,GAC1B;AAEI,YAAI,QAAQ,QACZ;AAEI,iBAAO,QAAQ,QAAQ,EAAE,KAAK,MAC9B;AACI,iBAAK,KAAK,QAAQ,QAAQ,SAAS,MAAM;AAAA,UAC7C,CAAC;AAAA,QACL;AAEA;AAAA,MACJ;AAGA,UAAI,WAAW,YAAY,YAAY;AACnC,aAAK,MAAM,QAAQ,EAAE,EAAE,QAAQ,CAAC;AAAA,UAC5B,IAAI;AAAA,YACA;AAAA,UAEJ;AAAA,QACJ;AAEJ,UAAI,KAAK,MAAM,QAAQ,EAAE,EAAE;AACvB,qBAAa,KAAK,MAAM,QAAQ,EAAE,EAAE,OAAO;AAE/C,UAAI,QAAQ,MAAO,MAAK,MAAM,QAAQ,EAAE,EAAE,QAAQ,CAAC,EAAE,QAAQ,KAAK;AAAA,UAC7D,MAAK,MAAM,QAAQ,EAAE,EAAE,QAAQ,CAAC,EAAE,QAAQ,MAAM;AAErD,aAAO,KAAK,MAAM,QAAQ,EAAE;AAAA,IAChC,CAAC;AAED,SAAK,OAAO,iBAAiB,SAAS,CAAC,UAAU,KAAK,KAAK,SAAS,KAAK,CAAC;AAE1E,SAAK,OAAO,iBAAiB,SAAS,CAAC,EAAE,MAAM,OAAO,MACtD;AACI,UAAI,KAAK;AAEL,mBAAW,MAAM,KAAK,KAAK,SAAS,MAAM,MAAM,GAAG,CAAC;AAExD,WAAK,QAAQ;AACb,WAAK,SAAS;AAEd,UAAI,SAAS,IAAM;AAEnB,WAAK;AAEL,UACI,KAAK,cACZ,KAAK,iBAAiB,KAAK,sBAC1B,KAAK,mBAAmB;AAElB,aAAK,qBAAqB;AAAA,UACtB,MAAM,KAAK,SAAS,SAAS,OAAO;AAAA,UACpC,KAAK;AAAA,QACT;AAAA,IACR,CAAC;AAAA,EACL;AACJ;;;AEzbO,IAAM,SAAN,cAAqB,aAC5B;AAAA,EACI,YACI,UAAU,uBACV;AAAA,IACI,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,qBAAqB;AAAA,IACrB,iBAAiB;AAAA,EACrB,IAAgC,CAAC,GACjC,qBAKJ;AACI;AAAA,MACI;AAAA,MACA;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AACJ", "sourcesContent": ["export { Buffer } from \"buffer\";\n", "/**\n * WebSocket implements a browser-side WebSocket specification.\n * @module Client\n */\n\n\"use strict\"\n\nimport { EventEmitter } from \"eventemitter3\"\n\nimport {\n    BrowserWebSocketType,\n    NodeWebSocketType,\n    IWSClientAdditionalOptions,\n} from \"./client.types.js\"\n\nclass WebSocket<PERSON>rowserImpl extends EventEmitter\n{\n    socket: BrowserWebSocketType\n\n    /** Instantiate a WebSocket class\n   * @constructor\n   * @param {String} address - url to a websocket server\n   * @param {(Object)} options - websocket options\n   * @param {(String|Array)} protocols - a list of protocols\n   * @return {WebSocketBrowserImpl} - returns a WebSocket instance\n   */\n    constructor(address: string, options: {}, protocols?: string | string[])\n    {\n        super()\n\n        this.socket = new window.WebSocket(address, protocols)\n\n        this.socket.onopen = () => this.emit(\"open\")\n        this.socket.onmessage = (event) => this.emit(\"message\", event.data)\n        this.socket.onerror = (error) => this.emit(\"error\", error)\n        this.socket.onclose = (event) =>\n        {\n            this.emit(\"close\", event.code, event.reason)\n        }\n    }\n\n    /**\n   * Sends data through a websocket connection\n   * @method\n   * @param {(String|Object)} data - data to be sent via websocket\n   * @param {Object} optionsOrCallback - ws options\n   * @param {Function} callback - a callback called once the data is sent\n   * @return {Undefined}\n   */\n    send(\n        data: Parameters<BrowserWebSocketType[\"send\"]>[0],\n        optionsOrCallback: (\n      error?: Error\n    ) => void | Parameters<NodeWebSocketType[\"send\"]>[1],\n        callback?: () => void\n    )\n    {\n        const cb = callback || optionsOrCallback\n\n        try\n        {\n            this.socket.send(data)\n            cb()\n        }\n        catch (error)\n        {\n            cb(error)\n        }\n    }\n\n    /**\n   * Closes an underlying socket\n   * @method\n   * @param {Number} code - status code explaining why the connection is being closed\n   * @param {String} reason - a description why the connection is closing\n   * @return {Undefined}\n   * @throws {Error}\n   */\n    close(code?: number, reason?: string)\n    {\n        this.socket.close(code, reason)\n    }\n\n    addEventListener<K extends keyof WebSocketEventMap>(\n        type: K,\n        listener: (ev: WebSocketEventMap[K]) => any,\n        options?: boolean | AddEventListenerOptions\n    ): void\n    {\n        this.socket.addEventListener(type, listener, options)\n    }\n}\n\n/**\n * factory method for common WebSocket instance\n * @method\n * @param {String} address - url to a websocket server\n * @param {(Object)} options - websocket options\n * @return {Undefined}\n */\nexport function WebSocket(\n    address: string,\n    options: IWSClientAdditionalOptions\n)\n{\n    return new WebSocketBrowserImpl(address, options)\n}\n", "/**\n * \"Client\" wraps \"ws\" or a browser-implemented \"WebSocket\" library\n * according to the environment providing JSON RPC 2.0 support on top.\n * @module Client\n */\n\n\"use strict\"\n\nimport NodeWebSocket from \"ws\"\nimport { EventEmitter } from \"eventemitter3\"\nimport {\n    ICommonWebSocket,\n    IWSClientAdditionalOptions,\n    NodeWebSocketType,\n    ICommonWebSocketFactory,\n} from \"./client/client.types.js\"\n\nimport { <PERSON>Pack, DefaultDataPack } from \"./utils.js\"\n\ninterface IQueueElement {\n  promise: [\n    Parameters<ConstructorParameters<typeof Promise>[0]>[0],\n    Parameters<ConstructorParameters<typeof Promise>[0]>[1]\n  ];\n  timeout?: ReturnType<typeof setTimeout>;\n}\n\nexport interface IQueue {\n  [x: number | string]: IQueueElement;\n}\n\nexport interface IWSRequestParams {\n  [x: string]: any;\n  [x: number]: any;\n}\n\nexport class CommonClient extends EventEmitter\n{\n    private address: string\n    private rpc_id: number | string\n    private queue: IQueue\n    private options: IWSClientAdditionalOptions & NodeWebSocket.ClientOptions\n    private autoconnect: boolean\n    private ready: boolean\n    private reconnect: boolean\n    private reconnect_timer_id: NodeJS.Timeout\n    private reconnect_interval: number\n    private max_reconnects: number\n    private rest_options: IWSClientAdditionalOptions &\n    NodeWebSocket.ClientOptions\n    private current_reconnects: number\n    private generate_request_id: (\n    method: string,\n    params: object | Array<any>\n  ) => number | string\n    private socket: ICommonWebSocket\n    private webSocketFactory: ICommonWebSocketFactory\n    private dataPack: DataPack<object, string>\n\n    /**\n   * Instantiate a Client class.\n   * @constructor\n   * @param {webSocketFactory} webSocketFactory - factory method for WebSocket\n   * @param {String} address - url to a websocket server\n   * @param {Object} options - ws options object with reconnect parameters\n   * @param {Function} generate_request_id - custom generation request Id\n   * @param {DataPack} dataPack - data pack contains encoder and decoder\n   * @return {CommonClient}\n   */\n    constructor(\n        webSocketFactory: ICommonWebSocketFactory,\n        address = \"ws://localhost:8080\",\n        {\n            autoconnect = true,\n            reconnect = true,\n            reconnect_interval = 1000,\n            max_reconnects = 5,\n            ...rest_options\n        } = {},\n        generate_request_id?: (\n      method: string,\n      params: object | Array<any>\n    ) => number | string,\n        dataPack?: DataPack<object, string>\n    )\n    {\n        super()\n\n        this.webSocketFactory = webSocketFactory\n\n        this.queue = {}\n        this.rpc_id = 0\n\n        this.address = address\n        this.autoconnect = autoconnect\n        this.ready = false\n        this.reconnect = reconnect\n        this.reconnect_timer_id = undefined\n        this.reconnect_interval = reconnect_interval\n        this.max_reconnects = max_reconnects\n        this.rest_options = rest_options\n        this.current_reconnects = 0\n        this.generate_request_id = generate_request_id || (() => typeof this.rpc_id === \"number\"\n            ? ++this.rpc_id\n            : Number(this.rpc_id) + 1)\n\n        if (!dataPack) this.dataPack = new DefaultDataPack()\n        else this.dataPack = dataPack\n\n        if (this.autoconnect)\n            this._connect(this.address, {\n                autoconnect: this.autoconnect,\n                reconnect: this.reconnect,\n                reconnect_interval: this.reconnect_interval,\n                max_reconnects: this.max_reconnects,\n                ...this.rest_options,\n            })\n    }\n\n    /**\n   * Connects to a defined server if not connected already.\n   * @method\n   * @return {Undefined}\n   */\n    connect()\n    {\n        if (this.socket) return\n\n        this._connect(this.address, {\n            autoconnect: this.autoconnect,\n            reconnect: this.reconnect,\n            reconnect_interval: this.reconnect_interval,\n            max_reconnects: this.max_reconnects,\n            ...this.rest_options,\n        })\n    }\n\n    /**\n   * Calls a registered RPC method on server.\n   * @method\n   * @param {String} method - RPC method name\n   * @param {Object|Array} params - optional method parameters\n   * @param {Number} timeout - RPC reply timeout value\n   * @param {Object} ws_opts - options passed to ws\n   * @return {Promise}\n   */\n    call(\n        method: string,\n        params?: IWSRequestParams,\n        timeout?: number,\n        ws_opts?: Parameters<NodeWebSocketType[\"send\"]>[1]\n    )\n    {\n        if (!ws_opts && \"object\" === typeof timeout)\n        {\n            ws_opts = timeout\n            timeout = null\n        }\n\n        return new Promise((resolve, reject) =>\n        {\n            if (!this.ready) return reject(new Error(\"socket not ready\"))\n\n            const rpc_id = this.generate_request_id(method, params)\n\n            const message = {\n                jsonrpc: \"2.0\",\n                method: method,\n                params: params || undefined,\n                id: rpc_id,\n            }\n\n            this.socket.send(this.dataPack.encode(message), ws_opts, (error) =>\n            {\n                if (error) return reject(error)\n\n                this.queue[rpc_id] = { promise: [resolve, reject] }\n\n                if (timeout)\n                {\n                    this.queue[rpc_id].timeout = setTimeout(() =>\n                    {\n                        delete this.queue[rpc_id]\n                        reject(new Error(\"reply timeout\"))\n                    }, timeout)\n                }\n            })\n        })\n    }\n\n    /**\n   * Logins with the other side of the connection.\n   * @method\n   * @param {Object} params - Login credentials object\n   * @return {Promise}\n   */\n    async login(params: IWSRequestParams)\n    {\n        const resp = await this.call(\"rpc.login\", params)\n\n        if (!resp) throw new Error(\"authentication failed\")\n\n        return resp\n    }\n\n    /**\n   * Fetches a list of client's methods registered on server.\n   * @method\n   * @return {Array}\n   */\n    async listMethods()\n    {\n        return await this.call(\"__listMethods\")\n    }\n\n    /**\n   * Sends a JSON-RPC 2.0 notification to server.\n   * @method\n   * @param {String} method - RPC method name\n   * @param {Object} params - optional method parameters\n   * @return {Promise}\n   */\n    notify(method: string, params?: IWSRequestParams)\n    {\n        return new Promise<void>((resolve, reject) =>\n        {\n            if (!this.ready) return reject(new Error(\"socket not ready\"))\n\n            const message = {\n                jsonrpc: \"2.0\",\n                method: method,\n                params,\n            }\n\n            this.socket.send(this.dataPack.encode(message), (error) =>\n            {\n                if (error) return reject(error)\n\n                resolve()\n            })\n        })\n    }\n\n    /**\n   * Subscribes for a defined event.\n   * @method\n   * @param {String|Array} event - event name\n   * @return {Undefined}\n   * @throws {Error}\n   */\n    async subscribe(event: string | Array<string>)\n    {\n        if (typeof event === \"string\") event = [event]\n\n        const result = await this.call(\"rpc.on\", event)\n\n        if (typeof event === \"string\" && result[event] !== \"ok\")\n            throw new Error(\n                \"Failed subscribing to an event '\" + event + \"' with: \" + result[event]\n            )\n\n        return result\n    }\n\n    /**\n   * Unsubscribes from a defined event.\n   * @method\n   * @param {String|Array} event - event name\n   * @return {Undefined}\n   * @throws {Error}\n   */\n    async unsubscribe(event: string | Array<string>)\n    {\n        if (typeof event === \"string\") event = [event]\n\n        const result = await this.call(\"rpc.off\", event)\n\n        if (typeof event === \"string\" && result[event] !== \"ok\")\n            throw new Error(\"Failed unsubscribing from an event with: \" + result)\n\n        return result\n    }\n\n    /**\n   * Closes a WebSocket connection gracefully.\n   * @method\n   * @param {Number} code - socket close code\n   * @param {String} data - optional data to be sent before closing\n   * @return {Undefined}\n   */\n    close(code?: number, data?: string)\n    {\n        this.socket.close(code || 1000, data)\n    }\n\n    /**\n   * Enable / disable automatic reconnection.\n   * @method\n   * @param {Boolean} reconnect - enable / disable reconnection\n   * @return {Undefined}\n   */\n    setAutoReconnect(reconnect: boolean)\n    {\n        this.reconnect = reconnect\n    }\n\n    /**\n   * Set the interval between reconnection attempts.\n   * @method\n   * @param {Number} interval - reconnection interval in milliseconds\n   * @return {Undefined}\n   */\n    setReconnectInterval(interval: number)\n    {\n        this.reconnect_interval = interval\n    }\n\n    /**\n   * Set the maximum number of reconnection attempts.\n   * @method\n   * @param {Number} max_reconnects - maximum reconnection attempts\n   * @return {Undefined}\n   */\n    setMaxReconnects(max_reconnects: number)\n    {\n        this.max_reconnects = max_reconnects\n    }\n\n    /**\n   * Connection/Message handler.\n   * @method\n   * @private\n   * @param {String} address - WebSocket API address\n   * @param {Object} options - ws options object\n   * @return {Undefined}\n   */\n    private _connect(\n        address: string,\n        options: IWSClientAdditionalOptions & NodeWebSocket.ClientOptions\n    )\n    {\n        clearTimeout(this.reconnect_timer_id)\n        this.socket = this.webSocketFactory(address, options)\n\n        this.socket.addEventListener(\"open\", () =>\n        {\n            this.ready = true\n            this.emit(\"open\")\n            this.current_reconnects = 0\n        })\n\n        this.socket.addEventListener(\"message\", ({ data: message }) =>\n        {\n            if (message instanceof ArrayBuffer)\n                message = Buffer.from(message).toString()\n\n            try\n            {\n                message = this.dataPack.decode(message)\n            }\n            catch (error)\n            {\n                return\n            }\n\n            // check if any listeners are attached and forward event\n            if (message.notification && this.listeners(message.notification).length)\n            {\n                if (!Object.keys(message.params).length)\n                    return this.emit(message.notification)\n\n                const args = [message.notification]\n\n                if (message.params.constructor === Object) args.push(message.params)\n                // using for-loop instead of unshift/spread because performance is better\n                else\n                    for (let i = 0; i < message.params.length; i++)\n                        args.push(message.params[i])\n\n                // run as microtask so that pending queue messages are resolved first\n                // eslint-disable-next-line prefer-spread\n                return Promise.resolve().then(() =>\n                {\n                    // eslint-disable-next-line prefer-spread\n                    this.emit.apply(this, args)\n                })\n            }\n\n            if (!this.queue[message.id])\n            {\n                // general JSON RPC 2.0 events\n                if (message.method)\n                {\n                    // run as microtask so that pending queue messages are resolved first\n                    return Promise.resolve().then(() =>\n                    {\n                        this.emit(message.method, message?.params)\n                    })\n                }\n\n                return\n            }\n\n            // reject early since server's response is invalid\n            if (\"error\" in message === \"result\" in message)\n                this.queue[message.id].promise[1](\n                    new Error(\n                        \"Server response malformed. Response must include either \\\"result\\\"\" +\n              \" or \\\"error\\\", but not both.\"\n                    )\n                )\n\n            if (this.queue[message.id].timeout)\n                clearTimeout(this.queue[message.id].timeout)\n\n            if (message.error) this.queue[message.id].promise[1](message.error)\n            else this.queue[message.id].promise[0](message.result)\n\n            delete this.queue[message.id]\n        })\n\n        this.socket.addEventListener(\"error\", (error) => this.emit(\"error\", error))\n\n        this.socket.addEventListener(\"close\", ({ code, reason }) =>\n        {\n            if (this.ready)\n            // Delay close event until internal state is updated\n                setTimeout(() => this.emit(\"close\", code, reason), 0)\n\n            this.ready = false\n            this.socket = undefined\n\n            if (code === 1000) return\n\n            this.current_reconnects++\n\n            if (\n                this.reconnect &&\n        (this.max_reconnects > this.current_reconnects ||\n          this.max_reconnects === 0)\n            )\n                this.reconnect_timer_id = setTimeout(\n                    () => this._connect(address, options),\n                    this.reconnect_interval\n                )\n        })\n    }\n}\n", "\"use strict\"\n\nexport interface DataPack<\n  T,\n  R extends string | ArrayBufferLike | Blob | ArrayBufferView\n> {\n  encode(value: T): R;\n  decode(value: R): T;\n}\n\nexport class DefaultDataPack implements DataPack<Object, string>\n{\n    encode(value: Object): string\n    {\n        return JSON.stringify(value)\n    }\n\n    decode(value: string): Object\n    {\n        return JSON.parse(value)\n    }\n}\n", "\"use strict\"\n\nimport { WebSocket } from \"./lib/client/websocket.browser.js\"\nimport { CommonClient } from \"./lib/client.js\"\nimport { IWSClientAdditionalOptions } from \"./lib/client/client.types.js\"\n\nexport class Client extends CommonClient\n{\n    constructor(\n        address = \"ws://localhost:8080\",\n        {\n            autoconnect = true,\n            reconnect = true,\n            reconnect_interval = 1000,\n            max_reconnects = 5,\n        }: IWSClientAdditionalOptions = {},\n        generate_request_id?: (\n      method: string,\n      params: object | Array<any>\n    ) => number | string\n    )\n    {\n        super(\n            WebSocket,\n            address,\n            {\n                autoconnect,\n                reconnect,\n                reconnect_interval,\n                max_reconnects,\n            },\n            generate_request_id\n        )\n    }\n}\n\nexport * from \"./lib/client.js\"\nexport * from \"./lib/client/websocket.browser.js\"\nexport * from \"./lib/client/client.types.js\"\nexport * from \"./lib/utils.js\"\n"]}