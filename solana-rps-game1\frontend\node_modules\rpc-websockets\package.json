{"name": "rpc-websockets", "version": "9.1.1", "description": "JSON-RPC 2.0 implementation over WebSockets for Node.js", "exports": {"browser": {"import": "./dist/index.browser.mjs", "require": "./dist/index.browser.cjs", "types": "./dist/index.browser.d.ts"}, "node": {"import": "./dist/index.mjs", "require": "./dist/index.cjs", "types": "./dist/index.d.ts"}, "types": "./dist/index.d.ts"}, "browser": {"./dist/index.cjs": "./dist/index.browser.cjs", "./dist/index.mjs": "./dist/index.browser.mjs"}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "type": "commonjs", "files": ["dist/"], "scripts": {"build": "npm run typecheck && mkdir -p ./dist && eslint --fix -c ./.eslintrc './src/**/*.ts' && tsup", "typecheck": "tsc", "pretest": "npm run-script build", "test": "mocha --exit test/*spec.js", "test:client": "mocha --exit test/client.spec.js", "test:server": "mocha --exit test/server.spec.js", "coverage": "istanbul cover _mocha --report lcovonly -- -R spec --exit && cat ./coverage/lcov.info | ./node_modules/coveralls/bin/coveralls.js && rm -rf ./coverage", "coveralls": "npm run-script coverage && coveralls < ./coverage/lcov.info"}, "repository": {"type": "git", "url": "git+https://github.com/elpheria/rpc-websockets.git"}, "dependencies": {"@swc/helpers": "^0.5.11", "@types/uuid": "^8.3.4", "@types/ws": "^8.2.2", "buffer": "^6.0.3", "eventemitter3": "^5.0.1", "uuid": "^8.3.2", "ws": "^8.5.0"}, "optionalDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "devDependencies": {"@microsoft/api-extractor": "^7.47.0", "@typescript-eslint/eslint-plugin": "^5.11.0", "@typescript-eslint/parser": "^5.11.0", "async": "^3.2.3", "chai": "^4.3.6", "coveralls": "^3.1.1", "esbuild-plugin-polyfill-node": "^0.3.0", "eslint": "^8.8.0", "istanbul": "^0.4.5", "mocha": "^10.0.0", "mocha-lcov-reporter": "^1.3.0", "tsup": "^8.1.0", "typescript": "^4.9.5", "url": "^0.11.3"}, "keywords": ["json", "rpc", "websocket", "ws", "client", "server"], "author": "Elpheria", "license": "LGPL-3.0-only", "bugs": {"url": "https://github.com/elpheria/rpc-websockets/issues"}, "homepage": "https://github.com/elpheria/rpc-websockets#readme", "funding": {"type": "paypal", "url": "https://paypal.me/kozjak"}}