{"name": "superstruct", "description": "A simple and composable way to validate data in JavaScript (and TypeScript).", "version": "2.0.2", "license": "MIT", "repository": "git://github.com/ianstormtaylor/superstruct.git", "type": "module", "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "sideEffects": false, "files": ["dist"], "publishConfig": {"registry": "https://registry.npmjs.org"}, "engines": {"node": ">=14.0.0"}, "devDependencies": {"@rollup/plugin-typescript": "^11.1.6", "@types/expect": "^24.3.0", "@types/lodash": "^4.14.144", "@types/node": "^18.7.14", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "lodash": "^4.17.15", "np": "^10.0.0", "prettier": "^3.2.5", "rollup": "^4.12.1", "typescript": "^4.8.3", "vitest": "^1.6.0"}, "scripts": {"build": "rm -rf ./{dist} && rollup --config ./rollup.config.js", "clean": "rm -rf ./{dist,node_modules}", "fix": "npm run fix:eslint && npm run fix:prettier", "fix:eslint": "npm run lint:eslint --fix", "fix:prettier": "prettier '**/*.{js,json,ts}' --write", "lint": "npm run lint:eslint && npm run lint:prettier", "lint:eslint": "eslint '{src,test}/*.{js,ts}'", "lint:prettier": "prettier '**/*.{js,json,ts}' --check", "release": "npm run build && npm run lint && np", "test": "npm run build && npm run test:types && npm run test:vitest", "test:types": "tsc --noEmit && tsc --project ./test/tsconfig.json --noEmit", "test:vitest": "vitest run", "test:watch": "vitest", "watch": "npm run build -- --watch"}, "keywords": ["api", "array", "assert", "cast", "check", "checker", "collection", "data", "error", "express", "hapi", "interface", "invalid", "joi", "json", "list", "model", "object", "orm", "scalar", "schema", "struct", "throw", "type", "types", "valid", "validate", "validation", "validator"]}