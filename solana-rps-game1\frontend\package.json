{"name": "solana-rps-game", "private": true, "version": "1.0.0", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "start": "vite preview --host 0.0.0.0 --port 5000"}, "dependencies": {"@solana/spl-token": "^0.4.13", "@solana/wallet-adapter-backpack": "0.1.12", "@solana/wallet-adapter-base": "^0.9.23", "@solana/wallet-adapter-coinbase": "0.1.18", "@solana/wallet-adapter-ledger": "0.9.25", "@solana/wallet-adapter-phantom": "0.9.24", "@solana/wallet-adapter-react": "^0.15.35", "@solana/wallet-adapter-react-ui": "^0.9.34", "@solana/wallet-adapter-solflare": "0.6.28", "@solana/wallet-adapter-wallets": "^0.19.22", "@solana/web3.js": "^1.98.2", "borsh": "0.7.0", "buffer": "6.0.3", "js-sha256": "0.11.0", "process": "^0.11.10", "react": "18.3.1", "react-dom": "18.3.1", "util": "^0.12.5"}, "devDependencies": {"@types/react": "18.3.18", "@types/react-dom": "18.3.5", "@vitejs/plugin-react": "4.3.4", "autoprefixer": "10.4.19", "postcss": "8.4.38", "tailwindcss": "3.4.1", "typescript": "5.6.2", "vite": "5.1.4"}}