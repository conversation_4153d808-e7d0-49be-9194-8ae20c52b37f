/* frontend/src/App.css */

/* -------------------- */
/* --- Root Variables --- */
/* -------------------- */
:root {
  --primary-color: #4a90e2; /* A vibrant blue */
  --secondary-color: #50e3c2; /* A teal/mint green */
  --accent-color: #f5a623; /* An orange accent */
  --background-color: #1a1a2e; /* Dark navy/purple */
  --surface-color: #1f1f3a; /* Slightly lighter navy for cards/surfaces */
  --text-color: #e0e0e0; /* Light grey for text */
  --text-color-dark: #a0a0a0; /* Darker grey for secondary text */
  --error-color: #e74c3c; /* Red for errors */
  --success-color: #2ecc71; /* Green for success */
  --font-family-main: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-family-headings: '<PERSON><PERSON><PERSON>', sans-serif;
  --border-radius: 8px;
  --box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  --transition-speed: 0.3s;

  /* Game specific colors */
  --rock-color: #ff7f50; /* Coral */
  --paper-color: #add8e6; /* Light Blue */
  --scissors-color: #90ee90; /* Light Green */
  --choice-bg-color: #2a2a4a;
}

/* -------------------- */
/* --- Global Styles --- */
/* -------------------- */
body {
  margin: 0;
  font-family: var(--font-family-main);
  background-color: var(--background-color);
  color: var(--text-color);
  line-height: 1.6;
  font-size: 16px;
  overflow-x: hidden; /* Prevent horizontal scroll */
}

* {
  box-sizing: border-box;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-headings);
  color: var(--secondary-color);
  margin-top: 0;
}

a {
  color: var(--secondary-color);
  text-decoration: none;
  transition: color var(--transition-speed) ease;
}

a:hover {
  color: var(--primary-color);
}

button {
  font-family: var(--font-family-main);
  cursor: pointer;
  border-radius: var(--border-radius);
  padding: 10px 20px;
  font-size: 1rem;
  transition: background-color var(--transition-speed) ease, transform var(--transition-speed) ease;
  border: none;
}

button.primary {
  background-color: var(--primary-color);
  color: white;
}

button.primary:hover {
  background-color: #3a80d2;
  transform: translateY(-2px);
}

button.secondary {
  background-color: var(--secondary-color);
  color: var(--background-color);
}

button.secondary:hover {
  background-color: #40d3b2;
  transform: translateY(-2px);
}

button:disabled {
  background-color: #555;
  color: #999;
  cursor: not-allowed;
  transform: none;
}

input, select, textarea {
  font-family: var(--font-family-main);
  padding: 10px;
  border-radius: var(--border-radius);
  border: 1px solid var(--surface-color);
  background-color: var(--choice-bg-color);
  color: var(--text-color);
  font-size: 1rem;
  margin-bottom: 10px;
  width: 100%;
}

input:focus, select:focus, textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.5);
}

/* -------------------- */
/* --- App Layout --- */
/* -------------------- */
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  background-color: var(--surface-color);
  box-shadow: var(--box-shadow);
  position: sticky;
  top: 0;
  z-index: 1000;
  border-bottom: 1px solid var(--background-color);
}

.app-header .logo {
  font-size: 1.8rem;
  font-weight: bold;
  color: var(--secondary-color);
  font-family: var(--font-family-headings);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.mobile-menu-toggle {
  display: none; /* Hidden by default, shown on mobile */
  background: none;
  border: none;
  color: var(--text-color);
  font-size: 2rem;
  cursor: pointer;
}

.app-nav {
  display: flex;
  gap: 15px;
  padding: 10px 0;
  background-color: var(--surface-color);
  border-radius: 0 0 var(--border-radius) var(--border-radius);
  margin-bottom: 20px;
  justify-content: center;
}

.app-nav button {
  background: none;
  color: var(--text-color);
  font-size: 1rem;
  padding: 8px 12px;
}

.app-nav button:hover, .app-nav button.active {
  background-color: var(--primary-color);
  color: white;
}

.app-main {
  flex-grow: 1;
  padding: 20px 0;
}

.app-footer {
  text-align: center;
  padding: 20px 0;
  color: var(--text-color-dark);
  font-size: 0.9rem;
  border-top: 1px solid var(--surface-color);
  margin-top: auto;
}

/* -------------------- */
/* --- View Specific Styles --- */
/* -------------------- */
.view-container {
  background-color: var(--surface-color);
  padding: 20px;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  text-align: center;
}

.view-container h2 {
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 15px;
  text-align: left;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: var(--text-color-dark);
}

/* -------------------- */
/* --- Game Elements --- */
/* -------------------- */
.choices-container {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin: 20px 0;
  flex-wrap: wrap; /* Allow choices to wrap on smaller screens */
}

.choice-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 120px;
  border: 3px solid var(--choice-bg-color);
  border-radius: var(--border-radius);
  background-color: var(--choice-bg-color);
  color: var(--text-color);
  font-size: 1.2rem;
  font-weight: bold;
  cursor: pointer;
  transition: transform 0.2s ease, border-color 0.2s ease, box-shadow 0.2s ease;
  box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.choice-button:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 10px rgba(0,0,0,0.3);
}

.choice-button.selected {
  border-color: var(--accent-color);
  box-shadow: 0 0 15px var(--accent-color);
  animation: pulse 1s infinite alternate;
}

.choice-button .icon {
  font-size: 3rem; /* Placeholder for actual icons or images */
  margin-bottom: 10px;
}

.choice-button.rock { border-color: var(--rock-color); }
.choice-button.rock:hover, .choice-button.rock.selected { border-color: var(--rock-color); box-shadow: 0 0 15px var(--rock-color); }
.choice-button.rock .icon { color: var(--rock-color); }

.choice-button.paper { border-color: var(--paper-color); }
.choice-button.paper:hover, .choice-button.paper.selected { border-color: var(--paper-color); box-shadow: 0 0 15px var(--paper-color); }
.choice-button.paper .icon { color: var(--paper-color); }

.choice-button.scissors { border-color: var(--scissors-color); }
.choice-button.scissors:hover, .choice-button.scissors.selected { border-color: var(--scissors-color); box-shadow: 0 0 15px var(--scissors-color); }
.choice-button.scissors .icon { color: var(--scissors-color); }

/* Animation for selected choice */
@keyframes pulse {
  0% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1.1);
  }
}

/* Game state visualization */
.game-status {
  margin: 20px 0;
  padding: 15px;
  border-radius: var(--border-radius);
  font-size: 1.1rem;
  font-weight: bold;
}

.game-status.waiting { background-color: rgba(245, 166, 35, 0.2); border: 1px solid var(--accent-color); color: var(--accent-color); }
.game-status.commit { background-color: rgba(74, 144, 226, 0.2); border: 1px solid var(--primary-color); color: var(--primary-color); }
.game-status.reveal { background-color: rgba(80, 227, 194, 0.2); border: 1px solid var(--secondary-color); color: var(--secondary-color); }
.game-status.finished { background-color: rgba(126, 211, 33, 0.2); border: 1px solid var(--success-color); color: var(--success-color); }
.game-status.error { background-color: rgba(231, 76, 60, 0.2); border: 1px solid var(--error-color); color: var(--error-color); }


.game-info, .player-info {
  margin-bottom: 15px;
  padding: 10px;
  background-color: var(--choice-bg-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--surface-color);
}

.game-info p, .player-info p {
  margin: 5px 0;
}

.game-results h3 {
  color: var(--accent-color);
  font-size: 1.5rem;
}

.player-cards-container {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  gap: 20px;
  margin-top: 20px;
}

.player-card {
  background-color: var(--choice-bg-color);
  padding: 15px;
  border-radius: var(--border-radius);
  width: clamp(200px, 30%, 250px); /* Responsive width */
  box-shadow: var(--box-shadow);
  border: 2px solid transparent;
}

.player-card.current-player {
  border-color: var(--primary-color);
}

.player-card h4 {
  color: var(--secondary-color);
  margin-bottom: 10px;
  font-size: 1.1rem;
  word-break: break-all; /* For long pubkeys */
}

.player-choice-display {
  font-size: 2.5rem;
  margin: 10px 0;
  height: 50px; /* Ensure consistent height */
  line-height: 50px;
}

.player-choice-display.rock { color: var(--rock-color); }
.player-choice-display.paper { color: var(--paper-color); }
.player-choice-display.scissors { color: var(--scissors-color); }
.player-choice-display.hidden { color: var(--text-color-dark); }


/* -------------------- */
/* --- Wallet Button & Modal --- */
/* -------------------- */
.wallet-adapter-button {
  background-color: var(--primary-color) !important;
  color: white !important;
  border-radius: var(--border-radius) !important;
  font-size: 1rem !important;
  padding: 10px 15px !important;
}
.wallet-adapter-button:hover {
  background-color: #3a80d2 !important;
}

.wallet-adapter-modal-overlay {
  background: rgba(0, 0, 0, 0.7) !important;
}

.wallet-adapter-modal-container {
  background: var(--surface-color) !important;
  box-shadow: var(--box-shadow) !important;
  border-radius: var(--border-radius) !important;
}

.wallet-adapter-modal-title {
  color: var(--secondary-color) !important;
  font-family: var(--font-family-headings) !important;
}

.wallet-adapter-modal-list .wallet-adapter-button {
  background-color: var(--choice-bg-color) !important;
  color: var(--text-color) !important;
}
.wallet-adapter-modal-list .wallet-adapter-button:hover {
  background-color: var(--primary-color) !important;
  color: white !important;
}

.wallet-adapter-modal-list .wallet-adapter-button-trigger {
    background: var(--primary-color) !important;
}

/* -------------------- */
/* --- Token Display & Modal --- */
/* -------------------- */
.token-display {
  background-color: var(--choice-bg-color);
  padding: 8px 15px;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: background-color var(--transition-speed);
}
.token-display:hover {
  background-color: var(--primary-color);
}
.token-display span {
  margin: 0 5px;
}

.token-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
}

.token-modal {
  background-color: var(--surface-color);
  padding: 25px;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  width: 90%;
  max-width: 400px;
  text-align: center;
}

.token-modal h3 {
  margin-top: 0;
  color: var(--secondary-color);
}

/* -------------------- */
/* --- Utility Classes --- */
/* -------------------- */
.error-message {
  color: var(--error-color);
  background-color: rgba(231, 76, 60, 0.1);
  padding: 10px;
  border-radius: var(--border-radius);
  margin-bottom: 15px;
  border: 1px solid var(--error-color);
}

.status-message {
  color: var(--primary-color);
  background-color: rgba(74, 144, 226, 0.1);
  padding: 10px;
  border-radius: var(--border-radius);
  margin-bottom: 15px;
  border: 1px solid var(--primary-color);
}

.loading-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  font-size: 1.2rem;
  color: var(--secondary-color);
}

.loading-indicator::after {
  content: '';
  width: 20px;
  height: 20px;
  border: 3px solid var(--secondary-color);
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: 10px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* -------------------- */
/* --- Responsive Design (Media Queries) --- */
/* -------------------- */

/* Medium screens (tablets) */
@media (max-width: 1024px) {
  .app-container {
    padding: 0 10px;
  }
  .choice-button {
    width: 100px;
    height: 100px;
    font-size: 1rem;
  }
  .choice-button .icon {
    font-size: 2.5rem;
  }
}


/* Small screens (mobile) */
@media (max-width: 768px) {
  body {
    font-size: 15px;
  }

  .app-header {
    padding: 10px 0;
    flex-wrap: wrap; /* Allow logo and actions to wrap if needed */
  }

  .app-header .logo {
    font-size: 1.5rem;
  }

  .header-actions {
    gap: 10px;
  }

  /* Show mobile menu toggle and hide desktop nav */
  .mobile-menu-toggle {
    display: block;
  }

  .app-nav {
    display: none; /* Hide desktop nav */
    flex-direction: column;
    position: absolute;
    top: 60px; /* Adjust based on header height */
    left: 0;
    right: 0;
    background-color: var(--surface-color);
    box-shadow: var(--box-shadow);
    z-index: 999;
    border-radius: 0;
    padding: 0;
  }

  .app-nav.open {
    display: flex; /* Show mobile nav when open */
  }

  .app-nav button {
    width: 100%;
    text-align: left;
    padding: 15px;
    border-bottom: 1px solid var(--background-color);
    border-radius: 0;
  }
  .app-nav button:last-child {
    border-bottom: none;
  }

  .choices-container {
    gap: 10px;
  }

  .choice-button {
    width: 90px;
    height: 90px;
    font-size: 0.9rem;
  }
  .choice-button .icon {
    font-size: 2rem;
  }

  .view-container {
    padding: 15px;
  }

  button {
    padding: 10px 15px;
    font-size: 0.95rem;
  }

  input, select, textarea {
    padding: 8px;
    font-size: 0.95rem;
  }

  .player-card {
    width: 100%; /* Full width on mobile */
    margin-bottom: 10px;
  }

  .token-modal {
    width: 95%;
  }
}

@media (max-width: 480px) {
  .app-header .logo {
    font-size: 1.3rem;
  }
  .header-actions .wallet-adapter-button {
    font-size: 0.85rem !important;
    padding: 8px 10px !important;
  }
  .token-display {
    padding: 6px 10px;
    font-size: 0.85rem;
  }
  .choice-button {
    width: 80px;
    height: 80px;
  }
  .choice-button .icon {
    font-size: 1.8rem;
  }
}
