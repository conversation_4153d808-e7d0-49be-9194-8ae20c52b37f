import React from 'react';
import ReactDOM from 'react-dom/client';

// Test each import one by one to find the problematic one
console.log('Step 1: Basic React imports loaded');

// Let's try importing CSS first
import './index.css';
console.log('Step 2: index.css loaded');

// Try polyfills
import './utils/polyfill-setup.js';
console.log('Step 3: polyfills loaded');

// Try animations CSS
import './styles/animations.css';
console.log('Step 4: animations.css loaded');

// Try ErrorBoundary
import ErrorBoundary from './components/ErrorBoundary';
console.log('Step 5: ErrorBoundary loaded');

// Finally try the main App
import App from './App.tsx';
console.log('Step 6: App component loaded');

const rootElement = document.getElementById('root');
if (!rootElement) {
  throw new Error('Failed to find root element');
}

// Log that main.tsx is loaded to verify the order
console.log('Main.tsx loaded - React application starting');

ReactDOM.createRoot(rootElement).render(
  <React.StrictMode>
    <ErrorBoundary>
      <App />
    </ErrorBoundary>
  </React.StrictMode>,
);
