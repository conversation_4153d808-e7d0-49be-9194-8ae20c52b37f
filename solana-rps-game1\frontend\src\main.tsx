import React from 'react';
import ReactDOM from 'react-dom/client';

// Simple test component to debug black screen
const TestApp = () => {
  console.log('TestApp rendering...');
  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: '#1a1a2e',
      color: 'white',
      padding: '20px',
      fontFamily: 'Arial, sans-serif'
    }}>
      <h1>🎮 Solana RPS Game - Debug Mode</h1>
      <p>If you can see this, React is working!</p>
      <p>Timestamp: {new Date().toLocaleString()}</p>
      <div style={{ marginTop: '20px', padding: '10px', backgroundColor: '#2a2a4a', borderRadius: '8px' }}>
        <h3>Debug Info:</h3>
        <ul>
          <li>✅ React is rendering</li>
          <li>✅ CSS is loading</li>
          <li>✅ JavaScript is working</li>
        </ul>
      </div>
    </div>
  );
};

const rootElement = document.getElementById('root');
if (!rootElement) {
  throw new Error('Failed to find root element');
}

// Log that main.tsx is loaded to verify the order
console.log('Main.tsx loaded - React application starting');

ReactDOM.createRoot(rootElement).render(
  <TestApp />
);
