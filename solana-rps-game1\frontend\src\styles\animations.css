/* Game animation styles */

/* Particle animation for game effects */
@keyframes particle-animation {
  0% {
    transform: translate(0, 0) rotate(0deg) scale(0);
    opacity: 1;
  }
  100% {
    transform: translate(var(--tx, 0px), var(--ty, 0px)) rotate(var(--rotate, 0deg)) scale(var(--scale, 1));
    opacity: 0;
  }
}

/* Floating animations for background elements */
@keyframes float {
  0% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-20px) rotate(5deg);
  }
  66% {
    transform: translateY(10px) rotate(-5deg);
  }
  100% {
    transform: translateY(0px) rotate(0deg);
  }
}

@keyframes float-reverse {
  0% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(20px) rotate(-5deg);
  }
  66% {
    transform: translateY(-10px) rotate(5deg);
  }
  100% {
    transform: translateY(0px) rotate(0deg);
  }
}

.animate-float {
  animation: float 15s ease-in-out infinite;
}

.animate-float-delayed {
  animation: float 18s ease-in-out infinite;
  animation-delay: 2s;
}

.animate-float-reverse {
  animation: float-reverse 20s ease-in-out infinite;
}

/* Enhanced shake animation */
@keyframes shake {
  0%, 100% {
    transform: rotate(0deg);
  }
  10% {
    transform: rotate(-10deg);
  }
  20% {
    transform: rotate(8deg);
  }
  30% {
    transform: rotate(-6deg);
  }
  40% {
    transform: rotate(4deg);
  }
  50% {
    transform: rotate(-2deg);
  }
  60% {
    transform: rotate(0deg);
  }
}

.animate-shake {
  animation: shake 0.4s ease-in-out;
}

/* Pulsing animation */
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 8px rgba(147, 51, 234, 0.2);
  }
  50% {
    transform: scale(1.08);
    box-shadow: 0 0 20px rgba(147, 51, 234, 0.4);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 8px rgba(147, 51, 234, 0.2);
  }
}

.animate-pulse-custom {
  animation: pulse 2s infinite;
}

/* Loading animation */
@keyframes loadPulse {
  0%, 100% {
    opacity: 0.5;
    transform: scale(0.95);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-loading {
  animation: loadPulse 1.5s infinite;
}

/* Confetti animation */
@keyframes confetti-rain {
  0% {
    background-position: 0 0;
    opacity: 1;
  }
  100% {
    background-position: 0 600px;
    opacity: 0;
  }
}

.confetti-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  background-image: url("data:image/svg+xml,%3Csvg width='600' height='90' viewBox='0 0 600 90' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect x='42' y='3' width='10' height='10' rx='5' fill='%23FF0066'/%3E%3Crect x='84' y='23' width='10' height='10' rx='5' fill='%234BC0C0'/%3E%3Crect x='118' y='37' width='10' height='10' rx='5' fill='%23FFCE56'/%3E%3Crect x='167' y='20' width='10' height='10' rx='5' fill='%2336A2EB'/%3E%3Crect x='219' y='13' width='10' height='10' rx='5' fill='%2375D701'/%3E%3Crect x='277' y='11' width='10' height='10' rx='5' fill='%239966FF'/%3E%3Crect x='328' y='26' width='10' height='10' rx='5' fill='%23FF9F40'/%3E%3Crect x='382' y='17' width='10' height='10' rx='5' fill='%238A2BE2'/%3E%3Crect x='435' y='32' width='10' height='10' rx='5' fill='%2373E0E0'/%3E%3Crect x='492' y='13' width='10' height='10' rx='5' fill='%23FF6384'/%3E%3Crect x='545' y='22' width='10' height='10' rx='5' fill='%2300CED1'/%3E%3C/svg%3E");
  background-repeat: repeat;
  animation: confetti-rain 7s linear forwards;
  z-index: 9999;
}

/* Glow effects */
.drop-shadow-glow-green {
  filter: drop-shadow(0 0 8px rgba(74, 222, 128, 0.8));
}

.drop-shadow-glow-red {
  filter: drop-shadow(0 0 8px rgba(248, 113, 113, 0.8));
}

.drop-shadow-glow-blue {
  filter: drop-shadow(0 0 8px rgba(96, 165, 250, 0.8));
}

/* Particle styles */
.particle {
  position: absolute;
  border-radius: 9999px;
  transform-origin: center;
  opacity: 0;
}
/* Particle animations */
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(10deg); }
}

@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 5px rgba(59, 130, 246, 0.5); }
  50% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.8); }
}

.particle {
  position: absolute;
  pointer-events: none;
  animation: float 3s ease-in-out infinite;
}

.glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Game choice animations */
.choice-button {
  transition: all 0.3s ease;
}

.choice-button:hover {
  transform: scale(1.1);
}

.choice-button:active {
  transform: scale(0.95);
}
