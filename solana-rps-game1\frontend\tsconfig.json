{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "allowJs": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": false, "noImplicitAny": false, "strictNullChecks": false, "noImplicitThis": false, "checkJs": false, "esModuleInterop": true, "baseUrl": "."}, "include": ["src"], "references": [{"path": "./tsconfig.node.json"}]}