# Solana RPS Game - Testing Script
# This script runs comprehensive tests and generates reports

Write-Host "🧪 Running Solana RPS Game tests..." -ForegroundColor Green

# Function to check if a command exists
function Test-Command($command) {
    try {
        Get-Command $command -ErrorAction Stop
        return $true
    } catch {
        return $false
    }
}

# Verify required tools
if (-not (Test-Command "node")) {
    Write-Host "❌ Node.js not found. Please run setup-windows.ps1 first." -ForegroundColor Red
    exit 1
}

# Check if test validator is running
Write-Host "🔍 Checking if Solana test validator is running..." -ForegroundColor Blue
try {
    $validatorCheck = solana cluster-version 2>&1
    if ($validatorCheck -like "*Connection refused*" -or $validatorCheck -like "*error*") {
        Write-Host "⚠️  Solana test validator not detected. Starting local validator..." -ForegroundColor Yellow
        Write-Host "💡 In a separate terminal, run: solana-test-validator" -ForegroundColor Cyan
        Write-Host "Press any key when the validator is running..." -ForegroundColor Yellow
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    } else {
        Write-Host "✅ Solana validator is running" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️  Could not check validator status" -ForegroundColor Yellow
}

# 1. Generate test wallets
Write-Host "🔑 Setting up test wallets..." -ForegroundColor Blue
Set-Location "testing"

try {
    npm run generate-wallets
    Write-Host "✅ Test wallets generated" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Test wallet generation had issues, continuing..." -ForegroundColor Yellow
}

# 2. Fund test wallets (if using local validator)
Write-Host "💰 Funding test wallets..." -ForegroundColor Blue
try {
    npm run fund-wallets
    Write-Host "✅ Test wallets funded" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Test wallet funding had issues, continuing..." -ForegroundColor Yellow
}

# 3. Run basic tests
Write-Host "🧪 Running basic functionality tests..." -ForegroundColor Blue
try {
    npm run test-basic
    Write-Host "✅ Basic tests completed" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Basic tests had issues: $_" -ForegroundColor Yellow
}

# 4. Run mock tests (these don't require blockchain)
Write-Host "🎭 Running mock tests..." -ForegroundColor Blue
try {
    npm run run-all-mock-tests
    Write-Host "✅ Mock tests completed" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Mock tests had issues: $_" -ForegroundColor Yellow
}

# 5. Run security tests
Write-Host "🔒 Running security tests..." -ForegroundColor Blue
try {
    npm run test-mock-security
    Write-Host "✅ Security tests completed" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Security tests had issues: $_" -ForegroundColor Yellow
}

# 6. Run performance tests
Write-Host "⚡ Running performance tests..." -ForegroundColor Blue
try {
    npm run test-performance
    Write-Host "✅ Performance tests completed" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Performance tests had issues: $_" -ForegroundColor Yellow
}

# 7. Generate test dashboard
Write-Host "📊 Generating test dashboard..." -ForegroundColor Blue
try {
    npm run generate-dashboard
    Write-Host "✅ Test dashboard generated" -ForegroundColor Green
    
    $dashboardPath = "dashboard\index.html"
    if (Test-Path $dashboardPath) {
        $fullPath = Resolve-Path $dashboardPath
        Write-Host "📋 Dashboard available at: file://$fullPath" -ForegroundColor Cyan
    }
} catch {
    Write-Host "⚠️  Dashboard generation had issues: $_" -ForegroundColor Yellow
}

Set-Location ".."

# 8. Run frontend build test
Write-Host "🏗️  Testing frontend build..." -ForegroundColor Blue
Set-Location "frontend"
try {
    npm run build
    Write-Host "✅ Frontend builds successfully" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Frontend build had issues: $_" -ForegroundColor Yellow
}
Set-Location ".."

Write-Host ""
Write-Host "🎉 Testing complete!" -ForegroundColor Green
Write-Host ""
Write-Host "📊 Test Results:" -ForegroundColor Yellow
Write-Host "- Basic functionality tests: Check terminal output above" -ForegroundColor White
Write-Host "- Security tests: Check for vulnerabilities" -ForegroundColor White
Write-Host "- Performance tests: Check response times" -ForegroundColor White
Write-Host "- Mock tests: Simulate game scenarios" -ForegroundColor White
Write-Host ""
Write-Host "📋 Next steps:" -ForegroundColor Yellow
Write-Host "1. Review test dashboard: testing\dashboard\index.html" -ForegroundColor White
Write-Host "2. Start the game: npm run dev" -ForegroundColor White
Write-Host "3. Open browser to: http://localhost:5173" -ForegroundColor White
Write-Host ""
Write-Host "🔍 For detailed logs, check the testing\results\ directory" -ForegroundColor Cyan
