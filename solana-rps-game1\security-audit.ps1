# Solana RPS Game - Security Audit Script
# This script performs security checks on the codebase

Write-Host "🔒 Running Security Audit for Solana RPS Game..." -ForegroundColor Green

$auditResults = @()
$criticalIssues = 0
$warningIssues = 0
$infoIssues = 0

# Function to add audit result
function Add-AuditResult($category, $severity, $description, $recommendation) {
    $script:auditResults += [PSCustomObject]@{
        Category = $category
        Severity = $severity
        Description = $description
        Recommendation = $recommendation
    }
    
    switch ($severity) {
        "CRITICAL" { $script:criticalIssues++ }
        "WARNING" { $script:warningIssues++ }
        "INFO" { $script:infoIssues++ }
    }
}

# 1. Check Solana Program Security
Write-Host "🔍 Checking Solana Program Security..." -ForegroundColor Blue

$programFile = "backend\solana-program\src\lib.rs"
if (Test-Path $programFile) {
    $programContent = Get-Content $programFile -Raw
    
    # Check for hardcoded keys
    if ($programContent -match "FeeCoLLeCToRyouNEEDtoUPDATEthiswithREALaccount111") {
        Add-AuditResult "Smart Contract" "WARNING" "Hardcoded fee collector address found" "Update FEE_COLLECTOR constant with real address"
    }
    
    # Check for proper access controls
    if ($programContent -match "is_signer") {
        Add-AuditResult "Smart Contract" "INFO" "Signer verification found" "Good: Proper access control implemented"
    } else {
        Add-AuditResult "Smart Contract" "CRITICAL" "Missing signer verification" "Add is_signer checks to all instructions"
    }
    
    # Check for overflow protection
    if ($programContent -match "saturating_") {
        Add-AuditResult "Smart Contract" "INFO" "Overflow protection found" "Good: Using saturating arithmetic"
    } else {
        Add-AuditResult "Smart Contract" "WARNING" "Limited overflow protection" "Consider using saturating arithmetic more extensively"
    }
    
    # Check for constant-time comparison
    if ($programContent -match "diff \|=") {
        Add-AuditResult "Smart Contract" "INFO" "Constant-time comparison found" "Good: Timing attack protection implemented"
    } else {
        Add-AuditResult "Smart Contract" "WARNING" "No constant-time comparison" "Implement constant-time comparison for sensitive operations"
    }
    
    # Check for proper error handling
    if ($programContent -match "RPSError::") {
        Add-AuditResult "Smart Contract" "INFO" "Custom error types found" "Good: Proper error handling implemented"
    }
    
} else {
    Add-AuditResult "Smart Contract" "CRITICAL" "Solana program source not found" "Ensure backend/solana-program/src/lib.rs exists"
}

# 2. Check Frontend Security
Write-Host "🔍 Checking Frontend Security..." -ForegroundColor Blue

$frontendFiles = @(
    "frontend\src\App.tsx",
    "frontend\src\rps-client.ts",
    "frontend\src\types.ts"
)

foreach ($file in $frontendFiles) {
    if (Test-Path $file) {
        $content = Get-Content $file -Raw
        
        # Check for hardcoded private keys
        if ($content -match "private.*key|secret.*key|keypair.*\[") {
            Add-AuditResult "Frontend" "CRITICAL" "Potential hardcoded private key in $file" "Remove any hardcoded private keys"
        }
        
        # Check for proper error boundaries
        if ($content -match "ErrorBoundary") {
            Add-AuditResult "Frontend" "INFO" "Error boundary found in $file" "Good: Error handling implemented"
        }
        
        # Check for input validation
        if ($content -match "validate|check|verify") {
            Add-AuditResult "Frontend" "INFO" "Input validation found in $file" "Good: Input validation implemented"
        }
    }
}

# Check for environment variable exposure
$envFiles = @(".env", "frontend\.env")
foreach ($envFile in $envFiles) {
    if (Test-Path $envFile) {
        $envContent = Get-Content $envFile -Raw
        
        # Check for sensitive data in env files
        if ($envContent -match "private.*key|secret|password") {
            Add-AuditResult "Configuration" "CRITICAL" "Sensitive data in $envFile" "Remove sensitive data from environment files"
        }
        
        # Check for proper RPC endpoint
        if ($envContent -match "localhost|127\.0\.0\.1") {
            Add-AuditResult "Configuration" "INFO" "Local RPC endpoint in $envFile" "Consider using devnet/mainnet for production"
        }
    }
}

# 3. Check Dependencies
Write-Host "🔍 Checking Dependencies..." -ForegroundColor Blue

$packageFiles = @(
    "package.json",
    "frontend\package.json",
    "testing\package.json"
)

foreach ($packageFile in $packageFiles) {
    if (Test-Path $packageFile) {
        $packageContent = Get-Content $packageFile -Raw | ConvertFrom-Json
        
        # Check for outdated Solana dependencies
        if ($packageContent.dependencies."@solana/web3.js") {
            $version = $packageContent.dependencies."@solana/web3.js"
            if ($version -match "^1\.[0-7]") {
                Add-AuditResult "Dependencies" "WARNING" "Outdated @solana/web3.js in $packageFile" "Update to latest version for security fixes"
            }
        }
        
        # Check for development dependencies in production
        if ($packageContent.dependencies -and $packageContent.dependencies.typescript) {
            Add-AuditResult "Dependencies" "WARNING" "TypeScript in production dependencies in $packageFile" "Move TypeScript to devDependencies"
        }
    }
}

# 4. Check File Permissions and Structure
Write-Host "🔍 Checking File Structure..." -ForegroundColor Blue

# Check for sensitive files that shouldn't be committed
$sensitiveFiles = @(
    "*.key",
    "*.pem",
    ".env.local",
    ".env.production",
    "id.json"
)

foreach ($pattern in $sensitiveFiles) {
    $files = Get-ChildItem -Recurse -Include $pattern -ErrorAction SilentlyContinue
    if ($files) {
        foreach ($file in $files) {
            Add-AuditResult "File Security" "WARNING" "Sensitive file found: $($file.FullName)" "Add to .gitignore and remove from repository"
        }
    }
}

# Check for proper .gitignore
if (Test-Path ".gitignore") {
    $gitignoreContent = Get-Content ".gitignore" -Raw
    if ($gitignoreContent -notmatch "\.env") {
        Add-AuditResult "File Security" "WARNING" ".env files not in .gitignore" "Add .env* to .gitignore"
    }
    if ($gitignoreContent -notmatch "node_modules") {
        Add-AuditResult "File Security" "WARNING" "node_modules not in .gitignore" "Add node_modules to .gitignore"
    }
} else {
    Add-AuditResult "File Security" "WARNING" "No .gitignore file found" "Create .gitignore to exclude sensitive files"
}

# 5. Check Testing Security
Write-Host "🔍 Checking Testing Security..." -ForegroundColor Blue

if (Test-Path "testing\scripts") {
    $testFiles = Get-ChildItem "testing\scripts" -Filter "*.ts" -ErrorAction SilentlyContinue
    
    if ($testFiles.Count -gt 0) {
        Add-AuditResult "Testing" "INFO" "Security test files found" "Good: Security testing implemented"
        
        # Check for specific security tests
        $securityTestContent = Get-ChildItem "testing\scripts" -Filter "*security*" | Get-Content -Raw
        if ($securityTestContent -match "timing.*attack|constant.*time") {
            Add-AuditResult "Testing" "INFO" "Timing attack tests found" "Good: Advanced security testing implemented"
        }
    } else {
        Add-AuditResult "Testing" "WARNING" "No test files found" "Implement comprehensive security tests"
    }
}

# Generate Report
Write-Host ""
Write-Host "📊 Security Audit Results" -ForegroundColor Green
Write-Host "=========================" -ForegroundColor Green
Write-Host ""

Write-Host "Summary:" -ForegroundColor Yellow
Write-Host "  Critical Issues: $criticalIssues" -ForegroundColor Red
Write-Host "  Warnings: $warningIssues" -ForegroundColor Yellow
Write-Host "  Info: $infoIssues" -ForegroundColor Green
Write-Host ""

if ($criticalIssues -gt 0) {
    Write-Host "🚨 CRITICAL ISSUES FOUND - IMMEDIATE ACTION REQUIRED" -ForegroundColor Red
    Write-Host ""
}

# Group results by category
$groupedResults = $auditResults | Group-Object Category

foreach ($group in $groupedResults) {
    Write-Host "$($group.Name):" -ForegroundColor Cyan
    foreach ($result in $group.Group) {
        $color = switch ($result.Severity) {
            "CRITICAL" { "Red" }
            "WARNING" { "Yellow" }
            "INFO" { "Green" }
        }
        Write-Host "  [$($result.Severity)] $($result.Description)" -ForegroundColor $color
        Write-Host "    → $($result.Recommendation)" -ForegroundColor Gray
    }
    Write-Host ""
}

# Save detailed report
$reportPath = "security-audit-report.txt"
$auditResults | Format-Table -AutoSize | Out-File $reportPath
Write-Host "📄 Detailed report saved to: $reportPath" -ForegroundColor Cyan

# Return exit code based on critical issues
if ($criticalIssues -gt 0) {
    Write-Host "❌ Security audit failed due to critical issues" -ForegroundColor Red
    exit 1
} elseif ($warningIssues -gt 0) {
    Write-Host "⚠️  Security audit completed with warnings" -ForegroundColor Yellow
    exit 0
} else {
    Write-Host "✅ Security audit passed" -ForegroundColor Green
    exit 0
}
