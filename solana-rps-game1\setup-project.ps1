# Solana RPS Game - Project Setup Script
# This script sets up the project dependencies and configuration

Write-Host "🎮 Setting up Solana RPS Game project..." -ForegroundColor Green

# Function to check if a command exists
function Test-Command($command) {
    try {
        Get-Command $command -ErrorAction Stop
        return $true
    } catch {
        return $false
    }
}

# Verify required tools are installed
$requiredTools = @("node", "npm", "rustc", "cargo", "solana")
$missingTools = @()

foreach ($tool in $requiredTools) {
    if (-not (Test-Command $tool)) {
        $missingTools += $tool
    }
}

if ($missingTools.Count -gt 0) {
    Write-Host "❌ Missing required tools: $($missingTools -join ', ')" -ForegroundColor Red
    Write-Host "Please run setup-windows.ps1 first or install these tools manually." -ForegroundColor Yellow
    exit 1
}

Write-Host "✅ All required tools are available" -ForegroundColor Green

# 1. Set up Solana configuration
Write-Host "🔧 Configuring Solana..." -ForegroundColor Blue
solana config set --url https://api.devnet.solana.com
solana config set --keypair "$env:USERPROFILE\.config\solana\id.json"

# Generate a new keypair if it doesn't exist
if (-not (Test-Path "$env:USERPROFILE\.config\solana\id.json")) {
    Write-Host "🔑 Generating new Solana keypair..." -ForegroundColor Blue
    solana-keygen new --no-bip39-passphrase --silent --outfile "$env:USERPROFILE\.config\solana\id.json"
}

# 2. Create environment files
Write-Host "📝 Setting up environment configuration..." -ForegroundColor Blue

# Create root .env file
$rootEnvContent = @"
# Solana RPS Game Environment Configuration
VITE_RPC_ENDPOINT=https://api.devnet.solana.com
VITE_RPS_PROGRAM_ID=7Y9dRMY6V9cmVkXNFrHeUZmYf2tAV5wSVFcYyD5bLQpZ
VITE_RPS_TOKEN_MINT=
VITE_FEE_COLLECTOR_ACCOUNT=FeeKHhL1CcJCyd82xextWTbBT5jGzVQwXVQKNjHV8SDD
"@

Set-Content -Path ".env" -Value $rootEnvContent
Write-Host "✅ Created root .env file" -ForegroundColor Green

# Ensure frontend .env exists
if (-not (Test-Path "frontend\.env")) {
    Copy-Item ".env" "frontend\.env"
    Write-Host "✅ Created frontend .env file" -ForegroundColor Green
}

# 3. Install root dependencies
Write-Host "📦 Installing root dependencies..." -ForegroundColor Blue
try {
    npm install
    Write-Host "✅ Root dependencies installed" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Root dependency installation had issues, continuing..." -ForegroundColor Yellow
}

# 4. Install frontend dependencies
Write-Host "📦 Installing frontend dependencies..." -ForegroundColor Blue
Set-Location "frontend"
try {
    npm install --legacy-peer-deps
    Write-Host "✅ Frontend dependencies installed" -ForegroundColor Green
} catch {
    Write-Host "❌ Frontend dependency installation failed: $_" -ForegroundColor Red
}
Set-Location ".."

# 5. Install testing dependencies
Write-Host "📦 Installing testing dependencies..." -ForegroundColor Blue
Set-Location "testing"
try {
    npm install
    Write-Host "✅ Testing dependencies installed" -ForegroundColor Green
} catch {
    Write-Host "❌ Testing dependency installation failed: $_" -ForegroundColor Red
}
Set-Location ".."

# 6. Install backend monitoring dependencies
Write-Host "📦 Installing backend monitoring dependencies..." -ForegroundColor Blue
Set-Location "backend\monitoring"
try {
    npm install
    Write-Host "✅ Backend monitoring dependencies installed" -ForegroundColor Green
} catch {
    Write-Host "❌ Backend monitoring dependency installation failed: $_" -ForegroundColor Red
}
Set-Location "..\..\"

# 7. Build the Solana program
Write-Host "🔨 Building Solana program..." -ForegroundColor Blue
Set-Location "backend\solana-program"
try {
    # Install Anchor dependencies first
    if (Test-Path "package.json") {
        npm install
    }
    
    # Build with Anchor
    if (Test-Command "anchor") {
        anchor build
        Write-Host "✅ Solana program built with Anchor" -ForegroundColor Green
    } else {
        # Fallback to cargo build-bpf
        cargo build-bpf
        Write-Host "✅ Solana program built with cargo" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️  Solana program build had issues: $_" -ForegroundColor Yellow
    Write-Host "This is normal if Anchor is not fully configured yet." -ForegroundColor Yellow
}
Set-Location "..\..\"

Write-Host ""
Write-Host "🎉 Project setup complete!" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Start local Solana test validator: solana-test-validator" -ForegroundColor White
Write-Host "2. Deploy the program: .\deploy-program.ps1" -ForegroundColor White
Write-Host "3. Start the frontend: npm run dev" -ForegroundColor White
Write-Host "4. Run tests: .\run-tests.ps1" -ForegroundColor White
Write-Host ""
Write-Host "🌐 The game will be available at: http://localhost:5173" -ForegroundColor Cyan
