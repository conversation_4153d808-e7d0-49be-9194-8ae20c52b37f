@echo off
REM Solana RPS Game - Wallet Setup Script
REM This script helps set up Solana wallets for the game

echo.
echo ========================================
echo   Solana Wallet Setup Guide
echo ========================================
echo.

echo [INFO] This script will help you set up wallets for the Solana RPS game.
echo.

REM Check if Solana CLI is installed
where solana >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Solana CLI not found. Please run setup.bat first.
    pause
    exit /b 1
)

echo [STEP 1] Setting up Solana CLI wallet...
echo.

REM Check if keypair already exists
if exist "%USERPROFILE%\.config\solana\id.json" (
    echo [INFO] Solana keypair already exists
    echo [INFO] Wallet address:
    solana address
    echo.
    
    choice /C YN /M "Do you want to create a new keypair (Y) or keep existing (N)"
    if errorlevel 2 goto check_balance
    if errorlevel 1 goto create_keypair
) else (
    echo [INFO] No Solana keypair found. Creating new one...
    goto create_keypair
)

:create_keypair
echo [INFO] Creating new Solana keypair...
echo [WARNING] This will overwrite any existing keypair!
echo.
pause

REM Create new keypair
solana-keygen new --no-bip39-passphrase --outfile "%USERPROFILE%\.config\solana\id.json"
if %errorLevel% neq 0 (
    echo [ERROR] Failed to create keypair
    pause
    exit /b 1
)

echo [SUCCESS] New keypair created!
echo [INFO] Wallet address:
solana address
echo.

:check_balance
REM Configure for devnet
echo [INFO] Configuring Solana for devnet...
solana config set --url https://api.devnet.solana.com
if %errorLevel% neq 0 (
    echo [ERROR] Failed to configure Solana
    pause
    exit /b 1
)

echo [INFO] Current configuration:
solana config get
echo.

REM Check balance
echo [INFO] Checking wallet balance...
for /f "tokens=1" %%i in ('solana balance --lamports 2^>nul') do set balance=%%i
if "%balance%"=="" set balance=0

set /a sol_balance=%balance%/1000000000
echo [INFO] Current balance: %sol_balance% SOL

if %sol_balance% lss 2 (
    echo [INFO] Balance is low. Requesting airdrop...
    solana airdrop 2
    if %errorLevel% neq 0 (
        echo [WARNING] Airdrop failed. You may need to:
        echo   1. Wait a few minutes and try again
        echo   2. Use the Solana faucet: https://faucet.solana.com/
        echo   3. Request airdrop manually: solana airdrop 2
    ) else (
        echo [SUCCESS] Airdrop completed!
        timeout /t 3 /nobreak >nul
        echo [INFO] New balance:
        solana balance
    )
) else (
    echo [SUCCESS] Wallet has sufficient balance for testing
)
echo.

echo [STEP 2] Browser Wallet Setup...
echo.

echo [INFO] For the best experience, install a browser wallet:
echo.
echo 1. PHANTOM WALLET (Recommended)
echo    - Visit: https://phantom.app/
echo    - Install browser extension
echo    - Create new wallet or import existing
echo    - Switch to Devnet in settings
echo.
echo 2. SOLFLARE WALLET
echo    - Visit: https://solflare.com/
echo    - Install browser extension
echo    - Create new wallet or import existing
echo    - Switch to Devnet in settings
echo.
echo 3. BACKPACK WALLET
echo    - Visit: https://backpack.app/
echo    - Install browser extension
echo    - Create new wallet or import existing
echo    - Switch to Devnet in settings
echo.

choice /C YN /M "Have you installed a browser wallet"
if errorlevel 2 goto wallet_instructions
if errorlevel 1 goto test_connection

:wallet_instructions
echo.
echo [ACTION REQUIRED] Please install a browser wallet:
echo.
echo 1. Open your browser (Chrome, Firefox, Edge)
echo 2. Go to https://phantom.app/ (recommended)
echo 3. Click "Download" and install the extension
echo 4. Create a new wallet or import existing
echo 5. In wallet settings, switch network to "Devnet"
echo 6. Get some devnet SOL from: https://faucet.solana.com/
echo.
echo After installing, run this script again or continue to test connection.
echo.
pause

:test_connection
echo [STEP 3] Testing wallet connection...
echo.

echo [INFO] When you start the game, you'll need to:
echo 1. Connect your browser wallet
echo 2. Approve the connection
echo 3. Switch to Devnet if not already
echo 4. Ensure you have some SOL for transactions
echo.

echo [INFO] Wallet addresses for reference:
echo CLI Wallet: 
solana address
echo.
echo Browser Wallet: (will be shown when you connect in the game)
echo.

echo [STEP 4] Final checks...
echo.

REM Check if game is ready
if exist "frontend\node_modules" (
    echo [PASS] Frontend dependencies installed
) else (
    echo [WARN] Frontend dependencies not installed
    echo [ACTION] Run: setup.bat
)

if exist "backend\solana-program\target\deploy\*.so" (
    echo [PASS] Solana program built
) else (
    echo [WARN] Solana program not built
    echo [ACTION] Run: deploy.bat
)

echo.
echo ========================================
echo   Wallet Setup Complete!
echo ========================================
echo.

echo [SUCCESS] Your wallets are ready for the Solana RPS game!
echo.
echo Summary:
echo - CLI Wallet: Configured for devnet with SOL
echo - Browser Wallet: Install Phantom/Solflare and switch to devnet
echo - Game: Ready to connect and play
echo.
echo Next steps:
echo 1. Ensure browser wallet is installed and on devnet
echo 2. Run: start.bat
echo 3. Open: http://localhost:5173
echo 4. Connect your browser wallet
echo 5. Start playing!
echo.

echo [TIP] If you need more devnet SOL:
echo - CLI: solana airdrop 2
echo - Browser: https://faucet.solana.com/
echo.

pause
