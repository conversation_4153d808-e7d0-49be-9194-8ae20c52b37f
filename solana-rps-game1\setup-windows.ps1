# Solana RPS Game - Windows Setup Script
# This script installs all required dependencies and sets up the development environment

Write-Host "🎮 Setting up Solana RPS Game on Windows..." -ForegroundColor Green
Write-Host "This will install Node.js, Rust, Solana CLI, and all project dependencies." -ForegroundColor Yellow

# Check if running as administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
if (-not $isAdmin) {
    Write-Host "⚠️  This script should be run as Administrator for best results." -ForegroundColor Yellow
    Write-Host "Some installations may require elevated privileges." -ForegroundColor Yellow
}

# Function to check if a command exists
function Test-Command($command) {
    try {
        Get-Command $command -ErrorAction Stop
        return $true
    } catch {
        return $false
    }
}

# Function to download and install from URL
function Install-FromUrl($name, $url, $installer) {
    Write-Host "📥 Downloading $name..." -ForegroundColor Blue
    $tempPath = "$env:TEMP\$installer"
    try {
        Invoke-WebRequest -Uri $url -OutFile $tempPath -UseBasicParsing
        Write-Host "🚀 Installing $name..." -ForegroundColor Blue
        Start-Process -FilePath $tempPath -Wait
        Remove-Item $tempPath -Force
        Write-Host "✅ $name installed successfully!" -ForegroundColor Green
    } catch {
        Write-Host "❌ Failed to install $name: $_" -ForegroundColor Red
    }
}

# 1. Install Chocolatey (Windows package manager)
if (-not (Test-Command "choco")) {
    Write-Host "📦 Installing Chocolatey package manager..." -ForegroundColor Blue
    Set-ExecutionPolicy Bypass -Scope Process -Force
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
    iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
    refreshenv
} else {
    Write-Host "✅ Chocolatey already installed" -ForegroundColor Green
}

# 2. Install Node.js
if (-not (Test-Command "node")) {
    Write-Host "📦 Installing Node.js..." -ForegroundColor Blue
    choco install nodejs -y
    refreshenv
} else {
    Write-Host "✅ Node.js already installed: $(node --version)" -ForegroundColor Green
}

# 3. Install Git (if not present)
if (-not (Test-Command "git")) {
    Write-Host "📦 Installing Git..." -ForegroundColor Blue
    choco install git -y
    refreshenv
} else {
    Write-Host "✅ Git already installed: $(git --version)" -ForegroundColor Green
}

# 4. Install Rust
if (-not (Test-Command "rustc")) {
    Write-Host "📦 Installing Rust..." -ForegroundColor Blue
    Install-FromUrl "Rust" "https://forge.rust-lang.org/infra/channel-layout.html#stable-releases" "rustup-init.exe"
    # Alternative: use chocolatey
    # choco install rust -y
    refreshenv
} else {
    Write-Host "✅ Rust already installed: $(rustc --version)" -ForegroundColor Green
}

# 5. Install Solana CLI
if (-not (Test-Command "solana")) {
    Write-Host "📦 Installing Solana CLI..." -ForegroundColor Blue
    # Download and install Solana CLI
    $solanaInstaller = "$env:TEMP\solana-install-init.exe"
    Invoke-WebRequest -Uri "https://release.solana.com/v1.18.22/solana-install-init-x86_64-pc-windows-msvc.exe" -OutFile $solanaInstaller
    Start-Process -FilePath $solanaInstaller -ArgumentList "v1.18.22" -Wait
    Remove-Item $solanaInstaller -Force
    
    # Add Solana to PATH
    $solanaPath = "$env:USERPROFILE\.local\share\solana\install\active_release\bin"
    $currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")
    if ($currentPath -notlike "*$solanaPath*") {
        [Environment]::SetEnvironmentVariable("PATH", "$currentPath;$solanaPath", "User")
    }
    refreshenv
} else {
    Write-Host "✅ Solana CLI already installed: $(solana --version)" -ForegroundColor Green
}

# 6. Install Anchor CLI (for Solana development)
if (-not (Test-Command "anchor")) {
    Write-Host "📦 Installing Anchor CLI..." -ForegroundColor Blue
    cargo install --git https://github.com/coral-xyz/anchor avm --locked --force
    avm install latest
    avm use latest
} else {
    Write-Host "✅ Anchor CLI already installed: $(anchor --version)" -ForegroundColor Green
}

Write-Host ""
Write-Host "🎉 Basic dependencies installed!" -ForegroundColor Green
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Restart your terminal/PowerShell" -ForegroundColor White
Write-Host "2. Run: cd solana-rps-game1" -ForegroundColor White
Write-Host "3. Run: .\setup-project.ps1" -ForegroundColor White
Write-Host ""
Write-Host "If you encounter any issues, please restart your computer and try again." -ForegroundColor Yellow
