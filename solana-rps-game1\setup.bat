@echo off
REM Solana RPS Game - Windows Batch Setup Script
REM This script sets up the game using Command Prompt (no PowerShell required)

echo.
echo ========================================
echo   Solana RPS Game - Windows Setup
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [INFO] Running as Administrator - Good!
) else (
    echo [WARNING] Not running as Administrator
    echo Some installations may require elevated privileges
    echo.
)

echo [STEP 1] Installing Chocolatey package manager...
echo.

REM Install Chocolatey if not present
where choco >nul 2>&1
if %errorLevel% == 0 (
    echo [INFO] Chocolatey already installed
) else (
    echo [INFO] Installing Chocolatey...
    powershell -Command "Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))"
    if %errorLevel% neq 0 (
        echo [ERROR] Failed to install Chocolatey
        echo Please install manually from https://chocolatey.org/install
        pause
        exit /b 1
    )
    REM Refresh environment
    call refreshenv
)

echo.
echo [STEP 2] Installing Node.js...
echo.

where node >nul 2>&1
if %errorLevel% == 0 (
    echo [INFO] Node.js already installed
    node --version
) else (
    echo [INFO] Installing Node.js...
    choco install nodejs -y
    if %errorLevel% neq 0 (
        echo [ERROR] Failed to install Node.js
        echo Please install manually from https://nodejs.org/
        pause
        exit /b 1
    )
    call refreshenv
)

echo.
echo [STEP 3] Installing Git...
echo.

where git >nul 2>&1
if %errorLevel% == 0 (
    echo [INFO] Git already installed
    git --version
) else (
    echo [INFO] Installing Git...
    choco install git -y
    if %errorLevel% neq 0 (
        echo [ERROR] Failed to install Git
        echo Please install manually from https://git-scm.com/
        pause
        exit /b 1
    )
    call refreshenv
)

echo.
echo [STEP 4] Installing Rust...
echo.

where rustc >nul 2>&1
if %errorLevel% == 0 (
    echo [INFO] Rust already installed
    rustc --version
) else (
    echo [INFO] Installing Rust...
    REM Download and run rustup installer
    curl -sSf https://win.rustup.rs/ -o rustup-init.exe
    if %errorLevel% neq 0 (
        echo [ERROR] Failed to download Rust installer
        pause
        exit /b 1
    )
    rustup-init.exe -y
    if %errorLevel% neq 0 (
        echo [ERROR] Failed to install Rust
        pause
        exit /b 1
    )
    del rustup-init.exe
    REM Add Rust to PATH for current session
    set PATH=%USERPROFILE%\.cargo\bin;%PATH%
)

echo.
echo [STEP 5] Installing Solana CLI...
echo.

where solana >nul 2>&1
if %errorLevel% == 0 (
    echo [INFO] Solana CLI already installed
    solana --version
) else (
    echo [INFO] Installing Solana CLI...
    REM Download Solana installer
    curl -sSfL https://release.solana.com/v1.18.22/solana-install-init-x86_64-pc-windows-msvc.exe -o solana-install.exe
    if %errorLevel% neq 0 (
        echo [ERROR] Failed to download Solana installer
        pause
        exit /b 1
    )
    solana-install.exe v1.18.22
    if %errorLevel% neq 0 (
        echo [ERROR] Failed to install Solana CLI
        pause
        exit /b 1
    )
    del solana-install.exe
    REM Add Solana to PATH for current session
    set PATH=%USERPROFILE%\.local\share\solana\install\active_release\bin;%PATH%
)

echo.
echo [STEP 6] Configuring Solana...
echo.

REM Configure Solana for devnet
solana config set --url https://api.devnet.solana.com
if %errorLevel% neq 0 (
    echo [ERROR] Failed to configure Solana
    pause
    exit /b 1
)

REM Generate keypair if it doesn't exist
if not exist "%USERPROFILE%\.config\solana\id.json" (
    echo [INFO] Generating new Solana keypair...
    solana-keygen new --no-bip39-passphrase --silent --outfile "%USERPROFILE%\.config\solana\id.json"
    if %errorLevel% neq 0 (
        echo [ERROR] Failed to generate keypair
        pause
        exit /b 1
    )
)

echo.
echo [STEP 7] Setting up project environment...
echo.

REM Create .env file
echo # Solana RPS Game Environment Configuration > .env
echo VITE_RPC_ENDPOINT=https://api.devnet.solana.com >> .env
echo VITE_RPS_PROGRAM_ID=7Y9dRMY6V9cmVkXNFrHeUZmYf2tAV5wSVFcYyD5bLQpZ >> .env
echo VITE_RPS_TOKEN_MINT= >> .env
echo VITE_FEE_COLLECTOR_ACCOUNT=FeeKHhL1CcJCyd82xextWTbBT5jGzVQwXVQKNjHV8SDD >> .env

REM Copy to frontend directory
if not exist "frontend\.env" (
    copy .env frontend\.env
)

echo.
echo [STEP 8] Installing project dependencies...
echo.

REM Install root dependencies
echo [INFO] Installing root dependencies...
call npm install
if %errorLevel% neq 0 (
    echo [WARNING] Root dependency installation had issues, continuing...
)

REM Install frontend dependencies
echo [INFO] Installing frontend dependencies...
cd frontend
call npm install --legacy-peer-deps
if %errorLevel% neq 0 (
    echo [ERROR] Failed to install frontend dependencies
    cd ..
    pause
    exit /b 1
)
cd ..

REM Install testing dependencies
echo [INFO] Installing testing dependencies...
cd testing
call npm install
if %errorLevel% neq 0 (
    echo [WARNING] Testing dependency installation had issues, continuing...
)
cd ..

REM Install backend monitoring dependencies
echo [INFO] Installing backend monitoring dependencies...
cd backend\monitoring
call npm install
if %errorLevel% neq 0 (
    echo [WARNING] Backend monitoring dependency installation had issues, continuing...
)
cd ..\..

echo.
echo [STEP 9] Building Solana program...
echo.

cd backend\solana-program
REM Try Anchor build first, fallback to cargo
where anchor >nul 2>&1
if %errorLevel% == 0 (
    echo [INFO] Building with Anchor...
    call anchor build
    if %errorLevel% neq 0 (
        echo [WARNING] Anchor build failed, trying cargo...
        call cargo build-bpf
    )
) else (
    echo [INFO] Building with cargo...
    call cargo build-bpf
    if %errorLevel% neq 0 (
        echo [WARNING] Cargo build failed, continuing...
    )
)
cd ..\..

echo.
echo ========================================
echo   Setup Complete!
echo ========================================
echo.

echo [SUCCESS] Solana RPS Game setup completed!
echo.
echo Next steps:
echo 1. Start Solana test validator: solana-test-validator
echo 2. Deploy the program: run deploy.bat
echo 3. Start the frontend: run start.bat
echo 4. Open browser to: http://localhost:5173
echo.
echo For testing: run test.bat
echo For security audit: run audit.bat
echo.

pause
