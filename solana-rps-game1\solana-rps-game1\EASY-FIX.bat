@echo off
REM Easy fix for Solana RPS Game

title Solana RPS Game - Easy Fix

echo.
echo ==========================================
echo    SOLANA RPS GAME - EASY FIX
echo ==========================================
echo.

echo Current directory: %CD%
echo.

REM Check if we're in the right directory
if not exist "frontend" (
    echo ERROR: frontend directory not found!
    echo You need to be in the solana-rps-game1 directory.
    echo.
    echo Please run this command first:
    echo cd "C:\Users\<USER>\Downloads\solana-rps-game1\solana-rps-game1"
    echo.
    pause
    exit /b 1
)

echo ✓ Found frontend directory
echo.

REM Add Node.js to PATH for this session
set "PATH=%PATH%;C:\Program Files\nodejs"

REM Test if Node.js is available
echo Testing Node.js installation...
"C:\Program Files\nodejs\node.exe" --version >nul 2>&1
if %errorLevel% == 0 (
    echo ✓ Node.js is working
    "C:\Program Files\nodejs\node.exe" --version
) else (
    echo ❌ Node.js not found at expected location
    echo.
    echo Please install Node.js from: https://nodejs.org/
    pause
    exit /b 1
)

echo.

REM Go to frontend directory
cd frontend

echo Installing dependencies...
"C:\Program Files\nodejs\npm.cmd" install --legacy-peer-deps
if %errorLevel% neq 0 (
    echo ❌ npm install failed
    echo.
    echo Try running this manually:
    echo cd frontend
    echo npm install --legacy-peer-deps
    pause
    exit /b 1
)

echo ✓ Dependencies installed
echo.

echo Starting development server...
echo.
echo ==========================================
echo   GAME WILL BE AVAILABLE AT:
echo   http://localhost:5173
echo ==========================================
echo.

REM Start the development server
"C:\Program Files\nodejs\npm.cmd" run dev

pause
