# Solana RPS Game - Start Game Script
# This script starts the complete game environment

Write-Host "🎮 Starting Solana RPS Game..." -ForegroundColor Green

# Function to check if a command exists
function Test-Command($command) {
    try {
        Get-Command $command -ErrorAction Stop
        return $true
    } catch {
        return $false
    }
}

# Function to check if a port is in use
function Test-Port($port) {
    try {
        $connection = New-Object System.Net.Sockets.TcpClient
        $connection.Connect("localhost", $port)
        $connection.Close()
        return $true
    } catch {
        return $false
    }
}

# Verify required tools
$requiredTools = @("node", "npm", "solana")
$missingTools = @()

foreach ($tool in $requiredTools) {
    if (-not (Test-Command $tool)) {
        $missingTools += $tool
    }
}

if ($missingTools.Count -gt 0) {
    Write-Host "❌ Missing required tools: $($missingTools -join ', ')" -ForegroundColor Red
    Write-Host "Please run setup-windows.ps1 first." -ForegroundColor Yellow
    exit 1
}

# Check if environment is set up
if (-not (Test-Path ".env")) {
    Write-Host "❌ Environment not configured. Please run setup-project.ps1 first." -ForegroundColor Red
    exit 1
}

# Load environment variables
Write-Host "📋 Loading environment configuration..." -ForegroundColor Blue
$envVars = Get-Content ".env" | Where-Object { $_ -match "^[^#].*=" } | ForEach-Object {
    $parts = $_ -split "=", 2
    if ($parts.Length -eq 2) {
        [Environment]::SetEnvironmentVariable($parts[0], $parts[1], "Process")
    }
}

# Check if Solana test validator is running
Write-Host "🔍 Checking Solana test validator..." -ForegroundColor Blue
$validatorRunning = $false

try {
    $clusterInfo = solana cluster-version 2>&1
    if ($clusterInfo -notlike "*Connection refused*" -and $clusterInfo -notlike "*error*") {
        $validatorRunning = $true
        Write-Host "✅ Solana validator is running" -ForegroundColor Green
    }
} catch {
    # Validator not running
}

if (-not $validatorRunning) {
    Write-Host "🚀 Starting Solana test validator..." -ForegroundColor Blue
    Write-Host "This will run in the background. Press Ctrl+C to stop the game." -ForegroundColor Yellow
    
    # Start validator in background
    Start-Process -FilePath "solana-test-validator" -WindowStyle Minimized
    
    # Wait for validator to start
    Write-Host "⏳ Waiting for validator to start..." -ForegroundColor Blue
    $timeout = 30
    $elapsed = 0
    
    while ($elapsed -lt $timeout) {
        Start-Sleep -Seconds 2
        $elapsed += 2
        
        try {
            $clusterInfo = solana cluster-version 2>&1
            if ($clusterInfo -notlike "*Connection refused*" -and $clusterInfo -notlike "*error*") {
                Write-Host "✅ Validator started successfully" -ForegroundColor Green
                $validatorRunning = $true
                break
            }
        } catch {
            # Still waiting
        }
        
        Write-Host "." -NoNewline -ForegroundColor Yellow
    }
    
    if (-not $validatorRunning) {
        Write-Host ""
        Write-Host "⚠️  Validator startup timeout. Continuing anyway..." -ForegroundColor Yellow
    }
}

# Check if program is deployed
Write-Host "🔍 Checking if program is deployed..." -ForegroundColor Blue
$programId = $env:VITE_RPS_PROGRAM_ID
if ($programId -and $programId -ne "7Y9dRMY6V9cmVkXNFrHeUZmYf2tAV5wSVFcYyD5bLQpZ") {
    try {
        $programInfo = solana account $programId 2>&1
        if ($programInfo -like "*Account not found*") {
            Write-Host "⚠️  Program not found. You may need to deploy it first." -ForegroundColor Yellow
            Write-Host "Run: .\deploy-program.ps1" -ForegroundColor Cyan
        } else {
            Write-Host "✅ Program is deployed" -ForegroundColor Green
        }
    } catch {
        Write-Host "⚠️  Could not verify program deployment" -ForegroundColor Yellow
    }
} else {
    Write-Host "⚠️  Using default program ID. Consider deploying your own." -ForegroundColor Yellow
}

# Start the frontend
Write-Host "🌐 Starting frontend development server..." -ForegroundColor Blue

# Check if port 5173 is available
if (Test-Port 5173) {
    Write-Host "⚠️  Port 5173 is already in use. The frontend may already be running." -ForegroundColor Yellow
    Write-Host "Check: http://localhost:5173" -ForegroundColor Cyan
} else {
    Set-Location "frontend"
    
    # Install dependencies if node_modules doesn't exist
    if (-not (Test-Path "node_modules")) {
        Write-Host "📦 Installing frontend dependencies..." -ForegroundColor Blue
        npm install --legacy-peer-deps
    }
    
    # Start the development server
    Write-Host "🚀 Starting Vite development server..." -ForegroundColor Blue
    Write-Host ""
    Write-Host "🌐 Game will be available at: http://localhost:5173" -ForegroundColor Cyan
    Write-Host "📱 Network access at: http://[your-ip]:5173" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Press Ctrl+C to stop the game server." -ForegroundColor Yellow
    Write-Host ""
    
    # Start the dev server (this will block)
    npm run dev
}

Write-Host ""
Write-Host "🎮 Game stopped." -ForegroundColor Yellow
