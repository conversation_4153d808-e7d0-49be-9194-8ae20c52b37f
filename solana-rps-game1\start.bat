@echo off
REM Solana RPS Game - Start Script (Command Prompt)
REM This script starts the complete game environment

echo.
echo ========================================
echo   Starting Solana RPS Game
echo ========================================
echo.

REM Check if dependencies are installed
where node >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Node.js not found. Please run setup.bat first.
    pause
    exit /b 1
)

where solana >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Solana CLI not found. Please run setup.bat first.
    pause
    exit /b 1
)

REM Check if .env file exists
if not exist ".env" (
    echo [ERROR] Environment not configured. Please run setup.bat first.
    pause
    exit /b 1
)

echo [INFO] Loading environment configuration...

REM Check if Solana test validator is running
echo [INFO] Checking Solana test validator...
solana cluster-version >nul 2>&1
if %errorLevel% neq 0 (
    echo [INFO] Starting Solana test validator...
    echo [INFO] This will run in a separate window. Keep it open while playing.
    start "Solana Test Validator" cmd /k "solana-test-validator"
    
    echo [INFO] Waiting for validator to start...
    timeout /t 10 /nobreak >nul
    
    REM Wait for validator to be ready
    :wait_validator
    solana cluster-version >nul 2>&1
    if %errorLevel% neq 0 (
        echo [INFO] Still waiting for validator...
        timeout /t 3 /nobreak >nul
        goto wait_validator
    )
    echo [SUCCESS] Validator is running!
) else (
    echo [SUCCESS] Solana validator is already running
)

REM Check wallet balance and airdrop if needed
echo [INFO] Checking wallet balance...
for /f "tokens=1" %%i in ('solana balance --lamports 2^>nul') do set balance=%%i
if "%balance%"=="" set balance=0

REM Convert lamports to SOL (divide by 1000000000)
set /a sol_balance=%balance%/1000000000

echo [INFO] Current balance: %sol_balance% SOL

if %sol_balance% lss 2 (
    echo [INFO] Requesting SOL airdrop...
    solana airdrop 2
    if %errorLevel% neq 0 (
        echo [WARNING] Airdrop failed. You may need to request SOL manually.
    ) else (
        echo [SUCCESS] Airdrop completed!
    )
)

REM Check if program is deployed
echo [INFO] Checking if program is deployed...
REM Read program ID from .env file
for /f "tokens=2 delims==" %%i in ('findstr "VITE_RPS_PROGRAM_ID" .env') do set PROGRAM_ID=%%i

if not "%PROGRAM_ID%"=="" (
    solana account %PROGRAM_ID% >nul 2>&1
    if %errorLevel% neq 0 (
        echo [WARNING] Program not found. You may need to deploy it first.
        echo [INFO] Run deploy.bat to deploy the program.
    ) else (
        echo [SUCCESS] Program is deployed
    )
) else (
    echo [WARNING] Program ID not configured
)

REM Check if port 5173 is available
netstat -an | findstr ":5173" >nul 2>&1
if %errorLevel% == 0 (
    echo [WARNING] Port 5173 is already in use. The frontend may already be running.
    echo [INFO] Check: http://localhost:5173
    pause
    exit /b 0
)

REM Start the frontend
echo [INFO] Starting frontend development server...
echo.
echo ========================================
echo   Game will be available at:
echo   http://localhost:5173
echo ========================================
echo.
echo Press Ctrl+C to stop the game server.
echo.

cd frontend

REM Check if node_modules exists
if not exist "node_modules" (
    echo [INFO] Installing frontend dependencies...
    call npm install --legacy-peer-deps
    if %errorLevel% neq 0 (
        echo [ERROR] Failed to install dependencies
        cd ..
        pause
        exit /b 1
    )
)

REM Start the development server
call npm run dev

cd ..

echo.
echo [INFO] Game stopped.
pause
