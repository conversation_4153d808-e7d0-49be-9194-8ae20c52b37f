@echo off
REM Solana RPS Game - Test Script (Command Prompt)
REM This script runs comprehensive tests

echo.
echo ========================================
echo   Running Solana RPS Game Tests
echo ========================================
echo.

REM Check if Node.js is available
where node >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Node.js not found. Please run setup.bat first.
    pause
    exit /b 1
)

REM Check if Solana test validator is running
echo [INFO] Checking if Solana test validator is running...
solana cluster-version >nul 2>&1
if %errorLevel% neq 0 (
    echo [WARNING] Solana test validator not detected.
    echo [INFO] Starting local validator for testing...
    start "Solana Test Validator" cmd /k "solana-test-validator"
    echo [INFO] Waiting for validator to start...
    timeout /t 15 /nobreak >nul
    
    REM Check again
    solana cluster-version >nul 2>&1
    if %errorLevel% neq 0 (
        echo [WARNING] Validator still not ready. Some tests may fail.
    ) else (
        echo [SUCCESS] Validator is running
    )
) else (
    echo [SUCCESS] Solana validator is running
)

REM Run tests
cd testing

echo [INFO] Setting up test wallets...
call npm run generate-wallets
if %errorLevel% neq 0 (
    echo [WARNING] Test wallet generation had issues, continuing...
)

echo [INFO] Funding test wallets...
call npm run fund-wallets
if %errorLevel% neq 0 (
    echo [WARNING] Test wallet funding had issues, continuing...
)

echo [INFO] Running basic functionality tests...
call npm run test-basic
if %errorLevel% neq 0 (
    echo [WARNING] Basic tests had issues, continuing...
)

echo [INFO] Running mock tests (these don't require blockchain)...
call npm run run-all-mock-tests
if %errorLevel% neq 0 (
    echo [WARNING] Mock tests had issues, continuing...
)

echo [INFO] Running security tests...
call npm run test-mock-security
if %errorLevel% neq 0 (
    echo [WARNING] Security tests had issues, continuing...
)

echo [INFO] Running performance tests...
call npm run test-performance
if %errorLevel% neq 0 (
    echo [WARNING] Performance tests had issues, continuing...
)

echo [INFO] Generating test dashboard...
call npm run generate-dashboard
if %errorLevel% neq 0 (
    echo [WARNING] Dashboard generation had issues, continuing...
) else (
    if exist "dashboard\index.html" (
        echo [SUCCESS] Test dashboard generated: testing\dashboard\index.html
    )
)

cd ..

REM Test frontend build
echo [INFO] Testing frontend build...
cd frontend
call npm run build >nul 2>&1
if %errorLevel% == 0 (
    echo [SUCCESS] Frontend builds successfully
) else (
    echo [WARNING] Frontend build had issues
)
cd ..

echo.
echo ========================================
echo   Testing Complete!
echo ========================================
echo.

echo [INFO] Test Results:
echo - Basic functionality tests: Check terminal output above
echo - Security tests: Check for vulnerabilities
echo - Performance tests: Check response times
echo - Mock tests: Simulate game scenarios
echo.

echo [INFO] Generated Reports:
if exist "testing\dashboard\index.html" (
    echo - Test dashboard: testing\dashboard\index.html
)
if exist "testing\results" (
    echo - Detailed logs: testing\results\ directory
)
echo.

echo [INFO] Next steps:
echo 1. Review test dashboard: testing\dashboard\index.html
echo 2. Start the game: start.bat
echo 3. Open browser to: http://localhost:5173
echo.

pause
