
  <!DOCTYPE html>
  <html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Solana RPS Game Testing Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
      .dashboard-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
      }
      .card {
        margin-bottom: 20px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
      }
      .metric-card {
        text-align: center;
        padding: 15px;
      }
      .metric-value {
        font-size: 2rem;
        font-weight: bold;
      }
      .metric-label {
        color: #666;
      }
      .test-table {
        font-size: 0.9rem;
      }
      .chart-container {
        height: 300px;
        margin-bottom: 30px;
      }
      .timestamp {
        font-size: 0.8rem;
        color: #666;
        text-align: right;
        margin-top: 15px;
      }
    </style>
  </head>
  <body>
    <div class="dashboard-container">
      <h1 class="mb-4">Solana RPS Game Testing Dashboard</h1>

      <div class="row">
        <div class="col-md-3">
          <div class="card metric-card">
            <div class="metric-value">8</div>
            <div class="metric-label">Total Tests Run</div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card metric-card">
            <div class="metric-value">6</div>
            <div class="metric-label">Tests Passed</div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card metric-card">
            <div class="metric-value">2</div>
            <div class="metric-label">Tests Failed</div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card metric-card">
            <div class="metric-value">2</div>
            <div class="metric-label">Security Vulnerabilities</div>
          </div>
        </div>
      </div>

      <div class="row mt-4">
        <div class="col-md-6">
          <div class="card">
            <div class="card-header">Pass Rate by Test Type</div>
            <div class="card-body">
              <div class="chart-container">
                <canvas id="passRateChart"></canvas>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="card">
            <div class="card-header">Tests Passed vs Failed</div>
            <div class="card-body">
              <div class="chart-container">
                <canvas id="testsChart"></canvas>
              </div>
            </div>
          </div>
        </div>
      </div>

      
      <div class="row mt-4">
        <div class="col-12">
          <div class="card">
            <div class="card-header">Security Test Results</div>
            <div class="card-body">
              <table class="table table-striped test-table">
                <thead>
                  <tr>
                    <th>Test Name</th>
                    <th>Description</th>
                    <th>Status</th>
                  </tr>
                </thead>
                <tbody>
                  
                    <tr>
                      <td>Commitment Hash Strength</td>
                      <td>Tests if the commitment hash function is strong enough</td>
                      <td><span class="badge bg-danger">FAILED</span></td>
                    </tr>
                  
                    <tr>
                      <td>Salt Randomness</td>
                      <td>Tests that generated salts have sufficient entropy</td>
                      <td><span class="badge bg-success">PASSED</span></td>
                    </tr>
                  
                    <tr>
                      <td>Frontrunning Protection</td>
                      <td>Tests protection against frontrunning attacks</td>
                      <td><span class="badge bg-success">PASSED</span></td>
                    </tr>
                  
                    <tr>
                      <td>Double Spending</td>
                      <td>Tests protection against double spending attacks</td>
                      <td><span class="badge bg-success">PASSED</span></td>
                    </tr>
                  
                    <tr>
                      <td>Timeout Manipulation</td>
                      <td>Tests protection against timeout manipulation</td>
                      <td><span class="badge bg-success">PASSED</span></td>
                    </tr>
                  
                    <tr>
                      <td>Transaction Replay</td>
                      <td>Tests protection against transaction replay attacks</td>
                      <td><span class="badge bg-success">PASSED</span></td>
                    </tr>
                  
                    <tr>
                      <td>Commitment Revelation Analysis</td>
                      <td>Tests if player choice can be inferred from commitment revelation</td>
                      <td><span class="badge bg-success">PASSED</span></td>
                    </tr>
                  
                    <tr>
                      <td>Cryptographic Timing Attack</td>
                      <td>Tests resistance to timing attacks on cryptographic operations</td>
                      <td><span class="badge bg-danger">FAILED</span></td>
                    </tr>
                  
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      

      

      <div class="row mt-4">
        <div class="col-12">
          <div class="card">
            <div class="card-header">Test Summary</div>
            <div class="card-body">
              <table class="table table-striped test-table">
                <thead>
                  <tr>
                    <th>Test Type</th>
                    <th>Tests Run</th>
                    <th>Tests Passed</th>
                    <th>Pass Rate</th>
                    <th>Last Run</th>
                  </tr>
                </thead>
                <tbody>
                  
                    <tr>
                      <td>Basic Tests</td>
                      <td>0</td>
                      <td>0</td>
                      <td>N/A%</td>
                      <td>4/6/2025, 3:49:50 PM</td>
                    </tr>
                  
                    <tr>
                      <td>Security Tests</td>
                      <td>8</td>
                      <td>6</td>
                      <td>75.0%</td>
                      <td>4/6/2025, 4:09:25 PM</td>
                    </tr>
                  
                    <tr>
                      <td>Performance Tests</td>
                      <td>0</td>
                      <td>0</td>
                      <td>N/A%</td>
                      <td>4/6/2025, 3:49:52 PM</td>
                    </tr>
                  
                    <tr>
                      <td>E2E Integration Tests</td>
                      <td>0</td>
                      <td>0</td>
                      <td>N/A%</td>
                      <td>4/6/2025, 3:49:53 PM</td>
                    </tr>
                  
                    <tr>
                      <td>UX Tests</td>
                      <td>0</td>
                      <td>0</td>
                      <td>N/A%</td>
                      <td>Invalid Date</td>
                    </tr>
                  
                    <tr>
                      <td>Fee Tests</td>
                      <td>0</td>
                      <td>0</td>
                      <td>N/A%</td>
                      <td>4/6/2025, 3:49:51 PM</td>
                    </tr>
                  
                    <tr>
                      <td>Fairness Tests</td>
                      <td>0</td>
                      <td>0</td>
                      <td>N/A%</td>
                      <td>Invalid Date</td>
                    </tr>
                  
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <div class="timestamp">
        Dashboard generated on: 4/6/2025, 4:09:33 PM
      </div>
    </div>

    <script>
      // Pass Rate Chart
      const passRateCtx = document.getElementById('passRateChart').getContext('2d');
      new Chart(passRateCtx, {
        type: 'bar',
        data: {"labels":["Basic Tests","Security Tests","Performance Tests","E2E Integration Tests","UX Tests","Fee Tests","Fairness Tests"],"datasets":[{"label":"Pass Rate (%)","data":[0,0,0,0,0,0,0],"backgroundColor":["#4285F480","#EA433580","#FBBC0580","#34A85380","#8E44AD80","#F39C1280","#16A08580"],"borderColor":["#4285F4","#EA4335","#FBBC05","#34A853","#8E44AD","#F39C12","#16A085"],"borderWidth":1}]},
        options: {
          scales: {
            y: {
              beginAtZero: true,
              max: 100,
              title: {
                display: true,
                text: 'Pass Rate (%)'
              }
            }
          },
          maintainAspectRatio: false
        }
      });

      // Tests Chart
      const testsCtx = document.getElementById('testsChart').getContext('2d');
      new Chart(testsCtx, {
        type: 'bar',
        data: {"labels":["Basic Tests","Security Tests","Performance Tests","E2E Integration Tests","UX Tests","Fee Tests","Fairness Tests"],"datasets":[{"label":"Passed Tests","data":[0,6,0,0,0,0,0],"backgroundColor":["#34A85380","#34A85380","#34A85380","#34A85380","#34A85380","#34A85380","#34A85380"],"borderColor":["#34A853","#34A853","#34A853","#34A853","#34A853","#34A853","#34A853"],"borderWidth":1},{"label":"Failed Tests","data":[0,2,0,0,0,0,0],"backgroundColor":["#EA433580","#EA433580","#EA433580","#EA433580","#EA433580","#EA433580","#EA433580"],"borderColor":["#EA4335","#EA4335","#EA4335","#EA4335","#EA4335","#EA4335","#EA4335"],"borderWidth":1}]},
        options: {
          scales: {
            y: {
              beginAtZero: true,
              title: {
                display: true,
                text: 'Number of Tests'
              }
            }
          },
          maintainAspectRatio: false
        }
      });

      
    </script>
  </body>
  </html>
  